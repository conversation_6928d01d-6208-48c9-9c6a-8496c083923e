import type { ClerkPaginatedResponse, GetUserOrganizationInvitationsParams, OrganizationCustomRoleKey, OrganizationInvitationStatus, UserOrganizationInvitationJSON, UserOrganizationInvitationResource } from '@clerk/types';
import { BaseResource } from './internal';
export declare class UserOrganizationInvitation extends BaseResource implements UserOrganizationInvitationResource {
    id: string;
    emailAddress: string;
    publicOrganizationData: UserOrganizationInvitationResource['publicOrganizationData'];
    publicMetadata: OrganizationInvitationPublicMetadata;
    status: OrganizationInvitationStatus;
    role: OrganizationCustomRoleKey;
    createdAt: Date;
    updatedAt: Date;
    static retrieve(params?: GetUserOrganizationInvitationsParams): Promise<ClerkPaginatedResponse<UserOrganizationInvitation>>;
    constructor(data: UserOrganizationInvitationJSON);
    accept: () => Promise<UserOrganizationInvitation>;
    protected fromJSON(data: UserOrganizationInvitationJSON | null): this;
}
