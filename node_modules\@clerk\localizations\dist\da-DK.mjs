// src/da-DK.ts
var daDK = {
  locale: "da-DK",
  backButton: "Tilbage",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Standard",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Anden enhed som efterligner",
  badge__primary: "Prim\xE6r",
  badge__renewsAt: void 0,
  badge__requiresAction: "Kr\xE6ver handling",
  badge__startsAt: void 0,
  badge__thisDevice: "Denne enhed",
  badge__unverified: "Ikke verificeret",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Brugerenhed",
  badge__you: "Dig",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Opret organisation",
    invitePage: {
      formButtonReset: "Spring over"
    },
    title: "Opret organisation"
  },
  dates: {
    lastDay: "I g\xE5r: {{ date | timeString('en-US') }}",
    next6Days: "{{ date | weekday('en-US','long') }} kl. {{ date | timeString('en-US') }}",
    nextDay: "I morgen: {{ date | timeString('en-US') }}",
    numeric: "{{ date | numeric('en-US') }}",
    previous6Days: "Sidste {{ date | weekday('en-US','long') }} kl. {{ date | timeString('en-US') }}",
    sameDay: "I dag: {{ date | timeString('en-US') }}"
  },
  dividerText: "eller",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Brug en anden metode",
  footerPageLink__help: "Hj\xE6lp",
  footerPageLink__privacy: "Privatliv",
  footerPageLink__terms: "Vilk\xE5r",
  formButtonPrimary: "Forts\xE6t",
  formButtonPrimary__verify: "Verificer",
  formFieldAction__forgotPassword: "Glemt adgangskode?",
  formFieldError__matchingPasswords: "Adgangskoderne matcher.",
  formFieldError__notMatchingPasswords: "Adgangskoderne matcher ikke.",
  formFieldError__verificationLinkExpired: "Bekr\xE6ftelseslinket er udl\xF8bet. Venligst anmod om et nyt link.",
  formFieldHintText__optional: "Valgfri",
  formFieldHintText__slug: "En slug er en menneskelig l\xE6sbar ID, der skal v\xE6re unik. Det bruges ofte i URL\u2019er.",
  formFieldInputPlaceholder__backupCode: "Indtast sikkerhedskode",
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Slet konto",
  formFieldInputPlaceholder__emailAddress: "Indtast e-mailadresse",
  formFieldInputPlaceholder__emailAddress_username: "Indtast e-mailadresse eller brugernavn",
  formFieldInputPlaceholder__emailAddresses: "Indtast eller inds\xE6t en eller flere e-mailadresser, adskilt af mellemrum eller kommaer",
  formFieldInputPlaceholder__firstName: "Indtast fornavn",
  formFieldInputPlaceholder__lastName: "Indtast efternavn",
  formFieldInputPlaceholder__organizationDomain: "Indtast dom\xE6ne",
  formFieldInputPlaceholder__organizationDomainEmailAddress: "Indtast e-mailadresse til verifikation",
  formFieldInputPlaceholder__organizationName: "Indtast organisationens navn",
  formFieldInputPlaceholder__organizationSlug: "Indtast slug URL",
  formFieldInputPlaceholder__password: "Indtast adgangskode",
  formFieldInputPlaceholder__phoneNumber: "Indtast telefonnummer",
  formFieldInputPlaceholder__username: "Indtast brugernavn",
  formFieldLabel__automaticInvitations: "Aktiver automatiske invitationer for dette dom\xE6ne",
  formFieldLabel__backupCode: "Sikkerhedskode",
  formFieldLabel__confirmDeletion: "Bekr\xE6ftelse",
  formFieldLabel__confirmPassword: "Bekr\xE6ft adgangskode",
  formFieldLabel__currentPassword: "Nuv\xE6rende adgangskode",
  formFieldLabel__emailAddress: "E-mailadresse",
  formFieldLabel__emailAddress_username: "E-mailadresse eller brugernavn",
  formFieldLabel__emailAddresses: "E-mailadresser",
  formFieldLabel__firstName: "Fornavn",
  formFieldLabel__lastName: "Efternavn",
  formFieldLabel__newPassword: "Ny adgangskode",
  formFieldLabel__organizationDomain: "Dom\xE6ne",
  formFieldLabel__organizationDomainDeletePending: "Slet ventende invitationer og forslag",
  formFieldLabel__organizationDomainEmailAddress: "Verifikation e-mailadresse",
  formFieldLabel__organizationDomainEmailAddressDescription: "Indtast en e-mailadresse under dette dom\xE6ne for at modtage en kode og verificere dette dom\xE6ne.",
  formFieldLabel__organizationName: "Organisationens navn",
  formFieldLabel__organizationSlug: "Slug URL",
  formFieldLabel__passkeyName: "Navn p\xE5 adgangsn\xF8gle",
  formFieldLabel__password: "Adgangskode",
  formFieldLabel__phoneNumber: "Telefonnummer",
  formFieldLabel__role: "Rolle",
  formFieldLabel__signOutOfOtherSessions: "Log ud af alle andre enheder",
  formFieldLabel__username: "Brugernavn",
  impersonationFab: {
    action__signOut: "Log ud",
    title: "Logget ind som {{identifier}}"
  },
  maintenanceMode: "Vedligeholdelsestilstand",
  membershipRole__admin: "Administrator",
  membershipRole__basicMember: "Medlem",
  membershipRole__guestMember: "G\xE6st",
  organizationList: {
    action__createOrganization: "Opret organisation",
    action__invitationAccept: "Deltag",
    action__suggestionsAccept: "Anmod om at deltage",
    createOrganization: "Opret Organisation",
    invitationAcceptedLabel: "Deltaget",
    subtitle: "for at forts\xE6tte til {{applicationName}}",
    suggestionsAcceptedLabel: "Afventer godkendelse",
    title: "V\xE6lg en konto",
    titleWithoutPersonal: "V\xE6lg en organisation"
  },
  organizationProfile: {
    badge__automaticInvitation: "Automatiske invitationer",
    badge__automaticSuggestion: "Automatiske forslag",
    badge__manualInvitation: "Ingen automatisk tilmelding",
    badge__unverified: "Ikke verificeret",
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Tilf\xF8j dom\xE6net for at verificere. Brugere med e-mailadresser under dette dom\xE6ne kan automatisk deltage i organisationen eller anmode om at deltage.",
      title: "Tilf\xF8j dom\xE6ne"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Invitationerne kunne ikke sendes. Ret f\xF8lgende, og pr\xF8v igen:",
      formButtonPrimary__continue: "Send invitationer",
      selectDropdown__role: "V\xE6lg rolle",
      subtitle: "Inviter nye medlemmer til denne organisation",
      successMessage: "Invitationer blev sendt",
      title: "Inviter medlemmer"
    },
    membersPage: {
      action__invite: "Inviter",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "Fjern medlem",
        tableHeader__actions: "Handlinger",
        tableHeader__joined: "Deltaget",
        tableHeader__role: "Rolle",
        tableHeader__user: "Bruger"
      },
      detailsTitle__emptyRow: "Ingen medlemmer at vise",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Inviter brugere ved at forbinde et e-maildom\xE6ne med din organisation. Alle, der tilmelder sig med en matchende e-maildom\xE6ne, vil kunne deltage i organisationen n\xE5r som helst.",
          headerTitle: "Automatiske invitationer",
          primaryButton: "Administrer verificerede dom\xE6ner"
        },
        table__emptyRow: "Ingen invitationer at vise"
      },
      invitedMembersTab: {
        menuAction__revoke: "Tilbagekald invitation",
        tableHeader__invited: "Inviteret"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Brugere, der tilmelder sig med en matchende e-maildom\xE6ne, vil kunne se et forslag om at anmode om at deltage i din organisation.",
          headerTitle: "Automatiske forslag",
          primaryButton: "Administrer verificerede dom\xE6ner"
        },
        menuAction__approve: "Godkend",
        menuAction__reject: "Afvis",
        tableHeader__requested: "Anmodet adgang",
        table__emptyRow: "Ingen anmodninger at vise"
      },
      start: {
        headerTitle__invitations: "Invitationer",
        headerTitle__members: "Medlemmer",
        headerTitle__requests: "Anmodninger"
      }
    },
    navbar: {
      billing: void 0,
      description: "Administrer din organisation.",
      general: "Generelt",
      members: "Medlemmer",
      title: "Organisation"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Skriv "{{organizationName}}" nedenfor for at forts\xE6tte.',
          messageLine1: "Er du sikker p\xE5, at du vil slette denne organisation?",
          messageLine2: "Denne handling er permanent og kan ikke fortrydes.",
          successMessage: "Du har slettet organisationen.",
          title: "Slet organisation"
        },
        leaveOrganization: {
          actionDescription: 'Skriv "{{organizationName}}" nedenfor for at forts\xE6tte.',
          messageLine1: "Er du sikker p\xE5, at du vil forlade denne organisation? Du mister adgang til denne organisation og dens applikationer.",
          messageLine2: "Denne handling er permanent og kan ikke fortrydes.",
          successMessage: "Du har forladt organisationen.",
          title: "Forlad organisationen"
        },
        title: "Fare"
      },
      domainSection: {
        menuAction__manage: "Administrer",
        menuAction__remove: "Slet",
        menuAction__verify: "Verificer",
        primaryButton: "Tilf\xF8j dom\xE6ne",
        subtitle: "Tillad brugere at deltage i organisationen automatisk eller anmode om at deltage baseret p\xE5 et verificeret e-maildom\xE6ne.",
        title: "Verificerede dom\xE6ner"
      },
      successMessage: "Organisationen er blevet opdateret.",
      title: "Organisationsprofil"
    },
    removeDomainPage: {
      messageLine1: "E-maildom\xE6net {{domain}} vil blive fjernet.",
      messageLine2: "Brugere vil ikke l\xE6ngere kunne deltage i organisationen automatisk efter dette.",
      successMessage: "{{domain}} er blevet fjernet.",
      title: "Fjern dom\xE6ne"
    },
    start: {
      headerTitle__general: "Generelt",
      headerTitle__members: "Medlemmer",
      profileSection: {
        primaryButton: "Opdater profil",
        title: "Organisationsprofil",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Fjernelse af dette dom\xE6ne vil p\xE5virke inviterede brugere.",
        removeDomainActionLabel__remove: "Fjern dom\xE6ne",
        removeDomainSubtitle: "Fjern dette dom\xE6ne fra dine verificerede dom\xE6ner",
        removeDomainTitle: "Fjern dom\xE6ne"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Brugere bliver automatisk inviteret til at deltage i organisationen, n\xE5r de tilmelder sig og kan deltage n\xE5r som helst.",
        automaticInvitationOption__label: "Automatiske invitationer",
        automaticSuggestionOption__description: "Brugere modtager et forslag om at anmode om at deltage, men skal godkendes af en administrator, f\xF8r de kan deltage i organisationen.",
        automaticSuggestionOption__label: "Automatiske forslag",
        calloutInfoLabel: "\xC6ndring af tilmeldingsindstilling vil kun p\xE5virke nye brugere.",
        calloutInvitationCountLabel: "Ventende invitationer sendt til brugere: {{count}}",
        calloutSuggestionCountLabel: "Ventende forslag sendt til brugere: {{count}}",
        manualInvitationOption__description: "Brugere kan kun inviteres manuelt til organisationen.",
        manualInvitationOption__label: "Ingen automatisk tilmelding",
        subtitle: "V\xE6lg hvordan brugere fra dette dom\xE6ne kan deltage i organisationen."
      },
      start: {
        headerTitle__danger: "Fare",
        headerTitle__enrollment: "Tilmeldingsindstillinger"
      },
      subtitle: "Dom\xE6net {{domain}} er nu verificeret. Forts\xE6t ved at v\xE6lge tilmeldingsmetode.",
      title: "Opdater {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Indtast den bekr\xE6ftelseskode, der blev sendt til din e-mailadresse",
      formTitle: "Bekr\xE6ftelseskode",
      resendButton: "Modtog du ikke en kode? Send igen",
      subtitle: "Dom\xE6net {{domainName}} skal verificeres via e-mail.",
      subtitleVerificationCodeScreen: "En bekr\xE6ftelseskode blev sendt til {{emailAddress}}. Indtast koden for at forts\xE6tte.",
      title: "Verificer dom\xE6ne"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Opret organisation",
    action__invitationAccept: "Deltag",
    action__manageOrganization: "Administrer organisation",
    action__suggestionsAccept: "Anmod om at deltage",
    notSelected: "Ingen organisation valgt",
    personalWorkspace: "Personligt arbejdsomr\xE5de",
    suggestionsAcceptedLabel: "Afventer godkendelse"
  },
  paginationButton__next: "N\xE6ste",
  paginationButton__previous: "Forrige",
  paginationRowText__displaying: "Viser",
  paginationRowText__of: "af",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Tilf\xF8j konto",
      action__signOutAll: "Log ud af alle konti",
      subtitle: "V\xE6lg den konto, du \xF8nsker at forts\xE6tte med.",
      title: "V\xE6lg en konto"
    },
    alternativeMethods: {
      actionLink: "F\xE5 hj\xE6lp",
      actionText: "Har du ingen af disse?",
      blockButton__backupCode: "Brug en backup-kode",
      blockButton__emailCode: "Send kode til {{identifier}}",
      blockButton__emailLink: "Send link til {{identifier}}",
      blockButton__passkey: "Brug en adgangsn\xF8gle",
      blockButton__password: "Log ind med adgangskode",
      blockButton__phoneCode: "Send kode til {{identifier}}",
      blockButton__totp: "Brug din godkendelsesapp",
      getHelp: {
        blockButton__emailSupport: "E-mail support",
        content: "Hvis du har problemer med at logge ind p\xE5 din konto, skal du sende en e-mail til os, og vi vil samarbejde med dig om at genoprette adgang s\xE5 hurtigt som muligt.",
        title: "F\xE5 hj\xE6lp"
      },
      subtitle: "Oplever du problemer? Du kan bruge en af disse metoder til at logge ind.",
      title: "Brug en anden metode"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "Forts\xE6t til {{applicationName}}",
      title: "Indtast en backup-kode"
    },
    emailCode: {
      formTitle: "Bekr\xE6ftelseskode",
      resendButton: "Send kode igen",
      subtitle: "Forts\xE6t til {{applicationName}}",
      title: "Tjek din email"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Klient uoverensstemmelse. Pr\xF8v igen.",
        title: "Klient fejl"
      },
      expired: {
        subtitle: "Vend tilbage til den oprindelige fane for at forts\xE6tte.",
        title: "Dette bekr\xE6ftelseslink er udl\xF8bet"
      },
      failed: {
        subtitle: "Vend tilbage til den oprindelige fane for at forts\xE6tte.",
        title: "Dette bekr\xE6ftelseslink er ugyldigt"
      },
      formSubtitle: "Brug bekr\xE6ftelseslinket sendt til din e-mail",
      formTitle: "Bekr\xE6ftelseslink",
      loading: {
        subtitle: "Du vil snart blive viderestillet",
        title: "Logger ind..."
      },
      resendButton: "Send link igen",
      subtitle: "Forts\xE6t til {{applicationName}}",
      title: "Tjek din email",
      unusedTab: {
        title: "Du kan lukke denne fane"
      },
      verified: {
        subtitle: "Du vil snart blive viderestillet",
        title: "Vellykket log ind"
      },
      verifiedSwitchTab: {
        subtitle: "Vend tilbage til den oprindelige fane for at forts\xE6tte",
        subtitleNewTab: "Vend tilbage til den nyligt \xE5bnede fane for at forts\xE6tte",
        titleNewTab: "Logget ind p\xE5 anden fane"
      }
    },
    forgotPassword: {
      formTitle: "Nulstil adgangskode",
      resendButton: "Modtog du ikke en kode? Send igen",
      subtitle: "for at nulstille din adgangskode",
      subtitle_email: "Indtast f\xF8rst koden sendt til din e-mailadresse",
      subtitle_phone: "Indtast f\xF8rst koden sendt til din telefon",
      title: "Nulstil adgangskode"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Nulstil din adgangskode",
      label__alternativeMethods: "Eller, log ind med en anden metode",
      title: "Glemt Adgangskode?"
    },
    noAvailableMethods: {
      message: "Kan ikke forts\xE6tte med login. Der er ingen tilg\xE6ngelige godkendelsesfaktorer.",
      subtitle: "En fejl opstod",
      title: "Kan ikke logge ind"
    },
    passkey: {
      subtitle: "Brug adgangsn\xF8gle til at logge ind.",
      title: "Adgangsn\xF8gle"
    },
    password: {
      actionLink: "Brug en anden metode",
      subtitle: "Forts\xE6t til {{applicationName}}",
      title: "Indtast din adgangskode"
    },
    passwordPwned: {
      title: "Sikkerhedsadvarsel"
    },
    phoneCode: {
      formTitle: "Bekr\xE6ftelseskode",
      resendButton: "Send kode igen",
      subtitle: "g\xE5 videre til {{applicationName}}",
      title: "Tjek din telefon"
    },
    phoneCodeMfa: {
      formTitle: "Bekr\xE6ftelseskode",
      resendButton: "Send kode igen",
      subtitle: void 0,
      title: "Tjek din telefon"
    },
    resetPassword: {
      formButtonPrimary: "Nulstil Adgangskode",
      requiredMessage: "Af sikkerhedsm\xE6ssige \xE5rsager er det n\xF8dvendigt at nulstille din adgangskode.",
      successMessage: "Din adgangskode blev \xE6ndret med succes. Logger dig ind, vent venligst et \xF8jeblik.",
      title: "Indstil ny adgangskode"
    },
    resetPasswordMfa: {
      detailsLabel: "Vi skal bekr\xE6fte din identitet, f\xF8r du nulstiller din adgangskode."
    },
    start: {
      actionLink: "Tilmeld dig",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "Brug email",
      actionLink__use_email_username: "Brug email eller brugernavn",
      actionLink__use_passkey: "Brug adgangsn\xF8gle",
      actionLink__use_phone: "Brug telefon",
      actionLink__use_username: "Brug brugernavn",
      actionText: "Ingen konto?",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "Fors\xE6t til {{applicationName}}",
      subtitleCombined: void 0,
      title: "Log ind",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "Bekr\xE6ftelseskode",
      subtitle: void 0,
      title: "Totrinsbekr\xE6ftelse"
    }
  },
  signInEnterPasswordTitle: "Indtast din adgangskode",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Log ind",
      actionText: "Har du en konto?",
      subtitle: "Fors\xE6t til {{applicationName}}",
      title: "Udfyld manglende felter"
    },
    emailCode: {
      formSubtitle: "Indtast bekr\xE6ftelseskoden sendt til din e-mailadresse",
      formTitle: "Bekr\xE6ftelseskode",
      resendButton: "Send kode igen",
      subtitle: "Forts\xE6t til {{applicationName}}",
      title: "Bekr\xE6ft din email"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Klient uoverensstemmelse. Pr\xF8v igen.",
        title: "Klient fejl"
      },
      formSubtitle: "Brug bekr\xE6ftelseslinket sendt til din e-mailadresse",
      formTitle: "Bekr\xE6ftelseslink",
      loading: {
        title: "Tilmelding..."
      },
      resendButton: "Send link igen",
      subtitle: "Fors\xE6t til {{applicationName}}",
      title: "Bekr\xE6ft din email",
      verified: {
        title: "Vellykket tilmelding"
      },
      verifiedSwitchTab: {
        subtitle: "Vend tilbage til den nyligt \xE5bnede fane for at forts\xE6tte",
        subtitleNewTab: "Vend tilbage til forrige fane for at forts\xE6tte",
        title: "E-mail er bekr\xE6ftet"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: void 0,
        label__onlyTermsOfService: void 0,
        label__termsOfServiceAndPrivacyPolicy: void 0
      },
      continue: {
        subtitle: void 0,
        title: void 0
      }
    },
    phoneCode: {
      formSubtitle: "Indtast bekr\xE6ftelseskoden sendt til dit telefonnummer",
      formTitle: "Bekr\xE6ftelseskode",
      resendButton: "Send kode igen",
      subtitle: "Forts\xE6t til {{applicationName}}",
      title: "Bekr\xE6ft din telefon"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "Log ind",
      actionLink__use_email: "Brug email",
      actionLink__use_phone: "Brug telefon",
      actionText: "Har du en konto?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "Fors\xE6t til {{applicationName}}",
      subtitleCombined: "Fors\xE6t til {{applicationName}}",
      title: "Opret din konto",
      titleCombined: "Opret din konto"
    }
  },
  socialButtonsBlockButton: "Fors\xE6t med {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "Tilmelding mislykkedes p\xE5 grund af fejlede sikkerhedsvalideringer. Opdater siden for at pr\xF8ve igen, eller kontakt support for yderligere assistance.",
    captcha_unavailable: "Tilmelding mislykkedes p\xE5 grund af fejlet botvalidering. Opdater siden for at pr\xF8ve igen, eller kontakt support for yderligere assistance.",
    form_code_incorrect: "Koden er forkert.",
    form_identifier_exists__email_address: "E-mailadressen er allerede i brug.",
    form_identifier_exists__phone_number: "Telefonnummeret er allerede i brug.",
    form_identifier_exists__username: "Brugernavnet er allerede i brug.",
    form_identifier_not_found: "Vi kunne ikke finde en konto med disse detaljer.",
    form_param_format_invalid: "Formatet er ugyldigt.",
    form_param_format_invalid__email_address: "E-mailadressen skal v\xE6re en gyldig e-mailadresse.",
    form_param_format_invalid__phone_number: "Telefonnummeret skal v\xE6re i et gyldigt internationalt format.",
    form_param_max_length_exceeded__first_name: "Fornavnet m\xE5 ikke overstige 256 tegn.",
    form_param_max_length_exceeded__last_name: "Efternavnet m\xE5 ikke overstige 256 tegn.",
    form_param_max_length_exceeded__name: "Navnet m\xE5 ikke overstige 256 tegn.",
    form_param_nil: "Dette felt kan ikke v\xE6re tomt.",
    form_param_value_invalid: void 0,
    form_password_incorrect: "Adgangskoden er forkert.",
    form_password_length_too_short: "Adgangskoden er for kort.",
    form_password_not_strong_enough: "Adgangskoden er ikke st\xE6rk nok.",
    form_password_pwned: "Adgangskoden er blevet kompromitteret.",
    form_password_pwned__sign_in: "Din adgangskode er blevet kompromitteret, v\xE6lg en ny.",
    form_password_size_in_bytes_exceeded: "Din adgangskode har overskredet det maksimalt tilladte antal bytes, forkort den eller fjern nogle specialtegn.",
    form_password_validation_failed: "Forkert adgangskode.",
    form_username_invalid_character: "Brugernavnet indeholder ugyldige tegn.",
    form_username_invalid_length: "Brugernavnet har en ugyldig l\xE6ngde.",
    identification_deletion_failed: "Du kan ikke slette din sidste identifikation.",
    not_allowed_access: "E-mailadressen eller telefonnummeret er ikke tilladt at tilmelde sig. Dette kan skyldes brug af '+', '=', '#' eller '.' i din e-mail-adresse, ved at bruge et dom\xE6ne, der er forbundet med en midlertidig e-mail-tjeneste, eller ved at blive eksplicit blokeret. Hvis du mener, at dette er en fejl, bedes du kontakte support.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: "Adgangsn\xF8gle findes allerede.",
    passkey_not_supported: "Adgangsn\xF8gler underst\xF8ttes ikke p\xE5 denne enhed.",
    passkey_pa_not_supported: "Adgangsn\xF8gler underst\xF8ttes ikke p\xE5 dette styresystem.",
    passkey_registration_cancelled: "Registrering af adgangsn\xF8gle blev annulleret.",
    passkey_retrieval_cancelled: "Hentning af adgangsn\xF8gle blev annulleret.",
    passwordComplexity: {
      maximumLength: "mindre end {{length}} tegn",
      minimumLength: "{{length}} eller flere tegn",
      requireLowercase: "et lille bogstav",
      requireNumbers: "et tal",
      requireSpecialCharacter: "et specialtegn",
      requireUppercase: "et stort bogstav",
      sentencePrefix: "Din adgangskode skal indeholde"
    },
    phone_number_exists: "Dette telefonnummer er allerede taget. Pr\xF8v et andet.",
    session_exists: "Du er allerede logget ind.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "Din adgangskode virker, men kunne v\xE6re st\xE6rkere. Pr\xF8v at tilf\xF8je flere tegn.",
      goodPassword: "Din adgangskode opfylder alle n\xF8dvendige krav.",
      notEnough: "Din adgangskode er ikke st\xE6rk nok.",
      suggestions: {
        allUppercase: "Brug store bogstaver p\xE5 nogle, men ikke alle bogstaver.",
        anotherWord: "Tilf\xF8j flere ord, der er mindre almindelige.",
        associatedYears: "Undg\xE5 \xE5r, der er forbundet med dig.",
        capitalization: "Brug store bogstaver p\xE5 mere end det f\xF8rste bogstav.",
        dates: "Undg\xE5 datoer og \xE5r, der er forbundet med dig.",
        l33t: "Undg\xE5 forudsigelige bogstavsubstitutioner som '@' for 'a'.",
        longerKeyboardPattern: "Brug l\xE6ngere tastaturm\xF8nstre og skift retning flere gange.",
        noNeed: "Du kan oprette st\xE6rke adgangskoder uden at bruge symboler, tal eller store bogstaver.",
        pwned: "Hvis du bruger denne adgangskode andre steder, b\xF8r du \xE6ndre den.",
        recentYears: "Undg\xE5 nylige \xE5rstal.",
        repeated: "Undg\xE5 gentagne ord og tegn.",
        reverseWords: "Undg\xE5 omvendte stavem\xE5der af almindelige ord.",
        sequences: "Undg\xE5 almindelige tegnsekvenser.",
        useWords: "Brug flere ord, men undg\xE5 almindelige s\xE6tninger."
      },
      warnings: {
        common: "Dette er en almindeligt brugt adgangskode.",
        commonNames: "Almindelige navne og efternavne er nemme at g\xE6tte.",
        dates: "Datoer er nemme at g\xE6tte.",
        extendedRepeat: 'Gentagne tegnm\xF8nstre som "abcabcabc" er nemme at g\xE6tte.',
        keyPattern: "Korte tastaturm\xF8nstre er nemme at g\xE6tte.",
        namesByThemselves: "Enkeltst\xE5ende navne eller efternavne er nemme at g\xE6tte.",
        pwned: "Din adgangskode er blevet eksponeret af et databrud p\xE5 internettet.",
        recentYears: "Nylige \xE5rstal er nemme at g\xE6tte.",
        sequences: 'Almindelige tegnsekvenser som "abc" er nemme at g\xE6tte.',
        similarToCommon: "Dette ligner en almindeligt brugt adgangskode.",
        simpleRepeat: 'Gentagne tegn som "aaa" er nemme at g\xE6tte.',
        straightRow: "Lige r\xE6kker af taster p\xE5 dit tastatur er nemme at g\xE6tte.",
        topHundred: "Dette er en ofte brugt adgangskode.",
        topTen: "Dette er en meget brugt adgangskode.",
        userInputs: "Der b\xF8r ikke v\xE6re nogen personlige eller sidespecifikke data.",
        wordByItself: "Enkeltst\xE5ende ord er nemme at g\xE6tte."
      }
    }
  },
  userButton: {
    action__addAccount: "Tilf\xF8j konto",
    action__manageAccount: "Administrer konto",
    action__signOut: "Log ud",
    action__signOutAll: "Log ud af alle konti"
  },
  userProfile: {
    backupCodePage: {
      actionLabel__copied: "Kopieret!",
      actionLabel__copy: "Kopier alle",
      actionLabel__download: "Download .txt",
      actionLabel__print: "Print",
      infoText1: "Backup-koder vil blive aktiveret for denne konto.",
      infoText2: "Hold backup-koderne hemmelige og gem dem sikkert. Du kan genskabe backup-koder, hvis du har mistanke om, at de er blevet kompromitteret.",
      subtitle__codelist: "Opbevar dem sikkert og hold dem hemmelige.",
      successMessage: "Backup-koder er nu aktiveret. Du kan bruge en af disse til at logge ind p\xE5 din konto, hvis du mister adgangen til din totrinsbekr\xE6ftelse. Hver kode kan kun bruges \xE9n gang.",
      successSubtitle: "Du kan bruge en af disse til at logge ind p\xE5 din konto, hvis du mister adgangen til din totrinsbekr\xE6ftelse.",
      title: "Tilf\xF8j bekr\xE6ftelse af backup-kode",
      title__codelist: "Backup-koder"
    },
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "V\xE6lg en udbyder for at forbinde din konto.",
      formHint__noAccounts: "Der er ingen tilg\xE6ngelige eksterne kontoudbydere.",
      removeResource: {
        messageLine1: "{{identifier}} vil blive fjernet fra denne konto.",
        messageLine2: "Du vil ikke l\xE6ngere v\xE6re i stand til at bruge denne tilsluttede konto, og eventuelle afh\xE6ngige funktioner vil ikke l\xE6ngere virke.",
        successMessage: "{{connectedAccount}} er blevet fjernet fra din konto.",
        title: "Fjern tilsluttet konto"
      },
      socialButtonsBlockButton: "Forbind {{provider|titleize}} konto",
      successMessage: "Udbyderen er blevet tilf\xF8jet til din konto",
      title: "Tilf\xF8j tilsluttet konto"
    },
    deletePage: {
      actionDescription: 'Skriv "Slet konto" nedenfor for at forts\xE6tte.',
      confirm: "Slet konto",
      messageLine1: "Er du sikker p\xE5, at du vil slette din konto?",
      messageLine2: "Denne handling er permanent og kan ikke fortrydes.",
      title: "Slet konto"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "En e-mail indeholdende en bekr\xE6ftelseskode vil blive sendt til denne e-mailadresse.",
        formSubtitle: "Indtast bekr\xE6ftelseskoden sendt til {{identifier}}",
        formTitle: "Bekr\xE6ftelseskode",
        resendButton: "Send kode igen",
        successMessage: "E-mailen {{identifier}} er blevet tilf\xF8jet til din konto."
      },
      emailLink: {
        formHint: "En e-mail indeholdende et bekr\xE6ftelseslink vil blive sendt til denne e-mailadresse.",
        formSubtitle: "Klik p\xE5 bekr\xE6ftelseslinket i e-mailen sendt til {{identifier}}",
        formTitle: "Bekr\xE6ftelseslink",
        resendButton: "Send link igen",
        successMessage: "E-mailen {{identifier}} er blevet tilf\xF8jet til din konto."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} vil blive fjernet fra denne konto.",
        messageLine2: "Du vil ikke l\xE6ngere kunne logge ind med denne e-mailadresse.",
        successMessage: "{{emailAddress}} er blevet fjernet fra din konto.",
        title: "Fjern e-mailadresse"
      },
      title: "Tilf\xF8j e-mailadresse",
      verifyTitle: "Verificer e-mailadresse"
    },
    formButtonPrimary__add: "Tilf\xF8j",
    formButtonPrimary__continue: "Forts\xE6t",
    formButtonPrimary__finish: "Afslut",
    formButtonPrimary__remove: "Fjern",
    formButtonPrimary__save: "Gem",
    formButtonReset: "Annuller",
    mfaPage: {
      formHint: "V\xE6lg en metode, der skal tilf\xF8jes.",
      title: "Tilf\xF8j totrinsbekr\xE6ftelse"
    },
    mfaPhoneCodePage: {
      backButton: "Brug eksisterende nummer",
      primaryButton__addPhoneNumber: "Tilf\xF8j et telefonnummer",
      removeResource: {
        messageLine1: "{{identifier}} vil ikke l\xE6ngere modtage bekr\xE6ftelseskoder, n\xE5r du logger ind.",
        messageLine2: "Din konto er muligvis ikke s\xE5 sikker. Er du sikker p\xE5, at du vil forts\xE6tte?",
        successMessage: "SMS-bekr\xE6ftelse er blevet fjernet for {{mfaPhoneCode}}",
        title: "Fjern SMS-bekr\xE6ftelse"
      },
      subtitle__availablePhoneNumbers: "V\xE6lg et telefonnummer for at registrere SMS-bekr\xE6ftelse til totrinsbekr\xE6ftelse.",
      subtitle__unavailablePhoneNumbers: "Der er ingen tilg\xE6ngelige telefonnumre til at registrere til SMS-bekr\xE6ftelse til totrinsbekr\xE6ftelse.",
      successMessage1: "N\xE5r du logger ind, skal du indtaste en bekr\xE6ftelseskode sendt til dette telefonnummer som et ekstra trin.",
      successMessage2: "Gem disse backup-koder og opbevar dem et sikkert sted. Hvis du mister adgangen til din godkendelsesenhed, kan du bruge backup-koder til at logge ind.",
      successTitle: "SMS-bekr\xE6ftelse aktiveret",
      title: "Tilf\xF8j SMS-bekr\xE6ftelse"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Scan QR-koden i stedet",
        buttonUnableToScan__nonPrimary: "Kan du ikke scanne QR-koden?",
        infoText__ableToScan: "Konfigurer en ny login-metode i din autentificeringsapp, og scan f\xF8lgende QR-kode for at linke den til din konto.",
        infoText__unableToScan: "Konfigurer en ny login-metode i din autentificeringsapp, og indtast n\xF8glen nedenfor.",
        inputLabel__unableToScan1: "S\xF8rg for, at tidsbaserede eller engangsadgangskoder er aktiveret, og afslut derefter tilknytningen af din konto.",
        inputLabel__unableToScan2: "Alternativt, hvis din autentificeringsapp underst\xF8tter TOTP URI'er, kan du ogs\xE5 kopiere hele URI'en."
      },
      removeResource: {
        messageLine1: "Bekr\xE6ftelseskoder fra denne autentificeringsapp kr\xE6ves ikke l\xE6ngere, n\xE5r du logger ind.",
        messageLine2: "Din konto er muligvis ikke s\xE5 sikker. Er du sikker p\xE5, at du vil forts\xE6tte?",
        successMessage: "Totrinsbekr\xE6ftelse via autentificeringsapp er blevet fjernet.",
        title: "Fjern totrinsbekr\xE6ftelse"
      },
      successMessage: "N\xE5r du logger ind, skal du indtaste en bekr\xE6ftelseskode fra denne autentificeringsapp som et ekstra trin.",
      title: "Tilf\xF8j autentificeringsapp",
      verifySubtitle: "Indtast bekr\xE6ftelseskode, der er genereret af din autentificeringsapp",
      verifyTitle: "Bekr\xE6ftelseskode"
    },
    mobileButton__menu: "Menu",
    navbar: {
      account: "Profil",
      billing: void 0,
      description: "Administrer dine kontooplysninger.",
      security: "Sikkerhed",
      title: "Konto"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "Adgangsn\xF8glen fjernes fra din konto.",
        title: "Fjern adgangsn\xF8gle"
      },
      subtitle__rename: "Omd\xF8b adgangsn\xF8gle",
      title__rename: "Omd\xF8b adgangsn\xF8gle"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Det anbefales at logge ud af alle andre enheder, der kan have brugt din gamle adgangskode.",
      readonly: "Din adgangskode kan i \xF8jeblikket ikke redigeres, fordi du kun kan logge ind via virksomhedens forbindelse.",
      successMessage__set: "Din adgangskode er blevet indstillet.",
      successMessage__signOutOfOtherSessions: "Alle andre enheder er blevet logget ud.",
      successMessage__update: "Din adgangskode er blevet opdateret.",
      title__set: "Indstil adgangskode",
      title__update: "Skift adgangskode"
    },
    phoneNumberPage: {
      infoText: "En SMS, der indeholder et bekr\xE6ftelseslink, sendes til dette telefonnummer.",
      removeResource: {
        messageLine1: "{{identifier}} vil blive fjernet fra denne konto.",
        messageLine2: "Du vil ikke l\xE6ngere kunne logge ind med dette telefonnummer.",
        successMessage: "{{phoneNumber}} er blevet fjernet fra din konto.",
        title: "Fjern telefonnummer"
      },
      successMessage: "{{identifier}} er blevet tilf\xF8jet til din konto.",
      title: "Tilf\xF8j telefonnummer",
      verifySubtitle: "Indtast bekr\xE6ftelseskoden sendt til {{identifier}}",
      verifyTitle: "Verificer telefonnummer"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "Upload et JPG, PNG, GIF, eller WEBP-billede mindre end 10 MB",
      imageFormDestructiveActionSubtitle: "Fjern billede",
      imageFormSubtitle: "Upload billede",
      imageFormTitle: "Profilbillede",
      readonly: "Dine profiloplysninger er leveret af virksomhedens forbindelse og kan ikke redigeres.",
      successMessage: "Din profil er blevet opdateret.",
      title: "Profil"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Log ud af enhed",
        title: "Aktive enheder"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Pr\xF8v igen",
        actionLabel__reauthorize: "Godkend nu",
        destructiveActionTitle: "Fjern",
        primaryButton: "Tilknyt konto",
        subtitle__disconnected: "Ingen tilknyttede konti",
        subtitle__reauthorize: "De kr\xE6vede tilladelser er blevet opdateret, og du kan opleve begr\xE6nset funktionalitet. Godkend venligst denne applikation igen for at undg\xE5 eventuelle problemer",
        title: "Tilknyttede konti"
      },
      dangerSection: {
        deleteAccountButton: "Slet Konto",
        title: "Konto afslutning"
      },
      emailAddressesSection: {
        destructiveAction: "Fjern e-mailadresse",
        detailsAction__nonPrimary: "S\xE6t som prim\xE6r",
        detailsAction__primary: "Fuldf\xF8r bekr\xE6ftelse",
        detailsAction__unverified: "Fuldf\xF8r bekr\xE6ftelse",
        primaryButton: "Tilf\xF8j en e-mailadresse",
        title: "E-mailadresser"
      },
      enterpriseAccountsSection: {
        title: "Virksomhedskonti"
      },
      headerTitle__account: "Konto",
      headerTitle__security: "Sikkerhed",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Generer koder",
          headerTitle: "Backup-koder",
          subtitle__regenerate: "F\xE5 et nyt s\xE6t sikre backup-koder. Tidligere backup-koder vil blive slettet og kan ikke bruges.",
          title__regenerate: "Generer backup-koder"
        },
        phoneCode: {
          actionLabel__setDefault: "Indstil som standard",
          destructiveActionLabel: "Fjern telefonnummer"
        },
        primaryButton: "Tilf\xF8j totrinsbekr\xE6ftelse",
        title: "Totrinsbekr\xE6ftelse",
        totp: {
          destructiveActionTitle: "Fjern",
          headerTitle: "Autentificeringsapp"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Fjern adgangsn\xF8gle",
        menuAction__rename: "Omd\xF8b adgangsn\xF8gle",
        primaryButton: void 0,
        title: "Adgangsn\xF8gler"
      },
      passwordSection: {
        primaryButton__setPassword: "Indtast adgangskode",
        primaryButton__updatePassword: "Skift adgangskode",
        title: "Adgangskode"
      },
      phoneNumbersSection: {
        destructiveAction: "Fjern telefonnummer",
        detailsAction__nonPrimary: "S\xE6t som prim\xE6r",
        detailsAction__primary: "Fuldf\xF8r bekr\xE6ftelse",
        detailsAction__unverified: "Fuldf\xF8r bekr\xE6ftelse",
        primaryButton: "Tilf\xF8j et telefonnummer",
        title: "Telefonnumre"
      },
      profileSection: {
        primaryButton: "Opdater profil",
        title: "Profil"
      },
      usernameSection: {
        primaryButton__setUsername: "S\xE6t brugernavn",
        primaryButton__updateUsername: "Skift brugernavn",
        title: "Brugernavn"
      },
      web3WalletsSection: {
        destructiveAction: "Fjern tegnebog",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Tilf\xF8j Web3 tegneb\xF8ger",
        title: "Web3 tegneb\xF8ger"
      }
    },
    usernamePage: {
      successMessage: "Dit brugernavn er blevet opdateret.",
      title__set: "Indstil brugernavn",
      title__update: "Opdater brugernavn"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} vil blive fjernet fra denne konto.",
        messageLine2: "Du vil ikke l\xE6ngere v\xE6re i stand til at logge ind med denne web3-tegnebog.",
        successMessage: "{{web3Wallet}} er blevet fjernet fra din konto.",
        title: "Fjern web3-tegnebog"
      },
      subtitle__availableWallets: "V\xE6lg en web3-tegnebog for at oprette forbindelse til din konto.",
      subtitle__unavailableWallets: "Der er ingen tilg\xE6ngelige web3-tegneb\xF8ger.",
      successMessage: "Tegnebogen er blevet tilf\xF8jet til din konto.",
      title: "Tilf\xF8j web3-tegnebog",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  daDK
};
//# sourceMappingURL=da-DK.mjs.map