import React from 'react';
import type { Button, LocalizationKey } from '../customizables';
import { Text } from '../customizables';
import type { ElementDescriptor, ElementId } from '../customizables/elementDescriptors';
import type { PropsOfComponent, ThemableCssProp } from '../styledSystem';
type ArrowBlockButtonProps = PropsOfComponent<typeof Button> & {
    rightIcon?: React.ComponentType;
    rightIconSx?: ThemableCssProp;
    leftIcon?: React.ComponentType | React.ReactElement;
    leftIconSx?: ThemableCssProp;
    childrenSx?: ThemableCssProp;
    leftIconElementDescriptor?: ElementDescriptor;
    leftIconElementId?: ElementId;
    badge?: React.ReactElement;
    textElementDescriptor?: ElementDescriptor;
    textElementId?: ElementId;
    arrowElementDescriptor?: ElementDescriptor;
    arrowElementId?: ElementId;
    spinnerElementDescriptor?: ElementDescriptor;
    spinnerElementId?: ElementId;
    textLocalizationKey?: LocalizationKey | string;
    textVariant?: PropsOfComponent<typeof Text>['variant'];
};
export declare const ArrowBlockButton: React.ForwardRefExoticComponent<Omit<ArrowBlockButtonProps, "ref"> & React.RefAttributes<HTMLButtonElement>>;
export {};
