import type { Clerk<PERSON>IError, PasswordSettingsData } from '@clerk/types';
import type { LocalizationKey } from '../localization';
export declare const mapComplexityErrors: (passwordSettings: Pick<PasswordSettingsData, "max_length" | "min_length">) => {
    form_password_length_too_long: (string | number)[];
    form_password_length_too_short: (string | number)[];
    form_password_no_uppercase: string;
    form_password_no_lowercase: string;
    form_password_no_number: string;
    form_password_no_special_char: string;
};
type LocalizationConfigProps = {
    t: (localizationKey: LocalizationKey | string | undefined) => string;
    locale: string;
    passwordSettings: Pick<PasswordSettingsData, 'max_length' | 'min_length'>;
};
export declare const createPasswordError: (errors: ClerkAPIError[], localizationConfig: LocalizationConfigProps) => string | undefined;
export declare const addFullStop: (string: string | undefined) => string;
export declare const createListFormat: (message: string[], locale: string) => string;
export {};
