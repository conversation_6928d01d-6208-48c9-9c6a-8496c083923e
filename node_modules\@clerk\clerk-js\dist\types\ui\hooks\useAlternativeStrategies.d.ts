import type { SignInFactor, SignInFirstFactor } from '@clerk/types';
export declare function useAlternativeStrategies<T = SignInFirstFactor>({ filterOutFactor, supportedFirstFactors: _supportedFirstFactors, }: {
    filterOutFactor: SignInFactor | null | undefined;
    supportedFirstFactors: SignInFirstFactor[] | null | undefined;
}): {
    hasAnyStrategy: boolean;
    hasFirstParty: boolean;
    firstPartyFactors: T[];
};
