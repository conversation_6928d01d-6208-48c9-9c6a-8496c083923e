import { Text } from '../customizables';
import type { PropsOfComponent } from '../styledSystem';
type FormattedPhoneProps = {
    value: string;
};
export declare const FormattedPhoneNumber: (props: FormattedPhoneProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export declare const FormattedPhoneNumberText: (props: FormattedPhoneProps & PropsOfComponent<typeof Text>) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
