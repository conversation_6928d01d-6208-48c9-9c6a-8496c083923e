import type { LocalizationKey } from '../customizables';
import { Alert as AlertCust } from '../customizables';
import type { PropsOfComponent } from '../styledSystem';
type _AlertProps = {
    variant?: 'danger' | 'warning' | 'info';
    title?: LocalizationKey | string;
    subtitle?: LocalizationKey | string;
};
type AlertProps = Omit<PropsOfComponent<typeof AlertCust>, keyof _AlertProps> & _AlertProps;
export declare const Alert: (props: AlertProps) => JSX.Element | null;
export {};
