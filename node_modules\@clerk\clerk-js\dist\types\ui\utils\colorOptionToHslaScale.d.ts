import type { ColorScale, CssColorOrAlphaScale, CssColorOrScale, HslaColorString } from '@clerk/types';
type InternalColorScale<T> = ColorScale<T> & Partial<Record<20, T>>;
type WithPrefix<T extends Record<string, string>, Prefix extends string> = {
    [k in keyof T as `${Prefix}${k & string}`]: T[k];
};
export declare const colorOptionToHslaAlphaScale: <Prefix extends string>(colorOption: CssColorOrAlphaScale | undefined, prefix: Prefix) => WithPrefix<InternalColorScale<HslaColorString>, Prefix> | undefined;
export declare const colorOptionToHslaLightnessScale: <Prefix extends string>(colorOption: CssColorOrScale | undefined, prefix: Prefix) => WithPrefix<InternalColorScale<HslaColorString>, Prefix> | undefined;
export {};
