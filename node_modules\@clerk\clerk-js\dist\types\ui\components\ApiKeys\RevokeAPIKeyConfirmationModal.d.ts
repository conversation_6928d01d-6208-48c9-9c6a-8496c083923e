type RevokeAPIKeyConfirmationModalProps = {
    subject: string;
    isOpen: boolean;
    onOpen: () => void;
    onClose: () => void;
    apiKeyId?: string;
    apiKeyName?: string;
    modalRoot?: React.MutableRefObject<HTMLElement | null>;
};
export declare const RevokeAPIKeyConfirmationModal: ({ subject, isOpen, onOpen, onClose, apiKeyId, apiKeyName, modalRoot, }: RevokeAPIKeyConfirmationModalProps) => import("@emotion/react/jsx-runtime").JSX.Element | null;
export {};
