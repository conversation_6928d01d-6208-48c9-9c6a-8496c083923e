import type { OrganizationPreviewId, UserOrganizationInvitationResource, UserResource } from '@clerk/types';
import React from 'react';
import { Flex, Text } from '../customizables';
import type { PropsOfComponent, ThemableCssProp } from '../styledSystem';
export type OrganizationPreviewProps = Omit<PropsOfComponent<typeof Flex>, 'elementId'> & {
    organization: UserOrganizationInvitationResource['publicOrganizationData'];
    user?: UserResource;
    size?: 'lg' | 'md' | 'sm' | 'xs';
    avatarSx?: ThemableCssProp;
    mainIdentifierSx?: ThemableCssProp;
    mainIdentifierVariant?: PropsOfComponent<typeof Text>['variant'];
    icon?: React.ReactNode;
    badge?: React.ReactNode;
    rounded?: boolean;
    elementId?: OrganizationPreviewId;
    fetchRoles?: boolean;
};
export declare const OrganizationPreview: (props: OrganizationPreviewProps) => import("@emotion/react/jsx-runtime").JSX.Element;
