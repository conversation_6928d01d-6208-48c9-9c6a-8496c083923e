import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, ClerkRuntimeError } from '@clerk/types';
import type { FormControlState } from './useFormControl';
type HandleError = {
    (err: Error, fieldStates: Array<FormControlState<string>>, setGlobalError?: (err: <PERSON><PERSON><PERSON><PERSON>Error | ClerkAPIError | string | undefined) => void): void;
};
export declare const handleError: HandleError;
export declare function getGlobalError(err: Error): ClerkAPIError | undefined;
export declare function getFieldError(err: Error): ClerkAPIError | undefined;
export declare function getClerkAPIErrorMessage(err: ClerkAPIError): string;
export {};
