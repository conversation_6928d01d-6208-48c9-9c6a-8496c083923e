export declare const animations: {
    spinning: import("@emotion/react").Keyframes;
    dropdownSlideInScaleAndFade: import("@emotion/react").Keyframes;
    modalSlideAndFade: import("@emotion/react").Keyframes;
    fadeIn: import("@emotion/react").Keyframes;
    fadeOut: import("@emotion/react").Keyframes;
    textInSmall: import("@emotion/react").Keyframes;
    textInBig: import("@emotion/react").Keyframes;
    blockBigIn: import("@emotion/react").Keyframes;
    expandIn: (max: string) => import("@emotion/react").Keyframes;
    navbarSlideIn: import("@emotion/react").Keyframes;
    inAnimation: import("@emotion/react").Keyframes;
    inDelayAnimation: import("@emotion/react").Keyframes;
    outAnimation: import("@emotion/react").Keyframes;
    notificationAnimation: import("@emotion/react").Keyframes;
};
