import type { ClerkPaginatedResponse } from '@clerk/types';
export declare const organizationListParams: {
    userMemberships: {
        infinite: true;
    };
    userInvitations: {
        infinite: true;
    };
    userSuggestions: {
        infinite: true;
        status: ("pending" | "accepted")[];
    };
};
export declare const populateCacheUpdateItem: <T extends {
    id: string;
}>(updatedItem: T, itemsInfinitePages: (ClerkPaginatedResponse<T> | undefined)[] | undefined, affectTotalCount?: "negative") => ({
    data: T[];
    total_count: number;
} | undefined)[];
export declare const populateCacheRemoveItem: <T extends {
    id: string;
}>(updatedItem: T, itemsInfinitePages: (ClerkPaginatedResponse<T> | undefined)[] | undefined) => ({
    data: T[];
    total_count: number;
} | undefined)[] | undefined;
