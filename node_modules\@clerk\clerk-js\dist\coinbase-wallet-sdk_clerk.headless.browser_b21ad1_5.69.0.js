(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["956"],{468:function(e,t){"use strict";function n(e){if(!Number.isSafeInteger(e)||e<0)throw Error("positive integer expected, got "+e)}function s(e,...t){if(!(e instanceof Uint8Array||ArrayBuffer.isView(e)&&"Uint8Array"===e.constructor.name))throw Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw Error("Uint8Array expected of length "+t+", got length="+e.length)}Object.defineProperty(t,"__esModule",{value:!0}),t.anumber=n,t.abytes=s,t.ahash=function(e){if("function"!=typeof e||"function"!=typeof e.create)throw Error("Hash should be wrapped by utils.wrapConstructor");n(e.outputLen),n(e.block<PERSON>en)},t.aexists=function(e,t=!0){if(e.destroyed)throw Error("Hash instance has been destroyed");if(t&&e.finished)throw Error("Hash#digest() has already been called")},t.aoutput=function(e,t){s(e);let n=t.outputLen;if(e.length<n)throw Error("digestInto() expects output buffer of length at least "+n)}},547:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.add5L=t.add5H=t.add4H=t.add4L=t.add3H=t.add3L=t.rotlBL=t.rotlBH=t.rotlSL=t.rotlSH=t.rotr32L=t.rotr32H=t.rotrBL=t.rotrBH=t.rotrSL=t.rotrSH=t.shrSL=t.shrSH=t.toBig=void 0,t.fromBig=r,t.split=i,t.add=b;let n=BigInt(0x100000000-1),s=BigInt(32);function r(e,t=!1){return t?{h:Number(e&n),l:Number(e>>s&n)}:{h:0|Number(e>>s&n),l:0|Number(e&n)}}function i(e,t=!1){let n=new Uint32Array(e.length),s=new Uint32Array(e.length);for(let i=0;i<e.length;i++){let{h:a,l:o}=r(e[i],t);[n[i],s[i]]=[a,o]}return[n,s]}let a=(e,t)=>BigInt(e>>>0)<<s|BigInt(t>>>0);t.toBig=a;let o=(e,t,n)=>e>>>n;t.shrSH=o;let c=(e,t,n)=>e<<32-n|t>>>n;t.shrSL=c;let l=(e,t,n)=>e>>>n|t<<32-n;t.rotrSH=l;let h=(e,t,n)=>e<<32-n|t>>>n;t.rotrSL=h;let d=(e,t,n)=>e<<64-n|t>>>n-32;t.rotrBH=d;let u=(e,t,n)=>e>>>n-32|t<<64-n;t.rotrBL=u;let p=(e,t)=>t;t.rotr32H=p;let f=(e,t)=>e;t.rotr32L=f;let g=(e,t,n)=>e<<n|t>>>32-n;t.rotlSH=g;let m=(e,t,n)=>t<<n|e>>>32-n;t.rotlSL=m;let y=(e,t,n)=>t<<n-32|e>>>64-n;t.rotlBH=y;let _=(e,t,n)=>e<<n-32|t>>>64-n;function b(e,t,n,s){let r=(t>>>0)+(s>>>0);return{h:e+n+(r/0x100000000|0)|0,l:0|r}}t.rotlBL=_;let w=(e,t,n)=>(e>>>0)+(t>>>0)+(n>>>0);t.add3L=w;let v=(e,t,n,s)=>t+n+s+(e/0x100000000|0)|0;t.add3H=v;let k=(e,t,n,s)=>(e>>>0)+(t>>>0)+(n>>>0)+(s>>>0);t.add4L=k;let x=(e,t,n,s,r)=>t+n+s+r+(e/0x100000000|0)|0;t.add4H=x;let C=(e,t,n,s,r)=>(e>>>0)+(t>>>0)+(n>>>0)+(s>>>0)+(r>>>0);t.add5L=C;let E=(e,t,n,s,r,i)=>t+n+s+r+i+(e/0x100000000|0)|0;t.add5H=E,t.default={fromBig:r,split:i,toBig:a,shrSH:o,shrSL:c,rotrSH:l,rotrSL:h,rotrBH:d,rotrBL:u,rotr32H:p,rotr32L:f,rotlSH:g,rotlSL:m,rotlBH:y,rotlBL:_,add:b,add3L:w,add3H:v,add4L:k,add4H:x,add5H:E,add5L:C}},221:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.crypto=void 0,t.crypto="object"==typeof globalThis&&"crypto"in globalThis?globalThis.crypto:void 0},145:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.shake256=t.shake128=t.keccak_512=t.keccak_384=t.keccak_256=t.keccak_224=t.sha3_512=t.sha3_384=t.sha3_256=t.sha3_224=t.Keccak=void 0,t.keccakP=b;let s=n(468),r=n(547),i=n(865),a=[],o=[],c=[],l=BigInt(0),h=BigInt(1),d=BigInt(2),u=BigInt(7),p=BigInt(256),f=BigInt(113);for(let e=0,t=h,n=1,s=0;e<24;e++){[n,s]=[s,(2*n+3*s)%5],a.push(2*(5*s+n)),o.push((e+1)*(e+2)/2%64);let r=l;for(let e=0;e<7;e++)(t=(t<<h^(t>>u)*f)%p)&d&&(r^=h<<(h<<BigInt(e))-h);c.push(r)}let[g,m]=(0,r.split)(c,!0),y=(e,t,n)=>n>32?(0,r.rotlBH)(e,t,n):(0,r.rotlSH)(e,t,n),_=(e,t,n)=>n>32?(0,r.rotlBL)(e,t,n):(0,r.rotlSL)(e,t,n);function b(e,t=24){let n=new Uint32Array(10);for(let s=24-t;s<24;s++){for(let t=0;t<10;t++)n[t]=e[t]^e[t+10]^e[t+20]^e[t+30]^e[t+40];for(let t=0;t<10;t+=2){let s=(t+8)%10,r=(t+2)%10,i=n[r],a=n[r+1],o=y(i,a,1)^n[s],c=_(i,a,1)^n[s+1];for(let n=0;n<50;n+=10)e[t+n]^=o,e[t+n+1]^=c}let t=e[2],r=e[3];for(let n=0;n<24;n++){let s=o[n],i=y(t,r,s),c=_(t,r,s),l=a[n];t=e[l],r=e[l+1],e[l]=i,e[l+1]=c}for(let t=0;t<50;t+=10){for(let s=0;s<10;s++)n[s]=e[t+s];for(let s=0;s<10;s++)e[t+s]^=~n[(s+2)%10]&n[(s+4)%10]}e[0]^=g[s],e[1]^=m[s]}n.fill(0)}class w extends i.Hash{constructor(e,t,n,r=!1,a=24){if(super(),this.blockLen=e,this.suffix=t,this.outputLen=n,this.enableXOF=r,this.rounds=a,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,(0,s.anumber)(n),0>=this.blockLen||this.blockLen>=200)throw Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=(0,i.u32)(this.state)}keccak(){i.isLE||(0,i.byteSwap32)(this.state32),b(this.state32,this.rounds),i.isLE||(0,i.byteSwap32)(this.state32),this.posOut=0,this.pos=0}update(e){(0,s.aexists)(this);let{blockLen:t,state:n}=this,r=(e=(0,i.toBytes)(e)).length;for(let s=0;s<r;){let i=Math.min(t-this.pos,r-s);for(let t=0;t<i;t++)n[this.pos++]^=e[s++];this.pos===t&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:e,suffix:t,pos:n,blockLen:s}=this;e[n]^=t,(128&t)!=0&&n===s-1&&this.keccak(),e[s-1]^=128,this.keccak()}writeInto(e){(0,s.aexists)(this,!1),(0,s.abytes)(e),this.finish();let t=this.state,{blockLen:n}=this;for(let s=0,r=e.length;s<r;){this.posOut>=n&&this.keccak();let i=Math.min(n-this.posOut,r-s);e.set(t.subarray(this.posOut,this.posOut+i),s),this.posOut+=i,s+=i}return e}xofInto(e){if(!this.enableXOF)throw Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return(0,s.anumber)(e),this.xofInto(new Uint8Array(e))}digestInto(e){if((0,s.aoutput)(e,this),this.finished)throw Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(e){let{blockLen:t,suffix:n,outputLen:s,rounds:r,enableXOF:i}=this;return e||(e=new w(t,n,s,i,r)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=r,e.suffix=n,e.outputLen=s,e.enableXOF=i,e.destroyed=this.destroyed,e}}t.Keccak=w;let v=(e,t,n)=>(0,i.wrapConstructor)(()=>new w(t,e,n));t.sha3_224=v(6,144,28),t.sha3_256=v(6,136,32),t.sha3_384=v(6,104,48),t.sha3_512=v(6,72,64),t.keccak_224=v(1,144,28),t.keccak_256=v(1,136,32),t.keccak_384=v(1,104,48),t.keccak_512=v(1,72,64);let k=(e,t,n)=>(0,i.wrapXOFConstructorWithOpts)((s={})=>new w(t,e,void 0===s.dkLen?n:s.dkLen,!0));t.shake128=k(31,168,16),t.shake256=k(31,136,32)},865:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Hash=t.nextTick=t.byteSwapIfBE=t.isLE=void 0,t.isBytes=function(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&"Uint8Array"===e.constructor.name},t.u8=function(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)},t.u32=function(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))},t.createView=function(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)},t.rotr=function(e,t){return e<<32-t|e>>>t},t.rotl=function(e,t){return e<<t|e>>>32-t>>>0},t.byteSwap=i,t.byteSwap32=function(e){for(let t=0;t<e.length;t++)e[t]=i(e[t])},t.bytesToHex=function(e){(0,r.abytes)(e);let t="";for(let n=0;n<e.length;n++)t+=a[e[n]];return t},t.hexToBytes=function(e){if("string"!=typeof e)throw Error("hex string expected, got "+typeof e);let t=e.length,n=t/2;if(t%2)throw Error("hex string expected, got unpadded hex of length "+t);let s=new Uint8Array(n);for(let t=0,r=0;t<n;t++,r+=2){let n=c(e.charCodeAt(r)),i=c(e.charCodeAt(r+1));if(void 0===n||void 0===i)throw Error('hex string expected, got non-hex character "'+(e[r]+e[r+1])+'" at index '+r);s[t]=16*n+i}return s},t.asyncLoop=h,t.utf8ToBytes=d,t.toBytes=u,t.concatBytes=function(...e){let t=0;for(let n=0;n<e.length;n++){let s=e[n];(0,r.abytes)(s),t+=s.length}let n=new Uint8Array(t);for(let t=0,s=0;t<e.length;t++){let r=e[t];n.set(r,s),s+=r.length}return n},t.checkOpts=function(e,t){if(void 0!==t&&"[object Object]"!==({}).toString.call(t))throw Error("Options should be object or undefined");return Object.assign(e,t)},t.wrapConstructor=function(e){let t=t=>e().update(u(t)).digest(),n=e();return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=()=>e(),t},t.wrapConstructorWithOpts=function(e){let t=(t,n)=>e(n).update(u(t)).digest(),n=e({});return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=t=>e(t),t},t.wrapXOFConstructorWithOpts=function(e){let t=(t,n)=>e(n).update(u(t)).digest(),n=e({});return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=t=>e(t),t},t.randomBytes=function(e=32){if(s.crypto&&"function"==typeof s.crypto.getRandomValues)return s.crypto.getRandomValues(new Uint8Array(e));if(s.crypto&&"function"==typeof s.crypto.randomBytes)return s.crypto.randomBytes(e);throw Error("crypto.getRandomValues must be defined")};let s=n(221),r=n(468);function i(e){return e<<24&0xff000000|e<<8&0xff0000|e>>>8&65280|e>>>24&255}t.isLE=68===new Uint8Array(new Uint32Array([0x11223344]).buffer)[0],t.byteSwapIfBE=t.isLE?e=>e:e=>i(e);let a=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0")),o={_0:48,_9:57,A:65,F:70,a:97,f:102};function c(e){return e>=o._0&&e<=o._9?e-o._0:e>=o.A&&e<=o.F?e-(o.A-10):e>=o.a&&e<=o.f?e-(o.a-10):void 0}let l=async()=>{};async function h(e,n,s){let r=Date.now();for(let i=0;i<e;i++){s(i);let e=Date.now()-r;e>=0&&e<n||(await (0,t.nextTick)(),r+=e)}}function d(e){if("string"!=typeof e)throw Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array(new TextEncoder().encode(e))}function u(e){return"string"==typeof e&&(e=d(e)),(0,r.abytes)(e),e}t.nextTick=l,t.Hash=class{clone(){return this._cloneInto()}}},825:function(e){"use strict";var t=Object.prototype.hasOwnProperty,n="~";function s(){}function r(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function i(e,t,s,i,a){if("function"!=typeof s)throw TypeError("The listener must be a function");var o=new r(s,i||e,a),c=n?n+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],o]:e._events[c].push(o):(e._events[c]=o,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new s:delete e._events[t]}function o(){this._events=new s,this._eventsCount=0}Object.create&&(s.prototype=Object.create(null),new s().__proto__||(n=!1)),o.prototype.eventNames=function(){var e,s,r=[];if(0===this._eventsCount)return r;for(s in e=this._events)t.call(e,s)&&r.push(n?s.slice(1):s);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(e)):r},o.prototype.listeners=function(e){var t=n?n+e:e,s=this._events[t];if(!s)return[];if(s.fn)return[s.fn];for(var r=0,i=s.length,a=Array(i);r<i;r++)a[r]=s[r].fn;return a},o.prototype.listenerCount=function(e){var t=n?n+e:e,s=this._events[t];return s?s.fn?1:s.length:0},o.prototype.emit=function(e,t,s,r,i,a){var o=n?n+e:e;if(!this._events[o])return!1;var c,l,h=this._events[o],d=arguments.length;if(h.fn){switch(h.once&&this.removeListener(e,h.fn,void 0,!0),d){case 1:return h.fn.call(h.context),!0;case 2:return h.fn.call(h.context,t),!0;case 3:return h.fn.call(h.context,t,s),!0;case 4:return h.fn.call(h.context,t,s,r),!0;case 5:return h.fn.call(h.context,t,s,r,i),!0;case 6:return h.fn.call(h.context,t,s,r,i,a),!0}for(l=1,c=Array(d-1);l<d;l++)c[l-1]=arguments[l];h.fn.apply(h.context,c)}else{var u,p=h.length;for(l=0;l<p;l++)switch(h[l].once&&this.removeListener(e,h[l].fn,void 0,!0),d){case 1:h[l].fn.call(h[l].context);break;case 2:h[l].fn.call(h[l].context,t);break;case 3:h[l].fn.call(h[l].context,t,s);break;case 4:h[l].fn.call(h[l].context,t,s,r);break;default:if(!c)for(u=1,c=Array(d-1);u<d;u++)c[u-1]=arguments[u];h[l].fn.apply(h[l].context,c)}}return!0},o.prototype.on=function(e,t,n){return i(this,e,t,n,!1)},o.prototype.once=function(e,t,n){return i(this,e,t,n,!0)},o.prototype.removeListener=function(e,t,s,r){var i=n?n+e:e;if(!this._events[i])return this;if(!t)return a(this,i),this;var o=this._events[i];if(o.fn)o.fn!==t||r&&!o.once||s&&o.context!==s||a(this,i);else{for(var c=0,l=[],h=o.length;c<h;c++)(o[c].fn!==t||r&&!o[c].once||s&&o[c].context!==s)&&l.push(o[c]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&a(this,t)):(this._events=new s,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=n,o.EventEmitter=o,e.exports=o},170:function(e,t,n){let s=n(689);function r(e){if(e.startsWith("int["))return"int256"+e.slice(3);if("int"===e)return"int256";if(e.startsWith("uint["))return"uint256"+e.slice(4);if("uint"===e)return"uint256";if(e.startsWith("fixed["))return"fixed128x128"+e.slice(5);else if("fixed"===e)return"fixed128x128";else if(e.startsWith("ufixed["))return"ufixed128x128"+e.slice(6);else if("ufixed"===e)return"ufixed128x128";return e}function i(e){return Number.parseInt(/^\D+(\d+)$/.exec(e)[1],10)}function a(e){var t=/^\D+(\d+)x(\d+)$/.exec(e);return[Number.parseInt(t[1],10),Number.parseInt(t[2],10)]}function o(e){var t=e.match(/(.*)\[(.*?)\]$/);return t?""===t[2]?"dynamic":Number.parseInt(t[2],10):null}function c(e){var t=typeof e;if("string"===t||"number"===t)return BigInt(e);if("bigint"===t)return e;throw Error("Argument is not a number")}function l(e,t){if("address"===e)return l("uint160",c(t));if("bool"===e)return l("uint8",+!!t);if("string"===e)return l("bytes",new Buffer(t,"utf8"));if((u=e).lastIndexOf("]")===u.length-1){if(void 0===t.length)throw Error("Not an array?");if("dynamic"!==(n=o(e))&&0!==n&&t.length>n)throw Error("Elements exceed array size: "+n);for(d in h=[],e=e.slice(0,e.lastIndexOf("[")),"string"==typeof t&&(t=JSON.parse(t)),t)h.push(l(e,t[d]));if("dynamic"===n){var n,r,h,d,u,p=l("uint256",t.length);h.unshift(p)}return Buffer.concat(h)}else if("bytes"===e)return t=new Buffer(t),h=Buffer.concat([l("uint256",t.length),t]),t.length%32!=0&&(h=Buffer.concat([h,s.zeros(32-t.length%32)])),h;else if(e.startsWith("bytes")){if((n=i(e))<1||n>32)throw Error("Invalid bytes<N> width: "+n);return s.setLengthRight(t,32)}else if(e.startsWith("uint")){if((n=i(e))%8||n<8||n>256)throw Error("Invalid uint<N> width: "+n);r=c(t);let a=s.bitLengthFromBigInt(r);if(a>n)throw Error("Supplied uint exceeds width: "+n+" vs "+a);if(r<0)throw Error("Supplied uint is negative");return s.bufferBEFromBigInt(r,32)}else if(e.startsWith("int")){if((n=i(e))%8||n<8||n>256)throw Error("Invalid int<N> width: "+n);r=c(t);let a=s.bitLengthFromBigInt(r);if(a>n)throw Error("Supplied int exceeds width: "+n+" vs "+a);let o=s.twosFromBigInt(r,256);return s.bufferBEFromBigInt(o,32)}else if(e.startsWith("ufixed")){if(n=a(e),(r=c(t))<0)throw Error("Supplied ufixed is negative");return l("uint256",r*BigInt(2)**BigInt(n[1]))}else if(e.startsWith("fixed"))return n=a(e),l("int256",c(t)*BigInt(2)**BigInt(n[1]));throw Error("Unsupported or invalid type: "+e)}function h(e,t){if(e.length!==t.length)throw Error("Number of types are not matching the values");for(var n,a,o=[],l=0;l<e.length;l++){var h=r(e[l]),d=t[l];if("bytes"===h)o.push(d);else if("string"===h)o.push(new Buffer(d,"utf8"));else if("bool"===h)o.push(new Buffer(d?"01":"00","hex"));else if("address"===h)o.push(s.setLength(d,20));else if(h.startsWith("bytes")){if((n=i(h))<1||n>32)throw Error("Invalid bytes<N> width: "+n);o.push(s.setLengthRight(d,n))}else if(h.startsWith("uint")){if((n=i(h))%8||n<8||n>256)throw Error("Invalid uint<N> width: "+n);a=c(d);let e=s.bitLengthFromBigInt(a);if(e>n)throw Error("Supplied uint exceeds width: "+n+" vs "+e);o.push(s.bufferBEFromBigInt(a,n/8))}else if(h.startsWith("int")){if((n=i(h))%8||n<8||n>256)throw Error("Invalid int<N> width: "+n);a=c(d);let e=s.bitLengthFromBigInt(a);if(e>n)throw Error("Supplied int exceeds width: "+n+" vs "+e);let t=s.twosFromBigInt(a,n);o.push(s.bufferBEFromBigInt(t,n/8))}else throw Error("Unsupported or invalid type: "+h)}return Buffer.concat(o)}e.exports={rawEncode:function(e,t){var n=[],s=[],i=32*e.length;for(var a in e){var c=r(e[a]),h=l(c,t[a]);"string"===c||"bytes"===c||"dynamic"===o(c)?(n.push(l("uint256",i)),s.push(h),i+=h.length):n.push(h)}return Buffer.concat(n.concat(s))},solidityPack:h,soliditySHA3:function(e,t){return s.keccak(h(e,t))}}},50:function(e,t,n){let s=n(689),r=n(170),i={type:"object",properties:{types:{type:"object",additionalProperties:{type:"array",items:{type:"object",properties:{name:{type:"string"},type:{type:"string"}},required:["name","type"]}}},primaryType:{type:"string"},domain:{type:"object"},message:{type:"object"}},required:["types","primaryType","domain","message"]},a={encodeData(e,t,n,i=!0){let a=["bytes32"],o=[this.hashType(e,n)];if(i){let c=(e,t,a)=>{if(void 0!==n[t])return["bytes32",null==a?"0x0000000000000000000000000000000000000000000000000000000000000000":s.keccak(this.encodeData(t,a,n,i))];if(void 0===a)throw Error(`missing value for field ${e} of type ${t}`);if("bytes"===t)return["bytes32",s.keccak(a)];if("string"===t)return"string"==typeof a&&(a=Buffer.from(a,"utf8")),["bytes32",s.keccak(a)];if(t.lastIndexOf("]")===t.length-1){let n=t.slice(0,t.lastIndexOf("[")),i=a.map(t=>c(e,n,t));return["bytes32",s.keccak(r.rawEncode(i.map(([e])=>e),i.map(([,e])=>e)))]}return[t,a]};for(let s of n[e]){let[e,n]=c(s.name,s.type,t[s.name]);a.push(e),o.push(n)}}else for(let r of n[e]){let e=t[r.name];if(void 0!==e){if("bytes"===r.type)a.push("bytes32"),e=s.keccak(e),o.push(e);else if("string"===r.type)a.push("bytes32"),"string"==typeof e&&(e=Buffer.from(e,"utf8")),e=s.keccak(e),o.push(e);else if(void 0!==n[r.type])a.push("bytes32"),e=s.keccak(this.encodeData(r.type,e,n,i)),o.push(e);else if(r.type.lastIndexOf("]")===r.type.length-1)throw Error("Arrays currently unimplemented in encodeData");else a.push(r.type),o.push(e)}}return r.rawEncode(a,o)},encodeType(e,t){let n="",s=this.findTypeDependencies(e,t).filter(t=>t!==e);for(let r of s=[e].concat(s.sort())){if(!t[r])throw Error("No type definition specified: "+r);n+=r+"("+t[r].map(({name:e,type:t})=>t+" "+e).join(",")+")"}return n},findTypeDependencies(e,t,n=[]){if(e=e.match(/^\w*/)[0],n.includes(e)||void 0===t[e])return n;for(let s of(n.push(e),t[e]))for(let e of this.findTypeDependencies(s.type,t,n))n.includes(e)||n.push(e);return n},hashStruct(e,t,n,r=!0){return s.keccak(this.encodeData(e,t,n,r))},hashType(e,t){return s.keccak(this.encodeType(e,t))},sanitizeData(e){let t={};for(let n in i.properties)e[n]&&(t[n]=e[n]);return t.types&&(t.types=Object.assign({EIP712Domain:[]},t.types)),t},hash(e,t=!0){let n=this.sanitizeData(e),r=[Buffer.from("1901","hex")];return r.push(this.hashStruct("EIP712Domain",n.domain,n.types,t)),"EIP712Domain"!==n.primaryType&&r.push(this.hashStruct(n.primaryType,n.message,n.types,t)),s.keccak(Buffer.concat(r))}};e.exports={TYPED_MESSAGE_SCHEMA:i,TypedDataUtils:a,hashForSignTypedDataLegacy:function(e){return function(e){let t=Error("Expect argument to be non-empty array");if("object"!=typeof e||!e.length)throw t;let n=e.map(function(e){return"bytes"===e.type?s.toBuffer(e.value):e.value}),i=e.map(function(e){return e.type}),a=e.map(function(e){if(!e.name)throw t;return e.type+" "+e.name});return r.soliditySHA3(["bytes32","bytes32"],[r.soliditySHA3(Array(e.length).fill("string"),a),r.soliditySHA3(i,n)])}(e.data)},hashForSignTypedData_v3:function(e){return a.hash(e.data,!1)},hashForSignTypedData_v4:function(e){return a.hash(e.data)}}},689:function(e,t,n){let{keccak_256:s}=n(145);function r(e){return Buffer.allocUnsafe(e).fill(0)}function i(e,t){let n=e.toString(16);n.length%2!=0&&(n="0"+n);let s=n.match(/.{1,2}/g).map(e=>parseInt(e,16));for(;s.length<t;)s.unshift(0);return Buffer.from(s)}function a(e,t,n){let s=r(t);return(e=o(e),n)?e.length<t?(e.copy(s),s):e.slice(0,t):e.length<t?(e.copy(s,t-e.length),s):e.slice(-t)}function o(e){if(!Buffer.isBuffer(e)){if(Array.isArray(e))e=Buffer.from(e);else if("string"==typeof e){var t;e=c(e)?Buffer.from((t=l(e)).length%2?"0"+t:t,"hex"):Buffer.from(e)}else if("number"==typeof e)e=intToBuffer(e);else if(null==e)e=Buffer.allocUnsafe(0);else if("bigint"==typeof e)e=i(e);else if(e.toArray)e=Buffer.from(e.toArray());else throw Error("invalid type")}return e}function c(e){return"string"==typeof e&&e.match(/^0x[0-9A-Fa-f]*$/)}function l(e){return"string"==typeof e&&e.startsWith("0x")?e.slice(2):e}e.exports={zeros:r,setLength:a,setLengthRight:function(e,t){return a(e,t,!0)},isHexString:c,stripHexPrefix:l,toBuffer:o,bufferToHex:function(e){return"0x"+(e=o(e)).toString("hex")},keccak:function(e,t){if(e=o(e),t||(t=256),256!==t)throw Error("unsupported");return Buffer.from(s(new Uint8Array(e)))},bitLengthFromBigInt:function(e){return e.toString(2).length},bufferBEFromBigInt:i,twosFromBigInt:function(e,t){let n;return(e<0n?(~e&(1n<<BigInt(t))-1n)+1n:e)&(1n<<BigInt(t))-1n}}},453:function(e,t,n){"use strict";let s;n.r(t),n.d(t,{createCoinbaseWalletSDK:()=>t2,default:()=>t3,CoinbaseWalletSDK:()=>t0});let r=(e,t)=>{let n;switch(e){case"standard":default:return n=t,`data:image/svg+xml,%3Csvg width='${t}' height='${n}' viewBox='0 0 1024 1024' fill='none' xmlns='http://www.w3.org/2000/svg'%3E %3Crect width='1024' height='1024' fill='%230052FF'/%3E %3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M152 512C152 710.823 313.177 872 512 872C710.823 872 872 710.823 872 512C872 313.177 710.823 152 512 152C313.177 152 152 313.177 152 512ZM420 396C406.745 396 396 406.745 396 420V604C396 617.255 406.745 628 420 628H604C617.255 628 628 617.255 628 604V420C628 406.745 617.255 396 604 396H420Z' fill='white'/%3E %3C/svg%3E `;case"circle":return n=t,`data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='${t}' height='${n}' viewBox='0 0 999.81 999.81'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%230052fe;%7D.cls-2%7Bfill:%23fefefe;%7D.cls-3%7Bfill:%230152fe;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M655-115.9h56c.83,1.59,2.36.88,3.56,1a478,478,0,0,1,75.06,10.42C891.4-81.76,978.33-32.58,1049.19,44q116.7,126,131.94,297.61c.38,4.14-.34,8.53,1.78,12.45v59c-1.58.84-.91,2.35-1,3.56a482.05,482.05,0,0,1-10.38,74.05c-24,106.72-76.64,196.76-158.83,268.93s-178.18,112.82-287.2,122.6c-4.83.43-9.86-.25-14.51,1.77H654c-1-1.68-2.69-.91-4.06-1a496.89,496.89,0,0,1-105.9-18.59c-93.54-27.42-172.78-77.59-236.91-150.94Q199.34,590.1,184.87,426.58c-.47-5.19.25-10.56-1.77-15.59V355c1.68-1,.91-2.7,1-4.06a498.12,498.12,0,0,1,18.58-105.9c26-88.75,72.64-164.9,140.6-227.57q126-116.27,297.21-131.61C645.32-114.57,650.35-113.88,655-115.9Zm377.92,500c0-192.44-156.31-349.49-347.56-350.15-194.13-.68-350.94,155.13-352.29,347.42-1.37,194.55,155.51,352.1,348.56,352.47C876.15,734.23,1032.93,577.84,1032.93,384.11Z' transform='translate(-183.1 115.9)'/%3E%3Cpath class='cls-2' d='M1032.93,384.11c0,193.73-156.78,350.12-351.29,349.74-193-.37-349.93-157.92-348.56-352.47C334.43,189.09,491.24,33.28,685.37,34,876.62,34.62,1032.94,191.67,1032.93,384.11ZM683,496.81q43.74,0,87.48,0c15.55,0,25.32-9.72,25.33-25.21q0-87.48,0-175c0-15.83-9.68-25.46-25.59-25.46H595.77c-15.88,0-25.57,9.64-25.58,25.46q0,87.23,0,174.45c0,16.18,9.59,25.7,25.84,25.71Z' transform='translate(-183.1 115.9)'/%3E%3Cpath class='cls-3' d='M683,496.81H596c-16.25,0-25.84-9.53-25.84-25.71q0-87.23,0-174.45c0-15.82,9.7-25.46,25.58-25.46H770.22c15.91,0,25.59,9.63,25.59,25.46q0,87.47,0,175c0,15.49-9.78,25.2-25.33,25.21Q726.74,496.84,683,496.81Z' transform='translate(-183.1 115.9)'/%3E%3C/svg%3E`;case"text":return n=(.1*t).toFixed(2),`data:image/svg+xml,%3Csvg width='${t}' height='${n}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 528.15 53.64'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%230052ff;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3ECoinbase_Wordmark_SubBrands_ALL%3C/title%3E%3Cpath class='cls-1' d='M164.45,15a15,15,0,0,0-11.74,5.4V0h-8.64V52.92h8.5V48a15,15,0,0,0,11.88,5.62c10.37,0,18.21-8.21,18.21-19.3S174.67,15,164.45,15Zm-1.3,30.67c-6.19,0-10.73-4.83-10.73-11.31S157,23,163.22,23s10.66,4.82,10.66,11.37S169.34,45.65,163.15,45.65Zm83.31-14.91-6.34-.93c-3-.43-5.18-1.44-5.18-3.82,0-2.59,2.8-3.89,6.62-3.89,4.18,0,6.84,1.8,7.42,4.76h8.35c-.94-7.49-6.7-11.88-15.55-11.88-9.15,0-15.2,4.68-15.2,11.3,0,6.34,4,10,12,11.16l6.33.94c3.1.43,4.83,1.65,4.83,4,0,2.95-3,4.17-7.2,4.17-5.12,0-8-2.09-8.43-5.25h-8.49c.79,7.27,6.48,12.38,16.84,12.38,9.44,0,15.7-4.32,15.7-11.74C258.12,35.28,253.58,31.82,246.46,30.74Zm-27.65-2.3c0-8.06-4.9-13.46-15.27-13.46-9.79,0-15.26,5-16.34,12.6h8.57c.43-3,2.73-5.4,7.63-5.4,4.39,0,6.55,1.94,6.55,4.32,0,3.09-4,3.88-8.85,4.39-6.63.72-14.84,3-14.84,11.66,0,6.7,5,11,12.89,11,6.19,0,10.08-2.59,12-6.7.28,3.67,3,6.05,6.84,6.05h5v-7.7h-4.25Zm-8.5,9.36c0,5-4.32,8.64-9.57,8.64-3.24,0-6-1.37-6-4.25,0-3.67,4.39-4.68,8.42-5.11s6-1.22,7.13-2.88ZM281.09,15c-11.09,0-19.23,8.35-19.23,19.36,0,11.6,8.72,19.3,19.37,19.3,9,0,16.06-5.33,17.86-12.89h-9c-1.3,3.31-4.47,5.19-8.71,5.19-5.55,0-9.72-3.46-10.66-9.51H299.3V33.12C299.3,22.46,291.53,15,281.09,15Zm-9.87,15.26c1.37-5.18,5.26-7.7,9.72-7.7,4.9,0,8.64,2.8,9.51,7.7ZM19.3,23a9.84,9.84,0,0,1,9.5,7h9.14c-1.65-8.93-9-15-18.57-15A19,19,0,0,0,0,34.34c0,11.09,8.28,19.3,19.37,19.3,9.36,0,16.85-6,18.5-15H28.8a9.75,9.75,0,0,1-9.43,7.06c-6.27,0-10.66-4.83-10.66-11.31S13,23,19.3,23Zm41.11-8A19,19,0,0,0,41,34.34c0,11.09,8.28,19.3,19.37,19.3A19,19,0,0,0,79.92,34.27C79.92,23.33,71.64,15,60.41,15Zm.07,30.67c-6.19,0-10.73-4.83-10.73-11.31S54.22,23,60.41,23s10.8,4.89,10.8,11.37S66.67,45.65,60.48,45.65ZM123.41,15c-5.62,0-9.29,2.3-11.45,5.54V15.7h-8.57V52.92H112V32.69C112,27,115.63,23,121,23c5,0,8.06,3.53,8.06,8.64V52.92h8.64V31C137.66,21.6,132.84,15,123.41,15ZM92,.36a5.36,5.36,0,0,0-5.55,5.47,5.55,5.55,0,0,0,11.09,0A5.35,5.35,0,0,0,92,.36Zm-9.72,23h5.4V52.92h8.64V15.7h-14Zm298.17-7.7L366.2,52.92H372L375.29,44H392l3.33,8.88h6L386.87,15.7ZM377,39.23l6.45-17.56h.1l6.56,17.56ZM362.66,15.7l-7.88,29h-.11l-8.14-29H341l-8,28.93h-.1l-8-28.87H319L329.82,53h5.45l8.19-29.24h.11L352,53h5.66L368.1,15.7Zm135.25,0v4.86h12.32V52.92h5.6V20.56h12.32V15.7ZM467.82,52.92h25.54V48.06H473.43v-12h18.35V31.35H473.43V20.56h19.93V15.7H467.82ZM443,15.7h-5.6V52.92h24.32V48.06H443Zm-30.45,0h-5.61V52.92h24.32V48.06H412.52Z'/%3E%3C/svg%3E`;case"textWithLogo":return n=(.25*t).toFixed(2),`data:image/svg+xml,%3Csvg width='${t}' height='${n}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 308.44 77.61'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%230052ff;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M142.94,20.2l-7.88,29H135l-8.15-29h-5.55l-8,28.93h-.11l-8-28.87H99.27l10.84,37.27h5.44l8.2-29.24h.1l8.41,29.24h5.66L148.39,20.2Zm17.82,0L146.48,57.42h5.82l3.28-8.88h16.65l3.34,8.88h6L167.16,20.2Zm-3.44,23.52,6.45-17.55h.11l6.56,17.55ZM278.2,20.2v4.86h12.32V57.42h5.6V25.06h12.32V20.2ZM248.11,57.42h25.54V52.55H253.71V40.61h18.35V35.85H253.71V25.06h19.94V20.2H248.11ZM223.26,20.2h-5.61V57.42H242V52.55H223.26Zm-30.46,0h-5.6V57.42h24.32V52.55H192.8Zm-154,38A19.41,19.41,0,1,1,57.92,35.57H77.47a38.81,38.81,0,1,0,0,6.47H57.92A19.39,19.39,0,0,1,38.81,58.21Z'/%3E%3C/svg%3E`;case"textLight":return n=(.1*t).toFixed(2),`data:image/svg+xml,%3Csvg width='${t}' height='${n}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 528.15 53.64'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23fefefe;%7D%3C/style%3E%3C/defs%3E%3Ctitle%3ECoinbase_Wordmark_SubBrands_ALL%3C/title%3E%3Cpath class='cls-1' d='M164.45,15a15,15,0,0,0-11.74,5.4V0h-8.64V52.92h8.5V48a15,15,0,0,0,11.88,5.62c10.37,0,18.21-8.21,18.21-19.3S174.67,15,164.45,15Zm-1.3,30.67c-6.19,0-10.73-4.83-10.73-11.31S157,23,163.22,23s10.66,4.82,10.66,11.37S169.34,45.65,163.15,45.65Zm83.31-14.91-6.34-.93c-3-.43-5.18-1.44-5.18-3.82,0-2.59,2.8-3.89,6.62-3.89,4.18,0,6.84,1.8,7.42,4.76h8.35c-.94-7.49-6.7-11.88-15.55-11.88-9.15,0-15.2,4.68-15.2,11.3,0,6.34,4,10,12,11.16l6.33.94c3.1.43,4.83,1.65,4.83,4,0,2.95-3,4.17-7.2,4.17-5.12,0-8-2.09-8.43-5.25h-8.49c.79,7.27,6.48,12.38,16.84,12.38,9.44,0,15.7-4.32,15.7-11.74C258.12,35.28,253.58,31.82,246.46,30.74Zm-27.65-2.3c0-8.06-4.9-13.46-15.27-13.46-9.79,0-15.26,5-16.34,12.6h8.57c.43-3,2.73-5.4,7.63-5.4,4.39,0,6.55,1.94,6.55,4.32,0,3.09-4,3.88-8.85,4.39-6.63.72-14.84,3-14.84,11.66,0,6.7,5,11,12.89,11,6.19,0,10.08-2.59,12-6.7.28,3.67,3,6.05,6.84,6.05h5v-7.7h-4.25Zm-8.5,9.36c0,5-4.32,8.64-9.57,8.64-3.24,0-6-1.37-6-4.25,0-3.67,4.39-4.68,8.42-5.11s6-1.22,7.13-2.88ZM281.09,15c-11.09,0-19.23,8.35-19.23,19.36,0,11.6,8.72,19.3,19.37,19.3,9,0,16.06-5.33,17.86-12.89h-9c-1.3,3.31-4.47,5.19-8.71,5.19-5.55,0-9.72-3.46-10.66-9.51H299.3V33.12C299.3,22.46,291.53,15,281.09,15Zm-9.87,15.26c1.37-5.18,5.26-7.7,9.72-7.7,4.9,0,8.64,2.8,9.51,7.7ZM19.3,23a9.84,9.84,0,0,1,9.5,7h9.14c-1.65-8.93-9-15-18.57-15A19,19,0,0,0,0,34.34c0,11.09,8.28,19.3,19.37,19.3,9.36,0,16.85-6,18.5-15H28.8a9.75,9.75,0,0,1-9.43,7.06c-6.27,0-10.66-4.83-10.66-11.31S13,23,19.3,23Zm41.11-8A19,19,0,0,0,41,34.34c0,11.09,8.28,19.3,19.37,19.3A19,19,0,0,0,79.92,34.27C79.92,23.33,71.64,15,60.41,15Zm.07,30.67c-6.19,0-10.73-4.83-10.73-11.31S54.22,23,60.41,23s10.8,4.89,10.8,11.37S66.67,45.65,60.48,45.65ZM123.41,15c-5.62,0-9.29,2.3-11.45,5.54V15.7h-8.57V52.92H112V32.69C112,27,115.63,23,121,23c5,0,8.06,3.53,8.06,8.64V52.92h8.64V31C137.66,21.6,132.84,15,123.41,15ZM92,.36a5.36,5.36,0,0,0-5.55,5.47,5.55,5.55,0,0,0,11.09,0A5.35,5.35,0,0,0,92,.36Zm-9.72,23h5.4V52.92h8.64V15.7h-14Zm298.17-7.7L366.2,52.92H372L375.29,44H392l3.33,8.88h6L386.87,15.7ZM377,39.23l6.45-17.56h.1l6.56,17.56ZM362.66,15.7l-7.88,29h-.11l-8.14-29H341l-8,28.93h-.1l-8-28.87H319L329.82,53h5.45l8.19-29.24h.11L352,53h5.66L368.1,15.7Zm135.25,0v4.86h12.32V52.92h5.6V20.56h12.32V15.7ZM467.82,52.92h25.54V48.06H473.43v-12h18.35V31.35H473.43V20.56h19.93V15.7H467.82ZM443,15.7h-5.6V52.92h24.32V48.06H443Zm-30.45,0h-5.61V52.92h24.32V48.06H412.52Z'/%3E%3C/svg%3E`;case"textWithLogoLight":return n=(.25*t).toFixed(2),`data:image/svg+xml,%3Csvg width='${t}' height='${n}' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 308.44 77.61'%3E%3Cdefs%3E%3Cstyle%3E.cls-1%7Bfill:%23fefefe;%7D%3C/style%3E%3C/defs%3E%3Cpath class='cls-1' d='M142.94,20.2l-7.88,29H135l-8.15-29h-5.55l-8,28.93h-.11l-8-28.87H99.27l10.84,37.27h5.44l8.2-29.24h.1l8.41,29.24h5.66L148.39,20.2Zm17.82,0L146.48,57.42h5.82l3.28-8.88h16.65l3.34,8.88h6L167.16,20.2Zm-3.44,23.52,6.45-17.55h.11l6.56,17.55ZM278.2,20.2v4.86h12.32V57.42h5.6V25.06h12.32V20.2ZM248.11,57.42h25.54V52.55H253.71V40.61h18.35V35.85H253.71V25.06h19.94V20.2H248.11ZM223.26,20.2h-5.61V57.42H242V52.55H223.26Zm-30.46,0h-5.6V57.42h24.32V52.55H192.8Zm-154,38A19.41,19.41,0,1,1,57.92,35.57H77.47a38.81,38.81,0,1,0,0,6.47H57.92A19.39,19.39,0,0,1,38.81,58.21Z'/%3E%3C/svg%3E`}};class i{constructor(e,t){this.scope=e,this.module=t}storeObject(e,t){this.setItem(e,JSON.stringify(t))}loadObject(e){let t=this.getItem(e);return t?JSON.parse(t):void 0}setItem(e,t){localStorage.setItem(this.scopedKey(e),t)}getItem(e){return localStorage.getItem(this.scopedKey(e))}removeItem(e){localStorage.removeItem(this.scopedKey(e))}clear(){let e=this.scopedKey(""),t=[];for(let n=0;n<localStorage.length;n++){let s=localStorage.key(n);"string"==typeof s&&s.startsWith(e)&&t.push(s)}t.forEach(e=>localStorage.removeItem(e))}scopedKey(e){return`-${this.scope}${this.module?`:${this.module}`:""}:${e}`}static clearAll(){new i("CBWSDK").clear(),new i("walletlink").clear()}}let a={rpc:{invalidInput:-32e3,resourceNotFound:-32001,resourceUnavailable:-32002,transactionRejected:-32003,methodNotSupported:-32004,limitExceeded:-32005,parse:-32700,invalidRequest:-32600,methodNotFound:-32601,invalidParams:-32602,internal:-32603},provider:{userRejectedRequest:4001,unauthorized:4100,unsupportedMethod:4200,disconnected:4900,chainDisconnected:4901,unsupportedChain:4902}},o={"-32700":{standard:"JSON RPC 2.0",message:"Invalid JSON was received by the server. An error occurred on the server while parsing the JSON text."},"-32600":{standard:"JSON RPC 2.0",message:"The JSON sent is not a valid Request object."},"-32601":{standard:"JSON RPC 2.0",message:"The method does not exist / is not available."},"-32602":{standard:"JSON RPC 2.0",message:"Invalid method parameter(s)."},"-32603":{standard:"JSON RPC 2.0",message:"Internal JSON-RPC error."},"-32000":{standard:"EIP-1474",message:"Invalid input."},"-32001":{standard:"EIP-1474",message:"Resource not found."},"-32002":{standard:"EIP-1474",message:"Resource unavailable."},"-32003":{standard:"EIP-1474",message:"Transaction rejected."},"-32004":{standard:"EIP-1474",message:"Method not supported."},"-32005":{standard:"EIP-1474",message:"Request limit exceeded."},4001:{standard:"EIP-1193",message:"User rejected the request."},4100:{standard:"EIP-1193",message:"The requested account and/or method has not been authorized by the user."},4200:{standard:"EIP-1193",message:"The requested method is not supported by this Ethereum provider."},4900:{standard:"EIP-1193",message:"The provider is disconnected from all chains."},4901:{standard:"EIP-1193",message:"The provider is disconnected from the specified chain."},4902:{standard:"EIP-3085",message:"Unrecognized chain ID."}},c="Unspecified error message.";function l(e,t=c){if(e&&Number.isInteger(e)){var n;let t=e.toString();if(d(o,t))return o[t].message;if((n=e)>=-32099&&n<=-32e3)return"Unspecified server error."}return t}function h(e){return e&&"object"==typeof e&&!Array.isArray(e)?Object.assign({},e):e}function d(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function u(e,t){return"object"==typeof e&&null!==e&&t in e&&"string"==typeof e[t]}let p={rpc:{parse:e=>f(a.rpc.parse,e),invalidRequest:e=>f(a.rpc.invalidRequest,e),invalidParams:e=>f(a.rpc.invalidParams,e),methodNotFound:e=>f(a.rpc.methodNotFound,e),internal:e=>f(a.rpc.internal,e),server:e=>{if(!e||"object"!=typeof e||Array.isArray(e))throw Error("Ethereum RPC Server errors must provide single object argument.");let{code:t}=e;if(!Number.isInteger(t)||t>-32005||t<-32099)throw Error('"code" must be an integer such that: -32099 <= code <= -32005');return f(t,e)},invalidInput:e=>f(a.rpc.invalidInput,e),resourceNotFound:e=>f(a.rpc.resourceNotFound,e),resourceUnavailable:e=>f(a.rpc.resourceUnavailable,e),transactionRejected:e=>f(a.rpc.transactionRejected,e),methodNotSupported:e=>f(a.rpc.methodNotSupported,e),limitExceeded:e=>f(a.rpc.limitExceeded,e)},provider:{userRejectedRequest:e=>g(a.provider.userRejectedRequest,e),unauthorized:e=>g(a.provider.unauthorized,e),unsupportedMethod:e=>g(a.provider.unsupportedMethod,e),disconnected:e=>g(a.provider.disconnected,e),chainDisconnected:e=>g(a.provider.chainDisconnected,e),unsupportedChain:e=>g(a.provider.unsupportedChain,e),custom:e=>{if(!e||"object"!=typeof e||Array.isArray(e))throw Error("Ethereum Provider custom errors must provide single object argument.");let{code:t,message:n,data:s}=e;if(!n||"string"!=typeof n)throw Error('"message" must be a nonempty string');return new _(t,n,s)}}};function f(e,t){let[n,s]=m(t);return new y(e,n||l(e),s)}function g(e,t){let[n,s]=m(t);return new _(e,n||l(e),s)}function m(e){if(e){if("string"==typeof e)return[e];if("object"==typeof e&&!Array.isArray(e)){let{message:t,data:n}=e;if(t&&"string"!=typeof t)throw Error("Must specify string message.");return[t||void 0,n]}}return[]}class y extends Error{constructor(e,t,n){if(!Number.isInteger(e))throw Error('"code" must be an integer.');if(!t||"string"!=typeof t)throw Error('"message" must be a nonempty string.');super(t),this.code=e,void 0!==n&&(this.data=n)}}class _ extends y{constructor(e,t,n){var s;if(!(Number.isInteger(s=e)&&s>=1e3&&s<=4999))throw Error('"code" must be an integer such that: 1000 <= code <= 4999');super(e,t,n)}}let b=e=>e,w=e=>e,v=e=>e;function k(e){return Math.floor(e)}let x=/^[0-9]*$/,C=/^[a-f0-9]*$/;function E(e){return I(crypto.getRandomValues(new Uint8Array(e)))}function I(e){return[...e].map(e=>e.toString(16).padStart(2,"0")).join("")}function S(e){return new Uint8Array(e.match(/.{1,2}/g).map(e=>Number.parseInt(e,16)))}function M(e,t=!1){let n=e.toString("hex");return b(t?`0x${n}`:n)}function L(e){return M(U(e),!0)}function A(e){return v(e.toString(10))}function P(e){return b(`0x${BigInt(e).toString(16)}`)}function D(e){return e.startsWith("0x")||e.startsWith("0X")}function N(e){return D(e)?e.slice(2):e}function T(e){return D(e)?`0x${e.slice(2)}`:`0x${e}`}function O(e){if("string"!=typeof e)return!1;let t=N(e).toLowerCase();return C.test(t)}function R(e,t=!1){let n=function(e,t=!1){if("string"==typeof e){let n=N(e).toLowerCase();if(C.test(n))return b(t?`0x${n}`:n)}throw p.rpc.invalidParams(`"${String(e)}" is not a hexadecimal string`)}(e,!1);return n.length%2==1&&(n=b(`0${n}`)),t?b(`0x${n}`):n}function j(e){if("string"==typeof e){let t=N(e).toLowerCase();if(O(t)&&40===t.length)return w(T(t))}throw p.rpc.invalidParams(`Invalid Ethereum address: ${String(e)}`)}function U(e){if(Buffer.isBuffer(e))return e;if("string"==typeof e){if(O(e)){let t=R(e,!1);return Buffer.from(t,"hex")}return Buffer.from(e,"utf8")}throw p.rpc.invalidParams(`Not binary data: ${String(e)}`)}function B(e){if("number"==typeof e&&Number.isInteger(e))return k(e);if("string"==typeof e){if(x.test(e))return k(Number(e));if(O(e))return k(Number(BigInt(R(e,!0))))}throw p.rpc.invalidParams(`Not an integer: ${String(e)}`)}function H(e){if(null!==e&&("bigint"==typeof e||function(e){if(null==e||"function"!=typeof e.constructor)return!1;let{constructor:t}=e;return"function"==typeof t.config&&"number"==typeof t.EUCLID}(e)))return BigInt(e.toString(10));if("number"==typeof e)return BigInt(B(e));if("string"==typeof e){if(x.test(e))return BigInt(e);if(O(e))return BigInt(R(e,!0))}throw p.rpc.invalidParams(`Not an integer: ${String(e)}`)}async function W(){return crypto.subtle.generateKey({name:"ECDH",namedCurve:"P-256"},!0,["deriveKey"])}async function q(e,t){return crypto.subtle.deriveKey({name:"ECDH",public:t},e,{name:"AES-GCM",length:256},!1,["encrypt","decrypt"])}async function V(e,t){let n=crypto.getRandomValues(new Uint8Array(12)),s=await crypto.subtle.encrypt({name:"AES-GCM",iv:n},e,new TextEncoder().encode(t));return{iv:n,cipherText:s}}async function Z(e,{iv:t,cipherText:n}){let s=await crypto.subtle.decrypt({name:"AES-GCM",iv:t},e,n);return new TextDecoder().decode(s)}function z(e){switch(e){case"public":return"spki";case"private":return"pkcs8"}}async function F(e,t){let n=z(e);return I(new Uint8Array(await crypto.subtle.exportKey(n,t)))}async function K(e,t){let n=z(e),s=S(t).buffer;return await crypto.subtle.importKey(n,new Uint8Array(s),{name:"ECDH",namedCurve:"P-256"},!0,"private"===e?["deriveKey"]:[])}async function $(e,t){return V(t,JSON.stringify(e,(e,t)=>t instanceof Error?Object.assign(Object.assign({},t.code?{code:t.code}:{}),{message:t.message}):t))}async function G(e,t){return JSON.parse(await Z(t,e))}let J={storageKey:"ownPrivateKey",keyType:"private"},Y={storageKey:"ownPublicKey",keyType:"public"},Q={storageKey:"peerPublicKey",keyType:"public"};class X{constructor(){this.storage=new i("CBWSDK","SCWKeyManager"),this.ownPrivateKey=null,this.ownPublicKey=null,this.peerPublicKey=null,this.sharedSecret=null}async getOwnPublicKey(){return await this.loadKeysIfNeeded(),this.ownPublicKey}async getSharedSecret(){return await this.loadKeysIfNeeded(),this.sharedSecret}async setPeerPublicKey(e){this.sharedSecret=null,this.peerPublicKey=e,await this.storeKey(Q,e),await this.loadKeysIfNeeded()}async clear(){this.ownPrivateKey=null,this.ownPublicKey=null,this.peerPublicKey=null,this.sharedSecret=null,this.storage.removeItem(Y.storageKey),this.storage.removeItem(J.storageKey),this.storage.removeItem(Q.storageKey)}async generateKeyPair(){let e=await W();this.ownPrivateKey=e.privateKey,this.ownPublicKey=e.publicKey,await this.storeKey(J,e.privateKey),await this.storeKey(Y,e.publicKey)}async loadKeysIfNeeded(){null===this.ownPrivateKey&&(this.ownPrivateKey=await this.loadKey(J)),null===this.ownPublicKey&&(this.ownPublicKey=await this.loadKey(Y)),(null===this.ownPrivateKey||null===this.ownPublicKey)&&await this.generateKeyPair(),null===this.peerPublicKey&&(this.peerPublicKey=await this.loadKey(Q)),null===this.sharedSecret&&null!==this.ownPrivateKey&&null!==this.peerPublicKey&&(this.sharedSecret=await q(this.ownPrivateKey,this.peerPublicKey))}async loadKey(e){let t=this.storage.getItem(e.storageKey);return t?K(e.keyType,t):null}async storeKey(e,t){let n=await F(e.keyType,t);this.storage.setItem(e.storageKey,n)}}let ee="4.3.0",et="@coinbase/wallet-sdk";async function en(e,t){let n=Object.assign(Object.assign({},e),{jsonrpc:"2.0",id:crypto.randomUUID()}),s=await window.fetch(t,{method:"POST",body:JSON.stringify(n),mode:"cors",headers:{"Content-Type":"application/json","X-Cbw-Sdk-Version":ee,"X-Cbw-Sdk-Platform":et}}),{result:r,error:i}=await s.json();if(i)throw i;return r}function es({metadata:e,preference:t}){var n,s;let{appName:r,appLogoUrl:i,appChainIds:a}=e;if("smartWalletOnly"!==t.options){let e=globalThis.coinbaseWalletExtension;if(e)return null===(n=e.setAppInfo)||void 0===n||n.call(e,r,i,a,t),e}let o=function(){var e,t;try{let n=globalThis;return null!==(e=n.ethereum)&&void 0!==e?e:null===(t=n.top)||void 0===t?void 0:t.ethereum}catch(e){return}}();if(null==o?void 0:o.isCoinbaseBrowser)return null===(s=o.setAppInfo)||void 0===s||s.call(o,r,i,a,t),o}let er="accounts",ei="activeChain",ea="availableChains",eo="walletCapabilities";class ec{constructor(e){var t,n,s;this.metadata=e.metadata,this.communicator=e.communicator,this.callback=e.callback,this.keyManager=new X,this.storage=new i("CBWSDK","SCWStateManager"),this.accounts=null!==(t=this.storage.loadObject(er))&&void 0!==t?t:[],this.chain=this.storage.loadObject(ei)||{id:null!==(s=null===(n=e.metadata.appChainIds)||void 0===n?void 0:n[0])&&void 0!==s?s:1},this.handshake=this.handshake.bind(this),this.request=this.request.bind(this),this.createRequestMessage=this.createRequestMessage.bind(this),this.decryptResponseMessage=this.decryptResponseMessage.bind(this)}async handshake(e){var t,n,s,r;await (null===(n=(t=this.communicator).waitForPopupLoaded)||void 0===n?void 0:n.call(t));let i=await this.createRequestMessage({handshake:{method:e.method,params:Object.assign({},this.metadata,null!==(s=e.params)&&void 0!==s?s:{})}}),a=await this.communicator.postRequestAndWaitForResponse(i);if("failure"in a.content)throw a.content.failure;let o=await K("public",a.sender);await this.keyManager.setPeerPublicKey(o);let c=(await this.decryptResponseMessage(a)).result;if("error"in c)throw c.error;if("eth_requestAccounts"===e.method){let e=c.value;this.accounts=e,this.storage.storeObject(er,e),null===(r=this.callback)||void 0===r||r.call(this,"accountsChanged",e)}}async request(e){var t;if(0===this.accounts.length){if("wallet_sendCalls"===e.method)return this.sendRequestToPopup(e);throw p.provider.unauthorized()}switch(e.method){case"eth_requestAccounts":return null===(t=this.callback)||void 0===t||t.call(this,"connect",{chainId:P(this.chain.id)}),this.accounts;case"eth_accounts":return this.accounts;case"eth_coinbase":return this.accounts[0];case"net_version":return this.chain.id;case"eth_chainId":return P(this.chain.id);case"wallet_getCapabilities":return this.storage.loadObject(eo);case"wallet_switchEthereumChain":return this.handleSwitchChainRequest(e);case"eth_ecRecover":case"personal_sign":case"wallet_sign":case"personal_ecRecover":case"eth_signTransaction":case"eth_sendTransaction":case"eth_signTypedData_v1":case"eth_signTypedData_v3":case"eth_signTypedData_v4":case"eth_signTypedData":case"wallet_addEthereumChain":case"wallet_watchAsset":case"wallet_sendCalls":case"wallet_showCallsStatus":case"wallet_grantPermissions":return this.sendRequestToPopup(e);default:if(!this.chain.rpcUrl)throw p.rpc.internal("No RPC URL set for chain");return en(e,this.chain.rpcUrl)}}async sendRequestToPopup(e){var t,n;await (null===(n=(t=this.communicator).waitForPopupLoaded)||void 0===n?void 0:n.call(t));let s=await this.sendEncryptedRequest(e),r=(await this.decryptResponseMessage(s)).result;if("error"in r)throw r.error;return r.value}async cleanup(){var e,t;this.storage.clear(),await this.keyManager.clear(),this.accounts=[],this.chain={id:null!==(t=null===(e=this.metadata.appChainIds)||void 0===e?void 0:e[0])&&void 0!==t?t:1}}async handleSwitchChainRequest(e){var t;let n=e.params;if(!n||!(null===(t=n[0])||void 0===t?void 0:t.chainId))throw p.rpc.invalidParams();let s=B(n[0].chainId);if(this.updateChain(s))return null;let r=await this.sendRequestToPopup(e);return null===r&&this.updateChain(s),r}async sendEncryptedRequest(e){let t=await this.keyManager.getSharedSecret();if(!t)throw p.provider.unauthorized("No valid session found, try requestAccounts before other methods");let n=await $({action:e,chainId:this.chain.id},t),s=await this.createRequestMessage({encrypted:n});return this.communicator.postRequestAndWaitForResponse(s)}async createRequestMessage(e){let t=await F("public",await this.keyManager.getOwnPublicKey());return{id:crypto.randomUUID(),sender:t,content:e,timestamp:new Date}}async decryptResponseMessage(e){var t,n;let s=e.content;if("failure"in s)throw s.failure;let r=await this.keyManager.getSharedSecret();if(!r)throw p.provider.unauthorized("Invalid session");let i=await G(s.encrypted,r),a=null===(t=i.data)||void 0===t?void 0:t.chains;if(a){let e=Object.entries(a).map(([e,t])=>({id:Number(e),rpcUrl:t}));this.storage.storeObject(ea,e),this.updateChain(this.chain.id,e)}let o=null===(n=i.data)||void 0===n?void 0:n.capabilities;return o&&this.storage.storeObject(eo,o),i}updateChain(e,t){var n;let s=null!=t?t:this.storage.loadObject(ea),r=null==s?void 0:s.find(t=>t.id===e);return!!r&&(r!==this.chain&&(this.chain=r,this.storage.storeObject(ei,r),null===(n=this.callback)||void 0===n||n.call(this,"chainChanged",P(r.id))),!0)}}var el=n(50);let eh="Addresses";function ed(e){return void 0!==e.errorMessage}class eu{constructor(e){this.secret=e}async encrypt(e){let t=this.secret;if(64!==t.length)throw Error("secret must be 256 bits");let n=crypto.getRandomValues(new Uint8Array(12)),s=await crypto.subtle.importKey("raw",S(t),{name:"aes-gcm"},!1,["encrypt","decrypt"]),r=new TextEncoder,i=await window.crypto.subtle.encrypt({name:"AES-GCM",iv:n},s,r.encode(e)),a=i.slice(i.byteLength-16),o=i.slice(0,i.byteLength-16),c=new Uint8Array(a),l=new Uint8Array(o);return I(new Uint8Array([...n,...c,...l]))}async decrypt(e){let t=this.secret;if(64!==t.length)throw Error("secret must be 256 bits");return new Promise((n,s)=>{!async function(){let r=await crypto.subtle.importKey("raw",S(t),{name:"aes-gcm"},!1,["encrypt","decrypt"]),i=S(e),a=i.slice(0,12),o=i.slice(12,28),c=new Uint8Array([...i.slice(28),...o]),l={name:"AES-GCM",iv:new Uint8Array(a)};try{let e=await window.crypto.subtle.decrypt(l,r,c),t=new TextDecoder;n(t.decode(e))}catch(e){s(e)}}()})}}class ep{constructor(e,t,n){this.linkAPIUrl=e,this.sessionId=t;let s=`${t}:${n}`;this.auth=`Basic ${btoa(s)}`}async markUnseenEventsAsSeen(e){return Promise.all(e.map(e=>fetch(`${this.linkAPIUrl}/events/${e.eventId}/seen`,{method:"POST",headers:{Authorization:this.auth}}))).catch(e=>console.error("Unabled to mark event as failed:",e))}async fetchUnseenEvents(){var e;let t=await fetch(`${this.linkAPIUrl}/events?unseen=true`,{headers:{Authorization:this.auth}});if(t.ok){let{events:n,error:s}=await t.json();if(s)throw Error(`Check unseen events failed: ${s}`);let r=null!==(e=null==n?void 0:n.filter(e=>"Web3Response"===e.event).map(e=>({type:"Event",sessionId:this.sessionId,eventId:e.id,event:e.event,data:e.data})))&&void 0!==e?e:[];return this.markUnseenEventsAsSeen(r),r}throw Error(`Check unseen events failed: ${t.status}`)}}(eR=ej||(ej={}))[eR.DISCONNECTED=0]="DISCONNECTED",eR[eR.CONNECTING=1]="CONNECTING",eR[eR.CONNECTED=2]="CONNECTED";class ef{setConnectionStateListener(e){this.connectionStateListener=e}setIncomingDataListener(e){this.incomingDataListener=e}constructor(e,t=WebSocket){this.WebSocketClass=t,this.webSocket=null,this.pendingData=[],this.url=e.replace(/^http/,"ws")}async connect(){if(this.webSocket)throw Error("webSocket object is not null");return new Promise((e,t)=>{var n;let s;try{this.webSocket=s=new this.WebSocketClass(this.url)}catch(e){t(e);return}null===(n=this.connectionStateListener)||void 0===n||n.call(this,ej.CONNECTING),s.onclose=e=>{var n;this.clearWebSocket(),t(Error(`websocket error ${e.code}: ${e.reason}`)),null===(n=this.connectionStateListener)||void 0===n||n.call(this,ej.DISCONNECTED)},s.onopen=t=>{var n;e(),null===(n=this.connectionStateListener)||void 0===n||n.call(this,ej.CONNECTED),this.pendingData.length>0&&([...this.pendingData].forEach(e=>this.sendData(e)),this.pendingData=[])},s.onmessage=e=>{var t,n;if("h"===e.data)null===(t=this.incomingDataListener)||void 0===t||t.call(this,{type:"Heartbeat"});else try{let t=JSON.parse(e.data);null===(n=this.incomingDataListener)||void 0===n||n.call(this,t)}catch(e){}}})}disconnect(){var e;let{webSocket:t}=this;if(t){this.clearWebSocket(),null===(e=this.connectionStateListener)||void 0===e||e.call(this,ej.DISCONNECTED),this.connectionStateListener=void 0,this.incomingDataListener=void 0;try{t.close()}catch(e){}}}sendData(e){let{webSocket:t}=this;if(!t){this.pendingData.push(e),this.connect();return}t.send(e)}clearWebSocket(){let{webSocket:e}=this;e&&(this.webSocket=null,e.onclose=null,e.onerror=null,e.onmessage=null,e.onopen=null)}}class eg{constructor({session:e,linkAPIUrl:t,listener:n}){this.destroyed=!1,this.lastHeartbeatResponse=0,this.nextReqId=k(1),this._connected=!1,this._linked=!1,this.shouldFetchUnseenEventsOnConnect=!1,this.requestResolutions=new Map,this.handleSessionMetadataUpdated=e=>{e&&new Map([["__destroyed",this.handleDestroyed],["EthereumAddress",this.handleAccountUpdated],["WalletUsername",this.handleWalletUsernameUpdated],["AppVersion",this.handleAppVersionUpdated],["ChainId",t=>e.JsonRpcUrl&&this.handleChainUpdated(t,e.JsonRpcUrl)]]).forEach((t,n)=>{let s=e[n];void 0!==s&&t(s)})},this.handleDestroyed=e=>{var t;"1"===e&&(null===(t=this.listener)||void 0===t||t.resetAndReload())},this.handleAccountUpdated=async e=>{var t;let n=await this.cipher.decrypt(e);null===(t=this.listener)||void 0===t||t.accountUpdated(n)},this.handleMetadataUpdated=async(e,t)=>{var n;let s=await this.cipher.decrypt(t);null===(n=this.listener)||void 0===n||n.metadataUpdated(e,s)},this.handleWalletUsernameUpdated=async e=>{this.handleMetadataUpdated("walletUsername",e)},this.handleAppVersionUpdated=async e=>{this.handleMetadataUpdated("AppVersion",e)},this.handleChainUpdated=async(e,t)=>{var n;let s=await this.cipher.decrypt(e),r=await this.cipher.decrypt(t);null===(n=this.listener)||void 0===n||n.chainUpdated(s,r)},this.session=e,this.cipher=new eu(e.secret),this.listener=n;let s=new ef(`${t}/rpc`,WebSocket);s.setConnectionStateListener(async e=>{let t=!1;switch(e){case ej.DISCONNECTED:if(!this.destroyed){let e=async()=>{await new Promise(e=>setTimeout(e,5e3)),this.destroyed||s.connect().catch(()=>{e()})};e()}break;case ej.CONNECTED:t=await this.handleConnected(),this.updateLastHeartbeat(),setInterval(()=>{this.heartbeat()},1e4),this.shouldFetchUnseenEventsOnConnect&&this.fetchUnseenEventsAPI();case ej.CONNECTING:}this.connected!==t&&(this.connected=t)}),s.setIncomingDataListener(e=>{var t;switch(e.type){case"Heartbeat":this.updateLastHeartbeat();return;case"IsLinkedOK":case"Linked":{let t="IsLinkedOK"===e.type?e.linked:void 0;this.linked=t||e.onlineGuests>0;break}case"GetSessionConfigOK":case"SessionConfigUpdated":this.handleSessionMetadataUpdated(e.metadata);break;case"Event":this.handleIncomingEvent(e)}void 0!==e.id&&(null===(t=this.requestResolutions.get(e.id))||void 0===t||t(e))}),this.ws=s,this.http=new ep(t,e.id,e.key)}connect(){if(this.destroyed)throw Error("instance is destroyed");this.ws.connect()}async destroy(){this.destroyed||(await this.makeRequest({type:"SetSessionConfig",id:k(this.nextReqId++),sessionId:this.session.id,metadata:{__destroyed:"1"}},{timeout:1e3}),this.destroyed=!0,this.ws.disconnect(),this.listener=void 0)}get connected(){return this._connected}set connected(e){this._connected=e}get linked(){return this._linked}set linked(e){var t,n;this._linked=e,e&&(null===(t=this.onceLinked)||void 0===t||t.call(this)),null===(n=this.listener)||void 0===n||n.linkedUpdated(e)}setOnceLinked(e){return new Promise(t=>{this.linked?e().then(t):this.onceLinked=()=>{e().then(t),this.onceLinked=void 0}})}async handleIncomingEvent(e){var t;if("Event"!==e.type||"Web3Response"!==e.event)return;let n=JSON.parse(await this.cipher.decrypt(e.data));if("WEB3_RESPONSE"!==n.type)return;let{id:s,response:r}=n;null===(t=this.listener)||void 0===t||t.handleWeb3ResponseMessage(s,r)}async checkUnseenEvents(){if(!this.connected){this.shouldFetchUnseenEventsOnConnect=!0;return}await new Promise(e=>setTimeout(e,250));try{await this.fetchUnseenEventsAPI()}catch(e){console.error("Unable to check for unseen events",e)}}async fetchUnseenEventsAPI(){this.shouldFetchUnseenEventsOnConnect=!1,(await this.http.fetchUnseenEvents()).forEach(e=>this.handleIncomingEvent(e))}async publishEvent(e,t,n=!1){let s=await this.cipher.encrypt(JSON.stringify(Object.assign(Object.assign({},t),{origin:location.origin,location:location.href,relaySource:"coinbaseWalletExtension"in window&&window.coinbaseWalletExtension?"injected_sdk":"sdk"}))),r={type:"PublishEvent",id:k(this.nextReqId++),sessionId:this.session.id,event:e,data:s,callWebhook:n};return this.setOnceLinked(async()=>{let e=await this.makeRequest(r);if("Fail"===e.type)throw Error(e.error||"failed to publish event");return e.eventId})}sendData(e){this.ws.sendData(JSON.stringify(e))}updateLastHeartbeat(){this.lastHeartbeatResponse=Date.now()}heartbeat(){if(Date.now()-this.lastHeartbeatResponse>2e4){this.ws.disconnect();return}try{this.ws.sendData("h")}catch(e){}}async makeRequest(e,t={timeout:6e4}){let n;let s=e.id;return this.sendData(e),Promise.race([new Promise((e,r)=>{n=window.setTimeout(()=>{r(Error(`request ${s} timed out`))},t.timeout)}),new Promise(e=>{this.requestResolutions.set(s,t=>{clearTimeout(n),e(t),this.requestResolutions.delete(s)})})])}async handleConnected(){return"Fail"!==(await this.makeRequest({type:"HostSession",id:k(this.nextReqId++),sessionId:this.session.id,sessionKey:this.session.key})).type&&(this.sendData({type:"IsLinked",id:k(this.nextReqId++),sessionId:this.session.id}),this.sendData({type:"GetSessionConfig",id:k(this.nextReqId++),sessionId:this.session.id}),!0)}}class em{constructor(){this._nextRequestId=0,this.callbacks=new Map}makeRequestId(){this._nextRequestId=(this._nextRequestId+1)%0x7fffffff;let e=this._nextRequestId,t=T(e.toString(16));return this.callbacks.get(t)&&this.callbacks.delete(t),e}}function ey(e,...t){if(!(e instanceof Uint8Array||ArrayBuffer.isView(e)&&"Uint8Array"===e.constructor.name))throw Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw Error("Uint8Array expected of length "+t+", got length="+e.length)}function e_(e,t=!0){if(e.destroyed)throw Error("Hash instance has been destroyed");if(t&&e.finished)throw Error("Hash#digest() has already been called")}function eb(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function ew(e,t){return e<<32-t|e>>>t}let ev=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0"));function ek(e){return"string"==typeof e&&(e=function(e){if("string"!=typeof e)throw Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array(new TextEncoder().encode(e))}(e)),ey(e),e}class ex{clone(){return this._cloneInto()}}class eC extends ex{constructor(e,t,n,s){super(),this.blockLen=e,this.outputLen=t,this.padOffset=n,this.isLE=s,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=eb(this.buffer)}update(e){e_(this);let{view:t,buffer:n,blockLen:s}=this,r=(e=ek(e)).length;for(let i=0;i<r;){let a=Math.min(s-this.pos,r-i);if(a===s){let t=eb(e);for(;s<=r-i;i+=s)this.process(t,i);continue}n.set(e.subarray(i,i+a),this.pos),this.pos+=a,i+=a,this.pos===s&&(this.process(t,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){e_(this),function(e,t){ey(e);let n=t.outputLen;if(e.length<n)throw Error("digestInto() expects output buffer of length at least "+n)}(e,this),this.finished=!0;let{buffer:t,view:n,blockLen:s,isLE:r}=this,{pos:i}=this;t[i++]=128,this.buffer.subarray(i).fill(0),this.padOffset>s-i&&(this.process(n,0),i=0);for(let e=i;e<s;e++)t[e]=0;!function(e,t,n,s){if("function"==typeof e.setBigUint64)return e.setBigUint64(t,n,s);let r=BigInt(32),i=BigInt(0xffffffff),a=Number(n>>r&i),o=Number(n&i),c=4*!!s,l=4*!s;e.setUint32(t+c,a,s),e.setUint32(t+l,o,s)}(n,s-8,BigInt(8*this.length),r),this.process(n,0);let a=eb(e),o=this.outputLen;if(o%4)throw Error("_sha2: outputLen should be aligned to 32bit");let c=o/4,l=this.get();if(c>l.length)throw Error("_sha2: outputLen bigger than state");for(let e=0;e<c;e++)a.setUint32(4*e,l[e],r)}digest(){let{buffer:e,outputLen:t}=this;this.digestInto(e);let n=e.slice(0,t);return this.destroy(),n}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());let{blockLen:t,buffer:n,length:s,finished:r,destroyed:i,pos:a}=this;return e.length=s,e.pos=a,e.finished=r,e.destroyed=i,s%t&&e.buffer.set(n),e}}let eE=new Uint32Array([0x428a2f98,0x71374491,0xb5c0fbcf,0xe9b5dba5,0x3956c25b,0x59f111f1,0x923f82a4,0xab1c5ed5,0xd807aa98,0x12835b01,0x243185be,0x550c7dc3,0x72be5d74,0x80deb1fe,0x9bdc06a7,0xc19bf174,0xe49b69c1,0xefbe4786,0xfc19dc6,0x240ca1cc,0x2de92c6f,0x4a7484aa,0x5cb0a9dc,0x76f988da,0x983e5152,0xa831c66d,0xb00327c8,0xbf597fc7,0xc6e00bf3,0xd5a79147,0x6ca6351,0x14292967,0x27b70a85,0x2e1b2138,0x4d2c6dfc,0x53380d13,0x650a7354,0x766a0abb,0x81c2c92e,0x92722c85,0xa2bfe8a1,0xa81a664b,0xc24b8b70,0xc76c51a3,0xd192e819,0xd6990624,0xf40e3585,0x106aa070,0x19a4c116,0x1e376c08,0x2748774c,0x34b0bcb5,0x391c0cb3,0x4ed8aa4a,0x5b9cca4f,0x682e6ff3,0x748f82ee,0x78a5636f,0x84c87814,0x8cc70208,0x90befffa,0xa4506ceb,0xbef9a3f7,0xc67178f2]),eI=new Uint32Array([0x6a09e667,0xbb67ae85,0x3c6ef372,0xa54ff53a,0x510e527f,0x9b05688c,0x1f83d9ab,0x5be0cd19]),eS=new Uint32Array(64);class eM extends eC{constructor(){super(64,32,8,!1),this.A=0|eI[0],this.B=0|eI[1],this.C=0|eI[2],this.D=0|eI[3],this.E=0|eI[4],this.F=0|eI[5],this.G=0|eI[6],this.H=0|eI[7]}get(){let{A:e,B:t,C:n,D:s,E:r,F:i,G:a,H:o}=this;return[e,t,n,s,r,i,a,o]}set(e,t,n,s,r,i,a,o){this.A=0|e,this.B=0|t,this.C=0|n,this.D=0|s,this.E=0|r,this.F=0|i,this.G=0|a,this.H=0|o}process(e,t){for(let n=0;n<16;n++,t+=4)eS[n]=e.getUint32(t,!1);for(let e=16;e<64;e++){let t=eS[e-15],n=eS[e-2],s=ew(t,7)^ew(t,18)^t>>>3,r=ew(n,17)^ew(n,19)^n>>>10;eS[e]=r+eS[e-7]+s+eS[e-16]|0}let{A:n,B:s,C:r,D:i,E:a,F:o,G:c,H:l}=this;for(let e=0;e<64;e++){var h,d,u,p;let t=l+(ew(a,6)^ew(a,11)^ew(a,25))+((h=a)&o^~h&c)+eE[e]+eS[e]|0,f=(ew(n,2)^ew(n,13)^ew(n,22))+((d=n)&(u=s)^d&(p=r)^u&p)|0;l=c,c=o,o=a,a=i+t|0,i=r,r=s,s=n,n=t+f|0}n=n+this.A|0,s=s+this.B|0,r=r+this.C|0,i=i+this.D|0,a=a+this.E|0,o=o+this.F|0,c=c+this.G|0,l=l+this.H|0,this.set(n,s,r,i,a,o,c,l)}roundClean(){eS.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}}let eL=function(e){let t=t=>e().update(ek(t)).digest(),n=e();return t.outputLen=n.outputLen,t.blockLen=n.blockLen,t.create=()=>e(),t}(()=>new eM),eA="session:id",eP="session:secret",eD="session:linked";class eN{constructor(e,t,n,s=!1){this.storage=e,this.id=t,this.secret=n,this.key=function(e){ey(e);let t="";for(let n=0;n<e.length;n++)t+=ev[e[n]];return t}(eL(`${t}, ${n} WalletLink`)),this._linked=!!s}static create(e){return new eN(e,E(16),E(32)).save()}static load(e){let t=e.getItem(eA),n=e.getItem(eD),s=e.getItem(eP);return t&&s?new eN(e,t,s,"1"===n):null}get linked(){return this._linked}set linked(e){this._linked=e,this.persistLinked()}save(){return this.storage.setItem(eA,this.id),this.storage.setItem(eP,this.secret),this.persistLinked(),this}persistLinked(){this.storage.setItem(eD,this._linked?"1":"0")}}function eT(){var e,t;return null!==(t=null===(e=null==window?void 0:window.matchMedia)||void 0===e?void 0:e.call(window,"(prefers-color-scheme: dark)").matches)&&void 0!==t&&t}function eO(){let e=document.createElement("style");e.type="text/css",e.appendChild(document.createTextNode('@namespace svg "http://www.w3.org/2000/svg";.-cbwsdk-css-reset,.-cbwsdk-css-reset *{animation:none;animation-delay:0;animation-direction:normal;animation-duration:0;animation-fill-mode:none;animation-iteration-count:1;animation-name:none;animation-play-state:running;animation-timing-function:ease;backface-visibility:visible;background:0;background-attachment:scroll;background-clip:border-box;background-color:rgba(0,0,0,0);background-image:none;background-origin:padding-box;background-position:0 0;background-position-x:0;background-position-y:0;background-repeat:repeat;background-size:auto auto;border:0;border-style:none;border-width:medium;border-color:inherit;border-bottom:0;border-bottom-color:inherit;border-bottom-left-radius:0;border-bottom-right-radius:0;border-bottom-style:none;border-bottom-width:medium;border-collapse:separate;border-image:none;border-left:0;border-left-color:inherit;border-left-style:none;border-left-width:medium;border-radius:0;border-right:0;border-right-color:inherit;border-right-style:none;border-right-width:medium;border-spacing:0;border-top:0;border-top-color:inherit;border-top-left-radius:0;border-top-right-radius:0;border-top-style:none;border-top-width:medium;box-shadow:none;box-sizing:border-box;caption-side:top;clear:none;clip:auto;color:inherit;columns:auto;column-count:auto;column-fill:balance;column-gap:normal;column-rule:medium none currentColor;column-rule-color:currentColor;column-rule-style:none;column-rule-width:none;column-span:1;column-width:auto;counter-increment:none;counter-reset:none;direction:ltr;empty-cells:show;float:none;font:normal;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Helvetica Neue",Arial,sans-serif;font-size:medium;font-style:normal;font-variant:normal;font-weight:normal;height:auto;hyphens:none;letter-spacing:normal;line-height:normal;list-style:none;list-style-image:none;list-style-position:outside;list-style-type:disc;margin:0;margin-bottom:0;margin-left:0;margin-right:0;margin-top:0;opacity:1;orphans:0;outline:0;outline-color:invert;outline-style:none;outline-width:medium;overflow:visible;overflow-x:visible;overflow-y:visible;padding:0;padding-bottom:0;padding-left:0;padding-right:0;padding-top:0;page-break-after:auto;page-break-before:auto;page-break-inside:auto;perspective:none;perspective-origin:50% 50%;pointer-events:auto;position:static;quotes:"\\201C" "\\201D" "\\2018" "\\2019";tab-size:8;table-layout:auto;text-align:inherit;text-align-last:auto;text-decoration:none;text-decoration-color:inherit;text-decoration-line:none;text-decoration-style:solid;text-indent:0;text-shadow:none;text-transform:none;transform:none;transform-style:flat;transition:none;transition-delay:0s;transition-duration:0s;transition-property:none;transition-timing-function:ease;unicode-bidi:normal;vertical-align:baseline;visibility:visible;white-space:normal;widows:0;word-spacing:normal;z-index:auto}.-cbwsdk-css-reset strong{font-weight:bold}.-cbwsdk-css-reset *{box-sizing:border-box;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Helvetica Neue",Arial,sans-serif;line-height:1}.-cbwsdk-css-reset [class*=container]{margin:0;padding:0}.-cbwsdk-css-reset style{display:none}')),document.documentElement.appendChild(e)}var eR,ej,eU,eB,eH,eW,eq,eV,eZ,ez,eF,eK,e$,eG=n(720),eJ={},eY=[],eQ=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,eX=Array.isArray;function e0(e,t){for(var n in t)e[n]=t[n];return e}function e1(e){e&&e.parentNode&&e.parentNode.removeChild(e)}function e2(e,t,n){var s,r,i,a={};for(i in t)"key"==i?s=t[i]:"ref"==i?r=t[i]:a[i]=t[i];if(arguments.length>2&&(a.children=arguments.length>3?eU.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(i in e.defaultProps)void 0===a[i]&&(a[i]=e.defaultProps[i]);return e3(e,a,s,r,null)}function e3(e,t,n,s,r){var i={type:e,props:t,key:n,ref:s,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==r?++eH:r,__i:-1,__u:0};return null==r&&null!=eB.vnode&&eB.vnode(i),i}function e5(e){return e.children}function e4(e,t){this.props=e,this.context=t}function e8(e,t){if(null==t)return e.__?e8(e.__,e.__i+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?e8(e):null}function e6(e){(!e.__d&&(e.__d=!0)&&eW.push(e)&&!e7.__r++||eq!==eB.debounceRendering)&&((eq=eB.debounceRendering)||eV)(e7)}function e7(){var e,t,n,s,r,i,a,o;for(eW.sort(eZ);e=eW.shift();)e.__d&&(t=eW.length,s=void 0,i=(r=(n=e).__v).__e,a=[],o=[],n.__P&&((s=e0({},r)).__v=r.__v+1,eB.vnode&&eB.vnode(s),ts(n.__P,s,r,n.__n,n.__P.namespaceURI,32&r.__u?[i]:null,a,null==i?e8(r):i,!!(32&r.__u),o),s.__v=r.__v,s.__.__k[s.__i]=s,tr(a,s,o),s.__e!=i&&function e(t){var n,s;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,n=0;n<t.__k.length;n++)if(null!=(s=t.__k[n])&&null!=s.__e){t.__e=t.__c.base=s.__e;break}return e(t)}}(s)),eW.length>t&&eW.sort(eZ));e7.__r=0}function e9(e,t,n,s,r,i,a,o,c,l,h){var d,u,p,f,g,m,y=s&&s.__k||eY,_=t.length;for(c=function(e,t,n,s,r){var i,a,o,c,l,h=n.length,d=h,u=0;for(e.__k=Array(r),i=0;i<r;i++)null!=(a=t[i])&&"boolean"!=typeof a&&"function"!=typeof a?(c=i+u,(a=e.__k[i]="string"==typeof a||"number"==typeof a||"bigint"==typeof a||a.constructor==String?e3(null,a,null,null,null):eX(a)?e3(e5,{children:a},null,null,null):void 0===a.constructor&&a.__b>0?e3(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a).__=e,a.__b=e.__b+1,o=null,-1!==(l=a.__i=function(e,t,n,s){var r,i,a=e.key,o=e.type,c=t[n];if(null===c||c&&a==c.key&&o===c.type&&0==(2&c.__u))return n;if(s>+(null!=c&&0==(2&c.__u)))for(r=n-1,i=n+1;r>=0||i<t.length;){if(r>=0){if((c=t[r])&&0==(2&c.__u)&&a==c.key&&o===c.type)return r;r--}if(i<t.length){if((c=t[i])&&0==(2&c.__u)&&a==c.key&&o===c.type)return i;i++}}return -1}(a,n,c,d))&&(d--,(o=n[l])&&(o.__u|=2)),null==o||null===o.__v?(-1==l&&u--,"function"!=typeof a.type&&(a.__u|=4)):l!=c&&(l==c-1?u--:l==c+1?u++:(l>c?u--:u++,a.__u|=4))):e.__k[i]=null;if(d)for(i=0;i<h;i++)null!=(o=n[i])&&0==(2&o.__u)&&(o.__e==s&&(s=e8(o)),function e(t,n,s){var r,i;if(eB.unmount&&eB.unmount(t),(r=t.ref)&&(r.current&&r.current!==t.__e||ti(r,null,n)),null!=(r=t.__c)){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(e){eB.__e(e,n)}r.base=r.__P=null}if(r=t.__k)for(i=0;i<r.length;i++)r[i]&&e(r[i],n,s||"function"!=typeof t.type);s||e1(t.__e),t.__c=t.__=t.__e=void 0}(o,o));return s}(n,t,y,c,_),d=0;d<_;d++)null!=(p=n.__k[d])&&(u=-1===p.__i?eJ:y[p.__i]||eJ,p.__i=d,m=ts(e,p,u,r,i,a,o,c,l,h),f=p.__e,p.ref&&u.ref!=p.ref&&(u.ref&&ti(u.ref,null,p),h.push(p.ref,p.__c||f,p)),null==g&&null!=f&&(g=f),4&p.__u||u.__k===p.__k?c=function e(t,n,s){var r,i;if("function"==typeof t.type){for(r=t.__k,i=0;r&&i<r.length;i++)r[i]&&(r[i].__=t,n=e(r[i],n,s));return n}t.__e!=n&&(n&&t.type&&!s.contains(n)&&(n=e8(t)),s.insertBefore(t.__e,n||null),n=t.__e);do n=n&&n.nextSibling;while(null!=n&&8==n.nodeType);return n}(p,c,e):"function"==typeof p.type&&void 0!==m?c=m:f&&(c=f.nextSibling),p.__u&=-7);return n.__e=g,c}function te(e,t,n){"-"==t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||eQ.test(t)?n:n+"px"}function tt(e,t,n,s,r){var i;e:if("style"==t){if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof s&&(e.style.cssText=s=""),s)for(t in s)n&&t in n||te(e.style,t,"");if(n)for(t in n)s&&n[t]===s[t]||te(e.style,t,n[t])}}else if("o"==t[0]&&"n"==t[1])i=t!=(t=t.replace(ez,"$1")),t=t.toLowerCase()in e||"onFocusOut"==t||"onFocusIn"==t?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+i]=n,n?s?n.u=s.u:(n.u=eF,e.addEventListener(t,i?e$:eK,i)):e.removeEventListener(t,i?e$:eK,i);else{if("http://www.w3.org/2000/svg"==r)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=t&&"height"!=t&&"href"!=t&&"list"!=t&&"form"!=t&&"tabIndex"!=t&&"download"!=t&&"rowSpan"!=t&&"colSpan"!=t&&"role"!=t&&"popover"!=t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null==n||!1===n&&"-"!=t[4]?e.removeAttribute(t):e.setAttribute(t,"popover"==t&&1==n?"":n))}}function tn(e){return function(t){if(this.l){var n=this.l[t.type+e];if(null==t.t)t.t=eF++;else if(t.t<n.u)return;return n(eB.event?eB.event(t):t)}}}function ts(e,t,n,s,r,i,a,o,c,l){var h,d,u,p,f,g,m,y,_,b,w,v,k,x,C,E,I,S=t.type;if(void 0!==t.constructor)return null;128&n.__u&&(c=!!(32&n.__u),i=[o=t.__e=n.__e]),(h=eB.__b)&&h(t);e:if("function"==typeof S)try{if(y=t.props,_="prototype"in S&&S.prototype.render,b=(h=S.contextType)&&s[h.__c],w=h?b?b.props.value:h.__:s,n.__c?m=(d=t.__c=n.__c).__=d.__E:(_?t.__c=d=new S(y,w):(t.__c=d=new e4(y,w),d.constructor=S,d.render=ta),b&&b.sub(d),d.props=y,d.state||(d.state={}),d.context=w,d.__n=s,u=d.__d=!0,d.__h=[],d._sb=[]),_&&null==d.__s&&(d.__s=d.state),_&&null!=S.getDerivedStateFromProps&&(d.__s==d.state&&(d.__s=e0({},d.__s)),e0(d.__s,S.getDerivedStateFromProps(y,d.__s))),p=d.props,f=d.state,d.__v=t,u)_&&null==S.getDerivedStateFromProps&&null!=d.componentWillMount&&d.componentWillMount(),_&&null!=d.componentDidMount&&d.__h.push(d.componentDidMount);else{if(_&&null==S.getDerivedStateFromProps&&y!==p&&null!=d.componentWillReceiveProps&&d.componentWillReceiveProps(y,w),!d.__e&&(null!=d.shouldComponentUpdate&&!1===d.shouldComponentUpdate(y,d.__s,w)||t.__v==n.__v)){for(t.__v!=n.__v&&(d.props=y,d.state=d.__s,d.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.some(function(e){e&&(e.__=t)}),v=0;v<d._sb.length;v++)d.__h.push(d._sb[v]);d._sb=[],d.__h.length&&a.push(d);break e}null!=d.componentWillUpdate&&d.componentWillUpdate(y,d.__s,w),_&&null!=d.componentDidUpdate&&d.__h.push(function(){d.componentDidUpdate(p,f,g)})}if(d.context=w,d.props=y,d.__P=e,d.__e=!1,k=eB.__r,x=0,_){for(d.state=d.__s,d.__d=!1,k&&k(t),h=d.render(d.props,d.state,d.context),C=0;C<d._sb.length;C++)d.__h.push(d._sb[C]);d._sb=[]}else do d.__d=!1,k&&k(t),h=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++x<25);d.state=d.__s,null!=d.getChildContext&&(s=e0(e0({},s),d.getChildContext())),_&&!u&&null!=d.getSnapshotBeforeUpdate&&(g=d.getSnapshotBeforeUpdate(p,f)),o=e9(e,eX(E=null!=h&&h.type===e5&&null==h.key?h.props.children:h)?E:[E],t,n,s,r,i,a,o,c,l),d.base=t.__e,t.__u&=-161,d.__h.length&&a.push(d),m&&(d.__E=d.__=null)}catch(e){if(t.__v=null,c||null!=i){if(e.then){for(t.__u|=c?160:128;o&&8==o.nodeType&&o.nextSibling;)o=o.nextSibling;i[i.indexOf(o)]=null,t.__e=o}else for(I=i.length;I--;)e1(i[I])}else t.__e=n.__e,t.__k=n.__k;eB.__e(e,t,n)}else null==i&&t.__v==n.__v?(t.__k=n.__k,t.__e=n.__e):o=t.__e=function(e,t,n,s,r,i,a,o,c){var l,h,d,u,p,f,g,m=n.props,y=t.props,_=t.type;if("svg"==_?r="http://www.w3.org/2000/svg":"math"==_?r="http://www.w3.org/1998/Math/MathML":r||(r="http://www.w3.org/1999/xhtml"),null!=i){for(l=0;l<i.length;l++)if((p=i[l])&&"setAttribute"in p==!!_&&(_?p.localName==_:3==p.nodeType)){e=p,i[l]=null;break}}if(null==e){if(null==_)return document.createTextNode(y);e=document.createElementNS(r,_,y.is&&y),o&&(eB.__m&&eB.__m(t,i),o=!1),i=null}if(null===_)m===y||o&&e.data===y||(e.data=y);else{if(i=i&&eU.call(e.childNodes),m=n.props||eJ,!o&&null!=i)for(m={},l=0;l<e.attributes.length;l++)m[(p=e.attributes[l]).name]=p.value;for(l in m)if(p=m[l],"children"==l);else if("dangerouslySetInnerHTML"==l)d=p;else if(!(l in y)){if("value"==l&&"defaultValue"in y||"checked"==l&&"defaultChecked"in y)continue;tt(e,l,null,p,r)}for(l in y)p=y[l],"children"==l?u=p:"dangerouslySetInnerHTML"==l?h=p:"value"==l?f=p:"checked"==l?g=p:o&&"function"!=typeof p||m[l]===p||tt(e,l,p,m[l],r);if(h)o||d&&(h.__html===d.__html||h.__html===e.innerHTML)||(e.innerHTML=h.__html),t.__k=[];else if(d&&(e.innerHTML=""),e9(e,eX(u)?u:[u],t,n,s,"foreignObject"==_?"http://www.w3.org/1999/xhtml":r,i,a,i?i[0]:n.__k&&e8(n,0),o,c),null!=i)for(l=i.length;l--;)e1(i[l]);o||(l="value","progress"==_&&null==f?e.removeAttribute("value"):void 0===f||f===e[l]&&("progress"!=_||f)&&("option"!=_||f===m[l])||tt(e,l,f,m[l],r),l="checked",void 0!==g&&g!==e[l]&&tt(e,l,g,m[l],r))}return e}(n.__e,t,n,s,r,i,a,c,l);return(h=eB.diffed)&&h(t),128&t.__u?void 0:o}function tr(e,t,n){for(var s=0;s<n.length;s++)ti(n[s],n[++s],n[++s]);eB.__c&&eB.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){eB.__e(e,t.__v)}})}function ti(e,t,n){try{if("function"==typeof e){var s="function"==typeof e.__u;s&&e.__u(),s&&null==t||(e.__u=e(t))}else e.current=t}catch(e){eB.__e(e,n)}}function ta(e,t,n){return this.constructor(e,n)}function to(e,t,n){var s,r,i,a;t==document&&(t=document.documentElement),eB.__&&eB.__(e,t),r=(s="function"==typeof n)?null:n&&n.__k||t.__k,i=[],a=[],ts(t,e=(!s&&n||t).__k=e2(e5,null,[e]),r||eJ,eJ,t.namespaceURI,!s&&n?[n]:r?null:t.firstChild?eU.call(t.childNodes):null,i,!s&&n?n:r?r.__e:t.firstChild,s,a),tr(i,e,a)}eU=eY.slice,eB={__e:function(e,t,n,s){for(var r,i,a;t=t.__;)if((r=t.__c)&&!r.__)try{if((i=r.constructor)&&null!=i.getDerivedStateFromError&&(r.setState(i.getDerivedStateFromError(e)),a=r.__d),null!=r.componentDidCatch&&(r.componentDidCatch(e,s||{}),a=r.__d),a)return r.__E=r}catch(t){e=t}throw e}},eH=0,e4.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=e0({},this.state),"function"==typeof e&&(e=e(e0({},n),this.props)),e&&e0(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),e6(this))},e4.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),e6(this))},e4.prototype.render=e5,eW=[],eV="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,eZ=function(e,t){return e.__v.__b-t.__v.__b},e7.__r=0,ez=/(PointerCapture)$|Capture$/i,eF=0,eK=tn(!1),e$=tn(!0);var tc,tl,th,td,tu=0,tp=[],tf=eB,tg=tf.__b,tm=tf.__r,ty=tf.diffed,t_=tf.__c,tb=tf.unmount,tw=tf.__;function tv(e,t){tf.__h&&tf.__h(tl,e,tu||t),tu=0;var n=tl.__H||(tl.__H={__:[],__h:[]});return e>=n.__.length&&n.__.push({}),n.__[e]}function tk(e,t,n){var s=tv(tc++,2);if(s.t=e,!s.__c&&(s.__=[n?n(t):tS(void 0,t),function(e){var t=s.__N?s.__N[0]:s.__[0],n=s.t(t,e);t!==n&&(s.__N=[n,s.__[1]],s.__c.setState({}))}],s.__c=tl,!tl.u)){var r=function(e,t,n){if(!s.__c.__H)return!0;var r=s.__c.__H.__.filter(function(e){return!!e.__c});if(r.every(function(e){return!e.__N}))return!i||i.call(this,e,t,n);var a=s.__c.props!==e;return r.forEach(function(e){if(e.__N){var t=e.__[0];e.__=e.__N,e.__N=void 0,t!==e.__[0]&&(a=!0)}}),i&&i.call(this,e,t,n)||a};tl.u=!0;var i=tl.shouldComponentUpdate,a=tl.componentWillUpdate;tl.componentWillUpdate=function(e,t,n){if(this.__e){var s=i;i=void 0,r(e,t,n),i=s}a&&a.call(this,e,t,n)},tl.shouldComponentUpdate=r}return s.__N||s.__}function tx(){for(var e;e=tp.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(tE),e.__H.__h.forEach(tI),e.__H.__h=[]}catch(t){e.__H.__h=[],tf.__e(t,e.__v)}}tf.__b=function(e){tl=null,tg&&tg(e)},tf.__=function(e,t){e&&t.__k&&t.__k.__m&&(e.__m=t.__k.__m),tw&&tw(e,t)},tf.__r=function(e){tm&&tm(e),tc=0;var t=(tl=e.__c).__H;t&&(th===tl?(t.__h=[],tl.__h=[],t.__.forEach(function(e){e.__N&&(e.__=e.__N),e.i=e.__N=void 0})):(t.__h.forEach(tE),t.__h.forEach(tI),t.__h=[],tc=0)),th=tl},tf.diffed=function(e){ty&&ty(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==tp.push(t)&&td===tf.requestAnimationFrame||((td=tf.requestAnimationFrame)||function(e){var t,n=function(){clearTimeout(s),tC&&cancelAnimationFrame(t),setTimeout(e)},s=setTimeout(n,100);tC&&(t=requestAnimationFrame(n))})(tx)),t.__H.__.forEach(function(e){e.i&&(e.__H=e.i),e.i=void 0})),th=tl=null},tf.__c=function(e,t){t.some(function(e){try{e.__h.forEach(tE),e.__h=e.__h.filter(function(e){return!e.__||tI(e)})}catch(n){t.some(function(e){e.__h&&(e.__h=[])}),t=[],tf.__e(n,e.__v)}}),t_&&t_(e,t)},tf.unmount=function(e){tb&&tb(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach(function(e){try{tE(e)}catch(e){t=e}}),n.__H=void 0,t&&tf.__e(t,n.__v))};var tC="function"==typeof requestAnimationFrame;function tE(e){var t=tl,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),tl=t}function tI(e){var t=tl;e.__c=e.__(),tl=t}function tS(e,t){return"function"==typeof t?t(e):t}class tM{constructor(){this.items=new Map,this.nextItemKey=0,this.root=null,this.darkMode=eT()}attach(e){this.root=document.createElement("div"),this.root.className="-cbwsdk-snackbar-root",e.appendChild(this.root),this.render()}presentItem(e){let t=this.nextItemKey++;return this.items.set(t,e),this.render(),()=>{this.items.delete(t),this.render()}}clear(){this.items.clear(),this.render()}render(){this.root&&to(e2("div",null,e2(tL,{darkMode:this.darkMode},Array.from(this.items.entries()).map(([e,t])=>e2(tA,Object.assign({},t,{key:e}))))),this.root)}}let tL=e=>e2("div",{class:(0,eG.W)("-cbwsdk-snackbar-container")},e2("style",null,".-cbwsdk-css-reset .-gear-container{margin-left:16px !important;margin-right:9px !important;display:flex;align-items:center;justify-content:center;width:24px;height:24px;transition:opacity .25s}.-cbwsdk-css-reset .-gear-container *{user-select:none}.-cbwsdk-css-reset .-gear-container svg{opacity:0;position:absolute}.-cbwsdk-css-reset .-gear-icon{height:12px;width:12px;z-index:10000}.-cbwsdk-css-reset .-cbwsdk-snackbar{align-items:flex-end;display:flex;flex-direction:column;position:fixed;right:0;top:0;z-index:2147483647}.-cbwsdk-css-reset .-cbwsdk-snackbar *{user-select:none}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance{display:flex;flex-direction:column;margin:8px 16px 0 16px;overflow:visible;text-align:left;transform:translateX(0);transition:opacity .25s,transform .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header:hover .-gear-container svg{opacity:1}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header{display:flex;align-items:center;background:#fff;overflow:hidden;border:1px solid #e7ebee;box-sizing:border-box;border-radius:8px;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header-cblogo{margin:8px 8px 8px 8px}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header *{cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-header-message{color:#000;font-size:13px;line-height:1.5;user-select:none}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu{background:#fff;transition:opacity .25s ease-in-out,transform .25s linear,visibility 0s;visibility:hidden;border:1px solid #e7ebee;box-sizing:border-box;border-radius:8px;opacity:0;flex-direction:column;padding-left:8px;padding-right:8px}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:last-child{margin-bottom:8px !important}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover{background:#f5f7f8;border-radius:6px;transition:background .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover span{color:#050f19;transition:color .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item:hover svg path{fill:#000;transition:fill .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item{visibility:inherit;height:35px;margin-top:8px;margin-bottom:0;display:flex;flex-direction:row;align-items:center;padding:8px;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item *{visibility:inherit;cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover{background:rgba(223,95,103,.2);transition:background .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover *{cursor:pointer}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover svg path{fill:#df5f67;transition:fill .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-is-red:hover span{color:#df5f67;transition:color .25s}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-menu-item-info{color:#aaa;font-size:13px;margin:0 8px 0 32px;position:absolute}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-hidden{opacity:0;text-align:left;transform:translateX(25%);transition:opacity .5s linear}.-cbwsdk-css-reset .-cbwsdk-snackbar-instance-expanded .-cbwsdk-snackbar-instance-menu{opacity:1;display:flex;transform:translateY(8px);visibility:visible}"),e2("div",{class:"-cbwsdk-snackbar"},e.children)),tA=({autoExpand:e,message:t,menuItems:n})=>{var s,r,i,a,o;let[c,l]=(tu=1,tk(tS,!0)),[h,d]=(tu=1,tk(tS,null!=e&&e));return s=()=>{let e=[window.setTimeout(()=>{l(!1)},1),window.setTimeout(()=>{d(!0)},1e4)];return()=>{e.forEach(window.clearTimeout)}},i=tv(tc++,3),!tf.__s&&(a=i.__H,o=void 0,!a||a.length!==o.length||o.some(function(e,t){return e!==a[t]}))&&(i.__=s,i.i=r,tl.__H.__h.push(i)),e2("div",{class:(0,eG.W)("-cbwsdk-snackbar-instance",c&&"-cbwsdk-snackbar-instance-hidden",h&&"-cbwsdk-snackbar-instance-expanded")},e2("div",{class:"-cbwsdk-snackbar-instance-header",onClick:()=>{d(!h)}},e2("img",{src:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEuNDkyIDEwLjQxOWE4LjkzIDguOTMgMCAwMTguOTMtOC45M2gxMS4xNjNhOC45MyA4LjkzIDAgMDE4LjkzIDguOTN2MTEuMTYzYTguOTMgOC45MyAwIDAxLTguOTMgOC45M0gxMC40MjJhOC45MyA4LjkzIDAgMDEtOC45My04LjkzVjEwLjQxOXoiIGZpbGw9IiMxNjUyRjAiLz48cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTEwLjQxOSAwSDIxLjU4QzI3LjMzNSAwIDMyIDQuNjY1IDMyIDEwLjQxOVYyMS41OEMzMiAyNy4zMzUgMjcuMzM1IDMyIDIxLjU4MSAzMkgxMC40MkM0LjY2NSAzMiAwIDI3LjMzNSAwIDIxLjU4MVYxMC40MkMwIDQuNjY1IDQuNjY1IDAgMTAuNDE5IDB6bTAgMS40ODhhOC45MyA4LjkzIDAgMDAtOC45MyA4LjkzdjExLjE2M2E4LjkzIDguOTMgMCAwMDguOTMgOC45M0gyMS41OGE4LjkzIDguOTMgMCAwMDguOTMtOC45M1YxMC40MmE4LjkzIDguOTMgMCAwMC04LjkzLTguOTNIMTAuNDJ6IiBmaWxsPSIjZmZmIi8+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNS45OTggMjYuMDQ5Yy01LjU0OSAwLTEwLjA0Ny00LjQ5OC0xMC4wNDctMTAuMDQ3IDAtNS41NDggNC40OTgtMTAuMDQ2IDEwLjA0Ny0xMC4wNDYgNS41NDggMCAxMC4wNDYgNC40OTggMTAuMDQ2IDEwLjA0NiAwIDUuNTQ5LTQuNDk4IDEwLjA0Ny0xMC4wNDYgMTAuMDQ3eiIgZmlsbD0iI2ZmZiIvPjxwYXRoIGQ9Ik0xMi43NjIgMTQuMjU0YzAtLjgyMi42NjctMS40ODkgMS40ODktMS40ODloMy40OTdjLjgyMiAwIDEuNDg4LjY2NiAxLjQ4OCAxLjQ4OXYzLjQ5N2MwIC44MjItLjY2NiAxLjQ4OC0xLjQ4OCAxLjQ4OGgtMy40OTdhMS40ODggMS40ODggMCAwMS0xLjQ4OS0xLjQ4OHYtMy40OTh6IiBmaWxsPSIjMTY1MkYwIi8+PC9zdmc+",class:"-cbwsdk-snackbar-instance-header-cblogo"})," ",e2("div",{class:"-cbwsdk-snackbar-instance-header-message"},t),e2("div",{class:"-gear-container"},!h&&e2("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e2("circle",{cx:"12",cy:"12",r:"12",fill:"#F5F7F8"})),e2("img",{src:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEyIDYuNzV2LTEuNWwtMS43Mi0uNTdjLS4wOC0uMjctLjE5LS41Mi0uMzItLjc3bC44MS0xLjYyLTEuMDYtMS4wNi0xLjYyLjgxYy0uMjQtLjEzLS41LS4yNC0uNzctLjMyTDYuNzUgMGgtMS41bC0uNTcgMS43MmMtLjI3LjA4LS41My4xOS0uNzcuMzJsLTEuNjItLjgxLTEuMDYgMS4wNi44MSAxLjYyYy0uMTMuMjQtLjI0LjUtLjMyLjc3TDAgNS4yNXYxLjVsMS43Mi41N2MuMDguMjcuMTkuNTMuMzIuNzdsLS44MSAxLjYyIDEuMDYgMS4wNiAxLjYyLS44MWMuMjQuMTMuNS4yMy43Ny4zMkw1LjI1IDEyaDEuNWwuNTctMS43MmMuMjctLjA4LjUyLS4xOS43Ny0uMzJsMS42Mi44MSAxLjA2LTEuMDYtLjgxLTEuNjJjLjEzLS4yNC4yMy0uNS4zMi0uNzdMMTIgNi43NXpNNiA4LjVhMi41IDIuNSAwIDAxMC01IDIuNSAyLjUgMCAwMTAgNXoiIGZpbGw9IiMwNTBGMTkiLz48L3N2Zz4=",class:"-gear-icon",title:"Expand"}))),n&&n.length>0&&e2("div",{class:"-cbwsdk-snackbar-instance-menu"},n.map((e,t)=>e2("div",{class:(0,eG.W)("-cbwsdk-snackbar-instance-menu-item",e.isRed&&"-cbwsdk-snackbar-instance-menu-item-is-red"),onClick:e.onClick,key:t},e2("svg",{width:e.svgWidth,height:e.svgHeight,viewBox:"0 0 10 11",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e2("path",{"fill-rule":e.defaultFillRule,"clip-rule":e.defaultClipRule,d:e.path,fill:"#AAAAAA"})),e2("span",{class:(0,eG.W)("-cbwsdk-snackbar-instance-menu-item-info",e.isRed&&"-cbwsdk-snackbar-instance-menu-item-info-is-red")},e.info)))))};class tP{constructor(){this.attached=!1,this.snackbar=new tM}attach(){if(this.attached)throw Error("Coinbase Wallet SDK UI is already attached");let e=document.documentElement,t=document.createElement("div");t.className="-cbwsdk-css-reset",e.appendChild(t),this.snackbar.attach(t),this.attached=!0,eO()}showConnecting(e){let t;return t=e.isUnlinkedErrorState?{autoExpand:!0,message:"Connection lost",menuItems:[{isRed:!1,info:"Reset connection",svgWidth:"10",svgHeight:"11",path:"M5.00008 0.96875C6.73133 0.96875 8.23758 1.94375 9.00008 3.375L10.0001 2.375V5.5H9.53133H7.96883H6.87508L7.80633 4.56875C7.41258 3.3875 6.31258 2.53125 5.00008 2.53125C3.76258 2.53125 2.70633 3.2875 2.25633 4.36875L0.812576 3.76875C1.50008 2.125 3.11258 0.96875 5.00008 0.96875ZM2.19375 6.43125C2.5875 7.6125 3.6875 8.46875 5 8.46875C6.2375 8.46875 7.29375 7.7125 7.74375 6.63125L9.1875 7.23125C8.5 8.875 6.8875 10.0312 5 10.0312C3.26875 10.0312 1.7625 9.05625 1 7.625L0 8.625V5.5H0.46875H2.03125H3.125L2.19375 6.43125Z",defaultFillRule:"evenodd",defaultClipRule:"evenodd",onClick:e.onResetConnection}]}:{message:"Confirm on phone",menuItems:[{isRed:!0,info:"Cancel transaction",svgWidth:"11",svgHeight:"11",path:"M10.3711 1.52346L9.21775 0.370117L5.37109 4.21022L1.52444 0.370117L0.371094 1.52346L4.2112 5.37012L0.371094 9.21677L1.52444 10.3701L5.37109 6.53001L9.21775 10.3701L10.3711 9.21677L6.53099 5.37012L10.3711 1.52346Z",defaultFillRule:"inherit",defaultClipRule:"inherit",onClick:e.onCancel},{isRed:!1,info:"Reset connection",svgWidth:"10",svgHeight:"11",path:"M5.00008 0.96875C6.73133 0.96875 8.23758 1.94375 9.00008 3.375L10.0001 2.375V5.5H9.53133H7.96883H6.87508L7.80633 4.56875C7.41258 3.3875 6.31258 2.53125 5.00008 2.53125C3.76258 2.53125 2.70633 3.2875 2.25633 4.36875L0.812576 3.76875C1.50008 2.125 3.11258 0.96875 5.00008 0.96875ZM2.19375 6.43125C2.5875 7.6125 3.6875 8.46875 5 8.46875C6.2375 8.46875 7.29375 7.7125 7.74375 6.63125L9.1875 7.23125C8.5 8.875 6.8875 10.0312 5 10.0312C3.26875 10.0312 1.7625 9.05625 1 7.625L0 8.625V5.5H0.46875H2.03125H3.125L2.19375 6.43125Z",defaultFillRule:"evenodd",defaultClipRule:"evenodd",onClick:e.onResetConnection}]},this.snackbar.presentItem(t)}}class tD{constructor(){this.root=null,this.darkMode=eT()}attach(){let e=document.documentElement;this.root=document.createElement("div"),this.root.className="-cbwsdk-css-reset",e.appendChild(this.root),eO()}present(e){this.render(e)}clear(){this.render(null)}render(e){this.root&&(to(null,this.root),e&&to(e2(tN,Object.assign({},e,{onDismiss:()=>{this.clear()},darkMode:this.darkMode})),this.root))}}let tN=({title:e,buttonText:t,darkMode:n,onButtonClick:s,onDismiss:r})=>e2(tL,{darkMode:n},e2("div",{class:"-cbwsdk-redirect-dialog"},e2("style",null,".-cbwsdk-css-reset .-cbwsdk-redirect-dialog-backdrop{position:fixed;top:0;left:0;right:0;bottom:0;transition:opacity .25s;background-color:rgba(10,11,13,.5)}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-backdrop-hidden{opacity:0}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box{display:block;position:fixed;top:50%;left:50%;transform:translate(-50%, -50%);padding:20px;border-radius:8px;background-color:#fff;color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box p{display:block;font-weight:400;font-size:14px;line-height:20px;padding-bottom:12px;color:#5b636e}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box button{appearance:none;border:none;background:none;color:#0052ff;padding:0;text-decoration:none;display:block;font-weight:600;font-size:16px;line-height:24px}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.dark{background-color:#0a0b0d;color:#fff}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.dark button{color:#0052ff}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.light{background-color:#fff;color:#0a0b0d}.-cbwsdk-css-reset .-cbwsdk-redirect-dialog-box.light button{color:#0052ff}"),e2("div",{class:"-cbwsdk-redirect-dialog-backdrop",onClick:r}),e2("div",{class:(0,eG.W)("-cbwsdk-redirect-dialog-box",n?"dark":"light")},e2("p",null,e),e2("button",{onClick:s},t)))),tT="https://www.walletlink.org";class tO{constructor(){this.attached=!1,this.redirectDialog=new tD}attach(){if(this.attached)throw Error("Coinbase Wallet SDK UI is already attached");this.redirectDialog.attach(),this.attached=!0}redirectToCoinbaseWallet(e){let t=new URL("https://go.cb-w.com/walletlink");t.searchParams.append("redirect_url",function(){try{if(function(){try{return null!==window.frameElement}catch(e){return!1}}()&&window.top)return window.top.location;return window.location}catch(e){return window.location}}().href),e&&t.searchParams.append("wl_url",e);let n=document.createElement("a");n.target="cbw-opener",n.href=t.href,n.rel="noreferrer noopener",n.click()}openCoinbaseWalletDeeplink(e){this.redirectDialog.present({title:"Redirecting to Coinbase Wallet...",buttonText:"Open",onButtonClick:()=>{this.redirectToCoinbaseWallet(e)}}),setTimeout(()=>{this.redirectToCoinbaseWallet(e)},99)}showConnecting(e){return()=>{this.redirectDialog.clear()}}}class tR{constructor(e){var t;this.chainCallbackParams={chainId:"",jsonRpcUrl:""},this.isMobileWeb=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(null===(t=null==window?void 0:window.navigator)||void 0===t?void 0:t.userAgent),this.linkedUpdated=e=>{this.isLinked=e;let t=this.storage.getItem(eh);if(e&&(this._session.linked=e),this.isUnlinkedErrorState=!1,t){let n=t.split(" "),s="true"===this.storage.getItem("IsStandaloneSigning");""===n[0]||e||!this._session.linked||s||(this.isUnlinkedErrorState=!0)}},this.metadataUpdated=(e,t)=>{this.storage.setItem(e,t)},this.chainUpdated=(e,t)=>{(this.chainCallbackParams.chainId!==e||this.chainCallbackParams.jsonRpcUrl!==t)&&(this.chainCallbackParams={chainId:e,jsonRpcUrl:t},this.chainCallback&&this.chainCallback(t,Number.parseInt(e,10)))},this.accountUpdated=e=>{this.accountsCallback&&this.accountsCallback([e]),tR.accountRequestCallbackIds.size>0&&(Array.from(tR.accountRequestCallbackIds.values()).forEach(t=>{this.invokeCallback(t,{method:"requestEthereumAccounts",result:[e]})}),tR.accountRequestCallbackIds.clear())},this.resetAndReload=this.resetAndReload.bind(this),this.linkAPIUrl=e.linkAPIUrl,this.storage=e.storage,this.metadata=e.metadata,this.accountsCallback=e.accountsCallback,this.chainCallback=e.chainCallback;let{session:n,ui:s,connection:r}=this.subscribe();this._session=n,this.connection=r,this.relayEventManager=new em,this.ui=s,this.ui.attach()}subscribe(){let e=eN.load(this.storage)||eN.create(this.storage),{linkAPIUrl:t}=this,n=new eg({session:e,linkAPIUrl:t,listener:this}),s=this.isMobileWeb?new tO:new tP;return n.connect(),{session:e,ui:s,connection:n}}resetAndReload(){this.connection.destroy().then(()=>{let e=eN.load(this.storage);(null==e?void 0:e.id)===this._session.id&&i.clearAll(),document.location.reload()}).catch(e=>{})}signEthereumTransaction(e){return this.sendRequest({method:"signEthereumTransaction",params:{fromAddress:e.fromAddress,toAddress:e.toAddress,weiValue:A(e.weiValue),data:M(e.data,!0),nonce:e.nonce,gasPriceInWei:e.gasPriceInWei?A(e.gasPriceInWei):null,maxFeePerGas:e.gasPriceInWei?A(e.gasPriceInWei):null,maxPriorityFeePerGas:e.gasPriceInWei?A(e.gasPriceInWei):null,gasLimit:e.gasLimit?A(e.gasLimit):null,chainId:e.chainId,shouldSubmit:!1}})}signAndSubmitEthereumTransaction(e){return this.sendRequest({method:"signEthereumTransaction",params:{fromAddress:e.fromAddress,toAddress:e.toAddress,weiValue:A(e.weiValue),data:M(e.data,!0),nonce:e.nonce,gasPriceInWei:e.gasPriceInWei?A(e.gasPriceInWei):null,maxFeePerGas:e.maxFeePerGas?A(e.maxFeePerGas):null,maxPriorityFeePerGas:e.maxPriorityFeePerGas?A(e.maxPriorityFeePerGas):null,gasLimit:e.gasLimit?A(e.gasLimit):null,chainId:e.chainId,shouldSubmit:!0}})}submitEthereumTransaction(e,t){return this.sendRequest({method:"submitEthereumTransaction",params:{signedTransaction:M(e,!0),chainId:t}})}getWalletLinkSession(){return this._session}sendRequest(e){let t=null,n=E(8),s=s=>{this.publishWeb3RequestCanceledEvent(n),this.handleErrorResponse(n,e.method,s),null==t||t()};return new Promise((r,i)=>{t=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:s,onResetConnection:this.resetAndReload}),this.relayEventManager.callbacks.set(n,e=>{if(null==t||t(),ed(e))return i(Error(e.errorMessage));r(e)}),this.publishWeb3RequestEvent(n,e)})}publishWeb3RequestEvent(e,t){let n={type:"WEB3_REQUEST",id:e,request:t};this.publishEvent("Web3Request",n,!0).then(e=>{}).catch(e=>{this.handleWeb3ResponseMessage(n.id,{method:t.method,errorMessage:e.message})}),this.isMobileWeb&&this.openCoinbaseWalletDeeplink(t.method)}openCoinbaseWalletDeeplink(e){if(this.ui instanceof tO)switch(e){case"requestEthereumAccounts":case"switchEthereumChain":return;default:window.addEventListener("blur",()=>{window.addEventListener("focus",()=>{this.connection.checkUnseenEvents()},{once:!0})},{once:!0}),this.ui.openCoinbaseWalletDeeplink()}}publishWeb3RequestCanceledEvent(e){this.publishEvent("Web3RequestCanceled",{type:"WEB3_REQUEST_CANCELED",id:e},!1).then()}publishEvent(e,t,n){return this.connection.publishEvent(e,t,n)}handleWeb3ResponseMessage(e,t){if("requestEthereumAccounts"===t.method){tR.accountRequestCallbackIds.forEach(e=>this.invokeCallback(e,t)),tR.accountRequestCallbackIds.clear();return}this.invokeCallback(e,t)}handleErrorResponse(e,t,n){var s;let r=null!==(s=null==n?void 0:n.message)&&void 0!==s?s:"Unspecified error message.";this.handleWeb3ResponseMessage(e,{method:t,errorMessage:r})}invokeCallback(e,t){let n=this.relayEventManager.callbacks.get(e);n&&(n(t),this.relayEventManager.callbacks.delete(e))}requestEthereumAccounts(){let{appName:e,appLogoUrl:t}=this.metadata,n={method:"requestEthereumAccounts",params:{appName:e,appLogoUrl:t}},s=E(8);return new Promise((e,t)=>{this.relayEventManager.callbacks.set(s,n=>{if(ed(n))return t(Error(n.errorMessage));e(n)}),tR.accountRequestCallbackIds.add(s),this.publishWeb3RequestEvent(s,n)})}watchAsset(e,t,n,s,r,i){let a={method:"watchAsset",params:{type:e,options:{address:t,symbol:n,decimals:s,image:r},chainId:i}},o=null,c=E(8);return o=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:e=>{this.publishWeb3RequestCanceledEvent(c),this.handleErrorResponse(c,a.method,e),null==o||o()},onResetConnection:this.resetAndReload}),new Promise((e,t)=>{this.relayEventManager.callbacks.set(c,n=>{if(null==o||o(),ed(n))return t(Error(n.errorMessage));e(n)}),this.publishWeb3RequestEvent(c,a)})}addEthereumChain(e,t,n,s,r,i){let a={method:"addEthereumChain",params:{chainId:e,rpcUrls:t,blockExplorerUrls:s,chainName:r,iconUrls:n,nativeCurrency:i}},o=null,c=E(8);return o=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:e=>{this.publishWeb3RequestCanceledEvent(c),this.handleErrorResponse(c,a.method,e),null==o||o()},onResetConnection:this.resetAndReload}),new Promise((e,t)=>{this.relayEventManager.callbacks.set(c,n=>{if(null==o||o(),ed(n))return t(Error(n.errorMessage));e(n)}),this.publishWeb3RequestEvent(c,a)})}switchEthereumChain(e,t){let n={method:"switchEthereumChain",params:Object.assign({chainId:e},{address:t})},s=null,r=E(8);return s=this.ui.showConnecting({isUnlinkedErrorState:this.isUnlinkedErrorState,onCancel:e=>{this.publishWeb3RequestCanceledEvent(r),this.handleErrorResponse(r,n.method,e),null==s||s()},onResetConnection:this.resetAndReload}),new Promise((e,t)=>{this.relayEventManager.callbacks.set(r,n=>(null==s||s(),ed(n)&&n.errorCode)?t(p.provider.custom({code:n.errorCode,message:"Unrecognized chain ID. Try adding the chain using addEthereumChain first."})):ed(n)?t(Error(n.errorMessage)):void e(n)),this.publishWeb3RequestEvent(r,n)})}}tR.accountRequestCallbackIds=new Set;let tj="DefaultChainId",tU="DefaultJsonRpcUrl";class tB{constructor(e){this._relay=null,this._addresses=[],this.metadata=e.metadata,this._storage=new i("walletlink",tT),this.callback=e.callback||null;let t=this._storage.getItem(eh);if(t){let e=t.split(" ");""!==e[0]&&(this._addresses=e.map(e=>j(e)))}this.initializeRelay()}getSession(){let{id:e,secret:t}=this.initializeRelay().getWalletLinkSession();return{id:e,secret:t}}async handshake(){await this._eth_requestAccounts()}get selectedAddress(){return this._addresses[0]||void 0}get jsonRpcUrl(){var e;return null!==(e=this._storage.getItem(tU))&&void 0!==e?e:void 0}set jsonRpcUrl(e){this._storage.setItem(tU,e)}updateProviderInfo(e,t){var n;this.jsonRpcUrl=e;let s=this.getChainId();this._storage.setItem(tj,t.toString(10)),B(t)!==s&&(null===(n=this.callback)||void 0===n||n.call(this,"chainChanged",P(t)))}async watchAsset(e){let t=Array.isArray(e)?e[0]:e;if(!t.type)throw p.rpc.invalidParams("Type is required");if((null==t?void 0:t.type)!=="ERC20")throw p.rpc.invalidParams(`Asset of type '${t.type}' is not supported`);if(!(null==t?void 0:t.options))throw p.rpc.invalidParams("Options are required");if(!(null==t?void 0:t.options.address))throw p.rpc.invalidParams("Address is required");let n=this.getChainId(),{address:s,symbol:r,image:i,decimals:a}=t.options,o=this.initializeRelay(),c=await o.watchAsset(t.type,s,r,a,i,null==n?void 0:n.toString());return!ed(c)&&!!c.result}async addEthereumChain(e){var t,n;let s=e[0];if((null===(t=s.rpcUrls)||void 0===t?void 0:t.length)===0)throw p.rpc.invalidParams("please pass in at least 1 rpcUrl");if(!s.chainName||""===s.chainName.trim())throw p.rpc.invalidParams("chainName is a required field");if(!s.nativeCurrency)throw p.rpc.invalidParams("nativeCurrency is a required field");let r=Number.parseInt(s.chainId,16);if(r===this.getChainId())return!1;let i=this.initializeRelay(),{rpcUrls:a=[],blockExplorerUrls:o=[],chainName:c,iconUrls:l=[],nativeCurrency:h}=s,d=await i.addEthereumChain(r.toString(),a,l,o,c,h);if(ed(d))return!1;if((null===(n=d.result)||void 0===n?void 0:n.isApproved)===!0)return this.updateProviderInfo(a[0],r),null;throw p.rpc.internal("unable to add ethereum chain")}async switchEthereumChain(e){let t=Number.parseInt(e[0].chainId,16),n=this.initializeRelay(),s=await n.switchEthereumChain(t.toString(10),this.selectedAddress||void 0);if(ed(s))throw s;let r=s.result;return r.isApproved&&r.rpcUrl.length>0&&this.updateProviderInfo(r.rpcUrl,t),null}async cleanup(){this.callback=null,this._relay&&this._relay.resetAndReload(),this._storage.clear()}_setAddresses(e,t){var n;if(!Array.isArray(e))throw Error("addresses is not an array");let s=e.map(e=>j(e));JSON.stringify(s)!==JSON.stringify(this._addresses)&&(this._addresses=s,null===(n=this.callback)||void 0===n||n.call(this,"accountsChanged",s),this._storage.setItem(eh,s.join(" ")))}async request(e){let t=e.params||[];switch(e.method){case"eth_accounts":return[...this._addresses];case"eth_coinbase":return this.selectedAddress||null;case"net_version":return this.getChainId().toString(10);case"eth_chainId":return P(this.getChainId());case"eth_requestAccounts":return this._eth_requestAccounts();case"eth_ecRecover":case"personal_ecRecover":return this.ecRecover(e);case"personal_sign":return this.personalSign(e);case"eth_signTransaction":return this._eth_signTransaction(t);case"eth_sendRawTransaction":return this._eth_sendRawTransaction(t);case"eth_sendTransaction":return this._eth_sendTransaction(t);case"eth_signTypedData_v1":case"eth_signTypedData_v3":case"eth_signTypedData_v4":case"eth_signTypedData":return this.signTypedData(e);case"wallet_addEthereumChain":return this.addEthereumChain(t);case"wallet_switchEthereumChain":return this.switchEthereumChain(t);case"wallet_watchAsset":return this.watchAsset(t);default:if(!this.jsonRpcUrl)throw p.rpc.internal("No RPC URL set for chain");return en(e,this.jsonRpcUrl)}}_ensureKnownAddress(e){let t=j(e);if(!this._addresses.map(e=>j(e)).includes(t))throw Error("Unknown Ethereum address")}_prepareTransactionParams(e){let t=e.from?j(e.from):this.selectedAddress;if(!t)throw Error("Ethereum address is unavailable");this._ensureKnownAddress(t);let n=e.to?j(e.to):null,s=null!=e.value?H(e.value):BigInt(0),r=e.data?U(e.data):Buffer.alloc(0),i=null!=e.nonce?B(e.nonce):null,a=null!=e.gasPrice?H(e.gasPrice):null,o=null!=e.maxFeePerGas?H(e.maxFeePerGas):null,c=null!=e.maxPriorityFeePerGas?H(e.maxPriorityFeePerGas):null;return{fromAddress:t,toAddress:n,weiValue:s,data:r,nonce:i,gasPriceInWei:a,maxFeePerGas:o,maxPriorityFeePerGas:c,gasLimit:null!=e.gas?H(e.gas):null,chainId:e.chainId?B(e.chainId):this.getChainId()}}async ecRecover(e){let{method:t,params:n}=e;if(!Array.isArray(n))throw p.rpc.invalidParams();let s=this.initializeRelay(),r=await s.sendRequest({method:"ethereumAddressFromSignedMessage",params:{message:L(n[0]),signature:L(n[1]),addPrefix:"personal_ecRecover"===t}});if(ed(r))throw r;return r.result}getChainId(){var e;return Number.parseInt(null!==(e=this._storage.getItem(tj))&&void 0!==e?e:"1",10)}async _eth_requestAccounts(){var e,t;if(this._addresses.length>0)return null===(e=this.callback)||void 0===e||e.call(this,"connect",{chainId:P(this.getChainId())}),this._addresses;let n=this.initializeRelay(),s=await n.requestEthereumAccounts();if(ed(s))throw s;if(!s.result)throw Error("accounts received is empty");return this._setAddresses(s.result),null===(t=this.callback)||void 0===t||t.call(this,"connect",{chainId:P(this.getChainId())}),this._addresses}async personalSign({params:e}){if(!Array.isArray(e))throw p.rpc.invalidParams();let t=e[1],n=e[0];this._ensureKnownAddress(t);let s=this.initializeRelay(),r=await s.sendRequest({method:"signEthereumMessage",params:{address:j(t),message:L(n),addPrefix:!0,typedDataJson:null}});if(ed(r))throw r;return r.result}async _eth_signTransaction(e){let t=this._prepareTransactionParams(e[0]||{}),n=this.initializeRelay(),s=await n.signEthereumTransaction(t);if(ed(s))throw s;return s.result}async _eth_sendRawTransaction(e){let t=U(e[0]),n=this.initializeRelay(),s=await n.submitEthereumTransaction(t,this.getChainId());if(ed(s))throw s;return s.result}async _eth_sendTransaction(e){let t=this._prepareTransactionParams(e[0]||{}),n=this.initializeRelay(),s=await n.signAndSubmitEthereumTransaction(t);if(ed(s))throw s;return s.result}async signTypedData(e){let{method:t,params:n}=e;if(!Array.isArray(n))throw p.rpc.invalidParams();let s=n[+("eth_signTypedData_v1"===t)],r=n[+("eth_signTypedData_v1"!==t)];this._ensureKnownAddress(s);let i=this.initializeRelay(),a=await i.sendRequest({method:"signEthereumMessage",params:{address:j(s),message:M(({eth_signTypedData_v1:el.hashForSignTypedDataLegacy,eth_signTypedData_v3:el.hashForSignTypedData_v3,eth_signTypedData_v4:el.hashForSignTypedData_v4,eth_signTypedData:el.hashForSignTypedData_v4})[t]({data:function(e){if("string"==typeof e)return JSON.parse(e);if("object"==typeof e)return e;throw p.rpc.invalidParams(`Not a JSON string or an object: ${String(e)}`)}(r)}),!0),typedDataJson:JSON.stringify(r,null,2),addPrefix:!1}});if(ed(a))throw a;return a.result}initializeRelay(){return this._relay||(this._relay=new tR({linkAPIUrl:tT,storage:this._storage,metadata:this.metadata,accountsCallback:this._setAddresses.bind(this),chainCallback:this.updateProviderInfo.bind(this)})),this._relay}}let tH="SignerType",tW=new i("CBWSDK","SignerConfigurator");async function tq(e){let{communicator:t,metadata:n,handshakeRequest:s,callback:r}=e;tV(t,n,r).catch(()=>{});let i={id:crypto.randomUUID(),event:"selectSignerType",data:Object.assign(Object.assign({},e.preference),{handshakeRequest:s})},{data:a}=await t.postRequestAndWaitForResponse(i);return a}async function tV(e,t,n){await e.onMessage(({event:e})=>"WalletLinkSessionRequest"===e);let s=new tB({metadata:t,callback:n});e.postMessage({event:"WalletLinkUpdate",data:{session:s.getSession()}}),await s.handshake(),e.postMessage({event:"WalletLinkUpdate",data:{connected:!0}})}let tZ=`Coinbase Wallet SDK requires the Cross-Origin-Opener-Policy header to not be set to 'same-origin'. This is to ensure that the SDK can communicate with the Coinbase Smart Wallet app.

Please see https://www.smartwallet.dev/guides/tips/popup-tips#cross-origin-opener-policy for more information.`,{checkCrossOriginOpenerPolicy:tz,getCrossOriginOpenerPolicy:tF}={getCrossOriginOpenerPolicy:()=>void 0===s?"undefined":s,checkCrossOriginOpenerPolicy:async()=>{if("undefined"==typeof window){s="non-browser-env";return}try{let e=`${window.location.origin}${window.location.pathname}`,t=await fetch(e,{method:"HEAD"});if(!t.ok)throw Error(`HTTP error! status: ${t.status}`);let n=t.headers.get("Cross-Origin-Opener-Policy");s=null!=n?n:"null","same-origin"===s&&console.error(tZ)}catch(e){console.error("Error checking Cross-Origin-Opener-Policy:",e.message),s="error"}}};class tK{constructor({url:e="https://keys.coinbase.com/connect",metadata:t,preference:n}){this.popup=null,this.listeners=new Map,this.postMessage=async e=>{(await this.waitForPopupLoaded()).postMessage(e,this.url.origin)},this.postRequestAndWaitForResponse=async e=>{let t=this.onMessage(({requestId:t})=>t===e.id);return this.postMessage(e),await t},this.onMessage=async e=>new Promise((t,n)=>{let s=n=>{if(n.origin!==this.url.origin)return;let r=n.data;e(r)&&(t(r),window.removeEventListener("message",s),this.listeners.delete(s))};window.addEventListener("message",s),this.listeners.set(s,{reject:n})}),this.disconnect=()=>{var e;(e=this.popup)&&!e.closed&&e.close(),this.popup=null,this.listeners.forEach(({reject:e},t)=>{e(p.provider.userRejectedRequest("Request rejected")),window.removeEventListener("message",t)}),this.listeners.clear()},this.waitForPopupLoaded=async()=>this.popup&&!this.popup.closed?(this.popup.focus(),this.popup):(this.popup=function(e){let t=(window.innerWidth-420)/2+window.screenX,n=(window.innerHeight-540)/2+window.screenY;(function(e){for(let[t,n]of Object.entries({sdkName:et,sdkVersion:ee,origin:window.location.origin,coop:tF()}))e.searchParams.append(t,n.toString())})(e);let s=`wallet_${crypto.randomUUID()}`,r=window.open(e,s,`width=420, height=540, left=${t}, top=${n}`);if(null==r||r.focus(),!r)throw p.rpc.internal("Pop up window failed to open");return r}(this.url),this.onMessage(({event:e})=>"PopupUnload"===e).then(this.disconnect).catch(()=>{}),this.onMessage(({event:e})=>"PopupLoaded"===e).then(e=>{this.postMessage({requestId:e.id,data:{version:ee,metadata:this.metadata,preference:this.preference,location:window.location.toString()}})}).then(()=>{if(!this.popup)throw p.rpc.internal();return this.popup})),this.url=new URL(e),this.metadata=t,this.preference=n}}var t$=n(825),tG=n.n(t$);class tJ extends tG(){}var tY=function(e,t){var n={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(n[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,s=Object.getOwnPropertySymbols(e);r<s.length;r++)0>t.indexOf(s[r])&&Object.prototype.propertyIsEnumerable.call(e,s[r])&&(n[s[r]]=e[s[r]]);return n};class tQ extends tJ{constructor(e){var{metadata:t}=e,n=e.preference,{keysUrl:s}=n,r=tY(n,["keysUrl"]);super(),this.signer=null,this.isCoinbaseWallet=!0,this.metadata=t,this.preference=r,this.communicator=new tK({url:s,metadata:t,preference:r});let i=tW.getItem(tH);i&&(this.signer=this.initSigner(i))}async request(e){try{if(!function(e){if(!e||"object"!=typeof e||Array.isArray(e))throw p.rpc.invalidParams({message:"Expected a single, non-array, object argument.",data:e});let{method:t,params:n}=e;if("string"!=typeof t||0===t.length)throw p.rpc.invalidParams({message:"'args.method' must be a non-empty string.",data:e});if(void 0!==n&&!Array.isArray(n)&&("object"!=typeof n||null===n))throw p.rpc.invalidParams({message:"'args.params' must be an object or array if provided.",data:e});switch(t){case"eth_sign":case"eth_signTypedData_v2":case"eth_subscribe":case"eth_unsubscribe":throw p.provider.unsupportedMethod()}}(e),!this.signer)switch(e.method){case"eth_requestAccounts":{let t=await this.requestSignerSelection(e),n=this.initSigner(t);await n.handshake(e),this.signer=n,tW.setItem(tH,t);break}case"wallet_sendCalls":{let t=this.initSigner("scw");await t.handshake({method:"handshake"});let n=await t.request(e);return await t.cleanup(),n}case"wallet_getCallsStatus":return en(e,"http://rpc.wallet.coinbase.com");case"net_version":return 1;case"eth_chainId":return P(1);default:throw p.provider.unauthorized("Must call 'eth_requestAccounts' before other methods")}return await this.signer.request(e)}catch(t){let{code:e}=t;return e===a.provider.unauthorized&&this.disconnect(),Promise.reject(function(e){let t=function(e,{shouldIncludeStack:t=!1}={}){var n,s;let r={};if(e&&"object"==typeof e&&!Array.isArray(e)&&d(e,"code")&&Number.isInteger(n=e.code)&&(o[n.toString()]||(s=n)>=-32099&&s<=-32e3))r.code=e.code,e.message&&"string"==typeof e.message?(r.message=e.message,d(e,"data")&&(r.data=e.data)):(r.message=l(r.code),r.data={originalError:h(e)});else r.code=a.rpc.internal,r.message=u(e,"message")?e.message:c,r.data={originalError:h(e)};return t&&(r.stack=u(e,"stack")?e.stack:void 0),r}(function(e){var t;if("string"==typeof e)return{message:e,code:a.rpc.internal};if(ed(e)){let n=e.errorMessage,s=null!==(t=e.errorCode)&&void 0!==t?t:n.match(/(denied|rejected)/i)?a.provider.userRejectedRequest:void 0;return Object.assign(Object.assign({},e),{message:n,code:s,data:{method:e.method}})}return e}(e),{shouldIncludeStack:!0}),n=new URL("https://docs.cloud.coinbase.com/wallet-sdk/docs/errors");return n.searchParams.set("version",ee),n.searchParams.set("code",t.code.toString()),n.searchParams.set("message",t.message),Object.assign(Object.assign({},t),{docUrl:n.href})}(t))}}async enable(){return console.warn('.enable() has been deprecated. Please use .request({ method: "eth_requestAccounts" }) instead.'),await this.request({method:"eth_requestAccounts"})}async disconnect(){var e;await (null===(e=this.signer)||void 0===e?void 0:e.cleanup()),this.signer=null,i.clearAll(),this.emit("disconnect",p.provider.disconnected("User initiated disconnection"))}requestSignerSelection(e){return tq({communicator:this.communicator,preference:this.preference,metadata:this.metadata,handshakeRequest:e,callback:this.emit.bind(this)})}initSigner(e){return function(e){let{signerType:t,metadata:n,communicator:s,callback:r}=e;switch(t){case"scw":return new ec({metadata:n,callback:r,communicator:s});case"walletlink":return new tB({metadata:n,callback:r})}}({signerType:e,metadata:this.metadata,communicator:this.communicator,callback:this.emit.bind(this)})}}function tX(e){if(e){if(!["all","smartWalletOnly","eoaOnly"].includes(e.options))throw Error(`Invalid options: ${e.options}`);if(e.attribution&&void 0!==e.attribution.auto&&void 0!==e.attribution.dataSuffix)throw Error("Attribution cannot contain both auto and dataSuffix properties")}}class t0{constructor(e){this.metadata={appName:e.appName||"Dapp",appLogoUrl:e.appLogoUrl||function(){let e=document.querySelector('link[sizes="192x192"]')||document.querySelector('link[sizes="180x180"]')||document.querySelector('link[rel="icon"]')||document.querySelector('link[rel="shortcut icon"]'),{protocol:t,host:n}=document.location,s=e?e.getAttribute("href"):null;return!s||s.startsWith("javascript:")||s.startsWith("vbscript:")?`${t}//${n}/favicon.ico`:s.startsWith("http://")||s.startsWith("https://")||s.startsWith("data:")?s:s.startsWith("//")?t+s:`${t}//${n}${s}`}(),appChainIds:e.appChainIds||[]},this.storeLatestVersion(),tz()}makeWeb3Provider(e={options:"all"}){var t;tX(e);let n={metadata:this.metadata,preference:e};return null!==(t=es(n))&&void 0!==t?t:new tQ(n)}getCoinbaseWalletLogo(e,t=240){return r(e,t)}storeLatestVersion(){new i("CBWSDK").setItem("VERSION",ee)}}let t1={options:"all"};function t2(e){var t;new i("CBWSDK").setItem("VERSION",ee),tz();let n={metadata:{appName:e.appName||"Dapp",appLogoUrl:e.appLogoUrl||"",appChainIds:e.appChainIds||[]},preference:Object.assign(t1,null!==(t=e.preference)&&void 0!==t?t:{})};tX(n.preference);let s=null;return{getProvider:()=>(s||(s=function(e){var t;let n={metadata:e.metadata,preference:e.preference};return null!==(t=es(n))&&void 0!==t?t:new tQ(n)}(n)),s)}}let t3=t0}}]);