import type { SignedInSessionResource, UserButtonProps, UserResource } from '@clerk/types';
type UseMultisessionActionsParams = {
    user: UserResource | null | undefined;
    actionCompleteCallback?: () => void;
    navigateAfterSignOut?: () => any;
    navigateAfterMultiSessionSingleSignOut?: () => any;
    afterSwitchSessionUrl?: string;
    userProfileUrl?: string;
    signInUrl?: string;
} & Pick<UserButtonProps, 'userProfileMode' | 'appearance' | 'userProfileProps'>;
export declare const useMultisessionActions: (opts: UseMultisessionActionsParams) => {
    handleSignOutSessionClicked: (session: SignedInSessionResource) => () => Promise<void>;
    handleManageAccountClicked: () => void | Promise<unknown>;
    handleUserProfileActionClicked: (__experimental_startPath?: string) => void | Promise<unknown>;
    handleSignOutAllClicked: () => Promise<void>;
    handleSessionClicked: (session: SignedInSessionResource) => () => Promise<void>;
    handleAddAccountClicked: () => Promise<unknown>;
    otherSessions: SignedInSessionResource[];
    signedInSessions: SignedInSessionResource[];
};
export {};
