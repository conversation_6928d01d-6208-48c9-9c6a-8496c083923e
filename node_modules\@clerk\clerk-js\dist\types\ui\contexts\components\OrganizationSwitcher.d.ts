import type { OrganizationResource, UserResource } from '@clerk/types';
import type { OrganizationSwitcherCtx } from '../../types';
export declare const OrganizationSwitcherContext: import("react").Context<OrganizationSwitcherCtx | null>;
export declare const useOrganizationSwitcherContext: () => {
    hidePersonal: boolean;
    organizationProfileMode: "modal" | "navigation";
    createOrganizationMode: "modal" | "navigation";
    skipInvitationScreen: boolean;
    hideSlug: boolean;
    afterCreateOrganizationUrl: string | ((organization: OrganizationResource) => string);
    afterLeaveOrganizationUrl: string;
    navigateOrganizationProfile: () => Promise<unknown>;
    navigateCreateOrganization: () => Promise<unknown>;
    navigateAfterSelectOrganization: (organization: OrganizationResource) => Promise<unknown>;
    navigateAfterSelectPersonal: (user: UserResource) => Promise<unknown>;
    afterSelectOrganizationUrl: (organization: OrganizationResource) => string | undefined;
    afterSelectPersonalUrl: (user: UserResource) => string | undefined;
    componentName: "OrganizationSwitcher";
    createOrganizationUrl: string;
    organizationProfileUrl: string;
    defaultOpen?: boolean;
    __experimental_asStandalone?: boolean | ((opened: boolean) => void);
    afterSwitchOrganizationUrl?: string;
    appearance?: import("@clerk/types").OrganizationSwitcherTheme;
    organizationProfileProps?: Pick<import("@clerk/types").OrganizationProfileProps, "appearance" | "customPages">;
    mode?: "modal" | "mounted";
} | {
    hidePersonal: boolean;
    organizationProfileMode: "modal" | "navigation";
    createOrganizationMode: "modal" | "navigation";
    skipInvitationScreen: boolean;
    hideSlug: boolean;
    afterCreateOrganizationUrl: string | ((organization: OrganizationResource) => string);
    afterLeaveOrganizationUrl: string;
    navigateOrganizationProfile: () => Promise<unknown>;
    navigateCreateOrganization: () => Promise<unknown>;
    navigateAfterSelectOrganization: (organization: OrganizationResource) => Promise<unknown>;
    navigateAfterSelectPersonal: (user: UserResource) => Promise<unknown>;
    afterSelectOrganizationUrl: (organization: OrganizationResource) => string | undefined;
    afterSelectPersonalUrl: (user: UserResource) => string | undefined;
    componentName: "OrganizationSwitcher";
    createOrganizationUrl: string;
    organizationProfileUrl?: never;
    defaultOpen?: boolean;
    __experimental_asStandalone?: boolean | ((opened: boolean) => void);
    afterSwitchOrganizationUrl?: string;
    appearance?: import("@clerk/types").OrganizationSwitcherTheme;
    organizationProfileProps?: Pick<import("@clerk/types").OrganizationProfileProps, "appearance" | "customPages">;
    mode?: "modal" | "mounted";
} | {
    hidePersonal: boolean;
    organizationProfileMode: "modal" | "navigation";
    createOrganizationMode: "modal" | "navigation";
    skipInvitationScreen: boolean;
    hideSlug: boolean;
    afterCreateOrganizationUrl: string | ((organization: OrganizationResource) => string);
    afterLeaveOrganizationUrl: string;
    navigateOrganizationProfile: () => Promise<unknown>;
    navigateCreateOrganization: () => Promise<unknown>;
    navigateAfterSelectOrganization: (organization: OrganizationResource) => Promise<unknown>;
    navigateAfterSelectPersonal: (user: UserResource) => Promise<unknown>;
    afterSelectOrganizationUrl: (organization: OrganizationResource) => string | undefined;
    afterSelectPersonalUrl: (user: UserResource) => string | undefined;
    componentName: "OrganizationSwitcher";
    createOrganizationUrl?: never;
    organizationProfileUrl: string;
    defaultOpen?: boolean;
    __experimental_asStandalone?: boolean | ((opened: boolean) => void);
    afterSwitchOrganizationUrl?: string;
    appearance?: import("@clerk/types").OrganizationSwitcherTheme;
    organizationProfileProps?: Pick<import("@clerk/types").OrganizationProfileProps, "appearance" | "customPages">;
    mode?: "modal" | "mounted";
} | {
    hidePersonal: boolean;
    organizationProfileMode: "modal" | "navigation";
    createOrganizationMode: "modal" | "navigation";
    skipInvitationScreen: boolean;
    hideSlug: boolean;
    afterCreateOrganizationUrl: string | ((organization: OrganizationResource) => string);
    afterLeaveOrganizationUrl: string;
    navigateOrganizationProfile: () => Promise<unknown>;
    navigateCreateOrganization: () => Promise<unknown>;
    navigateAfterSelectOrganization: (organization: OrganizationResource) => Promise<unknown>;
    navigateAfterSelectPersonal: (user: UserResource) => Promise<unknown>;
    afterSelectOrganizationUrl: (organization: OrganizationResource) => string | undefined;
    afterSelectPersonalUrl: (user: UserResource) => string | undefined;
    componentName: "OrganizationSwitcher";
    createOrganizationUrl?: never;
    organizationProfileUrl?: never;
    defaultOpen?: boolean;
    __experimental_asStandalone?: boolean | ((opened: boolean) => void);
    afterSwitchOrganizationUrl?: string;
    appearance?: import("@clerk/types").OrganizationSwitcherTheme;
    organizationProfileProps?: Pick<import("@clerk/types").OrganizationProfileProps, "appearance" | "customPages">;
    mode?: "modal" | "mounted";
};
