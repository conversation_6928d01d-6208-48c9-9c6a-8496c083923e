import type { AddPaymentSourceParams, GetPaymentSourcesParams, InitializePaymentSourceParams } from '@clerk/types';
import { CommerceInitializedPaymentSource, CommercePaymentSource } from '../../resources/internal';
export declare const initializePaymentSource: (params: InitializePaymentSourceParams) => Promise<CommerceInitializedPaymentSource>;
export declare const addPaymentSource: (params: AddPaymentSourceParams) => Promise<CommercePaymentSource>;
export declare const getPaymentSources: (params: GetPaymentSourcesParams) => Promise<{
    total_count: number;
    data: CommercePaymentSource[];
}>;
