/**
 * These helpers have been extracted from the following libraries,
 * converted to Typescript and adapted to our needs.
 *
 * https://github.com/Qix-/color-convert
 * https://github.com/Qix-/color-name
 * https://github.com/Qix-/color
 */
import type { HslaColor, HslaColorString } from '@clerk/types';
export declare const colors: {
    toHslaColor: (str: string) => HslaColor;
    toHslaString: (hsla: HslaColor | string) => HslaColorString;
    adjustForLightness: (color: string | undefined, lightness?: number) => string | undefined;
    changeHslaLightness: (color: HslaColor, num: number) => HslaColor;
    setHslaAlpha: (color: HslaColor, num: number) => HslaColor;
    lighten: (color: string | undefined, percentage?: number) => string | undefined;
    makeTransparent: (color: string | undefined, percentage?: number) => string | undefined;
    makeSolid: (color: string | undefined) => string | undefined;
    setAlpha: (color: string, alpha: number) => string;
};
