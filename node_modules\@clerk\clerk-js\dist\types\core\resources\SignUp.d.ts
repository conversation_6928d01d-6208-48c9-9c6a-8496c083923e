import type { AttemptEmailAddressVerificationParams, AttemptPhoneNumberVerificationParams, AttemptVerificationParams, AttemptWeb3WalletVerificationParams, AuthenticateWithPopupParams, AuthenticateWithRedirectParams, AuthenticateWithWeb3Params, CreateEmailLinkFlowReturn, PrepareEmailAddressVerificationParams, PreparePhoneNumberVerificationParams, PrepareVerificationParams, PrepareWeb3WalletVerificationParams, SignUpAuthenticateWithWeb3Params, SignUpCreateParams, SignUpField, SignUpIdentificationField, SignUpJSON, SignUpJSONSnapshot, SignUpResource, SignUpStatus, SignUpUpdateParams, StartEmailLinkFlowParams } from '@clerk/types';
import { createValidatePassword } from '../../utils/passwords/password';
import { BaseResource, SignUpVerifications } from './internal';
declare global {
    interface Window {
        ethereum: any;
    }
}
export declare class SignUp extends BaseResource implements SignUpResource {
    pathRoot: string;
    id: string | undefined;
    status: SignUpStatus | null;
    requiredFields: SignUpField[];
    optionalFields: SignUpField[];
    missingFields: SignUpField[];
    unverifiedFields: SignUpIdentificationField[];
    verifications: SignUpVerifications;
    username: string | null;
    firstName: string | null;
    lastName: string | null;
    emailAddress: string | null;
    phoneNumber: string | null;
    web3wallet: string | null;
    externalAccount: any;
    hasPassword: boolean;
    unsafeMetadata: SignUpUnsafeMetadata;
    createdSessionId: string | null;
    createdUserId: string | null;
    abandonAt: number | null;
    legalAcceptedAt: number | null;
    constructor(data?: SignUpJSON | SignUpJSONSnapshot | null);
    create: (_params: SignUpCreateParams) => Promise<SignUpResource>;
    prepareVerification: (params: PrepareVerificationParams) => Promise<this>;
    attemptVerification: (params: AttemptVerificationParams) => Promise<SignUpResource>;
    prepareEmailAddressVerification: (params?: PrepareEmailAddressVerificationParams) => Promise<SignUpResource>;
    attemptEmailAddressVerification: (params: AttemptEmailAddressVerificationParams) => Promise<SignUpResource>;
    createEmailLinkFlow: () => CreateEmailLinkFlowReturn<StartEmailLinkFlowParams, SignUpResource>;
    preparePhoneNumberVerification: (params?: PreparePhoneNumberVerificationParams) => Promise<SignUpResource>;
    attemptPhoneNumberVerification: (params: AttemptPhoneNumberVerificationParams) => Promise<SignUpResource>;
    prepareWeb3WalletVerification: (params?: PrepareWeb3WalletVerificationParams) => Promise<SignUpResource>;
    attemptWeb3WalletVerification: (params: AttemptWeb3WalletVerificationParams) => Promise<SignUpResource>;
    authenticateWithWeb3: (params: AuthenticateWithWeb3Params & {
        unsafeMetadata?: SignUpUnsafeMetadata;
        legalAccepted?: boolean;
    }) => Promise<SignUpResource>;
    authenticateWithMetamask: (params?: SignUpAuthenticateWithWeb3Params & {
        legalAccepted?: boolean;
    }) => Promise<SignUpResource>;
    authenticateWithCoinbaseWallet: (params?: SignUpAuthenticateWithWeb3Params & {
        legalAccepted?: boolean;
    }) => Promise<SignUpResource>;
    authenticateWithOKXWallet: (params?: SignUpAuthenticateWithWeb3Params & {
        legalAccepted?: boolean;
    }) => Promise<SignUpResource>;
    private authenticateWithRedirectOrPopup;
    authenticateWithRedirect: (params: AuthenticateWithRedirectParams & {
        unsafeMetadata?: SignUpUnsafeMetadata;
    }) => Promise<void>;
    authenticateWithPopup: (params: AuthenticateWithPopupParams & {
        unsafeMetadata?: SignUpUnsafeMetadata;
    }) => Promise<void>;
    update: (params: SignUpUpdateParams) => Promise<SignUpResource>;
    upsert: (params: SignUpCreateParams | SignUpUpdateParams) => Promise<SignUpResource>;
    validatePassword: ReturnType<typeof createValidatePassword>;
    protected fromJSON(data: SignUpJSON | SignUpJSONSnapshot | null): this;
    __internal_toSnapshot(): SignUpJSONSnapshot;
    private clientBypass;
    /**
     * We delegate bot detection to the following providers, instead of relying on turnstile exclusively
     */
    protected shouldBypassCaptchaForAttempt(params: SignUpCreateParams): boolean;
}
