import type { PasswordSettingsData, ZxcvbnResult } from '@clerk/types';
import type { zxcvbnFN } from '../zxcvbn';
export type PasswordStrength = {
    state: 'excellent';
    result: ZxcvbnResult;
} | {
    state: 'pass' | 'fail';
    keys: string[];
    result: ZxcvbnResult;
};
type CreateValidatePasswordStrength = (options: Pick<PasswordSettingsData, 'min_zxcvbn_strength'> & {
    onResult?: (res: ZxcvbnResult) => void;
}) => (zxcvbn: zxcvbnFN) => (password: string) => PasswordStrength;
export declare const createValidatePasswordStrength: CreateValidatePasswordStrength;
export {};
