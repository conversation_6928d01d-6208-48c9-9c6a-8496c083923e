"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["710"],{5518:function(e,t,i){i.d(t,{Vh:()=>m,Vs:()=>b,bx:()=>p,mQ:()=>_,s2:()=>g,t3:()=>d,xT:()=>u}),i(5027);var r=i(2208),l=i(7772),a=i(7623),n=i(577);let o=e=>t=>"safeIdentifier"in t&&t.safeIdentifier===e;function s(e){if((0,r.iW)()){let t=e.find(({strategy:e})=>"passkey"===e);if(t)return t}return null}function d(e,t,i){return e&&0!==e.length?i===l.kJ.Password?function(e,t){let i=s(e);if(i)return i;let r=e.sort(n.sZ)[0];return"password"===r.strategy?r:e.find(o(t))||r||null}(e,t):function(e,t){let i=s(e);if(i)return i;let r=e.sort(n.b8),l=r.find(o(t));if(l)return l;let a=r[0];return"email_link"===a.strategy?a:e.find(o(t))||a||null}(e,t):null}let c=["passkey","email_code","password","phone_code","email_link"];function u(e){return!!e&&c.includes(e.strategy)}function p(e){if(!e||0===e.length)return null;let t=e.find(e=>"totp"===e.strategy);if(t)return t;let i=e.find(e=>"phone_code"===e.strategy);return i||e[0]}let h=["reset_password_phone_code","reset_password_email_code"],m=e=>!!e&&h.includes(e),f=e=>/^\S+@\S+\.\S+$/.test(e);function g(e){return"tel"===e.type?"phoneNumber":f(e.value)?"emailAddress":"username"}let b=(e,t,i)=>{if(!t)return null;let r=e.find(e=>"strategy"===e.id)?.value;if(r&&"phone_code"!==r)return null;let l=e.find(e=>e.id===i)?.value;if(!l||!l?.startsWith("+"))return null;let n=(0,a.jR)(l,t);return"sms"===n?null:n},_=(e,t,i)=>{if(!e||!t||"phoneNumber"!==t||!i||!i?.startsWith("+"))return null;let r=(0,a.jR)(i,e);return"sms"===r?null:r}},5049:function(e,t,i){i.r(t),i.d(t,{SignUp:()=>et,SignUpSSOCallback:()=>k,SignUpContinue:()=>z,SignUpStart:()=>D,SignUpModal:()=>ei,SignUpVerifyEmail:()=>j,SignUpVerifyPhone:()=>J});var r=i(9109),l=i(3799),a=i(9144),n=i(7508),o=i(5112),s=i(1576),d=i(9541),c=i(4511),u=i(4676),p=i(4455),h=i(2672),m=i(2654),f=i(1455),g=i(8350),b=i(7623),_=i(431),v=i(8314),y=i(7321),A=i(1201);let U=e=>{let{handleSubmit:t,fields:i,formState:l,canToggleEmailPhone:a,onlyLegalAcceptedMissing:n=!1,handleEmailPhoneToggle:o}=e,{showOptionalFields:s}=(0,d.useAppearance)().parsedLayout,c=e=>("emailAddress"===e||"phoneNumber"===e)&&a?!!i[e]:!!i[e]&&(s||i[e]?.required);return(0,r.BX)(_.l.Root,{onSubmit:t,gap:8,children:[!n&&(0,r.BX)(d.Col,{gap:6,children:[(c("firstName")||c("lastName"))&&(0,r.BX)(_.l.ControlRow,{elementId:"name",sx:{[A.mqu.sm]:{flexWrap:"wrap"}},children:[c("firstName")&&(0,r.tZ)(_.l.PlainInput,{...l.firstName.props,isRequired:i.firstName?.required,isOptional:!i.firstName?.required}),c("lastName")&&(0,r.tZ)(_.l.PlainInput,{...l.lastName.props,isRequired:i.lastName?.required,isOptional:!i.lastName?.required})]}),c("username")&&(0,r.tZ)(_.l.ControlRow,{elementId:"username",children:(0,r.tZ)(_.l.PlainInput,{...l.username.props,isRequired:i.username?.required,isOptional:!i.username?.required})}),c("emailAddress")&&(0,r.tZ)(_.l.ControlRow,{elementId:"emailAddress",children:(0,r.tZ)(_.l.PlainInput,{...l.emailAddress.props,isRequired:i.emailAddress?.required,isOptional:!i.emailAddress?.required,isDisabled:i.emailAddress?.disabled,actionLabel:a?(0,d.localizationKeys)("signUp.start.actionLink__use_phone"):void 0,onActionClicked:a?()=>o("phoneNumber"):void 0})}),c("phoneNumber")&&(0,r.tZ)(_.l.ControlRow,{elementId:"phoneNumber",children:(0,r.tZ)(_.l.PhoneInput,{...l.phoneNumber.props,isRequired:i.phoneNumber?.required,isOptional:!i.phoneNumber?.required,actionLabel:a?(0,d.localizationKeys)("signUp.start.actionLink__use_email"):void 0,onActionClicked:a?()=>o("emailAddress"):void 0})}),c("password")&&(0,r.tZ)(_.l.ControlRow,{elementId:"password",children:(0,r.tZ)(_.l.PasswordInput,{...l.password.props,isRequired:i.password?.required,isOptional:!i.password?.required})})]}),(0,r.BX)(d.Col,{center:!0,children:[(0,r.tZ)(y.S,{}),(0,r.BX)(d.Col,{gap:6,sx:{width:"100%"},children:[c("legalAccepted")&&(0,r.tZ)(_.l.ControlRow,{elementId:"legalAccepted",children:(0,r.tZ)(v.H,{...l.legalAccepted.props,isRequired:i.legalAccepted?.required})}),(0,r.tZ)(_.l.SubmitButton,{hasArrow:!0,localizationKey:(0,d.localizationKeys)("formButtonPrimary")})]})]})]})};var S=i(5027);let C=["emailAddress","phoneNumber","username","firstName","lastName","password","ticket","legalAccepted"];function Z(e){return C.reduce((t,i)=>{let r=function(e,t){switch(e){case"emailAddress":return function({attributes:e,hasTicket:t,hasEmail:i,activeCommIdentifierType:r,isProgressiveSignUp:l}){if(l){if(!((!t||t&&i)&&e.email_address?.enabled)||P(e,l)&&"emailAddress"!==r)return;return{required:!!e.email_address?.required,disabled:!!t&&!!i}}if((!t||t&&i)&&e.email_address?.enabled&&e.email_address?.used_for_first_factor&&"emailAddress"===r)return{required:!0,disabled:!!t&&!!i}}(t);case"phoneNumber":return function({attributes:e,hasTicket:t,activeCommIdentifierType:i,isProgressiveSignUp:r}){if(r){if(!e.phone_number?.enabled||P(e,r)&&"phoneNumber"!==i)return;return{required:!!e.phone_number?.required}}if(!t&&e.phone_number?.enabled&&e.phone_number.used_for_first_factor&&"phoneNumber"===i)return{required:!0}}(t);case"password":return function(e){if(e.password?.enabled&&e.password.required)return{required:!!e.password?.required}}(t.attributes);case"ticket":return function(e){if(e)return{required:!0}}(t.hasTicket);case"legalAccepted":return function(e){if(e)return{required:!0}}(t.legalConsentRequired);case"username":case"firstName":case"lastName":return function(e,t){let i=(0,S.a1)(e);if(t[i]?.enabled)return{required:t[i]?.required}}(e,t.attributes);default:return}}(i,e);return r&&(t[i]=r),t},{})}let N=(e,t,i)=>{if(i?.emailAddress)return"emailAddress";if(i?.phoneNumber)return"phoneNumber";if(P(e,t))return"emailAddress";let{email_address:r,phone_number:l}=e;return(r?.enabled&&t?r.required:r?.used_for_first_factor)?"emailAddress":(l?.enabled&&t?l.required:l?.used_for_first_factor)?"phoneNumber":null};function P(e,t){let{email_address:i,phone_number:r}=e;return!!(t?i?.enabled&&r?.enabled&&!i.required&&!r.required:i?.used_for_first_factor&&r?.used_for_first_factor)}var E=i(138);let w=a.memo(e=>{let t=(0,l.cL)(),{navigate:i}=(0,u.useRouter)(),a=(0,h.useCardState)(),n=(0,s.useSignUpContext)(),o=(0,s.useCoreSignUp)(),d=n.ssoCallbackUrl,c=n.afterSignUpUrl||"/",p="popup"===n.oauthFlow||"auto"===n.oauthFlow&&(0,b.tc)(),{continueSignUp:m=!1,onAlternativePhoneCodeProviderClick:f,...g}=e;return(0,r.tZ)(E.L,{...g,idleAfterDelay:!p,oauthCallback:t=>{if(p){let i=window.open("about:blank","","width=600,height=800"),r=setInterval(()=>{(!i||i.closed)&&(clearInterval(r),a.setIdle())},500);return o.authenticateWithPopup({strategy:t,redirectUrl:d,redirectUrlComplete:c,popup:i,continueSignUp:m,unsafeMetadata:n.unsafeMetadata,legalAccepted:e.legalAccepted,oidcPrompt:n.oidcPrompt}).catch(e=>(0,b.S3)(e,[],a.setError))}return o.authenticateWithRedirect({continueSignUp:m,redirectUrl:d,redirectUrlComplete:c,strategy:t,unsafeMetadata:n.unsafeMetadata,legalAccepted:e.legalAccepted,oidcPrompt:n.oidcPrompt}).catch(e=>(0,b.S3)(e,[],a.setError))},web3Callback:r=>t.authenticateWithWeb3({customNavigate:i,redirectUrl:c,signUpContinueUrl:"continue",unsafeMetadata:n.unsafeMetadata,strategy:r,legalAccepted:e.legalAccepted}).catch(e=>(0,b.Ht)(e,a.setError)),alternativePhoneCodeCallback:e=>{f?.(e)}})});var I=i(6052);let z=(0,h.withCardStateProvider)(function(){let e=(0,h.useCardState)(),t=(0,l.cL)(),{navigate:i}=(0,u.useRouter)(),{displayConfig:n,userSettings:o}=(0,s.useEnvironment)(),{attributes:c,usernameSettings:_}=o,{t:v,locale:y}=(0,d.useLocalizations)(),{afterSignUpUrl:A,signInUrl:S,unsafeMetadata:C,initialValues:E={},isCombinedFlow:z}=(0,s.useSignUpContext)(),K=(0,s.useCoreSignUp)(),k=!!a.useContext(s.SignInContext),R=!!(z&&k),L=o.signUp.progressive,[F,T]=a.useState(N(c,o.signUp.progressive)),O=(0,s.useSignUpContext)(),B={firstName:(0,b.Yp)("firstName",E.firstName||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__firstName"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__firstName")}),lastName:(0,b.Yp)("lastName",E.lastName||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__lastName"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__lastName")}),emailAddress:(0,b.Yp)("emailAddress",E.emailAddress||K.emailAddress||"",{type:"email",label:(0,d.localizationKeys)("formFieldLabel__emailAddress"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__emailAddress")}),username:(0,b.Yp)("username",E.username||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__username"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__username"),transformer:e=>e.trim(),buildErrorMessage:e=>(0,b.fq)(e,{t:v,locale:y,usernameSettings:_})}),phoneNumber:(0,b.Yp)("phoneNumber",E.phoneNumber||"",{type:"tel",label:(0,d.localizationKeys)("formFieldLabel__phoneNumber"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__phoneNumber")}),password:(0,b.Yp)("password","",{type:"password",label:(0,d.localizationKeys)("formFieldLabel__password"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__password"),validatePassword:!0}),legalAccepted:(0,b.Yp)("legalAccepted","",{type:"checkbox",label:"",defaultChecked:!1,isRequired:o.signUp.legal_consent_enabled||!1})},q=(0,a.useMemo)(()=>K.missingFields&&1===K.missingFields.length&&"legal_accepted"===K.missingFields[0]&&K.unverifiedFields&&0===K.unverifiedFields.length,[K.missingFields,K.unverifiedFields]);if((0,a.useEffect)(()=>{K.id||!0===t.__internal_setActiveInProgress||i(n.signUpUrl)},[]),!K.id)return(0,r.tZ)(f.W,{});let x=!!B.emailAddress.value,M=K.verifications?.externalAccount?.status=="verified",D=Z({attributes:c,hasEmail:x,activeCommIdentifierType:F,signUp:K,isProgressiveSignUp:L,legalConsentRequired:o.signUp.legal_consent_enabled});!function(e,t){if(t){let i=!!t.emailAddress,r=t.verifications?.emailAddress?.status==="verified",l=t.verifications?.phoneNumber?.status==="verified",a=t.verifications?.externalAccount?.status==="verified",n=t.verifications?.web3Wallet?.status==="verified",o=null!==t.legalAcceptedAt;i&&r&&delete e.emailAddress,l&&delete e.phoneNumber,(a||n)&&delete e.password,t.firstName&&delete e.firstName,t.lastName&&delete e.lastName,t.username&&delete e.username,o&&delete e.legalAccepted,Object.entries(e).forEach(([t,i])=>{i&&!i.required&&delete e[t]})}}(D,K);let V=o.authenticatableSocialStrategies,X=async r=>{r.preventDefault();let l=Object.entries(D).reduce((e,[t,i])=>[...e,...i&&B[t]?[B[t]]:[]],[]);C&&l.push({id:"unsafeMetadata",value:C});let a=!!l.find(e=>"emailAddress"===e.id)?.value,n=!!l.find(e=>"phoneNumber"===e.id)?.value;return K.missingFields.includes("email_address")&&K.missingFields.includes("phone_number")&&!a&&!n&&P(c,L)&&(l.push(B.emailAddress),l.push(B.phoneNumber)),e.setLoading(),e.setError(void 0),K.update((0,b.ni)(l)).then(e=>(0,I.completeSignUpFlow)({signUp:e,verifyEmailPath:"./verify-email-address",verifyPhonePath:"./verify-phone-number",handleComplete:()=>t.setActive({session:e.createdSessionId,redirectUrl:A}),navigate:i,oidcPrompt:O.oidcPrompt})).catch(t=>(0,b.S3)(t,l,e.setError)).finally(()=>e.setIdle())},W=P(c,L),H=!M&&V.length>0,Y=q?(0,d.localizationKeys)("signUp.legalConsent.continue.title"):(0,d.localizationKeys)("signUp.continue.title"),$=q?(0,d.localizationKeys)("signUp.legalConsent.continue.subtitle"):(0,d.localizationKeys)("signUp.continue.subtitle");return(0,r.tZ)(d.Flow.Part,{part:"complete",children:(0,r.BX)(p.Z.Root,{children:[(0,r.BX)(p.Z.Content,{children:[(0,r.BX)(m.h.Root,{showLogo:!0,children:[(0,r.tZ)(m.h.Title,{localizationKey:Y}),(0,r.tZ)(m.h.Subtitle,{localizationKey:$})]}),(0,r.tZ)(p.Z.Alert,{children:e.error}),(0,r.tZ)(d.Flex,{direction:"col",elementDescriptor:d.descriptors.main,gap:8,children:(0,r.BX)(g.G,{children:[H&&!q&&(0,r.tZ)(w,{enableOAuthProviders:H,enableWeb3Providers:!1,enableAlternativePhoneCodeProviders:!1,continueSignUp:!0}),(0,r.tZ)(U,{handleSubmit:X,fields:D,formState:B,onlyLegalAcceptedMissing:q,canToggleEmailPhone:W,handleEmailPhoneToggle:e=>{P(c,L)&&T(e)}})]})})]}),(0,r.tZ)(p.Z.Footer,{children:R?null:(0,r.BX)(p.Z.Action,{elementId:"signUp",children:[(0,r.tZ)(p.Z.ActionText,{localizationKey:(0,d.localizationKeys)("signUp.continue.actionText")}),(0,r.tZ)(p.Z.ActionLink,{localizationKey:(0,d.localizationKeys)("signUp.continue.actionLink"),to:R?"../../":t.buildUrlWithAuth(S)})]})})]})})});var K=i(8969);let k=(0,K.sN)(K.L);var R=i(6543),L=i(753),F=i(4029),T=i(2464),O=i(5518),B=i(3234),q=i(4174);let x=()=>{let e=(0,l.cL)(),t=(0,h.useCardState)(),{navigate:i}=(0,u.useRouter)(),{signInUrl:a,waitlistUrl:n}=(0,s.useSignUpContext)(),o=(0,B.H)(),{userSettings:c}=(0,s.useEnvironment)(),{mode:f}=c.signUp,g=async()=>{await i(e.buildUrlWithAuth(n))},b=f===L.ci.RESTRICTED?(0,d.localizationKeys)("signUp.restrictedAccess.subtitle"):(0,d.localizationKeys)("signUp.restrictedAccess.subtitleWaitlist");return(0,r.BX)(p.Z.Root,{children:[(0,r.BX)(p.Z.Content,{children:[(0,r.BX)(m.h.Root,{showLogo:!0,children:[(0,r.tZ)(d.Icon,{icon:q.gO,sx:e=>({margin:"auto",width:e.sizes.$10,height:e.sizes.$10})}),(0,r.tZ)(m.h.Title,{localizationKey:(0,d.localizationKeys)("signUp.restrictedAccess.title")}),(0,r.tZ)(m.h.Subtitle,{localizationKey:b})]}),(0,r.tZ)(p.Z.Alert,{children:t.error}),f===L.ci.RESTRICTED&&o&&(0,r.tZ)(d.Flex,{direction:"col",gap:4,children:(0,r.tZ)(d.Button,{localizationKey:(0,d.localizationKeys)("signUp.restrictedAccess.blockButton__emailSupport"),onClick:()=>{window.location.href=`mailto:${o}`}})}),f===L.ci.WAITLIST&&(0,r.tZ)(d.Flex,{direction:"col",gap:4,children:(0,r.tZ)(d.Button,{localizationKey:(0,d.localizationKeys)("signUp.restrictedAccess.blockButton__joinWaitlist"),onClick:g})})]}),(0,r.tZ)(p.Z.Footer,{children:(0,r.BX)(p.Z.Action,{elementId:"signUp",children:[(0,r.tZ)(p.Z.ActionText,{localizationKey:(0,d.localizationKeys)("signUp.restrictedAccess.actionText")}),(0,r.tZ)(p.Z.ActionLink,{localizationKey:(0,d.localizationKeys)("signUp.restrictedAccess.actionLink"),to:e.buildUrlWithAuth(a)})]})})]})},M=e=>{let t;let{handleSubmit:i,fields:l,formState:a,onUseAnotherMethod:n,phoneCodeProvider:o}=e,{providerToDisplayData:s,strategyToDisplayData:c}=(0,T.vO)(),u=o.name,f=o.channel,g=(0,h.useCardState)();return(0,r.tZ)(p.Z.Root,{children:(0,r.BX)(p.Z.Content,{children:[(0,r.BX)(m.h.Root,{showLogo:!0,showDivider:!0,children:[(0,r.tZ)(d.Col,{center:!0,children:(0,r.tZ)(d.Image,{src:s[o.channel]?.iconUrl,alt:`${c[f].name} logo`,sx:e=>({width:e.sizes.$7,height:e.sizes.$7,maxWidth:"100%",marginBottom:e.sizes.$6})})}),(0,r.tZ)(m.h.Title,{localizationKey:(0,d.localizationKeys)("signUp.start.alternativePhoneCodeProvider.title",{provider:u})}),(0,r.tZ)(m.h.Subtitle,{localizationKey:(0,d.localizationKeys)("signUp.start.alternativePhoneCodeProvider.subtitle",{provider:u})})]}),(0,r.tZ)(p.Z.Alert,{children:g.error}),(0,r.tZ)(d.Flex,{direction:"col",elementDescriptor:d.descriptors.main,gap:6,children:(0,r.BX)(_.l.Root,{onSubmit:i,gap:8,children:[(0,r.tZ)(d.Col,{gap:6,children:(0,r.tZ)(_.l.ControlRow,{elementId:"phoneNumber",children:(0,r.tZ)(_.l.PhoneInput,{...a.phoneNumber.props,label:(0,d.localizationKeys)("signUp.start.alternativePhoneCodeProvider.label",{provider:u}),isRequired:!0,isOptional:!1,actionLabel:void 0,onActionClicked:void 0})})}),(0,r.BX)(d.Col,{center:!0,children:[(0,r.tZ)(y.S,{}),(0,r.BX)(d.Col,{gap:6,sx:{width:"100%"},children:[!!l[t="legalAccepted"]&&l[t]?.required&&(0,r.tZ)(_.l.ControlRow,{elementId:"legalAccepted",children:(0,r.tZ)(v.H,{...a.legalAccepted.props,isRequired:l.legalAccepted?.required})}),(0,r.tZ)(_.l.SubmitButton,{hasArrow:!0,localizationKey:(0,d.localizationKeys)("formButtonPrimary")})]})]}),(0,r.tZ)(d.Col,{center:!0,children:(0,r.tZ)(d.Button,{variant:"link",colorScheme:"neutral",onClick:n,localizationKey:(0,d.localizationKeys)("signUp.start.alternativePhoneCodeProvider.actionLink")})})]})})]})})},D=(0,K.sN)((0,h.withCardStateProvider)(function(){let e=(0,h.useCardState)(),t=(0,l.cL)(),i=(0,T._m)(),n=(0,s.useCoreSignUp)(),{showOptionalFields:o}=(0,d.useAppearance)().parsedLayout,{userSettings:c,authConfig:_}=(0,s.useEnvironment)(),{navigate:v}=(0,u.useRouter)(),{attributes:A}=c,{setActive:S}=(0,l.cL)(),C=(0,s.useSignUpContext)(),E=!!a.useContext(s.SignInContext),{afterSignUpUrl:z,signInUrl:K,unsafeMetadata:k}=C,B=!!(C.isCombinedFlow&&E),[q,D]=a.useState(()=>N(A,c.signUp.progressive,{phoneNumber:C.initialValues?.phoneNumber===null?void 0:C.initialValues?.phoneNumber,emailAddress:C.initialValues?.emailAddress===null?void 0:C.initialValues?.emailAddress,...B?{emailAddress:n.emailAddress,phoneNumber:n.phoneNumber}:{}})),{t:V,locale:X}=(0,d.useLocalizations)(),W=C.initialValues||{},[H,Y]=a.useState(null),[$,j]=a.useState(!1),{userSettings:{passwordSettings:G,usernameSettings:J}}=(0,s.useEnvironment)(),{mode:Q}=c.signUp,ee={firstName:(0,b.Yp)("firstName",n.firstName||W.firstName||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__firstName"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__firstName")}),lastName:(0,b.Yp)("lastName",n.lastName||W.lastName||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__lastName"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__lastName")}),emailAddress:(0,b.Yp)("emailAddress",n.emailAddress||W.emailAddress||"",{type:"email",label:(0,d.localizationKeys)("formFieldLabel__emailAddress"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__emailAddress")}),username:(0,b.Yp)("username",n.username||W.username||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__username"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__username"),transformer:e=>e.trim(),buildErrorMessage:e=>(0,b.fq)(e,{t:V,locale:X,usernameSettings:J})}),phoneNumber:(0,b.Yp)("phoneNumber",n.phoneNumber||W.phoneNumber||"",{type:"tel",label:(0,d.localizationKeys)("formFieldLabel__phoneNumber"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__phoneNumber")}),legalAccepted:(0,b.Yp)("legalAccepted","",{type:"checkbox",label:"",defaultChecked:!1,isRequired:c.signUp.legal_consent_enabled||!1}),password:(0,b.Yp)("password","",{type:"password",label:(0,d.localizationKeys)("formFieldLabel__password"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__password"),validatePassword:!0,buildErrorMessage:e=>(0,b.GM)(e,{t:V,locale:X,passwordSettings:G})}),ticket:(0,b.Yp)("ticket",(0,F.XV)("__clerk_ticket")||(0,F.XV)("__clerk_invitation_token")||"")},et=!!ee.ticket.value,ei=!!ee.emailAddress.value,er=c.signUp.progressive,el=c.signUp.legal_consent_enabled,ea=C.oidcPrompt,en=Z({attributes:A,hasTicket:et,hasEmail:ei,activeCommIdentifierType:q,isProgressiveSignUp:er,legalConsentRequired:el}),eo=()=>{ee.ticket.value&&(i.setLoading(),e.setLoading(),n.create({strategy:"ticket",ticket:ee.ticket.value}).then(e=>{ee.emailAddress.setValue(e.emailAddress||""),"missing_requirements"===e.status&&j(!0);let t=C.ssoCallbackUrl,i=C.afterSignUpUrl||"/";return(0,I.completeSignUpFlow)({signUp:e,redirectUrl:t,redirectUrlComplete:i,verifyEmailPath:"verify-email-address",verifyPhonePath:"verify-phone-number",handleComplete:()=>((0,F.xy)("__clerk_ticket"),(0,F.xy)("__clerk_invitation_token"),S({session:e.createdSessionId,redirectUrl:z})),navigate:v,oidcPrompt:ea})}).catch(t=>{ee.ticket.setValue(""),(0,b.S3)(t,[],e.setError)}).finally(()=>{n.missingFields.some(e=>"saml"===e||"enterprise_sso"===e)||(i.setIdle(),e.setIdle())}))};a.useLayoutEffect(()=>{eo()},[]),a.useEffect(()=>{(async function(){let t=n.verifications.externalAccount.error;if(t){switch(t.code){case L.O1.NOT_ALLOWED_TO_SIGN_UP:case L.O1.OAUTH_ACCESS_DENIED:case L.O1.NOT_ALLOWED_ACCESS:case L.O1.SAML_USER_ATTRIBUTE_MISSING:case L.O1.OAUTH_EMAIL_DOMAIN_RESERVED_BY_SAML:case L.O1.USER_LOCKED:case L.O1.ENTERPRISE_SSO_USER_ATTRIBUTE_MISSING:case L.O1.ENTERPRISE_SSO_EMAIL_ADDRESS_DOMAIN_MISMATCH:case L.O1.ENTERPRISE_SSO_HOSTED_DOMAIN_MISMATCH:case L.O1.SAML_EMAIL_ADDRESS_DOMAIN_MISMATCH:case L.O1.ORGANIZATION_MEMBERSHIP_QUOTA_EXCEEDED_FOR_SSO:case L.O1.CAPTCHA_INVALID:case L.O1.FRAUD_DEVICE_BLOCKED:case L.O1.FRAUD_ACTION_BLOCKED:case L.O1.SIGNUP_RATE_LIMIT_EXCEEDED:e.setError(t);break;default:e.setError("Unable to complete action at this time. If the problem persists please contact support.")}await n.create({})}})()},[]);let es=async t=>{let i;t.preventDefault();let r=Object.entries(en).reduce((e,[t,i])=>(e.push(...i&&ee[t]?[ee[t]]:[]),e),[]);if(k&&r.push({id:"unsafeMetadata",value:k}),en.ticket){let e=()=>{};r.push({id:"strategy",value:"ticket",clearFeedback:e,setValue:e,onChange:e,setError:e})}let l=H?.channel||(0,O.Vs)(r,_.preferredChannels,"phoneNumber");if(l){let e=()=>{};r.push({id:"strategy",value:"phone_code",clearFeedback:e,setValue:e,onChange:e,setError:e}),r.push({id:"channel",value:l,clearFeedback:e,setValue:e,onChange:e,setError:e})}let a=!!r.find(e=>"emailAddress"===e.id)?.value,o=!!r.find(e=>"phoneNumber"===e.id)?.value;!a&&!o&&P(A,er)&&(r.push(ee.emailAddress),r.push(ee.phoneNumber)),e.setLoading(),e.setError(void 0);let s=C.ssoCallbackUrl,d=C.afterSignUpUrl||"/";return(en.ticket?n.upsert((0,b.ni)(r)):n.create((0,b.ni)(r))).then(e=>(0,I.completeSignUpFlow)({signUp:e,verifyEmailPath:"verify-email-address",verifyPhonePath:"verify-phone-number",handleComplete:()=>S({session:e.createdSessionId,redirectUrl:z}),navigate:v,redirectUrl:s,redirectUrlComplete:d,oidcPrompt:ea})).catch(t=>(0,b.S3)(t,r,e.setError)).finally(()=>e.setIdle())};if(i.isLoading)return(0,r.tZ)(f.W,{});let ed=P(A,er),ec=Object.entries(en).filter(([e,t])=>o||t?.required),eu=function(e){let{authenticatableSocialStrategies:t,web3FirstFactors:i}=e;return e.hasValidAuthFactor||!t.length&&!i.length}(c)&&ec.length>0,ep=(!et||$)&&c.authenticatableSocialStrategies.length>0,eh=!et&&c.web3FirstFactors.length>0,em=!et&&c.alternativePhoneCodeChannels.length>0;return Q===L.ci.PUBLIC||et?(0,r.tZ)(d.Flow.Part,{part:"start",children:H?(0,r.tZ)(M,{handleSubmit:es,fields:en,formState:ee,onUseAnotherMethod:()=>{Y(null)},phoneCodeProvider:H}):(0,r.BX)(p.Z.Root,{children:[(0,r.BX)(p.Z.Content,{children:[(0,r.BX)(m.h.Root,{showLogo:!0,children:[(0,r.tZ)(m.h.Title,{localizationKey:B?(0,d.localizationKeys)("signUp.start.titleCombined"):(0,d.localizationKeys)("signUp.start.title")}),(0,r.tZ)(m.h.Subtitle,{localizationKey:B?(0,d.localizationKeys)("signUp.start.subtitleCombined"):(0,d.localizationKeys)("signUp.start.subtitle")})]}),(0,r.tZ)(p.Z.Alert,{children:e.error}),(0,r.BX)(d.Flex,{direction:"col",elementDescriptor:d.descriptors.main,gap:6,children:[(0,r.BX)(g.G,{children:[(ep||eh||em)&&(0,r.tZ)(w,{enableOAuthProviders:ep,enableWeb3Providers:eh,enableAlternativePhoneCodeProviders:em,onAlternativePhoneCodeProviderClick:e=>{Y((0,R.H)(e)||null)},continueSignUp:$,legalAccepted:!!ee.legalAccepted.checked||void 0}),eu&&(0,r.tZ)(U,{handleSubmit:es,fields:en,formState:ee,canToggleEmailPhone:ed,handleEmailPhoneToggle:e=>{P(A,er)&&D(e)}})]}),!eu&&(0,r.tZ)(y.S,{})]})]}),(0,r.tZ)(p.Z.Footer,{children:(0,r.BX)(p.Z.Action,{elementId:"signUp",children:[(0,r.tZ)(p.Z.ActionText,{localizationKey:(0,d.localizationKeys)("signUp.start.actionText")}),(0,r.tZ)(p.Z.ActionLink,{localizationKey:(0,d.localizationKeys)("signUp.start.actionLink"),to:B?"../":t.buildUrlWithAuth(K)})]})})]})}):(0,r.tZ)(x,{})}));var V=i(3465);let X=e=>{let{afterSignUpUrl:t}=(0,s.useSignUpContext)(),{setActive:i}=(0,l.cL)(),{navigate:a}=(0,u.useRouter)();return(0,r.tZ)(V.U,{cardTitle:e.cardTitle,cardSubtitle:e.cardSubtitle,resendButton:e.resendButton,onCodeEntryFinishedAction:(r,l,n)=>{e.attempt(r).then(async e=>(await l(),(0,I.completeSignUpFlow)({signUp:e,verifyEmailPath:"../verify-email-address",verifyPhonePath:"../verify-phone-number",continuePath:"../continue",handleComplete:()=>i({session:e.createdSessionId,redirectUrl:t}),navigate:a}))).catch(e=>n(e))},onResendCodeClicked:e.prepare,safeIdentifier:e.safeIdentifier,onIdentityPreviewEditClicked:()=>a("../",{searchParams:(0,F.z3)()}),alternativeMethodsLabel:e.alternativeMethodsLabel,onShowAlternativeMethodsClicked:e.onShowAlternativeMethodsClicked,showAlternativeMethods:e.showAlternativeMethods})},W=()=>{let e=(0,s.useCoreSignUp)(),t=(0,h.useCardState)(),i=e.verifications.emailAddress.status,l=!e.status||"verified"===i;return(0,T.ib)(l?void 0:()=>e.prepareEmailAddressVerification({strategy:"email_code"}).catch(e=>(0,b.S3)(e,[],t.setError)),{name:"prepare",strategy:"email_code",number:e.emailAddress},{staleTime:100}),(0,r.tZ)(d.Flow.Part,{part:"emailCode",children:(0,r.tZ)(X,{cardTitle:(0,d.localizationKeys)("signUp.emailCode.title"),cardSubtitle:(0,d.localizationKeys)("signUp.emailCode.subtitle"),inputLabel:(0,d.localizationKeys)("signUp.emailCode.formSubtitle"),resendButton:(0,d.localizationKeys)("signUp.emailCode.resendButton"),prepare:()=>{if(!l)return e.prepareEmailAddressVerification({strategy:"email_code"}).catch(e=>(0,b.S3)(e,[],t.setError))},attempt:t=>e.attemptEmailAddressVerification({code:t}),safeIdentifier:e.emailAddress})})};var H=i(5495),Y=i(4814);let $=()=>{let{t:e}=(0,d.useLocalizations)(),t=(0,s.useCoreSignUp)(),i=(0,s.useSignUpContext)(),{afterSignUpUrl:n}=i,o=(0,h.useCardState)(),{navigate:c}=(0,u.useRouter)(),{setActive:p}=(0,l.cL)(),[m,f]=a.useState(!1),{startEmailLinkFlow:g,cancelEmailLinkFlow:_}=(0,Y.E)(t);a.useEffect(()=>{v()},[]);let v=()=>g({redirectUrl:i.emailLinkRedirectUrl}).then(e=>y(e)).catch(e=>{(0,b.S3)(e,[],o.setError)}),y=async t=>{let i=t.verifications.emailAddress;"expired"===i.status?o.setError(e((0,d.localizationKeys)("formFieldError__verificationLinkExpired"))):i.verifiedFromTheSameClient()?f(!0):await (0,I.completeSignUpFlow)({signUp:t,continuePath:"../continue",verifyEmailPath:"../verify-email-address",verifyPhonePath:"../verify-phone-number",handleComplete:()=>p({session:t.createdSessionId,redirectUrl:n}),navigate:c})};return m?(0,r.tZ)(K.Ej,{title:(0,d.localizationKeys)("signUp.emailLink.verifiedSwitchTab.title"),subtitle:(0,d.localizationKeys)("signUp.emailLink.verifiedSwitchTab.subtitleNewTab"),status:"verified_switch_tab"}):(0,r.tZ)(d.Flow.Part,{part:"emailLink",children:(0,r.tZ)(H.J,{cardTitle:(0,d.localizationKeys)("signUp.emailLink.title"),cardSubtitle:(0,d.localizationKeys)("signUp.emailLink.subtitle"),formTitle:(0,d.localizationKeys)("signUp.emailLink.formTitle"),formSubtitle:(0,d.localizationKeys)("signUp.emailLink.formSubtitle"),resendButton:(0,d.localizationKeys)("signUp.emailLink.resendButton"),onResendCodeClicked:()=>{_(),v()},safeIdentifier:t.emailAddress})})},j=(0,h.withCardStateProvider)(()=>{let{userSettings:e}=(0,s.useEnvironment)(),{attributes:t}=e;return t.email_address?.verifications?.includes("email_link")?(0,r.tZ)($,{}):(0,r.tZ)(W,{})}),G=(0,h.withCardStateProvider)(()=>{let e=(0,s.useCoreSignUp)(),t=(0,h.useCardState)(),i=e.verifications.phoneNumber.channel,l=e.verifications.phoneNumber.status,a=!e.status||"verified"===l,n=!!i&&"sms"!==i,o=n?i:void 0;(0,T.ib)(a||n?void 0:()=>e.preparePhoneNumberVerification({strategy:"phone_code",channel:void 0}).catch(e=>(0,b.S3)(e,[],t.setError)),{name:"signUp.preparePhoneNumberVerification",strategy:"phone_code",number:e.phoneNumber},{staleTime:100});let c=(0,d.localizationKeys)("signUp.phoneCode.title"),u=(0,d.localizationKeys)("signUp.phoneCode.subtitle"),p=(0,d.localizationKeys)("signUp.phoneCode.resendButton");if(n){let e=R.H(i)?.name;c=(0,d.localizationKeys)("signUp.alternativePhoneCodeProvider.title",{provider:e}),u=(0,d.localizationKeys)("signUp.alternativePhoneCodeProvider.subtitle",{provider:e}),p=(0,d.localizationKeys)("signUp.alternativePhoneCodeProvider.resendButton")}return t.isLoading?(0,r.tZ)(f.W,{}):(0,r.tZ)(d.Flow.Part,{part:"phoneCode",children:(0,r.tZ)(X,{cardTitle:c,cardSubtitle:u,resendButton:p,prepare:()=>{if(!a)return e.preparePhoneNumberVerification({strategy:"phone_code",channel:o}).catch(e=>(0,b.S3)(e,[],t.setError))},attempt:t=>e.attemptPhoneNumberVerification({code:t}),safeIdentifier:e.phoneNumber,alternativeMethodsLabel:n?(0,d.localizationKeys)("footerActionLink__alternativePhoneCodeProvider"):void 0,onShowAlternativeMethodsClicked:n?()=>{t.setLoading(),t.setError(void 0),e.preparePhoneNumberVerification({strategy:"phone_code",channel:void 0}).catch(e=>(0,b.S3)(e,[],t.setError)).finally(()=>t.setIdle())}:void 0,showAlternativeMethods:!!n||void 0})})}),J=()=>(0,r.tZ)(G,{});function Q(){let e=(0,l.cL)();return a.useEffect(()=>{e.redirectToSignUp()},[]),null}function ee(){(0,c.z)();let{__internal_setComponentNavigationContext:e}=(0,l.cL)(),{navigate:t,indexPath:i}=(0,u.useRouter)(),p=(0,s.useSignUpContext)();return a.useEffect(()=>e?.({indexPath:i,navigate:t}),[i,t]),(0,r.tZ)(d.Flow.Root,{flow:"signUp",children:(0,r.BX)(u.Switch,{children:[(0,r.tZ)(u.Route,{path:"verify-email-address",canActivate:e=>!!e.client.signUp.emailAddress,children:(0,r.tZ)(j,{})}),(0,r.tZ)(u.Route,{path:"verify-phone-number",canActivate:e=>!!e.client.signUp.phoneNumber,children:(0,r.tZ)(J,{})}),(0,r.tZ)(u.Route,{path:"sso-callback",children:(0,r.tZ)(k,{signUpUrl:p.signUpUrl,signInUrl:p.signInUrl,signUpForceRedirectUrl:p.afterSignUpUrl,signInForceRedirectUrl:p.afterSignInUrl,secondFactorUrl:p.secondFactorUrl,continueSignUpUrl:"../continue",verifyEmailAddressUrl:"../verify-email-address",verifyPhoneNumberUrl:"../verify-phone-number"})}),(0,r.tZ)(u.Route,{path:"verify",children:(0,r.tZ)(o.$,{redirectUrlComplete:p.afterSignUpUrl,verifyEmailPath:"../verify-email-address",verifyPhonePath:"../verify-phone-number"})}),(0,r.BX)(u.Route,{path:"continue",children:[(0,r.tZ)(u.Route,{path:"verify-email-address",canActivate:e=>!!e.client.signUp.emailAddress,children:(0,r.tZ)(j,{})}),(0,r.tZ)(u.Route,{path:"verify-phone-number",canActivate:e=>!!e.client.signUp.phoneNumber,children:(0,r.tZ)(J,{})}),(0,r.tZ)(u.Route,{index:!0,children:(0,r.tZ)(z,{})})]}),(0,r.tZ)(u.Route,{path:"tasks",children:(0,r.tZ)(n.x7,{})}),(0,r.tZ)(u.Route,{index:!0,children:(0,r.tZ)(D,{})}),(0,r.tZ)(u.Route,{children:(0,r.tZ)(Q,{})})]})})}ee.displayName="SignUp";let et=(0,s.withCoreSessionSwitchGuard)(ee),ei=e=>{let t={signInUrl:`/${u.VIRTUAL_ROUTER_BASE_PATH}/sign-in`,waitlistUrl:`/${u.VIRTUAL_ROUTER_BASE_PATH}/waitlist`,...e};return(0,r.tZ)(u.Route,{path:"sign-up",children:(0,r.tZ)(s.SignUpContext.Provider,{value:{componentName:"SignUp",...t,routing:"virtual",mode:"modal"},children:(0,r.tZ)("div",{children:(0,r.tZ)(et,{...t,routing:"virtual"})})})})}},6052:function(e,t,i){i.r(t),i.d(t,{completeSignUpFlow:()=>r.v});var r=i(5756)}}]);