import type { CommerceStatementGroupJSON, CommerceStatementJSON, CommerceStatementResource, CommerceStatementStatus, CommerceStatementTotals } from '@clerk/types';
import { BaseResource, CommercePayment } from './internal';
export declare class CommerceStatement extends BaseResource implements CommerceStatementResource {
    id: string;
    status: CommerceStatementStatus;
    timestamp: Date;
    totals: CommerceStatementTotals;
    groups: CommerceStatementGroup[];
    constructor(data: CommerceStatementJSON);
    protected fromJSON(data: CommerceStatementJSON | null): this;
}
export declare class CommerceStatementGroup {
    id: string;
    timestamp: Date;
    items: CommercePayment[];
    constructor(data: CommerceStatementGroupJSON);
    protected fromJSON(data: CommerceStatementGroupJSON | null): this;
}
