// src/sr-RS.ts
var srRS = {
  locale: "sr-RS",
  backButton: "<PERSON><PERSON>",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Podrazumevano",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Drugi ure\u0111aj koji se predstavlja",
  badge__primary: "<PERSON><PERSON><PERSON><PERSON>",
  badge__renewsAt: void 0,
  badge__requiresAction: "Zahteva a<PERSON>",
  badge__startsAt: void 0,
  badge__thisDevice: "Ovaj ure\u0111aj",
  badge__unverified: "Nepotvr\u0111en",
  badge__upcomingPlan: void 0,
  badge__userDevice: "<PERSON><PERSON><PERSON>\u010Dki ure\u0111aj",
  badge__you: "Vi",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Kreiraj organizaciju",
    invitePage: {
      formButtonReset: "Presko\u010Di"
    },
    title: "Kreiraj organizaciju"
  },
  dates: {
    lastDay: "Ju\u010De u {{ date | timeString('sr-RS') }}",
    next6Days: "{{ date | weekday('sr-RS','long') }} u {{ date | timeString('sr-RS') }}",
    nextDay: "Sutra u {{ date | timeString('sr-RS') }}",
    numeric: "{{ date | numeric('sr-RS') }}",
    previous6Days: "Pro\u0161li {{ date | weekday('sr-RS','long') }} u {{ date | timeString('sr-RS') }}",
    sameDay: "Danas u {{ date | timeString('sr-RS') }}"
  },
  dividerText: "ili",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Koristi drugu metodu",
  footerPageLink__help: "Pomo\u0107",
  footerPageLink__privacy: "Privatnost",
  footerPageLink__terms: "Uslovi",
  formButtonPrimary: "Nastavi",
  formButtonPrimary__verify: "Verifikuj",
  formFieldAction__forgotPassword: "Zaboravljena lozinka?",
  formFieldError__matchingPasswords: "Lozinke se poklapaju.",
  formFieldError__notMatchingPasswords: "Lozinke se ne poklapaju.",
  formFieldError__verificationLinkExpired: "Link za verifikaciju je istekao. Molimo zatra\u017Eite novi link.",
  formFieldHintText__optional: "Opciono",
  formFieldHintText__slug: "Slug je lako \u010Ditljivi ID koji mora biti jedinstven. \u010Cesto se koristi u URL-ovima.",
  formFieldInputPlaceholder__backupCode: void 0,
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Obri\u0161i nalog",
  formFieldInputPlaceholder__emailAddress: void 0,
  formFieldInputPlaceholder__emailAddress_username: void 0,
  formFieldInputPlaceholder__emailAddresses: "<EMAIL>, <EMAIL>",
  formFieldInputPlaceholder__firstName: void 0,
  formFieldInputPlaceholder__lastName: void 0,
  formFieldInputPlaceholder__organizationDomain: void 0,
  formFieldInputPlaceholder__organizationDomainEmailAddress: void 0,
  formFieldInputPlaceholder__organizationName: void 0,
  formFieldInputPlaceholder__organizationSlug: "moja-org",
  formFieldInputPlaceholder__password: void 0,
  formFieldInputPlaceholder__phoneNumber: void 0,
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__automaticInvitations: "Omogu\u0107i automatske pozivnice za ovaj domen",
  formFieldLabel__backupCode: "Rezervni kod",
  formFieldLabel__confirmDeletion: "Potvrda",
  formFieldLabel__confirmPassword: "Potvrdi lozinku",
  formFieldLabel__currentPassword: "Trenutna lozinka",
  formFieldLabel__emailAddress: "E-mail adresa",
  formFieldLabel__emailAddress_username: "E-mail adresa ili korisni\u010Dko ime",
  formFieldLabel__emailAddresses: "E-mail adrese",
  formFieldLabel__firstName: "Ime",
  formFieldLabel__lastName: "Prezime",
  formFieldLabel__newPassword: "Nova lozinka",
  formFieldLabel__organizationDomain: "Domen",
  formFieldLabel__organizationDomainDeletePending: "Obri\u0161i \u010Dekaju\u0107e pozivnice i predloge",
  formFieldLabel__organizationDomainEmailAddress: "E-mail adresa za verifikaciju",
  formFieldLabel__organizationDomainEmailAddressDescription: "Unesite e-mail adresu pod ovim domenom da biste primili kod i verifikovali ovaj domen.",
  formFieldLabel__organizationName: "Naziv",
  formFieldLabel__organizationSlug: "Slug",
  formFieldLabel__passkeyName: "Naziv klju\u010Da za prolaz",
  formFieldLabel__password: "Lozinka",
  formFieldLabel__phoneNumber: "Telefonski broj",
  formFieldLabel__role: "Uloga",
  formFieldLabel__signOutOfOtherSessions: "Odjavi se sa svih drugih ure\u0111aja",
  formFieldLabel__username: "Korisni\u010Dko ime",
  impersonationFab: {
    action__signOut: "Odjavi se",
    title: "Prijavljeni ste kao {{identifier}}"
  },
  maintenanceMode: "Trenutno smo u modu odr\u017Eavanja, ali ne brinite, ne\u0107e trajati du\u017Ee od nekoliko minuta.",
  membershipRole__admin: "Administrator",
  membershipRole__basicMember: "\u010Clan",
  membershipRole__guestMember: "Gost",
  organizationList: {
    action__createOrganization: "Kreiraj organizaciju",
    action__invitationAccept: "Pridru\u017Ei se",
    action__suggestionsAccept: "Zatra\u017Ei pridru\u017Eivanje",
    createOrganization: "Kreiraj organizaciju",
    invitationAcceptedLabel: "Pridru\u017Een",
    subtitle: "da nastavite na {{applicationName}}",
    suggestionsAcceptedLabel: "\u010Ceka odobrenje",
    title: "Izaberi nalog",
    titleWithoutPersonal: "Izaberi organizaciju"
  },
  organizationProfile: {
    badge__automaticInvitation: "Automatske pozivnice",
    badge__automaticSuggestion: "Automatski predlozi",
    badge__manualInvitation: "Bez automatskog uklju\u010Divanja",
    badge__unverified: "Nepotvr\u0111en",
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Dodajte domen za verifikaciju. Korisnici sa e-mail adresama na ovom domenu mogu se automatski pridru\u017Eiti organizaciji ili zatra\u017Eiti pridru\u017Eivanje.",
      title: "Dodaj domen"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Pozivnice nisu poslate. Ve\u0107 postoje \u010Dekaju\u0107e pozivnice za slede\u0107e e-mail adrese: {{email_addresses}}.",
      formButtonPrimary__continue: "Po\u0161alji pozivnice",
      selectDropdown__role: "Izaberi ulogu",
      subtitle: "Unesi ili nalepi jednu ili vi\u0161e e-mail adresa, razdvojene razmacima ili zarezima.",
      successMessage: "Pozivnice su uspe\u0161no poslate",
      title: "Pozovi nove \u010Dlanove"
    },
    membersPage: {
      action__invite: "Pozovi",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "Ukloni \u010Dlana",
        tableHeader__actions: void 0,
        tableHeader__joined: "Pridru\u017Eio se",
        tableHeader__role: "Uloga",
        tableHeader__user: "Korisnik"
      },
      detailsTitle__emptyRow: "Nema \u010Dlanova za prikaz",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Pozovi korisnike povezivanjem e-mail domena sa va\u0161om organizacijom. Svako ko se registruje sa odgovaraju\u0107im e-mail domenom mo\u017Ee se pridru\u017Eiti organizaciji u bilo koje vreme.",
          headerTitle: "Automatske pozivnice",
          primaryButton: "Upravljaj potvr\u0111enim domenima"
        },
        table__emptyRow: "Nema pozivnica za prikaz"
      },
      invitedMembersTab: {
        menuAction__revoke: "Poni\u0161ti pozivnicu",
        tableHeader__invited: "Pozvan"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Korisnici koji se registruju sa odgovaraju\u0107im e-mail domenom, mo\u0107i \u0107e da vide predlog da zatra\u017Ee pridru\u017Eivanje va\u0161oj organizaciji.",
          headerTitle: "Automatski predlozi",
          primaryButton: "Upravljaj potvr\u0111enim domenima"
        },
        menuAction__approve: "Odobri",
        menuAction__reject: "Odbaci",
        tableHeader__requested: "Zatra\u017Een pristup",
        table__emptyRow: "Nema zahteva za prikaz"
      },
      start: {
        headerTitle__invitations: "Pozivnice",
        headerTitle__members: "\u010Clanovi",
        headerTitle__requests: "Zahtevi"
      }
    },
    navbar: {
      billing: void 0,
      description: "Upravljaj svojom organizacijom.",
      general: "Op\u0161te",
      members: "\u010Clanovi",
      title: "Organizacija"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Upi\u0161i "{{organizationName}}" ispod da nastavi\u0161.',
          messageLine1: "Da li ste sigurni da \u017Eelite da obri\u0161ete ovu organizaciju?",
          messageLine2: "Ova akcija je trajna i nepovratna.",
          successMessage: "Organizacija je obrisana.",
          title: "Obri\u0161i organizaciju"
        },
        leaveOrganization: {
          actionDescription: 'Upi\u0161i "{{organizationName}}" ispod da nastavi\u0161.',
          messageLine1: "Da li ste sigurni da \u017Eelite da napustite ovu organizaciju? Izgubi\u0107ete pristup ovoj organizaciji i njenim aplikacijama.",
          messageLine2: "Ova akcija je trajna i nepovratna.",
          successMessage: "Napustili ste organizaciju.",
          title: "Napusti organizaciju"
        },
        title: "Opasnost"
      },
      domainSection: {
        menuAction__manage: "Upravljaj",
        menuAction__remove: "Obri\u0161i",
        menuAction__verify: "Verifikuj",
        primaryButton: "Dodaj domen",
        subtitle: "Dozvoli korisnicima da se automatski pridru\u017Ee organizaciji ili zatra\u017Ee pridru\u017Eivanje na osnovu potvr\u0111enog e-mail domena.",
        title: "Potvr\u0111eni domeni"
      },
      successMessage: "Organizacija je a\u017Eurirana.",
      title: "A\u017Euriraj profil"
    },
    removeDomainPage: {
      messageLine1: "E-mail domen {{domain}} \u0107e biti uklonjen.",
      messageLine2: "Korisnici vi\u0161e ne\u0107e mo\u0107i automatski da se pridru\u017Ee organizaciji nakon ovoga.",
      successMessage: "{{domain}} je uklonjen.",
      title: "Ukloni domen"
    },
    start: {
      headerTitle__general: "Op\u0161te",
      headerTitle__members: "\u010Clanovi",
      profileSection: {
        primaryButton: "A\u017Euriraj profil",
        title: "Profil organizacije",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Uklanjanje ovog domena \u0107e uticati na pozvane korisnike.",
        removeDomainActionLabel__remove: "Ukloni domen",
        removeDomainSubtitle: "Ukloni ovaj domen iz tvojih potvr\u0111enih domena",
        removeDomainTitle: "Ukloni domen"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Korisnici se automatski pozivaju da se pridru\u017Ee organizaciji kada se registruju i mogu se pridru\u017Eiti u bilo koje vreme.",
        automaticInvitationOption__label: "Automatske pozivnice",
        automaticSuggestionOption__description: "Korisnici dobijaju predlog da zatra\u017Ee pridru\u017Eivanje, ali moraju biti odobreni od strane administratora pre nego \u0161to mogu da se pridru\u017Ee organizaciji.",
        automaticSuggestionOption__label: "Automatski predlozi",
        calloutInfoLabel: "Promena na\u010Dina upisa \u0107e uticati samo na nove korisnike.",
        calloutInvitationCountLabel: "\u010Cekaju\u0107e pozivnice poslate korisnicima: {{count}}",
        calloutSuggestionCountLabel: "\u010Cekaju\u0107i predlozi poslati korisnicima: {{count}}",
        manualInvitationOption__description: "Korisnici mogu biti pozvani samo ru\u010Dno u organizaciju.",
        manualInvitationOption__label: "Bez automatskog uklju\u010Divanja",
        subtitle: "Izaberi kako korisnici iz ovog domena mogu da se pridru\u017Ee organizaciji."
      },
      start: {
        headerTitle__danger: "Opasnost",
        headerTitle__enrollment: "Opcije upisa"
      },
      subtitle: "Domen {{domain}} je sada verifikovan. Nastavi biranjem na\u010Dina upisa.",
      title: "A\u017Euriraj {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Unesi verifikacioni kod poslat na tvoju e-mail adresu",
      formTitle: "Verifikacioni kod",
      resendButton: "Nisi primio kod? Po\u0161alji ponovo",
      subtitle: "Domen {{domainName}} mora biti verifikovan putem e-maila.",
      subtitleVerificationCodeScreen: "Verifikacioni kod je poslat na {{emailAddress}}. Unesi kod da nastavi\u0161.",
      title: "Verifikuj domen"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Kreiraj organizaciju",
    action__invitationAccept: "Pridru\u017Ei se",
    action__manageOrganization: "Upravljaj",
    action__suggestionsAccept: "Zatra\u017Ei pridru\u017Eivanje",
    notSelected: "Organizacija nije izabrana",
    personalWorkspace: "Li\u010Dni nalog",
    suggestionsAcceptedLabel: "\u010Ceka odobrenje"
  },
  paginationButton__next: "Slede\u0107i",
  paginationButton__previous: "Prethodni",
  paginationRowText__displaying: "Prikazujem",
  paginationRowText__of: "od",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Dodaj nalog",
      action__signOutAll: "Odjavi se sa svih naloga",
      subtitle: "Izaberi nalog s kojim \u017Eeli\u0161 da nastavi\u0161.",
      title: "Izaberi nalog"
    },
    alternativeMethods: {
      actionLink: "Zatra\u017Ei pomo\u0107",
      actionText: "Nema\u0161 ni jednu od ovih opcija?",
      blockButton__backupCode: "Koristi rezervni kod",
      blockButton__emailCode: "Po\u0161alji kod na e-mail {{identifier}}",
      blockButton__emailLink: "Po\u0161alji link na e-mail {{identifier}}",
      blockButton__passkey: "Prijavi se sa svojim klju\u010Dem za prolaz",
      blockButton__password: "Prijavi se sa svojom lozinkom",
      blockButton__phoneCode: "Po\u0161alji SMS kod na {{identifier}}",
      blockButton__totp: "Koristi svoju aplikaciju za autentifikaciju",
      getHelp: {
        blockButton__emailSupport: "Po\u0161alji e-mail podr\u0161ci",
        content: "Ako ima\u0161 problema sa prijavljivanjem na svoj nalog, po\u0161alji nam e-mail i pomo\u0107i \u0107emo ti da \u0161to pre povrati\u0161 pristup.",
        title: "Zatra\u017Ei pomo\u0107"
      },
      subtitle: "Ima\u0161 problema? Mo\u017Ee\u0161 koristiti bilo koju od ovih metoda za prijavljivanje.",
      title: "Koristi drugu metodu"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "Tvoj rezervni kod je onaj koji si dobio kada si postavio dvostepenu autentifikaciju.",
      title: "Unesi rezervni kod"
    },
    emailCode: {
      formTitle: "Verifikacioni kod",
      resendButton: "Nisi primio kod? Po\u0161alji ponovo",
      subtitle: "da nastavi\u0161 na {{applicationName}}",
      title: "Proveri svoj e-mail"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      expired: {
        subtitle: "Vrati se na originalni tab da nastavi\u0161.",
        title: "Ovaj verifikacioni link je istekao"
      },
      failed: {
        subtitle: "Vrati se na originalni tab da nastavi\u0161.",
        title: "Ovaj verifikacioni link je neva\u017Ee\u0107i"
      },
      formSubtitle: "Koristi verifikacioni link poslat na tvoj e-mail",
      formTitle: "Verifikacioni link",
      loading: {
        subtitle: "Uskoro \u0107e\u0161 biti preusmeren",
        title: "Prijavljujem se..."
      },
      resendButton: "Nisi primio link? Po\u0161alji ponovo",
      subtitle: "da nastavi\u0161 na {{applicationName}}",
      title: "Proveri svoj e-mail",
      unusedTab: {
        title: "Mo\u017Ee\u0161 zatvoriti ovaj tab"
      },
      verified: {
        subtitle: "Uskoro \u0107e\u0161 biti preusmeren",
        title: "Uspe\u0161no si prijavljen"
      },
      verifiedSwitchTab: {
        subtitle: "Vrati se na originalni tab da nastavi\u0161",
        subtitleNewTab: "Vrati se na novootvoreni tab da nastavi\u0161",
        titleNewTab: "Prijavljen na drugom tabu"
      }
    },
    forgotPassword: {
      formTitle: "Kod za resetovanje lozinke",
      resendButton: "Nisi primio kod? Po\u0161alji ponovo",
      subtitle: "da resetuje\u0161 svoju lozinku",
      subtitle_email: "Prvo, unesi kod poslat na tvoju e-mail adresu",
      subtitle_phone: "Prvo, unesi kod poslat na tvoj telefon",
      title: "Resetuj lozinku"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Resetuj svoju lozinku",
      label__alternativeMethods: "Ili, prijavi se drugom metodom",
      title: "Zaboravljena lozinka?"
    },
    noAvailableMethods: {
      message: "Nije mogu\u0107e nastaviti sa prijavom. Nema dostupnih metoda autentifikacije.",
      subtitle: "Do\u0161lo je do gre\u0161ke",
      title: "Nije mogu\u0107e prijaviti se"
    },
    passkey: {
      subtitle: "Kori\u0161\u0107enje tvojeg klju\u010Da za prolaz potvr\u0111uje da si to ti. Tvoj ure\u0111aj mo\u017Ee zatra\u017Eiti otisak prsta, lice ili ekran zaklju\u010Davanja.",
      title: "Koristi svoj klju\u010D za prolaz"
    },
    password: {
      actionLink: "Koristi drugu metodu",
      subtitle: "Unesi lozinku koja je povezana sa tvojim nalogom",
      title: "Unesi svoju lozinku"
    },
    passwordPwned: {
      title: "Lozinka kompromitovana"
    },
    phoneCode: {
      formTitle: "Verifikacioni kod",
      resendButton: "Nisi primio kod? Po\u0161alji ponovo",
      subtitle: "da nastavi\u0161 na {{applicationName}}",
      title: "Proveri svoj telefon"
    },
    phoneCodeMfa: {
      formTitle: "Verifikacioni kod",
      resendButton: "Nisi primio kod? Po\u0161alji ponovo",
      subtitle: "Da nastavi\u0161, molimo unesi verifikacioni kod poslat na tvoj telefon",
      title: "Proveri svoj telefon"
    },
    resetPassword: {
      formButtonPrimary: "Resetuj lozinku",
      requiredMessage: "Iz sigurnosnih razloga, potrebno je da resetuje\u0161 svoju lozinku.",
      successMessage: "Tvoja lozinka je uspe\u0161no promenjena. Prijavljujem te, sa\u010Dekaj trenutak.",
      title: "Postavi novu lozinku"
    },
    resetPasswordMfa: {
      detailsLabel: "Potrebno je da potvrdimo tvoj identitet pre resetovanja lozinke."
    },
    start: {
      actionLink: "Registruj se",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "Koristi e-mail",
      actionLink__use_email_username: "Koristi e-mail ili korisni\u010Dko ime",
      actionLink__use_passkey: "Koristi klju\u010D za prolaz umesto toga",
      actionLink__use_phone: "Koristi telefon",
      actionLink__use_username: "Koristi korisni\u010Dko ime",
      actionText: "Nema\u0161 nalog?",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "Dobro do\u0161ao nazad! Molimo prijavi se da nastavi\u0161",
      subtitleCombined: void 0,
      title: "Prijavi se na {{applicationName}}",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "Verifikacioni kod",
      subtitle: "Da nastavi\u0161, molimo unesi verifikacioni kod generisan tvojom aplikacijom za autentifikaciju",
      title: "Dvostepena verifikacija"
    }
  },
  signInEnterPasswordTitle: "Unesi svoju lozinku",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Prijavi se",
      actionText: "Ve\u0107 ima\u0161 nalog?",
      subtitle: "Molimo popuni preostale detalje da nastavi\u0161.",
      title: "Popuni nedostaju\u0107a polja"
    },
    emailCode: {
      formSubtitle: "Unesi verifikacioni kod poslat na tvoju e-mail adresu",
      formTitle: "Verifikacioni kod",
      resendButton: "Nisi primio kod? Po\u0161alji ponovo",
      subtitle: "Unesi verifikacioni kod poslat na tvoj e-mail",
      title: "Verifikuj svoj e-mail"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      formSubtitle: "Koristi verifikacioni link poslat na tvoju e-mail adresu",
      formTitle: "Verifikacioni link",
      loading: {
        title: "Registrujem se..."
      },
      resendButton: "Nisi primio link? Po\u0161alji ponovo",
      subtitle: "da nastavi\u0161 na {{applicationName}}",
      title: "Verifikuj svoj e-mail",
      verified: {
        title: "Uspe\u0161no registrovan"
      },
      verifiedSwitchTab: {
        subtitle: "Vrati se na novootvoreni tab da nastavi\u0161",
        subtitleNewTab: "Vrati se na prethodni tab da nastavi\u0161",
        title: "Uspe\u0161no verifikovan e-mail"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: void 0,
        label__onlyTermsOfService: void 0,
        label__termsOfServiceAndPrivacyPolicy: void 0
      },
      continue: {
        subtitle: void 0,
        title: void 0
      }
    },
    phoneCode: {
      formSubtitle: "Unesi verifikacioni kod poslat na tvoj telefonski broj",
      formTitle: "Verifikacioni kod",
      resendButton: "Nisi primio kod? Po\u0161alji ponovo",
      subtitle: "Unesi verifikacioni kod poslat na tvoj telefon",
      title: "Verifikuj svoj telefon"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "Prijavi se",
      actionLink__use_email: void 0,
      actionLink__use_phone: void 0,
      actionText: "Ve\u0107 ima\u0161 nalog?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "Dobrodo\u0161ao! Molimo popuni detalje da zapo\u010Dne\u0161.",
      subtitleCombined: "Dobrodo\u0161ao! Molimo popuni detalje da zapo\u010Dne\u0161.",
      title: "Kreiraj svoj nalog",
      titleCombined: "Kreiraj svoj nalog"
    }
  },
  socialButtonsBlockButton: "Nastavi sa {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: "{{provider|titleize}}",
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "Registracija neuspe\u0161na zbog neuspelog sigurnosnog proveravanja. Osve\u017Ei stranicu da poku\u0161a\u0161 ponovo ili se obrati podr\u0161ci za vi\u0161e pomo\u0107i.",
    captcha_unavailable: "Registracija neuspe\u0161na zbog neuspelog proveravanja bota. Osve\u017Ei stranicu da poku\u0161a\u0161 ponovo ili se obrati podr\u0161ci za vi\u0161e pomo\u0107i.",
    form_code_incorrect: "Uneti kod je neta\u010Dan.",
    form_identifier_exists__email_address: "Ova e-mail adresa je zauzeta. Molimo poku\u0161aj sa drugom.",
    form_identifier_exists__phone_number: "Ovaj telefonski broj je zauzet. Molimo poku\u0161aj sa drugim.",
    form_identifier_exists__username: "Ovo korisni\u010Dko ime je zauzeto. Molimo poku\u0161aj sa drugim.",
    form_identifier_not_found: "Nismo mogli prona\u0107i nalog sa ovim podacima.",
    form_param_format_invalid: "Format parametra je neva\u017Ee\u0107i.",
    form_param_format_invalid__email_address: "E-mail adresa mora biti va\u017Ee\u0107a e-mail adresa.",
    form_param_format_invalid__phone_number: "Telefonski broj mora biti u va\u017Ee\u0107em me\u0111unarodnom formatu",
    form_param_max_length_exceeded__first_name: "Ime ne sme prema\u0161iti 256 karaktera.",
    form_param_max_length_exceeded__last_name: "Prezime ne sme prema\u0161iti 256 karaktera.",
    form_param_max_length_exceeded__name: "Naziv ne sme prema\u0161iti 256 karaktera.",
    form_param_nil: "Parametar ne mo\u017Ee biti prazan.",
    form_param_value_invalid: void 0,
    form_password_incorrect: "Lozinka je neta\u010Dna.",
    form_password_length_too_short: "Lozinka je prekratka.",
    form_password_not_strong_enough: "Tvoja lozinka nije dovoljno jaka.",
    form_password_pwned: "Ova lozinka je prona\u0111ena kao deo kompromitovanih podataka i ne mo\u017Ee se koristiti, molimo poku\u0161aj sa drugom lozinkom.",
    form_password_pwned__sign_in: "Ova lozinka je prona\u0111ena kao deo kompromitovanih podataka i ne mo\u017Ee se koristiti, molimo resetuj svoju lozinku.",
    form_password_size_in_bytes_exceeded: "Tvoja lozinka je prema\u0161ila maksimalni dozvoljeni broj bajtova, molimo skrati je ili ukloni neke specijalne znakove.",
    form_password_validation_failed: "Neispravna lozinka",
    form_username_invalid_character: "Korisni\u010Dko ime sadr\u017Ei neva\u017Ee\u0107e karaktere.",
    form_username_invalid_length: "Du\u017Eina korisni\u010Dkog imena nije validna.",
    identification_deletion_failed: "Ne mo\u017Ee\u0161 obrisati svoju poslednju identifikaciju.",
    not_allowed_access: "Adresa e-maila ili broja telefona nije dozvoljena za registraciju. Ovo mo\u017Ee biti zbog kori\u0161\u0107enja '+', '=', '#' ili '.' u adresi e-maila, kori\u0161\u0107enja domena koji je povezan sa vremenskom e-mail uslugom ili eksplicitnom isklju\u010Denju.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: "Klju\u010D za prolaz je ve\u0107 registrovan sa ovim ure\u0111ajem.",
    passkey_not_supported: "Klju\u010Devi za prolaz nisu podr\u017Eani na ovom ure\u0111aju.",
    passkey_pa_not_supported: "Registracija zahteva platformski autentifikator, ali ure\u0111aj to ne podr\u017Eava.",
    passkey_registration_cancelled: "Registracija klju\u010Da za prolaz je otkazana ili je isteklo vreme.",
    passkey_retrieval_cancelled: "Verifikacija klju\u010Da za prolaz je otkazana ili je isteklo vreme.",
    passwordComplexity: {
      maximumLength: "manje od {{length}} karaktera",
      minimumLength: "{{length}} ili vi\u0161e karaktera",
      requireLowercase: "malo slovo",
      requireNumbers: "broj",
      requireSpecialCharacter: "specijalni znak",
      requireUppercase: "veliko slovo",
      sentencePrefix: "Tvoja lozinka mora sadr\u017Eati"
    },
    phone_number_exists: "Ovaj telefonski broj je zauzet. Molimo poku\u0161aj sa drugim.",
    session_exists: "Ve\u0107 ste prijavljeni.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "Tvoja lozinka funkcioni\u0161e, ali mo\u017Ee biti ja\u010Da. Poku\u0161aj dodati vi\u0161e karaktera.",
      goodPassword: "Tvoja lozinka ispunjava sve potrebne zahteve.",
      notEnough: "Tvoja lozinka nije dovoljno jaka.",
      suggestions: {
        allUppercase: "Kapitalizuj neka, ali ne sva slova.",
        anotherWord: "Dodaj vi\u0161e re\u010Di koje su manje uobi\u010Dajene.",
        associatedYears: "Izbegavaj godine koje su povezane sa tobom.",
        capitalization: "Kapitalizuj vi\u0161e od prvog slova.",
        dates: "Izbegavaj datume i godine koje su povezane sa tobom.",
        l33t: "Izbegavaj predvidljive zamene slova kao \u0161to su '@' za 'a'.",
        longerKeyboardPattern: "Koristi du\u017Ee \u0161ablone na tastaturi i promeni smer kucanja vi\u0161e puta.",
        noNeed: "Mo\u017Ee\u0161 kreirati jake lozinke bez kori\u0161\u0107enja simbola, brojeva ili velikih slova.",
        pwned: "Ako koristi\u0161 ovu lozinku negde drugde, trebalo bi da je promeni\u0161.",
        recentYears: "Izbegavaj skora\u0161nje godine.",
        repeated: "Izbegavaj ponavljane re\u010Di i karaktere.",
        reverseWords: "Izbegavaj obrnuto napisane uobi\u010Dajene re\u010Di.",
        sequences: "Izbegavaj uobi\u010Dajene sekvence karaktera.",
        useWords: "Koristi vi\u0161e re\u010Di, ali izbegavaj uobi\u010Dajene fraze."
      },
      warnings: {
        common: "Ovo je \u010Desto kori\u0161\u0107ena lozinka.",
        commonNames: "Uobi\u010Dajena imena i prezimena su lako za pogoditi.",
        dates: "Datumi su lako za pogoditi.",
        extendedRepeat: 'Ponavljani obrasci karaktera kao \u0161to su "abcabcabc" su lako za pogoditi.',
        keyPattern: "Kratki \u0161abloni na tastaturi su lako za pogoditi.",
        namesByThemselves: "Pojedina\u010Dna imena ili prezimena su lako za pogoditi.",
        pwned: "Tvoja lozinka je otkrivena u kr\u0161enju podataka na internetu.",
        recentYears: "Skora\u0161nje godine su lako za pogoditi.",
        sequences: 'Uobi\u010Dajene sekvence karaktera kao \u0161to su "abc" su lako za pogoditi.',
        similarToCommon: "Ovo je sli\u010Dno \u010Desto kori\u0161\u0107enoj lozinki.",
        simpleRepeat: 'Ponavljani karakteri kao \u0161to su "aaa" su lako za pogoditi.',
        straightRow: "Direktne linije tastera na tvojoj tastaturi su lako za pogoditi.",
        topHundred: "Ovo je \u010Desto kori\u0161\u0107ena lozinka.",
        topTen: "Ovo je veoma \u010Desto kori\u0161\u0107ena lozinka.",
        userInputs: "Ne sme biti li\u010Dnih podataka ili podataka vezanih za stranicu.",
        wordByItself: "Pojedina\u010Dne re\u010Di su lako za pogoditi."
      }
    }
  },
  userButton: {
    action__addAccount: "Dodaj nalog",
    action__manageAccount: "Upravljaj nalogom",
    action__signOut: "Odjavi se",
    action__signOutAll: "Odjavi se sa svih naloga"
  },
  userProfile: {
    backupCodePage: {
      actionLabel__copied: "Kopirano!",
      actionLabel__copy: "Kopiraj sve",
      actionLabel__download: "Preuzmi .txt",
      actionLabel__print: "\u0160tampaj",
      infoText1: "Rezervni kodovi \u0107e biti omogu\u0107eni za ovaj nalog.",
      infoText2: "\u010Cuvaj rezervne kodove u tajnosti i \u010Duvaj ih na sigurnom mestu. Mo\u017Ee\u0161 regenerisati rezervne kodove ako sumnja\u0161 da su kompromitovani.",
      subtitle__codelist: "\u010Cuvaj ih na sigurnom i dr\u017Ei ih u tajnosti.",
      successMessage: "Rezervni kodovi su sada omogu\u0107eni. Mo\u017Ee\u0161 koristiti jedan od ovih kodova za prijavu na svoj nalog, ako izgubi\u0161 pristup svom ure\u0111aju za autentifikaciju. Svaki kod mo\u017Ee biti kori\u0161\u0107en samo jednom.",
      successSubtitle: "Mo\u017Ee\u0161 koristiti jedan od ovih kodova za prijavu na svoj nalog, ako izgubi\u0161 pristup svom ure\u0111aju za autentifikaciju.",
      title: "Dodaj verifikaciju rezervnim kodom",
      title__codelist: "Rezervni kodovi"
    },
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "Izaberi provajdera da pove\u017Ee\u0161 svoj nalog.",
      formHint__noAccounts: "Nema dostupnih spoljnih provajdera naloga.",
      removeResource: {
        messageLine1: "{{identifier}} \u0107e biti uklonjen iz ovog naloga.",
        messageLine2: "Vi\u0161e ne\u0107e\u0161 mo\u0107i da koristi\u0161 ovaj povezani nalog i bilo koje zavisne funkcije vi\u0161e ne\u0107e raditi.",
        successMessage: "{{connectedAccount}} je uklonjen iz tvog naloga.",
        title: "Ukloni povezani nalog"
      },
      socialButtonsBlockButton: "{{provider|titleize}}",
      successMessage: "Provajder je dodat na tvoj nalog",
      title: "Dodaj povezani nalog"
    },
    deletePage: {
      actionDescription: 'Upi\u0161i "Delete account" ispod da nastavi\u0161.',
      confirm: "Obri\u0161i nalog",
      messageLine1: "Da li si siguran da \u017Eeli\u0161 da obri\u0161e\u0161 svoj nalog?",
      messageLine2: "Ova akcija je trajna i nepovratna.",
      title: "Obri\u0161i nalog"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "E-mail sadr\u017Ei verifikacioni kod koji \u0107e biti poslat na ovu e-mail adresu.",
        formSubtitle: "Unesi verifikacioni kod poslat na {{identifier}}",
        formTitle: "Verifikacioni kod",
        resendButton: "Nisi primio kod? Po\u0161alji ponovo",
        successMessage: "E-mail {{identifier}} je dodat na tvoj nalog."
      },
      emailLink: {
        formHint: "E-mail sadr\u017Ei verifikacioni link koji \u0107e biti poslat na ovu e-mail adresu.",
        formSubtitle: "Klikni na verifikacioni link u e-mailu poslatom na {{identifier}}",
        formTitle: "Verifikacioni link",
        resendButton: "Nisi primio link? Po\u0161alji ponovo",
        successMessage: "E-mail {{identifier}} je dodat na tvoj nalog."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} \u0107e biti uklonjen iz ovog naloga.",
        messageLine2: "Vi\u0161e ne\u0107e\u0161 mo\u0107i da se prijavi\u0161 koriste\u0107i ovu e-mail adresu.",
        successMessage: "{{emailAddress}} je uklonjen iz tvog naloga.",
        title: "Ukloni e-mail adresu"
      },
      title: "Dodaj e-mail adresu",
      verifyTitle: "Verifikuj e-mail adresu"
    },
    formButtonPrimary__add: "Dodaj",
    formButtonPrimary__continue: "Nastavi",
    formButtonPrimary__finish: "Zavr\u0161i",
    formButtonPrimary__remove: "Ukloni",
    formButtonPrimary__save: "Sa\u010Duvaj",
    formButtonReset: "Otka\u017Ei",
    mfaPage: {
      formHint: "Izaberi metodu za dodavanje.",
      title: "Dodaj dvostepenu verifikaciju"
    },
    mfaPhoneCodePage: {
      backButton: "Koristi postoje\u0107i broj",
      primaryButton__addPhoneNumber: "Dodaj telefonski broj",
      removeResource: {
        messageLine1: "{{identifier}} vi\u0161e ne\u0107e primati verifikacione kodove prilikom prijavljivanja.",
        messageLine2: "Tvoj nalog mo\u017Eda ne\u0107e biti toliko siguran. Da li si siguran da \u017Eeli\u0161 da nastavi\u0161?",
        successMessage: "SMS kod dvostepene verifikacije je uklonjen za {{mfaPhoneCode}}",
        title: "Ukloni dvostepenu verifikaciju"
      },
      subtitle__availablePhoneNumbers: "Izaberi postoje\u0107i telefonski broj za registraciju SMS kod dvostepene verifikacije ili dodaj novi.",
      subtitle__unavailablePhoneNumbers: "Nema dostupnih telefonskih brojeva za registraciju SMS kod dvostepene verifikacije, molimo dodaj novi.",
      successMessage1: "Kada se prijavi\u0161, mora\u0107e\u0161 uneti verifikacioni kod poslat na ovaj telefonski broj kao dodatni korak.",
      successMessage2: "Sa\u010Duvaj ove rezervne kodove i \u010Duvaj ih na sigurnom mestu. Ako izgubi\u0161 pristup svom ure\u0111aju za autentifikaciju, mo\u017Ee\u0161 koristiti rezervne kodove za prijavu.",
      successTitle: "SMS kod verifikacija je omogu\u0107ena",
      title: "Dodaj SMS kod verifikaciju"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Umesto toga, skeniraj QR kod",
        buttonUnableToScan__nonPrimary: "Ne mo\u017Ee\u0161 skenirati QR kod?",
        infoText__ableToScan: "Podesi novi na\u010Din prijave u svojoj aplikaciji za autentifikaciju i skeniraj slede\u0107i QR kod da ga pove\u017Ee\u0161 sa svojim nalogom.",
        infoText__unableToScan: "Podesi novi na\u010Din prijave u svojoj autentifikaciji i unesi klju\u010D naveden ispod.",
        inputLabel__unableToScan1: "Uveri se da su vremenski bazirane ili jednokratne lozinke omogu\u0107ene, zatim zavr\u0161i povezivanje svog naloga.",
        inputLabel__unableToScan2: "Alternativno, ako tvoja autentifikacija podr\u017Eava TOTP URI, mo\u017Ee\u0161 tako\u0111e kopirati celu URI adresu."
      },
      removeResource: {
        messageLine1: "Verifikacioni kodovi iz ove autentifikacije vi\u0161e ne\u0107e biti potrebni prilikom prijavljivanja.",
        messageLine2: "Tvoj nalog mo\u017Eda ne\u0107e biti toliko siguran. Da li si siguran da \u017Eeli\u0161 da nastavi\u0161?",
        successMessage: "Dvostepena verifikacija preko autentifikacione aplikacije je uklonjena.",
        title: "Ukloni dvostepenu verifikaciju"
      },
      successMessage: "Dvostepena verifikacija je sada omogu\u0107ena. Kada se prijavi\u0161, mora\u0107e\u0161 uneti verifikacioni kod iz ove autentifikacije kao dodatni korak.",
      title: "Dodaj autentifikacionu aplikaciju",
      verifySubtitle: "Unesi verifikacioni kod generisan tvojom autentifikacijom",
      verifyTitle: "Verifikacioni kod"
    },
    mobileButton__menu: "Meni",
    navbar: {
      account: "Profil",
      billing: void 0,
      description: "Upravljaj informacijama svog naloga.",
      security: "Sigurnost",
      title: "Nalog"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "{{name}} \u0107e biti uklonjen iz ovog naloga.",
        title: "Ukloni klju\u010D za prolaz"
      },
      subtitle__rename: "Mo\u017Ee\u0161 promeniti ime klju\u010Da za prolaz kako bi ga lak\u0161e prona\u0161ao.",
      title__rename: "Preimenuj klju\u010D za prolaz"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Preporu\u010Duje se da se odjavi\u0161 sa svih drugih ure\u0111aja koji su mo\u017Eda koristili tvoju staru lozinku.",
      readonly: "Tvoja lozinka trenutno ne mo\u017Ee biti ure\u0111ivana jer se mo\u017Ee\u0161 prijaviti samo preko korporativne veze.",
      successMessage__set: "Tvoja lozinka je postavljena.",
      successMessage__signOutOfOtherSessions: "Svi drugi ure\u0111aji su odjavljeni.",
      successMessage__update: "Tvoja lozinka je a\u017Eurirana.",
      title__set: "Postavi lozinku",
      title__update: "A\u017Euriraj lozinku"
    },
    phoneNumberPage: {
      infoText: "Tekstualna poruka sa verifikacionim kodom \u0107e biti poslata na ovaj telefonski broj. Mogu\u0107e su naknade za poruke i podatke.",
      removeResource: {
        messageLine1: "{{identifier}} \u0107e biti uklonjen iz ovog naloga.",
        messageLine2: "Vi\u0161e ne\u0107e\u0161 mo\u0107i da se prijavi\u0161 koriste\u0107i ovaj telefonski broj.",
        successMessage: "{{phoneNumber}} je uklonjen iz tvog naloga.",
        title: "Ukloni telefonski broj"
      },
      successMessage: "{{identifier}} je dodat na tvoj nalog.",
      title: "Dodaj telefonski broj",
      verifySubtitle: "Unesi verifikacioni kod poslat na {{identifier}}",
      verifyTitle: "Verifikuj telefonski broj"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "Preporu\u010Dena veli\u010Dina 1:1, do 10MB.",
      imageFormDestructiveActionSubtitle: "Ukloni",
      imageFormSubtitle: "Otpremi",
      imageFormTitle: "Profilna slika",
      readonly: "Tvoje profilne informacije su obezbe\u0111ene preko korporativne veze i ne mogu biti ure\u0111ivane.",
      successMessage: "Tvoj profil je a\u017Euriran.",
      title: "A\u017Euriraj profil"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Odjavi ure\u0111aj",
        title: "Aktivni ure\u0111aji"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Poku\u0161aj ponovo",
        actionLabel__reauthorize: "Autorizuj sada",
        destructiveActionTitle: "Ukloni",
        primaryButton: "Pove\u017Ei nalog",
        subtitle__disconnected: void 0,
        subtitle__reauthorize: "Potrebna ovla\u0161\u0107enja su a\u017Eurirana, i mo\u017Eda do\u017Eivljava\u0161 ograni\u010Denu funkcionalnost. Molimo re-autorizuj ovu aplikaciju da izbegne\u0161 bilo kakve probleme",
        title: "Povezani nalozi"
      },
      dangerSection: {
        deleteAccountButton: "Obri\u0161i nalog",
        title: "Obri\u0161i nalog"
      },
      emailAddressesSection: {
        destructiveAction: "Ukloni e-mail",
        detailsAction__nonPrimary: "Postavi kao primarni",
        detailsAction__primary: "Zavr\u0161i verifikaciju",
        detailsAction__unverified: "Verifikuj",
        primaryButton: "Dodaj e-mail adresu",
        title: "E-mail adrese"
      },
      enterpriseAccountsSection: {
        title: "Korporativni nalozi"
      },
      headerTitle__account: "Detalji profila",
      headerTitle__security: "Sigurnost",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Regeneri\u0161i",
          headerTitle: "Rezervni kodovi",
          subtitle__regenerate: "Dobij novi set sigurnih rezervnih kodova. Prethodni rezervni kodovi \u0107e biti obrisani i ne\u0107e mo\u0107i biti kori\u0161\u0107eni.",
          title__regenerate: "Regeneri\u0161i rezervne kodove"
        },
        phoneCode: {
          actionLabel__setDefault: "Postavi kao podrazumevani",
          destructiveActionLabel: "Ukloni"
        },
        primaryButton: "Dodaj dvostepenu verifikaciju",
        title: "Dvostepena verifikacija",
        totp: {
          destructiveActionTitle: "Ukloni",
          headerTitle: "Autentifikaciona aplikacija"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Ukloni",
        menuAction__rename: "Preimenuj",
        primaryButton: void 0,
        title: "Klju\u010Devi za prolaz"
      },
      passwordSection: {
        primaryButton__setPassword: "Postavi lozinku",
        primaryButton__updatePassword: "A\u017Euriraj lozinku",
        title: "Lozinka"
      },
      phoneNumbersSection: {
        destructiveAction: "Ukloni telefonski broj",
        detailsAction__nonPrimary: "Postavi kao podrazumevani",
        detailsAction__primary: "Zavr\u0161i verifikaciju",
        detailsAction__unverified: "Verifikuj telefonski broj",
        primaryButton: "Dodaj telefonski broj",
        title: "Telefonski brojevi"
      },
      profileSection: {
        primaryButton: "A\u017Euriraj profil",
        title: "Profil"
      },
      usernameSection: {
        primaryButton__setUsername: "Postavi korisni\u010Dko ime",
        primaryButton__updateUsername: "A\u017Euriraj korisni\u010Dko ime",
        title: "Korisni\u010Dko ime"
      },
      web3WalletsSection: {
        destructiveAction: "Ukloni nov\u010Danik",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Web3 nov\u010Danici",
        title: "Web3 nov\u010Danici"
      }
    },
    usernamePage: {
      successMessage: "Tvoje korisni\u010Dko ime je a\u017Eurirano.",
      title__set: "Postavi korisni\u010Dko ime",
      title__update: "A\u017Euriraj korisni\u010Dko ime"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} \u0107e biti uklonjen iz ovog naloga.",
        messageLine2: "Vi\u0161e ne\u0107e\u0161 mo\u0107i da se prijavi\u0161 koriste\u0107i ovaj web3 nov\u010Danik.",
        successMessage: "{{web3Wallet}} je uklonjen iz tvog naloga.",
        title: "Ukloni web3 nov\u010Danik"
      },
      subtitle__availableWallets: "Izaberi web3 nov\u010Danik da ga pove\u017Ee\u0161 sa svojim nalogom.",
      subtitle__unavailableWallets: "Nema dostupnih web3 nov\u010Danika.",
      successMessage: "Nov\u010Danik je dodat na tvoj nalog.",
      title: "Dodaj web3 nov\u010Danik",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  srRS
};
//# sourceMappingURL=sr-RS.mjs.map