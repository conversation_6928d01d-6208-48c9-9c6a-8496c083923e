"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["270"],{9327:function(e,t,o){o.d(t,{fB:()=>a,gW:()=>m,uj:()=>B});var n=o(9109),r=o(2305),s=o(8774),i=o(7295),c=o(4676),l=o(1673),u=o(1576),d=o(9541),p=o(4174);let a=e=>{let{navigate:t}=(0,c.useRouter)(),{handleManageAccountClicked:o,handleSignOutSessionClicked:s,handleUserProfileActionClicked:i,session:p}=e,{menutItems:a}=(0,u.useUserButtonContext)(),B=e=>({borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100,padding:`${e.space.$4} ${e.space.$5}`}),m=async n=>n?.path?(await t(n.path),e?.completedCallback()):n.id===l.Zb.MANAGE_ACCOUNT?await o():n?.open?i(n.open):(n.onClick?.(),e?.completedCallback());return(0,n.tZ)(r.eX,{role:"menu",elementDescriptor:d.descriptors.userButtonPopoverActions,elementId:d.descriptors.userButtonPopoverActions.setId("singleSession"),sx:e=>({borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100}),children:a?.map(e=>{let t;return t=Object.values(l.Zb).includes(e.id)?{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId(e.id)}:{elementDescriptor:d.descriptors.userButtonPopoverCustomItemButton,elementId:d.descriptors.userButtonPopoverCustomItemButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverCustomItemButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverCustomItemButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionItemButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionItemButtonIcon.setId(e.id)},n.tZ(r.aU,{...t,icon:e.icon,label:e.name,onClick:e.id===l.Zb.SIGN_OUT?s(p):()=>m(e),sx:B,iconSx:e=>({width:e.sizes.$4,height:e.sizes.$4})},e.id)})})},B=e=>{let{navigate:t}=(0,c.useRouter)(),{handleManageAccountClicked:o,handleSignOutSessionClicked:a,handleSessionClicked:B,handleAddAccountClicked:m,handleUserProfileActionClicked:I,session:v,otherSessions:A}=e,{menutItems:h}=(0,u.useUserButtonContext)(),C=async n=>n?.path?(await t(n.path),e?.completedCallback()):n.id===l.Zb.MANAGE_ACCOUNT?await o():n?.open?I(n.open):(n.onClick?.(),e?.completedCallback()),P=h.every(e=>Object.values(l.Zb).includes(e.id));return(0,n.BX)(n.HY,{children:[P?(0,n.tZ)(r.r5,{role:"menu",elementDescriptor:d.descriptors.userButtonPopoverActions,elementId:d.descriptors.userButtonPopoverActions.setId("multiSession"),children:(0,n.BX)(d.Flex,{justify:"between",sx:e=>({marginLeft:e.space.$12,padding:`0 ${e.space.$5} ${e.space.$4}`,gap:e.space.$2}),children:[(0,n.tZ)(r.U8,{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId("manageAccount"),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId("manageAccount"),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId("manageAccount"),icon:p.tc,label:(0,d.localizationKeys)("userButton.action__manageAccount"),onClick:o}),(0,n.tZ)(r.U8,{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId("signOut"),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId("signOut"),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId("signOut"),icon:p.lv,label:(0,d.localizationKeys)("userButton.action__signOut"),onClick:a(v)})]})}):(0,n.tZ)(r.r5,{role:"menu",elementDescriptor:d.descriptors.userButtonPopoverActions,elementId:d.descriptors.userButtonPopoverActions.setId("multiSession"),sx:e=>({gap:e.space.$1,paddingBottom:e.space.$2}),children:h?.map(e=>{let t;return t=Object.values(l.Zb).includes(e.id)?{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId(e.id)}:{elementDescriptor:d.descriptors.userButtonPopoverCustomItemButton,elementId:d.descriptors.userButtonPopoverCustomItemButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverCustomItemButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverCustomItemButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionItemButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionItemButtonIcon.setId(e.id)},n.tZ(r.aU,{...t,icon:e.icon,label:e.name,onClick:e.id===l.Zb.SIGN_OUT?a(v):()=>C(e),sx:e=>({border:0,padding:`${e.space.$2} ${e.space.$5}`,gap:e.space.$3x5}),iconSx:e=>({width:e.sizes.$4,height:e.sizes.$4}),iconBoxSx:e=>({minHeight:e.sizes.$4,minWidth:e.sizes.$4,alignItems:"center"})},e.id)})}),(0,n.BX)(r.eX,{role:"menu",sx:e=>({borderTopStyle:e.borderStyles.$solid,borderTopWidth:e.borderWidths.$normal,borderTopColor:e.colors.$neutralAlpha100}),children:[A.map(e=>(0,n.tZ)(s.K,{icon:p.nR,onClick:B(e),role:"menuitem",children:(0,n.tZ)(i.E,{user:e.user})},e.id)),(0,n.tZ)(r.aU,{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId("addAccount"),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId("addAccount"),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId("addAccount"),icon:p.mm,label:(0,d.localizationKeys)("userButton.action__addAccount"),onClick:m,iconSx:e=>({width:e.sizes.$9,height:e.sizes.$6}),iconBoxSx:e=>({minHeight:e.sizes.$9,minWidth:e.sizes.$6,alignItems:"center"}),spinnerSize:"md"})]})]})},m=e=>{let{handleSignOutAllClicked:t,elementDescriptor:o,elementId:s,iconBoxElementDescriptor:i,iconBoxElementId:c,iconElementDescriptor:l,iconElementId:u,label:a,sx:B,actionSx:m}=e;return(0,n.tZ)(r.eX,{role:"menu",sx:[e=>({padding:e.space.$2}),B],children:(0,n.tZ)(r.aU,{elementDescriptor:o||d.descriptors.userButtonPopoverActionButton,elementId:s||d.descriptors.userButtonPopoverActionButton.setId("signOutAll"),iconBoxElementDescriptor:i||d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:c||d.descriptors.userButtonPopoverActionButtonIconBox.setId("signOutAll"),iconElementDescriptor:l||d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:u||d.descriptors.userButtonPopoverActionButtonIcon.setId("signOutAll"),icon:p.lv,label:a||(0,d.localizationKeys)("userButton.action__signOutAll"),onClick:t,variant:"ghost",colorScheme:"neutral",sx:[e=>({backgroundColor:e.colors.$transparent,padding:`${e.space.$2} ${e.space.$3}`,borderBottomWidth:0,borderRadius:e.radii.$lg}),m],spinnerSize:"md"})})}},7322:function(e,t,o){o.r(t),o.d(t,{UserButton:()=>b});var n=o(9109),r=o(9144),s=o(2672),i=o(4045),c=o(1576),l=o(9541),u=o(2464),d=o(3799),p=o(1151),a=o(3929),B=o(7295),m=o(9327),I=o(5241);let v=r.forwardRef((e,t)=>{let{close:o,...r}=e,s=()=>o?.(!1),{session:i}=(0,d.kP)(),u=(0,c.useUserButtonContext)(),{__experimental_asStandalone:v}=u,{authConfig:A}=(0,c.useEnvironment)(),{user:h}=(0,d.aF)(),{handleAddAccountClicked:C,handleManageAccountClicked:P,handleSessionClicked:g,handleSignOutAllClicked:x,handleSignOutSessionClicked:b,handleUserProfileActionClicked:$,otherSessions:k}=(0,I.Z)({...u,actionCompleteCallback:s,user:h});return(0,n.tZ)(a.r,{elementDescriptor:l.descriptors.userButtonPopoverRootBox,children:(0,n.BX)(p.f.Root,{elementDescriptor:l.descriptors.userButtonPopoverCard,ref:t,role:"dialog","aria-label":"User button popover",shouldEntryAnimate:!v,...r,children:[(0,n.BX)(p.f.Content,{elementDescriptor:l.descriptors.userButtonPopoverMain,children:[(0,n.tZ)(B.E,{elementId:"userButton",user:h,sx:e=>({width:"100%",padding:`${e.space.$4} ${e.space.$5}`})}),A.singleSessionMode?(0,n.tZ)(m.fB,{handleManageAccountClicked:P,handleSignOutSessionClicked:b,handleUserProfileActionClicked:$,session:i,completedCallback:s}):(0,n.tZ)(m.uj,{session:i,otherSessions:k,handleManageAccountClicked:P,handleSignOutSessionClicked:b,handleSessionClicked:g,handleAddAccountClicked:C,handleUserProfileActionClicked:$,completedCallback:s})]}),(0,n.tZ)(p.f.Footer,{elementDescriptor:l.descriptors.userButtonPopoverFooter,children:!A.singleSessionMode&&k.length>0&&(0,n.tZ)(m.gW,{handleSignOutAllClicked:x})})]})})});var A=o(5472),h=o(7711),C=o(5878);let P=({showName:e})=>{let{user:t}=(0,d.aF)();return t&&e?(0,n.tZ)(l.Text,{variant:"subtitle",as:"span",elementDescriptor:l.descriptors.userButtonOuterIdentifier,sx:[e=>({paddingLeft:e.space.$2})],children:(0,C.Pp)(t)||(0,C.xC)(t)}):null},g=(0,h.C)((0,r.forwardRef)((e,t)=>{let{sx:o,...r}=e,{user:s}=(0,d.aF)(),{showName:i}=(0,c.useUserButtonContext)();return(0,n.tZ)(l.Button,{elementDescriptor:l.descriptors.userButtonTrigger,variant:"roundWrapper",sx:[e=>({borderRadius:i?e.radii.$md:e.radii.$circle,color:e.colors.$colorText}),o],ref:t,"aria-label":`${e.isOpen?"Close":"Open"} user button`,"aria-expanded":e.isOpen,"aria-haspopup":"dialog",...r,children:(0,n.BX)(l.Flex,{elementDescriptor:l.descriptors.userButtonBox,isOpen:e.isOpen,align:"center",as:"span",gap:2,children:[(0,n.tZ)(P,{showName:i}),(0,n.tZ)(A.Y,{boxElementDescriptor:l.descriptors.userButtonAvatarBox,imageElementDescriptor:l.descriptors.userButtonAvatarImage,...s,size:e=>e.sizes.$7})]})})})),x=(0,s.withFloatingTree)(({children:e})=>{let{defaultOpen:t}=(0,c.useUserButtonContext)(),{floating:o,reference:s,styles:l,toggle:d,isOpen:p,nodeId:a,context:B}=(0,u.Sv)({defaultOpen:t,placement:"bottom-end",offset:8}),m=(0,r.useId)();return(0,n.BX)(n.HY,{children:[(0,n.tZ)(g,{ref:s,onClick:d,isOpen:p,"aria-controls":p?m:void 0,"aria-expanded":p}),(0,n.tZ)(i.J,{nodeId:a,context:B,isOpen:p,children:(0,r.cloneElement)(e,{id:m,close:d,ref:o,style:l})})]})}),b=(0,c.withCoreUserGuard)((0,s.withCardStateProvider)(()=>{let{__experimental_asStandalone:e}=(0,c.useUserButtonContext)();return(0,n.tZ)(l.Flow.Root,{flow:"userButton",sx:{display:"inline-flex"},children:e?(0,n.tZ)(v,{close:"function"==typeof e?e:void 0}):(0,n.tZ)(x,{children:(0,n.tZ)(v,{})})})}))},5241:function(e,t,o){o.d(t,{Z:()=>u});var n=o(3799),r=o(2672),s=o(5809),i=o(4264),c=o(4676),l=o(7623);let u=e=>{let{setActive:t,signOut:o,openUserProfile:u}=(0,n.cL)(),d=(0,r.useCardState)(),{signedInSessions:p,otherSessions:a}=(0,i.j)({user:e.user}),{navigate:B}=(0,c.useRouter)();return{handleSignOutSessionClicked:t=>()=>0===a.length?o(e.navigateAfterSignOut):o(e.navigateAfterMultiSessionSingleSignOut,{sessionId:t.id}).finally(()=>d.setIdle()),handleManageAccountClicked:()=>"navigation"===e.userProfileMode?B(e.userProfileUrl||"").finally(()=>{(async()=>{await (0,l._v)(300),e.actionCompleteCallback?.()})()}):(u(e.userProfileProps),e.actionCompleteCallback?.()),handleUserProfileActionClicked:t=>"navigation"===e.userProfileMode?B(e.userProfileUrl||"").finally(()=>{(async()=>{await (0,l._v)(300),e.actionCompleteCallback?.()})()}):(u({...e.userProfileProps,...t&&{__experimental_startPath:t}}),e.actionCompleteCallback?.()),handleSignOutAllClicked:()=>o(e.navigateAfterSignOut),handleSessionClicked:o=>async()=>(d.setLoading(),t({session:o,redirectUrl:e.afterSwitchSessionUrl}).finally(()=>{d.setIdle(),e.actionCompleteCallback?.()})),handleAddAccountClicked:()=>((0,s.T7)(e.signInUrl||window.location.href),(0,l._v)(2e3)),otherSessions:a,signedInSessions:p}}}}]);