import type { FormProps } from '@/ui/elements/FormContainer';
type LeaveOrganizationFormProps = FormProps;
export declare const LeaveOrganizationForm: (props: LeaveOrganizationFormProps) => import("@emotion/react/jsx-runtime").JSX.Element | null;
type DeleteOrganizationFormProps = FormProps;
export declare const DeleteOrganizationForm: (props: DeleteOrganizationFormProps) => import("@emotion/react/jsx-runtime").JSX.Element | null;
export {};
