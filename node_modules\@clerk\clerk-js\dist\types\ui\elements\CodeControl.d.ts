import type { PropsWithChildren } from 'react';
import React from 'react';
import type { LocalizationKey } from '../customizables';
import type { FormControlState } from '../utils';
type UseCodeInputOptions = {
    length?: number;
};
type onCodeEntryFinishedCallback = (code: string) => unknown;
type onCodeEntryFinished = (cb: onCodeEntryFinishedCallback) => void;
type onCodeEntryFinishedActionCallback<R = unknown> = (code: string, resolve: (params?: R) => Promise<void>, reject: (err: unknown) => Promise<void>) => void;
type UseFieldOTP = <R = unknown>(params: {
    id?: 'code';
    onCodeEntryFinished: onCodeEntryFinishedActionCallback<R>;
    onResendCodeClicked?: React.MouseEventHandler;
    onResolve?: (a?: R) => Promise<void> | void;
}) => {
    isLoading: boolean;
    otpControl: ReturnType<typeof useCodeControl>;
    onResendCode: React.MouseEventHandler<HTMLButtonElement> | undefined;
    onFakeContinue: () => void;
};
export declare const useFieldOTP: UseFieldOTP;
declare const useCodeControl: (formControl: FormControlState, options?: UseCodeInputOptions) => {
    otpInputProps: {
        length: number;
        values: string[];
        setValues: React.Dispatch<React.SetStateAction<string[]>>;
        feedback: string;
        feedbackType: import("../utils").FeedbackType;
        clearFeedback: (() => void) & (() => void);
        ref: React.MutableRefObject<any>;
    };
    onCodeEntryFinished: onCodeEntryFinished;
    reset: () => any;
};
export type OTPInputProps = {
    label?: string | LocalizationKey;
    resendButton?: LocalizationKey;
    description?: string | LocalizationKey;
    isLoading: boolean;
    isDisabled?: boolean;
    onResendCode?: React.MouseEventHandler<HTMLButtonElement>;
    otpControl: ReturnType<typeof useFieldOTP>['otpControl'];
    centerAlign?: boolean;
};
export declare const OTPRoot: ({ children, ...props }: PropsWithChildren<OTPInputProps>) => import("@emotion/react/jsx-runtime").JSX.Element;
export declare const OTPResendButton: () => import("@emotion/react/jsx-runtime").JSX.Element | null;
export declare const OTPCodeControl: React.ForwardRefExoticComponent<React.RefAttributes<{
    reset: any;
}>>;
export {};
