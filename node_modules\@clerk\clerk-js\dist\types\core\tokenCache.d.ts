import type { TokenResource } from '@clerk/types';
interface TokenCacheKeyJSON {
    audience?: string;
    tokenId: string;
}
interface TokenCacheEntry extends TokenCacheKeyJSON {
    tokenResolver: Promise<TokenResource>;
}
interface TokenCache {
    set(entry: TokenCacheEntry): void;
    get(cacheKeyJSON: TokenCacheKeyJSON, leeway?: number): TokenCacheEntry | undefined;
    clear(): void;
    size(): number;
}
export declare class TokenCacheKey {
    prefix: string;
    data: TokenCacheKeyJSON;
    static from<PERSON>ey(key: string): TokenCacheKey;
    constructor(prefix: string, data: TokenCacheKeyJSON);
    toKey(): string;
}
export declare const SessionTokenCache: TokenCache;
export {};
