"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["662"],{7389:function(e,t,r){r.d(t,{v:()=>n});var i=r(79109),o=r(24562),a=r(39541);let n=e=>{let{onBackLinkClick:t}=e;return(0,i.tZ)(o._,{cardTitle:(0,a.localizationKeys)("signIn.alternativeMethods.getHelp.title"),cardSubtitle:(0,a.localizationKeys)("signIn.alternativeMethods.getHelp.content"),onBackLinkClick:t})}},95518:function(e,t,r){r.d(t,{Vh:()=>f,Vs:()=>k,bx:()=>h,mQ:()=>Z,s2:()=>y,t3:()=>s,xT:()=>u}),r(28419),r(56113),r(65027);var i=r(32208),o=r(7772),a=r(77623),n=r(90577);let l=e=>t=>"safeIdentifier"in t&&t.safeIdentifier===e;function c(e){if((0,i.iW)()){let t=e.find(e=>{let{strategy:t}=e;return"passkey"===t});if(t)return t}return null}function s(e,t,r){return e&&0!==e.length?r===o.kJ.Password?function(e,t){let r=c(e);if(r)return r;let i=e.sort(n.sZ)[0];return"password"===i.strategy?i:e.find(l(t))||i||null}(e,t):function(e,t){let r=c(e);if(r)return r;let i=e.sort(n.b8),o=i.find(l(t));if(o)return o;let a=i[0];return"email_link"===a.strategy?a:e.find(l(t))||a||null}(e,t):null}let d=["passkey","email_code","password","phone_code","email_link"];function u(e){return!!e&&d.includes(e.strategy)}function h(e){if(!e||0===e.length)return null;let t=e.find(e=>"totp"===e.strategy);if(t)return t;let r=e.find(e=>"phone_code"===e.strategy);return r||e[0]}let p=["reset_password_phone_code","reset_password_email_code"],f=e=>!!e&&p.includes(e),v=e=>/^\S+@\S+\.\S+$/.test(e);function y(e){return"tel"===e.type?"phoneNumber":v(e.value)?"emailAddress":"username"}let k=(e,t,r)=>{var i,o;if(!t)return null;let n=null===(i=e.find(e=>"strategy"===e.id))||void 0===i?void 0:i.value;if(n&&"phone_code"!==n)return null;let l=null===(o=e.find(e=>e.id===r))||void 0===o?void 0:o.value;if(!l||!(null==l?void 0:l.startsWith("+")))return null;let c=(0,a.jR)(l,t);return"sms"===c?null:c},Z=(e,t,r)=>{if(!e||!t||"phoneNumber"!==t||!r||!(null==r?void 0:r.startsWith("+")))return null;let i=(0,a.jR)(r,e);return"sms"===i?null:i}},61419:function(e,t,r){r.r(t),r.d(t,{UserVerification:()=>ei,UserVerificationModal:()=>eo});var i=r(79109),o=r(69144),a=r(11576),n=r(39541),l=r(24676);r(28419),r(65223);var c=r(2672),s=r(24562),d=r(21455),u=r(91085),h=r(95518);r(92037);var p=r(38246),f=r(5482),v=r(44455),y=r(92654),k=r(96519),Z=r(77623),m=r(32208);let C=(e,t)=>!!e&&!!t&&("email_code"===e.strategy&&"email_code"===t.strategy?e.emailAddressId===t.emailAddressId:"phone_code"===e.strategy&&"phone_code"===t.strategy?e.phoneNumberId===t.phoneNumberId:e.strategy===t.strategy),b=(e,t)=>!!e&&!!t&&("phone_code"===e.strategy&&"phone_code"===t.strategy?e.phoneNumberId===t.phoneNumberId:e.strategy===t.strategy);function g(e){let{filterOutFactor:t,supportedFirstFactors:r}=e,i=r?r.filter(e=>!(0,h.Vh)(e.strategy)):[],a=i&&i.length>0,n=(0,o.useMemo)(()=>r?r.filter(e=>!e.strategy.startsWith("oauth_")).filter(e=>(0,h.xT)(e)).filter(e=>!C(e,t)).filter(e=>"passkey"!==e.strategy||(0,m.iW)()).sort(Z.U6):[],[r,t]);return{hasAnyStrategy:a,hasFirstParty:n&&n.length>0,firstPartyFactors:n}}var w=r(83799),M=r(12464);let _=()=>{let{level:e}=(0,a.useUserVerification)();return(0,o.useMemo)(()=>({level:e||"second_factor"}),[e])},B=()=>{let{session:e}=(0,w.kP)(),t=_();return{...(0,M.ib)(e?e.startVerification:void 0,t,{throttleTime:300})}};function S(e){let t=t=>{let{isLoading:r,data:o}=B();return r||!o?(0,i.tZ)(d.W,{}):(0,i.tZ)(e,{...t})},r=e.displayName||e.name||"Component";return e.displayName=r,t.displayName=r,t}let A=e=>{let{onBackLinkClick:t}=e;return(0,i.tZ)(s._,{cardTitle:(0,n.localizationKeys)("reverification.alternativeMethods.getHelp.title"),cardSubtitle:(0,n.localizationKeys)("reverification.alternativeMethods.getHelp.content"),onBackLinkClick:t})},z=(e,t)=>{let[r,a]=o.useState(!1),n=o.useCallback(()=>a(e=>!e),[a]);return r?(0,i.tZ)(A,{onBackLinkClick:n}):(0,i.tZ)(e,{...t,onHavingTroubleClick:n})},K=e=>z(F,{...e}),F=e=>{let{onBackLinkClick:t,onHavingTroubleClick:r,onFactorSelected:o}=e,a=(0,c.useCardState)(),{data:l}=B(),{firstPartyFactors:s,hasAnyStrategy:d}=g({filterOutFactor:null==e?void 0:e.currentFactor,supportedFirstFactors:null==l?void 0:l.supportedFirstFactors});return(0,i.tZ)(n.Flow.Part,{part:"alternativeMethods",children:(0,i.BX)(v.Z.Root,{children:[(0,i.BX)(v.Z.Content,{children:[(0,i.BX)(y.h.Root,{children:[(0,i.tZ)(y.h.Title,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.title")}),(0,i.tZ)(y.h.Subtitle,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.subtitle")})]}),(0,i.tZ)(v.Z.Alert,{children:a.error}),(0,i.tZ)(n.Flex,{direction:"col",elementDescriptor:n.descriptors.main,gap:6,children:(0,i.BX)(n.Col,{gap:4,children:[d&&(0,i.tZ)(n.Flex,{elementDescriptor:n.descriptors.alternativeMethods,direction:"col",gap:2,children:s.map((e,t)=>{var r;return(0,i.tZ)(p.$,{leftIcon:(r=e,({email_code:k.GT,phone_code:k.iU,password:k.kh,passkey:k.IG})[r.strategy]),textLocalizationKey:function(e){switch(e.strategy){case"email_code":return(0,n.localizationKeys)("reverification.alternativeMethods.blockButton__emailCode",{identifier:(0,Z.HT)(e.safeIdentifier)||""});case"phone_code":return(0,n.localizationKeys)("reverification.alternativeMethods.blockButton__phoneCode",{identifier:(0,Z.HT)(e.safeIdentifier)||""});case"password":return(0,n.localizationKeys)("reverification.alternativeMethods.blockButton__password");case"passkey":return(0,n.localizationKeys)("reverification.alternativeMethods.blockButton__passkey");default:throw Error('Invalid sign in strategy: "'.concat(e.strategy,'"'))}}(e),elementDescriptor:n.descriptors.alternativeMethodsBlockButton,textElementDescriptor:n.descriptors.alternativeMethodsBlockButtonText,arrowElementDescriptor:n.descriptors.alternativeMethodsBlockButtonArrow,textVariant:"buttonLarge",isDisabled:a.isLoading,onClick:()=>{a.setError(void 0),o(e)}},t)})}),t&&(0,i.tZ)(f.h,{boxElementDescriptor:n.descriptors.backRow,linkElementDescriptor:n.descriptors.backLink,onClick:t})]})})]}),(0,i.tZ)(v.Z.Footer,{children:(0,i.BX)(v.Z.Action,{elementId:"havingTrouble",children:[(0,i.tZ)(v.Z.ActionText,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.actionText")}),(0,i.tZ)(v.Z.ActionLink,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.actionLink"),onClick:r})]})})]})})};var L=r(70431),T=r(7389),I=r(24152),P=r(93234);let R=()=>{let{afterVerification:e}=(0,a.useUserVerification)(),t=(0,P.H)(),{setActive:r}=(0,w.cL)(),{setCache:i}=B(),{navigate:n}=(0,l.useRouter)();return{handleVerificationResponse:(0,o.useCallback)(async o=>{switch(i({data:o,isLoading:!1,isValidating:!1,error:null,cachedAt:Date.now()}),o.status){case"complete":return await r({session:o.session.id}),null==e?void 0:e();case"needs_second_factor":return n("./factor-two");default:return console.error((0,I.Ws)(o.status,t))}},[n,r,t])}};function X(e){let{onShowAlternativeMethodsClick:t}=e,{session:r}=(0,w.kP)(),{handleVerificationResponse:a}=R(),l=(0,c.useCardState)(),[s,d]=o.useState(!1),u=o.useCallback(()=>d(e=>!e),[d]),h=(0,Z.Yp)("password","",{type:"password",label:(0,n.localizationKeys)("formFieldLabel__password"),placeholder:(0,n.localizationKeys)("formFieldInputPlaceholder__password")}),p=async e=>(e.preventDefault(),null==r?void 0:r.attemptFirstFactorVerification({strategy:"password",password:h.value}).then(a).catch(e=>(0,Z.S3)(e,[h],l.setError)));return s?(0,i.tZ)(T.v,{onBackLinkClick:u}):(0,i.tZ)(n.Flow.Part,{part:"password",children:(0,i.BX)(v.Z.Root,{children:[(0,i.BX)(v.Z.Content,{children:[(0,i.BX)(y.h.Root,{showLogo:!0,children:[(0,i.tZ)(y.h.Title,{localizationKey:(0,n.localizationKeys)("reverification.password.title")}),(0,i.tZ)(y.h.Subtitle,{localizationKey:(0,n.localizationKeys)("reverification.password.subtitle")})]}),(0,i.tZ)(v.Z.Alert,{children:l.error}),(0,i.BX)(n.Col,{elementDescriptor:n.descriptors.main,gap:4,children:[(0,i.BX)(L.l.Root,{onSubmit:p,gap:8,children:[(0,i.tZ)(L.l.ControlRow,{elementId:h.id,children:(0,i.tZ)(L.l.PasswordInput,{...h.props,autoFocus:!0})}),(0,i.tZ)(L.l.SubmitButton,{hasArrow:!0})]}),(0,i.tZ)(v.Z.Action,{elementId:t?"alternativeMethods":"havingTrouble",children:(0,i.tZ)(v.Z.ActionLink,{localizationKey:(0,n.localizationKeys)(t?"reverification.password.actionLink":"reverification.alternativeMethods.actionLink"),onClick:t||u})})]})]}),(0,i.tZ)(v.Z.Footer,{})]})})}function E(e,t){return"primary"in e&&e.primary&&!("primary"in t&&t.primary)?-1:"primary"in t&&t.primary&&!("primary"in e&&e.primary)?1:0}var x=r(23465);let V=e=>{var t;let{session:r}=(0,w.kP)(),a=(0,c.useCardState)(),{handleVerificationResponse:n}=R();o.useEffect(()=>{e.factorAlreadyPrepared||l()},[]);let l=()=>{r.prepareFirstFactorVerification(e.factor).then(()=>e.onFactorPrepare()).catch(e=>(0,Z.S3)(e,[],a.setError))};return(0,i.tZ)(x.U,{cardTitle:e.cardTitle,cardSubtitle:e.cardSubtitle,inputLabel:e.inputLabel,resendButton:e.resendButton,onCodeEntryFinishedAction:(t,i,o)=>{r.attemptFirstFactorVerification({strategy:e.factor.strategy,code:t}).then(async e=>(await i(),n(e))).catch(o)},onResendCodeClicked:l,safeIdentifier:e.factor.safeIdentifier,profileImageUrl:null==r?void 0:null===(t=r.user)||void 0===t?void 0:t.imageUrl,onShowAlternativeMethodsClicked:e.onShowAlternativeMethodsClicked,showAlternativeMethods:e.showAlternativeMethods,onBackLinkClicked:e.onBackLinkClicked})},D=e=>(0,i.tZ)(n.Flow.Part,{part:"emailCode",children:(0,i.tZ)(V,{...e,cardTitle:(0,n.localizationKeys)("reverification.emailCode.title"),cardSubtitle:(0,n.localizationKeys)("reverification.emailCode.subtitle"),inputLabel:(0,n.localizationKeys)("reverification.emailCode.formTitle"),resendButton:(0,n.localizationKeys)("reverification.emailCode.resendButton")})}),N=e=>{let{onShowAlternativeMethodsClicked:t}=e,{session:r}=(0,w.kP)(),{__internal_isWebAuthnSupported:o}=(0,w.cL)(),{handleVerificationResponse:a}=R(),l=(0,c.useCardState)(),s=()=>{null==r||r.verifyWithPasskey().then(e=>a(e)).catch(e=>(0,Z.S3)(e,[],l.setError))};return(0,i.BX)(v.Z.Root,{children:[(0,i.BX)(v.Z.Content,{children:[(0,i.BX)(y.h.Root,{showLogo:!0,children:[(0,i.tZ)(y.h.Title,{localizationKey:(0,n.localizationKeys)("reverification.passkey.title")}),(0,i.tZ)(y.h.Subtitle,{localizationKey:(0,n.localizationKeys)("reverification.passkey.subtitle")})]}),(0,i.tZ)(v.Z.Alert,{children:l.error}),(0,i.tZ)(n.Col,{elementDescriptor:n.descriptors.main,gap:8,children:(0,i.tZ)(L.l.Root,{children:(0,i.BX)(n.Col,{gap:3,children:[(0,i.tZ)(n.Button,{type:"button",onClick:e=>{e.preventDefault(),s()},localizationKey:(0,n.localizationKeys)("reverification.passkey.blockButton__passkey"),hasArrow:!0}),(0,i.tZ)(v.Z.Action,{elementId:"alternativeMethods",children:t&&(0,i.tZ)(v.Z.ActionLink,{localizationKey:(0,n.localizationKeys)("footerActionLink__useAnotherMethod"),onClick:t})})]})})})]}),(0,i.tZ)(v.Z.Footer,{})]})},U=e=>(0,i.tZ)(n.Flow.Part,{part:"phoneCode",children:(0,i.tZ)(V,{...e,cardTitle:(0,n.localizationKeys)("reverification.phoneCode.title"),cardSubtitle:(0,n.localizationKeys)("reverification.phoneCode.subtitle"),inputLabel:(0,n.localizationKeys)("reverification.phoneCode.formTitle"),resendButton:(0,n.localizationKeys)("reverification.phoneCode.resendButton"),showAlternativeMethods:e.showAlternativeMethods})}),W=e=>{if(!e)return"";let t=e.strategy;return"emailAddressId"in e&&(t+=e.emailAddressId),"phoneNumberId"in e&&(t+=e.phoneNumberId),t},H=["password","email_code","phone_code","passkey"],j=S((0,c.withCardStateProvider)(function(){let{data:e}=B(),t=(0,c.useCardState)(),{navigate:r}=(0,l.useRouter)(),n=o.useRef(""),p=(0,o.useMemo)(()=>{var t,r;return(null===(r=e.supportedFirstFactors)||void 0===r?void 0:null===(t=r.filter(e=>H.includes(e.strategy)))||void 0===t?void 0:t.sort(E))||null},[e.supportedFirstFactors]),{preferredSignInStrategy:f}=(0,a.useEnvironment)().displayConfig,[{currentFactor:v},y]=o.useState(()=>({currentFactor:(0,h.t3)(p,null,f),prevCurrentFactor:void 0})),{hasAnyStrategy:k,hasFirstParty:Z}=g({filterOutFactor:v,supportedFirstFactors:p}),[m,C]=o.useState(()=>!v||!(0,h.xT)(v)),b=k?()=>{t.setError(void 0),C(e=>!e)}:void 0,w=()=>{n.current=W(v)},M=e=>{y(t=>({currentFactor:e,prevCurrentFactor:t.currentFactor}))};if((0,o.useEffect)(()=>{"needs_second_factor"===e.status&&r("factor-two")},[]),!v)return(0,i.tZ)(s._,{cardTitle:(0,u.u1)("reverification.noAvailableMethods.title"),cardSubtitle:(0,u.u1)("reverification.noAvailableMethods.subtitle"),message:(0,u.u1)("reverification.noAvailableMethods.message"),shouldNavigateBack:!1});if(m){let e=(0,h.xT)(v);return(0,i.tZ)(K,{onBackLinkClick:e?()=>{t.setError(void 0),null==b||b()}:void 0,onFactorSelected:e=>{M(e),null==b||b()},currentFactor:v})}switch(null==v?void 0:v.strategy){case"password":return(0,i.tZ)(X,{onShowAlternativeMethodsClick:b});case"email_code":return(0,i.tZ)(D,{factorAlreadyPrepared:n.current===W(v),onFactorPrepare:w,onShowAlternativeMethodsClicked:b,factor:v,showAlternativeMethods:Z});case"phone_code":return(0,i.tZ)(U,{factorAlreadyPrepared:n.current===W(v),onFactorPrepare:w,onShowAlternativeMethodsClicked:b,factor:v,showAlternativeMethods:Z});case"passkey":return(0,i.tZ)(N,{onShowAlternativeMethodsClicked:b});default:return(0,i.tZ)(d.W,{})}})),G=e=>{var t;let r=(0,c.useCardState)(),{session:a}=(0,w.kP)(),{handleVerificationResponse:n}=R();o.useEffect(()=>{!e.factorAlreadyPrepared&&(null==l||l())},[]);let l=e.prepare?()=>{var t;return null===(t=e.prepare)||void 0===t?void 0:t.call(e).then(()=>e.onFactorPrepare()).catch(e=>(0,Z.S3)(e,[],r.setError))}:void 0;return(0,i.tZ)(x.U,{cardTitle:e.cardTitle,cardSubtitle:e.cardSubtitle,resendButton:e.resendButton,inputLabel:e.inputLabel,onCodeEntryFinishedAction:(t,r,i)=>{a.attemptSecondFactorVerification({strategy:e.factor.strategy,code:t}).then(async e=>(await r(),n(e))).catch(i)},onResendCodeClicked:l,safeIdentifier:"safeIdentifier"in e.factor?e.factor.safeIdentifier:void 0,profileImageUrl:null==a?void 0:null===(t=a.user)||void 0===t?void 0:t.imageUrl,onShowAlternativeMethodsClicked:e.onShowAlternativeMethodsClicked,showAlternativeMethods:e.showAlternativeMethods})};function $(e){return(0,i.tZ)(n.Flow.Part,{part:"totp2Fa",children:(0,i.tZ)(G,{...e,cardTitle:(0,n.localizationKeys)("reverification.totpMfa.title"),cardSubtitle:(0,n.localizationKeys)("reverification.totpMfa.subtitle"),inputLabel:(0,n.localizationKeys)("reverification.totpMfa.formTitle"),showAlternativeMethods:e.showAlternativeMethods})})}let O=e=>{let[t,r]=o.useState(!1),a=o.useCallback(()=>r(e=>!e),[r]);return t?(0,i.tZ)(A,{onBackLinkClick:a}):(0,i.tZ)(Q,{supportedSecondFactors:e.supportedSecondFactors,onBackLinkClick:e.onBackLinkClick,onFactorSelected:e.onFactorSelected,onHavingTroubleClick:a})},Q=e=>{let{supportedSecondFactors:t,onHavingTroubleClick:r,onFactorSelected:o,onBackLinkClick:a}=e,l=(0,c.useCardState)();return(0,i.tZ)(n.Flow.Part,{part:"alternativeMethods",children:(0,i.BX)(v.Z.Root,{children:[(0,i.BX)(v.Z.Content,{children:[(0,i.BX)(y.h.Root,{showLogo:!0,children:[(0,i.tZ)(y.h.Title,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.title")}),(0,i.tZ)(y.h.Subtitle,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.subtitle")})]}),(0,i.tZ)(v.Z.Alert,{children:l.error}),(0,i.BX)(n.Col,{elementDescriptor:n.descriptors.main,gap:3,children:[(0,i.tZ)(n.Col,{gap:2,children:null==t?void 0:t.sort(Z.Q0).map((e,t)=>(0,i.tZ)(p.$,{textLocalizationKey:function(e){switch(e.strategy){case"phone_code":return(0,n.localizationKeys)("reverification.alternativeMethods.blockButton__phoneCode",{identifier:(0,Z.HT)(e.safeIdentifier)||""});case"totp":return(0,n.localizationKeys)("reverification.alternativeMethods.blockButton__totp");case"backup_code":return(0,n.localizationKeys)("reverification.alternativeMethods.blockButton__backupCode");default:throw Error('Invalid verification strategy: "'.concat(e.strategy,'"'))}}(e),elementDescriptor:n.descriptors.alternativeMethodsBlockButton,textElementDescriptor:n.descriptors.alternativeMethodsBlockButtonText,arrowElementDescriptor:n.descriptors.alternativeMethodsBlockButtonArrow,isDisabled:l.isLoading,onClick:()=>o(e)},t))}),(0,i.tZ)(v.Z.Action,{elementId:"alternativeMethods",children:a&&(0,i.tZ)(v.Z.ActionLink,{localizationKey:(0,n.localizationKeys)("backButton"),onClick:e.onBackLinkClick})})]})]}),(0,i.tZ)(v.Z.Footer,{children:(0,i.BX)(v.Z.Action,{elementId:"havingTrouble",children:[(0,i.tZ)(v.Z.ActionText,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.actionText")}),(0,i.tZ)(v.Z.ActionLink,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.actionLink"),onClick:r})]})})]})})},Y=e=>{let{onShowAlternativeMethodsClicked:t}=e,{session:r}=(0,w.kP)(),{handleVerificationResponse:o}=R(),a=(0,c.useCardState)(),l=(0,Z.Yp)("code","",{type:"text",label:(0,n.localizationKeys)("formFieldLabel__backupCode"),isRequired:!0});return(0,i.BX)(v.Z.Root,{children:[(0,i.BX)(v.Z.Content,{children:[(0,i.BX)(y.h.Root,{showLogo:!0,children:[(0,i.tZ)(y.h.Title,{localizationKey:(0,n.localizationKeys)("reverification.backupCodeMfa.title")}),(0,i.tZ)(y.h.Subtitle,{localizationKey:(0,n.localizationKeys)("reverification.backupCodeMfa.subtitle")})]}),(0,i.tZ)(v.Z.Alert,{children:a.error}),(0,i.tZ)(n.Col,{elementDescriptor:n.descriptors.main,gap:8,children:(0,i.BX)(L.l.Root,{onSubmit:e=>(e.preventDefault(),r.attemptSecondFactorVerification({strategy:"backup_code",code:l.value}).then(o).catch(e=>(0,Z.S3)(e,[l],a.setError))),children:[(0,i.tZ)(L.l.ControlRow,{elementId:l.id,children:(0,i.tZ)(L.l.PlainInput,{...l.props,autoFocus:!0,onActionClicked:t})}),(0,i.BX)(n.Col,{gap:3,children:[(0,i.tZ)(L.l.SubmitButton,{hasArrow:!0}),(0,i.tZ)(v.Z.Action,{elementId:"alternativeMethods",children:t&&(0,i.tZ)(v.Z.ActionLink,{localizationKey:(0,n.localizationKeys)("footerActionLink__useAnotherMethod"),onClick:t})})]})]})})]}),(0,i.tZ)(v.Z.Footer,{})]})},q=e=>{let{session:t}=(0,w.kP)();return(0,i.tZ)(n.Flow.Part,{part:"phoneCode2Fa",children:(0,i.tZ)(G,{...e,cardTitle:(0,n.localizationKeys)("reverification.phoneCodeMfa.title"),cardSubtitle:(0,n.localizationKeys)("reverification.phoneCodeMfa.subtitle"),inputLabel:(0,n.localizationKeys)("reverification.phoneCodeMfa.formTitle"),resendButton:(0,n.localizationKeys)("reverification.phoneCodeMfa.resendButton"),prepare:()=>{let{phoneNumberId:r,strategy:i}=e.factor;return t.prepareSecondFactorVerification({phoneNumberId:r,strategy:i})}})})},J=e=>{if(!e)return"";let t=e.strategy;return"phoneNumberId"in e&&(t+=e.phoneNumberId),t},ee=["phone_code","totp","backup_code"],et=S((0,c.withCardStateProvider)(function(){let{navigate:e}=(0,l.useRouter)(),{data:t}=B(),r=(0,o.useMemo)(()=>{var e,r;return(null===(r=t.supportedSecondFactors)||void 0===r?void 0:null===(e=r.filter(e=>ee.includes(e.strategy)))||void 0===e?void 0:e.sort(E))||null},[t.supportedSecondFactors]),a=o.useRef(""),[n,c]=o.useState(()=>(0,h.bx)(r)),[s,u]=o.useState(!n),p=()=>u(e=>!e),f=(0,o.useMemo)(()=>null==r?void 0:r.filter(e=>!b(e,n)),[r,n]),v=()=>{a.current=J(n)},y=(0,o.useMemo)(()=>f&&f.length>0||!1,[f]);if((0,o.useEffect)(()=>{"needs_first_factor"===t.status&&e("../")},[]),!n)return(0,i.tZ)(d.W,{});if(s&&y)return(0,i.tZ)(O,{supportedSecondFactors:f||null,onBackLinkClick:p,onFactorSelected:e=>{c(e),p()}});switch(null==n?void 0:n.strategy){case"phone_code":return(0,i.tZ)(q,{factorAlreadyPrepared:a.current===J(n),onFactorPrepare:v,factor:n,onShowAlternativeMethodsClicked:p,showAlternativeMethods:y});case"totp":return(0,i.tZ)($,{factorAlreadyPrepared:a.current===J(n),onFactorPrepare:v,factor:n,onShowAlternativeMethodsClicked:p,showAlternativeMethods:y});case"backup_code":return(0,i.tZ)(Y,{onShowAlternativeMethodsClicked:p});default:return(0,i.tZ)(d.W,{})}}));function er(){let{invalidate:e}=B();return(0,o.useEffect)(()=>()=>{e()},[]),(0,i.tZ)(n.Flow.Root,{flow:"userVerification",children:(0,i.BX)(l.Switch,{children:[(0,i.tZ)(l.Route,{path:"factor-two",children:(0,i.tZ)(et,{})}),(0,i.tZ)(l.Route,{index:!0,children:(0,i.tZ)(j,{})})]})})}er.displayName="UserVerification";let ei=(0,a.withCoreSessionSwitchGuard)(er),eo=e=>(0,i.tZ)(l.Route,{path:"user-verification",children:(0,i.tZ)(a.UserVerificationContext.Provider,{value:{componentName:"UserVerification",...e,routing:"virtual"},children:(0,i.tZ)("div",{children:(0,i.tZ)(ei,{...e,routing:"virtual"})})})})}}]);