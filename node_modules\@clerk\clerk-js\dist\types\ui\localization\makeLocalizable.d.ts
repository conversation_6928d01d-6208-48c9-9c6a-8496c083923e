import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>Error } from '@clerk/types';
import React from 'react';
import type { LocalizationKey } from './localizationKeys';
type Localizable<T> = T & {
    localizationKey?: LocalizationKey | string;
};
type LocalizablePrimitive<T> = React.FunctionComponent<Localizable<T>>;
export declare const makeLocalizable: <P>(Component: React.FunctionComponent<P>) => LocalizablePrimitive<P>;
export declare const useLocalizations: () => {
    t: (localizationKey: LocalizationKey | string | undefined) => string;
    translateError: (error: ClerkRuntimeError | ClerkAPIError | string | undefined) => string;
    locale: string;
};
export {};
