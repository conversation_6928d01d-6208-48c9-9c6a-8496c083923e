import React from 'react';
declare const Button: React.ForwardRefExoticComponent<Omit<React.ClassAttributes<HTMLButtonElement> & React.ButtonHTMLAttributes<HTMLButtonElement> & {
    css?: import("../styledSystem").ThemableCssProp;
} & {
    isLoading?: boolean;
    loadingText?: string;
    isDisabled?: boolean;
    isActive?: boolean;
    hoverAsFocus?: boolean;
    hasArrow?: boolean;
} & {
    textVariant?: "body" | "caption" | "h1" | "h2" | "h3" | "subtitle" | "buttonLarge" | "buttonSmall" | undefined;
    size?: "sm" | "md" | "xs" | "iconLg" | undefined;
    colorScheme?: "primary" | "secondary" | "danger" | "neutral" | undefined;
    variant?: "link" | "solid" | "outline" | "bordered" | "ghost" | "linkDanger" | "unstyled" | "roundWrapper" | undefined;
    block?: boolean | undefined;
    focusRing?: boolean | undefined;
}, "ref"> & React.RefAttributes<HTMLButtonElement>>;
declare const SimpleButton: React.ForwardRefExoticComponent<Omit<React.ClassAttributes<HTMLButtonElement> & React.ButtonHTMLAttributes<HTMLButtonElement> & {
    css?: import("../styledSystem").ThemableCssProp;
} & {
    isLoading?: boolean;
    loadingText?: string;
    isDisabled?: boolean;
    isActive?: boolean;
    hoverAsFocus?: boolean;
    hasArrow?: boolean;
} & {
    textVariant?: "body" | "caption" | "h1" | "h2" | "h3" | "subtitle" | "buttonLarge" | "buttonSmall" | undefined;
    size?: "sm" | "md" | "xs" | "iconLg" | undefined;
    colorScheme?: "primary" | "secondary" | "danger" | "neutral" | undefined;
    variant?: "link" | "solid" | "outline" | "bordered" | "ghost" | "linkDanger" | "unstyled" | "roundWrapper" | undefined;
    block?: boolean | undefined;
    focusRing?: boolean | undefined;
}, "ref"> & React.RefAttributes<HTMLButtonElement>>;
export { Button, SimpleButton };
