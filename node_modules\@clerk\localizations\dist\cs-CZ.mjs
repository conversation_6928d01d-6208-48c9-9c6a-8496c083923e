// src/cs-CZ.ts
var csCZ = {
  locale: "cs-CZ",
  backButton: "Zp\u011Bt",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "V\xFDchoz\xED",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Jin\xE9 za\u0159\xEDzen\xED p\u0159edstavitele",
  badge__primary: "Hlavn\xED",
  badge__renewsAt: void 0,
  badge__requiresAction: "Vy\u017Eaduje akci",
  badge__startsAt: void 0,
  badge__thisDevice: "Toto za\u0159\xEDzen\xED",
  badge__unverified: "Nepotvrzen\xE9",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Za\u0159\xEDzen\xED u\u017Eivatele",
  badge__you: "Vy",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Vytvo\u0159it organizaci",
    invitePage: {
      formButtonReset: "P\u0159esko\u010Dit"
    },
    title: "Vytvo\u0159it organizaci"
  },
  dates: {
    lastDay: "V\u010Dera v {{ date | timeString('cs-CZ') }}",
    next6Days: "P\u0159\xED\u0161t\xED  {{ date | weekday('cs-CZ','long') }} v {{ date | timeString('cs-CZ') }}",
    nextDay: "Z\xEDtra v {{ date | timeString('cs-CZ') }}",
    numeric: "{{ date | numeric('cs-CZ') }}",
    previous6Days: "Minul\xFD {{ date | weekday('cs-CZ','long') }} v {{ date | timeString('cs-CZ') }}",
    sameDay: "Dnes v  {{ date | timeString('cs-CZ') }}"
  },
  dividerText: "nebo",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Pou\u017E\xEDt jinou metodu",
  footerPageLink__help: "N\xE1pov\u011Bda",
  footerPageLink__privacy: "Ochrana soukrom\xED",
  footerPageLink__terms: "Podm\xEDnky",
  formButtonPrimary: "Pokra\u010Dovat",
  formButtonPrimary__verify: "Verify",
  formFieldAction__forgotPassword: "Zapomn\u011Bli jste heslo?",
  formFieldError__matchingPasswords: "Hesla se shoduj\xED.",
  formFieldError__notMatchingPasswords: "Hesla se neshoduj\xED.",
  formFieldError__verificationLinkExpired: "Odkaz pro ov\u011B\u0159en\xED ji\u017E expiroval. Pros\xEDm, po\u017E\xE1dejte o nov\xFD.",
  formFieldHintText__optional: "Voliteln\xE9",
  formFieldHintText__slug: "Slug je \u010Dlov\u011Bkem \u010Diteln\xFD identifik\xE1tor, kter\xFD mus\xED b\xFDt unik\xE1tn\xED. \u010Casto pou\u017Eit v URL adres\xE1ch.",
  formFieldInputPlaceholder__backupCode: "Zadejte z\xE1lo\u017En\xED k\xF3d",
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Potvr\u010Fte smaz\xE1n\xED \xFA\u010Dtu",
  formFieldInputPlaceholder__emailAddress: "Zadejte va\u0161i emailovou adresu",
  formFieldInputPlaceholder__emailAddress_username: "Zadejte sv\u016Fj email nebo u\u017Eivatelsk\xE9 jm\xE9no",
  formFieldInputPlaceholder__emailAddresses: "Zadejte nebo vlo\u017Ete jednu nebo v\xEDce emailov\xFDch adres odd\u011Blen\xFDch mezerou nebo \u010D\xE1rkou",
  formFieldInputPlaceholder__firstName: "Zadejte sv\xE9 jm\xE9no",
  formFieldInputPlaceholder__lastName: "Zadejte sv\xE9 p\u0159\xEDjmen\xED",
  formFieldInputPlaceholder__organizationDomain: "Zadejte dom\xE9nu va\u0161\xED organizace",
  formFieldInputPlaceholder__organizationDomainEmailAddress: "Zadejte emailovou adresu va\u0161\xED organizace",
  formFieldInputPlaceholder__organizationName: "Zadejte n\xE1zev va\u0161\xED organizace",
  formFieldInputPlaceholder__organizationSlug: "Zadejte slug pro va\u0161i organizaci",
  formFieldInputPlaceholder__password: "Zadejte sv\xE9 heslo",
  formFieldInputPlaceholder__phoneNumber: "Zadejte sv\xE9 telefonn\xED \u010D\xEDslo",
  formFieldInputPlaceholder__username: "Zadejte sv\xE9 u\u017Eivatelsk\xE9 jm\xE9no",
  formFieldLabel__automaticInvitations: "Enable automatic invitations for this domain",
  formFieldLabel__backupCode: "Z\xE1lo\u017En\xED k\xF3d",
  formFieldLabel__confirmDeletion: "Potvrzen\xED",
  formFieldLabel__confirmPassword: "Potvrdit heslo",
  formFieldLabel__currentPassword: "St\xE1vaj\xEDc\xED heslo",
  formFieldLabel__emailAddress: "Emailov\xE1 adresa",
  formFieldLabel__emailAddress_username: "Emailov\xE1 adresa nebo u\u017Eivatelsk\xE9 jm\xE9no",
  formFieldLabel__emailAddresses: "Emailov\xE9 adresy",
  formFieldLabel__firstName: "Jm\xE9no",
  formFieldLabel__lastName: "P\u0159\xEDjmen\xED",
  formFieldLabel__newPassword: "Nov\xE9 heslo",
  formFieldLabel__organizationDomain: "Domain",
  formFieldLabel__organizationDomainDeletePending: "Delete pending invitations and suggestions",
  formFieldLabel__organizationDomainEmailAddress: "Verification email address",
  formFieldLabel__organizationDomainEmailAddressDescription: "Enter an email address under this domain to receive a code and verify this domain.",
  formFieldLabel__organizationName: "N\xE1zev organizace",
  formFieldLabel__organizationSlug: "URL adresa",
  formFieldLabel__passkeyName: "N\xE1zev kl\xED\u010De",
  formFieldLabel__password: "Heslo",
  formFieldLabel__phoneNumber: "Telefonn\xED \u010D\xEDslo",
  formFieldLabel__role: "Role",
  formFieldLabel__signOutOfOtherSessions: "Odhl\xE1sit se ze v\u0161ech ostatn\xEDch za\u0159\xEDzen\xED",
  formFieldLabel__username: "U\u017Eivatelsk\xE9 jm\xE9no",
  impersonationFab: {
    action__signOut: "Odhl\xE1sit se",
    title: "P\u0159ihl\xE1\u0161en(a) jako {{identifier}}"
  },
  maintenanceMode: "\xDAdr\u017Eba",
  membershipRole__admin: "Spr\xE1vce",
  membershipRole__basicMember: "\u010Clen",
  membershipRole__guestMember: "Host",
  organizationList: {
    action__createOrganization: "Create organization",
    action__invitationAccept: "Join",
    action__suggestionsAccept: "Request to join",
    createOrganization: "Create Organization",
    invitationAcceptedLabel: "Joined",
    subtitle: "to continue to {{applicationName}}",
    suggestionsAcceptedLabel: "Pending approval",
    title: "Choose an account",
    titleWithoutPersonal: "Choose an organization"
  },
  organizationProfile: {
    badge__automaticInvitation: "Automatic invitations",
    badge__automaticSuggestion: "Automatic suggestions",
    badge__manualInvitation: "No automatic enrollment",
    badge__unverified: "Unverified",
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Add the domain to verify. Users with email addresses at this domain can join the organization automatically or request to join.",
      title: "Add domain"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Pozv\xE1nky se nepoda\u0159ilo odeslat. Opravte n\xE1sleduj\xEDc\xED a zkuste to znovu:",
      formButtonPrimary__continue: "Odeslat pozv\xE1nky",
      selectDropdown__role: "Select role",
      subtitle: "Pozvat nov\xE9 \u010Dleny do t\xE9to organizace",
      successMessage: "Pozv\xE1nky byly \xFAsp\u011B\u0161n\u011B odesl\xE1ny",
      title: "Pozvat \u010Dleny"
    },
    membersPage: {
      action__invite: "Pozvat",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "Odstranit \u010Dlena",
        tableHeader__actions: "Akce",
        tableHeader__joined: "P\u0159ipojil se",
        tableHeader__role: "Role",
        tableHeader__user: "U\u017Eivatel"
      },
      detailsTitle__emptyRow: "\u017D\xE1dn\xED \u010Dlenov\xE9 k zobrazen\xED",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Invite users by connecting an email domain with your organization. Anyone who signs up with a matching email domain will be able to join the organization anytime.",
          headerTitle: "Automatic invitations",
          primaryButton: "Manage verified domains"
        },
        table__emptyRow: "No invitations to display"
      },
      invitedMembersTab: {
        menuAction__revoke: "Zru\u0161it pozv\xE1n\xED",
        tableHeader__invited: "Pozv\xE1ni"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Users who sign up with a matching email domain, will be able to see a suggestion to request to join your organization.",
          headerTitle: "Automatic suggestions",
          primaryButton: "Manage verified domains"
        },
        menuAction__approve: "Approve",
        menuAction__reject: "Reject",
        tableHeader__requested: "Requested access",
        table__emptyRow: "No requests to display"
      },
      start: {
        headerTitle__invitations: "Invitations",
        headerTitle__members: "Members",
        headerTitle__requests: "Requests"
      }
    },
    navbar: {
      billing: void 0,
      description: "Manage your organization.",
      general: "General",
      members: "Members",
      title: "Organization"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Type "{{organizationName}}" below to continue.',
          messageLine1: "Are you sure you want to delete this organization?",
          messageLine2: "This action is permanent and irreversible.",
          successMessage: "You have deleted the organization.",
          title: "Delete organization"
        },
        leaveOrganization: {
          actionDescription: 'Type "{{organizationName}}" below to continue.',
          messageLine1: "Opravdu chcete opustit tuto organizaci? Ztrat\xEDte p\u0159\xEDstup k t\xE9to organizaci a jej\xEDm aplikac\xEDm.",
          messageLine2: "Tato akce je trval\xE1 a nevratn\xE1.",
          successMessage: "Opustili jste organizaci.",
          title: "Opustit organizaci"
        },
        title: "Upozorn\u011Bn\xED"
      },
      domainSection: {
        menuAction__manage: "Manage",
        menuAction__remove: "Delete",
        menuAction__verify: "Verify",
        primaryButton: "Add domain",
        subtitle: "Allow users to join the organization automatically or request to join based on a verified email domain.",
        title: "Verified domains"
      },
      successMessage: "Organizace byla aktualizov\xE1na.",
      title: "Profil organizace"
    },
    removeDomainPage: {
      messageLine1: "The email domain {{domain}} will be removed.",
      messageLine2: "Users won\u2019t be able to join the organization automatically after this.",
      successMessage: "{{domain}} has been removed.",
      title: "Remove domain"
    },
    start: {
      headerTitle__general: "General",
      headerTitle__members: "\u010Clenov\xE9",
      profileSection: {
        primaryButton: "Pokra\u010Dovat",
        title: "Organization Profile",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Removing this domain will affect invited users.",
        removeDomainActionLabel__remove: "Remove domain",
        removeDomainSubtitle: "Remove this domain from your verified domains",
        removeDomainTitle: "Remove domain"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Users are automatically invited to join the organization when they sign-up and can join anytime.",
        automaticInvitationOption__label: "Automatic invitations",
        automaticSuggestionOption__description: "Users receive a suggestion to request to join, but must be approved by an admin before they are able to join the organization.",
        automaticSuggestionOption__label: "Automatic suggestions",
        calloutInfoLabel: "Changing the enrollment mode will only affect new users.",
        calloutInvitationCountLabel: "Pending invitations sent to users: {{count}}",
        calloutSuggestionCountLabel: "Pending suggestions sent to users: {{count}}",
        manualInvitationOption__description: "Users can only be invited manually to the organization.",
        manualInvitationOption__label: "No automatic enrollment",
        subtitle: "Choose how users from this domain can join the organization."
      },
      start: {
        headerTitle__danger: "Danger",
        headerTitle__enrollment: "Enrollment options"
      },
      subtitle: "The domain {{domain}} is now verified. Continue by selecting enrollment mode.",
      title: "Update {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Enter the verification code sent to your email address",
      formTitle: "Verification code",
      resendButton: "Didn't receive a code? Resend",
      subtitle: "The domain {{domainName}} needs to be verified via email.",
      subtitleVerificationCodeScreen: "A verification code was sent to {{emailAddress}}. Enter the code to continue.",
      title: "Verify domain"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Vytvo\u0159it organizaci",
    action__invitationAccept: "Join",
    action__manageOrganization: "Spravovat organizaci",
    action__suggestionsAccept: "Request to join",
    notSelected: "Nen\xED vybr\xE1na \u017E\xE1dn\xE1 organizace",
    personalWorkspace: "Osobn\xED pracovn\xED prostor",
    suggestionsAcceptedLabel: "Pending approval"
  },
  paginationButton__next: "Dal\u0161\xED",
  paginationButton__previous: "P\u0159edchoz\xED",
  paginationRowText__displaying: "Zobrazuje se",
  paginationRowText__of: "z",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Add account",
      action__signOutAll: "Sign out of all accounts",
      subtitle: "Select the account with which you wish to continue.",
      title: "Choose an account"
    },
    alternativeMethods: {
      actionLink: "Z\xEDskat pomoc",
      actionText: "Don\u2019t have any of these?",
      blockButton__backupCode: "Pou\u017E\xEDt z\xE1lo\u017En\xED k\xF3d",
      blockButton__emailCode: "Odeslat ov\u011B\u0159ovac\xED k\xF3d na email {{identifier}}",
      blockButton__emailLink: "Odeslat odkaz na email {{identifier}}",
      blockButton__passkey: "Pou\u017E\xEDt Passkey",
      blockButton__password: "P\u0159ihl\xE1sit se pomoc\xED hesla",
      blockButton__phoneCode: "Poslat SMS k\xF3d na telefonn\xED \u010D\xEDslo {{identifier}}",
      blockButton__totp: "Pou\u017E\xEDt autentiza\u010Dn\xED aplikaci",
      getHelp: {
        blockButton__emailSupport: "Podpora p\u0159es email",
        content: "Pokud m\xE1te pot\xED\u017Ee s p\u0159ihl\xE1\u0161en\xEDm do sv\xE9ho \xFA\u010Dtu, kontaktujte n\xE1s emailem a pokus\xEDme se v\xE1m co nejd\u0159\xEDve obnovit p\u0159\xEDstup.",
        title: "Z\xEDskat pomoc"
      },
      subtitle: "Facing issues? You can use any of these methods to sign in.",
      title: "Pou\u017E\xEDt jinou metodu"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      title: "Zadejte z\xE1lo\u017En\xED k\xF3d"
    },
    emailCode: {
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      resendButton: "Znovu poslat k\xF3d",
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      title: "Zkontrolujte sv\u016Fj email"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Zkontrolujte, \u017Ee pou\u017E\xEDv\xE1te spr\xE1vn\xE9ho klienta.",
        title: "Chyba klienta"
      },
      expired: {
        subtitle: "Vra\u0165te se do p\u016Fvodn\xEDho okna pro pokra\u010Dov\xE1n\xED.",
        title: "Tento ov\u011B\u0159ovac\xED odkaz vypr\u0161el"
      },
      failed: {
        subtitle: "Vra\u0165te se do p\u016Fvodn\xEDho okna pro pokra\u010Dov\xE1n\xED.",
        title: "Tento ov\u011B\u0159ovac\xED odkaz je neplatn\xFD"
      },
      formSubtitle: "Pou\u017Eijte ov\u011B\u0159ovac\xED odkaz zaslan\xFD na v\xE1\u0161 email",
      formTitle: "Ov\u011B\u0159ovac\xED odkaz",
      loading: {
        subtitle: "Brzy budete p\u0159esm\u011Brov\xE1ni",
        title: "P\u0159ihla\u0161uji..."
      },
      resendButton: "Znovu poslat odkaz",
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      title: "Zkontrolujte sv\u016Fj email",
      unusedTab: {
        title: "M\u016F\u017Eete zav\u0159\xEDt toto okno"
      },
      verified: {
        subtitle: "Brzy budete p\u0159esm\u011Brov\xE1ni",
        title: "\xDAsp\u011B\u0161n\u011B p\u0159ihl\xE1\u0161eno"
      },
      verifiedSwitchTab: {
        subtitle: "Vra\u0165te se do p\u016Fvodn\xEDho okna pro pokra\u010Dov\xE1n\xED",
        subtitleNewTab: "Vra\u0165te se do nov\u011B otev\u0159en\xE9ho okna pro pokra\u010Dov\xE1n\xED",
        titleNewTab: "P\u0159ihl\xE1\u0161eno v jin\xE9m okn\u011B"
      }
    },
    forgotPassword: {
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d pro obnoven\xED hesla",
      resendButton: "Znovu poslat k\xF3d",
      subtitle: "to reset your password",
      subtitle_email: "First, enter the code sent to your email ID",
      subtitle_phone: "First, enter the code sent to your phone",
      title: "Reset password"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Obnovit heslo",
      label__alternativeMethods: "Nebo se p\u0159ihlaste pomoc\xED jin\xE9 metody.",
      title: "Zapomn\u011Bli jste heslo?"
    },
    noAvailableMethods: {
      message: "Nelze pokra\u010Dovat v p\u0159ihl\xE1\u0161en\xED. Nen\xED k dispozici \u017E\xE1dn\xE1 dostupn\xE1 autentifika\u010Dn\xED metoda.",
      subtitle: "Do\u0161lo k chyb\u011B",
      title: "Nelze se p\u0159ihl\xE1sit"
    },
    passkey: {
      subtitle: "Pou\u017Eijte v\xE1\u0161 Passkey pro ov\u011B\u0159en\xED.",
      title: "Ov\u011B\u0159en\xED pomoc\xED Passkey"
    },
    password: {
      actionLink: "Pou\u017E\xEDt jinou metodu",
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      title: "Zadejte sv\xE9 heslo"
    },
    passwordPwned: {
      title: "Zadejte sv\xE9 heslo"
    },
    phoneCode: {
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      resendButton: "Znovu odeslat k\xF3d",
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      title: "Zkontrolujte sv\u016Fj telefon"
    },
    phoneCodeMfa: {
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      resendButton: "Znovu odeslat k\xF3d",
      subtitle: "Zadejte ov\u011B\u0159ovac\xED k\xF3d z aplikace pro dvoufaktorov\xE9 ov\u011B\u0159en\xED.",
      title: "Zkontrolujte sv\u016Fj telefon"
    },
    resetPassword: {
      formButtonPrimary: "Obnovit heslo",
      requiredMessage: "For security reasons, it is required to reset your password.",
      successMessage: "Va\u0161e heslo bylo \xFAsp\u011B\u0161n\u011B zm\u011Bn\u011Bno. P\u0159ihla\u0161uji v\xE1s, pros\xEDm po\u010Dkejte okam\u017Eik.",
      title: "Obnovit heslo"
    },
    resetPasswordMfa: {
      detailsLabel: "P\u0159ed obnoven\xEDm hesla je t\u0159eba ov\u011B\u0159it va\u0161i toto\u017Enost."
    },
    start: {
      actionLink: "Registrovat se",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "Pou\u017E\xEDt email",
      actionLink__use_email_username: "Pou\u017E\xEDt email nebo u\u017Eivatelsk\xE9 jm\xE9no",
      actionLink__use_passkey: "Pou\u017E\xEDt Passkey",
      actionLink__use_phone: "Pou\u017E\xEDt telefon",
      actionLink__use_username: "Pou\u017E\xEDt u\u017Eivatelsk\xE9 jm\xE9no",
      actionText: "Nem\xE1te \xFA\u010Det?",
      actionText__join_waitlist: "P\u0159ipojit se k \u010Dekac\xED listin\u011B",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      subtitleCombined: void 0,
      title: "P\u0159ihl\xE1sit se",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      subtitle: "Zadejte ov\u011B\u0159ovac\xED k\xF3d z aplikace pro dvoufaktorov\xE9 ov\u011B\u0159en\xED.",
      title: "Dvoufaktorov\xE9 ov\u011B\u0159en\xED"
    }
  },
  signInEnterPasswordTitle: "Zadejte sv\xE9 heslo",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "P\u0159ihl\xE1sit se",
      actionText: "M\xE1te \xFA\u010Det?",
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      title: "Vypl\u0148te chyb\u011Bj\xEDc\xED pole"
    },
    emailCode: {
      formSubtitle: "Zadejte ov\u011B\u0159ovac\xED k\xF3d poslan\xFD na va\u0161i emailovou adresu",
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      resendButton: "Znovu poslat k\xF3d",
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      title: "Ov\u011B\u0159te sv\u016Fj email"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Zkontrolujte, \u017Ee pou\u017E\xEDv\xE1te spr\xE1vn\xE9ho klienta.",
        title: "Chyba klienta"
      },
      formSubtitle: "Pou\u017Eijte ov\u011B\u0159ovac\xED odkaz poslan\xFD na va\u0161i emailovou adresu",
      formTitle: "Ov\u011B\u0159ovac\xED odkaz",
      loading: {
        title: "Prob\xEDh\xE1 registrace..."
      },
      resendButton: "Znovu poslat odkaz",
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      title: "Ov\u011B\u0159te sv\u016Fj email",
      verified: {
        title: "\xDAsp\u011B\u0161n\u011B zaregistrov\xE1no"
      },
      verifiedSwitchTab: {
        subtitle: "Vra\u0165te se do nov\u011B otev\u0159en\xE9ho okna pro pokra\u010Dov\xE1n\xED",
        subtitleNewTab: "Vra\u0165te se do p\u0159edchoz\xEDho okna pro pokra\u010Dov\xE1n\xED",
        title: "Email \xFAsp\u011B\u0161n\u011B ov\u011B\u0159en"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: "Souhlas\xEDm s politikou ochrany osobn\xEDch \xFAdaj\u016F",
        label__onlyTermsOfService: "Souhlas\xEDm s podm\xEDnkami slu\u017Eby",
        label__termsOfServiceAndPrivacyPolicy: "Souhlas\xEDm s podm\xEDnkami slu\u017Eby a politikou ochrany osobn\xEDch \xFAdaj\u016F"
      },
      continue: {
        subtitle: "Pokra\u010Dujte pro dokon\u010Den\xED registrace.",
        title: "Pokra\u010Dovat"
      }
    },
    phoneCode: {
      formSubtitle: "Zadejte ov\u011B\u0159ovac\xED k\xF3d poslan\xFD na va\u0161e telefonn\xED \u010D\xEDslo",
      formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
      resendButton: "Znovu poslat k\xF3d",
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      title: "Ov\u011B\u0159te sv\u016Fj telefon"
    },
    restrictedAccess: {
      actionLink: "Po\u017E\xE1dat o p\u0159\xEDstup",
      actionText: "Nem\xE1te p\u0159\xEDstup k t\xE9to oblasti.",
      blockButton__emailSupport: "Kontaktujte podporu",
      blockButton__joinWaitlist: "P\u0159ipojit se k \u010Dekac\xED listin\u011B",
      subtitle: "Tento obsah nen\xED k dispozici.",
      subtitleWaitlist: "P\u0159ipojte se k \u010Dekac\xED listin\u011B pro p\u0159\xEDstup.",
      title: "Omezen\xFD p\u0159\xEDstup"
    },
    start: {
      actionLink: "P\u0159ihl\xE1sit se",
      actionLink__use_email: void 0,
      actionLink__use_phone: "Pou\u017E\xEDt telefon",
      actionText: "M\xE1te \xFA\u010Det?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      subtitleCombined: "pro pokra\u010Dov\xE1n\xED do {{applicationName}}",
      title: "Vytvo\u0159te si \xFA\u010Det",
      titleCombined: "Vytvo\u0159te si \xFA\u010Det"
    }
  },
  socialButtonsBlockButton: "Pokra\u010Dovat s {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: "Ji\u017E jste \u010Dlenem organizace.",
    captcha_invalid: "Registrace ne\xFAsp\u011B\u0161n\xE1 kv\u016Fli ne\xFAsp\u011B\u0161n\xFDm bezpe\u010Dnostn\xEDm validac\xEDm. Pros\xEDm, obnovte str\xE1nku a zkuste to znovu nebo kontaktujte podporu.",
    captcha_unavailable: "Registrace ne\xFAsp\u011B\u0161n\xE1 kv\u016Fli selh\xE1n\xED ov\u011B\u0159en\xED botu. Pros\xEDm, obnovte str\xE1nku a zkuste to znovu nebo kontaktujte podporu.",
    form_code_incorrect: "K\xF3d je nespr\xE1vn\xFD.",
    form_identifier_exists__email_address: "Tato emailov\xE1 adresa ji\u017E byla pou\u017Eita.",
    form_identifier_exists__phone_number: "Toto telefonn\xED \u010D\xEDslo ji\u017E bylo pou\u017Eito.",
    form_identifier_exists__username: "Toto u\u017Eivatelsk\xE9 jm\xE9no ji\u017E bylo pou\u017Eito.",
    form_identifier_not_found: "Nebyl nalezen \xFA\u010Det s t\u011Bmihle detaily.",
    form_param_format_invalid: "Form\xE1t parametru je neplatn\xFD.",
    form_param_format_invalid__email_address: "Emailov\xE1 adresa mus\xED b\xFDt platn\xE1.",
    form_param_format_invalid__phone_number: "Telefonn\xED \u010D\xEDslo mus\xED b\xFDt ve validn\xEDm mezin\xE1rodn\xEDm form\xE1tu.",
    form_param_max_length_exceeded__first_name: "Jm\xE9no nesm\xED p\u0159es\xE1hnout 256 znak\u016F.",
    form_param_max_length_exceeded__last_name: "P\u0159\xEDjmen\xED nesm\xED p\u0159es\xE1hnout 256 znak\u016F.",
    form_param_max_length_exceeded__name: "Jm\xE9no nesm\xED p\u0159es\xE1hnout 256 znak\u016F.",
    form_param_nil: "Tento parametr je povinn\xFD.",
    form_param_value_invalid: "Tento parametr m\xE1 neplatnou hodnotu.",
    form_password_incorrect: "Heslo je nespr\xE1vn\xE9.",
    form_password_length_too_short: "Heslo je p\u0159\xEDli\u0161 kr\xE1tk\xE9.",
    form_password_not_strong_enough: "Va\u0161e heslo nen\xED dostate\u010Dn\u011B siln\xE9.",
    form_password_pwned: "Toto heslo bylo nalezeno v \xFAniku dat a nem\u016F\u017Ee b\xFDt pou\u017Eito. Zvolte pros\xEDm jin\xE9 heslo.",
    form_password_pwned__sign_in: "Zvolte siln\xE9 heslo pro p\u0159ihl\xE1\u0161en\xED.",
    form_password_size_in_bytes_exceeded: "Va\u0161e heslo p\u0159ekro\u010Dilo maxim\xE1ln\xED povolen\xFD po\u010Det byt\u016F, pros\xEDm zkr\xE1tit ho nebo odstranit n\u011Bkter\xE9 speci\xE1ln\xED znaky.",
    form_password_validation_failed: "Nespr\xE1vn\xE9 heslo",
    form_username_invalid_character: void 0,
    form_username_invalid_length: void 0,
    identification_deletion_failed: "You cannot delete your last identification.",
    not_allowed_access: "E-mailov\xE1 adresa nebo telefonn\xED \u010D\xEDslo nen\xED povoleno k registraci. M\u016F\u017Ee to b\xFDt zp\u016Fsobeno pou\u017Eit\xEDm '+', '=', '#' nebo '.' ve va\u0161\xED e-mailov\xE9 adrese, pomoc\xED dom\xE9ny propojen\xE9 s do\u010Dasnou e-mailovou slu\u017Ebou nebo jsou explicitn\u011B blokov\xE1ny. Pokud se domn\xEDv\xE1te, \u017Ee se jedn\xE1 o chybu, kontaktujte podporu.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: void 0,
    passkey_not_supported: void 0,
    passkey_pa_not_supported: void 0,
    passkey_registration_cancelled: void 0,
    passkey_retrieval_cancelled: void 0,
    passwordComplexity: {
      maximumLength: "m\xE9n\u011B ne\u017E {{length}} znak\u016F",
      minimumLength: "{{length}} nebo v\xEDce znak\u016F",
      requireLowercase: "mal\xE9 p\xEDsmeno",
      requireNumbers: "\u010D\xEDslici",
      requireSpecialCharacter: "speci\xE1ln\xED znak",
      requireUppercase: "velk\xE9 p\xEDsmeno",
      sentencePrefix: "Va\u0161e heslo mus\xED obsahovat"
    },
    phone_number_exists: "Toto telefonn\xED \u010D\xEDslo se pou\u017E\xEDv\xE1. Zkuste pros\xEDm jin\xFD.",
    session_exists: "Jste ji\u017E p\u0159ihl\xE1\u0161en.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "Va\u0161e heslo funguje, ale mohlo by b\xFDt siln\u011Bj\u0161\xED. Zkuste p\u0159idat v\xEDce znak\u016F.",
      goodPassword: "Dobr\xE1 pr\xE1ce. Toto je vynikaj\xEDc\xED heslo.",
      notEnough: "Va\u0161e heslo nen\xED dostate\u010Dn\u011B siln\xE9.",
      suggestions: {
        allUppercase: "Pou\u017Eijte velk\xE1 p\xEDsmena pouze u n\u011Bkter\xFDch, ne v\u0161ech p\xEDsmen.",
        anotherWord: "P\u0159idejte v\xEDce slov, kter\xE1 nejsou tak b\u011B\u017En\xE1.",
        associatedYears: "Vyhn\u011Bte se letopo\u010Dt\u016Fm, kter\xE9 jsou s v\xE1mi spojen\xE9.",
        capitalization: "P\xEDsmena pi\u0161te s velk\xFDm po\u010D\xE1te\u010Dn\xEDm p\xEDsmenem a v\xEDce ne\u017E jen prvn\xED p\xEDsmeno.",
        dates: "Vyhn\u011Bte se dat\u016Fm a letopo\u010Dt\u016Fm, kter\xE9 jsou s v\xE1mi spojen\xE9.",
        l33t: "Vyhn\u011Bte se p\u0159edv\xEDdateln\xFDm n\xE1hrad\xE1m p\xEDsmen, nap\u0159\xEDklad '@' m\xEDsto 'a'.",
        longerKeyboardPattern: "Pou\u017Eijte del\u0161\xED vzory na kl\xE1vesnici a m\u011B\u0148te sm\u011Br psan\xED v\xEDcekr\xE1t.",
        noNeed: "M\u016F\u017Eete vytv\xE1\u0159et siln\xE1 hesla i bez pou\u017Eit\xED symbol\u016F, \u010D\xEDsel nebo velk\xFDch p\xEDsmen.",
        pwned: "Pokud pou\u017E\xEDv\xE1te toto heslo i jinde, m\u011Bli byste ho zm\u011Bnit.",
        recentYears: "Vyhn\u011Bte se ned\xE1vn\xFDm rok\u016Fm.",
        repeated: "Vyhn\u011Bte se opakuj\xEDc\xEDm se slov\u016Fm a znak\u016Fm.",
        reverseWords: "Vyhn\u011Bte se obr\xE1cen\xFDm pravopis\u016Fm b\u011B\u017En\xFDch slov.",
        sequences: "Vyhn\u011Bte se b\u011B\u017En\xFDm sekvenc\xEDm znak\u016F.",
        useWords: "Pou\u017Eijte v\xEDce slov, ale vyhn\u011Bte se b\u011B\u017En\xFDm fr\xE1z\xEDm."
      },
      warnings: {
        common: "Toto je b\u011B\u017En\u011B pou\u017E\xEDvan\xE9 heslo.",
        commonNames: "B\u011B\u017En\xE1 jm\xE9na a p\u0159\xEDjmen\xED jsou snadno uh\xE1dnuteln\xE1.",
        dates: "Datum je snadno uh\xE1dnuteln\xE9.",
        extendedRepeat: 'Opakuj\xEDc\xED se vzory znak\u016F jako "abcabcabc" jsou snadno uh\xE1dnuteln\xE9.',
        keyPattern: "Kr\xE1tk\xE9 vzory na kl\xE1vesnici jsou snadno uh\xE1dnuteln\xE9.",
        namesByThemselves: "Samostatn\xE1 jm\xE9na nebo p\u0159\xEDjmen\xED jsou snadno uh\xE1dnuteln\xE1.",
        pwned: "Va\u0161e heslo bylo odhaleno p\u0159i \xFAniku dat na internetu.",
        recentYears: "Ned\xE1vn\xE9 roky jsou snadno uh\xE1dnuteln\xE9.",
        sequences: 'B\u011B\u017En\xE9 sekvence znak\u016F jako "abc" jsou snadno uh\xE1dnuteln\xE9.',
        similarToCommon: "Toto je podobn\xE9 b\u011B\u017En\u011B pou\u017E\xEDvan\xE9mu heslu.",
        simpleRepeat: 'Opakuj\xEDc\xED se znaky jako "aaa" jsou snadno uh\xE1dnuteln\xE9.',
        straightRow: "\u0158ady kl\xE1ves na kl\xE1vesnici jsou snadno uh\xE1dnuteln\xE9.",
        topHundred: "Toto je \u010Dasto pou\u017E\xEDvan\xE9 heslo.",
        topTen: "Toto je \u010Dasto pou\u017E\xEDvan\xE9 heslo.",
        userInputs: "Heslo by nem\u011Blo obsahovat osobn\xED nebo str\xE1nkou souvisej\xEDc\xED \xFAdaje.",
        wordByItself: "Samostatn\xE1 slova jsou snadno uh\xE1dnuteln\xE1."
      }
    }
  },
  userButton: {
    action__addAccount: "P\u0159idat \xFA\u010Det",
    action__manageAccount: "Spravovat \xFA\u010Det",
    action__signOut: "Odhl\xE1sit se",
    action__signOutAll: "Odhl\xE1sit se ze v\u0161ech \xFA\u010Dt\u016F"
  },
  userProfile: {
    backupCodePage: {
      actionLabel__copied: "Zkop\xEDrov\xE1no!",
      actionLabel__copy: "Zkop\xEDrovat v\u0161e",
      actionLabel__download: "St\xE1hnout .txt",
      actionLabel__print: "Vytisknout",
      infoText1: "Pro tento \xFA\u010Det budou povoleny z\xE1lo\u017En\xED k\xF3dy.",
      infoText2: "Z\xE1lo\u017En\xED k\xF3dy uchov\xE1vejte tajn\u011B a bezpe\u010Dn\u011B. M\u016F\u017Eete vygenerovat nov\xE9 z\xE1lo\u017En\xED k\xF3dy, pokud m\xE1te podez\u0159en\xED, \u017Ee byly kompromitov\xE1ny.",
      subtitle__codelist: "Uchov\xE1vejte je bezpe\u010Dn\u011B a tajn\u011B.",
      successMessage: "Z\xE1lo\u017En\xED k\xF3dy jsou nyn\xED povoleny. Pokud ztrat\xEDte p\u0159\xEDstup k va\u0161emu ov\u011B\u0159ovac\xEDmu za\u0159\xEDzen\xED, m\u016F\u017Eete pou\u017E\xEDt jeden z t\u011Bchto k\xF3d\u016F k p\u0159ihl\xE1\u0161en\xED do sv\xE9ho \xFA\u010Dtu. Ka\u017Ed\xFD k\xF3d lze pou\u017E\xEDt pouze jednou.",
      successSubtitle: "Pou\u017Eijte jeden z t\u011Bchto k\xF3d\u016F k p\u0159ihl\xE1\u0161en\xED do sv\xE9ho \xFA\u010Dtu, pokud ztrat\xEDte p\u0159\xEDstup k va\u0161emu ov\u011B\u0159ovac\xEDmu za\u0159\xEDzen\xED.",
      title: "P\u0159idat ov\u011B\u0159ov\xE1n\xED pomoc\xED z\xE1lo\u017En\xEDch k\xF3d\u016F",
      title__codelist: "Z\xE1lo\u017En\xED k\xF3dy"
    },
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "Vyberte poskytovatele pro p\u0159ipojen\xED va\u0161eho \xFA\u010Dtu.",
      formHint__noAccounts: "Nejsou k dispozici \u017E\xE1dn\xED dostupn\xED extern\xED poskytovatel\xE9 \xFA\u010Dt\u016F.",
      removeResource: {
        messageLine1: "{{identifier}} bude odebr\xE1n z tohoto \xFA\u010Dtu.",
        messageLine2: "Nebudete ji\u017E moci pou\u017E\xEDvat tento p\u0159ipojen\xFD \xFA\u010Det a jak\xE9koli z\xE1visl\xE9 funkce p\u0159estanou fungovat.",
        successMessage: "{{connectedAccount}} byl odebr\xE1n z va\u0161eho \xFA\u010Dtu.",
        title: "Odstranit p\u0159ipojen\xFD \xFA\u010Det"
      },
      socialButtonsBlockButton: "P\u0159ipojit \xFA\u010Det {{provider|titleize}}",
      successMessage: "Poskytovatel byl p\u0159id\xE1n k va\u0161emu \xFA\u010Dtu.",
      title: "P\u0159idat p\u0159ipojen\xFD \xFA\u010Det"
    },
    deletePage: {
      actionDescription: 'Type "Delete account" below to continue.',
      confirm: "Delete account",
      messageLine1: "Are you sure you want to delete your account?",
      messageLine2: "This action is permanent and irreversible.",
      title: "Delete account"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "Na tuto e-mailovou adresu bude odesl\xE1n ov\u011B\u0159ovac\xED k\xF3d.",
        formSubtitle: "Zadejte ov\u011B\u0159ovac\xED k\xF3d zaslan\xFD na adresu {{identifier}}",
        formTitle: "Ov\u011B\u0159ovac\xED k\xF3d",
        resendButton: "Znovu odeslat k\xF3d",
        successMessage: "E-mailov\xE1 adresa {{identifier}} byla p\u0159id\xE1na k va\u0161emu \xFA\u010Dtu."
      },
      emailLink: {
        formHint: "Na tuto e-mailovou adresu bude odesl\xE1n ov\u011B\u0159ovac\xED odkaz.",
        formSubtitle: "Klikn\u011Bte na ov\u011B\u0159ovac\xED odkaz v e-mailu zaslan\xE9m na adresu {{identifier}}",
        formTitle: "Ov\u011B\u0159ovac\xED odkaz",
        resendButton: "Znovu odeslat odkaz",
        successMessage: "E-mailov\xE1 adresa {{identifier}} byla p\u0159id\xE1na k va\u0161emu \xFA\u010Dtu."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} bude odstran\u011Bna z tohoto \xFA\u010Dtu.",
        messageLine2: "Nebudete se moci p\u0159ihl\xE1sit pomoc\xED t\xE9to e-mailov\xE9 adresy.",
        successMessage: "{{emailAddress}} byla odebr\xE1na z va\u0161eho \xFA\u010Dtu.",
        title: "Odstranit e-mailovou adresu"
      },
      title: "P\u0159idat e-mailovou adresu",
      verifyTitle: "Verify email address"
    },
    formButtonPrimary__add: "P\u0159idat",
    formButtonPrimary__continue: "Pokra\u010Dovat",
    formButtonPrimary__finish: "Dokon\u010Dit",
    formButtonPrimary__remove: "Odstranit",
    formButtonPrimary__save: "Ulo\u017Eit",
    formButtonReset: "Zru\u0161it",
    mfaPage: {
      formHint: "Vyberte zp\u016Fsob p\u0159id\xE1n\xED.",
      title: "P\u0159idat dvoufaktorov\xE9 ov\u011B\u0159ov\xE1n\xED"
    },
    mfaPhoneCodePage: {
      backButton: "Use existing number",
      primaryButton__addPhoneNumber: "P\u0159idat telefonn\xED \u010D\xEDslo",
      removeResource: {
        messageLine1: "{{identifier}} ji\u017E nebude dost\xE1vat ov\u011B\u0159ovac\xED k\xF3dy p\u0159i p\u0159ihla\u0161ov\xE1n\xED.",
        messageLine2: "V\xE1\u0161 \xFA\u010Det nemus\xED b\xFDt tak bezpe\u010Dn\xFD. Opravdu chcete pokra\u010Dovat?",
        successMessage: "Dvoufaktorov\xE9 ov\u011B\u0159ov\xE1n\xED pomoc\xED SMS k\xF3du bylo odebr\xE1no pro {{mfaPhoneCode}}",
        title: "Odstranit dvoufaktorov\xE9 ov\u011B\u0159ov\xE1n\xED"
      },
      subtitle__availablePhoneNumbers: "Vyberte telefonn\xED \u010D\xEDslo pro registraci dvoufaktorov\xE9ho ov\u011B\u0159ov\xE1n\xED pomoc\xED SMS k\xF3du.",
      subtitle__unavailablePhoneNumbers: "Nejsou k dispozici \u017E\xE1dn\xE1 dostupn\xE1 telefonn\xED \u010D\xEDsla pro registraci dvoufaktorov\xE9ho ov\u011B\u0159ov\xE1n\xED pomoc\xED SMS k\xF3du.",
      successMessage1: "When signing in, you will need to enter a verification code sent to this phone number as an additional step.",
      successMessage2: "Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.",
      successTitle: "SMS code verification enabled",
      title: "P\u0159idat ov\u011B\u0159ov\xE1n\xED pomoc\xED SMS k\xF3du"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Nam\xEDsto toho naskenujte QR k\xF3d",
        buttonUnableToScan__nonPrimary: "Nem\u016F\u017Eete naskenovat QR k\xF3d?",
        infoText__ableToScan: "Nastavte novou metodu p\u0159ihl\xE1\u0161en\xED ve va\u0161\xED aplikaci pro ov\u011B\u0159ov\xE1n\xED a naskenujte n\xE1sleduj\xEDc\xED QR k\xF3d, abyste jej propojili se sv\xFDm \xFA\u010Dtem.",
        infoText__unableToScan: "Nastavte novou metodu p\u0159ihl\xE1\u0161en\xED ve sv\xE9 aplikaci pro ov\u011B\u0159ov\xE1n\xED a zadejte n\xED\u017Ee poskytnut\xFD kl\xED\u010D.",
        inputLabel__unableToScan1: "Ujist\u011Bte se, \u017Ee je povoleno \u010Dasov\u011B z\xE1visl\xE9 nebo jednor\xE1zov\xE9 heslo, a dokon\u010Dete propojen\xED va\u0161eho \xFA\u010Dtu.",
        inputLabel__unableToScan2: "Alternativn\u011B, pokud va\u0161e aplikace pro ov\u011B\u0159ov\xE1n\xED podporuje TOTP URI, m\u016F\u017Eete tak\xE9 zkop\xEDrovat cel\xFD URI."
      },
      removeResource: {
        messageLine1: "P\u0159i p\u0159ihla\u0161ov\xE1n\xED ji\u017E nebudou vy\u017Eadov\xE1ny ov\u011B\u0159ovac\xED k\xF3dy z t\xE9to aplikace pro ov\u011B\u0159ov\xE1n\xED.",
        messageLine2: "V\xE1\u0161 \xFA\u010Det nemus\xED b\xFDt tak bezpe\u010Dn\xFD. Opravdu chcete pokra\u010Dovat?",
        successMessage: "Dvoufaktorov\xE9 ov\u011B\u0159ov\xE1n\xED pomoc\xED aplikace pro ov\u011B\u0159ov\xE1n\xED bylo odebr\xE1no.",
        title: "Odstranit dvoufaktorov\xE9 ov\u011B\u0159ov\xE1n\xED"
      },
      successMessage: "Dvoufaktorov\xE9 ov\u011B\u0159ov\xE1n\xED je nyn\xED povoleno. P\u0159i p\u0159ihl\xE1\u0161en\xED budete muset zadat ov\u011B\u0159ovac\xED k\xF3d z t\xE9to aplikace pro ov\u011B\u0159ov\xE1n\xED jako dal\u0161\xED krok.",
      title: "P\u0159idat aplikaci pro ov\u011B\u0159ov\xE1n\xED",
      verifySubtitle: "Zadejte ov\u011B\u0159ovac\xED k\xF3d generovan\xFD va\u0161\xED aplikac\xED pro ov\u011B\u0159ov\xE1n\xED",
      verifyTitle: "Ov\u011B\u0159ovac\xED k\xF3d"
    },
    mobileButton__menu: "Menu",
    navbar: {
      account: "Profil",
      billing: void 0,
      description: "Spravujte sv\xE9 \xFAdaje.",
      security: "Zabezpe\u010Den\xED",
      title: "\xDA\u010Det"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: void 0,
        title: void 0
      },
      subtitle__rename: void 0,
      title__rename: void 0
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Je doporu\u010Deno se odhl\xE1sit ze v\u0161ech ostatn\xEDch za\u0159\xEDzen\xEDch, kter\xE1 mohla pou\u017E\xEDt va\u0161e star\xE9 heslo.",
      readonly: "Your password can currently not be edited because you can sign in only via the enterprise connection.",
      successMessage__set: "Va\u0161e heslo bylo nastaveno.",
      successMessage__signOutOfOtherSessions: "V\u0161echna ostatn\xED za\u0159\xEDzen\xED byla odhl\xE1\u0161ena.",
      successMessage__update: "Va\u0161e heslo bylo aktualizov\xE1no.",
      title__set: "Nastavit heslo",
      title__update: "Zm\u011Bnit heslo"
    },
    phoneNumberPage: {
      infoText: "Na toto telefonn\xED \u010D\xEDslo bude odesl\xE1na textov\xE1 zpr\xE1va obsahuj\xEDc\xED ov\u011B\u0159ovac\xED odkaz.",
      removeResource: {
        messageLine1: "{{identifier}} bude odebr\xE1no z tohoto \xFA\u010Dtu.",
        messageLine2: "Nebudete ji\u017E moci p\u0159ihl\xE1sit se pomoc\xED tohoto telefonn\xEDho \u010D\xEDsla.",
        successMessage: "{{phoneNumber}} bylo odebr\xE1no z va\u0161eho \xFA\u010Dtu.",
        title: "Odstranit telefonn\xED \u010D\xEDslo"
      },
      successMessage: "{{identifier}} bylo p\u0159id\xE1no k va\u0161emu \xFA\u010Dtu.",
      title: "P\u0159idat telefonn\xED \u010D\xEDslo",
      verifySubtitle: "Enter the verification code sent to {{identifier}}",
      verifyTitle: "Verify phone number"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "Nahrajte obr\xE1zek ve form\xE1tech JPG, PNG, GIF nebo WEBP s velikost\xED men\u0161\xED ne\u017E 10 MB",
      imageFormDestructiveActionSubtitle: "Odstranit obr\xE1zek",
      imageFormSubtitle: "Nahr\xE1t obr\xE1zek",
      imageFormTitle: "Profilov\xFD obr\xE1zek",
      readonly: "Your profile information has been provided by the enterprise connection and cannot be edited.",
      successMessage: "V\xE1\u0161 profil byl aktualizov\xE1n.",
      title: "Aktualizovat profil"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Odhl\xE1sit se z za\u0159\xEDzen\xED",
        title: "Aktivn\xED za\u0159\xEDzen\xED"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Zkusit znovu",
        actionLabel__reauthorize: "Autorizovat nyn\xED",
        destructiveActionTitle: "Odstranit",
        primaryButton: "P\u0159ipojit \xFA\u010Det",
        subtitle__disconnected: void 0,
        subtitle__reauthorize: "The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues",
        title: "P\u0159ipojen\xE9 \xFA\u010Dty"
      },
      dangerSection: {
        deleteAccountButton: "Smazat \xFA\u010Det",
        title: "Zru\u0161en\xED \xFA\u010Dtu"
      },
      emailAddressesSection: {
        destructiveAction: "Odstranit emailovou adresu",
        detailsAction__nonPrimary: "Nastavit jako hlavn\xED",
        detailsAction__primary: "Dokon\u010Dit ov\u011B\u0159en\xED",
        detailsAction__unverified: "Dokon\u010Dit ov\u011B\u0159en\xED",
        primaryButton: "P\u0159idat emailovou adresu",
        title: "Emailov\xE9 adresy"
      },
      enterpriseAccountsSection: {
        title: "Enterprise accounts"
      },
      headerTitle__account: "\xDA\u010Det",
      headerTitle__security: "Bezpe\u010Dnost",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Obnovit k\xF3dy",
          headerTitle: "Z\xE1lo\u017En\xED k\xF3dy",
          subtitle__regenerate: "Z\xEDskejte novou sadu zabezpe\u010Den\xFDch z\xE1lo\u017En\xEDch k\xF3d\u016F. P\u0159edchoz\xED z\xE1lo\u017En\xED k\xF3dy budou smaz\xE1ny a nelze je pou\u017E\xEDt.",
          title__regenerate: "Obnovit z\xE1lo\u017En\xED k\xF3dy"
        },
        phoneCode: {
          actionLabel__setDefault: "Nastavit jako v\xFDchoz\xED",
          destructiveActionLabel: "Odstranit telefonn\xED \u010D\xEDslo"
        },
        primaryButton: "P\u0159idat dvoufaktorov\xE9 ov\u011B\u0159en\xED",
        title: "Dvoufaktorov\xE9 ov\u011B\u0159en\xED",
        totp: {
          destructiveActionTitle: "Odstranit",
          headerTitle: "Aplikace Authenticator"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Odstranit",
        menuAction__rename: "P\u0159ejmenovat",
        primaryButton: void 0,
        title: "Spr\xE1va kl\xED\u010D\u016F"
      },
      passwordSection: {
        primaryButton__setPassword: "Nastavit heslo",
        primaryButton__updatePassword: "Zm\u011Bnit heslo",
        title: "Heslo"
      },
      phoneNumbersSection: {
        destructiveAction: "Odstranit telefonn\xED \u010D\xEDslo",
        detailsAction__nonPrimary: "Nastavit jako hlavn\xED",
        detailsAction__primary: "Dokon\u010Dit ov\u011B\u0159en\xED",
        detailsAction__unverified: "Dokon\u010Dit ov\u011B\u0159en\xED",
        primaryButton: "P\u0159idat telefonn\xED \u010D\xEDslo",
        title: "Telefonn\xED \u010D\xEDsla"
      },
      profileSection: {
        primaryButton: void 0,
        title: "Profil"
      },
      usernameSection: {
        primaryButton__setUsername: "Nastavit u\u017Eivatelsk\xE9 jm\xE9no",
        primaryButton__updateUsername: "Zm\u011Bnit u\u017Eivatelsk\xE9 jm\xE9no",
        title: "U\u017Eivatelsk\xE9 jm\xE9no"
      },
      web3WalletsSection: {
        destructiveAction: "Odstranit pen\u011B\u017Eenku",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Web3 pen\u011B\u017Eenky",
        title: "Web3 pen\u011B\u017Eenky"
      }
    },
    usernamePage: {
      successMessage: "Va\u0161e u\u017Eivatelsk\xE9 jm\xE9no bylo aktualizov\xE1no.",
      title__set: "Aktualizovat u\u017Eivatelsk\xE9 jm\xE9no",
      title__update: "Aktualizovat u\u017Eivatelsk\xE9 jm\xE9no"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} bude odebr\xE1na z tohoto \xFA\u010Dtu.",
        messageLine2: "Nebudete se ji\u017E moci p\u0159ihl\xE1sit pomoc\xED t\xE9to web3 pen\u011B\u017Eenky.",
        successMessage: "{{web3Wallet}} byla odebr\xE1na z va\u0161eho \xFA\u010Dtu.",
        title: "Odstranit web3 pen\u011B\u017Eenku"
      },
      subtitle__availableWallets: "Vyberte web3 pen\u011B\u017Eenku k p\u0159ipojen\xED k va\u0161emu \xFA\u010Dtu.",
      subtitle__unavailableWallets: "Nejsou k dispozici \u017E\xE1dn\xE9 dostupn\xE9 web3 pen\u011B\u017Eenky.",
      successMessage: "Pen\u011B\u017Eenka byla p\u0159id\xE1na k va\u0161emu \xFA\u010Dtu.",
      title: "P\u0159idat web3 pen\u011B\u017Eenku",
      web3WalletButtonsBlockButton: "Pokra\u010Dovat s Web3 pen\u011B\u017Eenkou"
    }
  },
  waitlist: {
    start: {
      actionLink: "P\u0159ihl\xE1sit se na \u010Dekac\xED listinu",
      actionText: "Chcete-li b\xFDt informov\xE1ni, p\u0159ihlaste se na \u010Dekac\xED listinu.",
      formButton: "P\u0159ihl\xE1sit se",
      subtitle: "P\u0159idejte se na \u010Dekac\xED listinu pro p\u0159\xEDstup k {{applicationName}}.",
      title: "P\u0159ihl\xE1\u0161en\xED na \u010Dekac\xED listinu"
    },
    success: {
      message: "Byli jste \xFAsp\u011B\u0161n\u011B p\u0159id\xE1ni na \u010Dekac\xED listinu!",
      subtitle: "Po\u010Dkejte na dal\u0161\xED instrukce, kter\xE9 v\xE1m po\u0161leme na email.",
      title: "\xDAsp\u011Bch"
    }
  }
};
export {
  csCZ
};
//# sourceMappingURL=cs-CZ.mjs.map