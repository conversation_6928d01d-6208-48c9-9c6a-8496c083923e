// src/pl-PL.ts
var plPL = {
  locale: "pl-PL",
  backButton: "Powr\xF3t",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Domy\u015Blny",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Inne urz\u0105dzenie osobiste",
  badge__primary: "Podstawowy",
  badge__renewsAt: void 0,
  badge__requiresAction: "Wymaga dzia\u0142ania",
  badge__startsAt: void 0,
  badge__thisDevice: "To urz\u0105dzenie",
  badge__unverified: "Niezweryfikowany",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Urz\u0105dzenie u\u017Cytkownika",
  badge__you: "Ty",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Utw\xF3rz organizacj\u0119",
    invitePage: {
      formButtonReset: "Pomi\u0144"
    },
    title: "Utw\xF3rz organizacj\u0119"
  },
  dates: {
    lastDay: "Wczoraj o godzinie {{ date | timeString('pl-PL') }}",
    next6Days: "{{ date | weekday('pl-PL','long') }} o godzinie {{ date | timeString('pl-PL') }}",
    nextDay: "Jutro o godzinie {{ date | timeString('pl-PL') }}",
    numeric: "{{ date | numeric('pl-PL') }}",
    previous6Days: "Ostatni(a) {{ date | weekday('pl-PL','long') }} o godzinie {{ date | timeString('pl-PL') }}",
    sameDay: "Dzisiaj o godzinie {{ date | timeString('pl-PL') }}"
  },
  dividerText: "lub",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "U\u017Cyj innej metody",
  footerPageLink__help: "Pomoc",
  footerPageLink__privacy: "Prywatno\u015B\u0107",
  footerPageLink__terms: "Warunki",
  formButtonPrimary: "Kontynuuj",
  formButtonPrimary__verify: "Zweryfikuj",
  formFieldAction__forgotPassword: "Zapomnia\u0142em/am has\u0142a",
  formFieldError__matchingPasswords: "Has\u0142a si\u0119 zgadzaj\u0105.",
  formFieldError__notMatchingPasswords: "Has\u0142a si\u0119 nie zgadzaj\u0105.",
  formFieldError__verificationLinkExpired: "Link weryfikacyjny wygas\u0142. Spr\xF3buj ponownie.",
  formFieldHintText__optional: "Opcjonalne",
  formFieldHintText__slug: "Jest to unikalne ID, kt\xF3re jest czytelne dla cz\u0142owieka, cz\u0119sto u\u017Cywane w adresach URL.",
  formFieldInputPlaceholder__backupCode: "Wprowad\u017A kod zapasowy",
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Usu\u0144 konto",
  formFieldInputPlaceholder__emailAddress: "Wprowad\u017A adres email",
  formFieldInputPlaceholder__emailAddress_username: "Adres e-mail lub nazwa u\u017Cytkownika",
  formFieldInputPlaceholder__emailAddresses: "Wprowad\u017A lub wklej jeden lub wi\u0119cej adres\xF3w e-mail, oddzielonych spacjami lub przecinkami",
  formFieldInputPlaceholder__firstName: "Imi\u0119",
  formFieldInputPlaceholder__lastName: "Nazwisko",
  formFieldInputPlaceholder__organizationDomain: "example.com",
  formFieldInputPlaceholder__organizationDomainEmailAddress: "<EMAIL>",
  formFieldInputPlaceholder__organizationName: "Nazwa organizacji",
  formFieldInputPlaceholder__organizationSlug: "moja-organizacja",
  formFieldInputPlaceholder__password: "Wprowad\u017A swoje has\u0142o",
  formFieldInputPlaceholder__phoneNumber: "Wprowad\u017A numer telefonu",
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__automaticInvitations: "W\u0142\u0105cz automatyczne zaproszenia dla tej domeny",
  formFieldLabel__backupCode: "Kod zapasowy",
  formFieldLabel__confirmDeletion: "Potwierdzenie",
  formFieldLabel__confirmPassword: "Potwierd\u017A has\u0142o",
  formFieldLabel__currentPassword: "Obecne has\u0142o",
  formFieldLabel__emailAddress: "Adres e-mail",
  formFieldLabel__emailAddress_username: "Adres e-mail lub nazwa u\u017Cytkownika",
  formFieldLabel__emailAddresses: "Adresy e-mail",
  formFieldLabel__firstName: "Imi\u0119",
  formFieldLabel__lastName: "Nazwisko",
  formFieldLabel__newPassword: "Nowe has\u0142o",
  formFieldLabel__organizationDomain: "Domena",
  formFieldLabel__organizationDomainDeletePending: "Usu\u0144 oczekuj\u0105ce zaproszenia i propozycje",
  formFieldLabel__organizationDomainEmailAddress: "Weryfikacyjny adres e-mail",
  formFieldLabel__organizationDomainEmailAddressDescription: "Wprowad\u017A adres e-mail w tej domenie, aby otrzyma\u0107 kod i zweryfikowa\u0107 t\u0119 domen\u0119.",
  formFieldLabel__organizationName: "Nazwa organizacji",
  formFieldLabel__organizationSlug: "Slug URL",
  formFieldLabel__passkeyName: "Nazwa klucza dost\u0119pu",
  formFieldLabel__password: "Has\u0142o",
  formFieldLabel__phoneNumber: "Numer telefonu",
  formFieldLabel__role: "Rola",
  formFieldLabel__signOutOfOtherSessions: "Wyloguj si\u0119 ze wszystkich innych urz\u0105dze\u0144",
  formFieldLabel__username: "Nazwa u\u017Cytkownika",
  impersonationFab: {
    action__signOut: "Wyloguj",
    title: "Zalogowano jako {{identifier}}"
  },
  maintenanceMode: "Aktualnie trwaj\u0105 prace konserwacyjne, ale nie powinno to zaj\u0105\u0107 d\u0142u\u017Cej ni\u017C kilka minut.",
  membershipRole__admin: "Administrator",
  membershipRole__basicMember: "U\u017Cytkownik",
  membershipRole__guestMember: "Go\u015B\u0107",
  organizationList: {
    action__createOrganization: "Stw\xF3rz organizacj\u0119",
    action__invitationAccept: "Do\u0142\u0105cz",
    action__suggestionsAccept: "Popro\u015B o do\u0142\u0105czenie",
    createOrganization: "Stw\xF3rz organizacj\u0119",
    invitationAcceptedLabel: "Do\u0142\u0105czono",
    subtitle: "to continue to {{applicationName}}",
    suggestionsAcceptedLabel: "Pending approval",
    title: "Wybierz konto",
    titleWithoutPersonal: "Wybierz organizacj\u0119"
  },
  organizationProfile: {
    badge__automaticInvitation: "Automatyczne zaproszenia",
    badge__automaticSuggestion: "Automatyczne sugestie",
    badge__manualInvitation: "Brak automatycznej rejestracji",
    badge__unverified: "Niezweryfikowany",
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Dodaj domen\u0119 do weryfikacji. U\u017Cytkownicy z adresami e-mail w tej domenie mog\u0105 do\u0142\u0105czy\u0107 do organizacji automatycznie lub poprosi\u0107 o do\u0142\u0105czenie.",
      title: "Dodaj domen\u0119"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Nie uda\u0142o si\u0119 wys\u0142a\u0107 zaprosze\u0144. Napraw poni\u017Csze problemy i spr\xF3buj ponownie:",
      formButtonPrimary__continue: "Wy\u015Blij zaproszenia",
      selectDropdown__role: "Wybierz rol\u0119",
      subtitle: "Zapro\u015B nowych u\u017Cytkownik\xF3w do tej organizacji",
      successMessage: "Zaproszenia zosta\u0142y pomy\u015Blnie wys\u0142ane",
      title: "Zapro\u015B u\u017Cytkownik\xF3w"
    },
    membersPage: {
      action__invite: "Zapro\u015B",
      action__search: "Wyszukaj",
      activeMembersTab: {
        menuAction__remove: "Usu\u0144 u\u017Cytkownika",
        tableHeader__actions: "Akcje",
        tableHeader__joined: "Do\u0142\u0105czy\u0142",
        tableHeader__role: "Rola",
        tableHeader__user: "U\u017Cytkownik"
      },
      detailsTitle__emptyRow: "Brak u\u017Cytkownik\xF3w do wy\u015Bwietlenia",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Zapro\u015B u\u017Cytkownik\xF3w, \u0142\u0105cz\u0105c domen\u0119 e-mail ze swoj\u0105 organizacj\u0105. Ka\u017Cdy, kto zarejestruje si\u0119 za pomoc\u0105 pasuj\u0105cej domeny e-mail, b\u0119dzie m\xF3g\u0142 do\u0142\u0105czy\u0107 do organizacji w dowolnym momencie.",
          headerTitle: "Automatyczne zaproszenia",
          primaryButton: "Zarz\u0105dzanie zweryfikowanymi domenami"
        },
        table__emptyRow: "Brak zaprosze\u0144 do wy\u015Bwietlenia"
      },
      invitedMembersTab: {
        menuAction__revoke: "Anuluj zaproszenie",
        tableHeader__invited: "Zaproszony"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "U\u017Cytkownicy, kt\xF3rzy zarejestruj\u0105 si\u0119 za pomoc\u0105 pasuj\u0105cej domeny e-mail, b\u0119d\u0105 mogli zobaczy\u0107 propozycj\u0119, aby poprosi\u0107 o do\u0142\u0105czenie do Twojej organizacji.",
          headerTitle: "Automatyczne propozycje",
          primaryButton: "Zarz\u0105dzanie zweryfikowanymi domenami"
        },
        menuAction__approve: "Zatwierd\u017A",
        menuAction__reject: "Odrzu\u0107",
        tableHeader__requested: "Pro\u015Bby o dost\u0119p",
        table__emptyRow: "Brak pr\xF3\u015Bb do wy\u015Bwietlenia"
      },
      start: {
        headerTitle__invitations: "Zaproszenia",
        headerTitle__members: "Cz\u0142onkowie",
        headerTitle__requests: "Pro\u015Bby"
      }
    },
    navbar: {
      billing: void 0,
      description: "Zarz\u0105dzaj organizacj\u0105.",
      general: "G\u0142\xF3wne",
      members: "Cz\u0142onkowie",
      title: "Organizacja"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Wpisz "{{organizationName}}" poni\u017Cej aby kontynuowa\u0107.',
          messageLine1: "Czy na pewno chcesz usun\u0105\u0107 t\u0119 organizacj\u0119?",
          messageLine2: "To dzia\u0142anie jest trwa\u0142e i nieodwracalne.",
          successMessage: "Organizacja zosta\u0142a usuni\u0119ta.",
          title: "Usu\u0144 organizacj\u0119"
        },
        leaveOrganization: {
          actionDescription: 'Wpisz "{{organizationName}}" poni\u017Cej aby kontynuowa\u0107.',
          messageLine1: "Czy na pewno chcesz opu\u015Bci\u0107 t\u0119 organizacj\u0119? Stracisz dost\u0119p do tej organizacji i jej aplikacji.",
          messageLine2: "Ta akcja jest trwa\u0142a i nieodwracalna.",
          successMessage: "Opu\u015Bci\u0142e\u015B organizacj\u0119.",
          title: "Opu\u015B\u0107 organizacj\u0119"
        },
        title: "Zagro\u017Cenie"
      },
      domainSection: {
        menuAction__manage: "Zarz\u0105dzaj",
        menuAction__remove: "Usu\u0144",
        menuAction__verify: "Zweryfikuj",
        primaryButton: "Dodaj domen\u0119",
        subtitle: "Zezwalaj u\u017Cytkownikom na automatyczne do\u0142\u0105czanie do organizacji lub \u017C\u0105daj do\u0142\u0105czenia na podstawie zweryfikowanej domeny e-mail.",
        title: "Zweryfikowane domeny"
      },
      successMessage: "Organizacja zosta\u0142a zaktualizowana.",
      title: "Profil organizacji"
    },
    removeDomainPage: {
      messageLine1: "Domena e-mail {{domain}} zostanie usuni\u0119ta.",
      messageLine2: "Po wykonaniu tej czynno\u015Bci u\u017Cytkownicy nie b\u0119d\u0105 mogli automatycznie do\u0142\u0105czy\u0107 do organizacji.",
      successMessage: "Domena {{domain}} zosta\u0142a usuni\u0119ta.",
      title: "Usu\u0144 domen\u0119"
    },
    start: {
      headerTitle__general: "Og\xF3lne",
      headerTitle__members: "Cz\u0142onkowie",
      profileSection: {
        primaryButton: void 0,
        title: "Profil organizacji",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Usuni\u0119cie tej domeny wp\u0142ynie na zaproszonych u\u017Cytkownik\xF3w.",
        removeDomainActionLabel__remove: "Usu\u0144 domen\u0119",
        removeDomainSubtitle: "Usu\u0144 t\u0105 domen\u0119 ze zweryfikowanych domen",
        removeDomainTitle: "Usu\u0144 domen\u0119"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "U\u017Cytkownicy s\u0105 automatycznie zapraszani do do\u0142\u0105czenia do organizacji podczas rejestracji i mog\u0105 do\u0142\u0105czy\u0107 w dowolnym momencie.",
        automaticInvitationOption__label: "Automatyczne zaproszenia",
        automaticSuggestionOption__description: "U\u017Cytkownicy otrzymuj\u0105 propozycj\u0119, aby poprosi\u0107 o do\u0142\u0105czenie, ale musz\u0105 zosta\u0107 zatwierdzeni przez administratora, zanim b\u0119d\u0105 mogli do\u0142\u0105czy\u0107 do organizacji.",
        automaticSuggestionOption__label: "Automatyczne propozycje",
        calloutInfoLabel: "Zmiana trybu rejestracji b\u0119dzie mia\u0142a wp\u0142yw tylko na nowych u\u017Cytkownik\xF3w.",
        calloutInvitationCountLabel: "Oczekuj\u0105ce zaproszenia wys\u0142ane do u\u017Cytkownik\xF3w: {{count}}",
        calloutSuggestionCountLabel: "Oczekuj\u0105ce propozycje wys\u0142ane do u\u017Cytkownik\xF3w: {{count}}",
        manualInvitationOption__description: "U\u017Cytkownik\xF3w mo\u017Cna zaprasza\u0107 do organizacji wy\u0142\u0105cznie r\u0119cznie.",
        manualInvitationOption__label: "Brak automatycznej rejestracji",
        subtitle: "Wybierz spos\xF3b, w jaki u\u017Cytkownicy z tej domeny mog\u0105 do\u0142\u0105czy\u0107 do organizacji."
      },
      start: {
        headerTitle__danger: "Zagro\u017Cenie",
        headerTitle__enrollment: "Opcje rejestracji"
      },
      subtitle: "Domena {{domain}} zosta\u0142a zweryfikowana. Kontynuuj, wybieraj\u0105c opcje rejestracji",
      title: "Zaktualizuj {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Wprowad\u017A kod weryfikacyjny wys\u0142any na Tw\xF3j adres e-mail",
      formTitle: "Kod weryfikacyjny",
      resendButton: "Nie otrzyma\u0142e\u015B kodu? Wy\u015Blij ponownie",
      subtitle: "Domena {{domainName}} musi zosta\u0107 zweryfikowana przez e-mail.",
      subtitleVerificationCodeScreen: "Kod weryfikacyjny zosta\u0142 wys\u0142any na adres {{emailAddress}}. Wprowad\u017A kod, aby kontynuowa\u0107.",
      title: "Zweryfikuj domen\u0119"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Utw\xF3rz organizacj\u0119",
    action__invitationAccept: "Do\u0142\u0105cz",
    action__manageOrganization: "Zarz\u0105dzaj organizacj\u0105",
    action__suggestionsAccept: "Pro\u015Bba o do\u0142\u0105czenie",
    notSelected: "Nie wybrano organizacji",
    personalWorkspace: "Przestrze\u0144 osobista",
    suggestionsAcceptedLabel: "Oczekiwanie na zatwierdzenie"
  },
  paginationButton__next: "Nast\u0119pny",
  paginationButton__previous: "Poprzedni",
  paginationRowText__displaying: "Wy\u015Bwietlanie",
  paginationRowText__of: "z",
  reverification: {
    alternativeMethods: {
      actionLink: "Uzyskaj pomoc",
      actionText: "Nie u\u017Cywasz \u017Cadnej z tych metod?",
      blockButton__backupCode: "U\u017Cyj kodu zapasowego",
      blockButton__emailCode: "Wy\u015Blij kod e-mailem do {{identifier}}",
      blockButton__passkey: void 0,
      blockButton__password: "Zaloguj si\u0119 za pomoc\u0105 has\u0142a",
      blockButton__phoneCode: "Wy\u015Blij kod SMS-em do {{identifier}}",
      blockButton__totp: "U\u017Cyj aplikacji uwierzytelniaj\u0105cej",
      getHelp: {
        blockButton__emailSupport: "Skontaktuj si\u0119 z pomoc\u0105",
        content: "Je\u015Bli masz problem z weryfikacj\u0105 konta, wy\u015Blij do nas e-mail, a postaramy si\u0119 jak najszybciej przywr\xF3ci\u0107 dost\u0119p.",
        title: "Uzyskaj wsparcie"
      },
      subtitle: "Masz problem? Mo\u017Cesz u\u017Cy\u0107 dowolnej z tych metod weryfikacji.",
      title: "U\u017Cyj innej metody"
    },
    backupCodeMfa: {
      subtitle: "Tw\xF3j kod zapasowy to ten, kt\xF3ry otrzyma\u0142e\u015B podczas konfigurowania uwierzytelniania dwuetapowego.",
      title: "Wprowad\u017A kod zapasowy"
    },
    emailCode: {
      formTitle: "Kod weryfikacyjny",
      resendButton: "Nie otrzyma\u0142e\u015B kodu? Wy\u015Blij ponownie",
      subtitle: "Wprowad\u017A kod wys\u0142any na Tw\xF3j adres e-mail, aby kontynuowa\u0107",
      title: "Wymagana weryfikacja"
    },
    noAvailableMethods: {
      message: "Nie mo\u017Cna kontynuowa\u0107 weryfikacji. Brak dost\u0119pnych czynnik\xF3w uwierzytelniania.",
      subtitle: "Wyst\u0105pi\u0142 b\u0142\u0105d",
      title: "Nie mo\u017Cemy zweryfikowa\u0107 twojego konta"
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: "U\u017Cyj innej metody",
      subtitle: "Wprowad\u017A has\u0142o, aby kontynuowa\u0107",
      title: "Wymagana weryfikacja"
    },
    phoneCode: {
      formTitle: "Kod weryfikacyjny",
      resendButton: "Nie otrzyma\u0142e\u015B kodu? Wy\u015Blij ponownie",
      subtitle: "Wprowad\u017A kod wys\u0142any na Tw\xF3j telefon, aby kontynuowa\u0107",
      title: "Wymagana weryfikacja"
    },
    phoneCodeMfa: {
      formTitle: "Kod weryfikacyjny",
      resendButton: "Nie otrzyma\u0142e\u015B kodu? Wy\u015Blij ponownie",
      subtitle: "Aby kontynuowa\u0107, wprowad\u017A kod weryfikacyjny wys\u0142any na tw\xF3j telefon",
      title: "Wymagana weryfikacja"
    },
    totpMfa: {
      formTitle: "Kod weryfikacyjny",
      subtitle: "Aby kontynuowa\u0107, wprowad\u017A kod weryfikacyjny wygenerowany przez swoj\u0105 aplikacj\u0119 uwierzytelniaj\u0105c\u0105",
      title: "Wymagana weryfikacja"
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Dodaj konto",
      action__signOutAll: "Wyloguj si\u0119 ze wszystkich kont",
      subtitle: "Wybierz konto, na kt\xF3rym chcesz kontynuowa\u0107.",
      title: "Wybierz konto"
    },
    alternativeMethods: {
      actionLink: "Uzyskaj pomoc",
      actionText: "Nie u\u017Cywasz \u017Cadnej z tych metod?",
      blockButton__backupCode: "U\u017Cyj kodu zapasowego",
      blockButton__emailCode: "Wy\u015Blij kod do {{identifier}}",
      blockButton__emailLink: "Wy\u015Blij link do {{identifier}}",
      blockButton__passkey: "Zaloguj si\u0119 za pomoc\u0105 klucza dost\u0119powego",
      blockButton__password: "Zaloguj si\u0119 za pomoc\u0105 has\u0142a",
      blockButton__phoneCode: "Wy\u015Blij kod do {{identifier}}",
      blockButton__totp: "U\u017Cyj aplikacji uwierzytelniaj\u0105cej",
      getHelp: {
        blockButton__emailSupport: "Wy\u015Blij e-mail do pomocy technicznej",
        content: "Je\u015Bli masz problem z zalogowaniem si\u0119 do swojego konta, wy\u015Blij do nas e-mail, a postaramy si\u0119 jak najszybciej przywr\xF3ci\u0107 dost\u0119p.",
        title: "Uzyskaj pomoc"
      },
      subtitle: "Masz problem? Mo\u017Cesz u\u017Cy\u0107 dowolnej z tych metod weryfikacji.",
      title: "U\u017Cyj innego sposobu"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "aby przej\u015B\u0107 do {{applicationName}}",
      title: "Wprowad\u017A kod zapasowy"
    },
    emailCode: {
      formTitle: "Kod weryfikacyjny",
      resendButton: "Wy\u015Blij kod ponownie",
      subtitle: "aby kontynuowa\u0107 w {{applicationName}}",
      title: "Sprawd\u017A swoj\u0105 poczt\u0119 e-mail"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Aby kontynuowa\u0107, otw\xF3rz link weryfikacyjny na urz\u0105dzeniu i w przegl\u0105darce, w kt\xF3rej rozpocz\u0105\u0142e\u015B logowanie",
        title: "Link weryfikacyjny jest nieprawid\u0142owy dla tego urz\u0105dzenia"
      },
      expired: {
        subtitle: "Powr\xF3\u0107 do oryginalnej karty, aby kontynuowa\u0107.",
        title: "Ten link weryfikacyjny wygas\u0142"
      },
      failed: {
        subtitle: "Powr\xF3\u0107 do oryginalnej karty, aby kontynuowa\u0107.",
        title: "Ten link weryfikacyjny jest nieprawid\u0142owy"
      },
      formSubtitle: "U\u017Cyj linku weryfikacyjnego wys\u0142anego na Tw\xF3j adres e-mail",
      formTitle: "Link weryfikacyjny",
      loading: {
        subtitle: "Zostaniesz przekierowany wkr\xF3tce",
        title: "Logowanie..."
      },
      resendButton: "Wy\u015Blij link ponownie",
      subtitle: "aby kontynuowa\u0107 w {{applicationName}}",
      title: "Sprawd\u017A swoj\u0105 poczt\u0119 e-mail",
      unusedTab: {
        title: "Mo\u017Cesz zamkn\u0105\u0107 t\u0119 kart\u0119"
      },
      verified: {
        subtitle: "Zostaniesz przekierowany wkr\xF3tce",
        title: "Pomy\u015Blnie zalogowano"
      },
      verifiedSwitchTab: {
        subtitle: "Powr\xF3\u0107 do oryginalnej karty, aby kontynuowa\u0107",
        subtitleNewTab: "Powr\xF3\u0107 do nowo otwartej karty, aby kontynuowa\u0107",
        titleNewTab: "Zalogowano na innej karcie"
      }
    },
    forgotPassword: {
      formTitle: "Kod werfikacyjny resetowania has\u0142a",
      resendButton: "Nie otrzyma\u0142e\u015B kodu? Wy\u015Blij ponownie",
      subtitle: "aby zresetowa\u0107 has\u0142o",
      subtitle_email: "Najpierw wprowad\u017A kod wys\u0142any na Tw\xF3j adres e-mail",
      subtitle_phone: "Najpierw wprowad\u017A kod wys\u0142any na Tw\xF3j numer telefonu",
      title: "Zmie\u0144 has\u0142o"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Zresetuj has\u0142o",
      label__alternativeMethods: "Lub zaloguj si\u0119 za pomoc\u0105 innej metody",
      title: "Zapomnia\u0142e\u015B has\u0142a?"
    },
    noAvailableMethods: {
      message: "Nie mo\u017Cna kontynuowa\u0107 logowania. Brak dost\u0119pnych czynnik\xF3w uwierzytelniaj\u0105cych.",
      subtitle: "Wyst\u0105pi\u0142 b\u0142\u0105d",
      title: "Nie mo\u017Cna si\u0119 zalogowa\u0107"
    },
    passkey: {
      subtitle: "U\u017Cycie klucza dost\u0119pu potwierdza, \u017Ce to Ty. Urz\u0105dzenie mo\u017Ce poprosi\u0107 o tw\xF3j odcisk palca, twarz lub blokad\u0119 ekranu.",
      title: "U\u017Cyj swojego klucza dost\u0119powego"
    },
    password: {
      actionLink: "U\u017Cyj innego sposobu",
      subtitle: "aby kontynuowa\u0107 w {{applicationName}}",
      title: "Wprowad\u017A swoje has\u0142o"
    },
    passwordPwned: {
      title: "Has\u0142o skompromitowane"
    },
    phoneCode: {
      formTitle: "Kod weryfikacyjny",
      resendButton: "Wy\u015Blij kod ponownie",
      subtitle: "aby przej\u015B\u0107 do {{applicationName}}",
      title: "Sprawd\u017A sw\xF3j telefon"
    },
    phoneCodeMfa: {
      formTitle: "Kod weryfikacyjny",
      resendButton: "Wy\u015Blij kod ponownie",
      subtitle: "Aby kontynuowa\u0107, wprowad\u017A kod weryfikacyjny wys\u0142any na Tw\xF3j telefon",
      title: "Sprawd\u017A sw\xF3j telefon"
    },
    resetPassword: {
      formButtonPrimary: "Zmie\u0144 has\u0142o",
      requiredMessage: "Z powod\xF3w bezpiecze\u0144stwa konieczne jest zresetowanie has\u0142a.",
      successMessage: "Twoje has\u0142o zosta\u0142o pomy\u015Blnie zresetowane. Logujemy Ci\u0119, prosz\u0119 czeka\u0107...",
      title: "Ustaw nowe has\u0142o"
    },
    resetPasswordMfa: {
      detailsLabel: "Przed zresetowaniem has\u0142a musimy zweryfikowa\u0107 to\u017Csamo\u015B\u0107 u\u017Cytkownika."
    },
    start: {
      actionLink: "Zarejestruj si\u0119",
      actionLink__join_waitlist: "Do\u0142\u0105cz do listy oczekuj\u0105cych",
      actionLink__use_email: "U\u017Cyj adresu e-mail",
      actionLink__use_email_username: "U\u017Cyj adresu e-mail lub nazwy u\u017Cytkownika",
      actionLink__use_passkey: "U\u017Cyj klucza dost\u0119powego",
      actionLink__use_phone: "U\u017Cyj numeru telefonu",
      actionLink__use_username: "U\u017Cyj nazwy u\u017Cytkownika",
      actionText: "Nie masz konta?",
      actionText__join_waitlist: "Chcesz otrzyma\u0107 wczesny dost\u0119p?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "by przej\u015B\u0107 do aplikacji {{applicationName}}",
      subtitleCombined: void 0,
      title: "Zaloguj si\u0119",
      titleCombined: "Kontynuuj do {{applicationName}}"
    },
    totpMfa: {
      formTitle: "Kod weryfikacyjny",
      subtitle: "Aby kontynuowa\u0107, wprowad\u017A kod weryfikacyjny wygenerowany przez aplikacj\u0119 uwierzytelniaj\u0105c\u0105",
      title: "Weryfikacja dwustopniowa"
    }
  },
  signInEnterPasswordTitle: "Wprowad\u017A swoje has\u0142o",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Zaloguj si\u0119",
      actionText: "Masz ju\u017C konto?",
      subtitle: "by przej\u015B\u0107 do aplikacji {{applicationName}}",
      title: "Uzupe\u0142nij brakuj\u0105ce pola"
    },
    emailCode: {
      formSubtitle: "Wprowad\u017A kod weryfikacyjny wys\u0142any na Tw\xF3j adres e-mail",
      formTitle: "Kod weryfikacyjny",
      resendButton: "Nie otrzyma\u0142e\u015B kodu? Wy\u015Blij ponownie",
      subtitle: "by przej\u015B\u0107 do aplikacji {{applicationName}}",
      title: "Zweryfikuj sw\xF3j adres e-mail"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Aby kontynuowa\u0107, otw\xF3rz link weryfikacyjny na urz\u0105dzeniu i w przegl\u0105darce, w kt\xF3rej rozpocz\u0105\u0142e\u015B rejestracj\u0119",
        title: "Link weryfikacyjny jest nieprawid\u0142owy dla tego urz\u0105dzenia"
      },
      formSubtitle: "U\u017Cyj linku weryfikacyjnego wys\u0142anego na Tw\xF3j adres e-mail",
      formTitle: "Link weryfikacyjny",
      loading: {
        title: "Rejestrowanie..."
      },
      resendButton: "Nie otrzyma\u0142e\u015B linku? Wy\u015Blij ponownie",
      subtitle: "by przej\u015B\u0107 do aplikacji {{applicationName}}",
      title: "Zweryfikuj sw\xF3j adres e-mail",
      verified: {
        title: "Pomy\u015Blnie zarejestrowano"
      },
      verifiedSwitchTab: {
        subtitle: "Powr\xF3\u0107 do nowo otwartej karty, aby kontynuowa\u0107",
        subtitleNewTab: "Powr\xF3\u0107 do poprzedniej karty, aby kontynuowa\u0107",
        title: "Adres e-mail zosta\u0142 pomy\u015Blnie zweryfikowany"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: 'Akceptuj\u0119 {{ privacyPolicyLink || link("Polityk\u0119 prywatno\u015Bci") }}',
        label__onlyTermsOfService: 'Akceptuj\u0119 {{ termsOfServiceLink || link("Warunki \u015Bwiadczenia us\u0142ugi") }}"',
        label__termsOfServiceAndPrivacyPolicy: 'Akceptuj\u0119 {{ termsOfServiceLink || link("Warunki \u015Bwiadczenia us\u0142ugi") }} i {{ privacyPolicyLink || link("Polityk\u0119 prywatno\u015Bci") }}'
      },
      continue: {
        subtitle: "Przeczytaj i zaakceptuj warunki, aby kontynuowa\u0107",
        title: "Zgoda prawna"
      }
    },
    phoneCode: {
      formSubtitle: "Wprowad\u017A kod weryfikacyjny wys\u0142any na Tw\xF3j numer telefonu",
      formTitle: "Kod weryfikacyjny",
      resendButton: "Nie otrzyma\u0142e\u015B kodu? Wy\u015Blij ponownie",
      subtitle: "by przej\u015B\u0107 do aplikacji {{applicationName}}",
      title: "Zweryfikuj sw\xF3j numer telefonu"
    },
    restrictedAccess: {
      actionLink: "Zaloguj si\u0119",
      actionText: "Masz ju\u017C konto?",
      blockButton__emailSupport: "Skontaktuj si\u0119 z pomoc\u0105",
      blockButton__joinWaitlist: "Do\u0142\u0105cz do listy oczekuj\u0105cych",
      subtitle: "Rejestracja jest obecnie wy\u0142\u0105czona. Je\u015Bli uwa\u017Casz, \u017Ce powiniene\u015B mie\u0107 dost\u0119p, skontaktuj si\u0119 z pomoc\u0105.",
      subtitleWaitlist: "Rejestracja jest obecnie wy\u0142\u0105czona. Aby dowiedzie\u0107 si\u0119 jako pierwszy o naszym starcie, do\u0142\u0105cz do listy oczekuj\u0105cych.",
      title: "Dost\u0119p ograniczony"
    },
    start: {
      actionLink: "Zaloguj si\u0119",
      actionLink__use_email: "U\u017Cyj adresu e-mail",
      actionLink__use_phone: "U\u017Cyj numeru telefonu",
      actionText: "Masz ju\u017C konto?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "by przej\u015B\u0107 do aplikacji {{applicationName}}",
      subtitleCombined: "by przej\u015B\u0107 do aplikacji {{applicationName}}",
      title: "Utw\xF3rz swoje konto",
      titleCombined: "Utw\xF3rz swoje konto"
    }
  },
  socialButtonsBlockButton: "Kontynuuj z {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: "{{provider|titleize}}",
  unstable__errors: {
    already_a_member_in_organization: "{{email}} jest ju\u017C cz\u0142onkiem organizacji.",
    captcha_invalid: "Rejestracja nie powiod\u0142a si\u0119 z powodu niepowodzenia weryfikacji zabezpiecze\u0144. Od\u015Bwie\u017C stron\u0119, aby spr\xF3bowa\u0107 ponownie lub skontaktuj si\u0119 z pomoc\u0105, aby uzyska\u0107 wsparcie.",
    captcha_unavailable: "Rejestracja nie powiod\u0142a si\u0119 z powodu niedost\u0119pno\u015Bci weryfikacji bot\xF3w. Od\u015Bwie\u017C stron\u0119, aby spr\xF3bowa\u0107 ponownie lub skontaktuj si\u0119 z pomoc\u0105, aby uzyska\u0107 wsparcie.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: "Adres e-mail jest ju\u017C zaj\u0119ty. Prosz\u0119 spr\xF3bowa\u0107 innego.",
    form_identifier_exists__phone_number: "Ten numer telefonu jest zaj\u0119ty. Spr\xF3buj u\u017Cy\u0107 innego.",
    form_identifier_exists__username: "Ta nazwa u\u017Cytkownika jest zaj\u0119ta. Spr\xF3buj u\u017Cy\u0107 innej.",
    form_identifier_not_found: "Nie znaleziono konta o tym identyfikatorze. Sprawd\u017A i spr\xF3buj ponownie.",
    form_param_format_invalid: "Wprowadzona warto\u015B\u0107 ma nieprawid\u0142owy format. Sprawd\u017A i popraw j\u0105.",
    form_param_format_invalid__email_address: "Adres e-mail powinien by\u0107 poprawnym adresem e-mail.",
    form_param_format_invalid__phone_number: "Numer telefonu powinien mie\u0107 prawid\u0142owy format mi\u0119dzynarodowy",
    form_param_max_length_exceeded__first_name: "Imi\u0119 nie powinno przekracza\u0107 256 znak\xF3w.",
    form_param_max_length_exceeded__last_name: "Nazwisko nie powinno przekracza\u0107 256 znak\xF3w.",
    form_param_max_length_exceeded__name: "Nazwa nie powinna przekracza\u0107 256 znak\xF3w.",
    form_param_nil: "To pole jest wymagane i nie mo\u017Ce by\u0107 puste.",
    form_param_value_invalid: "Wprowadzona warto\u015B\u0107 jest nieprawid\u0142owa. Popraw j\u0105.",
    form_password_incorrect: "Wprowadzone has\u0142o jest nieprawid\u0142owe. Spr\xF3buj ponownie.",
    form_password_length_too_short: "Twoje has\u0142o jest zbyt kr\xF3tkie. Musi mie\u0107 co najmniej 8 znak\xF3w.",
    form_password_not_strong_enough: "Twoje has\u0142o nie jest wystarczaj\u0105co silne",
    form_password_pwned: "To has\u0142o zosta\u0142o znalezione w wyniku w\u0142amania i nie mo\u017Cna go u\u017Cy\u0107. Zamiast tego spr\xF3buj u\u017Cy\u0107 innego has\u0142a.",
    form_password_pwned__sign_in: "To has\u0142o zosta\u0142o znalezione w wyniku w\u0142amania i nie mo\u017Cna go u\u017Cy\u0107. Zresetuj has\u0142o.",
    form_password_size_in_bytes_exceeded: "Twoje has\u0142o przekroczy\u0142o maksymaln\u0105 dozwolon\u0105 liczb\u0119 bajt\xF3w, skr\xF3\u0107 je lub usu\u0144 niekt\xF3re znaki specjalne.",
    form_password_validation_failed: "Podane has\u0142o jest nieprawid\u0142owe",
    form_username_invalid_character: "Twoja nazwa u\u017Cytkownika zawiera nieprawid\u0142owe znaki. Prosimy o u\u017Cywanie wy\u0142\u0105cznie liter, cyfr i podkre\u015Ble\u0144.",
    form_username_invalid_length: "Nazwa u\u017Cytkownika musi zawiera\u0107 od {{min_length}} do {{max_length}} znak\xF3w.",
    identification_deletion_failed: "Nie mo\u017Cna usun\u0105\u0107 ostatniego identyfikatora.",
    not_allowed_access: "Adres e-mail lub numer telefonu nie jest dozwolony do rejestracji. Mo\u017Ce to by\u0107 spowodowane u\u017Cyciem '+', '=', '#' lub '.' w adresie e-mail, u\u017Cyciem domeny skojarzonej z us\u0142ug\u0105 poczty e-mail tymczasowej lub jawnego wykluczenia.",
    organization_domain_blocked: "To jest zablokowana domena dostawcy poczty e-mail. U\u017Cyj innej domeny.",
    organization_domain_common: "To jest popularna domena dostawcy poczty e-mail. U\u017Cyj innej domeny.",
    organization_domain_exists_for_enterprise_connection: "Ta domena jest ju\u017C u\u017Cywana do logowania jednokrotnego w organizacji.",
    organization_membership_quota_exceeded: "Osi\u0105gni\u0119to limit cz\u0142onkostwa w organizacji, w tym zaleg\u0142ych zaprosze\u0144.",
    organization_minimum_permissions_needed: "Musi istnie\u0107 co najmniej jeden cz\u0142onek organizacji z minimalnymi wymaganymi uprawnieniami.",
    passkey_already_exists: "Klucz dost\u0119pu jest ju\u017C zarejestrowany w tym urz\u0105dzeniu.",
    passkey_not_supported: "Klucze dost\u0119pu nie s\u0105 obs\u0142ugiwane przez to urz\u0105dzenie.",
    passkey_pa_not_supported: "Rejestracja wymaga platformy uwierzytelniaj\u0105cej, ale urz\u0105dzenie jej nie obs\u0142uguje.",
    passkey_registration_cancelled: "Rejestracja klucza dost\u0119pu zosta\u0142a anulowana lub up\u0142yn\u0105\u0142 jej limit czasu.",
    passkey_retrieval_cancelled: "Weryfikacja klucza dost\u0119pu zosta\u0142a anulowana lub up\u0142yn\u0105\u0142 limit czasu.",
    passwordComplexity: {
      maximumLength: "mniej ni\u017C {{length}} znak\xF3w",
      minimumLength: "{{length}} lub wi\u0119cej znak\xF3w",
      requireLowercase: "ma\u0142\u0105 liter\u0119",
      requireNumbers: "cyfr\u0119",
      requireSpecialCharacter: "znak specjalny",
      requireUppercase: "wielk\u0105 liter\u0119",
      sentencePrefix: "Twoje has\u0142o musi zawiera\u0107"
    },
    phone_number_exists: "Numer telefonu jest ju\u017C zaj\u0119ty. Prosz\u0119 spr\xF3bowa\u0107 innego.",
    session_exists: "Jeste\u015B ju\u017C zalogowany.",
    web3_missing_identifier: "Nie mo\u017Cna znale\u017A\u0107 rozszerzenia Web3 Wallet. Zainstaluj je, aby kontynuowa\u0107.",
    zxcvbn: {
      couldBeStronger: "Twoje has\u0142o jest odpowiednie, ale mog\u0142oby by\u0107 silniejsze. Spr\xF3buj doda\u0107 wi\u0119cej znak\xF3w.",
      goodPassword: "Twoje has\u0142o jest wystarczaj\u0105co silne.",
      notEnough: "Twoje has\u0142o jest zbyt s\u0142abe. Spr\xF3buj doda\u0107 wi\u0119cej znak\xF3w.",
      suggestions: {
        allUppercase: "Unikaj u\u017Cywania samych wielkich liter.",
        anotherWord: "Dodaj wi\u0119cej s\u0142\xF3w, kt\xF3re s\u0105 rzadsze.",
        associatedYears: "Unikaj lat zwi\u0105zanych z Tob\u0105.",
        capitalization: "U\u017Cywaj wielkich liter cz\u0119sciej.",
        dates: "Unikaj dat zwi\u0105zanych z Tob\u0105.",
        l33t: "Unikaj przewidywalnego zamieniania liter, takich jak '@' za 'a'.",
        longerKeyboardPattern: "U\u017Cywaj d\u0142ugich wzor\xF3w na klawiaturze, zmieniaj\u0105c kierunek pisania wielokrotnie.",
        noNeed: "Mo\u017Cesz tworzy\u0107 silne has\u0142a bez u\u017Cywania symboli, cyfr lub wielkich liter.",
        pwned: "Je\u017Celi u\u017Cywasz tego has\u0142a gdzie indziej, zmie\u0144 je jak najszybciej.",
        recentYears: "Unikaj ostatnich lat.",
        repeated: "Unikaj powtarzanych s\u0142\xF3w i znak\xF3w.",
        reverseWords: "Unikaj wpisywania popularnych s\u0142\xF3w od ty\u0142u.",
        sequences: "Unikaj popularnych kombinacji znak\xF3w.",
        useWords: "U\u017Cywaj wielu s\u0142\xF3w, ale unikaj popularnych fraz."
      },
      warnings: {
        common: "Jest to cz\u0119sto u\u017Cywane has\u0142o.",
        commonNames: "Imiona i nazwiska s\u0105 \u0142atwe do odgadni\u0119cia.",
        dates: "Daty s\u0105 \u0142atwe do odgadni\u0119cia.",
        extendedRepeat: 'Powtarzaj\u0105ce si\u0119 wzorce znak\xF3w, takie jak "abcabcabc", s\u0105 \u0142atwe do odgadni\u0119cia.',
        keyPattern: "Kr\xF3tkie wzory klawiatury s\u0105 \u0142atwe do odgadni\u0119cia.",
        namesByThemselves: "Pojedyncze imiona lub nazwiska s\u0105 \u0142atwe do odgadni\u0119cia.",
        pwned: "Twoje has\u0142o zosta\u0142o ujawnione w wyniku wycieku danych w Internecie.",
        recentYears: "Ostatnie lata s\u0105 \u0142atwe do odgadni\u0119cia.",
        sequences: 'Typowe sekwencje znak\xF3w, takie jak "abc", s\u0105 \u0142atwe do odgadni\u0119cia.',
        similarToCommon: "Jest to podobne do powszechnie u\u017Cywanego has\u0142a.",
        simpleRepeat: 'Powtarzaj\u0105ce si\u0119 znaki, takie jak "aaa", s\u0105 \u0142atwe do odgadni\u0119cia.',
        straightRow: "Proste rz\u0119dy klawiszy na klawiaturze s\u0105 \u0142atwe do odgadni\u0119cia.",
        topHundred: "To jest cz\u0119sto u\u017Cywane has\u0142o.",
        topTen: "To jest bardzo cz\u0119sto u\u017Cywane has\u0142o.",
        userInputs: "Nie powinno by\u0107 \u017Cadnych danych osobowych ani danych zwi\u0105zanych ze stron\u0105.",
        wordByItself: "Pojedyncze s\u0142owa s\u0105 \u0142atwe do odgadni\u0119cia."
      }
    }
  },
  userButton: {
    action__addAccount: "Dodaj konto",
    action__manageAccount: "Zarz\u0105dzaj kontem",
    action__signOut: "Wyloguj",
    action__signOutAll: "Wyloguj ze wszystkich kont"
  },
  userProfile: {
    backupCodePage: {
      actionLabel__copied: "Skopiowane!",
      actionLabel__copy: "Skopiuj wszystkie",
      actionLabel__download: "Pobierz .txt",
      actionLabel__print: "Drukuj",
      infoText1: "Kody zapasowe zostan\u0105 w\u0142\u0105czone dla tego konta.",
      infoText2: "Przechowuj kody zapasowe w tajemnicy i bezpiecznie. Mo\u017Cesz wygenerowa\u0107 nowe kody, je\u015Bli podejrzewasz, \u017Ce zosta\u0142y skompromitowane.",
      subtitle__codelist: "Przechowuj je bezpiecznie i zachowaj w tajemnicy.",
      successMessage: "Kody zapasowe s\u0105 teraz w\u0142\u0105czone. Mo\u017Cesz u\u017Cy\u0107 jednego z tych kod\xF3w do zalogowania si\u0119 na swoje konto, je\u015Bli utracisz dost\u0119p do urz\u0105dzenia uwierzytelniaj\u0105cego. Ka\u017Cdy kod mo\u017Cna u\u017Cy\u0107 tylko raz.",
      successSubtitle: "Mo\u017Cesz u\u017Cy\u0107 jednego z tych kod\xF3w do zalogowania si\u0119 na swoje konto, je\u015Bli utracisz dost\u0119p do urz\u0105dzenia uwierzytelniaj\u0105cego.",
      title: "Dodaj weryfikacj\u0119 kodem zapasowym",
      title__codelist: "Kody zapasowe"
    },
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "Wybierz dostawc\u0119, aby po\u0142\u0105czy\u0107 konto.",
      formHint__noAccounts: "Nie ma dost\u0119pnych zewn\u0119trznych dostawc\xF3w kont.",
      removeResource: {
        messageLine1: "{{identifier}} zostanie usuni\u0119te z tego konta.",
        messageLine2: "Nie b\u0119dziesz ju\u017C m\xF3g\u0142 korzysta\u0107 z tego po\u0142\u0105czonego konta i wszystkie zale\u017Cne funkcje przestan\u0105 dzia\u0142a\u0107.",
        successMessage: "{{connectedAccount}} zosta\u0142o usuni\u0119te z Twojego konta.",
        title: "Usu\u0144 po\u0142\u0105czone konto"
      },
      socialButtonsBlockButton: "Po\u0142\u0105cz konto {{provider|titleize}}",
      successMessage: "Dostawca zosta\u0142 dodany do Twojego konta.",
      title: "Dodaj po\u0142\u0105czone konto"
    },
    deletePage: {
      actionDescription: 'Wpisz "Usu\u0144 konto" poni\u017Cej aby kontynuowa\u0107.',
      confirm: "Usu\u0144 konto",
      messageLine1: "Czy na pewno chcesz usun\u0105\u0107 to konto?",
      messageLine2: "Ta operacja jest nieodwracalna i nie mo\u017Cna jej cofn\u0105\u0107.",
      title: "Usu\u0144 konto"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "E-mail zawieraj\u0105cy kod weryfikacyjny zostanie wys\u0142any na ten adres e-mail.",
        formSubtitle: "Wprowad\u017A kod weryfikacyjny wys\u0142any na adres {{identifier}}",
        formTitle: "Kod weryfikacyjny",
        resendButton: "Wy\u015Blij ponownie kod",
        successMessage: "Adres e-mail {{identifier}} zosta\u0142 dodany do twojego konta."
      },
      emailLink: {
        formHint: "E-mail zawieraj\u0105cy link weryfikacyjny zostanie wys\u0142any na ten adres e-mail.",
        formSubtitle: "Kliknij w link weryfikacyjny w e-mailu wys\u0142anym na adres {{identifier}}",
        formTitle: "Link weryfikacyjny",
        resendButton: "Wy\u015Blij ponownie link",
        successMessage: "Adres e-mail {{identifier}} zosta\u0142 dodany do twojego konta."
      },
      enterpriseSSOLink: {
        formButton: "Kliknij, aby si\u0119 zalogowa\u0107",
        formSubtitle: "Uko\u0144cz logowanie za pomoc\u0105 {{identifier}}"
      },
      formHint: "Przed dodaniem adresu e-mail do konta nale\u017Cy go zweryfikowa\u0107.",
      removeResource: {
        messageLine1: "{{identifier}} zostanie usuni\u0119ty z tego konta.",
        messageLine2: "Nie b\u0119dzie ju\u017C mo\u017Cliwe zalogowanie si\u0119 za pomoc\u0105 tego adresu e-mail.",
        successMessage: "{{emailAddress}} zosta\u0142 usuni\u0119ty z twojego konta.",
        title: "Usu\u0144 adres e-mail"
      },
      title: "Dodaj adres e-mail",
      verifyTitle: "Zweryfikuj adres e-mail"
    },
    formButtonPrimary__add: "Dodaj",
    formButtonPrimary__continue: "Kontynuuj",
    formButtonPrimary__finish: "Zako\u0144cz",
    formButtonPrimary__remove: "Usu\u0144",
    formButtonPrimary__save: "Zapisz",
    formButtonReset: "Anuluj",
    mfaPage: {
      formHint: "Wybierz metod\u0119 dodania.",
      title: "Dodaj weryfikacj\u0119 dwuetapow\u0105"
    },
    mfaPhoneCodePage: {
      backButton: "U\u017Cyj istniej\u0105cego numeru",
      primaryButton__addPhoneNumber: "Dodaj numer telefonu",
      removeResource: {
        messageLine1: "{{identifier}} nie b\u0119dzie ju\u017C otrzymywa\u0142 kod\xF3w weryfikacyjnych podczas logowania.",
        messageLine2: "Twoje konto mo\u017Ce by\u0107 mniej bezpieczne. Czy na pewno chcesz kontynuowa\u0107?",
        successMessage: "Weryfikacja kodem SMS w dwustopniowym procesie uwierzytelniania zosta\u0142a usuni\u0119ta dla {{mfaPhoneCode}}",
        title: "Usu\u0144 dwustopniow\u0105 weryfikacj\u0119"
      },
      subtitle__availablePhoneNumbers: "Wybierz numer telefonu, aby zarejestrowa\u0107 weryfikacj\u0119 kodem SMS w dwustopniowym procesie uwierzytelniania.",
      subtitle__unavailablePhoneNumbers: "Brak dost\u0119pnych numer\xF3w telefon\xF3w do zarejestrowania weryfikacji kodem SMS w dwustopniowym procesie uwierzytelniania.",
      successMessage1: "Podczas logowania nale\u017Cy dodatkowo wprowadzi\u0107 kod weryfikacyjny wys\u0142any na ten numer telefonu.",
      successMessage2: "Zapisz te kody zapasowe i przechowuj je w bezpiecznym miejscu. Je\u015Bli utracisz dost\u0119p do urz\u0105dzenia uwierzytelniaj\u0105cego, mo\u017Cesz u\u017Cy\u0107 kod\xF3w zapasowych, aby si\u0119 zalogowa\u0107.",
      successTitle: "Weryfikacja kodem SMS w\u0142\u0105czona",
      title: "Dodaj weryfikacj\u0119 kodem SMS"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Zamiast tego zeskanuj kod QR",
        buttonUnableToScan__nonPrimary: "Nie mo\u017Cna zeskanowa\u0107 kodu QR?",
        infoText__ableToScan: "Ustaw now\u0105 metod\u0119 logowania w swojej aplikacji autentykacyjnej i zeskanuj nast\u0119puj\u0105cy kod QR, aby po\u0142\u0105czy\u0107 go z Twoim kontem.",
        infoText__unableToScan: "Ustaw now\u0105 metod\u0119 logowania w swojej aplikacji autentykacyjnej i wprowad\u017A poni\u017Cszy klucz.",
        inputLabel__unableToScan1: "Upewnij si\u0119, \u017Ce w\u0142\u0105czona jest opcja jednorazowe has\u0142a lub has\u0142a oparte na czasie, a nast\u0119pnie zako\u0144cz \u0142\u0105czenie konta.",
        inputLabel__unableToScan2: "Alternatywnie, je\u015Bli Twoja aplikacja autentykacyjna obs\u0142uguje URI TOTP, mo\u017Cesz r\xF3wnie\u017C skopiowa\u0107 pe\u0142ny URI."
      },
      removeResource: {
        messageLine1: "Kody weryfikacyjne z tej aplikacji autentykacyjnej nie b\u0119d\u0105 ju\u017C wymagane podczas logowania.",
        messageLine2: "Twoje konto mo\u017Ce by\u0107 mniej bezpieczne. Czy na pewno chcesz kontynuowa\u0107?",
        successMessage: "Weryfikacja dwuetapowa za pomoc\u0105 aplikacji autentykacyjnej zosta\u0142a usuni\u0119ta.",
        title: "Usu\u0144 weryfikacj\u0119 dwuetapow\u0105"
      },
      successMessage: "Weryfikacja dwuetapowa jest teraz w\u0142\u0105czona. Przy logowaniu b\u0119dziesz musia\u0142 wprowadzi\u0107 kod weryfikacyjny z tej aplikacji jako dodatkowy krok.",
      title: "Dodaj aplikacj\u0119 autentykacyjn\u0105",
      verifySubtitle: "Wprowad\u017A kod weryfikacyjny wygenerowany przez Twoj\u0105 aplikacj\u0119 autentykacyjn\u0105",
      verifyTitle: "Kod weryfikacyjny"
    },
    mobileButton__menu: "Menu",
    navbar: {
      account: "Profil",
      billing: void 0,
      description: "Zarz\u0105dzaj danymi konta.",
      security: "Bezpiecze\u0144stwo",
      title: "Konto"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "{{name}} zostanie usuni\u0119ty z tego konta.",
        title: "Usu\u0144 klucz dost\u0119pu"
      },
      subtitle__rename: "Mo\u017Cesz zmieni\u0107 nazw\u0119 klucza dost\u0119pu aby go \u0142atwiej znale\u017A\u0107.",
      title__rename: "Zmie\u0144 nazw\u0119 klucza dost\u0119pu"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Zaleca si\u0119 wylogowanie z innych urz\u0105dze\u0144, kt\xF3re mog\u0142y u\u017Cywa\u0107 starego has\u0142a.",
      readonly: "Obecnie nie mo\u017Cna edytowa\u0107 has\u0142a, poniewa\u017C mo\u017Cesz zalogowa\u0107 si\u0119 tylko za po\u015Brednictwem po\u0142\u0105czenia firmowego.",
      successMessage__set: "Twoje has\u0142o zosta\u0142o ustawione.",
      successMessage__signOutOfOtherSessions: "Wylogowano z wszystkich innych urz\u0105dze\u0144.",
      successMessage__update: "Twoje has\u0142o zosta\u0142o zaktualizowane.",
      title__set: "Ustaw has\u0142o",
      title__update: "Zmie\u0144 has\u0142o"
    },
    phoneNumberPage: {
      infoText: "Wiadomo\u015B\u0107 tekstowa zawieraj\u0105ca link weryfikacyjny zostanie wys\u0142ana na ten numer telefonu.",
      removeResource: {
        messageLine1: "{{identifier}} zostanie usuni\u0119ty z tego konta.",
        messageLine2: "Nie b\u0119dzie ju\u017C mo\u017Cliwe zalogowanie si\u0119 za pomoc\u0105 tego numeru telefonu.",
        successMessage: "{{phoneNumber}} zosta\u0142 usuni\u0119ty z twojego konta.",
        title: "Usu\u0144 numer telefonu"
      },
      successMessage: "{{identifier}} zosta\u0142 dodany do twojego konta.",
      title: "Dodaj numer telefonu",
      verifySubtitle: "Wpisz kod weryfikacyjny wys\u0142any na {{identifier}}",
      verifyTitle: "Zweryfikuj numer telefonu"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "Prze\u015Blij zdj\u0119cie w formacie JPG, PNG, GIF lub WEBP mniejsze ni\u017C 10 MB",
      imageFormDestructiveActionSubtitle: "Usu\u0144 zdj\u0119cie",
      imageFormSubtitle: "Prze\u015Blij zdj\u0119cie",
      imageFormTitle: "Zdj\u0119cie profilowe",
      readonly: "Informacje o Twoim profilu zosta\u0142y udost\u0119pnione przez po\u0142\u0105czenie firmowe i nie mo\u017Cna ich edytowa\u0107.",
      successMessage: "Tw\xF3j profil zosta\u0142 zaktualizowany.",
      title: "Edytuj profil"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Wyloguj z urz\u0105dzenia",
        title: "Aktywne urz\u0105dzenia"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Spr\xF3buj ponownie",
        actionLabel__reauthorize: "Autoryzuj teraz",
        destructiveActionTitle: "Od\u0142\u0105cz",
        primaryButton: "Po\u0142\u0105cz konto",
        subtitle__disconnected: "To konto zosta\u0142o od\u0142\u0105czone",
        subtitle__reauthorize: "Wymagane zakresy zosta\u0142y zaktualizowane i funkcjonalno\u015B\u0107 aplikacji mo\u017Ce by\u0107 ograniczona. Aby unikn\u0105\u0107 problem\xF3w, nale\u017Cy ponownie autoryzowa\u0107 aplikacj\u0119",
        title: "Po\u0142\u0105czone konta"
      },
      dangerSection: {
        deleteAccountButton: "Usu\u0144 konto",
        title: "Niebezpiecze\u0144stwo"
      },
      emailAddressesSection: {
        destructiveAction: "Usu\u0144 adres email",
        detailsAction__nonPrimary: "Ustaw jako g\u0142\xF3wny",
        detailsAction__primary: "Zako\u0144cz weryfikacj\u0119",
        detailsAction__unverified: "Zako\u0144cz weryfikacj\u0119",
        primaryButton: "Dodaj adres email",
        title: "Adresy email"
      },
      enterpriseAccountsSection: {
        title: "Konta firmowe"
      },
      headerTitle__account: "Konto",
      headerTitle__security: "Bezpiecze\u0144stwo",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Wygeneruj kody",
          headerTitle: "Kody zapasowe",
          subtitle__regenerate: "Otrzymaj nowy zestaw bezpiecznych kod\xF3w zapasowych. Poprzednie kody zapasowe zostan\u0105 usuni\u0119te i nie b\u0119d\u0105 dzia\u0142a\u0107.",
          title__regenerate: "Wygeneruj nowe kody zapasowe"
        },
        phoneCode: {
          actionLabel__setDefault: "Ustaw jako domy\u015Blny",
          destructiveActionLabel: "Usu\u0144 numer telefonu"
        },
        primaryButton: "Dodaj weryfikacj\u0119 dwuetapow\u0105",
        title: "Weryfikacja dwuetapowa",
        totp: {
          destructiveActionTitle: "Usu\u0144",
          headerTitle: "Aplikacja autoryzacyjna"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Usu\u0144",
        menuAction__rename: "Zmie\u0144 nazw\u0119",
        primaryButton: void 0,
        title: "Klucze dost\u0119pu"
      },
      passwordSection: {
        primaryButton__setPassword: "Ustaw has\u0142o",
        primaryButton__updatePassword: "Zmie\u0144 has\u0142o",
        title: "Has\u0142o"
      },
      phoneNumbersSection: {
        destructiveAction: "Usu\u0144 numer telefonu",
        detailsAction__nonPrimary: "Ustaw jako g\u0142\xF3wny",
        detailsAction__primary: "Zako\u0144cz weryfikacj\u0119",
        detailsAction__unverified: "Zako\u0144cz weryfikacj\u0119",
        primaryButton: "Dodaj numer telefonu",
        title: "Numery telefon\xF3w"
      },
      profileSection: {
        primaryButton: "Zaaktualizuj profil",
        title: "Profil"
      },
      usernameSection: {
        primaryButton__setUsername: "Ustaw nazw\u0119 u\u017Cytkownika",
        primaryButton__updateUsername: "Zmie\u0144 nazw\u0119 u\u017Cytkownika",
        title: "Nazwa u\u017Cytkownika"
      },
      web3WalletsSection: {
        destructiveAction: "Usu\u0144 portfel",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Portfele Web3",
        title: "Portfele Web3"
      }
    },
    usernamePage: {
      successMessage: "Twoja nazwa u\u017Cytkownika zosta\u0142a zaktualizowana.",
      title__set: "Zmie\u0144 nazw\u0119 u\u017Cytkownika",
      title__update: "Zmie\u0144 nazw\u0119 u\u017Cytkownika"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} zostanie usuni\u0119ty z tego konta.",
        messageLine2: "Nie b\u0119dziesz ju\u017C m\xF3g\u0142 si\u0119 zalogowa\u0107 za pomoc\u0105 tego portfela web3.",
        successMessage: "{{web3Wallet}} zosta\u0142 usuni\u0119ty z Twojego konta.",
        title: "Usu\u0144 portfel web3"
      },
      subtitle__availableWallets: "Wybierz portfel web3 do po\u0142\u0105czenia z Twoim kontem.",
      subtitle__unavailableWallets: "Nie ma dost\u0119pnych portfeli web3.",
      successMessage: "Portfel zosta\u0142 dodany do Twojego konta.",
      title: "Dodaj portfel web3",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: "Zaloguj si\u0119",
      actionText: "Masz ju\u017C konto?",
      formButton: "Do\u0142\u0105cz do listy oczekuj\u0105cych",
      subtitle: "Wpisz sw\xF3j adres e-mail, a my powiadomimy Ci\u0119, gdy miejsce dla Ciebie b\u0119dzie gotowe.",
      title: "Do\u0142\u0105cz do listy oczekuj\u0105cych"
    },
    success: {
      message: "Wkr\xF3tce nast\u0105pi przekierowanie...",
      subtitle: "Skontaktujemy si\u0119 z Tob\u0105, gdy miejsce dla Ciebie b\u0119dzie gotowe",
      title: "Dzi\u0119kujemy za do\u0142\u0105czenie do listy oczekuj\u0105cych!"
    }
  }
};
export {
  plPL
};
//# sourceMappingURL=pl-PL.mjs.map