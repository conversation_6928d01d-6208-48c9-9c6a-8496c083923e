"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["861"],{6522:function(t,i,e){e.r(i),e.d(i,{WaitlistModal:()=>z,Waitlist:()=>w});var l=e(9109),a=e(3799),o=e(4455),n=e(2672),s=e(1576),r=e(9541),c=e(4676),d=e(7623),h=e(431),u=e(2654),m=e(8969),Z=e(2464),p=e(4174),y=e(1201);let g=t=>{let i=(0,a.cL)(),e=(0,n.useCardState)(),g=(0,Z._m)(),{navigate:w}=(0,c.useRouter)(),z=(0,s.useWaitlistContext)(),{formState:A}=t,K=(0,m.a2)({onNextStep:()=>e.setError(void 0)}),C=async t=>{t.preventDefault(),g.setLoading(),e.setLoading(),e.setError(void 0);let l={emailAddress:A.emailAddress.value};await i.joinWaitlist(l).then(()=>{K.nextStep(),setTimeout(()=>{z.afterJoinWaitlistUrl&&w(z.afterJoinWaitlistUrl)},2e3)}).catch(t=>{(0,d.S3)(t,[A.emailAddress],e.setError)}).finally(()=>{g.setIdle(),e.setIdle()})};return(0,l.BX)(m.en,{...K.props,children:[(0,l.BX)(r.Col,{gap:6,children:[(0,l.BX)(u.h.Root,{showLogo:!0,children:[(0,l.tZ)(u.h.Title,{localizationKey:(0,r.localizationKeys)("waitlist.start.title")}),(0,l.tZ)(u.h.Subtitle,{localizationKey:(0,r.localizationKeys)("waitlist.start.subtitle")})]}),(0,l.tZ)(o.Z.Alert,{children:e.error}),(0,l.tZ)(r.Flex,{direction:"col",elementDescriptor:r.descriptors.main,gap:6,children:(0,l.BX)(h.l.Root,{onSubmit:C,gap:8,children:[(0,l.tZ)(r.Col,{gap:6,children:(0,l.tZ)(h.l.ControlRow,{elementId:"emailAddress",children:(0,l.tZ)(h.l.PlainInput,{...A.emailAddress.props,isRequired:!0})})}),(0,l.tZ)(r.Col,{center:!0,children:(0,l.tZ)(h.l.SubmitButton,{localizationKey:(0,r.localizationKeys)("waitlist.start.formButton")})})]})})]}),(0,l.BX)(r.Col,{gap:6,children:[(0,l.BX)(u.h.Root,{showLogo:!0,children:[(0,l.tZ)(u.h.Title,{localizationKey:(0,r.localizationKeys)("waitlist.success.title")}),(0,l.tZ)(u.h.Subtitle,{localizationKey:(0,r.localizationKeys)("waitlist.success.subtitle")})]}),z.afterJoinWaitlistUrl&&(0,l.tZ)(r.Flex,{direction:"col",elementDescriptor:r.descriptors.main,gap:6,children:(0,l.tZ)(r.Col,{center:!0,children:(0,l.BX)(r.Flex,{gap:2,align:"center",children:[(0,l.tZ)(r.Icon,{icon:p.z6,sx:t=>({margin:"auto",width:t.sizes.$6,height:t.sizes.$6,animation:`${y.animations.spinning} 1s linear infinite`})}),(0,l.tZ)(r.Text,{colorScheme:"secondary",localizationKey:(0,r.localizationKeys)("waitlist.success.message")})]})})})]})]})},w=(0,n.withCardStateProvider)(()=>{let t=(0,a.cL)(),i=(0,s.useWaitlistContext)(),{signInUrl:e}=i,n=i.initialValues||{},c={emailAddress:(0,d.Yp)("emailAddress",n.emailAddress||"",{type:"email",label:(0,r.localizationKeys)("formFieldLabel__emailAddress"),placeholder:(0,r.localizationKeys)("formFieldInputPlaceholder__emailAddress")})};return(0,l.tZ)(r.Flow.Root,{flow:"waitlist",children:(0,l.BX)(o.Z.Root,{children:[(0,l.tZ)(o.Z.Content,{children:(0,l.tZ)(g,{formState:c})}),(0,l.tZ)(o.Z.Footer,{children:(0,l.BX)(o.Z.Action,{elementId:"waitlist",children:[(0,l.tZ)(o.Z.ActionText,{localizationKey:(0,r.localizationKeys)("waitlist.start.actionText")}),(0,l.tZ)(o.Z.ActionLink,{localizationKey:(0,r.localizationKeys)("waitlist.start.actionLink"),to:t.buildUrlWithAuth(e)})]})})]})})}),z=t=>{let i={signInUrl:`/${c.VIRTUAL_ROUTER_BASE_PATH}/sign-in`,...t,routing:"virtual"};return(0,l.tZ)(c.Route,{path:"waitlist",children:(0,l.tZ)(s.WaitlistContext.Provider,{value:{...i,componentName:"Waitlist",mode:"modal"},children:(0,l.tZ)("div",{children:(0,l.tZ)(w,{})})})})}}}]);