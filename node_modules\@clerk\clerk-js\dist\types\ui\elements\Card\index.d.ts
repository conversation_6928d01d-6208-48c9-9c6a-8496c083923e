export declare const Card: {
    Root: import("react").ForwardRefExoticComponent<Omit<Omit<import("../../primitives").FlexProps, "ref"> & import("react").RefAttributes<HTMLDivElement> & {
        elementDescriptor?: import("../../customizables/elementDescriptors").ElementDescriptor | Array<import("../../customizables/elementDescriptors").ElementDescriptor | undefined>;
        elementId?: import("../../customizables/elementDescriptors").ElementId;
        css?: never;
        sx?: import("../../styledSystem").ThemableCssProp;
    }, "ref"> & import("react").RefAttributes<HTMLDivElement>>;
    Content: import("react").ForwardRefExoticComponent<Omit<Omit<import("../../primitives").FlexProps, "ref"> & import("react").RefAttributes<HTMLDivElement> & {
        elementDescriptor?: import("../../customizables/elementDescriptors").ElementDescriptor | Array<import("../../customizables/elementDescriptors").ElementDescriptor | undefined>;
        elementId?: import("../../customizables/elementDescriptors").ElementId;
        css?: never;
        sx?: import("../../styledSystem").ThemableCssProp;
    }, "ref"> & import("react").RefAttributes<HTMLDivElement>>;
    Footer: import("react").ForwardRefExoticComponent<Omit<Omit<import("../../primitives").FlexProps, "ref"> & import("react").RefAttributes<HTMLDivElement> & {
        elementDescriptor?: import("../../customizables/elementDescriptors").ElementDescriptor | Array<import("../../customizables/elementDescriptors").ElementDescriptor | undefined>;
        elementId?: import("../../customizables/elementDescriptors").ElementId;
        css?: never;
        sx?: import("../../styledSystem").ThemableCssProp;
    } & {
        isProfileFooter?: boolean;
    }, "ref"> & import("react").RefAttributes<HTMLDivElement>>;
    Alert: import("react").MemoExoticComponent<(props: import("../../styledSystem").PropsOfComponent<typeof import("../Alert").Alert>) => import("@emotion/react/jsx-runtime").JSX.Element>;
    Action: (props: Omit<Omit<import("../../primitives").FlexProps, "ref"> & import("react").RefAttributes<HTMLDivElement> & {
        elementDescriptor?: import("../../customizables/elementDescriptors").ElementDescriptor | Array<import("../../customizables/elementDescriptors").ElementDescriptor | undefined>;
        elementId?: import("../../customizables/elementDescriptors").ElementId;
        css?: never;
        sx?: import("../../styledSystem").ThemableCssProp;
    }, "elementId"> & {
        elementId?: import("@clerk/types").CardActionId;
    }) => JSX.Element;
    ActionLink: (props: import("../../styledSystem").PropsOfComponent<typeof import("../RouterLink").RouterLink>) => JSX.Element;
    ActionText: (props: React.PropsWithChildren<any>) => JSX.Element;
    FooterLinks: import("react").MemoExoticComponent<() => JSX.Element | null>;
    ClerkAndPagesTag: import("react").MemoExoticComponent<import("react").ForwardRefExoticComponent<Omit<Omit<import("../../primitives").FlexProps, "ref"> & import("react").RefAttributes<HTMLDivElement> & {
        elementDescriptor?: import("../../customizables/elementDescriptors").ElementDescriptor | Array<import("../../customizables/elementDescriptors").ElementDescriptor | undefined>;
        elementId?: import("../../customizables/elementDescriptors").ElementId;
        css?: never;
        sx?: import("../../styledSystem").ThemableCssProp;
    } & {
        withFooterPages?: boolean;
        devModeNoticeSx?: import("../../styledSystem").ThemableCssProp;
        outerSx?: import("../../styledSystem").ThemableCssProp;
        withDevOverlay?: boolean;
    }, "ref"> & import("react").RefAttributes<HTMLDivElement>>>;
};
