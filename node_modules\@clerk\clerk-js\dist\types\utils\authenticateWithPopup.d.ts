import type { AuthenticateWithPopupParams, AuthenticateWithRedirectParams } from '@clerk/types';
import type { Clerk } from '../core/clerk';
export declare function _authenticateWithPopup(client: Clerk, reloadResource: 'signIn' | 'signUp', authenticateMethod: (params: AuthenticateWithRedirectParams, navigateCallback: (url: URL | string) => void) => Promise<void>, params: AuthenticateWithPopupParams & {
    unsafeMetadata?: SignUpUnsafeMetadata;
}, navigateCallback: (url: URL | string) => void): Promise<void>;
