"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["780"],{96061:function(t,e,o){o.r(e),o.d(e,{OrganizationSwitcher:()=>T});var i=o(79109),n=o(69144),r=o(2672),a=o(84045),c=o(11576),l=o(39541),s=o(12464),d=o(83799),u=o(42305),g=o(35973),p=o(62915),h=o(1151),z=o(26917),v=o(8969),m=o(53929),S=o(96519),w=o(24676);o(65223);var x=o(48774),f=o(81201),Z=o(77623),B=o(57484);let b=()=>{let{userInvitations:t,userSuggestions:e}=(0,d.eW)(B.AO),{ref:o}=(0,s.YD)({threshold:0,onChange:o=>{var i,n;o&&(t.hasNextPage?null===(i=t.fetchNext)||void 0===i||i.call(t):null===(n=e.fetchNext)||void 0===n||n.call(e))}});return{userInvitations:t,userSuggestions:e,ref:o}},I=t=>{let e=(0,r.useCardState)(),{userSuggestions:o}=(0,d.eW)({userSuggestions:B.AO.userSuggestions});return"accepted"===t.status?(0,i.tZ)(l.Text,{colorScheme:"secondary",localizationKey:(0,l.localizationKeys)("organizationSwitcher.suggestionsAcceptedLabel")}):(0,i.tZ)(l.Button,{elementDescriptor:l.descriptors.organizationSwitcherInvitationAcceptButton,textVariant:"buttonSmall",variant:"outline",colorScheme:"neutral",size:"sm",isLoading:e.isLoading,onClick:()=>e.runAsync(t.accept).then(t=>{var e;return null==o?void 0:null===(e=o.setData)||void 0===e?void 0:e.call(o,e=>(0,B.Sz)(t,e))}).catch(t=>(0,Z.S3)(t,[],e.setError)),localizationKey:(0,l.localizationKeys)("organizationSwitcher.action__suggestionsAccept")})},A=t=>{let e=(0,r.useCardState)();return(0,i.tZ)(l.Button,{elementDescriptor:l.descriptors.organizationSwitcherInvitationAcceptButton,textVariant:"buttonSmall",variant:"outline",colorScheme:"neutral",size:"xs",isLoading:e.isLoading,onClick:t.onAccept,localizationKey:(0,l.localizationKeys)("organizationSwitcher.action__invitationAccept")})},C=t=>{let{children:e,publicOrganizationData:o}=t;return(0,i.BX)(l.Flex,{align:"center",gap:2,sx:t=>({justifyContent:"space-between",padding:"".concat(t.space.$4," ").concat(t.space.$5)}),elementDescriptor:l.descriptors.organizationSwitcherPopoverInvitationActionsBox,children:[(0,i.tZ)(g.Z,{elementId:"organizationSwitcherListedOrganization",organization:o,sx:t=>({color:t.colors.$colorTextSecondary,":hover":{color:t.colors.$colorTextSecondary}})}),e]})},O=(0,r.withCardStateProvider)(t=>{var e;let{invitation:o,onOrganizationClick:n}=t,{accept:a,publicOrganizationData:s,status:u}=o,p=(0,r.useCardState)(),{getOrganization:h}=(0,d.cL)(),{organization:z}=(0,d.o8)(),{userInvitations:v}=(0,d.eW)({userInvitations:B.AO.userInvitations,userMemberships:B.AO.userMemberships}),{acceptedInvitations:m,setAcceptedInvitations:w}=(0,c.useAcceptedInvitations)(),f=null===(e=m.find(t=>t.invitation.id===o.id))||void 0===e?void 0:e.organization;return"accepted"===u?(null==f?void 0:f.id)&&(null==z?void 0:z.id)===f.id?null:(0,i.tZ)(x.K,{elementDescriptor:l.descriptors.organizationSwitcherPreviewButton,icon:S.nR,onClick:f?()=>n(f):void 0,role:"menuitem",children:(0,i.tZ)(g.Z,{elementId:"organizationSwitcherListedOrganization",organization:s,sx:t=>({color:t.colors.$colorTextSecondary,":hover":{color:t.colors.$colorTextSecondary}})})}):(0,i.tZ)(C,{publicOrganizationData:s,children:(0,i.tZ)(A,{onAccept:()=>p.runAsync(async()=>[await a(),await h(s.id)]).then(t=>{var e;let[o,i]=t;null==v||null===(e=v.setData)||void 0===e||e.call(v,t=>(0,B.Sz)(o,t,"negative")),w(t=>[...t,{organization:i,invitation:o}])}).catch(t=>(0,Z.S3)(t,[],p.setError))})})}),y=t=>{let{showBorder:e,...o}=t;return(0,i.tZ)(u.eX,{role:"menu",...o})},$=(0,r.withCardStateProvider)(t=>(0,i.tZ)(C,{publicOrganizationData:t.publicOrganizationData,children:(0,i.tZ)(I,{...t})})),P=t=>{var e,o;let{onOrganizationClick:n}=t,{ref:r,userSuggestions:a,userInvitations:c}=b(),s=c.isLoading||a.isLoading,d=c.hasNextPage||a.hasNextPage,u=(null===(e=c.data)||void 0===e?void 0:e.filter(t=>!!t))||[],g=(null===(o=a.data)||void 0===o?void 0:o.filter(t=>!!t))||[],p=u.length>0||g.length>0;return p||s?(0,i.tZ)(y,{showBorder:p||s,elementDescriptor:l.descriptors.organizationSwitcherPopoverInvitationActions,children:(0,i.BX)(l.Box,{sx:t=>({maxHeight:"calc(4 * ".concat(t.sizes.$17," + 4px)"),overflowY:"auto",...f.common.unstyledScrollbar(t)}),children:[null==u?void 0:u.map(t=>(0,i.tZ)(O,{invitation:t,onOrganizationClick:n},t.id)),!c.hasNextPage&&(null==g?void 0:g.map(t=>(0,i.tZ)($,{...t},t.id))),(d||s)&&(0,i.tZ)(v.q7,{ref:r})]})}):null},D=()=>{let{userMemberships:t}=(0,d.eW)({userMemberships:B.AO.userMemberships}),{ref:e}=(0,s.YD)({threshold:0,onChange:e=>{if(e&&t.hasNextPage){var o;null===(o=t.fetchNext)||void 0===o||o.call(t)}}});return{userMemberships:t,ref:e}},k=t=>{let{onPersonalWorkspaceClick:e,onOrganizationClick:o}=t,{hidePersonal:n}=(0,c.useOrganizationSwitcherContext)(),{organization:r}=(0,d.o8)(),{ref:a,userMemberships:s}=D(),{user:u}=(0,d.aF)(),h=((s.count||0)>0&&s.data||[]).map(t=>t.organization).filter(t=>t.id!==(null==r?void 0:r.id));if(!u)return null;let{username:z,primaryEmailAddress:m,primaryPhoneNumber:w,...Z}=u,{isLoading:B,hasNextPage:b}=s;return(0,i.BX)(l.Box,{sx:t=>({maxHeight:"calc((4 * ".concat(t.sizes.$17,") + 4px)"),overflowY:"auto","> button,div":{border:"0 solid ".concat(t.colors.$neutralAlpha100)},">:not([hidden])~:not([hidden])":{borderTopWidth:"1px",borderBottomWidth:"0"},...f.common.unstyledScrollbar(t)}),role:"group","aria-label":n?"List of all organization memberships":"List of all accounts",children:[r&&!n&&(0,i.tZ)(x.K,{elementDescriptor:l.descriptors.organizationSwitcherPreviewButton,elementId:l.descriptors.organizationSwitcherPreviewButton.setId("personal"),icon:S.nR,onClick:e,role:"menuitem",children:(0,i.tZ)(p.g,{user:Z,mainIdentifierVariant:"buttonLarge",title:(0,l.localizationKeys)("organizationSwitcher.personalWorkspace")})}),h.map(t=>(0,i.tZ)(x.K,{elementDescriptor:l.descriptors.organizationSwitcherPreviewButton,elementId:l.descriptors.organizationSwitcherPreviewButton.setId("organization"),icon:S.nR,onClick:()=>o(t),role:"menuitem",sx:t=>({border:"0 solid ".concat(t.colors.$neutralAlpha100)}),children:(0,i.tZ)(g.Z,{elementId:"organizationSwitcherListedOrganization",organization:t})},t.id)),(b||B)&&(0,i.tZ)(v.q7,{ref:a})]})},_=t=>{let{onCreateOrganizationClick:e}=t,{user:o}=(0,d.aF)();return(null==o?void 0:o.createOrganizationEnabled)?(0,i.tZ)(u.aU,{elementDescriptor:l.descriptors.organizationSwitcherPopoverActionButton,elementId:l.descriptors.organizationSwitcherPopoverActionButton.setId("createOrganization"),iconBoxElementDescriptor:l.descriptors.organizationSwitcherPopoverActionButtonIconBox,iconBoxElementId:l.descriptors.organizationSwitcherPopoverActionButtonIconBox.setId("createOrganization"),iconElementDescriptor:l.descriptors.organizationSwitcherPopoverActionButtonIcon,iconElementId:l.descriptors.organizationSwitcherPopoverActionButtonIcon.setId("createOrganization"),icon:S.mm,label:(0,l.localizationKeys)("organizationSwitcher.action__createOrganization"),onClick:e,sx:t=>({padding:"".concat(t.space.$5," ").concat(t.space.$5)}),iconSx:t=>({width:t.sizes.$9,height:t.sizes.$6}),iconBoxSx:t=>({width:t.sizes.$9,height:t.sizes.$6}),spinnerSize:"sm"}):null},L=t=>{let{onCreateOrganizationClick:e,onPersonalWorkspaceClick:o,onOrganizationClick:n}=t;return(0,i.BX)(i.HY,{children:[(0,i.tZ)(P,{onOrganizationClick:n}),(0,i.tZ)(k,{onPersonalWorkspaceClick:o,onOrganizationClick:n}),(0,i.tZ)(_,{onCreateOrganizationClick:e})]})},R=n.forwardRef((t,e)=>{let{close:o,...n}=t,a=()=>null==o?void 0:o(!1),s=(0,r.useCardState)(),{__experimental_asStandalone:v}=(0,c.useOrganizationSwitcherContext)(),{openOrganizationProfile:x,openCreateOrganization:f}=(0,d.cL)(),{organization:Z}=(0,d.o8)(),{isLoaded:B,setActive:b}=(0,d.eW)(),I=(0,w.useRouter)(),{hidePersonal:A,__unstable_manageBillingUrl:C,__unstable_manageBillingLabel:O,__unstable_manageBillingMembersLimit:y,createOrganizationMode:$,organizationProfileMode:P,afterLeaveOrganizationUrl:D,afterCreateOrganizationUrl:k,navigateCreateOrganization:_,navigateOrganizationProfile:R,afterSelectOrganizationUrl:K,afterSelectPersonalUrl:W,organizationProfileProps:X,skipInvitationScreen:T,hideSlug:F}=(0,c.useOrganizationSwitcherContext)(),{user:N}=(0,d.aF)();if(!N)return null;let{username:M,primaryEmailAddress:U,primaryPhoneNumber:Y,...j}=N;if(!B)return null;let H=()=>(a(),"navigation"===P)?R():x({...X,afterLeaveOrganizationUrl:D,__unstable_manageBillingUrl:C,__unstable_manageBillingLabel:O,__unstable_manageBillingMembersLimit:y}),V=(0,i.tZ)(u.U8,{elementDescriptor:l.descriptors.organizationSwitcherPopoverActionButton,elementId:l.descriptors.organizationSwitcherPopoverActionButton.setId("manageOrganization"),iconBoxElementDescriptor:l.descriptors.organizationSwitcherPopoverActionButtonIconBox,iconBoxElementId:l.descriptors.organizationSwitcherPopoverActionButtonIconBox.setId("manageOrganization"),iconElementDescriptor:l.descriptors.organizationSwitcherPopoverActionButtonIcon,iconElementId:l.descriptors.organizationSwitcherPopoverActionButtonIcon.setId("manageOrganization"),icon:S.tc,label:(0,l.localizationKeys)("organizationSwitcher.action__manageOrganization"),onClick:()=>H(),trailing:(0,i.tZ)(E,{})}),q=(0,i.tZ)(u.U8,{icon:S.Nj,label:(0,z.OR)(O)||"Upgrade",onClick:()=>I.navigate((0,z.OR)(C))});return(0,i.tZ)(m.r,{elementDescriptor:l.descriptors.organizationSwitcherPopoverRootBox,children:(0,i.BX)(h.f.Root,{elementDescriptor:l.descriptors.organizationSwitcherPopoverCard,ref:e,role:"dialog","aria-label":"".concat(null==Z?void 0:Z.name," is active"),shouldEntryAnimate:!v,...n,children:[(0,i.tZ)(h.f.Content,{elementDescriptor:l.descriptors.organizationSwitcherPopoverMain,children:(0,i.BX)(u.eX,{elementDescriptor:l.descriptors.organizationSwitcherPopoverActions,role:"menu",children:[Z?C?(0,i.BX)(i.HY,{children:[(0,i.tZ)(g.Z,{elementId:"organizationSwitcherActiveOrganization",organization:Z,user:N,fetchRoles:!0,mainIdentifierVariant:"buttonLarge",sx:t=>({padding:"".concat(t.space.$4," ").concat(t.space.$5)})}),(0,i.tZ)(u.eX,{role:"menu",sx:t=>({borderBottomWidth:t.borderWidths.$normal,borderBottomStyle:t.borderStyles.$solid,borderBottomColor:t.colors.$neutralAlpha100}),children:(0,i.BX)(l.Flex,{justify:"between",sx:t=>({marginLeft:t.space.$12,padding:"0 ".concat(t.space.$5," ").concat(t.space.$4),gap:t.space.$2}),children:[V,q]})})]}):(0,i.BX)(l.Flex,{justify:"between",align:"center",sx:t=>({width:"100%",paddingRight:t.space.$5}),children:[(0,i.tZ)(g.Z,{elementId:"organizationSwitcherActiveOrganization",organization:Z,user:N,fetchRoles:!0,mainIdentifierVariant:"buttonLarge",sx:t=>({padding:"".concat(t.space.$4," ").concat(t.space.$5)})}),(0,i.tZ)(u.eX,{role:"menu",children:V})]}):!A&&(0,i.tZ)(p.g,{user:j,sx:t=>({padding:"".concat(t.space.$4," ").concat(t.space.$5),width:"100%"}),title:(0,l.localizationKeys)("organizationSwitcher.personalWorkspace")}),(0,i.tZ)(L,{onCreateOrganizationClick:()=>(a(),"navigation"===$)?_():f({afterCreateOrganizationUrl:k,skipInvitationScreen:T,hideSlug:F}),onPersonalWorkspaceClick:()=>s.runAsync(()=>b({organization:null,redirectUrl:W(N)})).then(a),onOrganizationClick:t=>s.runAsync(()=>b({organization:t,redirectUrl:K(t)})).then(a)})]})}),(0,i.tZ)(h.f.Footer,{elementDescriptor:l.descriptors.organizationSwitcherPopoverFooter})]})})}),E=(0,v.Ci)(t=>{var e;let{sx:o}=t,{organizationSettings:n}=(0,c.useEnvironment)(),r=null==n?void 0:null===(e=n.domains)||void 0===e?void 0:e.enabled,{membershipRequests:a}=(0,d.o8)({membershipRequests:r||void 0});return(null==a?void 0:a.count)?(0,i.tZ)(v.dN,{notificationCount:a.count,containerSx:o}):null},{permission:"org:sys_memberships:manage"}),K=(0,o(77711).C)((0,n.forwardRef)((t,e)=>{let{sx:o,...n}=t,{user:r}=(0,d.aF)(),{organization:a}=(0,d.o8)(),{hidePersonal:s}=(0,c.useOrganizationSwitcherContext)();if(!r)return null;let{username:u,primaryEmailAddress:h,primaryPhoneNumber:z,...v}=r;return(0,i.BX)(l.Button,{elementDescriptor:l.descriptors.organizationSwitcherTrigger,elementId:l.descriptors.organizationSwitcherTrigger.setId(a?"organization":"personal"),variant:"ghost",colorScheme:"neutral",hoverAsFocus:!0,focusRing:!1,sx:[t=>({padding:"".concat(t.space.$1," ").concat(t.space.$2),position:"relative"}),o],ref:e,"aria-label":"".concat(t.isOpen?"Close":"Open"," organization switcher"),"aria-expanded":t.isOpen,"aria-haspopup":"dialog",...n,children:[a&&(0,i.tZ)(g.Z,{elementId:"organizationSwitcherTrigger",gap:3,size:"xs",fetchRoles:!0,organization:a,sx:{maxWidth:"30ch"}}),!a&&(0,i.tZ)(p.g,{size:"xs",gap:3,user:v,showAvatar:!s,sx:t=>({color:t.colors.$colorTextSecondary}),title:s?(0,l.localizationKeys)("organizationSwitcher.notSelected"):(0,l.localizationKeys)("organizationSwitcher.personalWorkspace")}),(0,i.tZ)(W,{}),(0,i.tZ)(l.Icon,{elementDescriptor:l.descriptors.organizationSwitcherTriggerIcon,icon:S._M,sx:t=>({marginLeft:"".concat(t.space.$2)})})]})})),W=()=>{var t;let{userInvitations:e,userSuggestions:o}=(0,d.eW)(B.AO),{organizationSettings:n}=(0,c.useEnvironment)(),r=(0,v.N2)({permission:"org:sys_memberships:manage"}),a=null==n?void 0:null===(t=n.domains)||void 0===t?void 0:t.enabled,{membershipRequests:l}=(0,d.o8)({membershipRequests:a&&r||void 0}),s=(e.count||0)+(o.count||0)+((null==l?void 0:l.count)||0);return s?(0,i.tZ)(v.dN,{containerSx:t=>({position:"absolute",top:"-".concat(t.space.$2),right:"-".concat(t.space.$2)}),notificationCount:s}):null},X=(0,r.withFloatingTree)(t=>{let{children:e}=t,{defaultOpen:o}=(0,c.useOrganizationSwitcherContext)(),{floating:r,reference:l,styles:d,toggle:u,isOpen:g,nodeId:p,context:h}=(0,s.Sv)({defaultOpen:o,placement:"bottom-start",offset:8}),z=(0,n.useId)();return(0,i.BX)(i.HY,{children:[(0,i.tZ)(K,{ref:l,onClick:u,isOpen:g,"aria-controls":g?z:void 0,"aria-expanded":g}),(0,i.tZ)(a.J,{nodeId:p,context:h,isOpen:g,children:(0,n.cloneElement)(e,{id:z,close:u,ref:r,style:d})})]})}),T=(0,c.withCoreUserGuard)((0,r.withCardStateProvider)(()=>{let{__experimental_asStandalone:t}=(0,c.useOrganizationSwitcherContext)();return(0,i.tZ)(l.Flow.Root,{flow:"organizationSwitcher",sx:{display:"inline-flex"},children:(0,i.tZ)(c.AcceptedInvitationsProvider,{children:t?(0,i.tZ)(R,{close:"function"==typeof t?t:void 0}):(0,i.tZ)(X,{children:(0,i.tZ)(R,{})})})})}))},57484:function(t,e,o){o.d(e,{AO:()=>i,Sz:()=>n});let i={userMemberships:{infinite:!0},userInvitations:{infinite:!0},userSuggestions:{infinite:!0,status:["pending","accepted"]}},n=(t,e,o)=>{var i;if(void 0===e)return[{data:[t],total_count:1}];let n=(null==e?void 0:null===(i=e[e.length-1])||void 0===i?void 0:i.total_count)||1;return e.map(e=>{if(void 0===e)return e;let i=e.data.map(e=>e.id===t.id?{...t}:e);return{...e,data:i,total_count:"negative"===o?n-1:n}})}}}]);