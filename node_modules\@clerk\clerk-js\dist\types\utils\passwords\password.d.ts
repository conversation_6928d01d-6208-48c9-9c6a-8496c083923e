import type { PasswordSettingsData, ValidatePasswordCallbacks } from '@clerk/types';
export type UsePasswordConfig = PasswordSettingsData & {
    validatePassword: boolean;
};
export type UsePasswordCbs = {
    onValidationError?: (error: string | undefined) => void;
    onValidationSuccess?: () => void;
    onValidationWarning?: (warning: string) => void;
    onValidationInfo?: (info: string) => void;
    onValidationComplexity?: (b: boolean) => void;
};
export declare const createValidatePassword: (config: UsePasswordConfig, callbacks?: ValidatePasswordCallbacks) => (password: string, internalCallbacks?: ValidatePasswordCallbacks) => void;
