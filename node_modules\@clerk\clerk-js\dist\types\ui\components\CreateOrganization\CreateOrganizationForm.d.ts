import type { OrganizationResource } from '@clerk/types';
import type { LocalizationKey } from '../../localization';
type CreateOrganizationFormProps = {
    skipInvitationScreen: boolean;
    navigateAfterCreateOrganization: (organization: OrganizationResource) => Promise<unknown>;
    onCancel?: () => void;
    onComplete?: () => void;
    flow: 'default' | 'organizationList';
    startPage?: {
        headerTitle?: LocalizationKey;
        headerSubtitle?: LocalizationKey;
    };
    hideSlug?: boolean;
};
export declare const CreateOrganizationForm: (props: CreateOrganizationFormProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
