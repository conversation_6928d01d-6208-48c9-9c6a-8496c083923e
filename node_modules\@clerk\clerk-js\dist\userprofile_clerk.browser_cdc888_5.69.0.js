"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["470"],{9140:function(e,t,r){r.d(t,{_:()=>b});var i=r(9109),o=r(3799),a=r(9144),l=r(2672),n=r(431),s=r(9460),c=r(8487),d=r(8969),u=r(1576),h=r(1085),m=r(7623),p=r(207),f=r(4676),g=r(6917),y=r(9541),v=r(2464);let P=e=>{let{navigate:t}=(0,f.useRouter)(),{email:r,nextStep:o,onReset:n}=e,c=(0,l.useCardState)(),d=(0,u.useUserProfileContext)(),{startEnterpriseSSOLinkFlow:h}=(0,v.eq)(r);async function p(){await t(r.verification.externalVerificationRedirectURL?.href||"")}return a.useEffect(()=>{!function(){let{mode:e,componentName:t}=d;h({redirectUrl:"modal"===e?(0,g.bX)({url:window.location.href,componentName:t}):window.location.href}).then(()=>o()).catch(e=>(0,m.S3)(e,[],c.setError))}()},[]),(0,i.BX)(i.HY,{children:[(0,i.tZ)(y.Flex,{justify:"center",children:(0,i.tZ)(y.Button,{variant:"link",onClick:p,localizationKey:(0,y.localizationKeys)("userProfile.emailAddressPage.enterpriseSSOLink.formButton")})}),(0,i.tZ)(s.K,{children:(0,i.tZ)(y.Button,{variant:"ghost",localizationKey:(0,y.localizationKeys)("userProfile.formButtonReset"),elementDescriptor:y.descriptors.formButtonReset,onClick:n})})]})};var Z=r(2786);let b=(0,l.withCardStateProvider)(e=>{let{emailId:t,onSuccess:r,onReset:f,disableAutoFocus:g=!1}=e,y=(0,l.useCardState)(),{user:v}=(0,o.aF)(),b=(0,u.useEnvironment)(),C=(0,o.WZ)(e=>v?.createEmailAddress({email:e})),K=a.useRef(v?.emailAddresses.find(e=>e.id===t)),x=S(K.current,b),R=(0,d.a2)({defaultStep:+!!K.current,onNextStep:()=>y.setError(void 0)}),_=(0,m.Yp)("emailAddress","",{type:"email",label:(0,h.u1)("formFieldLabel__emailAddress"),placeholder:(0,h.u1)("formFieldInputPlaceholder__emailAddress"),isRequired:!0}),B=_.value.length>1&&v?.username!==_.value,w=async e=>{if(e.preventDefault(),v)return C(_.value).then(e=>{K.current=e,R.nextStep()}).catch(e=>(0,m.S3)(e,[_],y.setError))},k=z(x);return(0,i.BX)(d.en,{...R.props,children:[(0,i.tZ)(c.Y,{headerTitle:e.title||(0,h.u1)("userProfile.emailAddressPage.title"),headerSubtitle:e.subtitle||(0,h.u1)("userProfile.emailAddressPage.formHint"),children:(0,i.BX)(n.l.Root,{onSubmit:w,children:[(0,i.tZ)(n.l.ControlRow,{elementId:_.id,children:(0,i.tZ)(n.l.PlainInput,{..._.props,autoFocus:!g})}),(0,i.tZ)(s.A,{submitLabel:(0,h.u1)("userProfile.formButtonPrimary__add"),isDisabled:!B,onReset:f})]})}),(0,i.BX)(c.Y,{headerTitle:(0,h.u1)("userProfile.emailAddressPage.verifyTitle"),headerSubtitle:(0,h.u1)(`${k}.formSubtitle`,{identifier:K.current?.emailAddress}),children:["email_link"===x&&(0,i.tZ)(Z.K,{nextStep:r,email:K.current,onReset:f}),"email_code"===x&&(0,i.tZ)(p.H,{nextStep:r,identification:K.current,identifier:K.current?.emailAddress,prepareVerification:()=>K.current?.prepareVerification({strategy:"email_code"}),onReset:f}),"enterprise_sso"===x&&(0,i.tZ)(P,{nextStep:r,email:K.current,onReset:f})]})]})}),z=e=>{switch(e){case"email_code":return"userProfile.emailAddressPage.emailCode";case"enterprise_sso":return"userProfile.emailAddressPage.enterpriseSSOLink";case"email_link":return"userProfile.emailAddressPage.emailLink";default:throw Error(`Unsupported strategy for email verification: ${e}`)}},S=(e,t)=>e?.matchesSsoConnection?"enterprise_sso":!function(e){let{userSettings:t}=e,{email_address:r}=t.attributes;return!!(r?.enabled&&r?.verifications.includes("email_link"))}(t)?"email_code":"email_link"},207:function(e,t,r){r.d(t,{H:()=>u});var i=r(9109),o=r(9144),a=r(2667),l=r(2672),n=r(431),s=r(9460),c=r(9541),d=r(7623);let u=e=>{let t=(0,l.useCardState)(),{nextStep:r,identification:u,identifier:h,onReset:m,prepareVerification:p}=e,f=()=>p?.()?.catch(e=>d.S3(e,[],t.setError)),g=(0,a.e3)({onCodeEntryFinished:(e,t,r)=>{u?.attemptVerification({code:e}).then(()=>t()).catch(r)},onResendCodeClicked:f,onResolve:r});return o.useEffect(()=>{f()},[]),(0,i.BX)(i.HY,{children:[(0,i.tZ)(n.l.OTPInput,{...g,label:(0,c.localizationKeys)("userProfile.emailAddressPage.emailCode.formTitle"),description:(0,c.localizationKeys)("userProfile.emailAddressPage.emailCode.formSubtitle",{identifier:h}),resendButton:(0,c.localizationKeys)("userProfile.emailAddressPage.emailCode.resendButton"),centerAlign:!1}),(0,i.BX)(s.K,{children:[(0,i.tZ)(c.Button,{isLoading:g.isLoading,localizationKey:(0,c.localizationKeys)("formButtonPrimary__verify"),elementDescriptor:c.descriptors.formButtonPrimary,onClick:g.onFakeContinue}),(0,i.tZ)(c.Button,{variant:"ghost",isDisabled:g.isLoading,localizationKey:(0,c.localizationKeys)("userProfile.formButtonReset"),elementDescriptor:c.descriptors.formButtonReset,onClick:m})]})]})}},2786:function(e,t,r){r.d(t,{K:()=>p,M:()=>f});var i=r(9109),o=r(9144),a=r(2672),l=r(9460),n=r(5495),s=r(8969),c=r(6749),d=r(1576),u=r(9541),h=r(2464),m=r(7623);let p=e=>{let{email:t,nextStep:r,onReset:s}=e,p=(0,a.useCardState)(),f=(0,d.useUserProfileContext)(),{startEmailLinkFlow:g}=(0,h.E2)(t),{displayConfig:y}=(0,d.useEnvironment)();function v(){let{routing:e}=f,t="virtual"===e?y.userProfileUrl:"";g({redirectUrl:(0,c.Uu)({ctx:f,baseUrl:t,intent:"profile"})}).then(()=>r()).catch(e=>(0,m.S3)(e,[],p.setError))}return o.useEffect(()=>{v()},[]),(0,i.BX)(i.HY,{children:[(0,i.tZ)(n.V,{resendButton:(0,u.localizationKeys)("userProfile.emailAddressPage.emailLink.resendButton"),onResendCodeClicked:v}),(0,i.tZ)(l.K,{children:(0,i.tZ)(u.Button,{variant:"ghost",localizationKey:(0,u.localizationKeys)("userProfile.formButtonReset"),elementDescriptor:u.descriptors.formButtonReset,onClick:s})})]})},f=()=>(0,i.tZ)(s.Ej,{title:(0,u.localizationKeys)("signUp.emailLink.verifiedSwitchTab.title"),subtitle:(0,u.localizationKeys)("signUp.emailLink.verifiedSwitchTab.subtitle"),status:"verified"})},6871:function(e,t,r){r.r(t),r.d(t,{UserProfile:()=>tB,UserProfileModal:()=>tw});var i=r(9109),o=r(9144),a=r(2672),l=r(5579),n=r(3394),s=r(1673),c=r(1576),d=r(9541),u=r(4676),h=r(1085);let m=e=>{let{pages:t}=(0,c.useUserProfileContext)();return(0,i.BX)(l.Uh,{contentRef:e.contentRef,children:[(0,i.tZ)(l.l2,{title:(0,h.u1)("userProfile.navbar.title"),description:(0,h.u1)("userProfile.navbar.description"),routes:t.routes,contentRef:e.contentRef}),e.children]})};var p=r(4995),f=r(2264),g=r(5515),y=r(3799),v=r(4455),P=r(2654),Z=r(9655),b=r(3009),z=r(6917),S=r(8969),C=r(9805),K=r(4709),x=r(2464),R=r(7623);let _=e=>{let{strategy:t}=e,r=(0,a.useCardState)(),{user:o}=(0,y.aF)(),{navigate:l}=(0,u.useRouter)(),{strategyToDisplayData:n}=(0,x.vO)(),{additionalOAuthScopes:s,componentName:h,mode:m}=(0,c.useUserProfileContext)(),p="modal"===m,f=(0,y.WZ)(()=>{let e=t.replace("oauth_",""),r=p?(0,z.bX)({url:window.location.href,componentName:h,socialProvider:e}):window.location.href,i=s?s[e]:[];return o?.createExternalAccount({strategy:t,redirectUrl:r,additionalScopes:i})}),g=n[t].iconUrl?(0,i.tZ)(d.Image,{isLoading:r.loadingMetadata===t,isDisabled:r.isLoading,elementDescriptor:d.descriptors.providerIcon,elementId:d.descriptors.providerIcon.setId(n[t].id),src:n[t].iconUrl,alt:`Connect ${n[t].name} account`,sx:e=>({width:e.sizes.$4})}):(0,i.tZ)(S.e_,{id:n[t].id,value:n[t].name,isLoading:r.loadingMetadata===t,isDisabled:r.isLoading});return(0,i.tZ)(Z.zd.ActionMenuItem,{id:n[t].id,onClick:()=>{if(o)return r.setLoading(t),f().then(e=>{e&&e.verification?.externalVerificationRedirectURL&&((0,R._v)(2e3).then(()=>r.setIdle(t)),l(e.verification.externalVerificationRedirectURL.href))}).catch(e=>{(0,R.S3)(e,[],r.setError),r.setIdle(t)})},isDisabled:r.isLoading,variant:"ghost",isLoading:r.loadingMetadata===t,focusRing:!1,closeAfterClick:!1,localizationKey:(0,d.localizationKeys)("userProfile.connectedAccountPage.socialButtonsBlockButton",{provider:n[t].name}),sx:e=>({justifyContent:"start",gap:e.space.$2}),leftIcon:g},t)},B=({onClick:e})=>{let{user:t}=(0,y.aF)(),{strategies:r}=(0,x.vO)(),o=r.filter(e=>e.startsWith("oauth")),a=t?.verifiedExternalAccounts.map(e=>`oauth_${e.provider}`),l=o.filter(e=>!a.includes(e));return 0===l.length?null:(0,i.tZ)(Z.zd.ActionMenu,{triggerLocalizationKey:(0,d.localizationKeys)("userProfile.start.connectedAccountsSection.primaryButton"),id:"connectedAccounts",onClick:e,children:l.map(e=>(0,i.tZ)(_,{strategy:e},e))})},w=e=>{let{onSuccess:t,onReset:r}=e,{user:a}=(0,y.aF)(),{emailId:l}=e,n=a?.emailAddresses.find(e=>e.id===l),s=o.useRef(n?.emailAddress),c=n?.verification?.status==="verified"?(0,d.localizationKeys)("userProfile.emailAddressPage.removeResource.messageLine2"):void 0;return s.current?(0,i.tZ)(S.LE,{title:(0,d.localizationKeys)("userProfile.emailAddressPage.removeResource.title"),messageLine1:(0,d.localizationKeys)("userProfile.emailAddressPage.removeResource.messageLine1",{identifier:s.current}),messageLine2:c,successMessage:(0,d.localizationKeys)("userProfile.emailAddressPage.removeResource.successMessage",{emailAddress:s.current}),deleteResource:()=>Promise.resolve(n?.destroy()),onSuccess:t,onReset:r}):null},k=e=>{let{phoneId:t,onSuccess:r,onReset:a}=e,{user:l}=(0,y.aF)(),n=l?.phoneNumbers.find(e=>e.id===t),s=o.useRef(n?.phoneNumber),c=n?.verification?.status==="verified"?(0,d.localizationKeys)("userProfile.phoneNumberPage.removeResource.messageLine2"):void 0;return s.current?(0,i.tZ)(S.LE,{title:(0,d.localizationKeys)("userProfile.phoneNumberPage.removeResource.title"),messageLine1:(0,d.localizationKeys)("userProfile.phoneNumberPage.removeResource.messageLine1",{identifier:s.current}),messageLine2:c,successMessage:(0,d.localizationKeys)("userProfile.phoneNumberPage.removeResource.successMessage",{phoneNumber:s.current}),deleteResource:()=>Promise.resolve(n?.destroy()),onSuccess:r,onReset:a}):null},I=e=>{let{accountId:t,onSuccess:r,onReset:a}=e,{user:l}=(0,y.aF)(),n=l?.externalAccounts.find(e=>e.id===t),s=o.useRef(n?.provider),{providerToDisplayData:c}=(0,x.vO)();return s.current?(0,i.tZ)(S.LE,{title:(0,d.localizationKeys)("userProfile.connectedAccountPage.removeResource.title"),messageLine1:(0,d.localizationKeys)("userProfile.connectedAccountPage.removeResource.messageLine1",{identifier:c[s.current]?.name}),messageLine2:(0,d.localizationKeys)("userProfile.connectedAccountPage.removeResource.messageLine2"),successMessage:(0,d.localizationKeys)("userProfile.connectedAccountPage.removeResource.successMessage",{connectedAccount:c[s.current]?.name}),deleteResource:()=>Promise.resolve(n?.destroy()),onSuccess:r,onReset:a}):null},T=e=>{let{user:t}=(0,y.aF)(),{walletId:r,onSuccess:a,onReset:l}=e,n=t?.web3Wallets.find(e=>e.id===r),s=o.useRef(n?.web3Wallet),c=n?.verification?.status==="verified"?(0,d.localizationKeys)("userProfile.web3WalletPage.removeResource.messageLine2"):void 0;return s.current?(0,i.tZ)(S.LE,{title:(0,d.localizationKeys)("userProfile.web3WalletPage.removeResource.title"),messageLine1:(0,d.localizationKeys)("userProfile.web3WalletPage.removeResource.messageLine1",{identifier:s.current}),messageLine2:c,successMessage:(0,d.localizationKeys)("userProfile.web3WalletPage.removeResource.successMessage",{web3Wallet:s.current}),deleteResource:()=>Promise.resolve(n?.destroy()),onSuccess:a,onReset:l}):null},A=e=>{let{user:t}=(0,y.aF)(),{phoneId:r,onSuccess:a,onReset:l}=e,n=t?.phoneNumbers.find(e=>e.id===r),s=o.useRef(n?.phoneNumber);return s.current?(0,i.tZ)(S.LE,{title:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.removeResource.title"),messageLine1:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.removeResource.messageLine1",{identifier:s.current}),messageLine2:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.removeResource.messageLine2"),successMessage:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.removeResource.successMessage",{mfaPhoneCode:s.current}),deleteResource:()=>Promise.resolve(n?.setReservedForSecondFactor({reserved:!1})),onSuccess:a,onReset:l}):null},X=e=>{let{onSuccess:t,onReset:r}=e,{user:o}=(0,y.aF)();return o?(0,i.tZ)(S.LE,{title:(0,d.localizationKeys)("userProfile.mfaTOTPPage.removeResource.title"),messageLine1:(0,d.localizationKeys)("userProfile.mfaTOTPPage.removeResource.messageLine1"),messageLine2:(0,d.localizationKeys)("userProfile.mfaTOTPPage.removeResource.messageLine2"),successMessage:(0,d.localizationKeys)("userProfile.mfaTOTPPage.removeResource.successMessage"),deleteResource:o.disableTOTP,onSuccess:t,onReset:r}):null},L=e=>{let{onSuccess:t,onReset:r,passkey:o}=e;return(0,i.tZ)(S.LE,{title:(0,d.localizationKeys)("userProfile.passkeyScreen.removeResource.title"),messageLine1:(0,d.localizationKeys)("userProfile.passkeyScreen.removeResource.messageLine1",{name:o.name}),deleteResource:o.delete,onSuccess:t,onReset:r})},$=e=>{let{close:t}=(0,K.XC)();return(0,i.tZ)(I,{onSuccess:t,onReset:t,...e})},F=["external_account_missing_refresh_token","oauth_fetch_user_error","oauth_token_exchange_error","external_account_email_address_verification_required"],E=(0,a.withCardStateProvider)(({shouldAllowCreation:e=!0})=>{let{user:t}=(0,y.aF)(),r=(0,a.useCardState)(),l=!!t?.externalAccounts?.length,[n,s]=(0,o.useState)(null);if(!t||!e&&!l)return null;let c=[...t.verifiedExternalAccounts,...t.unverifiedExternalAccounts.filter(e=>e.verification?.error)];return(0,i.BX)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.connectedAccountsSection.title"),centered:!1,id:"connectedAccounts",children:[(0,i.tZ)(v.Z.Alert,{children:r.error}),(0,i.BX)(C.a.Root,{value:n,onChange:s,children:[(0,i.tZ)(Z.zd.ItemList,{id:"connectedAccounts",children:c.map(e=>(0,i.tZ)(D,{account:e},e.id))}),e&&(0,i.tZ)(B,{onClick:()=>s(null)})]})]})}),D=({account:e})=>{let{additionalOAuthScopes:t,componentName:r,mode:l}=(0,c.useUserProfileContext)(),{navigate:n}=(0,u.useRouter)(),{user:s}=(0,y.aF)(),h=(0,a.useCardState)(),m=e.id,p="modal"===l,f=p?(0,z.bX)({url:window.location.href,componentName:r}):window.location.href,g=(0,y.WZ)(()=>s?.createExternalAccount({strategy:e.verification.strategy,redirectUrl:f,additionalScopes:K}));if(!s)return null;let{providerToDisplayData:v}=(0,x.vO)(),P=e.username||e.emailAddress,b=e.verification?.error?.longMessage,K=function(e,t){if(!t)return[];let r=t[e.provider]||[],i=e.approvedScopes.split(" ");return 0===r.filter(e=>!i.includes(e)).length?[]:r}(e,t),_=K.length>0&&""!=e.approvedScopes,B=F.includes(e.verification?.error?.code||"")||_,w=B?(0,d.localizationKeys)("userProfile.start.connectedAccountsSection.subtitle__disconnected"):b,k=async()=>{let t=p?(0,z.bX)({url:window.location.href,componentName:r}):window.location.href;try{let r;(r=_?await e.reauthorize({additionalScopes:K,redirectUrl:t}):await g())&&await n(r.verification.externalVerificationRedirectURL?.href||"")}catch(e){(0,R.S3)(e,[],h.setError)}};return(0,i.BX)(o.Fragment,{children:[(0,i.BX)(Z.zd.Item,{id:"connectedAccounts",children:[(0,i.BX)(d.Flex,{sx:e=>({overflow:"hidden",gap:e.space.$2}),children:[(0,i.tZ)(()=>v[e.provider].iconUrl?(0,i.tZ)(d.Image,{elementDescriptor:[d.descriptors.providerIcon],elementId:d.descriptors.socialButtonsProviderIcon.setId(e.provider),alt:v[e.provider].name,src:v[e.provider].iconUrl,sx:e=>({width:e.sizes.$4,flexShrink:0})}):(0,i.tZ)(S.e_,{id:e.provider,value:v[e.provider].name}),{}),(0,i.tZ)(d.Box,{sx:{whiteSpace:"nowrap",overflow:"hidden"},children:(0,i.BX)(d.Flex,{gap:1,center:!0,children:[(0,i.tZ)(d.Text,{sx:e=>({color:e.colors.$colorText}),children:`${v[e.provider].name}`}),(0,i.tZ)(d.Text,{truncate:!0,as:"span",colorScheme:"secondary",children:P?`• ${P}`:""})]})})]}),(0,i.tZ)(O,{account:e})]}),B&&(0,i.BX)(d.Box,{sx:e=>({padding:`${e.sizes.$none} ${e.sizes.$none} ${e.sizes.$1x5} ${e.sizes.$8x5}`}),children:[(0,i.tZ)(d.Text,{colorScheme:"secondary",sx:e=>({paddingRight:e.sizes.$1x5,display:"inline-block"}),localizationKey:w}),(0,i.tZ)(d.Button,{sx:{display:"inline-block"},onClick:k,variant:"link",localizationKey:(0,d.localizationKeys)("userProfile.start.connectedAccountsSection.actionLabel__connectionFailed")})]}),e.verification?.error?.code&&!B&&(0,i.tZ)(d.Text,{colorScheme:"danger",sx:e=>({padding:`${e.sizes.$none} ${e.sizes.$1x5} ${e.sizes.$1x5} ${e.sizes.$8x5}`}),children:b}),(0,i.tZ)(C.a.Open,{value:`remove-${m}`,children:(0,i.tZ)(C.a.Card,{variant:"destructive",children:(0,i.tZ)($,{accountId:e.id})})})]},e.id)},O=({account:e})=>{let{open:t}=(0,K.XC)(),r=e.id,o=[{label:(0,d.localizationKeys)("userProfile.start.connectedAccountsSection.destructiveActionTitle"),isDestructive:!0,onClick:()=>t(`remove-${r}`)}].filter(e=>null!==e);return(0,i.tZ)(b.a,{actions:o})},N=e=>t=>t.id===e?-1:1,W=e=>e.defaultSecondFactor?-1:1;function M(e){let t=[];return Object.entries(e).forEach(([,e])=>{e.used_for_second_factor&&t.push(...e.second_factors)}),t}function U(e,t){let r=M(e);return t.totpEnabled&&(r=r.filter(e=>"totp"!==e)),(t.backupCodeEnabled||!t.twoFactorEnabled)&&(r=r.filter(e=>"backup_code"!==e)),r}function Y(e,t){if(!e)return[];let r=e.filter(e=>e.id===t),i=e.filter(e=>e.id!==t),o=i.filter(e=>e.verification?.status==="verified"),a=i.filter(e=>!!e.verification?.status&&e.verification?.status!=="verified"),l=i.filter(e=>!e.verification.status);return o.sort((e,t)=>e.id.localeCompare(t.id)),a.sort((e,t)=>e.verification?.expireAt&&t.verification?.expireAt?e.verification.expireAt.getTime()-t.verification.expireAt.getTime():0),[...r,...o,...a,...l]}var q=r(9140);let j=e=>{let{close:t}=(0,K.XC)();return(0,i.tZ)(w,{onSuccess:t,onReset:t,...e})},V=e=>{let{close:t}=(0,K.XC)();return(0,i.tZ)(q._,{onSuccess:t,onReset:t,...e})},H=({shouldAllowCreation:e=!0})=>{let{user:t}=(0,y.aF)();return(0,i.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.emailAddressesSection.title"),centered:!1,id:"emailAddresses",children:(0,i.tZ)(C.a.Root,{children:(0,i.BX)(Z.zd.ItemList,{id:"emailAddresses",children:[Y(t?.emailAddresses,t?.primaryEmailAddressId).map(e=>{let r=e.id;return(0,i.BX)(o.Fragment,{children:[(0,i.BX)(Z.zd.Item,{id:"emailAddresses",children:[(0,i.BX)(d.Flex,{sx:e=>({overflow:"hidden",gap:e.space.$1}),children:[(0,i.tZ)(d.Text,{sx:e=>({color:e.colors.$colorText}),truncate:!0,children:e.emailAddress}),t?.primaryEmailAddressId===r&&(0,i.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__primary")}),"verified"!==e.verification.status&&(0,i.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__unverified")})]}),(0,i.tZ)(Q,{email:e})]}),(0,i.tZ)(C.a.Open,{value:`remove-${r}`,children:(0,i.tZ)(C.a.Card,{variant:"destructive",children:(0,i.tZ)(j,{emailId:r})})}),(0,i.tZ)(C.a.Open,{value:`verify-${r}`,children:(0,i.tZ)(C.a.Card,{children:(0,i.tZ)(V,{emailId:r})})})]},e.emailAddress)}),e&&(0,i.BX)(i.HY,{children:[(0,i.tZ)(C.a.Trigger,{value:"add",children:(0,i.tZ)(Z.zd.ArrowButton,{id:"emailAddresses",localizationKey:(0,d.localizationKeys)("userProfile.start.emailAddressesSection.primaryButton")})}),(0,i.tZ)(C.a.Open,{value:"add",children:(0,i.tZ)(C.a.Card,{children:(0,i.tZ)(V,{})})})]})]})})})},Q=({email:e})=>{let t=(0,a.useCardState)(),{user:r}=(0,y.aF)(),{open:o}=(0,K.XC)(),l=e.id,n=r?.primaryEmailAddressId===l,s="verified"===e.verification.status,c=(0,y.WZ)(()=>r?.update({primaryEmailAddressId:l})),u=[n&&!s?{label:(0,d.localizationKeys)("userProfile.start.emailAddressesSection.detailsAction__primary"),onClick:()=>o(`verify-${l}`)}:null,!n&&s?{label:(0,d.localizationKeys)("userProfile.start.emailAddressesSection.detailsAction__nonPrimary"),onClick:()=>{c().catch(e=>(0,R.S3)(e,[],t.setError))}}:null,n||s?null:{label:(0,d.localizationKeys)("userProfile.start.emailAddressesSection.detailsAction__unverified"),onClick:()=>o(`verify-${l}`)},{label:(0,d.localizationKeys)("userProfile.start.emailAddressesSection.destructiveAction"),isDestructive:!0,onClick:()=>o(`remove-${l}`)}].filter(e=>null!==e);return(0,i.tZ)(b.a,{actions:u})},G=()=>{let{user:e}=(0,y.aF)(),t=e?.enterpriseAccounts.filter(({enterpriseConnection:e})=>e?.active);return t?.length?(0,i.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.enterpriseAccountsSection.title"),id:"enterpriseAccounts",centered:!1,children:(0,i.tZ)(Z.zd.ItemList,{id:"enterpriseAccounts",children:t.map(e=>(0,i.tZ)(J,{account:e},e.id))})}):null},J=({account:e})=>{let t=e.emailAddress,r=e?.enterpriseConnection?.name,o=e.verification?.error?.longMessage;return(0,i.BX)(Z.zd.Item,{id:"enterpriseAccounts",sx:e=>({gap:e.space.$2,justifyContent:"start"}),children:[(0,i.tZ)(ee,{account:e}),(0,i.tZ)(d.Box,{sx:{whiteSpace:"nowrap",overflow:"hidden"},children:(0,i.BX)(d.Flex,{gap:1,center:!0,children:[(0,i.tZ)(d.Text,{truncate:!0,colorScheme:"body",children:r}),(0,i.tZ)(d.Text,{truncate:!0,as:"span",colorScheme:"secondary",children:t?`• ${t}`:""}),o&&(0,i.tZ)(d.Badge,{colorScheme:"danger",localizationKey:(0,d.localizationKeys)("badge__requiresAction")})]})})]},e.id)},ee=({account:e})=>{let{provider:t,enterpriseConnection:r}=e,o=t.replace(/(oauth_|saml_)/,"").trim(),a=r?.name??o,l={elementDescriptor:[d.descriptors.providerIcon],alt:a,sx:e=>({width:e.sizes.$4}),elementId:d.descriptors.enterpriseButtonsProviderIcon.setId(e.provider)};return r?.logoPublicUrl?(0,i.tZ)(d.Image,{...l,src:r.logoPublicUrl}):(0,i.tZ)(S.e_,{id:o,value:a,"aria-label":`${a}'s icon`,elementDescriptor:[d.descriptors.providerIcon,d.descriptors.providerInitialIcon],elementId:d.descriptors.providerInitialIcon.setId(o)})};var et=r(431),er=r(9460),ei=r(8487),eo=r(207);let ea=(0,a.withCardStateProvider)(e=>{let{phoneId:t,onSuccess:r,onReset:a}=e,{user:l}=(0,y.aF)(),n=o.useRef(l?.phoneNumbers.find(e=>e.id===t)),s=(0,S.a2)({defaultStep:+!!n.current});return(0,i.BX)(S.en,{...s.props,children:[(0,i.tZ)(el,{resourceRef:n,title:(0,d.localizationKeys)("userProfile.phoneNumberPage.title"),onSuccess:s.nextStep,onReset:a}),(0,i.tZ)(en,{resourceRef:n,title:(0,d.localizationKeys)("userProfile.phoneNumberPage.verifyTitle"),onSuccess:r,onReset:a})]})}),el=e=>{let{title:t,onSuccess:r,onReset:o,onUseExistingNumberClick:l,resourceRef:n}=e,s=(0,a.useCardState)(),{user:c}=(0,y.aF)(),u=(0,y.WZ)((e,t)=>e.createPhoneNumber(t)),h=(0,R.Yp)("phoneNumber","",{type:"tel",label:(0,d.localizationKeys)("formFieldLabel__phoneNumber"),isRequired:!0}),m=h.value.length>1&&c?.username!==h.value,p=!!c?.phoneNumbers?.length&&l,f=async e=>{if(e.preventDefault(),c)return u(c,{phoneNumber:h.value}).then(e=>{n.current=e,r()}).catch(e=>(0,R.S3)(e,[h],s.setError))};return(0,i.tZ)(ei.Y,{headerTitle:t,gap:1,children:(0,i.BX)(et.l.Root,{gap:4,onSubmit:f,children:[(0,i.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)("userProfile.phoneNumberPage.infoText"),colorScheme:"secondary"}),(0,i.tZ)(et.l.ControlRow,{elementId:h.id,children:(0,i.tZ)(et.l.PhoneInput,{...h.props,autoFocus:!0})}),(0,i.BX)(d.Flex,{justify:p?"between":"end",children:[p&&(0,i.tZ)(d.Button,{variant:"ghost",localizationKey:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.backButton"),onClick:l}),(0,i.tZ)(er.A,{submitLabel:(0,d.localizationKeys)("userProfile.formButtonPrimary__add"),isDisabled:!m,onReset:o})]})]})})},en=e=>{let{title:t,onSuccess:r,resourceRef:o,onReset:a}=e;return(0,i.tZ)(ei.Y,{headerTitle:t,headerSubtitle:(0,d.localizationKeys)("userProfile.phoneNumberPage.verifySubtitle",{identifier:o.current?.phoneNumber}),children:(0,i.tZ)(eo.H,{nextStep:r,identification:o.current,identifier:o.current?.phoneNumber,prepareVerification:o.current?.prepareVerification,onReset:a})})},es=e=>{let{close:t}=(0,K.XC)();return(0,i.tZ)(k,{onSuccess:t,onReset:t,...e})},ec=e=>{let{close:t}=(0,K.XC)();return(0,i.tZ)(ea,{onSuccess:t,onReset:t,...e})},ed=({shouldAllowCreation:e=!0})=>{let{user:t}=(0,y.aF)(),r=!!t?.phoneNumbers?.length;return e||r?(0,i.tZ)(Z.zd.Root,{centered:!1,title:(0,d.localizationKeys)("userProfile.start.phoneNumbersSection.title"),id:"phoneNumbers",children:(0,i.tZ)(C.a.Root,{children:(0,i.BX)(Z.zd.ItemList,{id:"phoneNumbers",children:[Y(t?.phoneNumbers,t?.primaryPhoneNumberId).map(e=>{let r=e.id;return(0,i.BX)(o.Fragment,{children:[(0,i.BX)(Z.zd.Item,{id:"phoneNumbers",children:[(0,i.tZ)(d.Box,{sx:{whiteSpace:"nowrap",overflow:"hidden"},children:(0,i.BX)(d.Flex,{gap:2,center:!0,children:[(0,i.tZ)(d.Text,{sx:e=>({color:e.colors.$colorText}),children:(0,R.L_)(e.phoneNumber)}),t?.primaryPhoneNumberId===r&&(0,i.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__primary")}),"verified"!==e.verification.status&&(0,i.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__unverified")})]})}),(0,i.tZ)(eu,{phone:e})]}),(0,i.tZ)(C.a.Open,{value:`remove-${r}`,children:(0,i.tZ)(C.a.Card,{variant:"destructive",children:(0,i.tZ)(es,{phoneId:r})})}),(0,i.tZ)(C.a.Open,{value:`verify-${r}`,children:(0,i.tZ)(C.a.Card,{children:(0,i.tZ)(ec,{phoneId:r})})})]},r)}),e&&(0,i.BX)(i.HY,{children:[(0,i.tZ)(C.a.Trigger,{value:"add",children:(0,i.tZ)(Z.zd.ArrowButton,{id:"phoneNumbers",localizationKey:(0,d.localizationKeys)("userProfile.start.phoneNumbersSection.primaryButton")})}),(0,i.tZ)(C.a.Open,{value:"add",children:(0,i.tZ)(C.a.Card,{children:(0,i.tZ)(ec,{})})})]})]})})}):null},eu=({phone:e})=>{let t=(0,a.useCardState)(),{open:r}=(0,K.XC)(),{user:o}=(0,y.aF)(),l=e.id,n=(0,y.WZ)(t=>t.update({primaryPhoneNumberId:e.id}));if(!o)return null;let s=o.primaryPhoneNumberId===e.id,c="verified"===e.verification.status,u=[s&&!c?{label:(0,d.localizationKeys)("userProfile.start.phoneNumbersSection.detailsAction__primary"),onClick:()=>r(`verify-${l}`)}:null,!s&&c?{label:(0,d.localizationKeys)("userProfile.start.phoneNumbersSection.detailsAction__nonPrimary"),onClick:()=>n(o).catch(e=>(0,R.S3)(e,[],t.setError))}:null,s||c?null:{label:(0,d.localizationKeys)("userProfile.start.phoneNumbersSection.detailsAction__unverified"),onClick:()=>r(`verify-${l}`)},{label:(0,d.localizationKeys)("userProfile.start.phoneNumbersSection.destructiveAction"),isDestructive:!0,onClick:()=>r(`remove-${l}`)}].filter(e=>null!==e);return(0,i.tZ)(b.a,{actions:u})};var eh=r(1201);let em=(0,a.withCardStateProvider)(e=>{let{onSuccess:t,onReset:r}=e,{user:o}=(0,y.aF)(),l=(0,y.WZ)(e=>o?.update({username:e})),{userSettings:n}=(0,c.useEnvironment)(),s=(0,a.useCardState)(),{t:u,locale:h}=(0,d.useLocalizations)(),{usernameSettings:m}=n,p=(0,R.Yp)("username",o?.username||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__username"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__username"),buildErrorMessage:e=>(0,R.fq)(e,{t:u,locale:h,usernameSettings:m})});if(!o)return null;let f=n.attributes.username?.required,g=(!f||p.value.length>0)&&o.username!==p.value,v=async()=>{try{await l(p.value),t()}catch(e){(0,R.S3)(e,[p],s.setError)}};return(0,i.tZ)(ei.Y,{headerTitle:o.username?(0,d.localizationKeys)("userProfile.usernamePage.title__update"):(0,d.localizationKeys)("userProfile.usernamePage.title__set"),children:(0,i.BX)(et.l.Root,{onSubmit:v,children:[(0,i.tZ)(et.l.ControlRow,{elementId:p.id,children:(0,i.tZ)(et.l.PlainInput,{...p.props,autoFocus:!0,isRequired:f})}),(0,i.tZ)(er.A,{isDisabled:!g,onReset:r})]})})}),ep=()=>{let{close:e}=(0,K.XC)();return(0,i.tZ)(em,{onSuccess:e,onReset:e})},ef=()=>{let{user:e}=(0,y.aF)();return e?(0,i.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.usernameSection.title"),id:"username",sx:{alignItems:"center",[eh.mqu.md]:{alignItems:"flex-start"}},children:(0,i.BX)(C.a.Root,{children:[(0,i.tZ)(C.a.Closed,{value:"edit",children:(0,i.BX)(Z.zd.Item,{id:"username",sx:{paddingLeft:e.username?void 0:"0"},children:[e.username&&(0,i.tZ)(d.Text,{truncate:!0,sx:e=>({color:e.colors.$colorText}),children:e.username}),(0,i.tZ)(C.a.Trigger,{value:"edit",children:(0,i.tZ)(Z.zd.Button,{id:"username",localizationKey:e.username?(0,d.localizationKeys)("userProfile.start.usernameSection.primaryButton__updateUsername"):(0,d.localizationKeys)("userProfile.start.usernameSection.primaryButton__setUsername")})})]})}),(0,i.tZ)(C.a.Open,{value:"edit",children:(0,i.tZ)(C.a.Card,{children:(0,i.tZ)(ep,{})})})]})}):null};var eg=r(7295),ey=r(3300),ev=r(6654),eP=r(5472);let eZ=e=>{let{user:t,...r}=e;return(0,i.tZ)(ev.C,{...r,title:(0,h.u1)("userProfile.profilePage.imageFormTitle"),avatarPreview:(0,i.tZ)(eP.Y,{size:e=>e.sizes.$12,...t})})},eb=(0,a.withCardStateProvider)(e=>{let{onSuccess:t,onReset:r}=e,o=(0,a.useCardState)(),{user:l}=(0,y.aF)();if(!l)return null;let{first_name:n,last_name:s}=(0,c.useEnvironment)().userSettings.attributes,u=n?.enabled,h=s?.enabled,m=l.firstName||"",p=l.lastName||"",f=(0,R.Yp)("firstName",l.firstName||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__firstName"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__firstName"),isRequired:s?.required}),g=(0,R.Yp)("lastName",l.lastName||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__lastName"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__lastName"),isRequired:s?.required}),v=u&&f.value!==m||h&&g.value!==p,P=u&&n.required||h&&s.required,Z=P&&!!g.value&&!!f.value&&v,b=l.enterpriseAccounts.some(e=>e.active),S=async e=>(e.preventDefault(),(v?l.update({firstName:f.value,lastName:g.value}):Promise.resolve()).then(t).catch(e=>{(0,R.S3)(e,[f,g],o.setError)}));return(0,i.BX)(ei.Y,{headerTitle:(0,d.localizationKeys)("userProfile.profilePage.title"),children:[b&&(0,i.tZ)(ey.e,{message:(0,d.localizationKeys)("userProfile.profilePage.readonly")}),(0,i.BX)(et.l.Root,{onSubmit:S,sx:e=>({gap:e.space.$6}),children:[(0,i.tZ)(eZ,{user:l,onAvatarChange:e=>l.setProfileImage({file:e}).then(()=>{o.setIdle()}).catch(e=>(0,R.S3)(e,[],o.setError)),onAvatarRemove:(0,z.QO)(l.imageUrl)?null:()=>{l.setProfileImage({file:null}).then(()=>{o.setIdle()}).catch(e=>(0,R.S3)(e,[],o.setError))}}),(u||h)&&(0,i.BX)(et.l.ControlRow,{elementId:"name",children:[u&&(0,i.tZ)(et.l.PlainInput,{...f.props,isDisabled:b,autoFocus:!0}),h&&(0,i.tZ)(et.l.PlainInput,{...g.props,isDisabled:b,autoFocus:!u})]}),(0,i.tZ)(er.A,{isDisabled:P?!Z:!v,onReset:r})]})]})}),ez=()=>{let{close:e}=(0,K.XC)();return(0,i.tZ)(eb,{onSuccess:e,onReset:e})},eS=()=>{let{user:e}=(0,y.aF)();if(!e)return null;let{username:t,primaryEmailAddress:r,primaryPhoneNumber:o,primaryWeb3Wallet:a,...l}=e;return(0,i.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.profileSection.title"),id:"profile",sx:{[eh.mqu.md]:{alignItems:"flex-start"}},children:(0,i.BX)(C.a.Root,{children:[(0,i.tZ)(C.a.Closed,{value:"edit",children:(0,i.BX)(Z.zd.Item,{id:"profile",children:[(0,i.tZ)(eg.E,{user:l,size:"lg",mainIdentifierVariant:"subtitle",sx:e=>({color:e.colors.$colorText})}),(0,i.tZ)(C.a.Trigger,{value:"edit",children:(0,i.tZ)(Z.zd.Button,{id:"profile",localizationKey:(0,d.localizationKeys)("userProfile.start.profileSection.primaryButton")})})]})}),(0,i.tZ)(C.a.Open,{value:"edit",children:(0,i.tZ)(C.a.Card,{children:(0,i.tZ)(ez,{})})})]})})};var eC=r(4101);let eK=(0,a.withCardStateProvider)(({onClick:e})=>{let t=(0,a.useCardState)(),{user:r}=(0,y.aF)(),{strategies:o,strategyToDisplayData:l}=(0,x.vO)(),n=o.filter(e=>e.startsWith("web3")),s=r?.verifiedWeb3Wallets.map(e=>e.verification.strategy),c=n.filter(e=>!s.includes(e)),u=(0,y.WZ)(e=>r?.createWeb3Wallet({web3Wallet:e})),h=async e=>{let i=e.replace("web3_","").replace("_signature","");t.setError(void 0);try{t.setLoading(e);let o=await (0,eC.Ly)({provider:i});if(!r)throw Error("user is not defined");let a=await u(o);a=await a?.prepareVerification({strategy:e});let l=a?.verification.message,n=await (0,eC.bQ)({identifier:o,nonce:l,provider:i});await a?.attemptVerification({signature:n}),t.setIdle()}catch(r){t.setIdle();let e=(0,R.zQ)(r);e?t.setError(e.longMessage):(0,R.S3)(r,[],t.setError)}};return 0===c.length?null:(0,i.BX)(i.HY,{children:[(0,i.tZ)(Z.zd.ActionMenu,{id:"web3Wallets",triggerLocalizationKey:(0,d.localizationKeys)("userProfile.start.web3WalletsSection.primaryButton"),onClick:e,children:c.map(e=>(0,i.tZ)(Z.zd.ActionMenuItem,{id:l[e].id,onClick:()=>h(e),isLoading:t.loadingMetadata===e,isDisabled:t.isLoading,localizationKey:(0,d.localizationKeys)("userProfile.web3WalletPage.web3WalletButtonsBlockButton",{provider:l[e].name}),sx:e=>({justifyContent:"start",gap:e.space.$2}),leftIcon:(0,i.tZ)(d.Image,{elementDescriptor:d.descriptors.providerIcon,elementId:d.descriptors.providerIcon.setId(l[e].id),isLoading:t.loadingMetadata===e,isDisabled:t.isLoading,src:l[e].iconUrl,alt:`Connect ${l[e].name}`,sx:e=>({width:e.sizes.$5})})},e))}),t.error&&(0,i.tZ)(d.Text,{colorScheme:"danger",sx:e=>({padding:e.sizes.$1x5,paddingLeft:e.sizes.$8x5}),children:t.error})]})}),ex=e=>{let{close:t}=(0,K.XC)();return(0,i.tZ)(T,{onSuccess:t,onReset:t,...e})},eR=e=>e.length<=10?e:e.slice(0,6)+"..."+e.slice(-4),e_=(0,a.withCardStateProvider)(({shouldAllowCreation:e=!0})=>{let{user:t}=(0,y.aF)(),r=(0,a.useCardState)(),{strategyToDisplayData:l}=(0,x.vO)(),n=!!t?.web3Wallets?.length,[s,c]=(0,o.useState)(null);return e||n?(0,i.BX)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.web3WalletsSection.title"),centered:!1,id:"web3Wallets",children:[(0,i.tZ)(v.Z.Alert,{children:r.error}),(0,i.BX)(C.a.Root,{value:s,onChange:c,children:[(0,i.tZ)(Z.zd.ItemList,{id:"web3Wallets",children:Y(t?.web3Wallets,t?.primaryWeb3WalletId).map(e=>{let r=e.verification.strategy,a=e.id;return l[r]&&(0,i.BX)(o.Fragment,{children:[(0,i.BX)(Z.zd.Item,{id:"web3Wallets",align:"start",children:[(0,i.BX)(d.Flex,{sx:e=>({alignItems:"center",gap:e.space.$2,width:"100%"}),children:[l[r].iconUrl&&(0,i.tZ)(d.Image,{src:l[r].iconUrl,alt:l[r].name,sx:e=>({width:e.sizes.$4})}),(0,i.tZ)(d.Box,{sx:{whiteSpace:"nowrap",overflow:"hidden"},children:(0,i.BX)(d.Flex,{gap:2,justify:"start",children:[(0,i.BX)(d.Text,{children:[l[r].name," (",eR(e.web3Wallet),")"]}),t?.primaryWeb3WalletId===a&&(0,i.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__primary")}),"verified"!==e.verification.status&&(0,i.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__unverified")})]})})]}),(0,i.tZ)(eB,{walletId:a})]},a),(0,i.tZ)(C.a.Open,{value:`remove-${a}`,children:(0,i.tZ)(C.a.Card,{variant:"destructive",children:(0,i.tZ)(ex,{walletId:e.id})})})]},e.id)})}),e&&(0,i.tZ)(eK,{onClick:()=>c(null)})]})]}):null}),eB=({walletId:e})=>{let t=(0,a.useCardState)(),{open:r}=(0,K.XC)(),{user:o}=(0,y.aF)(),l=o?.primaryWeb3WalletId===e,n=(0,y.WZ)(()=>o?.update({primaryWeb3WalletId:e})),s=[l?null:{label:(0,d.localizationKeys)("userProfile.start.web3WalletsSection.detailsAction__nonPrimary"),onClick:()=>{n().catch(e=>(0,R.S3)(e,[],t.setError))}},{label:(0,d.localizationKeys)("userProfile.start.web3WalletsSection.destructiveAction"),isDestructive:!0,onClick:()=>r(`remove-${e}`)}].filter(e=>null!==e);return(0,i.tZ)(b.a,{actions:s})},ew=(0,a.withCardStateProvider)(()=>{let{attributes:e,social:t,enterpriseSSO:r}=(0,c.useEnvironment)().userSettings,o=(0,a.useCardState)(),{user:l}=(0,y.aF)(),n=e.username?.enabled,s=e.email_address?.enabled,u=e.phone_number?.enabled,h=t&&Object.values(t).filter(e=>e.enabled).length>0,m=l&&r.enabled,p=e.web3_wallet?.enabled,f=!m||!l.enterpriseAccounts.some(e=>e.active&&e.enterpriseConnection?.disableAdditionalIdentifications);return(0,i.tZ)(d.Col,{elementDescriptor:d.descriptors.page,sx:e=>({gap:e.space.$8,color:e.colors.$colorText}),children:(0,i.BX)(d.Col,{elementDescriptor:d.descriptors.profilePage,elementId:d.descriptors.profilePage.setId("account"),children:[(0,i.tZ)(P.h.Root,{children:(0,i.tZ)(P.h.Title,{localizationKey:(0,d.localizationKeys)("userProfile.start.headerTitle__account"),sx:e=>({marginBottom:e.space.$4}),textVariant:"h2"})}),(0,i.tZ)(v.Z.Alert,{children:o.error}),(0,i.tZ)(eS,{}),n&&(0,i.tZ)(ef,{}),s&&(0,i.tZ)(H,{shouldAllowCreation:f}),u&&(0,i.tZ)(ed,{shouldAllowCreation:f}),h&&(0,i.tZ)(E,{shouldAllowCreation:f}),m&&(0,i.tZ)(G,{}),p&&(0,i.tZ)(e_,{shouldAllowCreation:f})]})})});var ek=r(5006);let eI=()=>{let{navigate:e}=(0,u.useRouter)();return(0,i.BX)(i.HY,{children:[(0,i.tZ)(P.h.Root,{sx:e=>({borderBottomWidth:e.borderWidths.$normal,borderBottomStyle:e.borderStyles.$solid,borderBottomColor:e.colors.$neutralAlpha100,marginBlockEnd:e.space.$4,paddingBlockEnd:e.space.$4}),children:(0,i.tZ)(P.h.BackLink,{onClick:()=>void e("../",{searchParams:new URLSearchParams("tab=subscriptions")}),children:(0,i.tZ)(P.h.Title,{localizationKey:(0,h.u1)("userProfile.plansPage.title"),textVariant:"h2"})})}),(0,i.tZ)(c.PricingTableContext.Provider,{value:{componentName:"PricingTable",mode:"modal"},children:(0,i.tZ)(ek.b,{})})]})},eT=()=>(0,i.tZ)(c.SubscriberTypeContext.Provider,{value:"user",children:(0,i.tZ)(eI,{})});var eA=r(8104),eX=r(4174);let eL=()=>{let{user:e}=(0,y.aF)(),{session:t}=(0,y.kP)(),{data:r,isLoading:o}=(0,x.ib)(e?.getSessions,"user-sessions");return(0,i.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.activeDevicesSection.title"),centered:!1,id:"activeDevices",children:(0,i.tZ)(Z.zd.ItemList,{id:"activeDevices",disableAnimation:!0,children:o?(0,i.tZ)(eA.m,{}):r?.sort(N(t.id)).map(e=>e$(e.status)?i.tZ(eF,{session:e},e.id):null)})})},e$=e=>["active","pending"].includes(e),eF=({session:e})=>{let t=y.kP().session?.id===e.id,r=(0,x._m)(),o=(0,y.WZ)(e.revoke.bind(e)),a=async()=>{if(!t&&e)return r.setLoading(),o().catch(e=>(0,R.S3)(e,[],r.setError)).finally(()=>r.setIdle())};return(0,i.tZ)(Z.zd.Item,{id:"activeDevices",elementDescriptor:d.descriptors.activeDeviceListItem,elementId:t?d.descriptors.activeDeviceListItem.setId("current"):void 0,sx:{alignItems:"flex-start",opacity:r.isLoading?.5:1},isDisabled:r.isLoading,children:(0,i.BX)(i.HY,{children:[(0,i.tZ)(eE,{session:e}),!t&&(0,i.tZ)(eD,{revoke:a})]})})},eE=e=>{let{session:t}=(0,y.kP)(),r=t?.id===e.session.id,o=!!t?.actor,a=!!e.session.actor,{city:l,country:n,browserName:s,browserVersion:c,deviceType:u,ipAddress:h,isMobile:m}=e.session.latestActivity,p=`${s||""} ${c||""}`.trim()||"Web browser",f=[l||"",n||""].filter(Boolean).join(", ").trim()||null,{t:g}=(0,d.useLocalizations)();return(0,i.BX)(d.Flex,{elementDescriptor:d.descriptors.activeDevice,elementId:r?d.descriptors.activeDevice.setId("current"):void 0,sx:e=>({width:"100%",overflow:"hidden",gap:e.space.$4,[eh.mqu.sm]:{gap:e.space.$2}}),children:[(0,i.tZ)(d.Flex,{sx:e=>({[eh.mqu.sm]:{padding:"0"},borderRadius:e.radii.$md}),children:(0,i.tZ)(d.Icon,{elementDescriptor:d.descriptors.activeDeviceIcon,elementId:d.descriptors.activeDeviceIcon.setId(m?"mobile":"desktop"),icon:m?eX.Fh:eX.eu,sx:e=>({"--cl-chassis-bottom":"#444444","--cl-chassis-back":"#343434","--cl-chassis-screen":"#575757","--cl-screen":"#000000",width:e.space.$8,height:e.space.$8})})}),(0,i.BX)(d.Col,{align:"start",gap:1,children:[(0,i.BX)(d.Flex,{center:!0,gap:2,children:[(0,i.tZ)(d.Text,{children:u||(m?"Mobile device":"Desktop device")}),r&&(0,i.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__thisDevice"),colorScheme:o?"danger":"primary"}),o&&!a&&(0,i.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__userDevice")}),!r&&a&&(0,i.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__otherImpersonatorDevice"),colorScheme:"danger"})]}),(0,i.tZ)(d.Text,{colorScheme:"secondary",children:p}),(0,i.BX)(d.Text,{colorScheme:"secondary",children:[h," (",f,")"]}),(0,i.tZ)(d.Text,{colorScheme:"secondary",children:g((0,R.Qg)(e.session.lastActiveAt))})]})]})},eD=({revoke:e})=>{let t=[{label:(0,d.localizationKeys)("userProfile.start.activeDevicesSection.destructiveAction"),isDestructive:!0,onClick:e}].filter(e=>null!==e);return(0,i.tZ)(b.a,{actions:t})};var eO=r(4264);let eN=(0,a.withCardStateProvider)(e=>{let{onReset:t}=e,r=(0,a.useCardState)(),{afterSignOutUrl:o,afterMultiSessionSingleSignOutUrl:l}=(0,c.useSignOutContext)(),{user:n}=(0,y.aF)(),{t:s}=(0,d.useLocalizations)(),{otherSessions:u}=(0,eO.j)({user:n}),{setActive:h}=(0,y.cL)(),m=(0,y.WZ)(()=>n?.delete()),p=(0,R.Yp)("deleteConfirmation","",{type:"text",label:(0,d.localizationKeys)("userProfile.deletePage.actionDescription"),isRequired:!0,placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__confirmDeletionUserAccount")}),f=p.value===(s((0,d.localizationKeys)("formFieldInputPlaceholder__confirmDeletionUserAccount"))||"Delete account"),g=async()=>{if(f)try{await m();let e=0===u.length?o:l;return await h({session:null,redirectUrl:e})}catch(e){(0,R.S3)(e,[],r.setError)}};return(0,i.tZ)(ei.Y,{headerTitle:(0,d.localizationKeys)("userProfile.deletePage.title"),sx:e=>({gap:e.space.$0x5}),children:(0,i.BX)(et.l.Root,{onSubmit:g,children:[(0,i.BX)(d.Col,{gap:1,children:[(0,i.tZ)(d.Text,{colorScheme:"secondary",localizationKey:(0,d.localizationKeys)("userProfile.deletePage.messageLine1")}),(0,i.tZ)(d.Text,{colorScheme:"danger",localizationKey:(0,d.localizationKeys)("userProfile.deletePage.messageLine2")})]}),(0,i.tZ)(et.l.ControlRow,{elementId:p.id,children:(0,i.tZ)(et.l.PlainInput,{...p.props,ignorePasswordManager:!0})}),(0,i.tZ)(er.A,{submitLabel:(0,d.localizationKeys)("userProfile.deletePage.confirm"),colorScheme:"danger",isDisabled:!f,onReset:t})]})})}),eW=()=>{let{close:e}=(0,K.XC)();return(0,i.tZ)(eN,{onSuccess:e,onReset:e})},eM=()=>(0,i.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.dangerSection.title"),id:"danger",sx:{alignItems:"center",[eh.mqu.md]:{alignItems:"flex-start"}},children:(0,i.BX)(C.a.Root,{children:[(0,i.tZ)(C.a.Closed,{value:"delete",children:(0,i.tZ)(Z.zd.Item,{id:"danger",sx:e=>({paddingLeft:e.space.$1}),children:(0,i.tZ)(C.a.Trigger,{value:"delete",children:(0,i.tZ)(Z.zd.Button,{id:"danger",variant:"ghost",colorScheme:"danger",localizationKey:(0,d.localizationKeys)("userProfile.start.dangerSection.deleteAccountButton")})})})}),(0,i.tZ)(C.a.Open,{value:"delete",children:(0,i.tZ)(C.a.Card,{variant:"destructive",children:(0,i.tZ)(eW,{})})})]})});var eU=r(5994),eY=r(3531),eq=r(5878);let ej=e=>{let{code:t}=e;return(0,i.tZ)(d.Flex,{center:!0,sx:e=>({padding:`${e.space.$1} ${e.space.$4}`}),children:(0,i.tZ)(d.Text,{children:t})})},eV=e=>{let{subtitle:t,backupCodes:r}=e,{applicationName:o}=(0,c.useEnvironment)().displayConfig,{user:a}=(0,y.aF)(),{print:l,printableProps:n}=(0,S.Qz)(),{onCopy:s,hasCopied:u}=(0,x.VP)(r?.join(",")||"");if(!a)return null;let h=(0,eq.xC)(a);return r?(0,i.BX)(i.HY,{children:[(0,i.BX)(d.Col,{gap:1,children:[(0,i.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)("userProfile.backupCodePage.title__codelist"),variant:"subtitle"}),(0,i.tZ)(d.Text,{localizationKey:t,variant:"caption",colorScheme:"secondary"})]}),(0,i.BX)(d.Box,{sx:e=>({borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha100,borderRadius:e.radii.$lg}),children:[(0,i.tZ)(d.Grid,{gap:2,sx:e=>({gridTemplateColumns:`repeat(2, minmax(${e.sizes.$12}, 1fr))`,padding:`${e.space.$4} ${e.space.$6}`}),children:r.map((e,t)=>(0,i.tZ)(ej,{code:e},t))}),(0,i.BX)(d.Grid,{sx:e=>({borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100,gridTemplateColumns:"repeat(3, minmax(0, 1fr))",">:not([hidden])~:not([hidden])":{borderRightWidth:"0px",borderLeftWidth:"1px",borderStyle:"solid",borderColor:e.colors.$neutralAlpha100},">:first-child":{borderBottomLeftRadius:e.radii.$lg},">:last-child":{borderBottomRightRadius:e.radii.$lg}}),children:[(0,i.tZ)(d.Button,{variant:"ghost",sx:e=>({width:"100%",padding:`${e.space.$2} 0`,borderRadius:0}),onClick:()=>{let e=document.createElement("a"),t=new Blob([function(e,t,r){let i=e?.join("\n");return`These are your backup codes for ${t} account ${r}.
Store them securely and keep them secret. Each code can only be used once.

${i}`}(r,o,h)],{type:"text/plain"});e.href=URL.createObjectURL(t),e.download=`${o}_backup_codes.txt`,document.body.appendChild(e),e.click()},children:(0,i.tZ)(d.Icon,{icon:eX.UW})}),(0,i.tZ)(d.Button,{variant:"ghost",sx:e=>({width:"100%",padding:`${e.space.$2} 0`,borderRadius:0}),onClick:l,children:(0,i.tZ)(d.Icon,{icon:eX.Kh})}),(0,i.tZ)(d.Button,{variant:"ghost",onClick:s,sx:e=>({width:"100%",padding:`${e.space.$2} 0`,borderRadius:0}),children:(0,i.tZ)(d.Icon,{icon:u?eX.Jr:eX.CK})})]})]}),(0,i.BX)(S.o4,{...n,children:[(0,i.BX)(d.Heading,{children:["Your backup codes for ",o," account ",h,":"]}),(0,i.tZ)(d.Col,{gap:2,children:r.map((e,t)=>(0,i.tZ)(ej,{code:e},t))})]})]}):null},eH=(0,a.withCardStateProvider)(e=>{let{onSuccess:t,onReset:r}=e,{user:l}=(0,y.aF)(),n=(0,a.useCardState)(),s=(0,y.WZ)(()=>l?.createBackupCode()),[c,u]=o.useState(void 0);return(o.useEffect(()=>{!c&&l&&s().then(e=>u(e)).catch(e=>{if((0,eY.mh)(e))return r();(0,R.S3)(e,[],n.setError)})},[]),n.error)?(0,i.tZ)(ei.Y,{headerTitle:(0,d.localizationKeys)("userProfile.backupCodePage.title")}):(0,i.tZ)(ei.Y,{headerTitle:(0,d.localizationKeys)("userProfile.backupCodePage.title"),children:c?(0,i.BX)(i.HY,{children:[(0,i.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)("userProfile.backupCodePage.successMessage")}),(0,i.tZ)(eV,{subtitle:(0,d.localizationKeys)("userProfile.backupCodePage.subtitle__codelist"),backupCodes:c.codes}),(0,i.tZ)(er.K,{children:(0,i.tZ)(d.Button,{autoFocus:!0,onClick:t,localizationKey:(0,d.localizationKeys)("userProfile.formButtonPrimary__finish"),elementDescriptor:d.descriptors.formButtonPrimary})})]}):(0,i.tZ)(eA.m,{})})}),eQ=(0,a.withCardStateProvider)(e=>{let{onSuccess:t,onReset:r}=e,o=(0,S.a2)();return(0,i.BX)(S.en,{...o.props,children:[(0,i.tZ)(eG,{onContinue:o.nextStep}),(0,i.tZ)(eH,{onSuccess:t,onReset:r})]})}),eG=e=>{let{onContinue:t}=e,{close:r}=(0,K.XC)();return(0,i.BX)(ei.Y,{headerTitle:(0,d.localizationKeys)("userProfile.backupCodePage.title"),children:[(0,i.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)("userProfile.backupCodePage.infoText1")}),(0,i.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)("userProfile.backupCodePage.infoText2")}),(0,i.BX)(er.K,{sx:{marginTop:0},children:[(0,i.tZ)(d.Button,{textVariant:"buttonSmall",onClick:t,localizationKey:(0,d.localizationKeys)("userProfile.formButtonPrimary__finish"),elementDescriptor:d.descriptors.formButtonPrimary}),(0,i.tZ)(d.Button,{variant:"ghost",onClick:r,localizationKey:(0,d.localizationKeys)("userProfile.formButtonReset"),elementDescriptor:d.descriptors.formButtonReset})]})]})};var eJ=r(3412),e0=r(7263);let e1=(0,a.withCardStateProvider)(e=>{let{onReset:t,onSuccess:r}=e,a=o.useRef(),l=(0,S.a2)({defaultStep:2}),n=c.useEnvironment().userSettings.attributes.backup_code?.enabled;return(0,i.BX)(S.en,{...l.props,children:[(0,i.tZ)(el,{title:(0,d.localizationKeys)("userProfile.phoneNumberPage.title"),resourceRef:a,onSuccess:l.nextStep,onUseExistingNumberClick:()=>l.goToStep(2),onReset:t}),(0,i.tZ)(e3,{title:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.title"),resourceRef:a,onSuccess:()=>n?l.goToStep(3):r(),onReset:()=>l.goToStep(2)}),(0,i.tZ)(e4,{onSuccess:n?l.nextStep:r,onReset:t,onAddPhoneClick:()=>l.goToStep(0),onUnverifiedPhoneClick:e=>{a.current=e,l.goToStep(1)},title:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.title"),resourceRef:a}),n&&(0,i.tZ)(e0.I,{title:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.successTitle"),text:a.current?.backupCodes&&a.current?.backupCodes.length>0?[(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.successMessage1"),(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.successMessage2")]:[(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.successMessage1")],onFinish:t,contents:(0,i.tZ)(eV,{backupCodes:a.current?.backupCodes})})]})}),e2=e=>{let{phone:t,onSuccess:r,onUnverifiedPhoneClick:o,resourceRef:l}=e,n=(0,a.useCardState)(),s=(0,y.WZ)(()=>t.setReservedForSecondFactor({reserved:!0})),{country:c}=(0,R.Z_)(t.phoneNumber),u=(0,R.L_)(t.phoneNumber),h=async()=>{if("verified"!==t.verification.status)return o(t);n.setLoading(t.id);try{await s(),l.current=t,r()}catch(e){(0,R.S3)(e,[],n.setError)}finally{n.setIdle()}};return(0,i.BX)(d.Button,{variant:"outline",colorScheme:"neutral",sx:{justifyContent:"start"},onClick:h,isLoading:n.loadingMetadata===t.id,isDisabled:n.isLoading,children:[c.iso.toUpperCase()," ",u]},t.id)},e3=e=>{let{title:t,onSuccess:r,resourceRef:o,onReset:l}=e,n=(0,a.useCardState)(),s=o.current,c=(0,y.WZ)(()=>s?.setReservedForSecondFactor({reserved:!0})),u=async()=>{n.setLoading(s?.id);try{await c(),o.current=s,r()}catch(e){(0,R.S3)(e,[],n.setError)}finally{n.setIdle()}};return(0,i.tZ)(ei.Y,{headerTitle:t,headerSubtitle:(0,d.localizationKeys)("userProfile.phoneNumberPage.verifySubtitle",{identifier:o.current?.phoneNumber}),children:(0,i.tZ)(eo.H,{nextStep:()=>{u()},identification:o.current,identifier:o.current?.phoneNumber,prepareVerification:o.current?.prepareVerification,onReset:l})})},e4=e=>{let{onSuccess:t,onReset:r,title:o,onAddPhoneClick:a,onUnverifiedPhoneClick:l,resourceRef:n}=e,{user:s}=(0,y.aF)();if(!s)return null;let c=s.phoneNumbers.filter(e=>!e.reservedForSecondFactor);return(0,i.BX)(ei.Y,{headerTitle:o,gap:1,children:[(0,i.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)(c.length?"userProfile.mfaPhoneCodePage.subtitle__availablePhoneNumbers":"userProfile.mfaPhoneCodePage.subtitle__unavailablePhoneNumbers"),colorScheme:"secondary"}),c.length>0&&(0,i.tZ)(d.Col,{gap:2,children:c.map(e=>(0,i.tZ)(e2,{onSuccess:t,phone:e,onUnverifiedPhoneClick:l,resourceRef:n},e.id))}),(0,i.BX)(er.K,{sx:{flexDirection:"row",justifyContent:"space-between"},children:[(0,i.tZ)(eJ.h,{variant:"ghost","aria-label":"Add phone number",icon:(0,i.tZ)(d.Icon,{icon:eX.v3,sx:e=>({marginRight:e.space.$2})}),localizationKey:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.primaryButton__addPhoneNumber"),onClick:a}),(0,i.tZ)(d.Button,{variant:"ghost",localizationKey:(0,d.localizationKeys)("userProfile.formButtonReset"),onClick:r})]})]})};var e6=r(8864);let e5=(0,a.withCardStateProvider)(e=>{let{title:t,onSuccess:r,onReset:l}=e,{user:n}=(0,y.aF)(),s=(0,a.useCardState)(),c=(0,y.WZ)(()=>n?.createTOTP()),{close:u}=(0,K.XC)(),[h,m]=o.useState(void 0),[p,f]=o.useState("qr");return(o.useEffect(()=>{n&&c().then(e=>m(e)).catch(e=>(0,eY.uX)(e)&&"reverification_cancelled"===e.code?u():(0,R.S3)(e,[],s.setError))},[]),s.error)?(0,i.tZ)(ei.Y,{headerTitle:t}):(0,i.BX)(ei.Y,{headerTitle:t,headerSubtitle:"qr"==p?(0,d.localizationKeys)("userProfile.mfaTOTPPage.authenticatorApp.infoText__ableToScan"):(0,d.localizationKeys)("userProfile.mfaTOTPPage.authenticatorApp.infoText__unableToScan"),children:[!h&&(0,i.tZ)(eA.m,{}),h&&(0,i.BX)(i.HY,{children:[(0,i.BX)(d.Col,{gap:4,children:["qr"==p&&(0,i.tZ)(S.s_,{justify:"center",url:h.uri||""}),"uri"==p&&(0,i.BX)(i.HY,{children:[(0,i.tZ)(d.Text,{colorScheme:"secondary",localizationKey:(0,d.localizationKeys)("userProfile.mfaTOTPPage.authenticatorApp.inputLabel__unableToScan1")}),(0,i.tZ)(e6.D,{value:h.secret}),(0,i.tZ)(d.Text,{colorScheme:"secondary",localizationKey:(0,d.localizationKeys)("userProfile.mfaTOTPPage.authenticatorApp.inputLabel__unableToScan2")}),(0,i.tZ)(e6.D,{value:h.uri})]})]}),(0,i.BX)(d.Flex,{justify:"between",align:"center",sx:{width:"100%"},children:["qr"==p&&(0,i.tZ)(d.Button,{variant:"link",textVariant:"buttonLarge",onClick:()=>f("uri"),localizationKey:(0,d.localizationKeys)("userProfile.mfaTOTPPage.authenticatorApp.buttonUnableToScan__nonPrimary")}),"uri"==p&&(0,i.tZ)(d.Button,{variant:"link",textVariant:"buttonLarge",onClick:()=>f("qr"),localizationKey:(0,d.localizationKeys)("userProfile.mfaTOTPPage.authenticatorApp.buttonAbleToScan__nonPrimary")}),(0,i.BX)(er.K,{children:[(0,i.tZ)(d.Button,{onClick:r,localizationKey:(0,d.localizationKeys)("userProfile.formButtonPrimary__continue"),elementDescriptor:d.descriptors.formButtonPrimary}),(0,i.tZ)(d.Button,{variant:"ghost",onClick:l,localizationKey:(0,d.localizationKeys)("userProfile.formButtonReset"),elementDescriptor:d.descriptors.formButtonReset})]})]})]})]})});var e9=r(2667);let e7=(0,a.withCardStateProvider)(e=>{let{onSuccess:t,onReset:r,resourceRef:o}=e,{user:a}=(0,y.aF)(),l=(0,e9.e3)({onCodeEntryFinished:(e,t,r)=>{a?.verifyTOTP({code:e}).then(e=>t(e)).catch(r)},onResolve:e=>{o.current=e,t()}});return(0,i.BX)(ei.Y,{headerTitle:(0,d.localizationKeys)("userProfile.mfaTOTPPage.title"),children:[(0,i.tZ)(d.Col,{children:(0,i.tZ)(et.l.OTPInput,{...l,label:(0,d.localizationKeys)("userProfile.mfaTOTPPage.verifyTitle"),description:(0,d.localizationKeys)("userProfile.mfaTOTPPage.verifySubtitle")})}),(0,i.tZ)(er.K,{sx:{marginTop:0},children:(0,i.tZ)(d.Button,{onClick:r,variant:"ghost",isDisabled:l.isLoading,localizationKey:(0,d.localizationKeys)("userProfile.formButtonReset"),elementDescriptor:d.descriptors.formButtonReset})})]})}),e8=(0,a.withCardStateProvider)(e=>{let{onReset:t}=e,r=(0,S.a2)(),a=o.useRef();return(0,i.BX)(S.en,{...r.props,children:[(0,i.tZ)(e5,{title:(0,d.localizationKeys)("userProfile.mfaTOTPPage.title"),onSuccess:r.nextStep,onReset:t}),(0,i.tZ)(e7,{onSuccess:r.nextStep,onReset:t,resourceRef:a}),(0,i.tZ)(e0.I,{title:(0,d.localizationKeys)("userProfile.mfaTOTPPage.title"),text:(0,d.localizationKeys)("userProfile.mfaTOTPPage.successMessage"),onFinish:t,contents:(0,i.tZ)(eV,{subtitle:(0,d.localizationKeys)("userProfile.backupCodePage.successSubtitle"),backupCodes:a.current?.backupCodes})})]})}),te=(0,a.withCardStateProvider)(e=>{let{onSuccess:t,onReset:r,selectedStrategy:l}=e,n=(0,a.useCardState)(),{userSettings:{attributes:s}}=(0,c.useEnvironment)(),{user:u}=(0,y.aF)();if(!u)return null;let h=(0,d.localizationKeys)("userProfile.mfaPage.title"),m=o.useMemo(()=>U(s,u),[]);return(o.useEffect(()=>{0===m.length&&n.setError("There are no second factors available to add")},[]),n.error)?(0,i.tZ)(ei.Y,{headerTitle:h}):0!==m.length||l?(0,i.tZ)(tt,{onSuccess:t,onReset:r,method:l||m[0]}):null}),tt=e=>{let{method:t,onSuccess:r,onReset:o}=e;switch(t){case"phone_code":return(0,i.tZ)(e1,{onSuccess:r,onReset:o});case"totp":return(0,i.tZ)(e8,{onSuccess:r,onReset:o});case"backup_code":return(0,i.tZ)(eQ,{onSuccess:r,onReset:o});default:return null}},tr=()=>{let{close:e}=(0,K.XC)();return(0,i.tZ)(X,{onSuccess:e,onReset:e})},ti=e=>{let{close:t}=(0,K.XC)();return(0,i.tZ)(A,{onSuccess:t,onReset:t,...e})},to=()=>{let{close:e}=(0,K.XC)();return(0,i.tZ)(eH,{onSuccess:e,onReset:e})},ta=e=>{let{close:t}=(0,K.XC)();return(0,i.tZ)(te,{onSuccess:t,onReset:t,selectedStrategy:e.selectedStrategy})},tl=()=>{let{userSettings:{attributes:e}}=(0,c.useEnvironment)(),{user:t}=(0,y.aF)(),[r,a]=(0,o.useState)(null);if(!t)return null;let l=M(e),n=U(e,t),s=l.includes("totp")&&t.totpEnabled,u=l.includes("backup_code")&&t.backupCodeEnabled,h=t.phoneNumbers.filter(e=>"verified"===e.verification.status).filter(e=>e.reservedForSecondFactor).sort(W);return(0,i.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.mfaSection.title"),centered:!1,id:"mfa",children:(0,i.tZ)(C.a.Root,{value:r,onChange:a,children:(0,i.BX)(Z.zd.ItemList,{id:"mfa",children:[s&&(0,i.BX)(i.HY,{children:[(0,i.BX)(Z.zd.Item,{id:"mfa",hoverable:!0,children:[(0,i.BX)(d.Flex,{sx:e=>({gap:e.space.$2,alignItems:"center"}),children:[(0,i.tZ)(d.Icon,{icon:eX.hc,sx:e=>({color:e.colors.$neutralAlpha700})}),(0,i.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)("userProfile.start.mfaSection.totp.headerTitle")}),(0,i.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__default")})]}),(0,i.tZ)(tc,{})]}),(0,i.tZ)(C.a.Open,{value:"remove-totp",children:(0,i.tZ)(C.a.Card,{variant:"destructive",children:(0,i.tZ)(tr,{})})})]}),l.includes("phone_code")&&h.map(e=>{let t=!s&&e.defaultSecondFactor,r=e.id;return(0,i.BX)(o.Fragment,{children:[(0,i.BX)(Z.zd.Item,{id:"mfa",hoverable:!0,children:[(0,i.BX)(d.Flex,{sx:e=>({gap:e.space.$2,alignItems:"center"}),children:[(0,i.tZ)(d.Icon,{icon:eX.ij,sx:e=>({color:e.colors.$neutralAlpha700})}),(0,i.BX)(d.Text,{children:["SMS Code ",(0,i.tZ)(eU.q,{value:e.phoneNumber})]}),t&&(0,i.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__default")})]}),(0,i.tZ)(tn,{phone:e,showTOTP:s})]}),(0,i.tZ)(C.a.Open,{value:`remove-${r}`,children:(0,i.tZ)(C.a.Card,{variant:"destructive",children:(0,i.tZ)(ti,{phoneId:r})})})]},r)}),u&&(0,i.BX)(i.HY,{children:[(0,i.BX)(Z.zd.Item,{id:"mfa",hoverable:!0,children:[(0,i.BX)(d.Flex,{sx:e=>({gap:e.space.$2,alignItems:"center"}),children:[(0,i.tZ)(d.Icon,{icon:eX.Qi,sx:e=>({color:e.colors.$neutralAlpha700})}),(0,i.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)("userProfile.start.mfaSection.backupCodes.headerTitle")})]}),(0,i.tZ)(ts,{})]}),(0,i.tZ)(C.a.Open,{value:"regenerate",children:(0,i.tZ)(C.a.Card,{children:(0,i.tZ)(to,{})})})]}),(0,i.tZ)(td,{secondFactorsAvailableToAdd:n,onClick:()=>a(null)})]})})})},tn=({phone:e,showTOTP:t})=>{let{open:r}=(0,K.XC)(),o=(0,a.useCardState)(),l=e.id,n=[t||e.defaultSecondFactor?null:{label:(0,d.localizationKeys)("userProfile.start.mfaSection.phoneCode.actionLabel__setDefault"),onClick:()=>e.makeDefaultSecondFactor().catch(e=>(0,R.S3)(e,[],o.setError))},{label:(0,d.localizationKeys)("userProfile.start.mfaSection.phoneCode.destructiveActionLabel"),isDestructive:!0,onClick:()=>r(`remove-${l}`)}].filter(e=>null!==e);return(0,i.tZ)(b.a,{actions:n})},ts=()=>{let{open:e}=(0,K.XC)(),t=[{label:(0,d.localizationKeys)("userProfile.start.mfaSection.backupCodes.actionLabel__regenerate"),onClick:()=>e("regenerate")}].filter(e=>null!==e);return(0,i.tZ)(b.a,{actions:t})},tc=()=>{let{open:e}=(0,K.XC)(),t=[{label:(0,d.localizationKeys)("userProfile.start.mfaSection.totp.destructiveActionTitle"),isDestructive:!0,onClick:()=>e("remove-totp")}].filter(e=>null!==e);return(0,i.tZ)(b.a,{actions:t})},td=e=>{let{open:t}=(0,K.XC)(),{secondFactorsAvailableToAdd:r,onClick:a}=e,[l,n]=(0,o.useState)(),s=o.useMemo(()=>r.map(e=>"phone_code"===e?{icon:eX.ij,text:"SMS code",key:"phone_code"}:"totp"===e?{icon:eX.hc,text:"Authenticator application",key:"totp"}:"backup_code"===e?{icon:eX.Qi,text:"Backup code",key:"backup_code"}:null).filter(e=>null!==e),[r]);return(0,i.BX)(i.HY,{children:[r.length>0&&(0,i.tZ)(C.a.Closed,{value:"multi-factor",children:(0,i.tZ)(Z.zd.ActionMenu,{id:"mfa",triggerLocalizationKey:(0,d.localizationKeys)("userProfile.start.mfaSection.primaryButton"),onClick:a,children:s.map(e=>e&&(0,i.tZ)(Z.zd.ActionMenuItem,{id:e.key,localizationKey:e.text,leftIcon:e.icon,onClick:()=>{n(e.key),t("multi-factor")}},e.key))})}),(0,i.tZ)(C.a.Open,{value:"multi-factor",children:(0,i.tZ)(C.a.Card,{children:l&&(0,i.tZ)(ta,{selectedStrategy:l})})})]})},tu=e=>{let{close:t}=(0,K.XC)();return(0,i.tZ)(L,{onSuccess:t,onReset:t,...e})},th=e=>{let{close:t}=(0,K.XC)();return(0,i.tZ)(tm,{onSuccess:t,onReset:t,passkey:e.passkey})},tm=(0,a.withCardStateProvider)(e=>{let{onSuccess:t,onReset:r,passkey:o}=e,l=(0,a.useCardState)(),n=(0,R.Yp)("passkeyName",o.name||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__passkeyName"),isRequired:!0}),s=n.value.length>1&&o.name!==n.value,c=async e=>(e.preventDefault(),o.update({name:n.value}).then(t).catch(e=>(0,R.S3)(e,[n],l.setError)));return(0,i.tZ)(ei.Y,{headerTitle:(0,d.localizationKeys)("userProfile.passkeyScreen.title__rename"),headerSubtitle:(0,d.localizationKeys)("userProfile.passkeyScreen.subtitle__rename"),children:(0,i.BX)(et.l.Root,{onSubmit:c,children:[(0,i.tZ)(et.l.ControlRow,{elementId:n.id,children:(0,i.tZ)(et.l.PlainInput,{...n.props,autoComplete:"off"})}),(0,i.tZ)(er.A,{submitLabel:(0,d.localizationKeys)("userProfile.formButtonPrimary__save"),isDisabled:!s,onReset:r})]})})}),tp=()=>{let{user:e}=(0,y.aF)(),[t,r]=(0,o.useState)(null);return e?(0,i.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.passkeysSection.title"),centered:!1,id:"passkeys",children:(0,i.tZ)(C.a.Root,{value:t,onChange:r,children:(0,i.BX)(Z.zd.ItemList,{id:"passkeys",children:[e.passkeys.map(e=>{let t=e.id;return(0,i.BX)(o.Fragment,{children:[(0,i.tZ)(tf,{...e},t),(0,i.tZ)(C.a.Open,{value:`remove-${t}`,children:(0,i.tZ)(C.a.Card,{variant:"destructive",children:(0,i.tZ)(tu,{passkey:e})})}),(0,i.tZ)(C.a.Open,{value:`rename-${t}`,children:(0,i.tZ)(C.a.Card,{children:(0,i.tZ)(th,{passkey:e})})})]},t)}),(0,i.tZ)(tv,{onClick:()=>r(null)})]})})}):null},tf=e=>(0,i.BX)(Z.zd.Item,{id:"passkeys",hoverable:!0,sx:{alignItems:"flex-start"},children:[(0,i.tZ)(tg,{...e}),(0,i.tZ)(ty,{passkey:e})]}),tg=e=>{let{name:t,createdAt:r,lastUsedAt:o}=e,{t:a}=(0,d.useLocalizations)();return(0,i.tZ)(d.Flex,{sx:e=>({width:"100%",overflow:"hidden",gap:e.space.$4,[eh.mqu.sm]:{gap:e.space.$2}}),children:(0,i.BX)(d.Col,{align:"start",gap:1,children:[(0,i.tZ)(d.Text,{children:t}),(0,i.BX)(d.Text,{colorScheme:"secondary",children:["Created: ",a((0,R.Qg)(r))]}),o&&(0,i.BX)(d.Text,{colorScheme:"secondary",children:["Last used: ",a((0,R.Qg)(o))]})]})})},ty=({passkey:e})=>{let{open:t}=(0,K.XC)(),r=e.id,o=[{label:(0,d.localizationKeys)("userProfile.start.passkeysSection.menuAction__rename"),onClick:()=>t(`rename-${r}`)},{label:(0,d.localizationKeys)("userProfile.start.passkeysSection.menuAction__destructive"),isDestructive:!0,onClick:()=>t(`remove-${r}`)}];return(0,i.tZ)(b.a,{actions:o})},tv=({onClick:e})=>{let t=(0,a.useCardState)(),{isSatellite:r}=(0,y.cL)(),{user:o}=(0,y.aF)(),l=(0,y.WZ)(()=>o?.createPasskey()),n=async()=>{if(e?.(),o)try{await l()}catch(e){(0,R.S3)(e,[],t.setError)}};return r?null:(0,i.tZ)(Z.zd.ArrowButton,{id:"passkeys",localizationKey:(0,d.localizationKeys)("userProfile.start.passkeysSection.primaryButton"),onClick:n})},tP=(e,t)=>{let r=[];return e?r.push((0,d.localizationKeys)("userProfile.passwordPage.successMessage__update")):r.push((0,d.localizationKeys)("userProfile.passwordPage.successMessage__set")),t&&r.push((0,d.localizationKeys)("userProfile.passwordPage.successMessage__signOutOfOtherSessions")),r},tZ=(0,a.withCardStateProvider)(e=>{let{onSuccess:t,onReset:r}=e,{user:l}=(0,y.aF)(),n=(0,y.WZ)((e,t)=>e.updatePassword(...t));if(!l)return null;let{userSettings:{passwordSettings:s},authConfig:{reverification:u}}=(0,c.useEnvironment)(),{session:h}=(0,y.kP)(),m=l.passwordEnabled?(0,d.localizationKeys)("userProfile.passwordPage.title__update"):(0,d.localizationKeys)("userProfile.passwordPage.title__set"),p=(0,a.useCardState)(),f=l.enterpriseAccounts.some(e=>e.active),g=l.passwordEnabled&&!u,v=(0,o.useRef)({title:(0,d.localizationKeys)("userProfile.passwordPage.title__set")}),P=(0,R.Yp)("currentPassword","",{type:"password",label:(0,d.localizationKeys)("formFieldLabel__currentPassword"),isRequired:!0}),Z=(0,R.Yp)("newPassword","",{type:"password",label:(0,d.localizationKeys)("formFieldLabel__newPassword"),isRequired:!0,validatePassword:!0,buildErrorMessage:e=>(0,R.GM)(e,{t:K,locale:_,passwordSettings:s})}),b=(0,R.Yp)("confirmPassword","",{type:"password",label:(0,d.localizationKeys)("formFieldLabel__confirmPassword"),isRequired:!0}),z=(0,R.Yp)("signOutOfOtherSessions","",{type:"checkbox",label:(0,d.localizationKeys)("formFieldLabel__signOutOfOtherSessions"),defaultChecked:!0}),{setConfirmPasswordFeedback:S,isPasswordMatch:C}=(0,x.p5)({passwordField:Z,confirmPasswordField:b}),{t:K,locale:_}=(0,d.useLocalizations)(),B=(g?P.value&&C:C)&&Z.value&&b.value,w=async()=>{try{v.current={title:l.passwordEnabled?(0,d.localizationKeys)("userProfile.passwordPage.title__update"):(0,d.localizationKeys)("userProfile.passwordPage.title__set"),text:tP(l.passwordEnabled,!!z.checked)};let e={newPassword:Z.value,signOutOfOtherSessions:z.checked,currentPassword:g?P.value:void 0};await n(l,[e]),t()}catch(e){(0,R.S3)(e,[P,Z,b],p.setError)}};return(0,i.BX)(ei.Y,{headerTitle:m,children:[f&&(0,i.tZ)(ey.e,{message:(0,d.localizationKeys)("userProfile.passwordPage.readonly")}),(0,i.BX)(et.l.Root,{onSubmit:w,onBlur:()=>{Z.value&&S(b.value)},children:[(0,i.tZ)("input",{readOnly:!0,"data-testid":"hidden-identifier",id:"identifier-field",name:"identifier",value:h?.publicUserData.identifier||"",style:{display:"none"}}),g&&(0,i.tZ)(et.l.ControlRow,{elementId:P.id,children:(0,i.tZ)(et.l.PasswordInput,{...P.props,minLength:6,isRequired:!0,autoFocus:!0,isDisabled:f})}),(0,i.tZ)(et.l.ControlRow,{elementId:Z.id,children:(0,i.tZ)(et.l.PasswordInput,{...Z.props,minLength:6,isRequired:!0,autoFocus:!l.passwordEnabled,isDisabled:f})}),(0,i.tZ)(et.l.ControlRow,{elementId:b.id,children:(0,i.tZ)(et.l.PasswordInput,{...b.props,onChange:e=>(e.target.value&&S(e.target.value),b.props.onChange(e)),isRequired:!0,isDisabled:f})}),(0,i.tZ)(et.l.ControlRow,{elementId:z.id,children:(0,i.tZ)(et.l.Checkbox,{...z.props,description:(0,d.localizationKeys)("userProfile.passwordPage.checkboxInfoText__signOutOfOtherSessions"),isDisabled:f})}),f?(0,i.tZ)(er.K,{children:(0,i.tZ)(et.l.ResetButton,{localizationKey:(0,d.localizationKeys)("userProfile.formButtonReset"),block:!1,onClick:r})}):(0,i.tZ)(er.A,{isDisabled:!B,onReset:r})]})]})}),tb=()=>{let{close:e}=(0,K.XC)();return(0,i.tZ)(tZ,{onSuccess:e,onReset:e})},tz=()=>{let{user:e}=(0,y.aF)();if(!e)return null;let{passwordEnabled:t}=e;return(0,i.tZ)(Z.zd.Root,{centered:!1,title:(0,d.localizationKeys)("userProfile.start.passwordSection.title"),id:"password",children:(0,i.BX)(C.a.Root,{children:[(0,i.tZ)(C.a.Closed,{value:"edit",children:(0,i.BX)(Z.zd.Item,{id:"password",sx:e=>({paddingLeft:t?void 0:"0",paddingTop:e.space.$0x25,paddingBottom:e.space.$0x25}),children:[t&&(0,i.tZ)(d.Text,{variant:"h2",children:"••••••••••"}),(0,i.tZ)(C.a.Trigger,{value:"edit",children:(0,i.tZ)(Z.zd.Button,{id:"password",localizationKey:t?(0,d.localizationKeys)("userProfile.start.passwordSection.primaryButton__updatePassword"):(0,d.localizationKeys)("userProfile.start.passwordSection.primaryButton__setPassword")})})]})}),(0,i.tZ)(C.a.Open,{value:"edit",children:(0,i.tZ)(C.a.Card,{children:(0,i.tZ)(tb,{})})})]})})},tS=(0,a.withCardStateProvider)(()=>{let{attributes:e,instanceIsPasswordBased:t}=(0,c.useEnvironment)().userSettings,r=(0,a.useCardState)(),{user:o}=(0,y.aF)(),l=e.passkey?.enabled,n=M(e).length>0,s=o?.deleteSelfEnabled;return(0,i.tZ)(d.Col,{elementDescriptor:d.descriptors.page,sx:e=>({gap:e.space.$8}),children:(0,i.BX)(d.Col,{elementDescriptor:d.descriptors.profilePage,elementId:d.descriptors.profilePage.setId("security"),children:[(0,i.tZ)(P.h.Root,{children:(0,i.tZ)(P.h.Title,{localizationKey:(0,d.localizationKeys)("userProfile.start.headerTitle__security"),sx:e=>({marginBottom:e.space.$4}),textVariant:"h2"})}),(0,i.tZ)(v.Z.Alert,{children:r.error}),t&&(0,i.tZ)(tz,{}),l&&(0,i.tZ)(tp,{}),n&&(0,i.tZ)(tl,{}),(0,i.tZ)(eL,{}),s&&(0,i.tZ)(eM,{})]})})}),tC=(0,o.lazy)(()=>Promise.all([r.e("507"),r.e("200"),r.e("573"),r.e("82")]).then(r.bind(r,6793)).then(e=>({default:e.BillingPage}))),tK=(0,o.lazy)(()=>Promise.all([r.e("200"),r.e("573"),r.e("616"),r.e("809")]).then(r.bind(r,7984)).then(e=>({default:e.APIKeysPage}))),tx=()=>{let{pages:e}=(0,c.useUserProfileContext)(),{apiKeysSettings:t,commerceSettings:r}=(0,c.useEnvironment)(),a=e.routes[0].id===s.xM.ACCOUNT,l=e.routes[0].id===s.xM.SECURITY,n=e.routes[0].id===s.xM.BILLING,d=e.routes[0].id===s.xM.API_KEYS,h=e.contents?.map((e,t)=>{let r=!a&&!l&&0===t;return i.tZ(u.Route,{index:r,path:r?void 0:e.url,children:i.tZ(p.O,{mount:e.mount,unmount:e.unmount})},`custom-page-${e.url}`)});return(0,i.BX)(u.Switch,{children:[h,(0,i.BX)(u.Route,{children:[(0,i.tZ)(u.Route,{path:a?void 0:"account",children:(0,i.tZ)(u.Switch,{children:(0,i.tZ)(u.Route,{index:!0,children:(0,i.tZ)(ew,{})})})}),(0,i.tZ)(u.Route,{path:l?void 0:"security",children:(0,i.tZ)(u.Switch,{children:(0,i.tZ)(u.Route,{index:!0,children:(0,i.tZ)(tS,{})})})}),r.billing.enabled&&r.billing.hasPaidUserPlans&&(0,i.tZ)(u.Route,{path:n?void 0:"billing",children:(0,i.BX)(u.Switch,{children:[(0,i.tZ)(u.Route,{index:!0,children:(0,i.tZ)(o.Suspense,{fallback:"",children:(0,i.tZ)(tC,{})})}),(0,i.tZ)(u.Route,{path:"plans",children:(0,i.tZ)(o.Suspense,{fallback:"",children:(0,i.tZ)(eT,{})})}),(0,i.tZ)(u.Route,{path:"statement/:statementId",children:(0,i.tZ)(o.Suspense,{fallback:"",children:(0,i.tZ)(g.j,{})})}),(0,i.tZ)(u.Route,{path:"payment-attempt/:paymentAttemptId",children:(0,i.tZ)(o.Suspense,{fallback:"",children:(0,i.tZ)(f.r,{})})})]})}),t.enabled&&(0,i.tZ)(u.Route,{path:d?void 0:"api-keys",children:(0,i.tZ)(u.Switch,{children:(0,i.tZ)(u.Route,{index:!0,children:(0,i.tZ)(o.Suspense,{fallback:"",children:(0,i.tZ)(tK,{})})})})})]})]})};var tR=r(2786);let t_=(0,c.withCoreUserGuard)(()=>{let e=o.useRef(null);return(0,i.tZ)(n.P.Root,{children:(0,i.BX)(m,{contentRef:e,children:[(0,i.tZ)(l.ap,{navbarTitleLocalizationKey:(0,d.localizationKeys)("userProfile.navbar.title")}),(0,i.tZ)(n.P.Content,{contentRef:e,scrollBoxId:s.g8,children:(0,i.tZ)(tx,{})})]})})}),tB=(0,a.withCardStateProvider)(e=>(0,i.tZ)(d.Flow.Root,{flow:"userProfile",children:(0,i.tZ)(d.Flow.Part,{children:(0,i.BX)(u.Switch,{children:[(0,i.tZ)(u.Route,{path:"verify",children:(0,i.tZ)(tR.M,{})}),(0,i.tZ)(u.Route,{children:(0,i.tZ)(t_,{})})]})})})),tw=e=>{let t={...e,routing:"virtual",componentName:"UserProfile",mode:"modal"};return(0,i.tZ)(u.Route,{path:"user",children:(0,i.tZ)(c.UserProfileContext.Provider,{value:t,children:(0,i.tZ)("div",{children:(0,i.tZ)(tB,{...t})})})})}}}]);