import { ArrowBlockButton } from '../elements/ArrowBlockButton';
import type { PropsOfComponent } from '../styledSystem';
type BlockButtonProps = PropsOfComponent<typeof ArrowBlockButton>;
export declare const BlockButton: (props: BlockButtonProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export declare const AddBlockButton: (props: BlockButtonProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
