import type { PhoneCodeChannel, PreferredSignInStrategy, SignInFactor, SignInResource, SignInStrategy } from '@clerk/types';
import { type FormControlState } from '../../utils';
export declare function determineStartingSignInFactor(firstFactors: SignInFactor[] | null, identifier: string | null, preferredSignInStrategy: PreferredSignInStrategy): SignInFactor | null | undefined;
export declare function determineSalutation(signIn: Partial<SignInResource>): string;
export declare function factorHasLocalStrategy(factor: SignInFactor | undefined | null): boolean;
export declare function determineStartingSignInSecondFactor(secondFactors: SignInFactor[] | null): SignInFactor | null;
export declare const isResetPasswordStrategy: (strategy: SignInStrategy | string | null | undefined) => boolean;
export declare function getSignUpAttributeFromIdentifier(identifier: FormControlState<'identifier'>): "username" | "emailAddress" | "phoneNumber";
export declare const getPreferredAlternativePhoneChannel: (fields: Array<FormControlState<string>>, preferredChannels: Record<string, PhoneCodeChannel> | null, phoneNumberFieldName: "identifier" | "phoneNumber") => PhoneCodeChannel | null;
export declare const getPreferredAlternativePhoneChannelForCombinedFlow: (preferredChannels: Record<string, PhoneCodeChannel> | null, identifierAttribute: "phoneNumber" | "emailAddress" | "username", identifierValue: string) => PhoneCodeChannel | null;
