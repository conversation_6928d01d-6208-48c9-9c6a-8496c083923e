import type { BackupCodeResource, CreateEmailAddressParams, CreateExternalAccountParams, CreatePhoneNumberParams, CreateWeb3WalletParams, DeletedObjectResource, EmailAddressResource, EnterpriseAccountResource, ExternalAccountResource, GetOrganizationMemberships, GetUserOrganizationInvitationsParams, GetUserOrganizationSuggestionsParams, ImageResource, OrganizationMembershipResource, PasskeyResource, PhoneNumberResource, RemoveUserPasswordParams, SamlAccountResource, SetProfileImageParams, TOTPResource, UpdateUserParams, UpdateUserPasswordParams, UserJSON, UserJSONSnapshot, UserResource, VerifyTOTPParams, Web3WalletResource } from '@clerk/types';
import { addPaymentSource, getPaymentSources, initializePaymentSource } from '../modules/commerce';
import { BaseResource, OrganizationSuggestion, SessionWithActivities, UserOrganizationInvitation } from './internal';
export declare class User extends BaseResource implements UserResource {
    pathRoot: string;
    id: string;
    externalId: string | null;
    username: string | null;
    emailAddresses: EmailAddressResource[];
    phoneNumbers: PhoneNumberResource[];
    web3Wallets: Web3WalletResource[];
    externalAccounts: ExternalAccountResource[];
    enterpriseAccounts: EnterpriseAccountResource[];
    passkeys: PasskeyResource[];
    samlAccounts: SamlAccountResource[];
    organizationMemberships: OrganizationMembershipResource[];
    passwordEnabled: boolean;
    firstName: string | null;
    lastName: string | null;
    fullName: string | null;
    primaryEmailAddressId: string | null;
    primaryEmailAddress: EmailAddressResource | null;
    primaryPhoneNumberId: string | null;
    primaryPhoneNumber: PhoneNumberResource | null;
    primaryWeb3WalletId: string | null;
    primaryWeb3Wallet: Web3WalletResource | null;
    imageUrl: string;
    hasImage: boolean;
    twoFactorEnabled: boolean;
    totpEnabled: boolean;
    backupCodeEnabled: boolean;
    publicMetadata: UserPublicMetadata;
    unsafeMetadata: UserUnsafeMetadata;
    createOrganizationEnabled: boolean;
    createOrganizationsLimit: number | null;
    deleteSelfEnabled: boolean;
    lastSignInAt: Date | null;
    legalAcceptedAt: Date | null;
    updatedAt: Date | null;
    createdAt: Date | null;
    private cachedSessionsWithActivities;
    static isUserResource(resource: unknown): resource is User;
    constructor(data: UserJSON | UserJSONSnapshot | null);
    protected path(): string;
    isPrimaryIdentification: (ident: EmailAddressResource | PhoneNumberResource | Web3WalletResource) => boolean;
    createEmailAddress: (params: CreateEmailAddressParams) => Promise<EmailAddressResource>;
    createPasskey: () => Promise<PasskeyResource>;
    createPhoneNumber: (params: CreatePhoneNumberParams) => Promise<PhoneNumberResource>;
    createWeb3Wallet: (params: CreateWeb3WalletParams) => Promise<Web3WalletResource>;
    createExternalAccount: (params: CreateExternalAccountParams) => Promise<ExternalAccountResource>;
    createTOTP: () => Promise<TOTPResource>;
    verifyTOTP: ({ code }: VerifyTOTPParams) => Promise<TOTPResource>;
    disableTOTP: () => Promise<DeletedObjectResource>;
    createBackupCode: () => Promise<BackupCodeResource>;
    update: (params: UpdateUserParams) => Promise<UserResource>;
    updatePassword: (params: UpdateUserPasswordParams) => Promise<UserResource>;
    removePassword: (params: RemoveUserPasswordParams) => Promise<UserResource>;
    delete: () => Promise<void>;
    getSessions: () => Promise<SessionWithActivities[]>;
    setProfileImage: (params: SetProfileImageParams) => Promise<ImageResource>;
    getOrganizationInvitations: (params?: GetUserOrganizationInvitationsParams) => Promise<import("@clerk/types").ClerkPaginatedResponse<UserOrganizationInvitation>>;
    getOrganizationSuggestions: (params?: GetUserOrganizationSuggestionsParams) => Promise<import("@clerk/types").ClerkPaginatedResponse<OrganizationSuggestion>>;
    getOrganizationMemberships: GetOrganizationMemberships;
    leaveOrganization: (organizationId: string) => Promise<DeletedObjectResource>;
    initializePaymentSource: typeof initializePaymentSource;
    addPaymentSource: typeof addPaymentSource;
    getPaymentSources: typeof getPaymentSources;
    get verifiedExternalAccounts(): ExternalAccountResource[];
    get unverifiedExternalAccounts(): ExternalAccountResource[];
    get verifiedWeb3Wallets(): Web3WalletResource[];
    get hasVerifiedEmailAddress(): boolean;
    get hasVerifiedPhoneNumber(): boolean;
    protected fromJSON(data: UserJSON | UserJSONSnapshot | null): this;
    __internal_toSnapshot(): UserJSONSnapshot;
}
