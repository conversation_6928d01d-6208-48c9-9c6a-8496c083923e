import type { __internal_ComponentNavigationContext, ClerkOptions, EnvironmentResource, SessionTask } from '@clerk/types';
export declare const SESSION_TASK_ROUTE_BY_KEY: Record<SessionTask['key'], string>;
interface NavigateToTaskOptions {
    componentNavigationContext: __internal_ComponentNavigationContext | null;
    globalNavigate: (to: string) => Promise<unknown>;
    options: ClerkOptions;
    environment: EnvironmentResource;
}
/**
 * Handles navigation to the tasks URL based on the application context such
 * as internal component routing or custom flows.
 * @internal
 */
export declare function navigateToTask(routeKey: keyof typeof SESSION_TASK_ROUTE_BY_KEY, { componentNavigationContext, globalNavigate, options, environment }: NavigateToTaskOptions): Promise<unknown>;
export {};
