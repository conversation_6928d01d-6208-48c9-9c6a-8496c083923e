import type { FieldId } from '@clerk/types';
import React from 'react';
import type { LocalizationKey } from '../customizables';
import { Button, Flex, Form as FormPrim } from '../customizables';
import type { PropsOfComponent } from '../styledSystem';
import type { OTPInputProps } from './CodeControl';
import { Field } from './FieldControl';
declare const useFormState: () => {
    isLoading: boolean;
    isDisabled: boolean;
    submittedWithEnter: boolean;
};
type FormProps = PropsOfComponent<typeof FormPrim>;
type CommonFieldRootProps = Omit<PropsOfComponent<typeof Field.Root>, 'children' | 'elementDescriptor' | 'elementId'>;
type CommonInputProps = CommonFieldRootProps & {
    isOptional?: boolean;
    actionLabel?: string | LocalizationKey;
    onActionClicked?: React.MouseEventHandler;
    icon?: React.ComponentType;
};
export declare const Form: {
    Root: (props: FormProps) => JSX.Element;
    ControlRow: (props: Omit<PropsOfComponent<typeof Flex>, "elementId"> & {
        elementId?: FieldId;
    }) => import("@emotion/react/jsx-runtime").JSX.Element;
    PlainInput: (props: CommonInputProps) => import("@emotion/react/jsx-runtime").JSX.Element;
    PasswordInput: React.ForwardRefExoticComponent<Omit<CommonInputProps, "ref"> & React.RefAttributes<HTMLInputElement>>;
    PhoneInput: (props: CommonInputProps) => import("@emotion/react/jsx-runtime").JSX.Element;
    OTPInput: (props: OTPInputProps) => import("@emotion/react/jsx-runtime").JSX.Element;
    InputGroup: (props: CommonInputProps & {
        groupPrefix?: string;
        groupSuffix?: string;
    }) => import("@emotion/react/jsx-runtime").JSX.Element;
    RadioGroup: (props: Omit<PropsOfComponent<typeof Field.Root>, "infoText" | "type" | "validatePassword" | "label" | "placeholder">) => import("@emotion/react/jsx-runtime").JSX.Element;
    Checkbox: (props: CommonFieldRootProps & {
        description?: string | LocalizationKey;
        termsLink?: string;
        privacyLink?: string;
    }) => import("@emotion/react/jsx-runtime").JSX.Element;
    SubmitButton: (props: PropsOfComponent<typeof Button>) => import("@emotion/react/jsx-runtime").JSX.Element;
    ResetButton: (props: PropsOfComponent<typeof Button>) => import("@emotion/react/jsx-runtime").JSX.Element;
};
export { useFormState };
