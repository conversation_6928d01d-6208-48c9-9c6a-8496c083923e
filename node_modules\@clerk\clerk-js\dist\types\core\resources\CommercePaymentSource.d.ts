import type { CommerceInitializedPaymentSourceJSON, CommerceInitializedPaymentSourceResource, CommercePaymentSourceJSON, CommercePaymentSourceResource, CommercePaymentSourceStatus, MakeDefaultPaymentSourceParams, RemovePaymentSourceParams } from '@clerk/types';
import { BaseResource, DeletedObject } from './internal';
export declare class CommercePaymentSource extends BaseResource implements CommercePaymentSourceResource {
    id: string;
    last4: string;
    paymentMethod: string;
    cardType: string;
    isDefault: boolean;
    isRemovable: boolean;
    status: CommercePaymentSourceStatus;
    walletType: string | undefined;
    constructor(data: CommercePaymentSourceJSON);
    protected fromJSON(data: CommercePaymentSourceJSON | null): this;
    remove(params?: RemovePaymentSourceParams): Promise<DeletedObject>;
    makeDefault(params?: MakeDefaultPaymentSourceParams): Promise<null>;
}
export declare class CommerceInitializedPaymentSource extends BaseResource implements CommerceInitializedPaymentSourceResource {
    externalClientSecret: string;
    externalGatewayId: string;
    paymentMethodOrder: string[];
    constructor(data: CommerceInitializedPaymentSourceJSON);
    protected fromJSON(data: CommerceInitializedPaymentSourceJSON | null): this;
}
