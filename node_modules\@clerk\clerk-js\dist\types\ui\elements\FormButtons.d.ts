import { Flex } from '../customizables';
import type { LocalizationKey } from '../localization';
import type { PropsOfComponent } from '../styledSystem';
import { Form } from './Form';
type FormButtonsProps = PropsOfComponent<typeof Form.SubmitButton> & {
    isDisabled?: boolean;
    onReset?: () => void;
    hideReset?: boolean;
    submitLabel?: LocalizationKey;
    resetLabel?: LocalizationKey;
};
export declare const FormButtons: (props: FormButtonsProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export declare const FormButtonContainer: (props: PropsOfComponent<typeof Flex>) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
