import type { CommerceCheckoutJSON, CommerceCheckoutResource, CommerceCheckoutTotals, CommerceSubscriptionPlanPeriod, ConfirmCheckoutParams } from '@clerk/types';
import { BaseResource, CommercePaymentSource, CommercePlan } from './internal';
export declare class CommerceCheckout extends BaseResource implements CommerceCheckoutResource {
    id: string;
    externalClientSecret: string;
    externalGatewayId: string;
    statement_id: string;
    paymentSource?: CommercePaymentSource;
    plan: CommercePlan;
    planPeriod: CommerceSubscriptionPlanPeriod;
    planPeriodStart: number | undefined;
    status: string;
    totals: CommerceCheckoutTotals;
    isImmediatePlanChange: boolean;
    constructor(data: CommerceCheckoutJSON, orgId?: string);
    protected fromJSON(data: CommerceCheckoutJSON | null): this;
    confirm: (params: ConfirmCheckoutParams) => Promise<this>;
}
