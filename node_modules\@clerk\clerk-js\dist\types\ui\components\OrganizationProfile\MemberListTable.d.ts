import React from 'react';
import type { LocalizationKey } from '../../customizables';
import { Tr } from '../../customizables';
import type { PropsOfComponent, ThemableCssProp } from '../../styledSystem';
type MembersListTableProps = {
    headers: LocalizationKey[];
    rows: React.ReactNode[];
    isLoading?: boolean;
    page: number;
    onPageChange: (page: number) => void;
    itemCount: number;
    emptyStateLocalizationKey: LocalizationKey;
    pageCount: number;
    itemsPerPage: number;
};
export declare const DataTable: (props: MembersListTableProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export declare const RowContainer: (props: PropsOfComponent<typeof Tr>) => import("@emotion/react/jsx-runtime").JSX.Element;
export declare const RoleSelect: (props: {
    roles: {
        label: string;
        value: string;
    }[] | undefined;
    value: string;
    fallbackLabel?: string;
    onChange: (params: string) => unknown;
    isDisabled?: boolean;
    triggerSx?: ThemableCssProp;
    optionListSx?: ThemableCssProp;
    prefixLocalizationKey?: LocalizationKey | string;
}) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
