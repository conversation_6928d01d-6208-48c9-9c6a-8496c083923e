(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["507"],{8057:function(e,t,n){"use strict";n.d(t,{ph:()=>$,fC:()=>D,hG:()=>_,OL:()=>M}),n(65223);var r,o=n(79109),i=n(83799),a=n(9386);n(56113),n(92037),n(50725);var c="https://js.stripe.com/v3",u=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,l=function(){for(var e=document.querySelectorAll('script[src^="'.concat(c,'"]')),t=0;t<e.length;t++){var n=e[t];if(u.test(n.src))return n}return null},s=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",n=document.createElement("script");n.src="".concat(c).concat(t);var r=document.head||document.body;if(!r)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return r.appendChild(n),n},d=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"5.6.0",startTime:t})},p=null,f=null,m=null,h=function(e,t,n){if(null===e)return null;var r=e.apply(void 0,t);return d(r,n),r},y=!1,g=function(){return r?r:r=(null!==p?p:(p=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var n,r=l();r?r&&null!==m&&null!==f&&(r.removeEventListener("load",m),r.removeEventListener("error",f),null===(n=r.parentNode)||void 0===n||n.removeChild(r),r=s(null)):r=s(null),m=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},f=function(){t(Error("Failed to load Stripe.js"))},r.addEventListener("load",m),r.addEventListener("error",f)}catch(e){t(e);return}})).catch(function(e){return p=null,Promise.reject(e)})).catch(function(e){return r=null,Promise.reject(e)})};Promise.resolve().then(function(){return g()}).catch(function(e){y||console.warn(e)});var v=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];y=!0;var r=Date.now();return g().then(function(e){return h(e,t,r)})},S=n(69144),b=n(69626),C=n(50903),E=n(44455),k=n(2672),w=n(70431),P=n(19460),x=n(68487);n(24152);var R=n(11576),j=n(39541),T=n(77623);let O=()=>{let{organization:e}=(0,i.o8)(),{user:t}=(0,i.aF)(),n="org"===(0,R.useSubscriberTypeContext)()?e:t,{data:r,trigger:o}=(0,C.Z)({key:"commerce-payment-source-initialize",resourceId:null==n?void 0:n.id},()=>null==n?void 0:n.initializePaymentSource({gateway:"stripe"})),{commerceSettings:a}=(0,R.useEnvironment)(),c=null==r?void 0:r.externalGatewayId,u=null==r?void 0:r.externalClientSecret,l=null==r?void 0:r.paymentMethodOrder,s=a.billing.stripePublishableKey,{data:d}=(0,b.default)(c&&s?{key:"stripe-sdk",externalGatewayId:c,stripePublishableKey:s}:null,e=>{let{stripePublishableKey:t,externalGatewayId:n}=e;return v(t,{stripeAccount:n})},{keepPreviousData:!0,revalidateOnFocus:!1,dedupingInterval:6e4});return{stripe:d,initializePaymentSource:o,externalClientSecret:u,paymentMethodOrder:l}},[Z,z]=(0,i.uH)("AddPaymentSourceRoot"),A=e=>{let{children:t,...n}=e,{initializePaymentSource:r,externalClientSecret:i,stripe:a,paymentMethodOrder:c}=O(),[u,l]=(0,S.useState)(void 0),[s,d]=(0,S.useState)(void 0),[p,f]=(0,S.useState)(void 0);return(0,S.useEffect)(()=>{r()},[]),(0,o.tZ)(Z.Provider,{value:{value:{headerTitle:u,headerSubtitle:s,submitLabel:p,setHeaderTitle:l,setHeaderSubtitle:d,setSubmitLabel:f,initializePaymentSource:r,externalClientSecret:i,stripe:a,paymentMethodOrder:c,...n}},children:t})},B=e=>{let{stripe:t,externalClientSecret:n}=z();return t&&n?null:e.children},I=e=>{let{externalClientSecret:t,stripe:n}=z(),{colors:r,fontWeights:i,fontSizes:c,radii:u,space:l}=(0,j.useAppearance)().parsedInternalTheme,s={variables:{colorPrimary:(0,T.YV)(r.$primary500),colorBackground:(0,T.YV)(r.$colorInputBackground),colorText:(0,T.YV)(r.$colorText),colorTextSecondary:(0,T.YV)(r.$colorTextSecondary),colorSuccess:(0,T.YV)(r.$success500),colorDanger:(0,T.YV)(r.$danger500),colorWarning:(0,T.YV)(r.$warning500),fontWeightNormal:i.$normal.toString(),fontWeightMedium:i.$medium.toString(),fontWeightBold:i.$bold.toString(),fontSizeXl:c.$xl,fontSizeLg:c.$lg,fontSizeSm:c.$md,fontSizeXs:c.$sm,borderRadius:u.$md,spacingUnit:l.$1}};return n&&t?(0,o.tZ)(a.Elements,{stripe:n,options:{clientSecret:t,appearance:s},children:e.children},t):null},D=e=>{let{children:t,...n}=e;return(0,o.BX)(A,{...n,children:[(0,o.tZ)(B,{children:(0,o.tZ)(j.Flex,{direction:"row",align:"center",justify:"center",sx:e=>({width:"100%",minHeight:e.sizes.$60}),children:(0,o.tZ)(j.Spinner,{size:"lg",colorScheme:"primary",elementDescriptor:j.descriptors.spinner})})}),(0,o.tZ)(I,{children:(0,o.tZ)(K,{children:t})})]})},L=(e,t)=>{(0,S.useRef)(()=>{t(e)}),(0,S.useEffect)(()=>{t(e)},[e,t])},$=e=>{let{text:t}=e,{setHeaderTitle:n}=z();return L(t,n),null},_=e=>{let{text:t}=e,{setHeaderSubtitle:n}=z();return L(t,n),null},M=e=>{let{text:t}=e,{setSubmitLabel:n}=z();return L(t,n),null},K=e=>{var t;let{children:n}=e,{headerTitle:r,headerSubtitle:i,submitLabel:c,checkout:u,initializePaymentSource:l,onSuccess:s,cancelAction:d,paymentMethodOrder:p}=z(),[f,m]=(0,S.useState)(!1),h=(0,a.useStripe)(),y=(0,k.useCardState)(),g=(0,a.useElements)(),{displayConfig:v}=(0,R.useEnvironment)(),{t:b}=(0,j.useLocalizations)(),C=(0,R.useSubscriberTypeLocalizationRoot)(),O=async e=>{if(e.preventDefault(),!h||!g)return;y.setLoading(),y.setError(void 0);let{setupIntent:t,error:n}=await h.confirmSetup({elements:g,confirmParams:{return_url:""},redirect:"if_required"});if(!n)try{await s({stripeSetupIntent:t})}catch(e){(0,T.S3)(e,[],y.setError)}finally{y.setIdle(),l()}};return(0,o.tZ)(x.Y,{headerTitle:r,headerSubtitle:i,children:(0,o.BX)(w.l.Root,{onSubmit:O,sx:e=>({display:"flex",flexDirection:"column",rowGap:e.space.$3}),children:[n,(0,o.tZ)(a.PaymentElement,{onReady:()=>m(!0),options:{layout:{type:"tabs",defaultCollapsed:!1},paymentMethodOrder:p,applePay:u?{recurringPaymentRequest:{paymentDescription:"".concat(b((0,j.localizationKeys)("month"===u.planPeriod?"commerce.paymentSource.applePayDescription.monthly":"commerce.paymentSource.applePayDescription.annual"))),managementURL:v.homeUrl,regularBilling:{amount:(null===(t=u.totals.totalDueNow)||void 0===t?void 0:t.amount)||u.totals.grandTotal.amount,label:u.plan.name,recurringPaymentIntervalUnit:"annual"===u.planPeriod?"year":"month"}}}:void 0}}),(0,o.tZ)(E.Z.Alert,{children:y.error}),(0,o.tZ)(P.A,{isDisabled:!f,submitLabel:null!=c?c:(0,j.localizationKeys)("".concat(C,".billingPage.paymentSourcesSection.formButtonPrimary__add")),onReset:d,hideReset:!d,sx:{flex:u?1:void 0}})]})})}},57106:function(e,t,n){"use strict";n.d(t,{j:()=>a});var r=n(79109),o=n(39541),i=n(96519);let a=e=>{let{paymentSource:t}=e;return(0,r.BX)(o.Flex,{sx:{overflow:"hidden"},gap:2,align:"baseline",elementDescriptor:o.descriptors.paymentSourceRow,children:[(0,r.tZ)(o.Icon,{icon:"card"===t.paymentMethod?i.aB:i.Nn,sx:{alignSelf:"center"},elementDescriptor:o.descriptors.paymentSourceRowIcon}),(0,r.tZ)(o.Text,{sx:e=>({color:e.colors.$colorText,textTransform:"capitalize"}),truncate:!0,elementDescriptor:o.descriptors.paymentSourceRowType,children:"card"===t.paymentMethod?t.cardType:t.paymentMethod}),(0,r.tZ)(o.Text,{sx:e=>({color:e.colors.$colorTextSecondary}),variant:"caption",truncate:!0,elementDescriptor:o.descriptors.paymentSourceRowValue,children:"card"===t.paymentMethod?"⋯ ".concat(t.last4):null}),t.isDefault&&(0,r.tZ)(o.Badge,{elementDescriptor:o.descriptors.paymentSourceRowBadge,elementId:o.descriptors.paymentSourceRowBadge.setId("default"),localizationKey:(0,o.localizationKeys)("badge__default")}),"expired"===t.status&&(0,r.tZ)(o.Badge,{elementDescriptor:o.descriptors.paymentSourceRowBadge,elementId:o.descriptors.paymentSourceRowBadge.setId("expired"),colorScheme:"danger",localizationKey:(0,o.localizationKeys)("badge__expired")})]})}},86054:function(e,t,n){"use strict";n.d(t,{Hw:()=>k});var r=n(8057);n(50725),n(838);var o=n(79109),i=n(83799),a=n(69144),c=n(2672),u=n(58104),l=n(19655),s=n(33009),d=n(8969),p=n(22189),f=n(11576),m=n(39541),h=n(99805),y=n(44709),g=n(77623),v=n(57106),S=n(28e3);let b=()=>{let{t:e}=(0,m.useLocalizations)();return(0,o.BX)(m.Box,{sx:e=>({background:e.colors.$neutralAlpha50,padding:e.space.$2x5,borderRadius:e.radii.$md,borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha100,display:"flex",flexDirection:"column",rowGap:e.space.$2,position:"relative"}),children:[(0,o.tZ)(m.Box,{sx:e=>({position:"absolute",inset:0,background:"repeating-linear-gradient(-45deg,".concat(e.colors.$warningAlpha100,",").concat(e.colors.$warningAlpha100," 6px,").concat(e.colors.$warningAlpha150," 6px,").concat(e.colors.$warningAlpha150," 12px)"),maskImage:"linear-gradient(transparent 20%, black)",pointerEvents:"none"})}),(0,o.BX)(m.Box,{sx:{display:"flex",alignItems:"baseline",justifyContent:"space-between"},children:[(0,o.tZ)(m.Text,{variant:"caption",colorScheme:"body",localizationKey:(0,m.localizationKeys)("commerce.paymentSource.dev.testCardInfo")}),(0,o.tZ)(m.Text,{variant:"caption",sx:e=>({color:e.colors.$warning500,fontWeight:e.fontWeights.$semibold}),localizationKey:(0,m.localizationKeys)("commerce.paymentSource.dev.developmentMode")})]}),(0,o.BX)(S.a.Root,{children:[(0,o.BX)(S.a.Group,{variant:"tertiary",children:[(0,o.tZ)(S.a.Title,{title:(0,m.localizationKeys)("commerce.paymentSource.dev.cardNumber")}),(0,o.tZ)(S.a.Description,{text:"4242 4242 4242 4242"})]}),(0,o.BX)(S.a.Group,{variant:"tertiary",children:[(0,o.tZ)(S.a.Title,{title:(0,m.localizationKeys)("commerce.paymentSource.dev.expirationDate")}),(0,o.tZ)(S.a.Description,{text:"11/44"})]}),(0,o.BX)(S.a.Group,{variant:"tertiary",children:[(0,o.tZ)(S.a.Title,{title:(0,m.localizationKeys)("commerce.paymentSource.dev.cvcZip")}),(0,o.tZ)(S.a.Description,{text:e((0,m.localizationKeys)("commerce.paymentSource.dev.anyNumbers"))})]})]})]})},C=(0,c.withCardStateProvider)(e=>{let{onSuccess:t}=e,{close:n}=(0,y.XC)(),a=(0,i.cL)(),c=(0,f.useSubscriberTypeContext)(),u=(0,f.useSubscriberTypeLocalizationRoot)(),l=async e=>{var r;let o="org"===c?null==a?void 0:a.organization:a.user;return await (null==o?void 0:o.addPaymentSource({gateway:"stripe",paymentToken:null===(r=e.stripeSetupIntent)||void 0===r?void 0:r.payment_method})),t(),n(),Promise.resolve()};return(0,o.BX)(r.fC,{onSuccess:l,cancelAction:n,children:[(0,o.tZ)(r.ph,{text:(0,m.localizationKeys)("".concat(u,".billingPage.paymentSourcesSection.add"))}),(0,o.tZ)(r.hG,{text:(0,m.localizationKeys)("".concat(u,".billingPage.paymentSourcesSection.addSubtitle"))}),(0,o.tZ)(p.P,{children:(0,o.tZ)(b,{})})]})}),E=e=>{let{paymentSource:t,revalidate:n}=e,{close:r}=(0,y.XC)(),u=(0,c.useCardState)(),l=(0,f.useSubscriberTypeContext)(),{organization:s}=(0,i.o8)(),p=(0,f.useSubscriberTypeLocalizationRoot)(),h=(0,a.useRef)("".concat("card"===t.paymentMethod?t.cardType:t.paymentMethod," ").concat("card"===t.paymentMethod?"⋯ ".concat(t.last4):"-"));if(!h.current)return null;let v=async()=>{await t.remove({orgId:"org"===l?null==s?void 0:s.id:void 0}).then(n).catch(e=>{(0,g.S3)(e,[],u.setError)})};return(0,o.tZ)(d.LE,{title:(0,m.localizationKeys)("".concat(p,".billingPage.paymentSourcesSection.removeResource.title")),messageLine1:(0,m.localizationKeys)("".concat(p,".billingPage.paymentSourcesSection.removeResource.messageLine1"),{identifier:h.current}),messageLine2:(0,m.localizationKeys)("".concat(p,".billingPage.paymentSourcesSection.removeResource.messageLine2")),successMessage:(0,m.localizationKeys)("".concat(p,".billingPage.paymentSourcesSection.removeResource.successMessage"),{paymentSource:h.current}),deleteResource:v,onSuccess:r,onReset:r})},k=(0,c.withCardStateProvider)(()=>{let e=(0,i.cL)(),t=(0,f.useSubscriberTypeContext)(),n=(0,f.useSubscriberTypeLocalizationRoot)(),r="org"===t?null==e?void 0:e.organization:e.user,{data:c,isLoading:s,mutate:d}=(0,f.usePaymentSources)(),{data:p=[]}=c||{},y=(0,a.useMemo)(()=>p.sort((e,t)=>e.isDefault&&!t.isDefault?-1:1),[p]),g=(0,a.useCallback)(()=>void d(),[d]);return r?(0,o.tZ)(l.zd.Root,{title:(0,m.localizationKeys)("".concat(n,".billingPage.paymentSourcesSection.title")),centered:!1,id:"paymentSources",sx:e=>({flex:1,borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100}),children:(0,o.tZ)(h.a.Root,{children:(0,o.tZ)(l.zd.ItemList,{id:"paymentSources",disableAnimation:!0,children:s?(0,o.tZ)(u.m,{}):(0,o.BX)(o.HY,{children:[y.map(e=>(0,o.BX)(a.Fragment,{children:[(0,o.BX)(l.zd.Item,{id:"paymentSources",children:[(0,o.tZ)(v.j,{paymentSource:e}),(0,o.tZ)(w,{paymentSource:e,revalidate:g})]}),(0,o.tZ)(h.a.Open,{value:"remove-".concat(e.id),children:(0,o.tZ)(h.a.Card,{variant:"destructive",children:(0,o.tZ)(E,{paymentSource:e,revalidate:g})})})]},e.id)),(0,o.tZ)(h.a.Trigger,{value:"add",children:(0,o.tZ)(l.zd.ArrowButton,{id:"paymentSources",localizationKey:(0,m.localizationKeys)("".concat(n,".billingPage.paymentSourcesSection.add"))})}),(0,o.tZ)(h.a.Open,{value:"add",children:(0,o.tZ)(h.a.Card,{children:(0,o.tZ)(C,{onSuccess:g})})})]})})})}):null}),w=e=>{let{paymentSource:t,revalidate:n}=e,{open:r}=(0,y.XC)(),a=(0,c.useCardState)(),{organization:u}=(0,i.o8)(),l=(0,f.useSubscriberTypeContext)(),d=(0,f.useSubscriberTypeLocalizationRoot)(),p=[{label:(0,m.localizationKeys)("".concat(d,".billingPage.paymentSourcesSection.actionLabel__remove")),isDestructive:!0,onClick:()=>r("remove-".concat(t.id)),isDisabled:!t.isRemovable}];return t.isDefault||p.unshift({label:(0,m.localizationKeys)("".concat(d,".billingPage.paymentSourcesSection.actionLabel__default")),isDestructive:!1,onClick:()=>{t.makeDefault({orgId:"org"===l?null==u?void 0:u.id:void 0}).then(n).catch(e=>{(0,g.S3)(e,[],a.setError)})},isDisabled:!1}),(0,o.tZ)(s.a,{actions:p})}},9386:function(e,t,n){n(64728),n(79876),n(65223),n(56113),n(92037),n(93008),n(28419),n(50725),function(e,t){"use strict";function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){i(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,r,o=e&&("undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null!=o){var i=[],a=!0,c=!1;try{for(o=o.call(e);!(a=(n=o.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){c=!0,r=e}finally{try{a||null==o.return||o.return()}finally{if(c)throw r}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return c(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var u,l,s,d,p,f={exports:{}};f.exports=(function(){if(p)return d;p=1;var e=s?l:(s=1,l="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");function t(){}function n(){}return n.resetWarningCache=t,d=function(){function r(t,n,r,o,i,a){if(a!==e){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function o(){return r}r.isRequired=r;var i={array:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:o,element:r,elementType:r,instanceOf:o,node:r,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:n,resetWarningCache:t};return i.PropTypes=i,i}})()();var m=(u=f.exports)&&u.__esModule&&Object.prototype.hasOwnProperty.call(u,"default")?u.default:u,h=function(e,n,r){var o=!!r,i=t.useRef(r);t.useEffect(function(){i.current=r},[r]),t.useEffect(function(){if(!o||!e)return function(){};var t=function(){i.current&&i.current.apply(i,arguments)};return e.on(n,t),function(){e.off(n,t)}},[o,n,e,i])},y=function(e){var n=t.useRef(e);return t.useEffect(function(){n.current=e},[e]),n.current},g=function(e){return null!==e&&"object"===o(e)},v="[object Object]",S=function e(t,n){if(!g(t)||!g(n))return t===n;var r=Array.isArray(t);if(r!==Array.isArray(n))return!1;var o=Object.prototype.toString.call(t)===v;if(o!==(Object.prototype.toString.call(n)===v))return!1;if(!o&&!r)return t===n;var i=Object.keys(t),a=Object.keys(n);if(i.length!==a.length)return!1;for(var c={},u=0;u<i.length;u+=1)c[i[u]]=!0;for(var l=0;l<a.length;l+=1)c[a[l]]=!0;var s=Object.keys(c);return s.length===i.length&&s.every(function(r){return e(t[r],n[r])})},b=function(e,t,n){return g(e)?Object.keys(e).reduce(function(o,a){var c=!g(t)||!S(e[a],t[a]);return n.includes(a)?(c&&console.warn("Unsupported prop change: options.".concat(a," is not a mutable property.")),o):c?r(r({},o||{}),{},i({},a,e[a])):o},null):null},C="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",E=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:C;if(null===e||g(e)&&"function"==typeof e.elements&&"function"==typeof e.createToken&&"function"==typeof e.createPaymentMethod&&"function"==typeof e.confirmCardPayment)return e;throw Error(t)},k=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:C;if(g(e)&&"function"==typeof e.then)return{tag:"async",stripePromise:Promise.resolve(e).then(function(e){return E(e,t)})};var n=E(e,t);return null===n?{tag:"empty"}:{tag:"sync",stripe:n}},w=function(e){e&&e._registerWrapper&&e.registerAppInfo&&(e._registerWrapper({name:"react-stripe-js",version:"3.1.0"}),e.registerAppInfo({name:"react-stripe-js",version:"3.1.0",url:"https://stripe.com/docs/stripe-js/react"}))},P=t.createContext(null);P.displayName="ElementsContext";var x=function(e,t){if(!e)throw Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},R=function(e){var n=e.stripe,r=e.options,o=e.children,i=t.useMemo(function(){return k(n)},[n]),c=a(t.useState(function(){return{stripe:"sync"===i.tag?i.stripe:null,elements:"sync"===i.tag?i.stripe.elements(r):null}}),2),u=c[0],l=c[1];t.useEffect(function(){var e=!0,t=function(e){l(function(t){return t.stripe?t:{stripe:e,elements:e.elements(r)}})};return"async"!==i.tag||u.stripe?"sync"!==i.tag||u.stripe||t(i.stripe):i.stripePromise.then(function(n){n&&e&&t(n)}),function(){e=!1}},[i,u,r]);var s=y(n);t.useEffect(function(){null!==s&&s!==n&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[s,n]);var d=y(r);return t.useEffect(function(){if(u.elements){var e=b(r,d,["clientSecret","fonts"]);e&&u.elements.update(e)}},[r,d,u.elements]),t.useEffect(function(){w(u.stripe)},[u.stripe]),t.createElement(P.Provider,{value:u},o)};R.propTypes={stripe:m.any,options:m.object};var j=function(e){return x(t.useContext(P),e)},T=function(e){return(0,e.children)(j("mounts <ElementsConsumer>"))};T.propTypes={children:m.func.isRequired};var O=["on","session"],Z=t.createContext(null);Z.displayName="CheckoutSdkContext";var z=function(e,t){if(!e)throw Error("Could not find CheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CheckoutProvider> provider."));return e},A=t.createContext(null);A.displayName="CheckoutContext";var B=function(e,t){if(!e)return null;e.on,e.session;var n=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,O);return t?r(r({},n),t):r(r({},n),e.session())},I=function(e){var n=e.stripe,r=e.options,o=e.children,i=t.useMemo(function(){return k(n,"Invalid prop `stripe` supplied to `CheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")},[n]),c=a(t.useState(null),2),u=c[0],l=c[1],s=a(t.useState(function(){return{stripe:"sync"===i.tag?i.stripe:null,checkoutSdk:null}}),2),d=s[0],p=s[1],f=function(e,t){p(function(n){return n.stripe&&n.checkoutSdk?n:{stripe:e,checkoutSdk:t}})},m=t.useRef(!1);t.useEffect(function(){var e=!0;return"async"!==i.tag||d.stripe?"sync"===i.tag&&i.stripe&&!m.current&&(m.current=!0,i.stripe.initCheckout(r).then(function(e){e&&(f(i.stripe,e),e.on("change",l))})):i.stripePromise.then(function(t){t&&e&&!m.current&&(m.current=!0,t.initCheckout(r).then(function(e){e&&(f(t,e),e.on("change",l))}))}),function(){e=!1}},[i,d,r,l]);var h=y(n);t.useEffect(function(){null!==h&&h!==n&&console.warn("Unsupported prop change on CheckoutProvider: You cannot change the `stripe` prop after setting it.")},[h,n]);var v=y(r);t.useEffect(function(){if(d.checkoutSdk){!r.clientSecret||g(v)||S(r.clientSecret,v.clientSecret)||console.warn("Unsupported prop change: options.clientSecret is not a mutable property.");var e,t,n=null==v?void 0:null===(e=v.elementsOptions)||void 0===e?void 0:e.appearance,o=null==r?void 0:null===(t=r.elementsOptions)||void 0===t?void 0:t.appearance;o&&!S(o,n)&&d.checkoutSdk.changeAppearance(o)}},[r,v,d.checkoutSdk]),t.useEffect(function(){w(d.stripe)},[d.stripe]);var b=t.useMemo(function(){return B(d.checkoutSdk,u)},[d.checkoutSdk,u]);return d.checkoutSdk?t.createElement(Z.Provider,{value:d},t.createElement(A.Provider,{value:b},o)):null};I.propTypes={stripe:m.any,options:m.shape({clientSecret:m.string.isRequired,elementsOptions:m.object}).isRequired};var D=function(e){var n=t.useContext(Z),r=t.useContext(P);if(n&&r)throw Error("You cannot wrap the part of your app that ".concat(e," in both <CheckoutProvider> and <Elements> providers."));return n?z(n,e):x(r,e)},L=function(e,n){var r="".concat(e.charAt(0).toUpperCase()+e.slice(1),"Element"),o=n?function(e){D("mounts <".concat(r,">"));var n=e.id,o=e.className;return t.createElement("div",{id:n,className:o})}:function(n){var o,i=n.id,c=n.className,u=n.options,l=void 0===u?{}:u,s=n.onBlur,d=n.onFocus,p=n.onReady,f=n.onChange,m=n.onEscape,g=n.onClick,v=n.onLoadError,S=n.onLoaderStart,C=n.onNetworksChange,E=n.onConfirm,k=n.onCancel,w=n.onShippingAddressChange,P=n.onShippingRateChange,x=D("mounts <".concat(r,">")),R="elements"in x?x.elements:null,j="checkoutSdk"in x?x.checkoutSdk:null,T=a(t.useState(null),2),O=T[0],Z=T[1],z=t.useRef(null),A=t.useRef(null);h(O,"blur",s),h(O,"focus",d),h(O,"escape",m),h(O,"click",g),h(O,"loaderror",v),h(O,"loaderstart",S),h(O,"networkschange",C),h(O,"confirm",E),h(O,"cancel",k),h(O,"shippingaddresschange",w),h(O,"shippingratechange",P),h(O,"change",f),p&&(o="expressCheckout"===e?p:function(){p(O)}),h(O,"ready",o),t.useLayoutEffect(function(){if(null===z.current&&null!==A.current&&(R||j)){var t=null;j?t=j.createElement(e,l):R&&(t=R.create(e,l)),z.current=t,Z(t),t&&t.mount(A.current)}},[R,j,l]);var B=y(l);return t.useEffect(function(){if(z.current){var e=b(l,B,["paymentRequest"]);e&&"update"in z.current&&z.current.update(e)}},[l,B]),t.useLayoutEffect(function(){return function(){if(z.current&&"function"==typeof z.current.destroy)try{z.current.destroy(),z.current=null}catch(e){}}},[]),t.createElement("div",{id:i,className:c,ref:A})};return o.propTypes={id:m.string,className:m.string,onChange:m.func,onBlur:m.func,onFocus:m.func,onReady:m.func,onEscape:m.func,onClick:m.func,onLoadError:m.func,onLoaderStart:m.func,onNetworksChange:m.func,onConfirm:m.func,onCancel:m.func,onShippingAddressChange:m.func,onShippingRateChange:m.func,options:m.object},o.displayName=r,o.__elementType=e,o},$="undefined"==typeof window,_=t.createContext(null);_.displayName="EmbeddedCheckoutProviderContext";var M=function(){var e=t.useContext(_);if(!e)throw Error("<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>");return e},K=$?function(e){var n=e.id,r=e.className;return M(),t.createElement("div",{id:n,className:r})}:function(e){var n=e.id,r=e.className,o=M().embeddedCheckout,i=t.useRef(!1),a=t.useRef(null);return t.useLayoutEffect(function(){return!i.current&&o&&null!==a.current&&(o.mount(a.current),i.current=!0),function(){if(i.current&&o)try{o.unmount(),i.current=!1}catch(e){}}},[o]),t.createElement("div",{ref:a,id:n,className:r})},N=L("auBankAccount",$),Y=L("card",$),U=L("cardNumber",$),W=L("cardExpiry",$),X=L("cardCvc",$),F=L("fpxBank",$),q=L("iban",$),V=L("idealBank",$),G=L("p24Bank",$),H=L("epsBank",$),J=L("payment",$),Q=L("expressCheckout",$),ee=L("currencySelector",$),et=L("paymentRequestButton",$),en=L("linkAuthentication",$),er=L("address",$),eo=L("shippingAddress",$),ei=L("paymentMethodMessaging",$),ea=L("affirmMessage",$),ec=L("afterpayClearpayMessage",$);e.AddressElement=er,e.AffirmMessageElement=ea,e.AfterpayClearpayMessageElement=ec,e.AuBankAccountElement=N,e.CardCvcElement=X,e.CardElement=Y,e.CardExpiryElement=W,e.CardNumberElement=U,e.CheckoutProvider=I,e.CurrencySelectorElement=ee,e.Elements=R,e.ElementsConsumer=T,e.EmbeddedCheckout=K,e.EmbeddedCheckoutProvider=function(e){var n=e.stripe,r=e.options,o=e.children,i=t.useMemo(function(){return k(n,"Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")},[n]),c=t.useRef(null),u=t.useRef(null),l=a(t.useState({embeddedCheckout:null}),2),s=l[0],d=l[1];t.useEffect(function(){if(!u.current&&!c.current){var e=function(e){u.current||c.current||(u.current=e,c.current=u.current.initEmbeddedCheckout(r).then(function(e){d({embeddedCheckout:e})}))};"async"===i.tag&&!u.current&&(r.clientSecret||r.fetchClientSecret)?i.stripePromise.then(function(t){t&&e(t)}):"sync"===i.tag&&!u.current&&(r.clientSecret||r.fetchClientSecret)&&e(i.stripe)}},[i,r,s,u]),t.useEffect(function(){return function(){s.embeddedCheckout?(c.current=null,s.embeddedCheckout.destroy()):c.current&&c.current.then(function(){c.current=null,s.embeddedCheckout&&s.embeddedCheckout.destroy()})}},[s.embeddedCheckout]),t.useEffect(function(){w(u)},[u]);var p=y(n);t.useEffect(function(){null!==p&&p!==n&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.")},[p,n]);var f=y(r);return t.useEffect(function(){if(null!=f){if(null==r){console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them.");return}void 0===r.clientSecret&&void 0===r.fetchClientSecret&&console.warn("Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`."),null!=f.clientSecret&&r.clientSecret!==f.clientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=f.fetchClientSecret&&r.fetchClientSecret!==f.fetchClientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=f.onComplete&&r.onComplete!==f.onComplete&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it."),null!=f.onShippingDetailsChange&&r.onShippingDetailsChange!==f.onShippingDetailsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it."),null!=f.onLineItemsChange&&r.onLineItemsChange!==f.onLineItemsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.")}},[f,r]),t.createElement(_.Provider,{value:s},o)},e.EpsBankElement=H,e.ExpressCheckoutElement=Q,e.FpxBankElement=F,e.IbanElement=q,e.IdealBankElement=V,e.LinkAuthenticationElement=en,e.P24BankElement=G,e.PaymentElement=J,e.PaymentMethodMessagingElement=ei,e.PaymentRequestButtonElement=et,e.ShippingAddressElement=eo,e.useCheckout=function(){e="calls useCheckout()",z(t.useContext(Z),e);var e,n=t.useContext(A);if(!n)throw Error("Could not find Checkout Context; You need to wrap the part of your app that calls useCheckout() in an <CheckoutProvider> provider.");return n},e.useElements=function(){return j("calls useElements()").elements},e.useStripe=function(){return D("calls useStripe()").stripe}}(t,n(69144))}}]);