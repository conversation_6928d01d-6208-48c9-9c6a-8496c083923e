import type { ClerkOptions } from '@clerk/types';
import React from 'react';
export declare const OptionsContext: React.Context<ClerkOptions>;
interface OptionsProviderProps {
    children: React.ReactNode;
    value: ClerkOptions;
}
declare function OptionsProvider({ children, value }: OptionsProviderProps): JSX.Element;
declare function useOptions(): ClerkOptions;
export { OptionsProvider, useOptions };
