export type OnCreateParams = {
    name: string;
    description?: string;
    expiration: number | undefined;
};
interface CreateApiKeyFormProps {
    onCreate: (params: OnCreateParams, closeCardFn: () => void) => void;
    isSubmitting: boolean;
}
export type Expiration = 'never' | '30d' | '90d' | 'custom';
export declare const CreateApiKeyForm: ({ onCreate, isSubmitting }: CreateApiKeyFormProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
