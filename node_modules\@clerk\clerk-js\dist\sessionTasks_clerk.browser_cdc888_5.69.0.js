"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["877"],{1618:function(e,t,n){n.r(t),n.d(t,{SessionTask:()=>f});var r=n(9109),i=n(3799),o=n(2810),a=n(9144),l=n(4455),s=n(2672),u=n(1455),c=n(6663),k=n(1576),d=n(6988),h=n(4676),g=n(683);let Z=(0,s.withCardStateProvider)(()=>{let e=(0,i.cL)(),{navigate:t}=(0,h.useRouter)(),{redirectUrlComplete:n}=(0,d.G)();return(0,a.useEffect)(()=>{let t=setTimeout(()=>{e.__experimental_navigateToTask({redirectUrlComplete:n})},500);return()=>clearTimeout(t)},[t,e,n]),(0,r.BX)(l.<PERSON>.<PERSON>,{children:[(0,r.tZ)(l.Z.Content,{children:(0,r.tZ)(u.I,{})}),(0,r.tZ)(l.Z.Footer,{})]})});function _(){return(0,r.BX)(h.Switch,{children:[(0,r.tZ)(h.Route,{path:c.m.org,children:(0,r.tZ)(k.OrganizationListContext.Provider,{value:{componentName:"OrganizationList",skipInvitationScreen:!0},children:(0,r.tZ)(g.OrganizationList,{})})}),(0,r.tZ)(h.Route,{index:!0,children:(0,r.tZ)(Z,{})})]})}function f(){let e=(0,i.cL)(),{navigate:t}=(0,h.useRouter)(),n=(0,a.useContext)(k.SignInContext),l=(0,a.useContext)(k.SignUpContext),s=n?.afterSignInUrl??l?.afterSignUpUrl??e?.buildAfterSignInUrl();(0,a.useEffect)(()=>{let n=e.session?.currentTask;if(!n){t(s);return}e.telemetry?.record(o.Zg("SessionTask",{task:n.key}))},[e,t,s]);let u=(0,a.useCallback)(()=>e.__experimental_navigateToTask({redirectUrlComplete:s}),[e,s]);return(0,r.tZ)(d.H.Provider,{value:{nextTask:u,redirectUrlComplete:s},children:(0,r.tZ)(_,{})})}}}]);