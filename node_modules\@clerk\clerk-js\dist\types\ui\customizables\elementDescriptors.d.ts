import type { ElementObjectKey, ElementsConfig, IdSelectors, StateSelectors } from '@clerk/types';
export declare const CLASS_PREFIX = "cl-";
export declare const ID_CLASS_PREFIX = "cl-id-";
export declare const OBJ_KEY_DELIMITER = "__";
/**
 * This object is strictly typed using the ElementsConfig type
 * and is used as the single source of truth to generate the
 * descriptor map
 */
export declare const APPEARANCE_KEYS: (keyof ElementsConfig)[];
type TargettableClassname<K extends keyof ElementsConfig> = `${typeof CLASS_PREFIX}${K}`;
type AllowedIds<T extends keyof ElementsConfig> = ElementsConfig[T]['ids'];
type AllowedStates<T extends keyof ElementsConfig> = ElementsConfig[T]['states'];
type ObjectKeyWithState<K extends keyof ElementsConfig> = StateSelectors<K, ElementsConfig[K]['states']>;
type ObjectKeyWithIds<K extends keyof ElementsConfig> = IdSelectors<K, ElementsConfig[K]['ids']>;
type ObjectKeyWithIdAndState<K extends keyof ElementsConfig> = StateSelectors<IdSelectors<K, ElementsConfig[K]['ids']>, ElementsConfig[K]['states']>;
export type ElementId<Id = string> = {
    id: Id;
    __type: 'id';
};
export type ElementDescriptor<K extends keyof ElementsConfig = any> = {
    targettableClassname: TargettableClassname<K>;
    objectKey: ElementObjectKey<K>;
    getTargettableIdClassname: (params: {
        id: AllowedIds<K> | never;
    }) => string;
    getObjectKeyWithState: (state: AllowedStates<K> | never) => ObjectKeyWithState<K>;
    getObjectKeyWithId: (param: ElementId<AllowedIds<K>> | never) => ObjectKeyWithIds<K>;
    getObjectKeyWithIdAndState: (id: ElementId<AllowedIds<K>>, state: AllowedStates<K>) => ObjectKeyWithIdAndState<K>;
    setId: <Id extends AllowedIds<K>>(id?: Id) => ElementId<Id> | undefined;
};
type ElementDescriptors = {
    [k in keyof ElementsConfig as ElementObjectKey<k>]: ElementDescriptor<k>;
};
export declare const descriptors: ElementDescriptors;
export {};
