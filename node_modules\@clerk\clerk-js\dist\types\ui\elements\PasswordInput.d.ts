import type { <PERSON><PERSON>IError } from '@clerk/types';
import React from 'react';
import { Input } from '../customizables';
import type { PropsOfComponent } from '../styledSystem';
type PasswordInputProps = PropsOfComponent<typeof Input> & {
    validatePassword?: boolean;
    setError: (error: string | ClerkAPIError | undefined) => void;
    setWarning: (warning: string) => void;
    setSuccess: (message: string) => void;
    setInfo: (info: string) => void;
    setHasPassedComplexity: (b: boolean) => void;
};
export declare const PasswordInput: React.ForwardRefExoticComponent<Omit<PasswordInputProps, "ref"> & React.RefAttributes<HTMLInputElement>>;
export {};
