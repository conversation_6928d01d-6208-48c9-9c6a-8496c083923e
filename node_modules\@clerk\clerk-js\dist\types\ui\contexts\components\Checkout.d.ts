import type { CheckoutCtx } from '../../types';
export declare const CheckoutContext: import("react").Context<CheckoutCtx | null>;
export declare const useCheckoutContext: () => {
    componentName: "Checkout";
    newSubscriptionRedirectUrl: string | undefined;
    subscriber: () => import("@clerk/types").UserResource | import("@clerk/types").OrganizationResource;
    appearance?: import("@clerk/types").CheckoutTheme;
    planId?: string;
    planPeriod?: import("@clerk/types").CommerceSubscriptionPlanPeriod;
    subscriberType?: import("@clerk/types").CommerceSubscriberType;
    onSubscriptionComplete?: () => void;
    portalId?: string;
    portalRoot?: HTMLElement | null | undefined;
    onClose?: () => void;
};
