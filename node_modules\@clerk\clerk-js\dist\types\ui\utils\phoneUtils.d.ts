import type { PhoneCodeChannel } from '@clerk/types';
import type { CountryEntry, CountryIso } from '../elements/PhoneInput/countryCodeData';
export declare function getFlagEmojiFromCountryIso(iso: CountryIso, fallbackIso?: string): string;
export declare function getCountryIsoFromFormattedNumber(formattedNumber: string, fallbackIso?: string): string;
export declare function formatPhoneNumber(phoneNumber: string, pattern: string | undefined, countryCode?: string): string;
export declare function extractDigits(formattedPhone: string): string;
export declare function parsePhoneString(str: string): {
    iso: "at" | "br" | "hr" | "li" | "td" | "th" | "tr" | "id" | "mn" | "mo" | "ms" | "tt" | "sm" | "md" | "us" | "gb" | "in" | "ca" | "de" | "fr" | "ru" | "af" | "al" | "dz" | "as" | "ad" | "ao" | "ai" | "ag" | "ar" | "am" | "aw" | "au" | "az" | "bs" | "bh" | "bd" | "bb" | "by" | "be" | "bz" | "bj" | "bm" | "bt" | "bo" | "ba" | "bw" | "io" | "vg" | "bn" | "bg" | "bf" | "bi" | "kh" | "cm" | "cv" | "bq" | "ky" | "cf" | "cl" | "cn" | "co" | "km" | "cd" | "cg" | "ck" | "cr" | "ci" | "cu" | "cw" | "cy" | "cz" | "dk" | "dj" | "dm" | "do" | "ec" | "eg" | "sv" | "gq" | "er" | "ee" | "et" | "fk" | "fo" | "fj" | "fi" | "gf" | "pf" | "ga" | "gm" | "ge" | "gh" | "gi" | "gr" | "gl" | "gd" | "gp" | "gu" | "gt" | "gn" | "gw" | "gy" | "ht" | "hn" | "hk" | "hu" | "is" | "ir" | "iq" | "ie" | "il" | "it" | "jm" | "jp" | "jo" | "kz" | "ke" | "ki" | "kw" | "kg" | "la" | "lv" | "lb" | "ls" | "lr" | "ly" | "lt" | "lu" | "mk" | "mg" | "mw" | "my" | "mv" | "ml" | "mt" | "mh" | "mq" | "mr" | "mu" | "mx" | "fm" | "mc" | "me" | "ma" | "mz" | "mm" | "na" | "nr" | "np" | "nl" | "nc" | "nz" | "ni" | "ne" | "ng" | "nu" | "nf" | "kp" | "mp" | "no" | "om" | "pk" | "pw" | "ps" | "pa" | "pg" | "py" | "pe" | "ph" | "pl" | "pt" | "pr" | "qa" | "re" | "ro" | "rw" | "bl" | "sh" | "kn" | "lc" | "mf" | "pm" | "vc" | "ws" | "st" | "sa" | "sn" | "rs" | "sc" | "sl" | "sg" | "sx" | "sk" | "si" | "sb" | "so" | "za" | "kr" | "ss" | "es" | "lk" | "sd" | "sr" | "sz" | "se" | "ch" | "sy" | "tw" | "tj" | "tz" | "tl" | "tg" | "tk" | "to" | "tn" | "tm" | "tc" | "tv" | "vi" | "ug" | "ua" | "ae" | "uy" | "uz" | "vu" | "va" | "ve" | "vn" | "wf" | "ye" | "zm" | "zw";
    pattern: string;
    code: string;
    number: string;
    formattedNumberWithCode: string;
};
export declare function stringToFormattedPhoneString(str: string): string;
export declare const byPriority: (a: CountryEntry, b: CountryEntry) => number;
export declare function getCountryFromPhoneString(phone: string): {
    number: string;
    country: CountryEntry;
};
export declare function getPreferredPhoneCodeChannelByCountry(phoneNumber: string, preferredChannels: Record<string, PhoneCodeChannel>): PhoneCodeChannel | null;
