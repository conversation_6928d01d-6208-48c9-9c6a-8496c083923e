import type { FieldId } from '@clerk/types';
import type { ElementDescriptor } from '../customizables/elementDescriptors';
import type { ThemableCssProp } from '../styledSystem';
import type { useFormControlFeedback } from '../utils';
export declare function useFormTextAnimation(): {
    getFormTextAnimation: (enterAnimation: boolean, options?: {
        inDelay?: boolean;
    }) => ThemableCssProp;
};
export declare const useCalculateErrorTextHeight: ({ feedback }: {
    feedback: string;
}) => {
    height: number;
    calculateHeight: (element: HTMLElement | null) => void;
};
export type FormFeedbackDescriptorsKeys = 'error' | 'warning' | 'info' | 'success';
export type FormFeedbackProps = Partial<ReturnType<typeof useFormControlFeedback>['debounced'] & {
    id: FieldId;
}> & {
    errorMessageId?: string;
    elementDescriptors?: Partial<Record<FormFeedbackDescriptorsKeys, ElementDescriptor>>;
    center?: boolean;
    sx?: ThemableCssProp;
};
export declare const FormFeedback: (props: FormFeedbackProps) => import("@emotion/react/jsx-runtime").JSX.Element;
