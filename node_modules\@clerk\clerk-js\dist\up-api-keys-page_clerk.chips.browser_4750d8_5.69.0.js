"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["809"],{7984:function(e,l,t){t.r(l),t.d(l,{APIKeysPage:()=>h});var a=t(9109),i=t(3799),o=t(1576),r=t(9541),c=t(2654),n=t(5579),s=t(2010);let h=()=>{let{user:e}=(0,i.aF)(),{contentRef:l}=(0,n.jh)();return e?(0,a.BX)(r.Col,{gap:4,children:[(0,a.tZ)(c.h.Root,{children:(0,a.tZ)(c.h.Title,{localizationKey:(0,r.localizationKeys)("userProfile.apiKeysPage.title"),textVariant:"h2"})}),(0,a.tZ)(o.ApiKeysContext.Provider,{value:{componentName:"APIKeys"},children:(0,a.tZ)(s.APIKeysPage,{subject:e.id,revokeModalRoot:l})})]}):null}}}]);