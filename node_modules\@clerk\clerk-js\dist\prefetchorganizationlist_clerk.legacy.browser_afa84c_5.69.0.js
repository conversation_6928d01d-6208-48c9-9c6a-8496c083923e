"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["211"],{57484:function(t,e,n){n.d(e,{AO:()=>i,Sz:()=>r});let i={userMemberships:{infinite:!0},userInvitations:{infinite:!0},userSuggestions:{infinite:!0,status:["pending","accepted"]}},r=(t,e,n)=>{var i;if(void 0===e)return[{data:[t],total_count:1}];let r=(null==e?void 0:null===(i=e[e.length-1])||void 0===i?void 0:i.total_count)||1;return e.map(e=>{if(void 0===e)return e;let i=e.data.map(e=>e.id===t.id?{...t}:e);return{...e,data:i,total_count:"negative"===n?r-1:r}})}},66703:function(t,e,n){n.r(e),n.d(e,{OrganizationSwitcherPrefetch:()=>a});var i=n(83799),r=n(57484);function a(){return(0,i.eW)(r.AO),null}}}]);