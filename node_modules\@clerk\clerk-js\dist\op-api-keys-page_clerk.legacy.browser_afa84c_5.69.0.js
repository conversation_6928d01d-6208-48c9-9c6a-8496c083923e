"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["150"],{53879:function(e,t,l){l.r(t),l.d(t,{OrganizationAPIKeysPage:()=>h});var a=l(79109),i=l(83799),o=l(11576),n=l(39541),r=l(92654),c=l(15579),s=l(52010);let h=()=>{let{organization:e}=(0,i.o8)(),{contentRef:t}=(0,c.jh)();return e?(0,a.BX)(n.Col,{gap:4,children:[(0,a.tZ)(r.h.Root,{children:(0,a.tZ)(r.h.Title,{localizationKey:(0,n.localizationKeys)("organizationProfile.apiKeysPage.title"),textVariant:"h2"})}),(0,a.tZ)(o.ApiKeysContext.Provider,{value:{componentName:"APIKeys"},children:(0,a.tZ)(s.APIKeysPage,{subject:e.id,revokeModalRoot:t})})]}):null}}}]);