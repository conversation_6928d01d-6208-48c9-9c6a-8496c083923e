export declare const whiteAlpha: Readonly<{
    readonly whiteAlpha25: "hsla(0, 0%, 100%, 0.02)";
    readonly whiteAlpha50: "hsla(0, 0%, 100%, 0.03)";
    readonly whiteAlpha100: "hsla(0, 0%, 100%, 0.07)";
    readonly whiteAlpha150: "hsla(0, 0%, 100%, 0.11)";
    readonly whiteAlpha200: "hsla(0, 0%, 100%, 0.15)";
    readonly whiteAlpha300: "hsla(0, 0%, 100%, 0.28)";
    readonly whiteAlpha400: "hsla(0, 0%, 100%, 0.41)";
    readonly whiteAlpha500: "hsla(0, 0%, 100%, 0.53)";
    readonly whiteAlpha600: "hsla(0, 0%, 100%, 0.62)";
    readonly whiteAlpha700: "hsla(0, 0%, 100%, 0.73)";
    readonly whiteAlpha750: "hsla(0, 0%, 100%, 0.78)";
    readonly whiteAlpha800: "hsla(0, 0%, 100%, 0.81)";
    readonly whiteAlpha850: "hsla(0, 0%, 100%, 0.84)";
    readonly whiteAlpha900: "hsla(0, 0%, 100%, 0.87)";
    readonly whiteAlpha950: "hsla(0, 0%, 100%, 0.92)";
}>;
export declare const neutralAlpha: Readonly<{
    readonly neutralAlpha25: "hsla(0, 0%, 0%, 0.02)";
    readonly neutralAlpha50: "hsla(0, 0%, 0%, 0.03)";
    readonly neutralAlpha100: "hsla(0, 0%, 0%, 0.07)";
    readonly neutralAlpha150: "hsla(0, 0%, 0%, 0.11)";
    readonly neutralAlpha200: "hsla(0, 0%, 0%, 0.15)";
    readonly neutralAlpha300: "hsla(0, 0%, 0%, 0.28)";
    readonly neutralAlpha400: "hsla(0, 0%, 0%, 0.41)";
    readonly neutralAlpha500: "hsla(0, 0%, 0%, 0.53)";
    readonly neutralAlpha600: "hsla(0, 0%, 0%, 0.62)";
    readonly neutralAlpha700: "hsla(0, 0%, 0%, 0.73)";
    readonly neutralAlpha750: "hsla(0, 0%, 0%, 0.78)";
    readonly neutralAlpha800: "hsla(0, 0%, 0%, 0.81)";
    readonly neutralAlpha850: "hsla(0, 0%, 0%, 0.84)";
    readonly neutralAlpha900: "hsla(0, 0%, 0%, 0.87)";
    readonly neutralAlpha950: "hsla(0, 0%, 0%, 0.92)";
}>;
export declare const colors: Readonly<{
    readonly successAlpha300: import("@clerk/types").HslaColorString;
    readonly successAlpha600: import("@clerk/types").HslaColorString;
    readonly successAlpha25: import("@clerk/types").HslaColorString;
    readonly successAlpha50: import("@clerk/types").HslaColorString;
    readonly successAlpha100: import("@clerk/types").HslaColorString;
    readonly successAlpha150: import("@clerk/types").HslaColorString;
    readonly successAlpha200: import("@clerk/types").HslaColorString;
    readonly successAlpha400: import("@clerk/types").HslaColorString;
    readonly successAlpha500: import("@clerk/types").HslaColorString;
    readonly successAlpha700: import("@clerk/types").HslaColorString;
    readonly successAlpha750: import("@clerk/types").HslaColorString;
    readonly successAlpha800: import("@clerk/types").HslaColorString;
    readonly successAlpha850: import("@clerk/types").HslaColorString;
    readonly successAlpha900: import("@clerk/types").HslaColorString;
    readonly successAlpha950: import("@clerk/types").HslaColorString;
    readonly success50: "#F0FDF2";
    readonly success100: "#DCFCE2";
    readonly success200: "#BBF7C6";
    readonly success300: "#86EF9B";
    readonly success400: "#4ADE68";
    readonly success500: "#22C543";
    readonly success600: "#16A332";
    readonly success700: "#15802A";
    readonly success800: "#166527";
    readonly success900: "#145323";
    readonly success950: "#052E0F";
    readonly warningAlpha300: import("@clerk/types").HslaColorString;
    readonly warningAlpha600: import("@clerk/types").HslaColorString;
    readonly warningAlpha25: import("@clerk/types").HslaColorString;
    readonly warningAlpha50: import("@clerk/types").HslaColorString;
    readonly warningAlpha100: import("@clerk/types").HslaColorString;
    readonly warningAlpha150: import("@clerk/types").HslaColorString;
    readonly warningAlpha200: import("@clerk/types").HslaColorString;
    readonly warningAlpha400: import("@clerk/types").HslaColorString;
    readonly warningAlpha500: import("@clerk/types").HslaColorString;
    readonly warningAlpha700: import("@clerk/types").HslaColorString;
    readonly warningAlpha750: import("@clerk/types").HslaColorString;
    readonly warningAlpha800: import("@clerk/types").HslaColorString;
    readonly warningAlpha850: import("@clerk/types").HslaColorString;
    readonly warningAlpha900: import("@clerk/types").HslaColorString;
    readonly warningAlpha950: import("@clerk/types").HslaColorString;
    readonly warning50: "#FFF6ED";
    readonly warning100: "#FFEBD5";
    readonly warning200: "#FED1AA";
    readonly warning300: "#FDB674";
    readonly warning400: "#F98C49";
    readonly warning500: "#F36B16";
    readonly warning600: "#EA520C";
    readonly warning700: "#C23A0C";
    readonly warning800: "#9A2F12";
    readonly warning900: "#7C2912";
    readonly warning950: "#431207";
    readonly dangerAlpha300: import("@clerk/types").HslaColorString;
    readonly dangerAlpha600: import("@clerk/types").HslaColorString;
    readonly dangerAlpha25: import("@clerk/types").HslaColorString;
    readonly dangerAlpha50: import("@clerk/types").HslaColorString;
    readonly dangerAlpha100: import("@clerk/types").HslaColorString;
    readonly dangerAlpha150: import("@clerk/types").HslaColorString;
    readonly dangerAlpha200: import("@clerk/types").HslaColorString;
    readonly dangerAlpha400: import("@clerk/types").HslaColorString;
    readonly dangerAlpha500: import("@clerk/types").HslaColorString;
    readonly dangerAlpha700: import("@clerk/types").HslaColorString;
    readonly dangerAlpha750: import("@clerk/types").HslaColorString;
    readonly dangerAlpha800: import("@clerk/types").HslaColorString;
    readonly dangerAlpha850: import("@clerk/types").HslaColorString;
    readonly dangerAlpha900: import("@clerk/types").HslaColorString;
    readonly dangerAlpha950: import("@clerk/types").HslaColorString;
    readonly danger50: "#FEF2F2";
    readonly danger100: "#FEE5E5";
    readonly danger200: "#FECACA";
    readonly danger300: "#FCA5A5";
    readonly danger400: "#F87171";
    readonly danger500: "#EF4444";
    readonly danger600: "#DC2626";
    readonly danger700: "#B91C1C";
    readonly danger800: "#991B1B";
    readonly danger900: "#7F1D1D";
    readonly danger950: "#450A0A";
    readonly primaryAlpha300: import("@clerk/types").HslaColorString;
    readonly primaryAlpha600: import("@clerk/types").HslaColorString;
    readonly primaryAlpha25: import("@clerk/types").HslaColorString;
    readonly primaryAlpha50: import("@clerk/types").HslaColorString;
    readonly primaryAlpha100: import("@clerk/types").HslaColorString;
    readonly primaryAlpha150: import("@clerk/types").HslaColorString;
    readonly primaryAlpha200: import("@clerk/types").HslaColorString;
    readonly primaryAlpha400: import("@clerk/types").HslaColorString;
    readonly primaryAlpha500: import("@clerk/types").HslaColorString;
    readonly primaryAlpha700: import("@clerk/types").HslaColorString;
    readonly primaryAlpha750: import("@clerk/types").HslaColorString;
    readonly primaryAlpha800: import("@clerk/types").HslaColorString;
    readonly primaryAlpha850: import("@clerk/types").HslaColorString;
    readonly primaryAlpha900: import("@clerk/types").HslaColorString;
    readonly primaryAlpha950: import("@clerk/types").HslaColorString;
    readonly colorBackground: "white";
    readonly colorInputBackground: "white";
    readonly colorText: "#212126";
    readonly colorTextSecondary: "#747686";
    readonly colorInputText: "#131316";
    readonly colorTextOnPrimaryBackground: "white";
    readonly colorShimmer: "rgba(255, 255, 255, 0.36)";
    readonly transparent: "transparent";
    readonly white: "white";
    readonly black: "black";
    readonly primary50: "#B9BDBC";
    readonly primary100: "#9EA1A2";
    readonly primary200: "#828687";
    readonly primary300: "#66696D";
    readonly primary400: "#4B4D52";
    readonly primary500: "#2F3037";
    readonly primary600: "#2A2930";
    readonly primary700: "#25232A";
    readonly primary800: "#201D23";
    readonly primary900: "#1B171C";
    readonly primaryHover: "#3B3C45";
    readonly whiteAlpha25: "hsla(0, 0%, 100%, 0.02)";
    readonly whiteAlpha50: "hsla(0, 0%, 100%, 0.03)";
    readonly whiteAlpha100: "hsla(0, 0%, 100%, 0.07)";
    readonly whiteAlpha150: "hsla(0, 0%, 100%, 0.11)";
    readonly whiteAlpha200: "hsla(0, 0%, 100%, 0.15)";
    readonly whiteAlpha300: "hsla(0, 0%, 100%, 0.28)";
    readonly whiteAlpha400: "hsla(0, 0%, 100%, 0.41)";
    readonly whiteAlpha500: "hsla(0, 0%, 100%, 0.53)";
    readonly whiteAlpha600: "hsla(0, 0%, 100%, 0.62)";
    readonly whiteAlpha700: "hsla(0, 0%, 100%, 0.73)";
    readonly whiteAlpha750: "hsla(0, 0%, 100%, 0.78)";
    readonly whiteAlpha800: "hsla(0, 0%, 100%, 0.81)";
    readonly whiteAlpha850: "hsla(0, 0%, 100%, 0.84)";
    readonly whiteAlpha900: "hsla(0, 0%, 100%, 0.87)";
    readonly whiteAlpha950: "hsla(0, 0%, 100%, 0.92)";
    readonly neutralAlpha25: "hsla(0, 0%, 0%, 0.02)";
    readonly neutralAlpha50: "hsla(0, 0%, 0%, 0.03)";
    readonly neutralAlpha100: "hsla(0, 0%, 0%, 0.07)";
    readonly neutralAlpha150: "hsla(0, 0%, 0%, 0.11)";
    readonly neutralAlpha200: "hsla(0, 0%, 0%, 0.15)";
    readonly neutralAlpha300: "hsla(0, 0%, 0%, 0.28)";
    readonly neutralAlpha400: "hsla(0, 0%, 0%, 0.41)";
    readonly neutralAlpha500: "hsla(0, 0%, 0%, 0.53)";
    readonly neutralAlpha600: "hsla(0, 0%, 0%, 0.62)";
    readonly neutralAlpha700: "hsla(0, 0%, 0%, 0.73)";
    readonly neutralAlpha750: "hsla(0, 0%, 0%, 0.78)";
    readonly neutralAlpha800: "hsla(0, 0%, 0%, 0.81)";
    readonly neutralAlpha850: "hsla(0, 0%, 0%, 0.84)";
    readonly neutralAlpha900: "hsla(0, 0%, 0%, 0.87)";
    readonly neutralAlpha950: "hsla(0, 0%, 0%, 0.92)";
    readonly avatarBorder: "hsla(0, 0%, 0%, 0.15)";
    readonly avatarBackground: "hsla(0, 0%, 0%, 0.41)";
    readonly modalBackdrop: "hsla(0, 0%, 0%, 0.73)";
}>;
