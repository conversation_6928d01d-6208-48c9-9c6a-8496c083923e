{"version": 3, "sources": ["../src/zh-TW.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const zhTW: LocalizationResource = {\n  locale: 'zh-TW',\n  backButton: '返回',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: '默認',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: '其他模擬器設備',\n  badge__primary: '主要',\n  badge__renewsAt: undefined,\n  badge__requiresAction: '需要操作',\n  badge__startsAt: undefined,\n  badge__thisDevice: '此設備',\n  badge__unverified: '未驗證',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: '用戶設備',\n  badge__you: '您',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: '創建組織',\n    invitePage: {\n      formButtonReset: '跳過',\n    },\n    title: '創建組織',\n  },\n  dates: {\n    lastDay: \"昨天{{ date | timeString('zh-TW') }}\",\n    next6Days: \"{{ date | weekday('zh-TW','long') }} {{ date | timeString('zh-TW') }}\",\n    nextDay: \"明天{{ date | timeString('zh-TW') }}\",\n    numeric: \"{{ date | numeric('zh-TW') }}\",\n    previous6Days: \"上週{{ date | weekday('zh-TW','long') }} {{ date | timeString('zh-TW') }}\",\n    sameDay: \"今天{{ date | timeString('zh-TW') }}\",\n  },\n  dividerText: '或者',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: '使用另一種方法',\n  footerPageLink__help: '幫助',\n  footerPageLink__privacy: '隱私',\n  footerPageLink__terms: '條款',\n  formButtonPrimary: '繼續',\n  formButtonPrimary__verify: '核實',\n  formFieldAction__forgotPassword: '忘記密碼？',\n  formFieldError__matchingPasswords: '密碼匹配。',\n  formFieldError__notMatchingPasswords: '密碼不匹配。',\n  formFieldError__verificationLinkExpired: '驗證連結已過期。請請求新的連結。',\n  formFieldHintText__optional: '選填',\n  formFieldHintText__slug: 'slug 是人類可讀的 ID，必須是唯一的。它經常在 URL 中使用。',\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: '刪除帳戶',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses: '輸入或黏貼一個或多個電子郵件地址，用空格或逗號分隔',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: undefined,\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__automaticInvitations: '為該網域啟用自動邀請',\n  formFieldLabel__backupCode: '備用代碼',\n  formFieldLabel__confirmDeletion: '確定',\n  formFieldLabel__confirmPassword: '確認密碼',\n  formFieldLabel__currentPassword: '當前密碼',\n  formFieldLabel__emailAddress: '電子郵件地址',\n  formFieldLabel__emailAddress_username: '電子郵件地址或使用者名稱',\n  formFieldLabel__emailAddresses: '電子郵件地址',\n  formFieldLabel__firstName: '名字',\n  formFieldLabel__lastName: '姓氏',\n  formFieldLabel__newPassword: '新密碼',\n  formFieldLabel__organizationDomain: '領域',\n  formFieldLabel__organizationDomainDeletePending: '刪除待處理的邀請和建議',\n  formFieldLabel__organizationDomainEmailAddress: '驗證電子郵件地址',\n  formFieldLabel__organizationDomainEmailAddressDescription: '輸入此網域下的電子郵件地址以接收代碼並驗證此網域名稱。',\n  formFieldLabel__organizationName: '組織名稱',\n  formFieldLabel__organizationSlug: 'URL 簡稱',\n  formFieldLabel__passkeyName: undefined,\n  formFieldLabel__password: '密碼',\n  formFieldLabel__phoneNumber: '電話號碼',\n  formFieldLabel__role: '角色',\n  formFieldLabel__signOutOfOtherSessions: '登出所有其他設備',\n  formFieldLabel__username: '使用者名稱',\n  impersonationFab: {\n    action__signOut: '退出登錄',\n    title: '以 {{identifier}} 登錄',\n  },\n  maintenanceMode: undefined,\n  membershipRole__admin: '管理員',\n  membershipRole__basicMember: '成員',\n  membershipRole__guestMember: '訪客',\n  organizationList: {\n    action__createOrganization: '創建組織',\n    action__invitationAccept: '加入',\n    action__suggestionsAccept: '申請加入',\n    createOrganization: '創建組織',\n    invitationAcceptedLabel: '已加入',\n    subtitle: '繼續 {{applicationName}}',\n    suggestionsAcceptedLabel: '待批准',\n    title: '選擇一個帳戶',\n    titleWithoutPersonal: '選擇一個組織',\n  },\n  organizationProfile: {\n    badge__automaticInvitation: '自動邀請',\n    badge__automaticSuggestion: '自動建議',\n    badge__manualInvitation: '不自動註冊',\n    badge__unverified: '未經驗證',\n    billingPage: {\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle: '新增要驗證的網域。擁有此網域電子郵件地址的使用者可以自動加入該組織或要求加入。',\n      title: '新增網域',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed: '邀請無法發送。修覆以下問題然後重試：',\n      formButtonPrimary__continue: '發送邀請',\n      selectDropdown__role: '選擇角色',\n      subtitle: '邀請新成員加入此組織',\n      successMessage: '邀請成功發送',\n      title: '邀請成員',\n    },\n    membersPage: {\n      action__invite: '邀請',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: '移除成員',\n        tableHeader__actions: undefined,\n        tableHeader__joined: '加入',\n        tableHeader__role: '角色',\n        tableHeader__user: '用戶',\n      },\n      detailsTitle__emptyRow: '沒有可顯示的成員',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            '透過將電子郵件網域與您的組織連接來邀請使用者。任何使用匹配電子郵件網域註冊的人都可以隨時加入該組織。',\n          headerTitle: '自動邀請',\n          primaryButton: '管理已驗證的域名',\n        },\n        table__emptyRow: '沒有可顯示的邀請',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: '撤銷邀請',\n        tableHeader__invited: '已邀請',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle: '使用符合的電子郵件網域註冊的用戶將能夠看到請求加入您的組織的建議。',\n          headerTitle: '自動建議',\n          primaryButton: '管理已驗證的域名',\n        },\n        menuAction__approve: '批准',\n        menuAction__reject: '拒絕',\n        tableHeader__requested: '請求存取權限',\n        table__emptyRow: '沒有顯示請求',\n      },\n      start: {\n        headerTitle__invitations: '邀請函',\n        headerTitle__members: '成員',\n        headerTitle__requests: '請求',\n      },\n    },\n    navbar: {\n      billing: undefined,\n      description: '管理您的組織。',\n      general: '一般的',\n      members: '成員',\n      title: '組織',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: '類型 \"{{organizationName}}\" 下面繼續。',\n          messageLine1: '您確定要刪除該組織嗎？',\n          messageLine2: '此操作是永久性的且不可逆轉的。',\n          successMessage: '您已刪除該組織。',\n          title: '刪除組織',\n        },\n        leaveOrganization: {\n          actionDescription: '類型 \"{{organizationName}}\" 下面繼續。',\n          messageLine1: '您確定要離開此組織嗎？您將失去對此組織及其應用程式的訪問權限。',\n          messageLine2: '此操作是永久性的且無法撤銷。',\n          successMessage: '您已離開了組織。',\n          title: '離開組織',\n        },\n        title: '危險',\n      },\n      domainSection: {\n        menuAction__manage: '管理',\n        menuAction__remove: '刪除',\n        menuAction__verify: '核實',\n        primaryButton: '新增網域',\n        subtitle: '允許使用者自動加入組織或根據已驗證的電子郵件網域請求加入。',\n        title: '已驗證域名',\n      },\n      successMessage: '組織已更新。',\n      title: '組織簡介',\n    },\n    removeDomainPage: {\n      messageLine1: '電子郵件域名 {{domain}} 將被刪除。',\n      messageLine2: '此後用戶將無法自動加入該組織。',\n      successMessage: '{{domain}} 已刪除。',\n      title: '刪除網域',\n    },\n    start: {\n      headerTitle__general: '一般的',\n      headerTitle__members: '成員',\n      profileSection: {\n        primaryButton: undefined,\n        title: '組織簡介',\n        uploadAction__title: '標識',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: '刪除此網域將影響受邀使用者。',\n        removeDomainActionLabel__remove: '刪除網域',\n        removeDomainSubtitle: '從您的已驗證域名中移除此域名',\n        removeDomainTitle: '刪除網域',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description: '用戶註冊時會自動被邀請加入該組織，並且可以隨時加入。',\n        automaticInvitationOption__label: '自動邀請',\n        automaticSuggestionOption__description: '使用者會收到加入請求的建議，但必須得到管理員的批准才能加入組織。',\n        automaticSuggestionOption__label: '自動建議',\n        calloutInfoLabel: '更改註冊模式只會影響新用戶。',\n        calloutInvitationCountLabel: '已向用戶發送待處理的邀請: {{count}}',\n        calloutSuggestionCountLabel: '已將待處理的建議發送給用戶: {{count}}',\n        manualInvitationOption__description: '只能手動邀請使用者加入組織。',\n        manualInvitationOption__label: '不自動註冊',\n        subtitle: '選擇此網域中的使用者加入組織的方式。',\n      },\n      start: {\n        headerTitle__danger: '危險',\n        headerTitle__enrollment: '註冊選項',\n      },\n      subtitle: '網域 {{domain}} 現已驗證。選擇註冊模式繼續。',\n      title: '更新 {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: '輸入發送到您信箱的驗證碼',\n      formTitle: '驗證碼',\n      resendButton: '沒有收到代碼？重新發送',\n      subtitle: '網域 {{domainName}} 需要透過電子郵件進行驗證。',\n      subtitleVerificationCodeScreen: '驗證碼已發送至 {{emailAddress}}。輸入代碼以繼續。',\n      title: '驗證域名',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: '創建組織',\n    action__invitationAccept: '加入',\n    action__manageOrganization: '管理組織',\n    action__suggestionsAccept: '申請加入',\n    notSelected: '未選擇組織',\n    personalWorkspace: '個人工作區',\n    suggestionsAcceptedLabel: '待批准',\n  },\n  paginationButton__next: '下一頁',\n  paginationButton__previous: '上一頁',\n  paginationRowText__displaying: '顯示',\n  paginationRowText__of: '的',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: '新增帳戶',\n      action__signOutAll: '登出所有帳戶',\n      subtitle: '選擇您想要繼續使用的帳戶。',\n      title: '選擇一個帳戶',\n    },\n    alternativeMethods: {\n      actionLink: '獲取幫助',\n      actionText: '這些都沒有嗎？',\n      blockButton__backupCode: '使用備用代碼',\n      blockButton__emailCode: '電子郵件驗證碼到 {{identifier}}',\n      blockButton__emailLink: '電子郵件連結到 {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: '使用您的密碼登錄',\n      blockButton__phoneCode: '發送簡訊代碼到 {{identifier}}',\n      blockButton__totp: '使用您的驗證應用程式',\n      getHelp: {\n        blockButton__emailSupport: '郵件支持',\n        content: '如果您在登入帳戶時遇到困難，請給我們發送電子郵件，我們將盡快讓您恢覆訪問。',\n        title: '獲取幫助',\n      },\n      subtitle: '遇到問題了嗎？您可以使用其中任何一種方式登入。',\n      title: '使用其他方法',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: '繼續使用 {{applicationName}}',\n      title: '輸入備用代碼',\n    },\n    emailCode: {\n      formTitle: '驗證碼',\n      resendButton: '重新發送驗證碼',\n      subtitle: '繼續使用 {{applicationName}}',\n      title: '查看您的電子郵件',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: '返回原始標籤頁繼續。',\n        title: '此驗證連結已過期',\n      },\n      failed: {\n        subtitle: '返回原始標籤頁繼續。',\n        title: '此驗證連結無效',\n      },\n      formSubtitle: '使用發送到您的電子郵件的驗證連結',\n      formTitle: '驗證連結',\n      loading: {\n        subtitle: '即將為您重定向',\n        title: '正在登錄...',\n      },\n      resendButton: '重新發送連結',\n      subtitle: '繼續使用 {{applicationName}}',\n      title: '查看您的電子郵件',\n      unusedTab: {\n        title: '您可以關閉此標籤頁',\n      },\n      verified: {\n        subtitle: '即將為您重定向',\n        title: '成功登錄',\n      },\n      verifiedSwitchTab: {\n        subtitle: '返回原始標籤頁繼續',\n        subtitleNewTab: '返回新打開的標籤頁繼續',\n        titleNewTab: '在其他標籤頁上登入',\n      },\n    },\n    forgotPassword: {\n      formTitle: '重設密碼代碼',\n      resendButton: '重新發送代碼',\n      subtitle: '重設您的密碼',\n      subtitle_email: '首先，輸入傳送到您的電子郵件 ID 的代碼',\n      subtitle_phone: '首先，輸入發送到您手機的代碼',\n      title: '重設密碼',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: '重設密碼',\n      label__alternativeMethods: '或者，使用其他方式登錄。',\n      title: '忘記密碼？',\n    },\n    noAvailableMethods: {\n      message: '無法繼續登錄。沒有可用的身份驗證因素。',\n      subtitle: '出現錯誤',\n      title: '無法登錄',\n    },\n    passkey: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: '使用其他方法',\n      subtitle: '繼續使用 {{applicationName}}',\n      title: '輸入您的密碼',\n    },\n    passwordPwned: {\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: '驗證碼',\n      resendButton: '重新發送驗證碼',\n      subtitle: '繼續使用 {{applicationName}}',\n      title: '檢查手機簡訊',\n    },\n    phoneCodeMfa: {\n      formTitle: '驗證碼',\n      resendButton: '重新發送驗證碼',\n      subtitle: undefined,\n      title: '檢查手機簡訊',\n    },\n    resetPassword: {\n      formButtonPrimary: '重設密碼',\n      requiredMessage: '出於安全原因，需要重設您的密碼。',\n      successMessage: '您的密碼已成功更改。正在為您登錄，請稍等。',\n      title: '重設密碼',\n    },\n    resetPasswordMfa: {\n      detailsLabel: '我們需要驗證您的身份才能重設您的密碼。',\n    },\n    start: {\n      actionLink: '註冊',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: '使用電子郵件',\n      actionLink__use_email_username: '使用電子郵件或使用者名稱',\n      actionLink__use_passkey: undefined,\n      actionLink__use_phone: '使用電話',\n      actionLink__use_username: '使用使用者名稱',\n      actionText: '還沒有帳戶？',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: '繼續使用 {{applicationName}}',\n      subtitleCombined: undefined,\n      title: '登錄',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: '驗證碼',\n      subtitle: undefined,\n      title: '兩步驗證',\n    },\n  },\n  signInEnterPasswordTitle: '輸入您的密碼',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: '登錄',\n      actionText: '已經有帳戶了？',\n      subtitle: '繼續使用 {{applicationName}}',\n      title: '填寫缺少的欄位',\n    },\n    emailCode: {\n      formSubtitle: '輸入發送到您的電子郵件地址的驗證碼',\n      formTitle: '驗證碼',\n      resendButton: '重新發送驗證碼',\n      subtitle: '繼續使用 {{applicationName}}',\n      title: '驗證您的電子郵件',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: '使用發送到您的電子郵件地址的驗證連結',\n      formTitle: '驗證連結',\n      loading: {\n        title: '正在註冊...',\n      },\n      resendButton: '重新發送連結',\n      subtitle: '繼續使用 {{applicationName}}',\n      title: '驗證您的電子郵件',\n      verified: {\n        title: '成功註冊',\n      },\n      verifiedSwitchTab: {\n        subtitle: '返回新打開的標籤頁繼續',\n        subtitleNewTab: '返回上一個標籤頁繼續',\n        title: '成功驗證電子郵件',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: '我同意 {{ privacyPolicyLink || link(\"隱私條款\") }}',\n        label__onlyTermsOfService: '我同意 {{ termsOfServiceLink || link(\"使用條款\") }}',\n        label__termsOfServiceAndPrivacyPolicy:\n          '我同意 {{ termsOfServiceLink || link(\"使用條款\") }} 和 {{ privacyPolicyLink || link(\"隱私條款\") }}',\n      },\n      continue: {\n        subtitle: '請閱讀並接受條款以繼續',\n        title: '同意條款',\n      },\n    },\n    phoneCode: {\n      formSubtitle: '輸入發送到您的電話號碼的驗證碼',\n      formTitle: '驗證碼',\n      resendButton: '重新發送驗證碼',\n      subtitle: '繼續使用 {{applicationName}}',\n      title: '驗證您的電話',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: '登錄',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: '已經有帳戶了？',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: '繼續使用 {{applicationName}}',\n      subtitleCombined: '繼續使用 {{applicationName}}',\n      title: '創建您的帳戶',\n      titleCombined: '創建您的帳戶',\n    },\n  },\n  socialButtonsBlockButton: '使用 {{provider|titleize}} 登錄',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid: '由於安全驗證失敗，註冊未成功。請刷新頁面重試或聯絡支持獲取更多幫助。',\n    captcha_unavailable: '由於機器人驗證失敗，註冊失敗。請重新整理頁面重試或聯絡支援人員以取得更多協助。',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: undefined,\n    form_identifier_exists__phone_number: undefined,\n    form_identifier_exists__username: undefined,\n    form_identifier_not_found: '我們無法找到具有這些信息的帳戶。',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: '電子郵件地址必須是有效的電子郵件地址。',\n    form_param_format_invalid__phone_number: '電話號碼必須採用有效的國際格式。',\n    form_param_max_length_exceeded__first_name: '名字不超過 256 個字元。',\n    form_param_max_length_exceeded__last_name: '姓氏不得超過 256 個字元。',\n    form_param_max_length_exceeded__name: '名稱不應超過 256 個字元。',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: '您的密碼強度不夠。',\n    form_password_pwned: '這個密碼在數據洩露中被發現，不能使用，請換一個密碼試試。',\n    form_password_pwned__sign_in: undefined,\n    form_password_size_in_bytes_exceeded: '您的密碼超過了允許的最大位元組數，請縮短它或去掉一些特殊字元。',\n    form_password_validation_failed: '密碼錯誤',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: '您無法刪除您的最後一個身分證明。',\n    not_allowed_access:\n      \"您使用的電子郵件地址或電話號碼不允許註冊。這可能因為您在電子郵件地址中使用了 '+', '=', '#' 或 '.'，使用了與臨時電子郵件服務關聯的域名，或者有明確的排除。如果您認為這是錯誤，請聯絡支持。\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: undefined,\n    passkey_not_supported: undefined,\n    passkey_pa_not_supported: undefined,\n    passkey_registration_cancelled: undefined,\n    passkey_retrieval_cancelled: undefined,\n    passwordComplexity: {\n      maximumLength: '少於{{length}}個字元',\n      minimumLength: '{{length}}個或更多字元',\n      requireLowercase: '一個小寫字母',\n      requireNumbers: '一個數字',\n      requireSpecialCharacter: '一個特殊字元',\n      requireUppercase: '一個大寫字母',\n      sentencePrefix: '您的密碼必須包含',\n    },\n    phone_number_exists: '這個電話號碼已被使用。請嘗試另一個。',\n    session_exists: '您已經登錄。',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: '您的密碼可以用，但可以更強。試著添加更多字元。',\n      goodPassword: '做得好。這是一個優秀的密碼。',\n      notEnough: '您的密碼強度不夠。',\n      suggestions: {\n        allUppercase: '大寫一些，但不是所有的字母。',\n        anotherWord: '添加更不常見的更多單字。',\n        associatedYears: '避免與你有關的年份。',\n        capitalization: '大寫不僅僅是第一個字母。',\n        dates: '避免與你有關的日期和年份。',\n        l33t: '避免預測的字母替換，如\"@\"代替\"a\"。',\n        longerKeyboardPattern: '使用更長的鍵盤模式，並多次改變打字方向。',\n        noNeed: '你可以創建強密碼，而無需使用符號，數字或大寫字母。',\n        pwned: '如果您在其他地方使用此密碼，您應該更改它。',\n        recentYears: '避免近年來。',\n        repeated: '避免重複的單字和字元。',\n        reverseWords: '避免常用詞的反向拼寫。',\n        sequences: '避免常見字元序列。',\n        useWords: '使用多個單字，但避免常見短語。',\n      },\n      warnings: {\n        common: '這是一個常用的密碼。',\n        commonNames: '常見的名字和姓氏易被猜到。',\n        dates: '日期易被猜到。',\n        extendedRepeat: '像\"abcabcabc\"這樣的重複字元模式易被猜到。',\n        keyPattern: '短鍵盤模式易被猜到。',\n        namesByThemselves: '單個名字或姓氏易被猜到。',\n        pwned: '您的密碼在網路上的數據洩露中被暴露。',\n        recentYears: '近年來易被猜到。',\n        sequences: '像\"abc\"這樣的常見字元序列易被猜到。',\n        similarToCommon: '這個密碼和常用密碼相似。',\n        simpleRepeat: '像\"aaa\"這樣的重複字元易被猜到。',\n        straightRow: '鍵盤上的直行鍵易被猜到。',\n        topHundred: '這是一個頻繁使用的密碼。',\n        topTen: '這是一個大量使用的密碼。',\n        userInputs: '不應該有任何個人或頁面相關的數據。',\n        wordByItself: '單個單字易被猜到。',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: '添加帳戶',\n    action__manageAccount: '管理帳戶',\n    action__signOut: '退出登錄',\n    action__signOutAll: '退出所有帳戶',\n  },\n  userProfile: {\n    backupCodePage: {\n      actionLabel__copied: '已複製！',\n      actionLabel__copy: '複製全部',\n      actionLabel__download: '下載 .txt',\n      actionLabel__print: '列印',\n      infoText1: '將為此帳戶啟用備份代碼。',\n      infoText2: '保密並安全儲存備份代碼。如果您懷疑它們已經洩露，您可以重新生成備份代碼。',\n      subtitle__codelist: '安全儲存並保守秘密。',\n      successMessage:\n        '現在已啟用備份代碼。如果您失去了驗證設備的訪問權限，您可以使用其中之一登入您的帳戶。每個代碼只能使用一次。',\n      successSubtitle: '如果您失去了驗證設備的訪問權限，您可以使用其中之一登入您的帳戶。',\n      title: '添加備份代碼驗證',\n      title__codelist: '備份代碼',\n    },\n    billingPage: {\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: '選擇一個供應商來連接您的帳戶。',\n      formHint__noAccounts: '沒有可用的外部帳戶供應商。',\n      removeResource: {\n        messageLine1: '{{identifier}} 將從此帳戶中被移除。',\n        messageLine2: '您將無法再使用這個已連接的帳戶，任何依賴的功能將不再工作。',\n        successMessage: '{{connectedAccount}} 已從您的帳戶中移除。',\n        title: '移除已連接的帳戶',\n      },\n      socialButtonsBlockButton: '連接 {{provider|titleize}} 帳戶',\n      successMessage: '供應商已被添加到您的帳戶',\n      title: '添加已連接的帳戶',\n    },\n    deletePage: {\n      actionDescription: '請在下方輸入“Delete account”以繼續。',\n      confirm: '移除帳戶',\n      messageLine1: '您確定要刪除您的帳戶嗎？',\n      messageLine2: '這個動作是永久性且無法還原的。',\n      title: '移除帳戶',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: '一封含有驗證碼的郵件將會被發送到這個電子郵件地址。',\n        formSubtitle: '輸入發送到 {{identifier}} 的驗證碼',\n        formTitle: '驗證碼',\n        resendButton: '重發驗證碼',\n        successMessage: '電子郵件 {{identifier}} 已被添加到您的帳戶。',\n      },\n      emailLink: {\n        formHint: '一封含有驗證連結的郵件將會被發送到這個電子郵件地址。',\n        formSubtitle: '點擊發送到 {{identifier}} 的郵件中的驗證連結',\n        formTitle: '驗證連結',\n        resendButton: '重發連結',\n        successMessage: '電子郵件 {{identifier}} 已被添加到您的帳戶。',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} 將從此帳戶中被移除。',\n        messageLine2: '您將無法使用這個電子郵件地址登錄。',\n        successMessage: '電子郵件 {{emailAddress}} 已從您的帳戶中移除。',\n        title: '移除電子郵件地址',\n      },\n      title: '添加電子郵件地址',\n      verifyTitle: 'Verify email address',\n    },\n    formButtonPrimary__add: 'Add',\n    formButtonPrimary__continue: '繼續',\n    formButtonPrimary__finish: '完成',\n    formButtonPrimary__remove: 'Remove',\n    formButtonPrimary__save: 'Save',\n    formButtonReset: '取消',\n    mfaPage: {\n      formHint: '選擇一個添加的方法。',\n      title: '添加兩步驗證',\n    },\n    mfaPhoneCodePage: {\n      backButton: '使用現有號碼',\n      primaryButton__addPhoneNumber: '添加電話號碼',\n      removeResource: {\n        messageLine1: '{{identifier}} 將不再在登錄時接收驗證代碼。',\n        messageLine2: '您的帳戶可能不再安全。您確定要繼續嗎？',\n        successMessage: '已移除{{mfaPhoneCode}}的簡訊驗證碼兩步驗證',\n        title: '移除兩步驗證',\n      },\n      subtitle__availablePhoneNumbers: '選擇一個電話號碼來註冊簡訊驗證碼兩步驗證。',\n      subtitle__unavailablePhoneNumbers: '沒有可用的電話號碼來註冊簡訊驗證碼兩步驗證。',\n      successMessage1: '登入時，您需要輸入發送到此電話號碼的驗證碼作為附加步驟。',\n      successMessage2: '保存這些備份代碼並將其儲存在安全的地方。如果您無法存取身份驗證設備，則可以使用備用代碼登入。',\n      successTitle: '已啟用簡訊驗證碼',\n      title: '添加簡訊驗證碼驗證',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: '掃描二維碼',\n        buttonUnableToScan__nonPrimary: '不能掃描二維碼？',\n        infoText__ableToScan: '在您的驗證器應用中設置一個新的登錄方法，並掃描下面的二維碼將其連結到您的帳戶。',\n        infoText__unableToScan: '在驗證器中設置一個新的登錄方法，並輸入下面提供的 Key。',\n        inputLabel__unableToScan1: '確保啟用了基於時間或一次性密碼，然後完成連結您的帳戶。',\n        inputLabel__unableToScan2: '或者，如果您的驗證器支持 TOTP URIs，您也可以複製完整的 URI。',\n      },\n      removeResource: {\n        messageLine1: '登錄時，將不再需要來自此驗證器的驗證碼。',\n        messageLine2: '您的帳戶可能不再安全。您確定要繼續嗎？',\n        successMessage: '已移除通過驗證器應用程式的兩步驗證。',\n        title: '移除兩步驗證',\n      },\n      successMessage: '現在已啟用兩步驗證。在登錄時，您需要輸入來自此驗證器的驗證碼作為額外步驟。',\n      title: '添加驗證器應用程式',\n      verifySubtitle: '輸入您的驗證器生成的驗證碼',\n      verifyTitle: '驗證代碼',\n    },\n    mobileButton__menu: '菜單',\n    navbar: {\n      account: '輪廓',\n      billing: undefined,\n      description: '管理您的帳戶資訊。',\n      security: '安全',\n      title: '帳戶',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: undefined,\n        title: undefined,\n      },\n      subtitle__rename: undefined,\n      title__rename: undefined,\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions: '建議退出所有可能使用過舊密碼的其他裝置。',\n      readonly: '您的密碼目前無法編輯，因為您只能透過企業連線登入。',\n      successMessage__set: '您的密碼已設置。',\n      successMessage__signOutOfOtherSessions: '所有其他設備已退出。',\n      successMessage__update: '您的密碼已更新。',\n      title__set: '設置密碼',\n      title__update: '更改密碼',\n    },\n    phoneNumberPage: {\n      infoText: '一條包含驗證連結的簡訊將會發送到這個電話號碼。',\n      removeResource: {\n        messageLine1: '{{identifier}} 將從此帳戶中被移除。',\n        messageLine2: '您將無法使用這個電話號碼登錄。',\n        successMessage: '電話號碼 {{phoneNumber}} 已從您的帳戶中移除。',\n        title: '移除電話號碼',\n      },\n      successMessage: '{{identifier}} 已被添加到您的帳戶。',\n      title: '添加電話號碼',\n      verifySubtitle: '輸入發送至的驗證碼 {{identifier}}',\n      verifyTitle: '驗證電話號碼',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: '上傳小於10MB的JPG, PNG, GIF, 或WEBP格式的圖片',\n      imageFormDestructiveActionSubtitle: '移除圖片',\n      imageFormSubtitle: '上傳圖片',\n      imageFormTitle: '個人資料圖片',\n      readonly: '您的個人資料資訊已由企業連線提供，無法編輯。',\n      successMessage: '您的個人資料已更新。',\n      title: '更新個人資料',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: '退出設備',\n        title: '活動設備',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: '再試一次',\n        actionLabel__reauthorize: '立即授權',\n        destructiveActionTitle: '移除',\n        primaryButton: '連接帳戶',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize: '所需範圍已更新，您可能會遇到功能受限的情況。請重新授權此應用程式以避免任何問題。',\n        title: '已連接的帳戶',\n      },\n      dangerSection: {\n        deleteAccountButton: '移除帳戶',\n        title: '危險',\n      },\n      emailAddressesSection: {\n        destructiveAction: '移除電子郵件地址',\n        detailsAction__nonPrimary: '設為主要',\n        detailsAction__primary: '完成驗證',\n        detailsAction__unverified: '完成驗證',\n        primaryButton: '添加電子郵件地址',\n        title: '電子郵件地址',\n      },\n      enterpriseAccountsSection: {\n        title: '企業帳戶',\n      },\n      headerTitle__account: '帳戶',\n      headerTitle__security: '安全',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: '重新生成代碼',\n          headerTitle: '備份代碼',\n          subtitle__regenerate: '獲取一套新的安全備份代碼。之前的備份代碼將被刪除，無法使用。',\n          title__regenerate: '重新生成備份代碼',\n        },\n        phoneCode: {\n          actionLabel__setDefault: '設為默認',\n          destructiveActionLabel: '移除電話號碼',\n        },\n        primaryButton: '添加兩步驗證',\n        title: '兩步驗證',\n        totp: {\n          destructiveActionTitle: '移除',\n          headerTitle: '驗證器應用程式',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: undefined,\n        menuAction__rename: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      passwordSection: {\n        primaryButton__setPassword: '設置密碼',\n        primaryButton__updatePassword: '更改密碼',\n        title: '密碼',\n      },\n      phoneNumbersSection: {\n        destructiveAction: '移除電話號碼',\n        detailsAction__nonPrimary: '設為主要',\n        detailsAction__primary: '完成驗證',\n        detailsAction__unverified: '完成驗證',\n        primaryButton: '添加電話號碼',\n        title: '電話號碼',\n      },\n      profileSection: {\n        primaryButton: '更新個人資料',\n        title: '個人資料',\n      },\n      usernameSection: {\n        primaryButton__setUsername: '設置使用者名稱',\n        primaryButton__updateUsername: '更改使用者名稱',\n        title: '使用者名稱',\n      },\n      web3WalletsSection: {\n        destructiveAction: '移除錢包',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 錢包',\n        title: 'Web3 錢包',\n      },\n    },\n    usernamePage: {\n      successMessage: '您的使用者名稱已更新。',\n      title__set: '更新使用者名稱',\n      title__update: '更新使用者名稱',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} 將從此帳戶中被移除。',\n        messageLine2: '您將無法使用這個 web3 錢包登錄。',\n        successMessage: '{{web3Wallet}} 已從您的帳戶中移除。',\n        title: '移除 web3 錢包',\n      },\n      subtitle__availableWallets: '選擇一個 web3 錢包連接到您的帳戶。',\n      subtitle__unavailableWallets: '沒有可用的 web3 錢包。',\n      successMessage: '錢包已被添加到您的帳戶。',\n      title: '添加web3錢包',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCAAwC;AAAA,QACxC,kCAAkC;AAAA,QAClC,wCAAwC;AAAA,QACxC,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,sCAAsC;AAAA,IACtC,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WAAW;AAAA,MACX,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCAAiC;AAAA,MACjC,mCAAmC;AAAA,MACnC,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBAAsB;AAAA,QACtB,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,2BAA2B;AAAA,MAC7B;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CAA0C;AAAA,MAC1C,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBAAsB;AAAA,UACtB,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}