import type { SignInFactor } from '@clerk/types';
import React from 'react';
import type { LocalizationKey } from '../../customizables';
export type AlternativeMethodsProps = {
    onBackLinkClick: React.MouseEventHandler | undefined;
    onFactorSelected: (factor: SignInFactor) => void;
};
export declare const SignInFactorTwoAlternativeMethods: (props: AlternativeMethodsProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export declare function getButtonLabel(factor: SignInFactor): LocalizationKey;
