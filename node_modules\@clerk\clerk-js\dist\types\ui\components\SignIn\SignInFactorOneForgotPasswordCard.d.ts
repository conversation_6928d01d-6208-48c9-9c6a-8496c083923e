import type { ResetPasswordCodeFactor } from '@clerk/types';
import type { SignInFactorOneCodeCard, SignInFactorOneCodeFormProps } from './SignInFactorOneCodeForm';
type SignInForgotPasswordCardProps = SignInFactorOneCodeCard & Pick<SignInFactorOneCodeFormProps, 'cardSubtitle'> & {
    factor: ResetPasswordCodeFactor;
};
export declare const SignInFactorOneForgotPasswordCard: (props: SignInForgotPasswordCardProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
