import type { MetamaskWeb3Provider, OKXWalletWeb3Provider } from '@clerk/types';
type InjectedWeb3Provider = MetamaskWeb3Provider | OKXWalletWeb3Provider;
declare class InjectedWeb3Providers {
    #private;
    private constructor();
    static getInstance(): InjectedWeb3Providers;
    get: (provider: InjectedWeb3Provider) => any;
}
export declare const getInjectedWeb3Providers: () => InjectedWeb3Providers;
export {};
