import type { SignInFactor } from '@clerk/types';
export declare const passwordPrefFactorComparator: (a: SignInFactor, b: SignInFactor) => number;
export declare const otpPrefFactorComparator: (a: SignInFactor, b: SignInFactor) => number;
export declare const backupCodePrefFactorComparator: (a: SignInFactor, b: SignInFactor) => number;
export declare const allStrategiesButtonsComparator: (a: SignInFactor, b: SignInFactor) => number;
