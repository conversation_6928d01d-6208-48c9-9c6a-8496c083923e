"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["554"],{9743:function(e,i,t){t.r(i),t.d(i,{OrganizationProfileModal:()=>eU,OrganizationProfile:()=>eW});var a=t(9109),o=t(3799),n=t(9144),r=t(2672),l=t(5579),s=t(3394),d=t(1673),c=t(1576),g=t(9541),m=t(4676),u=t(8969),p=t(1085);let h=e=>{let{organization:i}=(0,o.o8)(),{pages:t}=(0,c.useOrganizationProfileContext)(),n=(0,u.N2)(e=>e({permission:"org:sys_memberships:read"})||e({permission:"org:sys_memberships:manage"})),r=(0,u.N2)(e=>e({permission:"org:sys_billing:read"})||e({permission:"org:sys_billing:manage"})),s=t.routes.filter(e=>e.id!==d.bm.MEMBERS||e.id===d.bm.MEMBERS&&n).filter(e=>e.id!==d.bm.BILLING||e.id===d.bm.BILLING&&r);return i?(0,a.BX)(l.Uh,{contentRef:e.contentRef,children:[(0,a.tZ)(l.l2,{title:(0,p.u1)("organizationProfile.navbar.title"),description:(0,p.u1)("organizationProfile.navbar.description"),routes:s,contentRef:e.contentRef}),e.children]}):null};var f=t(4995),z=t(2654),b=t(5973),v=t(9655),Z=t(9805),P=t(4709),y=t(431),S=t(9460),C=t(8487),x=t(7263),_=t(7623),K=t(7484);let B=e=>{let i=(0,r.useCardState)(),{navigateAfterLeaveOrganization:t}=(0,c.useOrganizationProfileContext)(),{userMemberships:a,userInvitations:n}=(0,o.eW)({userMemberships:K.AO.userMemberships,userInvitations:K.AO.userInvitations});return()=>i.runAsync(async()=>{await e?.()}).then(()=>{a.revalidate?.(),n.revalidate?.(),t()})},D=e=>{let{organization:i}=(0,o.o8)(),{user:t}=(0,o.aF)(),n=B(()=>t.leaveOrganization(i.id));return i&&t?(0,a.tZ)(T,{organizationName:i?.name,title:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.title"),messageLine1:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.messageLine1"),messageLine2:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.messageLine2"),actionDescription:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.actionDescription",{organizationName:i?.name}),submitLabel:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.title"),successMessage:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.successMessage"),onConfirmation:n,...e}):null},R=e=>{let{organization:i,membership:t}=(0,o.o8)(),n=B(i?.destroy);return i&&t?(0,a.tZ)(T,{organizationName:i?.name,title:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.title"),messageLine1:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.messageLine1"),messageLine2:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.messageLine2"),actionDescription:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.actionDescription",{organizationName:i?.name}),submitLabel:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.title"),successMessage:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.successMessage"),onConfirmation:n,...e}):null},T=(0,r.withCardStateProvider)(e=>{let{title:i,messageLine1:t,messageLine2:o,actionDescription:n,organizationName:l,successMessage:s,submitLabel:d,onSuccess:c,onReset:m,onConfirmation:p,colorScheme:h="danger"}=e,f=(0,u.a2)(),z=(0,r.useCardState)(),b=(0,_.Yp)("deleteOrganizationConfirmation","",{type:"text",label:n||(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.actionDescription"),isRequired:!0,placeholder:l}),v=!n||b.value===l,Z=async()=>{if(v)try{await p().then(()=>f.nextStep())}catch(e){(0,_.S3)(e,[],z.setError)}};return(0,a.BX)(u.en,{...f.props,children:[(0,a.tZ)(C.Y,{headerTitle:i,gap:1,children:(0,a.BX)(y.l.Root,{onSubmit:Z,children:[(0,a.BX)(g.Col,{children:[(0,a.tZ)(g.Text,{localizationKey:t,colorScheme:"secondary"}),(0,a.tZ)(g.Text,{localizationKey:o,colorScheme:"danger"})]}),(0,a.tZ)(y.l.ControlRow,{elementId:b.id,children:(0,a.tZ)(y.l.PlainInput,{...b.props})}),(0,a.BX)(S.K,{children:[(0,a.tZ)(y.l.SubmitButton,{block:!1,colorScheme:h,localizationKey:d,isDisabled:!v}),(0,a.tZ)(y.l.ResetButton,{localizationKey:(0,g.localizationKeys)("userProfile.formButtonReset"),block:!1,onClick:m})]})]})}),(0,a.tZ)(x.I,{title:i,text:s,onFinish:c})]})});var w=t(2667),I=t(2464),L=t(4174);let $=(e,{infoLabel:i})=>{let t=e?.totalPendingInvitations||0;return 0===(e?.totalPendingSuggestions||0)+t?[]:[i,(0,g.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.calloutInvitationCountLabel",{count:t}),(0,g.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.calloutSuggestionCountLabel",{count:t})]},X=e=>{let i=[];return e.domains.enrollmentModes.includes("manual_invitation")&&i.push({value:"manual_invitation",label:(0,g.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.manualInvitationOption__label"),description:(0,g.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.manualInvitationOption__description")}),e.domains.enrollmentModes.includes("automatic_invitation")&&i.push({value:"automatic_invitation",label:(0,g.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.automaticInvitationOption__label"),description:(0,g.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.automaticInvitationOption__description")}),e.domains.enrollmentModes.includes("automatic_suggestion")&&i.push({value:"automatic_suggestion",label:(0,g.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.automaticSuggestionOption__label"),description:(0,g.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.automaticSuggestionOption__description")}),i},k=()=>{let{organizationSettings:e}=(0,c.useEnvironment)();return X(e)},O=(0,r.withCardStateProvider)(e=>{let{domainId:i,mode:t="edit",onSuccess:l,onReset:s}=e,d=(0,r.useCardState)(),{organizationSettings:m}=(0,c.useEnvironment)(),{membership:p,organization:h,domains:f}=(0,o.o8)({domains:{infinite:!0}}),b="edit"===t,v=k(),Z=(0,_.Yp)("enrollmentMode","",{type:"radio",radioOptions:v,isRequired:!0}),P=(0,_.Yp)("deleteExistingInvitationsSuggestions","",{label:(0,g.localizationKeys)("formFieldLabel__organizationDomainDeletePending"),type:"checkbox"}),{data:x,isLoading:K}=(0,I.ib)(h?.getDomain,{domainId:i});(0,n.useEffect)(()=>{x&&Z.setValue(x.enrollmentMode)},[x?.id]);let B=(0,g.localizationKeys)("organizationProfile.verifiedDomainPage.title",{domain:x?.name}),D=(0,g.localizationKeys)("organizationProfile.verifiedDomainPage.subtitle",{domain:x?.name}),R=$(x,{infoLabel:(0,g.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.calloutInfoLabel")}),T=async()=>{if(x&&h&&p&&f)try{await x.updateEnrollmentMode({enrollmentMode:Z.value,deletePending:P.checked}),await f.revalidate(),l()}catch(e){(0,_.S3)(e,[Z],d.setError)}};return h&&m?K||!x?(0,a.tZ)(g.Flex,{direction:"row",align:"center",justify:"center",sx:{height:"100%"},children:(0,a.tZ)(g.Spinner,{size:"lg",colorScheme:"primary",elementDescriptor:g.descriptors.spinner})}):(x.verification&&"verified"===x.verification.status||s(),(0,a.tZ)(C.Y,{headerTitle:B,headerSubtitle:b?void 0:D,gap:4,children:(0,a.BX)(g.Col,{gap:6,children:[R.length>0&&(0,a.tZ)(u.aX,{icon:L.I$,children:R.map((e,i)=>(0,a.tZ)(g.Text,{as:"span",sx:{display:"block"},localizationKey:e},i))}),(0,a.tZ)(z.h.Root,{children:(0,a.tZ)(z.h.Subtitle,{localizationKey:(0,g.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.subtitle"),variant:"subtitle"})}),(0,a.BX)(y.l.Root,{onSubmit:T,gap:6,children:[(0,a.tZ)(y.l.ControlRow,{elementId:Z.id,children:(0,a.tZ)(y.l.RadioGroup,{...Z.props})}),b&&"manual_invitation"===Z.value&&(0,a.tZ)(y.l.ControlRow,{elementId:P.id,children:(0,a.tZ)(y.l.Checkbox,{...P.props})}),(0,a.tZ)(S.A,{isDisabled:K||!x,onReset:s})]})]})})):null}),A=(0,r.withCardStateProvider)(e=>{let{domainId:i,onSuccess:t,onReset:l,skipToVerified:s}=e,d=(0,r.useCardState)(),{organizationSettings:m}=(0,c.useEnvironment)(),{organization:p}=(0,o.o8)(),{data:h,isLoading:f}=(0,I.ib)(s?void 0:p?.getDomain,{domainId:i}),z=(0,g.localizationKeys)("organizationProfile.verifyDomainPage.title"),b=(0,g.localizationKeys)("organizationProfile.verifyDomainPage.subtitle",{domainName:h?.name??""}),v=(0,u.a2)({defaultStep:2*!!s,onNextStep:()=>d.setError(void 0)}),Z=(0,_.Yp)("affiliationEmailAddress","",{type:"text",label:(0,g.localizationKeys)("formFieldLabel__organizationDomainEmailAddress"),placeholder:(0,g.localizationKeys)("formFieldInputPlaceholder__organizationDomainEmailAddress"),infoText:(0,g.localizationKeys)("formFieldLabel__organizationDomainEmailAddressDescription"),isRequired:!0}),P=(0,n.useRef)(),x=(0,g.localizationKeys)("organizationProfile.verifyDomainPage.subtitleVerificationCodeScreen",{emailAddress:P.current}),K=(e,i,a)=>{h?.attemptAffiliationVerification?.({code:e}).then(async e=>{await i(),e.verification?.status==="verified"?v.nextStep():t?.()}).catch(e=>a(e))},B=(0,w.e3)({onCodeEntryFinished:(e,i,t)=>{K(e,i,t)},onResendCodeClicked:()=>{h?.prepareAffiliationVerification({affiliationEmailAddress:Z.value}).catch(e=>{_.S3(e,[Z],d.setError)})}});if(!p||!m)return null;let D=p.name!==Z.value,R=`@${h?.name}`;return!f&&h||s?(0,a.BX)(u.en,{...v.props,children:[(0,a.tZ)(C.Y,{headerTitle:z,headerSubtitle:b,children:(0,a.BX)(y.l.Root,{onSubmit:e=>{if(e.preventDefault(),h)return P.current=`${Z.value}${R}`,h.prepareAffiliationVerification({affiliationEmailAddress:P.current}).then(v.nextStep).catch(e=>{(0,_.S3)(e,[Z],d.setError)})},children:[(0,a.tZ)(y.l.ControlRow,{elementId:Z.id,children:(0,a.tZ)(y.l.InputGroup,{...Z.props,autoFocus:!0,groupSuffix:R,ignorePasswordManager:!0})}),(0,a.tZ)(S.A,{isDisabled:!D,onReset:l})]})}),(0,a.BX)(C.Y,{headerTitle:z,headerSubtitle:x,children:[(0,a.tZ)(y.l.OTPInput,{...B,label:(0,g.localizationKeys)("organizationProfile.verifyDomainPage.formTitle"),description:(0,g.localizationKeys)("organizationProfile.verifyDomainPage.formSubtitle"),resendButton:(0,g.localizationKeys)("organizationProfile.verifyDomainPage.resendButton")}),(0,a.tZ)(S.K,{children:(0,a.tZ)(g.Button,{elementDescriptor:g.descriptors.formButtonReset,block:!1,variant:"ghost",textVariant:"buttonSmall",type:"reset",isDisabled:B.isLoading||"success"===B.otpControl.otpInputProps.feedbackType,onClick:()=>{B.otpControl.otpInputProps.clearFeedback(),B.otpControl.reset(),v.prevStep()},localizationKey:(0,g.localizationKeys)("userProfile.formButtonReset")})})]}),(0,a.tZ)(O,{domainId:i,mode:"select",onSuccess:t,onReset:l})]}):(0,a.tZ)(g.Flex,{direction:"row",align:"center",justify:"center",children:(0,a.tZ)(g.Spinner,{size:"lg",colorScheme:"primary",elementDescriptor:g.descriptors.spinner})})}),E=(0,r.withCardStateProvider)(e=>{let{onSuccess:i,onReset:t}=e,{organizationSettings:l}=(0,c.useEnvironment)(),{domains:s}=(0,o.o8)({domains:{infinite:!0}}),d=(0,u.a2)({onNextStep:()=>v.setError(void 0)}),[m,p]=(0,n.useState)(""),[h,f]=(0,n.useState)(!1),z=(0,g.localizationKeys)("organizationProfile.createDomainPage.title"),b=(0,g.localizationKeys)("organizationProfile.createDomainPage.subtitle"),v=(0,r.useCardState)(),{organization:Z}=(0,o.o8)(),P=(0,_.Yp)("name","",{type:"text",label:(0,g.localizationKeys)("formFieldLabel__organizationDomain"),placeholder:(0,g.localizationKeys)("formFieldInputPlaceholder__organizationDomain")});if(!Z||!l)return null;let x=""!==P.value.trim();return(0,a.BX)(u.en,{...d.props,children:[(0,a.tZ)(C.Y,{headerTitle:z,headerSubtitle:b,children:(0,a.BX)(y.l.Root,{onSubmit:e=>(P.clearFeedback(),e.preventDefault(),Z.createDomain(P.value).then(async e=>{p(e.id),s?.revalidate?.(),e.verification&&"verified"===e.verification.status&&f(!0),d.nextStep()}).catch(e=>{(0,_.S3)(e,[P],v.setError)})),children:[(0,a.tZ)(y.l.ControlRow,{elementId:P.id,children:(0,a.tZ)(y.l.PlainInput,{...P.props,autoFocus:!0,ignorePasswordManager:!0,isRequired:!0})}),(0,a.tZ)(S.A,{isDisabled:!x,onReset:t})]})}),(0,a.tZ)(A,{domainId:m,onSuccess:()=>{s?.revalidate?.(),i?.()},skipToVerified:h,onReset:t})]})});var M=t(3009);let F={manual_invitation:(0,g.localizationKeys)("organizationProfile.badge__manualInvitation"),automatic_invitation:(0,g.localizationKeys)("organizationProfile.badge__automaticInvitation"),automatic_suggestion:(0,g.localizationKeys)("organizationProfile.badge__automaticSuggestion")},q=e=>{let{organizationDomain:i}=e;return i?i.verification&&"verified"===i.verification.status?(0,a.tZ)(g.Badge,{localizationKey:F[i.enrollmentMode],colorScheme:"manual_invitation"===i.enrollmentMode?"primary":"success"}):(0,a.tZ)(g.Badge,{localizationKey:(0,g.localizationKeys)("organizationProfile.badge__unverified"),colorScheme:"warning"}):null},N=e=>{let{organizationSettings:i}=(0,c.useEnvironment)(),{organization:t}=(0,o.o8)(),{domainId:r,onSuccess:l,onReset:s}=e,d=n.useRef(),{data:m,isLoading:h}=(0,I.ib)(t?.getDomain,{domainId:r},{onSuccess(e){d.current={...e}}}),{domains:f}=(0,o.o8)({domains:{infinite:!0}});return t&&i?h||!m?(0,a.tZ)(g.Flex,{direction:"row",align:"center",justify:"center",children:(0,a.tZ)(g.Spinner,{size:"lg",colorScheme:"primary",elementDescriptor:g.descriptors.spinner})}):(0,a.tZ)(u.LE,{title:(0,p.u1)("organizationProfile.removeDomainPage.title"),messageLine1:(0,p.u1)("organizationProfile.removeDomainPage.messageLine1",{domain:d.current?.name}),messageLine2:(0,p.u1)("organizationProfile.removeDomainPage.messageLine2"),deleteResource:()=>m?.delete().then(async()=>{await f?.revalidate?.(),l()}),onSuccess:l,onReset:s}):null},V=e=>{let{close:i}=(0,P.XC)();return(0,a.tZ)(N,{onSuccess:i,onReset:i,...e})},Y=e=>{let{close:i}=(0,P.XC)();return(0,a.tZ)(O,{onSuccess:i,onReset:i,...e})},j=e=>{let{close:i}=(0,P.XC)();return(0,a.tZ)(A,{onSuccess:i,onReset:i,skipToVerified:!1,...e})},H=e=>{let{open:i}=(0,P.XC)(),t=[];return e.verification&&"verified"===e.verification.status?t.push({label:(0,g.localizationKeys)("organizationProfile.profilePage.domainSection.menuAction__manage"),onClick:()=>i("manage")}):t.push({label:(0,g.localizationKeys)("organizationProfile.profilePage.domainSection.menuAction__verify"),onClick:()=>i("verify")}),t.push({label:(0,g.localizationKeys)("organizationProfile.profilePage.domainSection.menuAction__remove"),isDestructive:!0,onClick:()=>i("remove")}),t},W=({domain:e})=>{let i=H(e);return(0,a.tZ)(M.a,{actions:i})},U=(0,u.Ci)(e=>{let{verificationStatus:i,enrollmentMode:t,fallback:r,...l}=e,{organization:s,domains:d}=(0,o.o8)({domains:{infinite:!0,...l}}),{ref:c}=(0,I.YD)({threshold:0,onChange:e=>{e&&d?.fetchNext?.()}}),m=(0,n.useMemo)(()=>d?.data?d.data.filter(e=>{let a=!0,o=!0;return i&&(a=!!e.verification&&e.verification.status===i),t&&(o=e.enrollmentMode===t),a&&o}):[],[d?.data]);if(!s)return null;let p=d?.hasNextPage||d?.isFetching;return 0!==m.length||d?.isLoading||r?(0,a.BX)(v.zd.ItemList,{id:"organizationDomains",children:[0===m.length&&!d?.isLoading&&r,m.map(e=>(0,a.BX)(Z.a.Root,{children:[(0,a.BX)(v.zd.Item,{id:"organizationDomains",hoverable:!0,children:[(0,a.BX)(g.Flex,{sx:e=>({gap:e.space.$1}),children:[(0,a.tZ)(g.Text,{children:e.name}),(0,a.tZ)(q,{organizationDomain:e})]}),(0,a.tZ)(u.Cv,{permission:"org:sys_domains:manage",children:(0,a.tZ)(W,{domain:e})})]}),(0,a.tZ)(Z.a.Open,{value:"remove",children:(0,a.tZ)(Z.a.Card,{variant:"destructive",children:(0,a.tZ)(V,{domainId:e.id})})}),(0,a.tZ)(Z.a.Open,{value:"verify",children:(0,a.tZ)(Z.a.Card,{children:(0,a.tZ)(j,{domainId:e.id})})}),(0,a.tZ)(Z.a.Open,{value:"manage",children:(0,a.tZ)(Z.a.Card,{children:(0,a.tZ)(Y,{domainId:e.id})})})]},e.id)),(0,a.tZ)(g.Box,{ref:d?.hasNextPage&&!d.isFetching?c:void 0,sx:{visibility:"hidden"}}),p&&0===d.data.length&&(0,a.tZ)(g.Box,{sx:[e=>({width:"100%",height:e.space.$10,position:"relative"})],children:(0,a.tZ)(g.Box,{sx:{display:"flex",margin:"auto",position:"absolute",left:"50%",top:"50%",transform:"translateY(-50%) translateX(-50%)"},children:(0,a.tZ)(g.Spinner,{size:"sm",colorScheme:"primary",elementDescriptor:g.descriptors.spinner})})})]}):null},{permission:"org:sys_domains:read"});var Q=t(6917),G=t(8181);let J=(0,r.withCardStateProvider)(e=>{let{onSuccess:i,onReset:t}=e,n=(0,g.localizationKeys)("organizationProfile.profilePage.title"),l=(0,r.useCardState)(),{organization:s}=(0,o.o8)(),d=(0,_.Yp)("name",s?.name||"",{type:"text",label:(0,g.localizationKeys)("formFieldLabel__organizationName"),placeholder:(0,g.localizationKeys)("formFieldInputPlaceholder__organizationName")}),c=(0,_.Yp)("slug",s?.slug||"",{type:"text",label:(0,g.localizationKeys)("formFieldLabel__organizationSlug"),placeholder:(0,g.localizationKeys)("formFieldInputPlaceholder__organizationSlug")});if(!s)return null;let m=(s.name!==d.value||s.slug!==c.value)&&"error"!==c.feedbackType,u=async e=>(e.preventDefault(),(m?s.update({name:d.value,slug:c.value}):Promise.resolve()).then(i).catch(e=>{(0,_.S3)(e,[d,c],l.setError)})),p=e=>{c.setValue(e),c.clearFeedback()};return(0,a.tZ)(C.Y,{headerTitle:n,children:(0,a.BX)(y.l.Root,{onSubmit:u,children:[(0,a.tZ)(G.D,{organization:s,onAvatarChange:e=>s.setLogo({file:e}).then(()=>{l.setIdle()}).catch(e=>(0,_.S3)(e,[],l.setError)),onAvatarRemove:(0,Q.QO)(s.imageUrl)?null:()=>{s.setLogo({file:null}).then(()=>{l.setIdle()}).catch(e=>(0,_.S3)(e,[],l.setError))}}),(0,a.tZ)(y.l.ControlRow,{elementId:d.id,children:(0,a.tZ)(y.l.PlainInput,{...d.props,autoFocus:!0,isRequired:!0,ignorePasswordManager:!0})}),(0,a.tZ)(y.l.ControlRow,{elementId:c.id,children:(0,a.tZ)(y.l.PlainInput,{...c.props,onChange:e=>{p(e.target.value)},ignorePasswordManager:!0})}),(0,a.tZ)(S.A,{isDisabled:!m,onReset:t})]})})}),ee=()=>{let{close:e}=(0,P.XC)();return(0,a.tZ)(J,{onSuccess:e,onReset:e})},ei=()=>{let{close:e}=(0,P.XC)();return(0,a.tZ)(E,{onSuccess:e,onReset:e})},et=()=>{let{close:e}=(0,P.XC)();return(0,a.tZ)(D,{onSuccess:e,onReset:e})},ea=()=>{let{close:e}=(0,P.XC)();return(0,a.tZ)(R,{onSuccess:e,onReset:e})},eo=()=>(0,a.tZ)(g.Col,{elementDescriptor:g.descriptors.page,sx:e=>({gap:e.space.$8}),children:(0,a.BX)(g.Col,{elementDescriptor:g.descriptors.profilePage,elementId:g.descriptors.profilePage.setId("organizationGeneral"),children:[(0,a.tZ)(z.h.Root,{children:(0,a.tZ)(z.h.Title,{localizationKey:(0,g.localizationKeys)("organizationProfile.start.headerTitle__general"),sx:e=>({marginBottom:e.space.$4}),textVariant:"h2"})}),(0,a.tZ)(en,{}),(0,a.tZ)(u.Cv,{permission:"org:sys_domains:read",children:(0,a.tZ)(er,{})}),(0,a.tZ)(el,{}),(0,a.tZ)(es,{})]})}),en=()=>{let{organization:e}=(0,o.o8)();if(!e)return null;let i=(0,a.tZ)(b.Z,{size:"lg",mainIdentifierVariant:"subtitle",organization:e});return(0,a.tZ)(v.zd.Root,{title:(0,g.localizationKeys)("organizationProfile.start.profileSection.title"),id:"organizationProfile",children:(0,a.BX)(Z.a.Root,{children:[(0,a.tZ)(u.Cv,{permission:"org:sys_profile:manage",fallback:i,children:(0,a.tZ)(Z.a.Closed,{value:"edit",children:(0,a.BX)(v.zd.Item,{id:"organizationProfile",children:[i,(0,a.tZ)(Z.a.Trigger,{value:"edit",children:(0,a.tZ)(v.zd.Button,{id:"organizationProfile",localizationKey:(0,g.localizationKeys)("organizationProfile.start.profileSection.primaryButton")})})]})})}),(0,a.tZ)(Z.a.Open,{value:"edit",children:(0,a.tZ)(Z.a.Card,{children:(0,a.tZ)(ee,{})})})]})})},er=()=>{let{organizationSettings:e}=(0,c.useEnvironment)(),{organization:i}=(0,o.o8)();return e&&i&&e.domains.enabled?(0,a.tZ)(v.zd.Root,{title:(0,g.localizationKeys)("organizationProfile.profilePage.domainSection.title"),id:"organizationDomains",centered:!1,children:(0,a.BX)(Z.a.Root,{children:[(0,a.tZ)(U,{}),(0,a.BX)(u.Cv,{permission:"org:sys_domains:manage",children:[(0,a.tZ)(Z.a.Trigger,{value:"add",children:(0,a.BX)(g.Col,{children:[(0,a.tZ)(v.zd.ArrowButton,{localizationKey:(0,g.localizationKeys)("organizationProfile.profilePage.domainSection.primaryButton"),id:"organizationDomains"}),(0,a.tZ)(g.Text,{localizationKey:(0,g.localizationKeys)("organizationProfile.profilePage.domainSection.subtitle"),sx:e=>({paddingLeft:e.space.$9}),colorScheme:"secondary"})]})}),(0,a.tZ)(Z.a.Open,{value:"add",children:(0,a.tZ)(Z.a.Card,{children:(0,a.tZ)(ei,{})})})]})]})}):null},el=()=>{let{organization:e}=(0,o.o8)();return e?(0,a.tZ)(v.zd.Root,{id:"organizationDanger",title:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.title"),children:(0,a.BX)(Z.a.Root,{children:[(0,a.tZ)(Z.a.Closed,{value:"leave",children:(0,a.tZ)(v.zd.Item,{sx:e=>({paddingTop:0,paddingBottom:0,paddingLeft:e.space.$1}),id:"organizationDanger",children:(0,a.tZ)(Z.a.Trigger,{value:"leave",children:(0,a.tZ)(v.zd.Button,{id:"organizationDanger",variant:"ghost",colorScheme:"danger",textVariant:"buttonLarge",localizationKey:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.title")})})})}),(0,a.tZ)(Z.a.Open,{value:"leave",children:(0,a.tZ)(Z.a.Card,{variant:"destructive",children:(0,a.tZ)(et,{})})})]})}):null},es=()=>{let{organization:e}=(0,o.o8)(),i=(0,u.N2)({permission:"org:sys_profile:delete"});if(!e)return null;let t=e.adminDeleteEnabled;return i&&t?(0,a.tZ)(v.zd.Root,{id:"organizationDanger",title:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.title"),sx:e=>({marginBottom:e.space.$4}),children:(0,a.BX)(Z.a.Root,{children:[(0,a.tZ)(Z.a.Closed,{value:"delete",children:(0,a.tZ)(v.zd.Item,{sx:e=>({paddingTop:0,paddingBottom:0,paddingLeft:e.space.$1}),id:"organizationDanger",children:(0,a.tZ)(Z.a.Trigger,{value:"delete",children:(0,a.tZ)(v.zd.Button,{id:"organizationDanger",variant:"ghost",colorScheme:"danger",textVariant:"buttonLarge",localizationKey:(0,g.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.title")})})})}),(0,a.tZ)(Z.a.Open,{value:"delete",children:(0,a.tZ)(Z.a.Card,{variant:"destructive",children:(0,a.tZ)(ea,{})})})]})}):null};var ed=t(6781),ec=t(4455),eg=t(708),em=t(1201),eu=t(7295),ep=t(3559),eh=t(5872),ef=t(408);let ez=({memberships:e,pageSize:i})=>{let t=(0,r.useCardState)(),{organization:n}=(0,o.o8)(),{options:l,isLoading:s}=(0,eh.e)();if(!n)return null;let d=e=>i=>t.runAsync(()=>e.update({role:i})).catch(e=>(0,_.S3)(e,[],t.setError)),c=i=>async()=>t.runAsync(async()=>{let t=await i.destroy();return await e?.revalidate?.(),t}).catch(e=>(0,_.S3)(e,[],t.setError));return(0,a.tZ)(ef.wQ,{page:e?.page||1,onPageChange:i=>e?.fetchPage?.(i),itemCount:e?.count||0,pageCount:e?.pageCount||0,itemsPerPage:i,isLoading:e?.isLoading&&!e?.data.length||s,emptyStateLocalizationKey:(0,g.localizationKeys)("organizationProfile.membersPage.detailsTitle__emptyRow"),headers:[(0,g.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__user"),(0,g.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__joined"),(0,g.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__role"),(0,g.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__actions")],rows:(e?.data||[]).map(e=>(0,a.tZ)(eb,{membership:e,options:l,onRoleChange:d(e),onRemove:c(e)},e.id))})},eb=e=>{let{membership:i,onRemove:t,onRoleChange:n,options:l}=e,{localizeCustomRole:s}=(0,eh.q)(),d=(0,r.useCardState)(),{user:c}=(0,o.aF)(),m=c?.id===i.publicUserData?.userId,u=l?.find(e=>e.value===i.role)?.label;return(0,a.BX)(ef.RN,{children:[(0,a.tZ)(g.Td,{children:(0,a.tZ)(eu.E,{sx:{maxWidth:"30ch"},user:i.publicUserData,subtitle:i.publicUserData?.identifier,badge:m&&(0,a.tZ)(g.Badge,{localizationKey:(0,g.localizationKeys)("badge__you")})})}),(0,a.tZ)(g.Td,{children:(0,a.tZ)(g.Box,{as:"span",elementDescriptor:g.descriptors.formattedDate,elementId:g.descriptors.formattedDate.setId("tableCell"),children:i.createdAt.toLocaleDateString()})}),(0,a.tZ)(g.Td,{children:(0,a.tZ)(ep.Cv,{permission:"org:sys_memberships:manage",fallback:(0,a.tZ)(g.Text,{sx:e=>({opacity:e.opacity.$inactive}),children:s(i.role)||u}),children:(0,a.tZ)(ef.DQ,{isDisabled:d.isLoading||!n,value:i.role,fallbackLabel:i.roleName,onChange:n,roles:l})})}),(0,a.tZ)(g.Td,{children:(0,a.tZ)(ep.Cv,{permission:"org:sys_memberships:manage",children:(0,a.tZ)(M.a,{actions:[{label:(0,g.localizationKeys)("organizationProfile.membersPage.activeMembersTab.menuAction__remove"),isDestructive:!0,onClick:t,isDisabled:m}],elementId:"member"})})})]})};var ev=t(5727);let eZ=({actionSlot:e})=>{let i=(0,u.N2)({permission:"org:sys_memberships:manage"});return(0,a.BX)(Z.a.Root,{animate:!1,children:[(0,a.tZ)(ed.f,{asChild:!0,children:(0,a.BX)(g.Flex,{justify:e?"between":"end",sx:e=>({width:"100%",marginLeft:"auto",padding:`${e.space.$none} ${e.space.$1}`}),gap:e?2:void 0,children:[e,i&&(0,a.tZ)(Z.a.Trigger,{value:"invite",hideOnActive:!e,children:(0,a.tZ)(g.Button,{elementDescriptor:g.descriptors.membersPageInviteButton,"aria-label":"Invite",localizationKey:(0,g.localizationKeys)("organizationProfile.membersPage.action__invite")})})]})}),i&&(0,a.tZ)(ed.f,{children:(0,a.tZ)(Z.a.Open,{value:"invite",children:(0,a.tZ)(g.Flex,{sx:e=>({paddingBottom:e.space.$6,padding:`${e.space.$none} ${e.space.$1} ${e.space.$6} ${e.space.$1}`}),children:(0,a.tZ)(Z.a.Card,{sx:{width:"100%"},children:(0,a.tZ)(ev.N,{})})})})})]})};var eP=t(932);let ey=()=>{let{organization:e}=(0,o.o8)(),{__unstable_manageBillingUrl:i,__unstable_manageBillingLabel:t,__unstable_manageBillingMembersLimit:n}=(0,c.useOrganizationProfileContext)(),r=(0,m.useRouter)();if(!e)return null;let l=e?.membersCount+e?.pendingInvitationsCount,s=(0,Q.OR)(n);return(0,a.tZ)(g.Flex,{sx:e=>({background:e.colors.$neutralAlpha50,padding:e.space.$2,borderRadius:e.radii.$md,gap:e.space.$4}),children:(0,a.BX)(g.Flex,{align:"center",sx:e=>({[em.mqu.sm]:{gap:e.space.$3},gap:e.space.$2}),children:[s>0&&(0,a.tZ)(eP.a,{limit:s,value:l,size:"xs"}),(0,a.BX)(g.Flex,{sx:e=>({[em.mqu.sm]:{flexDirection:"column"},gap:e.space.$0x5}),children:[(0,a.BX)(g.Text,{children:["You can invite ",n?`up to ${s}`:"unlimited"," members."]}),s>0&&(0,a.BX)(g.Link,{sx:e=>({fontWeight:e.fontWeights.$medium}),variant:"body",onClick:()=>r.navigate((0,Q.OR)(i)),children:[(0,Q.OR)(t)||"Manage billing",(0,a.tZ)(g.Icon,{icon:L.LZ})]})]})]})})};var eS=t(1319),eC=t(215);let ex=({query:e,value:i,memberships:t,onSearchChange:o,onQueryTrigger:r})=>{let{t:l}=(0,g.useLocalizations)(),s=(0,n.useRef)(null);(0,n.useEffect)(()=>{e&&t?.data&&(t?.count??0)<=e$&&t?.fetchPage?.(1)},[e,t]);let d=i&&!!t?.isLoading&&!!t.data?.length;return(0,a.tZ)(ed.f,{asChild:!0,children:(0,a.tZ)(g.Flex,{sx:{width:"50%",[em.mqu.sm]:{width:"auto"}},children:(0,a.tZ)(eS.W,{value:i,type:"search",autoCapitalize:"none",spellCheck:!1,"aria-label":"Search",placeholder:l((0,g.localizationKeys)("organizationProfile.membersPage.action__search")),leftIcon:d?(0,a.tZ)(eC.$j,{size:"xs"}):(0,a.tZ)(g.Icon,{icon:L.Yt,elementDescriptor:g.descriptors.organizationProfileMembersSearchInputIcon}),onKeyUp:function(){s.current&&clearTimeout(s.current),s.current=setTimeout(()=>{r(i.trim())},500)},onChange:e=>{let i=e.target.value;o(i),""===i&&r(i)},elementDescriptor:g.descriptors.organizationProfileMembersSearchInput})})})},e_={invitations:{pageSize:10,keepPreviousData:!0}},eK=()=>{let e=(0,r.useCardState)(),{organization:i,invitations:t}=(0,o.o8)(e_),{options:n,isLoading:l}=(0,eh.e)();if(!i)return null;let s=i=>async()=>e.runAsync(async()=>(await i.revoke(),await t?.revalidate?.(),i)).catch(i=>(0,_.S3)(i,[],e.setError));return(0,a.tZ)(ef.wQ,{page:t?.page||1,onPageChange:t?.fetchPage||(()=>null),itemCount:t?.count||0,pageCount:t?.pageCount||0,itemsPerPage:e_.invitations.pageSize,isLoading:t?.isLoading||l,emptyStateLocalizationKey:(0,g.localizationKeys)("organizationProfile.membersPage.invitationsTab.table__emptyRow"),headers:[(0,g.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__user"),(0,g.localizationKeys)("organizationProfile.membersPage.invitedMembersTab.tableHeader__invited"),(0,g.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__role"),(0,g.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__actions")],rows:(t?.data||[]).map(e=>(0,a.tZ)(eB,{options:n,invitation:e,onRevoke:s(e)},e.id))})},eB=e=>{let{invitation:i,onRevoke:t,options:o}=e,{localizeCustomRole:n}=(0,eh.q)(),r=o?.find(e=>e.value===i.role)?.label;return(0,a.BX)(ef.RN,{children:[(0,a.tZ)(g.Td,{children:(0,a.tZ)(eu.E,{sx:{maxWidth:"30ch"},user:{primaryEmailAddress:{emailAddress:i.emailAddress}}})}),(0,a.tZ)(g.Td,{children:(0,a.tZ)(g.Box,{as:"span",elementDescriptor:g.descriptors.formattedDate,elementId:g.descriptors.formattedDate.setId("tableCell"),children:i.createdAt.toLocaleDateString()})}),(0,a.tZ)(g.Td,{children:(0,a.tZ)(g.Text,{colorScheme:"secondary",localizationKey:n(i.role)||r})}),(0,a.tZ)(g.Td,{children:(0,a.tZ)(M.a,{actions:[{label:(0,g.localizationKeys)("organizationProfile.membersPage.invitedMembersTab.menuAction__revoke"),isDestructive:!0,onClick:t}],elementId:"invitation"})})]})},eD=(0,r.withCardStateProvider)(()=>{let{organizationSettings:e}=(0,c.useEnvironment)(),{__unstable_manageBillingUrl:i,navigateToGeneralPageRoot:t}=(0,c.useOrganizationProfileContext)(),o=e?.domains?.enabled;return(0,a.BX)(g.Col,{gap:4,sx:{width:"100%"},children:[i&&(0,a.tZ)(ey,{}),o&&(0,a.tZ)(u.Cv,{permission:"org:sys_domains:manage",children:(0,a.BX)(g.Flex,{sx:e=>({width:"100%",gap:e.space.$8,paddingBottom:e.space.$4,paddingLeft:e.space.$1,paddingRight:e.space.$1,borderBottomWidth:e.borderWidths.$normal,borderBottomStyle:e.borderStyles.$solid,borderBottomColor:e.colors.$neutralAlpha100,[em.mqu.md]:{flexDirection:"column",gap:e.space.$2}}),children:[(0,a.tZ)(g.Col,{sx:e=>({width:e.space.$66,marginTop:e.space.$2}),children:(0,a.tZ)(z.h.Root,{children:(0,a.tZ)(z.h.Title,{localizationKey:(0,g.localizationKeys)("organizationProfile.membersPage.invitationsTab.autoInvitations.headerTitle"),textVariant:"h3"})})}),(0,a.tZ)(g.Col,{sx:{width:"100%"},children:(0,a.tZ)(U,{fallback:(0,a.BX)(a.HY,{children:[(0,a.tZ)(v.zd.ArrowButton,{localizationKey:(0,g.localizationKeys)("organizationProfile.membersPage.invitationsTab.autoInvitations.primaryButton"),id:"manageVerifiedDomains",sx:e=>({gap:e.space.$2}),onClick:t}),(0,a.tZ)(g.Text,{localizationKey:(0,g.localizationKeys)("organizationProfile.membersPage.invitationsTab.autoInvitations.headerSubtitle"),sx:e=>({paddingLeft:e.space.$10,color:e.colors.$colorTextSecondary,[em.mqu.md]:{paddingLeft:0}})})]}),verificationStatus:"verified",enrollmentMode:"automatic_invitation"})})]})}),(0,a.BX)(g.Flex,{direction:"col",gap:2,sx:{width:"100%"},children:[(0,a.tZ)(eZ,{}),(0,a.tZ)(eK,{})]})]})}),eR={membershipRequests:{pageSize:10,keepPreviousData:!0}},eT=()=>{let e=(0,r.useCardState)(),{organization:i,membershipRequests:t}=(0,o.o8)(eR);return i?(0,a.tZ)(ef.wQ,{page:t?.page||1,onPageChange:t?.fetchPage??(()=>null),itemCount:t?.count||0,pageCount:t?.pageCount||0,itemsPerPage:eR.membershipRequests.pageSize,isLoading:t?.isLoading,emptyStateLocalizationKey:(0,g.localizationKeys)("organizationProfile.membersPage.requestsTab.table__emptyRow"),headers:[(0,g.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__user"),(0,g.localizationKeys)("organizationProfile.membersPage.requestsTab.tableHeader__requested"),(0,g.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__actions")],rows:(t?.data||[]).map(i=>(0,a.tZ)(ew,{request:i,onError:e.setError},i.id))}):null},ew=(0,r.withCardStateProvider)(e=>{let{request:i,onError:t}=e,n=(0,r.useCardState)(),{membership:l,membershipRequests:s}=(0,o.o8)(eR);return(0,a.BX)(ef.RN,{children:[(0,a.tZ)(g.Td,{children:(0,a.tZ)(eu.E,{sx:{maxWidth:"30ch"},showAvatar:!1,user:{primaryEmailAddress:{emailAddress:i.publicUserData.identifier}}})}),(0,a.tZ)(g.Td,{children:(0,a.tZ)(g.Box,{as:"span",elementDescriptor:g.descriptors.formattedDate,elementId:g.descriptors.formattedDate.setId("tableCell"),children:i.createdAt.toLocaleDateString()})}),(0,a.tZ)(g.Td,{children:(0,a.tZ)(eI,{onAccept:()=>{if(l&&s)return n.runAsync(async()=>{await i.accept(),await s.revalidate()},"accept").catch(e=>(0,_.S3)(e,[],t))},onReject:()=>{if(l&&s)return n.runAsync(async()=>{await i.reject(),await s.revalidate()},"reject").catch(e=>(0,_.S3)(e,[],t))}})})]})}),eI=e=>{let i=(0,r.useCardState)();return(0,a.BX)(g.Flex,{gap:2,children:[(0,a.tZ)(g.Button,{textVariant:"buttonSmall",variant:"ghost",isLoading:i.isLoading&&"reject"===i.loadingMetadata,isDisabled:i.isLoading&&"reject"!==i.loadingMetadata,onClick:e.onReject,localizationKey:(0,g.localizationKeys)("organizationProfile.membersPage.requestsTab.menuAction__reject")}),(0,a.tZ)(g.Button,{textVariant:"buttonSmall",isLoading:i.isLoading&&"accept"===i.loadingMetadata,isDisabled:i.isLoading&&"accept"!==i.loadingMetadata,onClick:e.onAccept,localizationKey:(0,g.localizationKeys)("organizationProfile.membersPage.requestsTab.menuAction__approve")})]})},eL=()=>{let{organizationSettings:e}=(0,c.useEnvironment)(),{__unstable_manageBillingUrl:i,navigateToGeneralPageRoot:t}=(0,c.useOrganizationProfileContext)(),o=e?.domains?.enabled;return(0,a.BX)(g.Col,{gap:4,sx:{width:"100%"},children:[i&&(0,a.tZ)(ey,{}),o&&(0,a.tZ)(u.Cv,{permission:"org:sys_domains:manage",children:(0,a.BX)(g.Flex,{sx:e=>({width:"100%",gap:e.space.$8,paddingBottom:e.space.$4,paddingLeft:e.space.$1,paddingRight:e.space.$1,borderBottomWidth:e.borderWidths.$normal,borderBottomStyle:e.borderStyles.$solid,borderBottomColor:e.colors.$neutralAlpha100,[em.mqu.md]:{flexDirection:"column",gap:e.space.$2}}),children:[(0,a.tZ)(g.Col,{sx:e=>({width:e.space.$66,marginTop:e.space.$2}),children:(0,a.tZ)(z.h.Root,{children:(0,a.tZ)(z.h.Title,{localizationKey:(0,g.localizationKeys)("organizationProfile.membersPage.requestsTab.autoSuggestions.headerTitle"),textVariant:"h3"})})}),(0,a.tZ)(g.Col,{sx:{width:"100%"},children:(0,a.tZ)(U,{fallback:(0,a.BX)(a.HY,{children:[(0,a.tZ)(v.zd.ArrowButton,{localizationKey:(0,g.localizationKeys)("organizationProfile.membersPage.requestsTab.autoSuggestions.primaryButton"),sx:e=>({gap:e.space.$2}),id:"manageVerifiedDomains",onClick:t}),(0,a.tZ)(g.Text,{localizationKey:(0,g.localizationKeys)("organizationProfile.membersPage.requestsTab.autoSuggestions.headerSubtitle"),sx:e=>({paddingLeft:e.space.$10,color:e.colors.$colorTextSecondary,[em.mqu.md]:{paddingLeft:0}})})]}),verificationStatus:"verified",enrollmentMode:"automatic_suggestion"})})]})}),(0,a.BX)(g.Flex,{direction:"col",gap:2,sx:{width:"100%"},children:[(0,a.tZ)(eZ,{}),(0,a.tZ)(eT,{})]})]})},e$=10,eX=(0,r.withCardStateProvider)(()=>{let{organizationSettings:e}=(0,c.useEnvironment)(),i=(0,r.useCardState)(),t=(0,u.N2)({permission:"org:sys_memberships:manage"}),l=(0,u.N2)({permission:"org:sys_memberships:read"}),s=e?.domains?.enabled&&t,[d,m]=(0,n.useState)(""),[p,h]=(0,n.useState)(""),{membershipRequests:f,memberships:b,invitations:v}=(0,o.o8)({membershipRequests:s||void 0,invitations:t||void 0,memberships:l?{keepPreviousData:!0,query:d||void 0}:void 0}),{__unstable_manageBillingUrl:P}=(0,c.useOrganizationProfileContext)();return null===t?null:(0,a.tZ)(g.Col,{elementDescriptor:g.descriptors.page,gap:2,children:(0,a.tZ)(g.Col,{elementDescriptor:g.descriptors.profilePage,elementId:g.descriptors.profilePage.setId("organizationMembers"),gap:4,children:(0,a.BX)(Z.a.Root,{animate:!1,children:[(0,a.tZ)(ed.f,{asChild:!0,children:(0,a.tZ)(z.h.Root,{contentSx:{[em.mqu.md]:{flexDirection:"row",width:"100%",justifyContent:"space-between"}},children:(0,a.tZ)(z.h.Title,{localizationKey:(0,g.localizationKeys)("organizationProfile.start.headerTitle__members"),textVariant:"h2"})})}),(0,a.tZ)(ec.Z.Alert,{children:i.error}),(0,a.BX)(eg.mQ,{children:[(0,a.BX)(eg.dr,{sx:e=>({gap:e.space.$2}),children:[l&&(0,a.tZ)(eg.OK,{localizationKey:(0,g.localizationKeys)("organizationProfile.membersPage.start.headerTitle__members"),children:!!b?.count&&(0,a.tZ)(u.dN,{shouldAnimate:!d,notificationCount:b.count,colorScheme:"outline"})}),t&&(0,a.tZ)(eg.OK,{localizationKey:(0,g.localizationKeys)("organizationProfile.membersPage.start.headerTitle__invitations"),children:v?.data&&!v.isLoading&&(0,a.tZ)(u.dN,{notificationCount:v.count,colorScheme:"outline"})}),t&&s&&(0,a.tZ)(eg.OK,{localizationKey:(0,g.localizationKeys)("organizationProfile.membersPage.start.headerTitle__requests"),children:f?.data&&!f.isLoading&&(0,a.tZ)(u.dN,{notificationCount:f.count,colorScheme:"outline"})})]}),(0,a.BX)(eg.nP,{children:[l&&(0,a.tZ)(eg.x4,{sx:{width:"100%"},children:(0,a.BX)(g.Flex,{gap:4,direction:"col",sx:{width:"100%"},children:[t&&P&&(0,a.tZ)(ey,{}),(0,a.BX)(g.Flex,{gap:2,direction:"col",sx:{width:"100%"},children:[(0,a.tZ)(eZ,{actionSlot:(0,a.tZ)(ex,{query:d,value:p,memberships:b,onSearchChange:e=>h(e),onQueryTrigger:e=>m(e)})}),(0,a.tZ)(ez,{pageSize:e$,memberships:b})]})]})}),t&&(0,a.tZ)(eg.x4,{sx:{width:"100%"},children:(0,a.tZ)(eD,{})}),t&&s&&(0,a.tZ)(eg.x4,{sx:{width:"100%"},children:(0,a.tZ)(eL,{})})]})]})]})})})});var ek=t(2264);let eO=()=>(0,a.tZ)(c.SubscriberTypeContext.Provider,{value:"org",children:(0,a.tZ)(ek.r,{})});var eA=t(9117),eE=t(5006);let eM=()=>{let{navigate:e}=(0,m.useRouter)();return(0,a.BX)(a.HY,{children:[(0,a.tZ)(z.h.Root,{sx:e=>({borderBottomWidth:e.borderWidths.$normal,borderBottomStyle:e.borderStyles.$solid,borderBottomColor:e.colors.$neutralAlpha100,marginBlockEnd:e.space.$4,paddingBlockEnd:e.space.$4}),children:(0,a.tZ)(z.h.BackLink,{onClick:()=>void e("../",{searchParams:new URLSearchParams("tab=subscriptions")}),children:(0,a.tZ)(z.h.Title,{localizationKey:(0,p.u1)("organizationProfile.plansPage.title"),textVariant:"h2"})})}),(0,a.BX)(g.Flex,{direction:"col",gap:4,children:[(0,a.tZ)(u.Cv,{condition:e=>!e({permission:"org:sys_billing:manage"}),children:(0,a.tZ)(eA.b,{variant:"info",colorScheme:"info",title:(0,p.u1)("organizationProfile.plansPage.alerts.noPermissionsToManageBilling")})}),(0,a.tZ)(c.PricingTableContext.Provider,{value:{componentName:"PricingTable",mode:"modal"},children:(0,a.tZ)(eE.b,{})})]})]})},eF=()=>(0,a.tZ)(c.SubscriberTypeContext.Provider,{value:"org",children:(0,a.tZ)(eM,{})});var eq=t(5515);let eN=()=>(0,a.tZ)(c.SubscriberTypeContext.Provider,{value:"org",children:(0,a.tZ)(eq.j,{})}),eV=(0,n.lazy)(()=>Promise.all([t.e("507"),t.e("200"),t.e("573"),t.e("916")]).then(t.bind(t,4961)).then(e=>({default:e.OrganizationBillingPage}))),eY=(0,n.lazy)(()=>Promise.all([t.e("200"),t.e("573"),t.e("616"),t.e("150")]).then(t.bind(t,3879)).then(e=>({default:e.OrganizationAPIKeysPage}))),ej=()=>{let{pages:e,isMembersPageRoot:i,isGeneralPageRoot:t,isBillingPageRoot:o,isApiKeysPageRoot:r}=(0,c.useOrganizationProfileContext)(),{apiKeysSettings:l,commerceSettings:s}=(0,c.useEnvironment)(),d=e.contents?.map((e,o)=>{let n=!t&&!i&&0===o;return a.tZ(m.Route,{index:n,path:n?void 0:e.url,children:a.tZ(f.O,{mount:e.mount,unmount:e.unmount})},`custom-page-${e.url}`)});return(0,a.BX)(m.Switch,{children:[d,(0,a.BX)(m.Route,{children:[(0,a.tZ)(m.Route,{path:t?void 0:"organization-general",children:(0,a.tZ)(m.Switch,{children:(0,a.tZ)(m.Route,{index:!0,children:(0,a.tZ)(eo,{})})})}),(0,a.tZ)(m.Route,{path:i?void 0:"organization-members",children:(0,a.tZ)(m.Switch,{children:(0,a.tZ)(m.Route,{index:!0,children:(0,a.tZ)(u.Cv,{condition:e=>e({permission:"org:sys_memberships:read"})||e({permission:"org:sys_memberships:manage"}),redirectTo:t?"../":"./organization-general",children:(0,a.tZ)(eX,{})})})})}),s.billing.enabled&&s.billing.hasPaidOrgPlans&&(0,a.tZ)(u.Cv,{condition:e=>e({permission:"org:sys_billing:read"})||e({permission:"org:sys_billing:manage"}),children:(0,a.tZ)(m.Route,{path:o?void 0:"organization-billing",children:(0,a.BX)(m.Switch,{children:[(0,a.tZ)(m.Route,{index:!0,children:(0,a.tZ)(n.Suspense,{fallback:"",children:(0,a.tZ)(eV,{})})}),(0,a.tZ)(m.Route,{path:"plans",children:(0,a.tZ)(n.Suspense,{fallback:"",children:(0,a.tZ)(eF,{})})}),(0,a.tZ)(m.Route,{path:"statement/:statementId",children:(0,a.tZ)(n.Suspense,{fallback:"",children:(0,a.tZ)(eN,{})})}),(0,a.tZ)(m.Route,{path:"payment-attempt/:paymentAttemptId",children:(0,a.tZ)(n.Suspense,{fallback:"",children:(0,a.tZ)(eO,{})})})]})})}),l.enabled&&(0,a.tZ)(m.Route,{path:r?void 0:"organization-api-keys",children:(0,a.tZ)(m.Switch,{children:(0,a.tZ)(m.Route,{index:!0,children:(0,a.tZ)(n.Suspense,{fallback:"",children:(0,a.tZ)(eY,{})})})})})]})]})},eH=(0,c.withCoreUserGuard)(()=>{let e=n.useRef(null);return(0,a.tZ)(s.P.Root,{sx:e=>({display:"grid",gridTemplateColumns:"1fr 3fr",height:e.sizes.$176,overflow:"hidden"}),children:(0,a.BX)(h,{contentRef:e,children:[(0,a.tZ)(l.ap,{navbarTitleLocalizationKey:(0,g.localizationKeys)("organizationProfile.navbar.title")}),(0,a.tZ)(s.P.Content,{contentRef:e,scrollBoxId:d.aw,children:(0,a.tZ)(ej,{})})]})})}),eW=(0,r.withCardStateProvider)(e=>{let{organization:i}=(0,o.o8)();return i?(0,a.tZ)(g.Flow.Root,{flow:"organizationProfile",children:(0,a.tZ)(g.Flow.Part,{children:(0,a.tZ)(m.Switch,{children:(0,a.tZ)(m.Route,{children:(0,a.tZ)(eH,{})})})})}):null}),eU=e=>{let i={...e,routing:"virtual",componentName:"OrganizationProfile",mode:"modal"};return(0,a.tZ)(m.Route,{path:"organizationProfile",children:(0,a.tZ)(c.OrganizationProfileContext.Provider,{value:i,children:(0,a.tZ)("div",{children:(0,a.tZ)(eW,{...i})})})})}}}]);