// src/vi-VN.ts
var viVN = {
  locale: "vi-VN",
  backButton: "Quay l\u1EA1i",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "M\u1EB7c \u0111\u1ECBnh",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Thi\u1EBFt b\u1ECB nh\xE2n danh kh\xE1c",
  badge__primary: "Ch\xEDnh",
  badge__renewsAt: void 0,
  badge__requiresAction: "Y\xEAu c\u1EA7u h\xE0nh \u0111\u1ED9ng",
  badge__startsAt: void 0,
  badge__thisDevice: "Thi\u1EBFt b\u1ECB n\xE0y",
  badge__unverified: "Ch\u01B0a x\xE1c minh",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Thi\u1EBFt b\u1ECB ng\u01B0\u1EDDi d\xF9ng",
  badge__you: "B\u1EA1n",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "T\u1EA1o t\u1ED5 ch\u1EE9c",
    invitePage: {
      formButtonReset: "B\u1ECF qua"
    },
    title: "T\u1EA1o T\u1ED5 ch\u1EE9c"
  },
  dates: {
    lastDay: "H\xF4m qua l\xFAc {{ date | timeString('vi-VN') }}",
    next6Days: "V\xE0o {{ date | weekday('vi-VN','long') }} t\u1EDBi l\xFAc {{ date | timeString('vi-VN') }}",
    nextDay: "Ng\xE0y mai l\xFAc {{ date | timeString('vi-VN') }}",
    numeric: "{{ date | numeric('vi-VN') }}",
    previous6Days: "V\xE0o {{ date | weekday('vi-VN','long') }} tr\u01B0\u1EDBc \u0111\xF3 l\xFAc {{ date | timeString('vi-VN') }}",
    sameDay: "H\xF4m nay l\xFAc {{ date | timeString('vi-VN') }}"
  },
  dividerText: "ho\u1EB7c",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "S\u1EED d\u1EE5ng ph\u01B0\u01A1ng ph\xE1p kh\xE1c",
  footerPageLink__help: "Tr\u1EE3 gi\xFAp",
  footerPageLink__privacy: "Quy\u1EC1n ri\xEAng t\u01B0",
  footerPageLink__terms: "\u0110i\u1EC1u kho\u1EA3n",
  formButtonPrimary: "Ti\u1EBFp t\u1EE5c",
  formButtonPrimary__verify: "Verify",
  formFieldAction__forgotPassword: "Qu\xEAn m\u1EADt kh\u1EA9u?",
  formFieldError__matchingPasswords: "M\u1EADt kh\u1EA9u kh\u1EDBp.",
  formFieldError__notMatchingPasswords: "M\u1EADt kh\u1EA9u kh\xF4ng kh\u1EDBp.",
  formFieldError__verificationLinkExpired: "The verification link expired. Please request a new link.",
  formFieldHintText__optional: "T\xF9y ch\u1ECDn",
  formFieldHintText__slug: "A slug is a human-readable ID that must be unique. It\u2019s often used in URLs.",
  formFieldInputPlaceholder__backupCode: void 0,
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Delete account",
  formFieldInputPlaceholder__emailAddress: void 0,
  formFieldInputPlaceholder__emailAddress_username: void 0,
  formFieldInputPlaceholder__emailAddresses: "Nh\u1EADp ho\u1EB7c d\xE1n m\u1ED9t ho\u1EB7c nhi\u1EC1u \u0111\u1ECBa ch\u1EC9 email, c\xE1ch nhau b\u1EB1ng kho\u1EA3ng tr\u1EAFng ho\u1EB7c d\u1EA5u ph\u1EA9y",
  formFieldInputPlaceholder__firstName: void 0,
  formFieldInputPlaceholder__lastName: void 0,
  formFieldInputPlaceholder__organizationDomain: void 0,
  formFieldInputPlaceholder__organizationDomainEmailAddress: void 0,
  formFieldInputPlaceholder__organizationName: void 0,
  formFieldInputPlaceholder__organizationSlug: void 0,
  formFieldInputPlaceholder__password: void 0,
  formFieldInputPlaceholder__phoneNumber: void 0,
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__automaticInvitations: "Enable automatic invitations for this domain",
  formFieldLabel__backupCode: "M\xE3 sao l\u01B0u",
  formFieldLabel__confirmDeletion: "Confirmation",
  formFieldLabel__confirmPassword: "X\xE1c nh\u1EADn m\u1EADt kh\u1EA9u",
  formFieldLabel__currentPassword: "M\u1EADt kh\u1EA9u hi\u1EC7n t\u1EA1i",
  formFieldLabel__emailAddress: "\u0110\u1ECBa ch\u1EC9 email",
  formFieldLabel__emailAddress_username: "\u0110\u1ECBa ch\u1EC9 email ho\u1EB7c t\xEAn ng\u01B0\u1EDDi d\xF9ng",
  formFieldLabel__emailAddresses: "C\xE1c \u0111\u1ECBa ch\u1EC9 email",
  formFieldLabel__firstName: "T\xEAn",
  formFieldLabel__lastName: "H\u1ECD",
  formFieldLabel__newPassword: "M\u1EADt kh\u1EA9u m\u1EDBi",
  formFieldLabel__organizationDomain: "Domain",
  formFieldLabel__organizationDomainDeletePending: "Delete pending invitations and suggestions",
  formFieldLabel__organizationDomainEmailAddress: "Verification email address",
  formFieldLabel__organizationDomainEmailAddressDescription: "Enter an email address under this domain to receive a code and verify this domain.",
  formFieldLabel__organizationName: "T\xEAn t\u1ED5 ch\u1EE9c",
  formFieldLabel__organizationSlug: "\u0110\u01B0\u1EDDng d\u1EABn r\xFAt g\u1ECDn",
  formFieldLabel__passkeyName: void 0,
  formFieldLabel__password: "M\u1EADt kh\u1EA9u",
  formFieldLabel__phoneNumber: "S\u1ED1 \u0111i\u1EC7n tho\u1EA1i",
  formFieldLabel__role: "Vai tr\xF2",
  formFieldLabel__signOutOfOtherSessions: "\u0110\u0103ng xu\u1EA5t kh\u1ECFi t\u1EA5t c\u1EA3 c\xE1c thi\u1EBFt b\u1ECB kh\xE1c",
  formFieldLabel__username: "T\xEAn ng\u01B0\u1EDDi d\xF9ng",
  impersonationFab: {
    action__signOut: "\u0110\u0103ng xu\u1EA5t",
    title: "\u0110\u0103ng nh\u1EADp v\u1EDBi t\u01B0 c\xE1ch {{identifier}}"
  },
  maintenanceMode: void 0,
  membershipRole__admin: "Qu\u1EA3n tr\u1ECB vi\xEAn",
  membershipRole__basicMember: "Th\xE0nh vi\xEAn",
  membershipRole__guestMember: "Kh\xE1ch",
  organizationList: {
    action__createOrganization: "Create organization",
    action__invitationAccept: "Join",
    action__suggestionsAccept: "Request to join",
    createOrganization: "Create Organization",
    invitationAcceptedLabel: "Joined",
    subtitle: "to continue to {{applicationName}}",
    suggestionsAcceptedLabel: "Pending approval",
    title: "Choose an account",
    titleWithoutPersonal: "Choose an organization"
  },
  organizationProfile: {
    badge__automaticInvitation: "Automatic invitations",
    badge__automaticSuggestion: "Automatic suggestions",
    badge__manualInvitation: "No automatic enrollment",
    badge__unverified: "Unverified",
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Add the domain to verify. Users with email addresses at this domain can join the organization automatically or request to join.",
      title: "Add domain"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Kh\xF4ng th\u1EC3 g\u1EEDi l\u1EDDi m\u1EDDi. S\u1EEDa c\xE1c l\u1ED7i sau v\xE0 th\u1EED l\u1EA1i:",
      formButtonPrimary__continue: "G\u1EEDi l\u1EDDi m\u1EDDi",
      selectDropdown__role: "Select role",
      subtitle: "M\u1EDDi th\xE0nh vi\xEAn m\u1EDBi v\xE0o t\u1ED5 ch\u1EE9c n\xE0y",
      successMessage: "M\u1EDDi \u0111\xE3 \u0111\u01B0\u1EE3c g\u1EEDi th\xE0nh c\xF4ng",
      title: "M\u1EDDi th\xE0nh vi\xEAn"
    },
    membersPage: {
      action__invite: "M\u1EDDi",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "G\u1EE1 b\u1ECF th\xE0nh vi\xEAn",
        tableHeader__actions: void 0,
        tableHeader__joined: "Tham gia",
        tableHeader__role: "Vai tr\xF2",
        tableHeader__user: "Ng\u01B0\u1EDDi d\xF9ng"
      },
      detailsTitle__emptyRow: "Kh\xF4ng c\xF3 th\xE0nh vi\xEAn \u0111\u1EC3 hi\u1EC3n th\u1ECB",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Invite users by connecting an email domain with your organization. Anyone who signs up with a matching email domain will be able to join the organization anytime.",
          headerTitle: "Automatic invitations",
          primaryButton: "Manage verified domains"
        },
        table__emptyRow: "No invitations to display"
      },
      invitedMembersTab: {
        menuAction__revoke: "Thu h\u1ED3i l\u1EDDi m\u1EDDi",
        tableHeader__invited: "\u0110\xE3 m\u1EDDi"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Users who sign up with a matching email domain, will be able to see a suggestion to request to join your organization.",
          headerTitle: "Automatic suggestions",
          primaryButton: "Manage verified domains"
        },
        menuAction__approve: "Approve",
        menuAction__reject: "Reject",
        tableHeader__requested: "Requested access",
        table__emptyRow: "No requests to display"
      },
      start: {
        headerTitle__invitations: "Invitations",
        headerTitle__members: "Members",
        headerTitle__requests: "Requests"
      }
    },
    navbar: {
      billing: void 0,
      description: "Manage your organization.",
      general: "General",
      members: "Members",
      title: "Organization"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Type "{{organizationName}}" below to continue.',
          messageLine1: "B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n x\xF3a t\u1ED5 ch\u1EE9c n\xE0y kh\xF4ng?",
          messageLine2: "H\xE0nh \u0111\u1ED9ng n\xE0y l\xE0 v\u0129nh vi\u1EC5n v\xE0 kh\xF4ng th\u1EC3 ho\xE0n t\xE1c.",
          successMessage: "B\u1EA1n \u0111\xE3 x\xF3a t\u1ED5 ch\u1EE9c.",
          title: "X\xF3a t\u1ED5 ch\u1EE9c"
        },
        leaveOrganization: {
          actionDescription: 'Type "{{organizationName}}" below to continue.',
          messageLine1: "B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n r\u1EDDi kh\u1ECFi t\u1ED5 ch\u1EE9c n\xE0y? B\u1EA1n s\u1EBD m\u1EA5t quy\u1EC1n truy c\u1EADp v\xE0o t\u1ED5 ch\u1EE9c n\xE0y v\xE0 c\xE1c \u1EE9ng d\u1EE5ng c\u1EE7a n\xF3.",
          messageLine2: "H\xE0nh \u0111\u1ED9ng n\xE0y l\xE0 v\u0129nh vi\u1EC5n v\xE0 kh\xF4ng th\u1EC3 ho\xE0n t\xE1c.",
          successMessage: "B\u1EA1n \u0111\xE3 r\u1EDDi kh\u1ECFi t\u1ED5 ch\u1EE9c.",
          title: "R\u1EDDi kh\u1ECFi t\u1ED5 ch\u1EE9c"
        },
        title: "Nguy hi\u1EC3m"
      },
      domainSection: {
        menuAction__manage: "Manage",
        menuAction__remove: "Delete",
        menuAction__verify: "Verify",
        primaryButton: "Add domain",
        subtitle: "Allow users to join the organization automatically or request to join based on a verified email domain.",
        title: "Verified domains"
      },
      successMessage: "T\u1ED5 ch\u1EE9c \u0111\xE3 \u0111\u01B0\u1EE3c c\u1EADp nh\u1EADt.",
      title: "H\u1ED3 s\u01A1 T\u1ED5 ch\u1EE9c"
    },
    removeDomainPage: {
      messageLine1: "The email domain {{domain}} will be removed.",
      messageLine2: "Users won\u2019t be able to join the organization automatically after this.",
      successMessage: "{{domain}} has been removed.",
      title: "Remove domain"
    },
    start: {
      headerTitle__general: "General",
      headerTitle__members: "Th\xE0nh vi\xEAn",
      profileSection: {
        primaryButton: void 0,
        title: "Organization Profile",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Removing this domain will affect invited users.",
        removeDomainActionLabel__remove: "Remove domain",
        removeDomainSubtitle: "Remove this domain from your verified domains",
        removeDomainTitle: "Remove domain"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Users are automatically invited to join the organization when they sign-up and can join anytime.",
        automaticInvitationOption__label: "Automatic invitations",
        automaticSuggestionOption__description: "Users receive a suggestion to request to join, but must be approved by an admin before they are able to join the organization.",
        automaticSuggestionOption__label: "Automatic suggestions",
        calloutInfoLabel: "Changing the enrollment mode will only affect new users.",
        calloutInvitationCountLabel: "Pending invitations sent to users: {{count}}",
        calloutSuggestionCountLabel: "Pending suggestions sent to users: {{count}}",
        manualInvitationOption__description: "Users can only be invited manually to the organization.",
        manualInvitationOption__label: "No automatic enrollment",
        subtitle: "Choose how users from this domain can join the organization."
      },
      start: {
        headerTitle__danger: "Danger",
        headerTitle__enrollment: "Enrollment options"
      },
      subtitle: "The domain {{domain}} is now verified. Continue by selecting enrollment mode.",
      title: "Update {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Enter the verification code sent to your email address",
      formTitle: "Verification code",
      resendButton: "Didn't receive a code? Resend",
      subtitle: "The domain {{domainName}} needs to be verified via email.",
      subtitleVerificationCodeScreen: "A verification code was sent to {{emailAddress}}. Enter the code to continue.",
      title: "Verify domain"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "T\u1EA1o T\u1ED5 ch\u1EE9c",
    action__invitationAccept: "Join",
    action__manageOrganization: "Qu\u1EA3n l\xFD T\u1ED5 ch\u1EE9c",
    action__suggestionsAccept: "Request to join",
    notSelected: "Ch\u01B0a ch\u1ECDn t\u1ED5 ch\u1EE9c",
    personalWorkspace: "Kh\xF4ng gian C\xE1 nh\xE2n",
    suggestionsAcceptedLabel: "Pending approval"
  },
  paginationButton__next: "Ti\u1EBFp",
  paginationButton__previous: "Tr\u01B0\u1EDBc",
  paginationRowText__displaying: "Hi\u1EC3n th\u1ECB",
  paginationRowText__of: "c\u1EE7a",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Add account",
      action__signOutAll: "Sign out of all accounts",
      subtitle: "Select the account with which you wish to continue.",
      title: "Choose an account"
    },
    alternativeMethods: {
      actionLink: "Nh\u1EADn tr\u1EE3 gi\xFAp",
      actionText: "Don\u2019t have any of these?",
      blockButton__backupCode: "S\u1EED d\u1EE5ng m\xE3 sao l\u01B0u",
      blockButton__emailCode: "G\u1EEDi m\xE3 qua email cho {{identifier}}",
      blockButton__emailLink: "G\u1EEDi li\xEAn k\u1EBFt qua email cho {{identifier}}",
      blockButton__passkey: void 0,
      blockButton__password: "\u0110\u0103ng nh\u1EADp b\u1EB1ng m\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n",
      blockButton__phoneCode: "G\u1EEDi m\xE3 SMS cho {{identifier}}",
      blockButton__totp: "S\u1EED d\u1EE5ng \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c c\u1EE7a b\u1EA1n",
      getHelp: {
        blockButton__emailSupport: "H\u1ED7 tr\u1EE3 qua email",
        content: "N\u1EBFu b\u1EA1n g\u1EB7p kh\xF3 kh\u0103n khi \u0111\u0103ng nh\u1EADp v\xE0o t\xE0i kho\u1EA3n c\u1EE7a m\xECnh, h\xE3y g\u1EEDi email cho ch\xFAng t\xF4i v\xE0 ch\xFAng t\xF4i s\u1EBD c\xF9ng b\u1EA1n kh\xF4i ph\u1EE5c quy\u1EC1n truy c\u1EADp trong th\u1EDDi gian ng\u1EAFn nh\u1EA5t.",
        title: "Nh\u1EADn tr\u1EE3 gi\xFAp"
      },
      subtitle: "Facing issues? You can use any of these methods to sign in.",
      title: "S\u1EED d\u1EE5ng ph\u01B0\u01A1ng ph\xE1p kh\xE1c"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c v\u1EDBi {{applicationName}}",
      title: "Nh\u1EADp m\xE3 sao l\u01B0u"
    },
    emailCode: {
      formTitle: "M\xE3 x\xE1c minh",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c v\u1EDBi {{applicationName}}",
      title: "Ki\u1EC3m tra email c\u1EE7a b\u1EA1n"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      expired: {
        subtitle: "Quay tr\u1EDF l\u1EA1i c\u1EEDa s\u1ED5 g\u1ED1c \u0111\u1EC3 ti\u1EBFp t\u1EE5c.",
        title: "Li\xEAn k\u1EBFt x\xE1c minh n\xE0y \u0111\xE3 h\u1EBFt h\u1EA1n"
      },
      failed: {
        subtitle: "Quay tr\u1EDF l\u1EA1i c\u1EEDa s\u1ED5 g\u1ED1c \u0111\u1EC3 ti\u1EBFp t\u1EE5c.",
        title: "Li\xEAn k\u1EBFt x\xE1c minh n\xE0y kh\xF4ng h\u1EE3p l\u1EC7"
      },
      formSubtitle: "S\u1EED d\u1EE5ng li\xEAn k\u1EBFt x\xE1c minh \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn email c\u1EE7a b\u1EA1n",
      formTitle: "Li\xEAn k\u1EBFt x\xE1c minh",
      loading: {
        subtitle: "B\u1EA1n s\u1EBD \u0111\u01B0\u1EE3c chuy\u1EC3n h\u01B0\u1EDBng s\u1EDBm",
        title: "\u0110ang \u0111\u0103ng nh\u1EADp..."
      },
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c li\xEAn k\u1EBFt? G\u1EEDi l\u1EA1i",
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c v\u1EDBi {{applicationName}}",
      title: "Ki\u1EC3m tra email c\u1EE7a b\u1EA1n",
      unusedTab: {
        title: "B\u1EA1n c\xF3 th\u1EC3 \u0111\xF3ng c\u1EEDa s\u1ED5 n\xE0y"
      },
      verified: {
        subtitle: "B\u1EA1n s\u1EBD \u0111\u01B0\u1EE3c chuy\u1EC3n h\u01B0\u1EDBng s\u1EDBm",
        title: "\u0110\u0103ng nh\u1EADp th\xE0nh c\xF4ng"
      },
      verifiedSwitchTab: {
        subtitle: "Quay tr\u1EDF l\u1EA1i c\u1EEDa s\u1ED5 g\u1ED1c \u0111\u1EC3 ti\u1EBFp t\u1EE5c",
        subtitleNewTab: "Quay tr\u1EDF l\u1EA1i c\u1EEDa s\u1ED5 m\u1EDBi m\u1EDF \u0111\u1EC3 ti\u1EBFp t\u1EE5c",
        titleNewTab: "\u0110\u0103ng nh\u1EADp tr\xEAn c\u1EEDa s\u1ED5 kh\xE1c"
      }
    },
    forgotPassword: {
      formTitle: "M\xE3 \u0111\u1EB7t l\u1EA1i m\u1EADt kh\u1EA9u",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "to reset your password",
      subtitle_email: "First, enter the code sent to your email ID",
      subtitle_phone: "First, enter the code sent to your phone",
      title: "Reset password"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "\u0110\u1EB7t l\u1EA1i m\u1EADt kh\u1EA9u",
      label__alternativeMethods: "Ho\u1EB7c, \u0111\u0103ng nh\u1EADp b\u1EB1ng ph\u01B0\u01A1ng ph\xE1p kh\xE1c.",
      title: "Qu\xEAn m\u1EADt kh\u1EA9u?"
    },
    noAvailableMethods: {
      message: "Kh\xF4ng th\u1EC3 ti\u1EBFp t\u1EE5c \u0111\u0103ng nh\u1EADp. Kh\xF4ng c\xF3 ph\u01B0\u01A1ng th\u1EE9c x\xE1c th\u1EF1c n\xE0o kh\u1EA3 d\u1EE5ng.",
      subtitle: "\u0110\xE3 x\u1EA3y ra l\u1ED7i",
      title: "Kh\xF4ng th\u1EC3 \u0111\u0103ng nh\u1EADp"
    },
    passkey: {
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: "S\u1EED d\u1EE5ng ph\u01B0\u01A1ng ph\xE1p kh\xE1c",
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c v\u1EDBi {{applicationName}}",
      title: "Nh\u1EADp m\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n"
    },
    passwordPwned: {
      title: void 0
    },
    phoneCode: {
      formTitle: "M\xE3 x\xE1c nh\u1EADn",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c v\u1EDBi {{applicationName}}",
      title: "Ki\u1EC3m tra \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n"
    },
    phoneCodeMfa: {
      formTitle: "M\xE3 x\xE1c nh\u1EADn",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: void 0,
      title: "Ki\u1EC3m tra \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n"
    },
    resetPassword: {
      formButtonPrimary: "\u0110\u1EB7t l\u1EA1i m\u1EADt kh\u1EA9u",
      requiredMessage: "For security reasons, it is required to reset your password.",
      successMessage: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n \u0111\xE3 \u0111\u01B0\u1EE3c thay \u0111\u1ED5i th\xE0nh c\xF4ng. \u0110ang \u0111\u0103ng nh\u1EADp, vui l\xF2ng ch\u1EDD m\u1ED9t ch\xFAt.",
      title: "\u0110\u1EB7t l\u1EA1i m\u1EADt kh\u1EA9u"
    },
    resetPasswordMfa: {
      detailsLabel: "Ch\xFAng t\xF4i c\u1EA7n x\xE1c minh danh t\xEDnh c\u1EE7a b\u1EA1n tr\u01B0\u1EDBc khi \u0111\u1EB7t l\u1EA1i m\u1EADt kh\u1EA9u."
    },
    start: {
      actionLink: "\u0110\u0103ng k\xFD",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "S\u1EED d\u1EE5ng email",
      actionLink__use_email_username: "S\u1EED d\u1EE5ng email ho\u1EB7c t\xEAn \u0111\u0103ng nh\u1EADp",
      actionLink__use_passkey: void 0,
      actionLink__use_phone: "S\u1EED d\u1EE5ng s\u1ED1 \u0111i\u1EC7n tho\u1EA1i",
      actionLink__use_username: "S\u1EED d\u1EE5ng t\xEAn \u0111\u0103ng nh\u1EADp",
      actionText: "Ch\u01B0a c\xF3 t\xE0i kho\u1EA3n?",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c v\u1EDBi {{applicationName}}",
      subtitleCombined: void 0,
      title: "\u0110\u0103ng nh\u1EADp",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "M\xE3 x\xE1c minh",
      subtitle: void 0,
      title: "X\xE1c minh hai b\u01B0\u1EDBc"
    }
  },
  signInEnterPasswordTitle: "Nh\u1EADp m\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "\u0110\u0103ng nh\u1EADp",
      actionText: "\u0110\xE3 c\xF3 t\xE0i kho\u1EA3n?",
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c v\u1EDBi {{applicationName}}",
      title: "\u0110i\u1EC1n c\xE1c tr\u01B0\u1EDDng b\u1ECB thi\u1EBFu"
    },
    emailCode: {
      formSubtitle: "Nh\u1EADp m\xE3 x\xE1c minh \u0111\xE3 \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn \u0111\u1ECBa ch\u1EC9 email c\u1EE7a b\u1EA1n",
      formTitle: "M\xE3 x\xE1c minh",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c v\u1EDBi {{applicationName}}",
      title: "X\xE1c minh email c\u1EE7a b\u1EA1n"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      formSubtitle: "S\u1EED d\u1EE5ng li\xEAn k\u1EBFt x\xE1c minh \u0111\xE3 \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn \u0111\u1ECBa ch\u1EC9 email c\u1EE7a b\u1EA1n",
      formTitle: "Li\xEAn k\u1EBFt x\xE1c minh",
      loading: {
        title: "\u0110ang \u0111\u0103ng k\xFD..."
      },
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c li\xEAn k\u1EBFt? G\u1EEDi l\u1EA1i",
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c v\u1EDBi {{applicationName}}",
      title: "X\xE1c minh email c\u1EE7a b\u1EA1n",
      verified: {
        title: "\u0110\u0103ng k\xFD th\xE0nh c\xF4ng"
      },
      verifiedSwitchTab: {
        subtitle: "Quay l\u1EA1i c\u1EEDa s\u1ED5 m\u1EDBi \u0111\u01B0\u1EE3c m\u1EDF \u0111\u1EC3 ti\u1EBFp t\u1EE5c",
        subtitleNewTab: "Quay l\u1EA1i c\u1EEDa s\u1ED5 tr\u01B0\u1EDBc \u0111\u1EC3 ti\u1EBFp t\u1EE5c",
        title: "X\xE1c minh email th\xE0nh c\xF4ng"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: void 0,
        label__onlyTermsOfService: void 0,
        label__termsOfServiceAndPrivacyPolicy: void 0
      },
      continue: {
        subtitle: void 0,
        title: void 0
      }
    },
    phoneCode: {
      formSubtitle: "Nh\u1EADp m\xE3 x\xE1c minh \u0111\xE3 \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn s\u1ED1 \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n",
      formTitle: "M\xE3 x\xE1c minh",
      resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c v\u1EDBi {{applicationName}}",
      title: "X\xE1c minh s\u1ED1 \u0111i\u1EC7n tho\u1EA1i c\u1EE7a b\u1EA1n"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "\u0110\u0103ng nh\u1EADp",
      actionLink__use_email: void 0,
      actionLink__use_phone: void 0,
      actionText: "\u0110\xE3 c\xF3 t\xE0i kho\u1EA3n?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c v\u1EDBi {{applicationName}}",
      subtitleCombined: "\u0111\u1EC3 ti\u1EBFp t\u1EE5c v\u1EDBi {{applicationName}}",
      title: "T\u1EA1o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n",
      titleCombined: "T\u1EA1o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n"
    }
  },
  socialButtonsBlockButton: "Ti\u1EBFp t\u1EE5c v\u1EDBi {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "\u0110\u0103ng k\xFD kh\xF4ng th\xE0nh c\xF4ng do kh\xF4ng v\u01B0\u1EE3t qua c\xE1c x\xE1c th\u1EF1c b\u1EA3o m\u1EADt. Vui l\xF2ng l\xE0m m\u1EDBi trang v\xE0 th\u1EED l\u1EA1i ho\u1EB7c li\xEAn h\u1EC7 h\u1ED7 tr\u1EE3 \u0111\u1EC3 \u0111\u01B0\u1EE3c tr\u1EE3 gi\xFAp th\xEAm.",
    captcha_unavailable: "Sign up unsuccessful due to failed bot validation. Please refresh the page to try again or reach out to support for more assistance.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: void 0,
    form_identifier_exists__phone_number: void 0,
    form_identifier_exists__username: void 0,
    form_identifier_not_found: "Kh\xF4ng t\xECm th\u1EA5y t\xE0i kho\u1EA3n v\u1EDBi th\xF4ng tin n\xE0y.",
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: "\u0110\u1ECBa ch\u1EC9 email ph\u1EA3i l\xE0 m\u1ED9t \u0111\u1ECBa ch\u1EC9 email h\u1EE3p l\u1EC7",
    form_param_format_invalid__phone_number: "Phone number must be in a valid international format",
    form_param_max_length_exceeded__first_name: "First name should not exceed 256 characters.",
    form_param_max_length_exceeded__last_name: "Last name should not exceed 256 characters.",
    form_param_max_length_exceeded__name: "Name should not exceed 256 characters.",
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: void 0,
    form_password_length_too_short: void 0,
    form_password_not_strong_enough: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n kh\xF4ng \u0111\u1EE7 m\u1EA1nh.",
    form_password_pwned: "M\u1EADt kh\u1EA9u n\xE0y \u0111\xE3 \u0111\u01B0\u1EE3c ph\xE1t hi\u1EC7n trong m\u1ED9t cu\u1ED9c t\u1EA5n c\xF4ng v\xE0 kh\xF4ng th\u1EC3 s\u1EED d\u1EE5ng, vui l\xF2ng th\u1EED m\u1EADt kh\u1EA9u kh\xE1c.",
    form_password_pwned__sign_in: void 0,
    form_password_size_in_bytes_exceeded: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n \u0111\xE3 v\u01B0\u1EE3t qu\xE1 s\u1ED1 byte t\u1ED1i \u0111a cho ph\xE9p, vui l\xF2ng r\xFAt ng\u1EAFn ho\u1EB7c lo\u1EA1i b\u1ECF m\u1ED9t s\u1ED1 k\xFD t\u1EF1 \u0111\u1EB7c bi\u1EC7t.",
    form_password_validation_failed: "M\u1EADt kh\u1EA9u kh\xF4ng \u0111\xFAng",
    form_username_invalid_character: void 0,
    form_username_invalid_length: void 0,
    identification_deletion_failed: "B\u1EA1n kh\xF4ng th\u1EC3 x\xF3a th\xF4ng tin nh\u1EADn d\u1EA1ng cu\u1ED1i c\xF9ng c\u1EE7a b\u1EA1n.",
    not_allowed_access: "\u0110\u1ECBa ch\u1EC9 email ho\u1EB7c s\u1ED1 \u0111i\u1EC7n tho\u1EA1i b\u1EA1n \u0111ang s\u1EED d\u1EE5ng cho \u0111\u0103ng k\xFD kh\xF4ng \u0111\u01B0\u1EE3c ph\xE9p. \u0110i\u1EC1u n\xE0y c\xF3 th\u1EC3 do vi\u1EC7c s\u1EED d\u1EE5ng '+', '=', '#' ho\u1EB7c '.' trong \u0111\u1ECBa ch\u1EC9 email c\u1EE7a b\u1EA1n, s\u1EED d\u1EE5ng m\u1ED9t mi\u1EC1n \u0111\u01B0\u1EE3c k\u1EBFt n\u1ED1i v\u1EDBi d\u1ECBch v\u1EE5 email t\u1EA1m th\u1EDDi ho\u1EB7c m\u1ED9t lo\u1EA1i lo\u1EA1i tr\u1EEB r\xF5 r\xE0ng.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: void 0,
    passkey_not_supported: void 0,
    passkey_pa_not_supported: void 0,
    passkey_registration_cancelled: void 0,
    passkey_retrieval_cancelled: void 0,
    passwordComplexity: {
      maximumLength: "\xEDt h\u01A1n {{length}} k\xFD t\u1EF1",
      minimumLength: "{{length}} ho\u1EB7c nhi\u1EC1u k\xFD t\u1EF1",
      requireLowercase: "m\u1ED9t ch\u1EEF c\xE1i vi\u1EBFt th\u01B0\u1EDDng",
      requireNumbers: "m\u1ED9t s\u1ED1",
      requireSpecialCharacter: "m\u1ED9t k\xFD t\u1EF1 \u0111\u1EB7c bi\u1EC7t",
      requireUppercase: "m\u1ED9t ch\u1EEF c\xE1i vi\u1EBFt hoa",
      sentencePrefix: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n ph\u1EA3i ch\u1EE9a"
    },
    phone_number_exists: "S\u1ED1 \u0111i\u1EC7n tho\u1EA1i n\xE0y \u0111\xE3 \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng. Vui l\xF2ng th\u1EED s\u1ED1 kh\xE1c.",
    session_exists: "B\u1EA1n \u0111\xE3 \u0111\u0103ng nh\u1EADp r\u1ED3i.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n \u0111\u1EE7 m\u1EA1nh, nh\u01B0ng c\xF3 th\u1EC3 m\u1EA1nh h\u01A1n. H\xE3y th\xEAm nhi\u1EC1u k\xFD t\u1EF1 h\u01A1n.",
      goodPassword: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n \u0111\xE1p \u1EE9ng t\u1EA5t c\u1EA3 c\xE1c y\xEAu c\u1EA7u c\u1EA7n thi\u1EBFt.",
      notEnough: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n kh\xF4ng \u0111\u1EE7 m\u1EA1nh.",
      suggestions: {
        allUppercase: "Vi\u1EBFt hoa m\u1ED9t s\u1ED1 k\xFD t\u1EF1, nh\u01B0ng kh\xF4ng ph\u1EA3i t\u1EA5t c\u1EA3.",
        anotherWord: "Th\xEAm nhi\u1EC1u t\u1EEB \xEDt ph\u1ED5 bi\u1EBFn h\u01A1n.",
        associatedYears: "Tr\xE1nh c\xE1c n\u0103m li\xEAn quan \u0111\u1EBFn b\u1EA1n.",
        capitalization: "Vi\u1EBFt hoa nhi\u1EC1u h\u01A1n ch\u1EC9 ch\u1EEF c\xE1i \u0111\u1EA7u ti\xEAn.",
        dates: "Tr\xE1nh s\u1EED d\u1EE5ng ng\xE0y th\xE1ng n\u0103m li\xEAn quan \u0111\u1EBFn b\u1EA1n.",
        l33t: "Tr\xE1nh vi\u1EC7c thay th\u1EBF ch\u1EEF c\xE1i d\u1EC5 \u0111o\xE1n b\u1EB1ng c\xE1c k\xFD t\u1EF1 nh\u01B0 '@' thay cho 'a'.",
        longerKeyboardPattern: "S\u1EED d\u1EE5ng c\xE1c m\u1EABu b\xE0n ph\xEDm d\xE0i h\u01A1n v\xE0 thay \u0111\u1ED5i h\u01B0\u1EDBng g\xF5 nhi\u1EC1u l\u1EA7n.",
        noNeed: "B\u1EA1n c\xF3 th\u1EC3 t\u1EA1o m\u1EADt kh\u1EA9u m\u1EA1nh m\xE0 kh\xF4ng c\u1EA7n s\u1EED d\u1EE5ng k\xFD t\u1EF1 \u0111\u1EB7c bi\u1EC7t, s\u1ED1 ho\u1EB7c ch\u1EEF c\xE1i vi\u1EBFt hoa.",
        pwned: "N\u1EBFu b\u1EA1n s\u1EED d\u1EE5ng m\u1EADt kh\u1EA9u n\xE0y \u1EDF nh\u1EEFng n\u01A1i kh\xE1c, b\u1EA1n n\xEAn thay \u0111\u1ED5i n\xF3.",
        recentYears: "Tr\xE1nh c\xE1c n\u0103m g\u1EA7n \u0111\xE2y.",
        repeated: "Tr\xE1nh vi\u1EC7c l\u1EB7p l\u1EA1i t\u1EEB v\xE0 k\xFD t\u1EF1.",
        reverseWords: "Tr\xE1nh vi\u1EC7c vi\u1EBFt ng\u01B0\u1EE3c c\xE1c t\u1EEB th\xF4ng th\u01B0\u1EDDng.",
        sequences: "Tr\xE1nh c\xE1c chu\u1ED7i k\xFD t\u1EF1 th\xF4ng th\u01B0\u1EDDng.",
        useWords: "S\u1EED d\u1EE5ng nhi\u1EC1u t\u1EEB, nh\u01B0ng tr\xE1nh c\xE1c c\u1EE5m t\u1EEB th\xF4ng th\u01B0\u1EDDng."
      },
      warnings: {
        common: "\u0110\xE2y l\xE0 m\u1ED9t m\u1EADt kh\u1EA9u ph\u1ED5 bi\u1EBFn.",
        commonNames: "C\xE1c t\xEAn ri\xEAng v\xE0 h\u1ECD ph\u1ED5 bi\u1EBFn d\u1EC5 \u0111o\xE1n.",
        dates: "Ng\xE0y th\xE1ng d\u1EC5 \u0111o\xE1n.",
        extendedRepeat: 'C\xE1c m\u1EABu k\xFD t\u1EF1 l\u1EB7p l\u1EA1i nh\u01B0 "abcabcabc" d\u1EC5 \u0111o\xE1n.',
        keyPattern: "M\u1EABu b\xE0n ph\xEDm ng\u1EAFn d\u1EC5 \u0111o\xE1n.",
        namesByThemselves: "C\xE1c t\xEAn ri\xEAng ho\u1EB7c h\u1ECD ri\xEAng d\u1EC5 \u0111o\xE1n.",
        pwned: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n \u0111\xE3 b\u1ECB r\xF2 r\u1EC9 qua m\u1ED9t cu\u1ED9c t\u1EA5n c\xF4ng d\u1EEF li\u1EC7u tr\xEAn Internet.",
        recentYears: "C\xE1c n\u0103m g\u1EA7n \u0111\xE2y d\u1EC5 \u0111o\xE1n.",
        sequences: 'C\xE1c chu\u1ED7i k\xFD t\u1EF1 ph\u1ED5 bi\u1EBFn nh\u01B0 "abc" d\u1EC5 \u0111o\xE1n.',
        similarToCommon: "\u0110\xE2y gi\u1ED1ng v\u1EDBi m\u1ED9t m\u1EADt kh\u1EA9u ph\u1ED5 bi\u1EBFn.",
        simpleRepeat: 'C\xE1c k\xFD t\u1EF1 l\u1EB7p l\u1EA1i nh\u01B0 "aaa" d\u1EC5 \u0111o\xE1n.',
        straightRow: "C\xE1c h\xE0ng ph\xEDm tr\xEAn b\xE0n ph\xEDm c\u1EE7a b\u1EA1n d\u1EC5 \u0111o\xE1n.",
        topHundred: "\u0110\xE2y l\xE0 m\u1ED9t m\u1EADt kh\u1EA9u \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng th\u01B0\u1EDDng xuy\xEAn.",
        topTen: "\u0110\xE2y l\xE0 m\u1ED9t m\u1EADt kh\u1EA9u \u0111\u01B0\u1EE3c s\u1EED d\u1EE5ng r\u1EA5t nhi\u1EC1u.",
        userInputs: "Kh\xF4ng n\xEAn c\xF3 b\u1EA5t k\u1EF3 d\u1EEF li\u1EC7u c\xE1 nh\xE2n ho\u1EB7c li\xEAn quan \u0111\u1EBFn trang web.",
        wordByItself: "M\u1ED9t t\u1EEB \u0111\u01A1n d\u1EC5 \u0111o\xE1n."
      }
    }
  },
  userButton: {
    action__addAccount: "Th\xEAm t\xE0i kho\u1EA3n",
    action__manageAccount: "Qu\u1EA3n l\xFD t\xE0i kho\u1EA3n",
    action__signOut: "\u0110\u0103ng xu\u1EA5t",
    action__signOutAll: "\u0110\u0103ng xu\u1EA5t kh\u1ECFi t\u1EA5t c\u1EA3 c\xE1c t\xE0i kho\u1EA3n"
  },
  userProfile: {
    backupCodePage: {
      actionLabel__copied: "\u0110\xE3 sao ch\xE9p!",
      actionLabel__copy: "Sao ch\xE9p t\u1EA5t c\u1EA3",
      actionLabel__download: "T\u1EA3i xu\u1ED1ng .txt",
      actionLabel__print: "In",
      infoText1: "C\xE1c m\xE3 sao l\u01B0u s\u1EBD \u0111\u01B0\u1EE3c k\xEDch ho\u1EA1t cho t\xE0i kho\u1EA3n n\xE0y.",
      infoText2: "Gi\u1EEF c\xE1c m\xE3 sao l\u01B0u b\xED m\u1EADt v\xE0 l\u01B0u ch\xFAng m\u1ED9t c\xE1ch an to\xE0n. B\u1EA1n c\xF3 th\u1EC3 t\u1EA1o l\u1EA1i c\xE1c m\xE3 sao l\u01B0u n\u1EBFu b\u1EA1n nghi ng\u1EDD ch\xFAng \u0111\xE3 b\u1ECB x\xE2m ph\u1EA1m.",
      subtitle__codelist: "L\u01B0u ch\xFAng m\u1ED9t c\xE1ch an to\xE0n v\xE0 gi\u1EEF ch\xFAng b\xED m\u1EADt.",
      successMessage: "M\xE3 sao l\u01B0u \u0111\xE3 \u0111\u01B0\u1EE3c k\xEDch ho\u1EA1t. B\u1EA1n c\xF3 th\u1EC3 s\u1EED d\u1EE5ng m\u1ED9t trong c\xE1c m\xE3 n\xE0y \u0111\u1EC3 \u0111\u0103ng nh\u1EADp v\xE0o t\xE0i kho\u1EA3n c\u1EE7a m\xECnh, n\u1EBFu b\u1EA1n m\u1EA5t quy\u1EC1n truy c\u1EADp v\xE0o thi\u1EBFt b\u1ECB x\xE1c th\u1EF1c c\u1EE7a m\xECnh. M\u1ED7i m\xE3 ch\u1EC9 c\xF3 th\u1EC3 s\u1EED d\u1EE5ng m\u1ED9t l\u1EA7n.",
      successSubtitle: "B\u1EA1n c\xF3 th\u1EC3 s\u1EED d\u1EE5ng m\u1ED9t trong c\xE1c m\xE3 n\xE0y \u0111\u1EC3 \u0111\u0103ng nh\u1EADp v\xE0o t\xE0i kho\u1EA3n c\u1EE7a m\xECnh, n\u1EBFu b\u1EA1n m\u1EA5t quy\u1EC1n truy c\u1EADp v\xE0o thi\u1EBFt b\u1ECB x\xE1c th\u1EF1c c\u1EE7a m\xECnh.",
      title: "Th\xEAm m\xE3 x\xE1c th\u1EF1c sao l\u01B0u",
      title__codelist: "C\xE1c m\xE3 sao l\u01B0u"
    },
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "Ch\u1ECDn m\u1ED9t nh\xE0 cung c\u1EA5p \u0111\u1EC3 k\u1EBFt n\u1ED1i t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
      formHint__noAccounts: "Kh\xF4ng c\xF3 nh\xE0 cung c\u1EA5p t\xE0i kho\u1EA3n b\xEAn ngo\xE0i kh\u1EA3 d\u1EE5ng.",
      removeResource: {
        messageLine1: "{{identifier}} s\u1EBD b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n n\xE0y.",
        messageLine2: "B\u1EA1n s\u1EBD kh\xF4ng th\u1EC3 s\u1EED d\u1EE5ng t\xE0i kho\u1EA3n li\xEAn k\u1EBFt n\xE0y v\xE0 b\u1EA5t k\u1EF3 t\xEDnh n\u0103ng ph\u1EE5 thu\u1ED9c n\xE0o s\u1EBD kh\xF4ng c\xF2n ho\u1EA1t \u0111\u1ED9ng.",
        successMessage: "{{connectedAccount}} \u0111\xE3 \u0111\u01B0\u1EE3c x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
        title: "X\xF3a t\xE0i kho\u1EA3n li\xEAn k\u1EBFt"
      },
      socialButtonsBlockButton: "K\u1EBFt n\u1ED1i t\xE0i kho\u1EA3n {{provider|titleize}}",
      successMessage: "Nh\xE0 cung c\u1EA5p \u0111\xE3 \u0111\u01B0\u1EE3c th\xEAm v\xE0o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n",
      title: "Th\xEAm t\xE0i kho\u1EA3n li\xEAn k\u1EBFt"
    },
    deletePage: {
      actionDescription: 'Type "Delete account" below to continue.',
      confirm: "X\xF3a t\xE0i kho\u1EA3n",
      messageLine1: "B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n x\xF3a t\xE0i kho\u1EA3n c\u1EE7a m\xECnh kh\xF4ng?",
      messageLine2: "H\xE0nh \u0111\u1ED9ng n\xE0y l\xE0 v\u0129nh vi\u1EC5n v\xE0 kh\xF4ng th\u1EC3 ho\xE0n t\xE1c.",
      title: "X\xF3a t\xE0i kho\u1EA3n"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "M\u1ED9t email ch\u1EE9a m\xE3 x\xE1c minh s\u1EBD \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn \u0111\u1ECBa ch\u1EC9 email n\xE0y.",
        formSubtitle: "Nh\u1EADp m\xE3 x\xE1c minh \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn {{identifier}}",
        formTitle: "M\xE3 x\xE1c minh",
        resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c m\xE3? G\u1EEDi l\u1EA1i",
        successMessage: "Email {{identifier}} \u0111\xE3 \u0111\u01B0\u1EE3c th\xEAm v\xE0o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n."
      },
      emailLink: {
        formHint: "M\u1ED9t email ch\u1EE9a li\xEAn k\u1EBFt x\xE1c minh s\u1EBD \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn \u0111\u1ECBa ch\u1EC9 email n\xE0y.",
        formSubtitle: "Nh\u1EA5p v\xE0o li\xEAn k\u1EBFt x\xE1c minh trong email \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn {{identifier}}",
        formTitle: "Li\xEAn k\u1EBFt x\xE1c minh",
        resendButton: "Kh\xF4ng nh\u1EADn \u0111\u01B0\u1EE3c li\xEAn k\u1EBFt? G\u1EEDi l\u1EA1i",
        successMessage: "Email {{identifier}} \u0111\xE3 \u0111\u01B0\u1EE3c th\xEAm v\xE0o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} s\u1EBD b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n n\xE0y.",
        messageLine2: "B\u1EA1n s\u1EBD kh\xF4ng th\u1EC3 \u0111\u0103ng nh\u1EADp b\u1EB1ng \u0111\u1ECBa ch\u1EC9 email n\xE0y n\u1EEFa.",
        successMessage: "{{emailAddress}} \u0111\xE3 \u0111\u01B0\u1EE3c x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
        title: "X\xF3a \u0111\u1ECBa ch\u1EC9 email"
      },
      title: "Th\xEAm \u0111\u1ECBa ch\u1EC9 email",
      verifyTitle: "Verify email address"
    },
    formButtonPrimary__add: "Add",
    formButtonPrimary__continue: "Ti\u1EBFp t\u1EE5c",
    formButtonPrimary__finish: "Ho\xE0n th\xE0nh",
    formButtonPrimary__remove: "Remove",
    formButtonPrimary__save: "Save",
    formButtonReset: "H\u1EE7y",
    mfaPage: {
      formHint: "Ch\u1ECDn m\u1ED9t ph\u01B0\u01A1ng ph\xE1p \u0111\u1EC3 th\xEAm.",
      title: "Th\xEAm x\xE1c minh hai b\u01B0\u1EDBc"
    },
    mfaPhoneCodePage: {
      backButton: "Use existing number",
      primaryButton__addPhoneNumber: "Th\xEAm s\u1ED1 \u0111i\u1EC7n tho\u1EA1i",
      removeResource: {
        messageLine1: "{{identifier}} s\u1EBD kh\xF4ng c\xF2n nh\u1EADn \u0111\u01B0\u1EE3c m\xE3 x\xE1c th\u1EF1c khi \u0111\u0103ng nh\u1EADp.",
        messageLine2: "T\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n c\xF3 th\u1EC3 kh\xF4ng an to\xE0n. B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n ti\u1EBFp t\u1EE5c kh\xF4ng?",
        successMessage: "X\xE1c th\u1EF1c hai b\u01B0\u1EDBc b\u1EB1ng m\xE3 SMS \u0111\xE3 \u0111\u01B0\u1EE3c g\u1EE1 b\u1ECF cho {{mfaPhoneCode}}",
        title: "G\u1EE1 b\u1ECF x\xE1c th\u1EF1c hai b\u01B0\u1EDBc"
      },
      subtitle__availablePhoneNumbers: "Ch\u1ECDn m\u1ED9t s\u1ED1 \u0111i\u1EC7n tho\u1EA1i \u0111\u1EC3 \u0111\u0103ng k\xFD x\xE1c th\u1EF1c hai b\u01B0\u1EDBc b\u1EB1ng m\xE3 SMS.",
      subtitle__unavailablePhoneNumbers: "Kh\xF4ng c\xF3 s\u1ED1 \u0111i\u1EC7n tho\u1EA1i n\xE0o kh\u1EA3 d\u1EE5ng \u0111\u1EC3 \u0111\u0103ng k\xFD x\xE1c th\u1EF1c hai b\u01B0\u1EDBc b\u1EB1ng m\xE3 SMS.",
      successMessage1: "When signing in, you will need to enter a verification code sent to this phone number as an additional step.",
      successMessage2: "Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.",
      successTitle: "SMS code verification enabled",
      title: "Th\xEAm m\xE3 x\xE1c th\u1EF1c SMS"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Qu\xE9t m\xE3 QR thay v\xEC \u0111\xF3",
        buttonUnableToScan__nonPrimary: "Kh\xF4ng th\u1EC3 qu\xE9t m\xE3 QR?",
        infoText__ableToScan: "Thi\u1EBFt l\u1EADp m\u1ED9t ph\u01B0\u01A1ng th\u1EE9c \u0111\u0103ng nh\u1EADp m\u1EDBi trong \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c c\u1EE7a b\u1EA1n v\xE0 qu\xE9t m\xE3 QR d\u01B0\u1EDBi \u0111\xE2y \u0111\u1EC3 li\xEAn k\u1EBFt n\xF3 v\u1EDBi t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
        infoText__unableToScan: "Thi\u1EBFt l\u1EADp m\u1ED9t ph\u01B0\u01A1ng th\u1EE9c \u0111\u0103ng nh\u1EADp m\u1EDBi trong \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c v\xE0 nh\u1EADp Kh\xF3a \u0111\u01B0\u1EE3c cung c\u1EA5p b\xEAn d\u01B0\u1EDBi.",
        inputLabel__unableToScan1: "\u0110\u1EA3m b\u1EA3o \u0111\xE3 k\xEDch ho\u1EA1t m\u1EADt kh\u1EA9u d\u1EF1a tr\xEAn th\u1EDDi gian ho\u1EB7c m\u1EADt kh\u1EA9u m\u1ED9t l\u1EA7n, sau \u0111\xF3 ho\xE0n th\xE0nh vi\u1EC7c li\xEAn k\u1EBFt t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
        inputLabel__unableToScan2: "Ho\u1EB7c n\u1EBFu \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c c\u1EE7a b\u1EA1n h\u1ED7 tr\u1EE3 TOTP URIs, b\u1EA1n c\u0169ng c\xF3 th\u1EC3 sao ch\xE9p to\xE0n b\u1ED9 URI."
      },
      removeResource: {
        messageLine1: "M\xE3 x\xE1c th\u1EF1c t\u1EEB \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c n\xE0y s\u1EBD kh\xF4ng c\xF2n \u0111\u01B0\u1EE3c y\xEAu c\u1EA7u khi \u0111\u0103ng nh\u1EADp.",
        messageLine2: "T\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n c\xF3 th\u1EC3 kh\xF4ng an to\xE0n. B\u1EA1n c\xF3 ch\u1EAFc ch\u1EAFn mu\u1ED1n ti\u1EBFp t\u1EE5c kh\xF4ng?",
        successMessage: "X\xE1c th\u1EF1c hai b\u01B0\u1EDBc qua \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c \u0111\xE3 \u0111\u01B0\u1EE3c g\u1EE1 b\u1ECF.",
        title: "G\u1EE1 b\u1ECF x\xE1c th\u1EF1c hai b\u01B0\u1EDBc"
      },
      successMessage: "X\xE1c th\u1EF1c hai b\u01B0\u1EDBc \u0111\xE3 \u0111\u01B0\u1EE3c k\xEDch ho\u1EA1t. Khi \u0111\u0103ng nh\u1EADp, b\u1EA1n s\u1EBD c\u1EA7n nh\u1EADp m\xE3 x\xE1c th\u1EF1c t\u1EEB \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c n\xE0y nh\u01B0 m\u1ED9t b\u01B0\u1EDBc b\u1ED5 sung.",
      title: "Th\xEAm \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c",
      verifySubtitle: "Nh\u1EADp m\xE3 x\xE1c th\u1EF1c \u0111\u01B0\u1EE3c t\u1EA1o b\u1EDFi \u1EE9ng d\u1EE5ng x\xE1c th\u1EF1c c\u1EE7a b\u1EA1n",
      verifyTitle: "M\xE3 x\xE1c th\u1EF1c"
    },
    mobileButton__menu: "Menu",
    navbar: {
      account: "Profile",
      billing: void 0,
      description: "Manage your account info.",
      security: "Security",
      title: "Account"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: void 0,
        title: void 0
      },
      subtitle__rename: void 0,
      title__rename: void 0
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "It is recommended to sign out of all other devices which may have used your old password.",
      readonly: "Hi\u1EC7n t\u1EA1i b\u1EA1n kh\xF4ng th\u1EC3 ch\u1EC9nh s\u1EEDa m\u1EADt kh\u1EA9u v\xEC b\u1EA1n ch\u1EC9 c\xF3 th\u1EC3 \u0111\u0103ng nh\u1EADp qua k\u1EBFt n\u1ED1i doanh nghi\u1EC7p.",
      successMessage__set: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n \u0111\xE3 \u0111\u01B0\u1EE3c thi\u1EBFt l\u1EADp.",
      successMessage__signOutOfOtherSessions: "T\u1EA5t c\u1EA3 c\xE1c thi\u1EBFt b\u1ECB kh\xE1c \u0111\xE3 \u0111\u01B0\u1EE3c \u0111\u0103ng xu\u1EA5t.",
      successMessage__update: "M\u1EADt kh\u1EA9u c\u1EE7a b\u1EA1n \u0111\xE3 \u0111\u01B0\u1EE3c c\u1EADp nh\u1EADt.",
      title__set: "Thi\u1EBFt l\u1EADp m\u1EADt kh\u1EA9u",
      title__update: "Thay \u0111\u1ED5i m\u1EADt kh\u1EA9u"
    },
    phoneNumberPage: {
      infoText: "M\u1ED9t tin nh\u1EAFn ch\u1EE9a li\xEAn k\u1EBFt x\xE1c minh s\u1EBD \u0111\u01B0\u1EE3c g\u1EEDi \u0111\u1EBFn s\u1ED1 \u0111i\u1EC7n tho\u1EA1i n\xE0y.",
      removeResource: {
        messageLine1: "{{identifier}} s\u1EBD b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n n\xE0y.",
        messageLine2: "B\u1EA1n s\u1EBD kh\xF4ng th\u1EC3 \u0111\u0103ng nh\u1EADp b\u1EB1ng s\u1ED1 \u0111i\u1EC7n tho\u1EA1i n\xE0y n\u1EEFa.",
        successMessage: "{{phoneNumber}} \u0111\xE3 \u0111\u01B0\u1EE3c x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
        title: "X\xF3a s\u1ED1 \u0111i\u1EC7n tho\u1EA1i"
      },
      successMessage: "{{identifier}} \u0111\xE3 \u0111\u01B0\u1EE3c th\xEAm v\xE0o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
      title: "Th\xEAm s\u1ED1 \u0111i\u1EC7n tho\u1EA1i",
      verifySubtitle: "Enter the verification code sent to {{identifier}}",
      verifyTitle: "Verify phone number"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "T\u1EA3i l\xEAn \u1EA3nh JPG, PNG, GIF, ho\u1EB7c WEBP c\xF3 dung l\u01B0\u1EE3ng nh\u1ECF h\u01A1n 10 MB",
      imageFormDestructiveActionSubtitle: "X\xF3a \u1EA3nh",
      imageFormSubtitle: "T\u1EA3i \u1EA3nh l\xEAn",
      imageFormTitle: "H\xECnh \u1EA3nh h\u1ED3 s\u01A1",
      readonly: "Th\xF4ng tin h\u1ED3 s\u01A1 c\u1EE7a b\u1EA1n \u0111\xE3 \u0111\u01B0\u1EE3c cung c\u1EA5p b\u1EDFi k\u1EBFt n\u1ED1i doanh nghi\u1EC7p v\xE0 kh\xF4ng th\u1EC3 ch\u1EC9nh s\u1EEDa.",
      successMessage: "H\u1ED3 s\u01A1 c\u1EE7a b\u1EA1n \u0111\xE3 \u0111\u01B0\u1EE3c c\u1EADp nh\u1EADt.",
      title: "C\u1EADp nh\u1EADt h\u1ED3 s\u01A1"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "\u0110\u0103ng xu\u1EA5t kh\u1ECFi thi\u1EBFt b\u1ECB",
        title: "Thi\u1EBFt b\u1ECB ho\u1EA1t \u0111\u1ED9ng"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Th\u1EED l\u1EA1i",
        actionLabel__reauthorize: "X\xE1c th\u1EF1c ngay",
        destructiveActionTitle: "X\xF3a",
        primaryButton: "K\u1EBFt n\u1ED1i t\xE0i kho\u1EA3n",
        subtitle__disconnected: void 0,
        subtitle__reauthorize: "The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues",
        title: "T\xE0i kho\u1EA3n \u0111\xE3 k\u1EBFt n\u1ED1i"
      },
      dangerSection: {
        deleteAccountButton: "X\xF3a T\xE0i kho\u1EA3n",
        title: "Nguy hi\u1EC3m"
      },
      emailAddressesSection: {
        destructiveAction: "X\xF3a \u0111\u1ECBa ch\u1EC9 email",
        detailsAction__nonPrimary: "\u0110\u1EB7t l\xE0m ch\xEDnh",
        detailsAction__primary: "Ho\xE0n t\u1EA5t x\xE1c minh",
        detailsAction__unverified: "Ho\xE0n t\u1EA5t x\xE1c minh",
        primaryButton: "Th\xEAm \u0111\u1ECBa ch\u1EC9 email",
        title: "\u0110\u1ECBa ch\u1EC9 email"
      },
      enterpriseAccountsSection: {
        title: "T\xE0i kho\u1EA3n doanh nghi\u1EC7p"
      },
      headerTitle__account: "T\xE0i kho\u1EA3n",
      headerTitle__security: "B\u1EA3o m\u1EADt",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "T\u1EA1o l\u1EA1i m\xE3",
          headerTitle: "M\xE3 sao l\u01B0u",
          subtitle__regenerate: "Nh\u1EADn m\u1ED9t b\u1ED9 m\xE3 sao l\u01B0u an to\xE0n m\u1EDBi. C\xE1c m\xE3 sao l\u01B0u tr\u01B0\u1EDBc \u0111\xF3 s\u1EBD b\u1ECB x\xF3a v\xE0 kh\xF4ng th\u1EC3 s\u1EED d\u1EE5ng \u0111\u01B0\u1EE3c.",
          title__regenerate: "T\u1EA1o l\u1EA1i m\xE3 sao l\u01B0u"
        },
        phoneCode: {
          actionLabel__setDefault: "\u0110\u1EB7t l\xE0m m\u1EB7c \u0111\u1ECBnh",
          destructiveActionLabel: "X\xF3a s\u1ED1 \u0111i\u1EC7n tho\u1EA1i"
        },
        primaryButton: "Th\xEAm x\xE1c th\u1EF1c hai b\u01B0\u1EDBc",
        title: "X\xE1c th\u1EF1c hai b\u01B0\u1EDBc",
        totp: {
          destructiveActionTitle: "X\xF3a",
          headerTitle: "\u1EE8ng d\u1EE5ng x\xE1c th\u1EF1c"
        }
      },
      passkeysSection: {
        menuAction__destructive: void 0,
        menuAction__rename: void 0,
        primaryButton: void 0,
        title: void 0
      },
      passwordSection: {
        primaryButton__setPassword: "\u0110\u1EB7t m\u1EADt kh\u1EA9u",
        primaryButton__updatePassword: "Thay \u0111\u1ED5i m\u1EADt kh\u1EA9u",
        title: "M\u1EADt kh\u1EA9u"
      },
      phoneNumbersSection: {
        destructiveAction: "X\xF3a s\u1ED1 \u0111i\u1EC7n tho\u1EA1i",
        detailsAction__nonPrimary: "\u0110\u1EB7t l\xE0m ch\xEDnh",
        detailsAction__primary: "Ho\xE0n t\u1EA5t x\xE1c minh",
        detailsAction__unverified: "Ho\xE0n t\u1EA5t x\xE1c minh",
        primaryButton: "Th\xEAm s\u1ED1 \u0111i\u1EC7n tho\u1EA1i",
        title: "S\u1ED1 \u0111i\u1EC7n tho\u1EA1i"
      },
      profileSection: {
        primaryButton: void 0,
        title: "H\u1ED3 s\u01A1"
      },
      usernameSection: {
        primaryButton__setUsername: "\u0110\u1EB7t t\xEAn ng\u01B0\u1EDDi d\xF9ng",
        primaryButton__updateUsername: "Thay \u0111\u1ED5i t\xEAn ng\u01B0\u1EDDi d\xF9ng",
        title: "T\xEAn ng\u01B0\u1EDDi d\xF9ng"
      },
      web3WalletsSection: {
        destructiveAction: "X\xF3a v\xED",
        detailsAction__nonPrimary: void 0,
        primaryButton: "V\xED Web3",
        title: "V\xED Web3"
      }
    },
    usernamePage: {
      successMessage: "T\xEAn ng\u01B0\u1EDDi d\xF9ng c\u1EE7a b\u1EA1n \u0111\xE3 \u0111\u01B0\u1EE3c c\u1EADp nh\u1EADt.",
      title__set: "C\u1EADp nh\u1EADt t\xEAn ng\u01B0\u1EDDi d\xF9ng",
      title__update: "C\u1EADp nh\u1EADt t\xEAn ng\u01B0\u1EDDi d\xF9ng"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} s\u1EBD b\u1ECB x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n n\xE0y.",
        messageLine2: "B\u1EA1n s\u1EBD kh\xF4ng th\u1EC3 \u0111\u0103ng nh\u1EADp b\u1EB1ng v\xED web3 n\xE0y n\u1EEFa.",
        successMessage: "{{web3Wallet}} \u0111\xE3 \u0111\u01B0\u1EE3c x\xF3a kh\u1ECFi t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
        title: "X\xF3a v\xED web3"
      },
      subtitle__availableWallets: "Ch\u1ECDn m\u1ED9t v\xED web3 \u0111\u1EC3 k\u1EBFt n\u1ED1i v\u1EDBi t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
      subtitle__unavailableWallets: "Kh\xF4ng c\xF3 v\xED web3 kh\u1EA3 d\u1EE5ng.",
      successMessage: "V\xED \u0111\xE3 \u0111\u01B0\u1EE3c th\xEAm v\xE0o t\xE0i kho\u1EA3n c\u1EE7a b\u1EA1n.",
      title: "Th\xEAm v\xED web3",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  viVN
};
//# sourceMappingURL=vi-VN.mjs.map