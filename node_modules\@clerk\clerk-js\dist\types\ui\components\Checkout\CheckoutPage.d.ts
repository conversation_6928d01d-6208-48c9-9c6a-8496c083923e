import type { ClerkAPIError, CommerceCheckoutResource } from '@clerk/types';
type CheckoutStatus = 'pending' | 'ready' | 'completed' | 'missing_payer_email' | 'invalid_plan_change' | 'error';
export declare const useCheckoutContextRoot: () => {
    checkout: CommerceCheckoutResource | undefined;
    isLoading: boolean;
    updateCheckout: (checkout: CommerceCheckoutResource) => void;
    errors: ClerkAPIError[];
    startCheckout: () => void;
    status: CheckoutStatus;
};
declare const Root: ({ children }: {
    children: React.ReactNode;
}) => import("@emotion/react/jsx-runtime").JSX.Element;
declare const Stage: ({ children, name }: {
    children: React.ReactNode;
    name: CheckoutStatus;
}) => import("react").ReactNode;
export { Root, Stage };
