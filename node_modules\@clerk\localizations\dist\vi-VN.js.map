{"version": 3, "sources": ["../src/vi-VN.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const viVN: LocalizationResource = {\n  locale: 'vi-VN',\n  backButton: 'Quay lại',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Mặc định',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Thiết bị nhân danh khác',\n  badge__primary: 'Chính',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Yêu cầu hành động',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Thiết bị này',\n  badge__unverified: 'Chưa xác minh',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Thiết bị người dùng',\n  badge__you: 'Bạn',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Tạo tổ chức',\n    invitePage: {\n      formButtonReset: 'Bỏ qua',\n    },\n    title: 'Tạo Tổ chức',\n  },\n  dates: {\n    lastDay: \"Hôm qua lúc {{ date | timeString('vi-VN') }}\",\n    next6Days: \"Vào {{ date | weekday('vi-VN','long') }} tới lúc {{ date | timeString('vi-VN') }}\",\n    nextDay: \"Ngày mai lúc {{ date | timeString('vi-VN') }}\",\n    numeric: \"{{ date | numeric('vi-VN') }}\",\n    previous6Days: \"Vào {{ date | weekday('vi-VN','long') }} trước đó lúc {{ date | timeString('vi-VN') }}\",\n    sameDay: \"Hôm nay lúc {{ date | timeString('vi-VN') }}\",\n  },\n  dividerText: 'hoặc',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Sử dụng phương pháp khác',\n  footerPageLink__help: 'Trợ giúp',\n  footerPageLink__privacy: 'Quyền riêng tư',\n  footerPageLink__terms: 'Điều khoản',\n  formButtonPrimary: 'Tiếp tục',\n  formButtonPrimary__verify: 'Verify',\n  formFieldAction__forgotPassword: 'Quên mật khẩu?',\n  formFieldError__matchingPasswords: 'Mật khẩu khớp.',\n  formFieldError__notMatchingPasswords: 'Mật khẩu không khớp.',\n  formFieldError__verificationLinkExpired: 'The verification link expired. Please request a new link.',\n  formFieldHintText__optional: 'Tùy chọn',\n  formFieldHintText__slug: 'A slug is a human-readable ID that must be unique. It’s often used in URLs.',\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Delete account',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses:\n    'Nhập hoặc dán một hoặc nhiều địa chỉ email, cách nhau bằng khoảng trắng hoặc dấu phẩy',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: undefined,\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__automaticInvitations: 'Enable automatic invitations for this domain',\n  formFieldLabel__backupCode: 'Mã sao lưu',\n  formFieldLabel__confirmDeletion: 'Confirmation',\n  formFieldLabel__confirmPassword: 'Xác nhận mật khẩu',\n  formFieldLabel__currentPassword: 'Mật khẩu hiện tại',\n  formFieldLabel__emailAddress: 'Địa chỉ email',\n  formFieldLabel__emailAddress_username: 'Địa chỉ email hoặc tên người dùng',\n  formFieldLabel__emailAddresses: 'Các địa chỉ email',\n  formFieldLabel__firstName: 'Tên',\n  formFieldLabel__lastName: 'Họ',\n  formFieldLabel__newPassword: 'Mật khẩu mới',\n  formFieldLabel__organizationDomain: 'Domain',\n  formFieldLabel__organizationDomainDeletePending: 'Delete pending invitations and suggestions',\n  formFieldLabel__organizationDomainEmailAddress: 'Verification email address',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Enter an email address under this domain to receive a code and verify this domain.',\n  formFieldLabel__organizationName: 'Tên tổ chức',\n  formFieldLabel__organizationSlug: 'Đường dẫn rút gọn',\n  formFieldLabel__passkeyName: undefined,\n  formFieldLabel__password: 'Mật khẩu',\n  formFieldLabel__phoneNumber: 'Số điện thoại',\n  formFieldLabel__role: 'Vai trò',\n  formFieldLabel__signOutOfOtherSessions: 'Đăng xuất khỏi tất cả các thiết bị khác',\n  formFieldLabel__username: 'Tên người dùng',\n  impersonationFab: {\n    action__signOut: 'Đăng xuất',\n    title: 'Đăng nhập với tư cách {{identifier}}',\n  },\n  maintenanceMode: undefined,\n  membershipRole__admin: 'Quản trị viên',\n  membershipRole__basicMember: 'Thành viên',\n  membershipRole__guestMember: 'Khách',\n  organizationList: {\n    action__createOrganization: 'Create organization',\n    action__invitationAccept: 'Join',\n    action__suggestionsAccept: 'Request to join',\n    createOrganization: 'Create Organization',\n    invitationAcceptedLabel: 'Joined',\n    subtitle: 'to continue to {{applicationName}}',\n    suggestionsAcceptedLabel: 'Pending approval',\n    title: 'Choose an account',\n    titleWithoutPersonal: 'Choose an organization',\n  },\n  organizationProfile: {\n    badge__automaticInvitation: 'Automatic invitations',\n    badge__automaticSuggestion: 'Automatic suggestions',\n    badge__manualInvitation: 'No automatic enrollment',\n    badge__unverified: 'Unverified',\n    billingPage: {\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Add the domain to verify. Users with email addresses at this domain can join the organization automatically or request to join.',\n      title: 'Add domain',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed: 'Không thể gửi lời mời. Sửa các lỗi sau và thử lại:',\n      formButtonPrimary__continue: 'Gửi lời mời',\n      selectDropdown__role: 'Select role',\n      subtitle: 'Mời thành viên mới vào tổ chức này',\n      successMessage: 'Mời đã được gửi thành công',\n      title: 'Mời thành viên',\n    },\n    membersPage: {\n      action__invite: 'Mời',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Gỡ bỏ thành viên',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'Tham gia',\n        tableHeader__role: 'Vai trò',\n        tableHeader__user: 'Người dùng',\n      },\n      detailsTitle__emptyRow: 'Không có thành viên để hiển thị',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Invite users by connecting an email domain with your organization. Anyone who signs up with a matching email domain will be able to join the organization anytime.',\n          headerTitle: 'Automatic invitations',\n          primaryButton: 'Manage verified domains',\n        },\n        table__emptyRow: 'No invitations to display',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Thu hồi lời mời',\n        tableHeader__invited: 'Đã mời',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Users who sign up with a matching email domain, will be able to see a suggestion to request to join your organization.',\n          headerTitle: 'Automatic suggestions',\n          primaryButton: 'Manage verified domains',\n        },\n        menuAction__approve: 'Approve',\n        menuAction__reject: 'Reject',\n        tableHeader__requested: 'Requested access',\n        table__emptyRow: 'No requests to display',\n      },\n      start: {\n        headerTitle__invitations: 'Invitations',\n        headerTitle__members: 'Members',\n        headerTitle__requests: 'Requests',\n      },\n    },\n    navbar: {\n      billing: undefined,\n      description: 'Manage your organization.',\n      general: 'General',\n      members: 'Members',\n      title: 'Organization',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Type \"{{organizationName}}\" below to continue.',\n          messageLine1: 'Bạn có chắc chắn muốn xóa tổ chức này không?',\n          messageLine2: 'Hành động này là vĩnh viễn và không thể hoàn tác.',\n          successMessage: 'Bạn đã xóa tổ chức.',\n          title: 'Xóa tổ chức',\n        },\n        leaveOrganization: {\n          actionDescription: 'Type \"{{organizationName}}\" below to continue.',\n          messageLine1:\n            'Bạn có chắc chắn muốn rời khỏi tổ chức này? Bạn sẽ mất quyền truy cập vào tổ chức này và các ứng dụng của nó.',\n          messageLine2: 'Hành động này là vĩnh viễn và không thể hoàn tác.',\n          successMessage: 'Bạn đã rời khỏi tổ chức.',\n          title: 'Rời khỏi tổ chức',\n        },\n        title: 'Nguy hiểm',\n      },\n      domainSection: {\n        menuAction__manage: 'Manage',\n        menuAction__remove: 'Delete',\n        menuAction__verify: 'Verify',\n        primaryButton: 'Add domain',\n        subtitle:\n          'Allow users to join the organization automatically or request to join based on a verified email domain.',\n        title: 'Verified domains',\n      },\n      successMessage: 'Tổ chức đã được cập nhật.',\n      title: 'Hồ sơ Tổ chức',\n    },\n    removeDomainPage: {\n      messageLine1: 'The email domain {{domain}} will be removed.',\n      messageLine2: 'Users won’t be able to join the organization automatically after this.',\n      successMessage: '{{domain}} has been removed.',\n      title: 'Remove domain',\n    },\n    start: {\n      headerTitle__general: 'General',\n      headerTitle__members: 'Thành viên',\n      profileSection: {\n        primaryButton: undefined,\n        title: 'Organization Profile',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Removing this domain will affect invited users.',\n        removeDomainActionLabel__remove: 'Remove domain',\n        removeDomainSubtitle: 'Remove this domain from your verified domains',\n        removeDomainTitle: 'Remove domain',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Users are automatically invited to join the organization when they sign-up and can join anytime.',\n        automaticInvitationOption__label: 'Automatic invitations',\n        automaticSuggestionOption__description:\n          'Users receive a suggestion to request to join, but must be approved by an admin before they are able to join the organization.',\n        automaticSuggestionOption__label: 'Automatic suggestions',\n        calloutInfoLabel: 'Changing the enrollment mode will only affect new users.',\n        calloutInvitationCountLabel: 'Pending invitations sent to users: {{count}}',\n        calloutSuggestionCountLabel: 'Pending suggestions sent to users: {{count}}',\n        manualInvitationOption__description: 'Users can only be invited manually to the organization.',\n        manualInvitationOption__label: 'No automatic enrollment',\n        subtitle: 'Choose how users from this domain can join the organization.',\n      },\n      start: {\n        headerTitle__danger: 'Danger',\n        headerTitle__enrollment: 'Enrollment options',\n      },\n      subtitle: 'The domain {{domain}} is now verified. Continue by selecting enrollment mode.',\n      title: 'Update {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Enter the verification code sent to your email address',\n      formTitle: 'Verification code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'The domain {{domainName}} needs to be verified via email.',\n      subtitleVerificationCodeScreen: 'A verification code was sent to {{emailAddress}}. Enter the code to continue.',\n      title: 'Verify domain',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Tạo Tổ chức',\n    action__invitationAccept: 'Join',\n    action__manageOrganization: 'Quản lý Tổ chức',\n    action__suggestionsAccept: 'Request to join',\n    notSelected: 'Chưa chọn tổ chức',\n    personalWorkspace: 'Không gian Cá nhân',\n    suggestionsAcceptedLabel: 'Pending approval',\n  },\n  paginationButton__next: 'Tiếp',\n  paginationButton__previous: 'Trước',\n  paginationRowText__displaying: 'Hiển thị',\n  paginationRowText__of: 'của',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Add account',\n      action__signOutAll: 'Sign out of all accounts',\n      subtitle: 'Select the account with which you wish to continue.',\n      title: 'Choose an account',\n    },\n    alternativeMethods: {\n      actionLink: 'Nhận trợ giúp',\n      actionText: 'Don’t have any of these?',\n      blockButton__backupCode: 'Sử dụng mã sao lưu',\n      blockButton__emailCode: 'Gửi mã qua email cho {{identifier}}',\n      blockButton__emailLink: 'Gửi liên kết qua email cho {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Đăng nhập bằng mật khẩu của bạn',\n      blockButton__phoneCode: 'Gửi mã SMS cho {{identifier}}',\n      blockButton__totp: 'Sử dụng ứng dụng xác thực của bạn',\n      getHelp: {\n        blockButton__emailSupport: 'Hỗ trợ qua email',\n        content:\n          'Nếu bạn gặp khó khăn khi đăng nhập vào tài khoản của mình, hãy gửi email cho chúng tôi và chúng tôi sẽ cùng bạn khôi phục quyền truy cập trong thời gian ngắn nhất.',\n        title: 'Nhận trợ giúp',\n      },\n      subtitle: 'Facing issues? You can use any of these methods to sign in.',\n      title: 'Sử dụng phương pháp khác',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'để tiếp tục với {{applicationName}}',\n      title: 'Nhập mã sao lưu',\n    },\n    emailCode: {\n      formTitle: 'Mã xác minh',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'để tiếp tục với {{applicationName}}',\n      title: 'Kiểm tra email của bạn',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: 'Quay trở lại cửa sổ gốc để tiếp tục.',\n        title: 'Liên kết xác minh này đã hết hạn',\n      },\n      failed: {\n        subtitle: 'Quay trở lại cửa sổ gốc để tiếp tục.',\n        title: 'Liên kết xác minh này không hợp lệ',\n      },\n      formSubtitle: 'Sử dụng liên kết xác minh được gửi đến email của bạn',\n      formTitle: 'Liên kết xác minh',\n      loading: {\n        subtitle: 'Bạn sẽ được chuyển hướng sớm',\n        title: 'Đang đăng nhập...',\n      },\n      resendButton: 'Không nhận được liên kết? Gửi lại',\n      subtitle: 'để tiếp tục với {{applicationName}}',\n      title: 'Kiểm tra email của bạn',\n      unusedTab: {\n        title: 'Bạn có thể đóng cửa sổ này',\n      },\n      verified: {\n        subtitle: 'Bạn sẽ được chuyển hướng sớm',\n        title: 'Đăng nhập thành công',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Quay trở lại cửa sổ gốc để tiếp tục',\n        subtitleNewTab: 'Quay trở lại cửa sổ mới mở để tiếp tục',\n        titleNewTab: 'Đăng nhập trên cửa sổ khác',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Mã đặt lại mật khẩu',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'to reset your password',\n      subtitle_email: 'First, enter the code sent to your email ID',\n      subtitle_phone: 'First, enter the code sent to your phone',\n      title: 'Reset password',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Đặt lại mật khẩu',\n      label__alternativeMethods: 'Hoặc, đăng nhập bằng phương pháp khác.',\n      title: 'Quên mật khẩu?',\n    },\n    noAvailableMethods: {\n      message: 'Không thể tiếp tục đăng nhập. Không có phương thức xác thực nào khả dụng.',\n      subtitle: 'Đã xảy ra lỗi',\n      title: 'Không thể đăng nhập',\n    },\n    passkey: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Sử dụng phương pháp khác',\n      subtitle: 'để tiếp tục với {{applicationName}}',\n      title: 'Nhập mật khẩu của bạn',\n    },\n    passwordPwned: {\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: 'Mã xác nhận',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'để tiếp tục với {{applicationName}}',\n      title: 'Kiểm tra điện thoại của bạn',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Mã xác nhận',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: undefined,\n      title: 'Kiểm tra điện thoại của bạn',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Đặt lại mật khẩu',\n      requiredMessage: 'For security reasons, it is required to reset your password.',\n      successMessage: 'Mật khẩu của bạn đã được thay đổi thành công. Đang đăng nhập, vui lòng chờ một chút.',\n      title: 'Đặt lại mật khẩu',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Chúng tôi cần xác minh danh tính của bạn trước khi đặt lại mật khẩu.',\n    },\n    start: {\n      actionLink: 'Đăng ký',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Sử dụng email',\n      actionLink__use_email_username: 'Sử dụng email hoặc tên đăng nhập',\n      actionLink__use_passkey: undefined,\n      actionLink__use_phone: 'Sử dụng số điện thoại',\n      actionLink__use_username: 'Sử dụng tên đăng nhập',\n      actionText: 'Chưa có tài khoản?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'để tiếp tục với {{applicationName}}',\n      subtitleCombined: undefined,\n      title: 'Đăng nhập',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Mã xác minh',\n      subtitle: undefined,\n      title: 'Xác minh hai bước',\n    },\n  },\n  signInEnterPasswordTitle: 'Nhập mật khẩu của bạn',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Đăng nhập',\n      actionText: 'Đã có tài khoản?',\n      subtitle: 'để tiếp tục với {{applicationName}}',\n      title: 'Điền các trường bị thiếu',\n    },\n    emailCode: {\n      formSubtitle: 'Nhập mã xác minh đã được gửi đến địa chỉ email của bạn',\n      formTitle: 'Mã xác minh',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'để tiếp tục với {{applicationName}}',\n      title: 'Xác minh email của bạn',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: 'Sử dụng liên kết xác minh đã được gửi đến địa chỉ email của bạn',\n      formTitle: 'Liên kết xác minh',\n      loading: {\n        title: 'Đang đăng ký...',\n      },\n      resendButton: 'Không nhận được liên kết? Gửi lại',\n      subtitle: 'để tiếp tục với {{applicationName}}',\n      title: 'Xác minh email của bạn',\n      verified: {\n        title: 'Đăng ký thành công',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Quay lại cửa sổ mới được mở để tiếp tục',\n        subtitleNewTab: 'Quay lại cửa sổ trước để tiếp tục',\n        title: 'Xác minh email thành công',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Nhập mã xác minh đã được gửi đến số điện thoại của bạn',\n      formTitle: 'Mã xác minh',\n      resendButton: 'Không nhận được mã? Gửi lại',\n      subtitle: 'để tiếp tục với {{applicationName}}',\n      title: 'Xác minh số điện thoại của bạn',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'Đăng nhập',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: 'Đã có tài khoản?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'để tiếp tục với {{applicationName}}',\n      subtitleCombined: 'để tiếp tục với {{applicationName}}',\n      title: 'Tạo tài khoản của bạn',\n      titleCombined: 'Tạo tài khoản của bạn',\n    },\n  },\n  socialButtonsBlockButton: 'Tiếp tục với {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      'Đăng ký không thành công do không vượt qua các xác thực bảo mật. Vui lòng làm mới trang và thử lại hoặc liên hệ hỗ trợ để được trợ giúp thêm.',\n    captcha_unavailable:\n      'Sign up unsuccessful due to failed bot validation. Please refresh the page to try again or reach out to support for more assistance.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: undefined,\n    form_identifier_exists__phone_number: undefined,\n    form_identifier_exists__username: undefined,\n    form_identifier_not_found: 'Không tìm thấy tài khoản với thông tin này.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'Địa chỉ email phải là một địa chỉ email hợp lệ',\n    form_param_format_invalid__phone_number: 'Phone number must be in a valid international format',\n    form_param_max_length_exceeded__first_name: 'First name should not exceed 256 characters.',\n    form_param_max_length_exceeded__last_name: 'Last name should not exceed 256 characters.',\n    form_param_max_length_exceeded__name: 'Name should not exceed 256 characters.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: 'Mật khẩu của bạn không đủ mạnh.',\n    form_password_pwned:\n      'Mật khẩu này đã được phát hiện trong một cuộc tấn công và không thể sử dụng, vui lòng thử mật khẩu khác.',\n    form_password_pwned__sign_in: undefined,\n    form_password_size_in_bytes_exceeded:\n      'Mật khẩu của bạn đã vượt quá số byte tối đa cho phép, vui lòng rút ngắn hoặc loại bỏ một số ký tự đặc biệt.',\n    form_password_validation_failed: 'Mật khẩu không đúng',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: 'Bạn không thể xóa thông tin nhận dạng cuối cùng của bạn.',\n    not_allowed_access:\n      \"Địa chỉ email hoặc số điện thoại bạn đang sử dụng cho đăng ký không được phép. Điều này có thể do việc sử dụng '+', '=', '#' hoặc '.' trong địa chỉ email của bạn, sử dụng một miền được kết nối với dịch vụ email tạm thời hoặc một loại loại trừ rõ ràng.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: undefined,\n    passkey_not_supported: undefined,\n    passkey_pa_not_supported: undefined,\n    passkey_registration_cancelled: undefined,\n    passkey_retrieval_cancelled: undefined,\n    passwordComplexity: {\n      maximumLength: 'ít hơn {{length}} ký tự',\n      minimumLength: '{{length}} hoặc nhiều ký tự',\n      requireLowercase: 'một chữ cái viết thường',\n      requireNumbers: 'một số',\n      requireSpecialCharacter: 'một ký tự đặc biệt',\n      requireUppercase: 'một chữ cái viết hoa',\n      sentencePrefix: 'Mật khẩu của bạn phải chứa',\n    },\n    phone_number_exists: 'Số điện thoại này đã được sử dụng. Vui lòng thử số khác.',\n    session_exists: 'Bạn đã đăng nhập rồi.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Mật khẩu của bạn đủ mạnh, nhưng có thể mạnh hơn. Hãy thêm nhiều ký tự hơn.',\n      goodPassword: 'Mật khẩu của bạn đáp ứng tất cả các yêu cầu cần thiết.',\n      notEnough: 'Mật khẩu của bạn không đủ mạnh.',\n      suggestions: {\n        allUppercase: 'Viết hoa một số ký tự, nhưng không phải tất cả.',\n        anotherWord: 'Thêm nhiều từ ít phổ biến hơn.',\n        associatedYears: 'Tránh các năm liên quan đến bạn.',\n        capitalization: 'Viết hoa nhiều hơn chỉ chữ cái đầu tiên.',\n        dates: 'Tránh sử dụng ngày tháng năm liên quan đến bạn.',\n        l33t: \"Tránh việc thay thế chữ cái dễ đoán bằng các ký tự như '@' thay cho 'a'.\",\n        longerKeyboardPattern: 'Sử dụng các mẫu bàn phím dài hơn và thay đổi hướng gõ nhiều lần.',\n        noNeed: 'Bạn có thể tạo mật khẩu mạnh mà không cần sử dụng ký tự đặc biệt, số hoặc chữ cái viết hoa.',\n        pwned: 'Nếu bạn sử dụng mật khẩu này ở những nơi khác, bạn nên thay đổi nó.',\n        recentYears: 'Tránh các năm gần đây.',\n        repeated: 'Tránh việc lặp lại từ và ký tự.',\n        reverseWords: 'Tránh việc viết ngược các từ thông thường.',\n        sequences: 'Tránh các chuỗi ký tự thông thường.',\n        useWords: 'Sử dụng nhiều từ, nhưng tránh các cụm từ thông thường.',\n      },\n      warnings: {\n        common: 'Đây là một mật khẩu phổ biến.',\n        commonNames: 'Các tên riêng và họ phổ biến dễ đoán.',\n        dates: 'Ngày tháng dễ đoán.',\n        extendedRepeat: 'Các mẫu ký tự lặp lại như \"abcabcabc\" dễ đoán.',\n        keyPattern: 'Mẫu bàn phím ngắn dễ đoán.',\n        namesByThemselves: 'Các tên riêng hoặc họ riêng dễ đoán.',\n        pwned: 'Mật khẩu của bạn đã bị rò rỉ qua một cuộc tấn công dữ liệu trên Internet.',\n        recentYears: 'Các năm gần đây dễ đoán.',\n        sequences: 'Các chuỗi ký tự phổ biến như \"abc\" dễ đoán.',\n        similarToCommon: 'Đây giống với một mật khẩu phổ biến.',\n        simpleRepeat: 'Các ký tự lặp lại như \"aaa\" dễ đoán.',\n        straightRow: 'Các hàng phím trên bàn phím của bạn dễ đoán.',\n        topHundred: 'Đây là một mật khẩu được sử dụng thường xuyên.',\n        topTen: 'Đây là một mật khẩu được sử dụng rất nhiều.',\n        userInputs: 'Không nên có bất kỳ dữ liệu cá nhân hoặc liên quan đến trang web.',\n        wordByItself: 'Một từ đơn dễ đoán.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Thêm tài khoản',\n    action__manageAccount: 'Quản lý tài khoản',\n    action__signOut: 'Đăng xuất',\n    action__signOutAll: 'Đăng xuất khỏi tất cả các tài khoản',\n  },\n  userProfile: {\n    backupCodePage: {\n      actionLabel__copied: 'Đã sao chép!',\n      actionLabel__copy: 'Sao chép tất cả',\n      actionLabel__download: 'Tải xuống .txt',\n      actionLabel__print: 'In',\n      infoText1: 'Các mã sao lưu sẽ được kích hoạt cho tài khoản này.',\n      infoText2:\n        'Giữ các mã sao lưu bí mật và lưu chúng một cách an toàn. Bạn có thể tạo lại các mã sao lưu nếu bạn nghi ngờ chúng đã bị xâm phạm.',\n      subtitle__codelist: 'Lưu chúng một cách an toàn và giữ chúng bí mật.',\n      successMessage:\n        'Mã sao lưu đã được kích hoạt. Bạn có thể sử dụng một trong các mã này để đăng nhập vào tài khoản của mình, nếu bạn mất quyền truy cập vào thiết bị xác thực của mình. Mỗi mã chỉ có thể sử dụng một lần.',\n      successSubtitle:\n        'Bạn có thể sử dụng một trong các mã này để đăng nhập vào tài khoản của mình, nếu bạn mất quyền truy cập vào thiết bị xác thực của mình.',\n      title: 'Thêm mã xác thực sao lưu',\n      title__codelist: 'Các mã sao lưu',\n    },\n    billingPage: {\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Chọn một nhà cung cấp để kết nối tài khoản của bạn.',\n      formHint__noAccounts: 'Không có nhà cung cấp tài khoản bên ngoài khả dụng.',\n      removeResource: {\n        messageLine1: '{{identifier}} sẽ bị xóa khỏi tài khoản này.',\n        messageLine2:\n          'Bạn sẽ không thể sử dụng tài khoản liên kết này và bất kỳ tính năng phụ thuộc nào sẽ không còn hoạt động.',\n        successMessage: '{{connectedAccount}} đã được xóa khỏi tài khoản của bạn.',\n        title: 'Xóa tài khoản liên kết',\n      },\n      socialButtonsBlockButton: 'Kết nối tài khoản {{provider|titleize}}',\n      successMessage: 'Nhà cung cấp đã được thêm vào tài khoản của bạn',\n      title: 'Thêm tài khoản liên kết',\n    },\n    deletePage: {\n      actionDescription: 'Type \"Delete account\" below to continue.',\n      confirm: 'Xóa tài khoản',\n      messageLine1: 'Bạn có chắc chắn muốn xóa tài khoản của mình không?',\n      messageLine2: 'Hành động này là vĩnh viễn và không thể hoàn tác.',\n      title: 'Xóa tài khoản',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Một email chứa mã xác minh sẽ được gửi đến địa chỉ email này.',\n        formSubtitle: 'Nhập mã xác minh được gửi đến {{identifier}}',\n        formTitle: 'Mã xác minh',\n        resendButton: 'Không nhận được mã? Gửi lại',\n        successMessage: 'Email {{identifier}} đã được thêm vào tài khoản của bạn.',\n      },\n      emailLink: {\n        formHint: 'Một email chứa liên kết xác minh sẽ được gửi đến địa chỉ email này.',\n        formSubtitle: 'Nhấp vào liên kết xác minh trong email được gửi đến {{identifier}}',\n        formTitle: 'Liên kết xác minh',\n        resendButton: 'Không nhận được liên kết? Gửi lại',\n        successMessage: 'Email {{identifier}} đã được thêm vào tài khoản của bạn.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} sẽ bị xóa khỏi tài khoản này.',\n        messageLine2: 'Bạn sẽ không thể đăng nhập bằng địa chỉ email này nữa.',\n        successMessage: '{{emailAddress}} đã được xóa khỏi tài khoản của bạn.',\n        title: 'Xóa địa chỉ email',\n      },\n      title: 'Thêm địa chỉ email',\n      verifyTitle: 'Verify email address',\n    },\n    formButtonPrimary__add: 'Add',\n    formButtonPrimary__continue: 'Tiếp tục',\n    formButtonPrimary__finish: 'Hoàn thành',\n    formButtonPrimary__remove: 'Remove',\n    formButtonPrimary__save: 'Save',\n    formButtonReset: 'Hủy',\n    mfaPage: {\n      formHint: 'Chọn một phương pháp để thêm.',\n      title: 'Thêm xác minh hai bước',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Use existing number',\n      primaryButton__addPhoneNumber: 'Thêm số điện thoại',\n      removeResource: {\n        messageLine1: '{{identifier}} sẽ không còn nhận được mã xác thực khi đăng nhập.',\n        messageLine2: 'Tài khoản của bạn có thể không an toàn. Bạn có chắc chắn muốn tiếp tục không?',\n        successMessage: 'Xác thực hai bước bằng mã SMS đã được gỡ bỏ cho {{mfaPhoneCode}}',\n        title: 'Gỡ bỏ xác thực hai bước',\n      },\n      subtitle__availablePhoneNumbers: 'Chọn một số điện thoại để đăng ký xác thực hai bước bằng mã SMS.',\n      subtitle__unavailablePhoneNumbers:\n        'Không có số điện thoại nào khả dụng để đăng ký xác thực hai bước bằng mã SMS.',\n      successMessage1:\n        'When signing in, you will need to enter a verification code sent to this phone number as an additional step.',\n      successMessage2:\n        'Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.',\n      successTitle: 'SMS code verification enabled',\n      title: 'Thêm mã xác thực SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Quét mã QR thay vì đó',\n        buttonUnableToScan__nonPrimary: 'Không thể quét mã QR?',\n        infoText__ableToScan:\n          'Thiết lập một phương thức đăng nhập mới trong ứng dụng xác thực của bạn và quét mã QR dưới đây để liên kết nó với tài khoản của bạn.',\n        infoText__unableToScan:\n          'Thiết lập một phương thức đăng nhập mới trong ứng dụng xác thực và nhập Khóa được cung cấp bên dưới.',\n        inputLabel__unableToScan1:\n          'Đảm bảo đã kích hoạt mật khẩu dựa trên thời gian hoặc mật khẩu một lần, sau đó hoàn thành việc liên kết tài khoản của bạn.',\n        inputLabel__unableToScan2:\n          'Hoặc nếu ứng dụng xác thực của bạn hỗ trợ TOTP URIs, bạn cũng có thể sao chép toàn bộ URI.',\n      },\n      removeResource: {\n        messageLine1: 'Mã xác thực từ ứng dụng xác thực này sẽ không còn được yêu cầu khi đăng nhập.',\n        messageLine2: 'Tài khoản của bạn có thể không an toàn. Bạn có chắc chắn muốn tiếp tục không?',\n        successMessage: 'Xác thực hai bước qua ứng dụng xác thực đã được gỡ bỏ.',\n        title: 'Gỡ bỏ xác thực hai bước',\n      },\n      successMessage:\n        'Xác thực hai bước đã được kích hoạt. Khi đăng nhập, bạn sẽ cần nhập mã xác thực từ ứng dụng xác thực này như một bước bổ sung.',\n      title: 'Thêm ứng dụng xác thực',\n      verifySubtitle: 'Nhập mã xác thực được tạo bởi ứng dụng xác thực của bạn',\n      verifyTitle: 'Mã xác thực',\n    },\n    mobileButton__menu: 'Menu',\n    navbar: {\n      account: 'Profile',\n      billing: undefined,\n      description: 'Manage your account info.',\n      security: 'Security',\n      title: 'Account',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: undefined,\n        title: undefined,\n      },\n      subtitle__rename: undefined,\n      title__rename: undefined,\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'It is recommended to sign out of all other devices which may have used your old password.',\n      readonly: 'Hiện tại bạn không thể chỉnh sửa mật khẩu vì bạn chỉ có thể đăng nhập qua kết nối doanh nghiệp.',\n      successMessage__set: 'Mật khẩu của bạn đã được thiết lập.',\n      successMessage__signOutOfOtherSessions: 'Tất cả các thiết bị khác đã được đăng xuất.',\n      successMessage__update: 'Mật khẩu của bạn đã được cập nhật.',\n      title__set: 'Thiết lập mật khẩu',\n      title__update: 'Thay đổi mật khẩu',\n    },\n    phoneNumberPage: {\n      infoText: 'Một tin nhắn chứa liên kết xác minh sẽ được gửi đến số điện thoại này.',\n      removeResource: {\n        messageLine1: '{{identifier}} sẽ bị xóa khỏi tài khoản này.',\n        messageLine2: 'Bạn sẽ không thể đăng nhập bằng số điện thoại này nữa.',\n        successMessage: '{{phoneNumber}} đã được xóa khỏi tài khoản của bạn.',\n        title: 'Xóa số điện thoại',\n      },\n      successMessage: '{{identifier}} đã được thêm vào tài khoản của bạn.',\n      title: 'Thêm số điện thoại',\n      verifySubtitle: 'Enter the verification code sent to {{identifier}}',\n      verifyTitle: 'Verify phone number',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Tải lên ảnh JPG, PNG, GIF, hoặc WEBP có dung lượng nhỏ hơn 10 MB',\n      imageFormDestructiveActionSubtitle: 'Xóa ảnh',\n      imageFormSubtitle: 'Tải ảnh lên',\n      imageFormTitle: 'Hình ảnh hồ sơ',\n      readonly: 'Thông tin hồ sơ của bạn đã được cung cấp bởi kết nối doanh nghiệp và không thể chỉnh sửa.',\n      successMessage: 'Hồ sơ của bạn đã được cập nhật.',\n      title: 'Cập nhật hồ sơ',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Đăng xuất khỏi thiết bị',\n        title: 'Thiết bị hoạt động',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Thử lại',\n        actionLabel__reauthorize: 'Xác thực ngay',\n        destructiveActionTitle: 'Xóa',\n        primaryButton: 'Kết nối tài khoản',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          'The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues',\n        title: 'Tài khoản đã kết nối',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Xóa Tài khoản',\n        title: 'Nguy hiểm',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Xóa địa chỉ email',\n        detailsAction__nonPrimary: 'Đặt làm chính',\n        detailsAction__primary: 'Hoàn tất xác minh',\n        detailsAction__unverified: 'Hoàn tất xác minh',\n        primaryButton: 'Thêm địa chỉ email',\n        title: 'Địa chỉ email',\n      },\n      enterpriseAccountsSection: {\n        title: 'Tài khoản doanh nghiệp',\n      },\n      headerTitle__account: 'Tài khoản',\n      headerTitle__security: 'Bảo mật',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Tạo lại mã',\n          headerTitle: 'Mã sao lưu',\n          subtitle__regenerate:\n            'Nhận một bộ mã sao lưu an toàn mới. Các mã sao lưu trước đó sẽ bị xóa và không thể sử dụng được.',\n          title__regenerate: 'Tạo lại mã sao lưu',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Đặt làm mặc định',\n          destructiveActionLabel: 'Xóa số điện thoại',\n        },\n        primaryButton: 'Thêm xác thực hai bước',\n        title: 'Xác thực hai bước',\n        totp: {\n          destructiveActionTitle: 'Xóa',\n          headerTitle: 'Ứng dụng xác thực',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: undefined,\n        menuAction__rename: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Đặt mật khẩu',\n        primaryButton__updatePassword: 'Thay đổi mật khẩu',\n        title: 'Mật khẩu',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Xóa số điện thoại',\n        detailsAction__nonPrimary: 'Đặt làm chính',\n        detailsAction__primary: 'Hoàn tất xác minh',\n        detailsAction__unverified: 'Hoàn tất xác minh',\n        primaryButton: 'Thêm số điện thoại',\n        title: 'Số điện thoại',\n      },\n      profileSection: {\n        primaryButton: undefined,\n        title: 'Hồ sơ',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Đặt tên người dùng',\n        primaryButton__updateUsername: 'Thay đổi tên người dùng',\n        title: 'Tên người dùng',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Xóa ví',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Ví Web3',\n        title: 'Ví Web3',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Tên người dùng của bạn đã được cập nhật.',\n      title__set: 'Cập nhật tên người dùng',\n      title__update: 'Cập nhật tên người dùng',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} sẽ bị xóa khỏi tài khoản này.',\n        messageLine2: 'Bạn sẽ không thể đăng nhập bằng ví web3 này nữa.',\n        successMessage: '{{web3Wallet}} đã được xóa khỏi tài khoản của bạn.',\n        title: 'Xóa ví web3',\n      },\n      subtitle__availableWallets: 'Chọn một ví web3 để kết nối với tài khoản của bạn.',\n      subtitle__unavailableWallets: 'Không có ví web3 khả dụng.',\n      successMessage: 'Ví đã được thêm vào tài khoản của bạn.',\n      title: 'Thêm ví web3',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBACE;AAAA,IACF,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCAAiC;AAAA,MACjC,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}