import React from 'react';
export declare const CardContent: React.ForwardRefExoticComponent<Omit<Omit<import("../../primitives").FlexProps, "ref"> & React.RefAttributes<HTMLDivElement> & {
    elementDescriptor?: import("../../customizables/elementDescriptors").ElementDescriptor | Array<import("../../customizables/elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("../../customizables/elementDescriptors").ElementId;
    css?: never;
    sx?: import("../../styledSystem").ThemableCssProp;
}, "ref"> & React.RefAttributes<HTMLDivElement>>;
