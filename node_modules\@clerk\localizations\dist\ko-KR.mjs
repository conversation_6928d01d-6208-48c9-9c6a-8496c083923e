// src/ko-KR.ts
var koKR = {
  locale: "ko-KR",
  backButton: "\uB3CC\uC544\uAC00\uAE30",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "\uAE30\uBCF8\uAC12",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "\uAE30\uD0C0 \uC0AC\uCE6D \uC7A5\uCE58",
  badge__primary: "\uAE30\uBCF8",
  badge__renewsAt: void 0,
  badge__requiresAction: "\uC870\uCE58 \uD544\uC694",
  badge__startsAt: void 0,
  badge__thisDevice: "\uC774 \uC7A5\uCE58",
  badge__unverified: "\uBBF8\uD655\uC778",
  badge__upcomingPlan: void 0,
  badge__userDevice: "\uC0AC\uC6A9\uC790 \uC7A5\uCE58",
  badge__you: "\uB2F9\uC2E0",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "\uC870\uC9C1 \uB9CC\uB4E4\uAE30",
    invitePage: {
      formButtonReset: "\uB118\uAE30\uAE30"
    },
    title: "\uC870\uC9C1 \uB9CC\uB4E4\uAE30"
  },
  dates: {
    lastDay: "\uC5B4\uC81C {{ date | timeString('ko-KR') }}",
    next6Days: "\uB2E4\uC74C {{ date | weekday('ko-KR','long') }} {{ date | timeString('ko-KR') }}",
    nextDay: "\uB0B4\uC77C {{ date | timeString('ko-KR') }}",
    numeric: "{{ date | numeric('ko-KR') }}",
    previous6Days: "\uC9C0\uB09C {{ date | weekday('ko-KR','long') }} {{ date | timeString('ko-KR') }}",
    sameDay: "\uC624\uB298 {{ date | timeString('ko-KR') }}"
  },
  dividerText: "\uB610\uB294",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "\uB2E4\uB978 \uBC29\uBC95 \uC0AC\uC6A9\uD558\uAE30",
  footerPageLink__help: "\uB3C4\uC6C0",
  footerPageLink__privacy: "\uAC1C\uC778\uC815\uBCF4\uCC98\uB9AC\uBC29\uCE68",
  footerPageLink__terms: "\uC57D\uAD00",
  formButtonPrimary: "\uACC4\uC18D",
  formButtonPrimary__verify: "Verify",
  formFieldAction__forgotPassword: "\uBE44\uBC00\uBC88\uD638\uB97C \uC78A\uC73C\uC168\uB098\uC694?",
  formFieldError__matchingPasswords: "\uBE44\uBC00\uBC88\uD638\uAC00 \uC77C\uCE58\uD569\uB2C8\uB2E4.",
  formFieldError__notMatchingPasswords: "\uBE44\uBC00\uBC88\uD638\uAC00 \uC77C\uCE58\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.",
  formFieldError__verificationLinkExpired: "The verification link expired. Please request a new link.",
  formFieldHintText__optional: "\uC120\uD0DD\uC0AC\uD56D",
  formFieldHintText__slug: "A slug is a human-readable ID that must be unique. It\u2019s often used in URLs.",
  formFieldInputPlaceholder__backupCode: void 0,
  formFieldInputPlaceholder__confirmDeletionUserAccount: "\uACC4\uC815 \uC0AD\uC81C",
  formFieldInputPlaceholder__emailAddress: void 0,
  formFieldInputPlaceholder__emailAddress_username: void 0,
  formFieldInputPlaceholder__emailAddresses: "\uD558\uB098 \uC774\uC0C1\uC758 \uC774\uBA54\uC77C \uC8FC\uC18C\uB97C \uACF5\uBC31 \uB610\uB294 \uC27C\uD45C\uB85C \uAD6C\uBD84\uD558\uC5EC \uC785\uB825\uD558\uAC70\uB098 \uBD99\uC5EC\uB123\uC2B5\uB2C8\uB2E4",
  formFieldInputPlaceholder__firstName: void 0,
  formFieldInputPlaceholder__lastName: void 0,
  formFieldInputPlaceholder__organizationDomain: void 0,
  formFieldInputPlaceholder__organizationDomainEmailAddress: void 0,
  formFieldInputPlaceholder__organizationName: void 0,
  formFieldInputPlaceholder__organizationSlug: void 0,
  formFieldInputPlaceholder__password: void 0,
  formFieldInputPlaceholder__phoneNumber: void 0,
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__automaticInvitations: "Enable automatic invitations for this domain",
  formFieldLabel__backupCode: "\uBC31\uC5C5 \uCF54\uB4DC",
  formFieldLabel__confirmDeletion: "\uD655\uC778",
  formFieldLabel__confirmPassword: "\uBE44\uBC00\uBC88\uD638 \uD655\uC778",
  formFieldLabel__currentPassword: "\uD604\uC7AC \uBE44\uBC00\uBC88\uD638",
  formFieldLabel__emailAddress: "\uC774\uBA54\uC77C \uC8FC\uC18C",
  formFieldLabel__emailAddress_username: "\uC774\uBA54\uC77C \uC8FC\uC18C \uD639\uC740 \uC0AC\uC6A9\uC790 \uC774\uB984",
  formFieldLabel__emailAddresses: "\uC774\uBA54\uC77C \uC8FC\uC18C",
  formFieldLabel__firstName: "\uC774\uB984",
  formFieldLabel__lastName: "\uC131",
  formFieldLabel__newPassword: "\uC0C8 \uBE44\uBC00\uBC88\uD638",
  formFieldLabel__organizationDomain: "\uB3C4\uBA54\uC778",
  formFieldLabel__organizationDomainDeletePending: "Delete pending invitations and suggestions",
  formFieldLabel__organizationDomainEmailAddress: "Verification email address",
  formFieldLabel__organizationDomainEmailAddressDescription: "Enter an email address under this domain to receive a code and verify this domain.",
  formFieldLabel__organizationName: "\uC774\uB984",
  formFieldLabel__organizationSlug: "\uC2AC\uB7EC\uADF8",
  formFieldLabel__passkeyName: void 0,
  formFieldLabel__password: "\uBE44\uBC00\uBC88\uD638",
  formFieldLabel__phoneNumber: "\uD734\uB300\uD3F0 \uBC88\uD638",
  formFieldLabel__role: "\uC5ED\uD560",
  formFieldLabel__signOutOfOtherSessions: "\uB2E4\uB978 \uBAA8\uB4E0 \uAE30\uAE30\uC5D0\uC11C \uB85C\uADF8\uC544\uC6C3",
  formFieldLabel__username: "\uC0AC\uC6A9\uC790 \uC774\uB984",
  impersonationFab: {
    action__signOut: "\uB85C\uADF8\uC544\uC6C3",
    title: "{{identifier}}\uB85C \uB85C\uADF8\uC778\uD588\uC2B5\uB2C8\uB2E4"
  },
  maintenanceMode: void 0,
  membershipRole__admin: "\uAD00\uB9AC\uC790",
  membershipRole__basicMember: "\uBA64\uBC84",
  membershipRole__guestMember: "\uAC8C\uC2A4\uD2B8",
  organizationList: {
    action__createOrganization: "Create organization",
    action__invitationAccept: "Join",
    action__suggestionsAccept: "Request to join",
    createOrganization: "Create Organization",
    invitationAcceptedLabel: "Joined",
    subtitle: "to continue to {{applicationName}}",
    suggestionsAcceptedLabel: "Pending approval",
    title: "Choose an account",
    titleWithoutPersonal: "Choose an organization"
  },
  organizationProfile: {
    badge__automaticInvitation: "Automatic invitations",
    badge__automaticSuggestion: "Automatic suggestions",
    badge__manualInvitation: "No automatic enrollment",
    badge__unverified: "Unverified",
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Add the domain to verify. Users with email addresses at this domain can join the organization automatically or request to join.",
      title: "Add domain"
    },
    invitePage: {
      detailsTitle__inviteFailed: "\uCD08\uB300\uB97C \uBCF4\uB0BC \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uB2E4\uC74C\uC744 \uC218\uC815\uD558\uACE0 \uB2E4\uC2DC \uC2DC\uB3C4\uD558\uC138\uC694:",
      formButtonPrimary__continue: "\uCD08\uB300 \uBCF4\uB0B4\uAE30",
      selectDropdown__role: "Select role",
      subtitle: "\uC774 \uC870\uC9C1\uC5D0 \uC0C8 \uBA64\uBC84 \uCD08\uB300",
      successMessage: "\uCD08\uB300\uAC00 \uC131\uACF5\uC801\uC73C\uB85C \uC804\uC1A1\uB418\uC5C8\uC2B5\uB2C8\uB2E4",
      title: "\uD68C\uC6D0 \uCD08\uB300"
    },
    membersPage: {
      action__invite: "\uCD08\uB300",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "\uD68C\uC6D0 \uC81C\uAC70",
        tableHeader__actions: void 0,
        tableHeader__joined: "\uAC00\uC785\uB428",
        tableHeader__role: "\uC5ED\uD560",
        tableHeader__user: "\uC0AC\uC6A9\uC790"
      },
      detailsTitle__emptyRow: "\uD45C\uC2DC\uD560 \uBA64\uBC84 \uC5C6\uC74C",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Invite users by connecting an email domain with your organization. Anyone who signs up with a matching email domain will be able to join the organization anytime.",
          headerTitle: "Automatic invitations",
          primaryButton: "Manage verified domains"
        },
        table__emptyRow: "No invitations to display"
      },
      invitedMembersTab: {
        menuAction__revoke: "\uCD08\uB300 \uCDE8\uC18C",
        tableHeader__invited: "\uCD08\uB300\uB428"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Users who sign up with a matching email domain, will be able to see a suggestion to request to join your organization.",
          headerTitle: "Automatic suggestions",
          primaryButton: "Manage verified domains"
        },
        menuAction__approve: "Approve",
        menuAction__reject: "Reject",
        tableHeader__requested: "Requested access",
        table__emptyRow: "No requests to display"
      },
      start: {
        headerTitle__invitations: "Invitations",
        headerTitle__members: "Members",
        headerTitle__requests: "Requests"
      }
    },
    navbar: {
      billing: void 0,
      description: "Manage your organization.",
      general: "General",
      members: "Members",
      title: "Organization"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: "\uACC4\uC18D\uD558\uB824\uBA74 \uC544\uB798\uC5D0 {{organizationName}}\uC744(\uB97C) \uC785\uB825\uD558\uC138\uC694.",
          messageLine1: "\uC774 \uC870\uC9C1\uC744 \uC0AD\uC81C\uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C?",
          messageLine2: "\uC774 \uC791\uC5C5\uC740 \uC601\uAD6C\uC801\uC774\uBA70 \uB418\uB3CC\uB9B4 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.",
          successMessage: "\uC870\uC9C1\uC744 \uC0AD\uC81C\uD588\uC2B5\uB2C8\uB2E4.",
          title: "\uC870\uC9C1 \uC0AD\uC81C"
        },
        leaveOrganization: {
          actionDescription: 'Type "{{organizationName}}" below to continue.',
          messageLine1: "\uC774 \uC870\uC9C1\uC744 \uD0C8\uD1F4\uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C? \uC774 \uC870\uC9C1 \uBC0F \uD574\uB2F9 \uC560\uD50C\uB9AC\uCF00\uC774\uC158\uC5D0 \uB300\uD55C \uC561\uC138\uC2A4 \uAD8C\uD55C\uC744 \uC783\uAC8C\uB429\uB2C8\uB2E4.",
          messageLine2: "\uC774 \uC791\uC5C5\uC740 \uC601\uAD6C\uC801\uC774\uBA70 \uB418\uB3CC\uB9B4 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4",
          successMessage: "\uC870\uC9C1\uC744 \uD0C8\uD1F4\uD588\uC2B5\uB2C8\uB2E4.",
          title: "\uC870\uC9C1 \uB5A0\uB098\uAE30"
        },
        title: "\uC704\uD5D8"
      },
      domainSection: {
        menuAction__manage: "Manage",
        menuAction__remove: "Delete",
        menuAction__verify: "Verify",
        primaryButton: "\uB3C4\uBA54\uC778 \uCD94\uAC00",
        subtitle: "\uC778\uC99D\uB41C \uC774\uBA54\uC77C \uB3C4\uBA54\uC778\uC744 \uAE30\uBC18\uC73C\uB85C \uC0AC\uC6A9\uC790\uAC00 \uC870\uC9C1\uC5D0 \uC790\uB3D9\uC73C\uB85C \uAC00\uC785\uD558\uAC70\uB098 \uAC00\uC785 \uC694\uCCAD\uC744 \uD560 \uC218 \uC788\uAC8C \uD569\uB2C8\uB2E4.",
        title: "\uC778\uC99D\uB41C \uB3C4\uBA54\uC778"
      },
      successMessage: "\uC870\uC9C1\uC774 \uC5C5\uB370\uC774\uD2B8\uB418\uC5C8\uC2B5\uB2C8\uB2E4.",
      title: "\uC870\uC9C1 \uD504\uB85C\uD544"
    },
    removeDomainPage: {
      messageLine1: "The email domain {{domain}} will be removed.",
      messageLine2: "Users won\u2019t be able to join the organization automatically after this.",
      successMessage: "{{domain}} has been removed.",
      title: "Remove domain"
    },
    start: {
      headerTitle__general: "General",
      headerTitle__members: "\uBA64\uBC84",
      profileSection: {
        primaryButton: void 0,
        title: "Organization Profile",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Removing this domain will affect invited users.",
        removeDomainActionLabel__remove: "Remove domain",
        removeDomainSubtitle: "Remove this domain from your verified domains",
        removeDomainTitle: "Remove domain"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Users are automatically invited to join the organization when they sign-up and can join anytime.",
        automaticInvitationOption__label: "Automatic invitations",
        automaticSuggestionOption__description: "Users receive a suggestion to request to join, but must be approved by an admin before they are able to join the organization.",
        automaticSuggestionOption__label: "Automatic suggestions",
        calloutInfoLabel: "Changing the enrollment mode will only affect new users.",
        calloutInvitationCountLabel: "Pending invitations sent to users: {{count}}",
        calloutSuggestionCountLabel: "Pending suggestions sent to users: {{count}}",
        manualInvitationOption__description: "Users can only be invited manually to the organization.",
        manualInvitationOption__label: "No automatic enrollment",
        subtitle: "Choose how users from this domain can join the organization."
      },
      start: {
        headerTitle__danger: "Danger",
        headerTitle__enrollment: "Enrollment options"
      },
      subtitle: "The domain {{domain}} is now verified. Continue by selecting enrollment mode.",
      title: "Update {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Enter the verification code sent to your email address",
      formTitle: "Verification code",
      resendButton: "Didn't receive a code? Resend",
      subtitle: "The domain {{domainName}} needs to be verified via email.",
      subtitleVerificationCodeScreen: "A verification code was sent to {{emailAddress}}. Enter the code to continue.",
      title: "Verify domain"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "\uC870\uC9C1 \uB9CC\uB4E4\uAE30",
    action__invitationAccept: "Join",
    action__manageOrganization: "\uC870\uC9C1 \uAD00\uB9AC",
    action__suggestionsAccept: "Request to join",
    notSelected: "\uC120\uD0DD\uD55C \uC870\uC9C1 \uC5C6\uC74C",
    personalWorkspace: "\uAC1C\uC778 \uC6CC\uD06C\uC2A4\uD398\uC774\uC2A4",
    suggestionsAcceptedLabel: "Pending approval"
  },
  paginationButton__next: "\uB2E4\uC74C",
  paginationButton__previous: "\uC774\uC804",
  paginationRowText__displaying: "\uD45C\uC2DC\uC911",
  paginationRowText__of: "\uC758",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Add account",
      action__signOutAll: "Sign out of all accounts",
      subtitle: "Select the account with which you wish to continue.",
      title: "Choose an account"
    },
    alternativeMethods: {
      actionLink: "\uB3C4\uC6C0 \uC694\uCCAD\uD558\uAE30",
      actionText: "Don\u2019t have any of these?",
      blockButton__backupCode: "\uBC31\uC5C5 \uCF54\uB4DC \uC0AC\uC6A9\uD558\uAE30",
      blockButton__emailCode: "{{identifier}}\uB85C \uC774\uBA54\uC77C \uCF54\uB4DC \uBCF4\uB0B4\uAE30",
      blockButton__emailLink: "{{identifier}}\uB85C \uC774\uBA54\uC77C \uB9C1\uD06C \uBCF4\uB0B4\uAE30",
      blockButton__passkey: void 0,
      blockButton__password: "\uBE44\uBC00\uBC88\uD638\uB85C \uB85C\uADF8\uC778",
      blockButton__phoneCode: "{{identifier}}\uB85C SMS \uCF54\uB4DC \uBCF4\uB0B4\uAE30",
      blockButton__totp: "\uC778\uC99D \uC571 \uC0AC\uC6A9\uD558\uAE30",
      getHelp: {
        blockButton__emailSupport: "\uC774\uBA54\uC77C \uC9C0\uC6D0",
        content: "\uACC4\uC815\uC5D0 \uB85C\uADF8\uC778\uD558\uB294 \uB370 \uBB38\uC81C\uAC00 \uC788\uB294 \uACBD\uC6B0 \uC774\uBA54\uC77C\uC744 \uBCF4\uB0B4\uC8FC\uC2DC\uBA74 \uCD5C\uB300\uD55C \uBE68\uB9AC \uC561\uC138\uC2A4\uB97C \uBCF5\uAD6C\uD574 \uB4DC\uB9AC\uACA0\uC2B5\uB2C8\uB2E4.",
        title: "\uB3C4\uC6C0 \uC694\uCCAD\uD558\uAE30"
      },
      subtitle: "\uBB38\uC81C\uAC00 \uC788\uB098\uC694? \uB2E4\uB978 \uBC29\uBC95\uC73C\uB85C \uB85C\uADF8\uC778\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.",
      title: "\uB2E4\uB978 \uBC29\uBC95 \uC0AC\uC6A9\uD558\uAE30"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "\uBC31\uC5C5\uCF54\uB4DC\uB294 2\uB2E8\uACC4 \uC778\uC99D\uC744 \uC124\uC815\uD560 \uB54C \uC5BB\uC740 \uCF54\uB4DC\uC785\uB2C8\uB2E4",
      title: "\uBC31\uC5C5 \uCF54\uB4DC \uC785\uB825"
    },
    emailCode: {
      formTitle: "\uC778\uC99D \uCF54\uB4DC",
      resendButton: "\uCF54\uB4DC \uC7AC\uC804\uC1A1",
      subtitle: "{{applicationName}}\uB85C \uACC4\uC18D\uD558\uB824\uBA74",
      title: "\uC774\uBA54\uC77C\uC744 \uD655\uC778\uD558\uC138\uC694"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      expired: {
        subtitle: "\uACC4\uC18D\uD558\uB824\uBA74 \uC6D0\uB798 \uD0ED\uC73C\uB85C \uB3CC\uC544\uAC00\uC138\uC694.",
        title: "\uC774 \uC778\uC99D \uB9C1\uD06C\uB294 \uB9CC\uB8CC\uB418\uC5C8\uC2B5\uB2C8\uB2E4"
      },
      failed: {
        subtitle: "\uACC4\uC18D\uD558\uB824\uBA74 \uC6D0\uB798 \uD0ED\uC73C\uB85C \uB3CC\uC544\uAC00\uC138\uC694.",
        title: "\uC774 \uC778\uC99D \uB9C1\uD06C\uB294 \uC720\uD6A8\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4"
      },
      formSubtitle: "\uC774\uBA54\uC77C\uB85C \uC804\uC1A1\uB41C \uC778\uC99D \uB9C1\uD06C\uB97C \uC0AC\uC6A9\uD558\uC138\uC694",
      formTitle: "\uC778\uC99D \uB9C1\uD06C",
      loading: {
        subtitle: "\uACE7 \uB9AC\uB2E4\uC774\uB809\uC158 \uB429\uB2C8\uB2E4",
        title: "\uB85C\uADF8\uC778 \uC911..."
      },
      resendButton: "\uB9C1\uD06C \uC7AC\uC804\uC1A1",
      subtitle: "{{applicationName}}\uB85C \uACC4\uC18D\uD558\uB824\uBA74",
      title: "\uC774\uBA54\uC77C\uC744 \uD655\uC778\uD558\uC138\uC694",
      unusedTab: {
        title: "\uC774 \uD0ED\uC744 \uB2EB\uC73C\uC154\uB3C4 \uB429\uB2C8\uB2E4"
      },
      verified: {
        subtitle: "\uACE7 \uB9AC\uB2E4\uC774\uB809\uC158 \uB420 \uC608\uC815\uC785\uB2C8\uB2E4",
        title: "\uB85C\uADF8\uC778\uC5D0 \uC131\uACF5\uD588\uC2B5\uB2C8\uB2E4"
      },
      verifiedSwitchTab: {
        subtitle: "\uACC4\uC18D\uD558\uB824\uBA74 \uC6D0\uB798 \uD0ED\uC73C\uB85C \uB3CC\uC544\uAC00\uC138\uC694",
        subtitleNewTab: "\uACC4\uC18D\uD558\uB824\uBA74 \uC0C8\uB85C \uC5F0 \uD0ED\uC73C\uB85C \uB3CC\uC544\uAC00\uC138\uC694",
        titleNewTab: "\uB2E4\uB978 \uD0ED\uC5D0\uC11C \uB85C\uADF8\uC778"
      }
    },
    forgotPassword: {
      formTitle: "\uBE44\uBC00\uBC88\uD638 \uC7AC\uC124\uC815 \uCF54\uB4DC",
      resendButton: "\uCF54\uB4DC \uC7AC\uC804\uC1A1",
      subtitle: "to reset your password",
      subtitle_email: "\uBA3C\uC800 \uC774\uBA54\uC77C\uB85C \uC804\uC1A1\uB41C \uCF54\uB4DC\uB97C \uC785\uB825\uD558\uC138\uC694",
      subtitle_phone: "\uBA3C\uC800 \uD734\uB300\uD3F0\uC73C\uB85C \uC804\uC1A1\uB41C \uCF54\uB4DC\uB97C \uC785\uB825\uD558\uC138\uC694",
      title: "\uBE44\uBC00\uBC88\uD638 \uC7AC\uC124\uC815"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "\uBE44\uBC00\uBC88\uD638 \uC7AC\uC124\uC815",
      label__alternativeMethods: "\uB610\uB294 \uB2E4\uB978 \uBC29\uBC95\uC73C\uB85C \uB85C\uADF8\uC778",
      title: "\uBE44\uBC00\uBC88\uD638\uB97C \uC78A\uC73C\uC168\uB098\uC694?"
    },
    noAvailableMethods: {
      message: "\uB85C\uADF8\uC778\uC744 \uACC4\uC18D\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uC0AC\uC6A9 \uAC00\uB2A5\uD55C \uC778\uC99D \uBC29\uBC95\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.",
      subtitle: "\uC624\uB958\uAC00 \uBC1C\uC0DD\uD588\uC2B5\uB2C8\uB2E4",
      title: "\uB85C\uADF8\uC778\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4"
    },
    passkey: {
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: "\uB2E4\uB978 \uBC29\uBC95 \uC0AC\uC6A9\uD558\uAE30",
      subtitle: "\uACC4\uC815\uC5D0 \uB4F1\uB85D\uB41C \uBE44\uBC00\uBC88\uD638\uB97C \uC785\uB825\uD574 \uC8FC\uC138\uC694",
      title: "\uBE44\uBC00\uBC88\uD638\uB97C \uC785\uB825\uD558\uC138\uC694"
    },
    passwordPwned: {
      title: void 0
    },
    phoneCode: {
      formTitle: "\uC778\uC99D \uCF54\uB4DC",
      resendButton: "\uCF54\uB4DC \uB2E4\uC2DC \uBCF4\uB0B4\uAE30",
      subtitle: "{{applicationName}}\uB85C \uACC4\uC18D\uD558\uB824\uBA74",
      title: "\uD734\uB300\uD3F0 \uD655\uC778"
    },
    phoneCodeMfa: {
      formTitle: "\uC778\uC99D \uCF54\uB4DC",
      resendButton: "\uCF54\uB4DC \uB2E4\uC2DC \uBCF4\uB0B4\uAE30",
      subtitle: "\uACC4\uC18D\uD558\uB824\uBA74 \uD734\uB300\uD3F0\uC73C\uB85C \uC804\uC1A1\uB41C \uC778\uC99D \uCF54\uB4DC\uB97C \uC785\uB825\uD558\uC138\uC694",
      title: "\uD734\uB300\uD3F0 \uD655\uC778"
    },
    resetPassword: {
      formButtonPrimary: "\uBE44\uBC00\uBC88\uD638 \uC7AC\uC124\uC815",
      requiredMessage: "For security reasons, it is required to reset your password.",
      successMessage: "\uBE44\uBC00\uBC88\uD638\uAC00 \uC131\uACF5\uC801\uC73C\uB85C \uBCC0\uACBD\uB418\uC5C8\uC2B5\uB2C8\uB2E4. \uB85C\uADF8\uC778\uD558\uB294 \uC911\uC785\uB2C8\uB2E4. \uC7A0\uC2DC\uB9CC \uAE30\uB2E4\uB824\uC8FC\uC138\uC694.",
      title: "\uBE44\uBC00\uBC88\uD638 \uC7AC\uC124\uC815"
    },
    resetPasswordMfa: {
      detailsLabel: "\uBE44\uBC00\uBC88\uD638\uB97C \uC7AC\uC124\uC815\uD558\uAE30 \uC804\uC5D0 \uC2E0\uC6D0\uC744 \uD655\uC778\uD574\uC57C \uD569\uB2C8\uB2E4."
    },
    start: {
      actionLink: "\uD68C\uC6D0\uAC00\uC785",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "\uC774\uBA54\uC77C \uC0AC\uC6A9\uD558\uAE30",
      actionLink__use_email_username: "\uC774\uBA54\uC77C \uB610\uB294 \uC0AC\uC6A9\uC790 \uC774\uB984 \uC0AC\uC6A9\uD558\uAE30",
      actionLink__use_passkey: void 0,
      actionLink__use_phone: "\uD734\uB300\uD3F0 \uBC88\uD638 \uC0AC\uC6A9\uD558\uAE30",
      actionLink__use_username: "\uC0AC\uC6A9\uC790 \uC774\uB984 \uC0AC\uC6A9\uD558\uAE30",
      actionText: "\uACC4\uC815\uC774 \uC5C6\uC73C\uC2E0\uAC00\uC694?",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "\uD658\uC601\uD569\uB2C8\uB2E4! \uACC4\uC18D\uD558\uB824\uBA74 \uB85C\uADF8\uC778\uD574 \uC8FC\uC138\uC694",
      subtitleCombined: void 0,
      title: "{{applicationName}}\uC5D0 \uB85C\uADF8\uC778",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "\uC778\uC99D \uCF54\uB4DC",
      subtitle: "\uACC4\uC18D\uD558\uB824\uBA74 \uC778\uC99D \uC571\uC5D0\uC11C \uC0DD\uC131\uB41C \uC778\uC99D \uCF54\uB4DC\uB97C \uC785\uB825\uD558\uC138\uC694",
      title: "2\uB2E8\uACC4 \uC778\uC99D"
    }
  },
  signInEnterPasswordTitle: "\uBE44\uBC00\uBC88\uD638\uB97C \uC785\uB825\uD558\uC138\uC694",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "\uB85C\uADF8\uC778",
      actionText: "\uACC4\uC815\uC774 \uC788\uC73C\uC2E0\uAC00\uC694?",
      subtitle: "\uACC4\uC18D\uD558\uB824\uBA74 \uB204\uB77D\uB41C \uD544\uB4DC\uC5D0 \uAC12\uC744 \uC785\uB825\uD558\uC138\uC694",
      title: "\uB204\uB77D\uB41C \uD544\uB4DC\uB97C \uC785\uB825\uD558\uC138\uC694"
    },
    emailCode: {
      formSubtitle: "\uC774\uBA54\uC77C \uC8FC\uC18C\uB85C \uC804\uC1A1\uB41C \uC778\uC99D \uCF54\uB4DC\uB97C \uC785\uB825\uD558\uC138\uC694",
      formTitle: "\uC778\uC99D \uCF54\uB4DC",
      resendButton: "\uCF54\uB4DC \uC7AC\uC804\uC1A1",
      subtitle: "\uC774\uBA54\uC77C\uB85C \uC804\uC1A1\uB41C \uC778\uC99D \uCF54\uB4DC\uB97C \uC785\uB825\uD558\uC138\uC694",
      title: "\uC774\uBA54\uC77C \uC778\uC99D"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      formSubtitle: "\uC774\uBA54\uC77C \uC8FC\uC18C\uB85C \uC804\uC1A1\uB41C \uC778\uC99D \uB9C1\uD06C\uB97C \uC0AC\uC6A9\uD569\uB2C8\uB2E4.",
      formTitle: "\uC778\uC99D \uB9C1\uD06C",
      loading: {
        title: "\uAC00\uC785 \uC911..."
      },
      resendButton: "\uB9C1\uD06C \uC7AC\uC804\uC1A1",
      subtitle: "{{applicationName}}\uB85C \uACC4\uC18D\uD558\uB824\uBA74",
      title: "\uC774\uBA54\uC77C \uC778\uC99D\uD558\uAE30",
      verified: {
        title: "\uC131\uACF5\uC801\uC73C\uB85C \uAC00\uC785\uC5D0 \uC131\uACF5\uD588\uC2B5\uB2C8\uB2E4"
      },
      verifiedSwitchTab: {
        subtitle: "\uACC4\uC18D\uD558\uB824\uBA74 \uC0C8\uB85C \uC5F0 \uD0ED\uC73C\uB85C \uB3CC\uC544\uAC00\uAE30",
        subtitleNewTab: "\uACC4\uC18D\uD558\uB824\uBA74 \uC774\uC804 \uD0ED\uC73C\uB85C \uB3CC\uC544\uAC00\uAE30",
        title: "\uC774\uBA54\uC77C \uC778\uC99D \uC131\uACF5"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: void 0,
        label__onlyTermsOfService: void 0,
        label__termsOfServiceAndPrivacyPolicy: void 0
      },
      continue: {
        subtitle: void 0,
        title: void 0
      }
    },
    phoneCode: {
      formSubtitle: "\uD734\uB300\uD3F0 \uBC88\uD638\uB85C \uC804\uC1A1\uB41C \uC778\uC99D \uCF54\uB4DC\uB97C \uC785\uB825\uD558\uC138\uC694.",
      formTitle: "\uC778\uC99D \uCF54\uB4DC",
      resendButton: "\uCF54\uB4DC \uC7AC\uC804\uC1A1",
      subtitle: "\uD734\uB300\uD3F0\uC73C\uB85C \uC804\uC1A1\uB41C \uC778\uC99D \uCF54\uB4DC\uB97C \uC785\uB825\uD558\uC138\uC694",
      title: "\uD734\uB300\uD3F0 \uBC88\uD638 \uC778\uC99D"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "\uB85C\uADF8\uC778\uD558\uAE30",
      actionLink__use_email: void 0,
      actionLink__use_phone: void 0,
      actionText: "\uACC4\uC815\uC774 \uC788\uC73C\uC2E0\uAC00\uC694?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "\uD658\uC601\uD569\uB2C8\uB2E4! \uC544\uB798 \uC815\uBCF4\uB97C \uC785\uB825\uD574\uC8FC\uC138\uC694.",
      subtitleCombined: "\uD658\uC601\uD569\uB2C8\uB2E4! \uC544\uB798 \uC815\uBCF4\uB97C \uC785\uB825\uD574\uC8FC\uC138\uC694.",
      title: "\uACC4\uC815 \uB9CC\uB4E4\uAE30",
      titleCombined: "\uACC4\uC815 \uB9CC\uB4E4\uAE30"
    }
  },
  socialButtonsBlockButton: "{{provider|titleize}}\uB85C \uACC4\uC18D\uD558\uAE30",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "Sign up unsuccessful due to failed security validations. Please refresh the page to try again or reach out to support for more assistance.",
    captcha_unavailable: "Sign up unsuccessful due to failed bot validation. Please refresh the page to try again or reach out to support for more assistance.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: void 0,
    form_identifier_exists__phone_number: void 0,
    form_identifier_exists__username: void 0,
    form_identifier_not_found: "\uC774 \uC138\uBD80 \uC815\uBCF4\uC640 \uC77C\uCE58\uD558\uB294 \uACC4\uC815\uC744 \uCC3E\uC744 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.",
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: "Email address must be a valid email address.",
    form_param_format_invalid__phone_number: "Phone number must be in a valid international format",
    form_param_max_length_exceeded__first_name: "First name should not exceed 256 characters.",
    form_param_max_length_exceeded__last_name: "Last name should not exceed 256 characters.",
    form_param_max_length_exceeded__name: "Name should not exceed 256 characters.",
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: void 0,
    form_password_length_too_short: void 0,
    form_password_not_strong_enough: "\uBE44\uBC00\uBC88\uD638\uAC00 \uCDA9\uBD84\uD788 \uC548\uC804\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.",
    form_password_pwned: "\uC774 \uBE44\uBC00\uBC88\uD638\uB294 \uC720\uCD9C\uC0AC\uD56D\uC774 \uBC1C\uACAC\uB418\uC5B4 \uC0AC\uC6A9\uD560 \uC218 \uC5C6\uC73C\uBBC0\uB85C \uB300\uC2E0 \uB2E4\uB978 \uBE44\uBC00\uBC88\uD638\uB97C \uC0AC\uC6A9\uD574 \uBCF4\uC138\uC694.",
    form_password_pwned__sign_in: void 0,
    form_password_size_in_bytes_exceeded: "\uBE44\uBC00\uBC88\uD638\uAC00 \uD5C8\uC6A9\uB418\uB294 \uCD5C\uB300 \uBC14\uC774\uD2B8 \uC218\uB97C \uCD08\uACFC\uD588\uC2B5\uB2C8\uB2E4. \uBE44\uBC00\uBC88\uD638\uB97C \uC904\uC774\uAC70\uB098 \uC77C\uBD80 \uD2B9\uC218 \uBB38\uC790\uB97C \uC81C\uAC70\uD574 \uC8FC\uC138\uC694.",
    form_password_validation_failed: "\uC798\uBABB\uB41C \uBE44\uBC00\uBC88\uD638",
    form_username_invalid_character: void 0,
    form_username_invalid_length: void 0,
    identification_deletion_failed: "You cannot delete your last identification.",
    not_allowed_access: "\uC774\uBA54\uC77C \uC8FC\uC18C \uB610\uB294 \uC804\uD654\uBC88\uD638\uB294 \uAC00\uC785\uC5D0 \uC0AC\uC6A9\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uC774\uB294 '+', '=', '#' \uB610\uB294 '.'\uC774 \uC774\uBA54\uC77C \uC8FC\uC18C\uC5D0 \uC0AC\uC6A9\uB418\uC5C8\uAC70\uB098 \uC784\uC2DC \uC774\uBA54\uC77C \uC11C\uBE44\uC2A4\uC5D0 \uC5F0\uACB0\uB41C \uB3C4\uBA54\uC778\uC774 \uC0AC\uC6A9\uB418\uC5C8\uAC70\uB098 \uBA85\uC2DC\uC801 \uC81C\uC678\uAC00 \uC788\uB294 \uACBD\uC6B0\uC785\uB2C8\uB2E4. \uC774 \uC624\uB958\uAC00 \uBC1C\uC0DD\uD55C \uACBD\uC6B0 \uC9C0\uC6D0\uC5D0 \uBB38\uC758\uD558\uC138\uC694.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: void 0,
    passkey_not_supported: void 0,
    passkey_pa_not_supported: void 0,
    passkey_registration_cancelled: void 0,
    passkey_retrieval_cancelled: void 0,
    passwordComplexity: {
      maximumLength: "{{length}} \uBCF4\uB2E4 \uC9E7\uC740 \uBB38\uC790\uC5F4",
      minimumLength: "{{length}} \uB610\uB294 \uADF8 \uC774\uC0C1\uC758 \uBB38\uC790\uC5F4",
      requireLowercase: "\uC18C\uBB38\uC790",
      requireNumbers: "\uC22B\uC790",
      requireSpecialCharacter: "\uD2B9\uC218\uBB38\uC790",
      requireUppercase: "\uB300\uBB38\uC790",
      sentencePrefix: "\uB2F9\uC2E0\uC758 \uBE44\uBC00\uBC88\uD638\uB294 \uBC18\uB4DC\uC2DC \uD3EC\uD568\uD574\uC57C\uD569\uB2C8\uB2E4"
    },
    phone_number_exists: "\uC774 \uC804\uD654\uBC88\uD638\uB294 \uC774\uBBF8 \uC0AC\uC6A9\uC911\uC785\uB2C8\uB2E4. \uB2E4\uB978 \uBC88\uD638\uB97C \uC2DC\uB3C4\uD574 \uC8FC\uC138\uC694.",
    session_exists: "\uC774\uBBF8 \uB85C\uADF8\uC778 \uC911\uC785\uB2C8\uB2E4.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "\uBE44\uBC00\uBC88\uD638\uB294 \uC791\uB3D9\uD558\uC9C0\uB9CC \uB354 \uAC15\uB825\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4. \uBB38\uC790\uB97C \uB354 \uCD94\uAC00\uD574 \uBCF4\uC138\uC694.",
      goodPassword: "\uC218\uACE0\uD558\uC168\uC2B5\uB2C8\uB2E4. \uD6CC\uB96D\uD55C \uBE44\uBC00\uBC88\uD638\uC785\uB2C8\uB2E4.",
      notEnough: "\uBE44\uBC00\uBC88\uD638\uAC00 \uCDA9\uBD84\uD788 \uC548\uC804\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.",
      suggestions: {
        allUppercase: "\uBAA8\uB4E0 \uBB38\uC790\uAC00 \uC544\uB2CC \uC77C\uBD80 \uBB38\uC790\uB97C \uB300\uBB38\uC790\uB85C \uD45C\uC2DC\uD569\uB2C8\uB2E4.",
        anotherWord: "\uB35C \uC77C\uBC18\uC801\uC778 \uB2E8\uC5B4\uB97C \uB354 \uCD94\uAC00\uD569\uB2C8\uB2E4.",
        associatedYears: "\uADC0\uD558\uC640 \uC5F0\uAD00\uB41C \uC5F0\uB3C4\uB294 \uD53C\uD558\uC138\uC694.",
        capitalization: "\uD55C \uAE00\uC790 \uC774\uC0C1\uC744 \uB300\uBB38\uC790\uB85C \uD45C\uAE30\uD558\uC138\uC694.",
        dates: "\uC790\uC2E0\uACFC \uAD00\uB828\uB41C \uB0A0\uC9DC\uC640 \uC5F0\uB3C4\uB294 \uD53C\uD558\uC138\uC694.",
        l33t: "'a'\uC5D0\uC11C '@'\uC640 \uAC19\uC774 \uC608\uCE21 \uAC00\uB2A5\uD55C \uBB38\uC790 \uB300\uCCB4\uB97C \uD53C\uD558\uC138\uC694.",
        longerKeyboardPattern: "\uB354 \uAE34 \uD0A4\uBCF4\uB4DC \uD328\uD134\uC744 \uC0AC\uC6A9\uD558\uACE0 \uD0C0\uC774\uD551 \uBC29\uD5A5\uC744 \uC5EC\uB7EC \uBC88 \uBCC0\uACBD\uD569\uB2C8\uB2E4.",
        noNeed: "\uAE30\uD638, \uC22B\uC790 \uB610\uB294 \uB300\uBB38\uC790\uB97C \uC0AC\uC6A9\uD558\uC9C0 \uC54A\uACE0\uB3C4 \uAC15\uB825\uD55C \uBE44\uBC00\uBC88\uD638\uB97C \uB9CC\uB4E4 \uC218 \uC788\uC2B5\uB2C8\uB2E4.",
        pwned: "\uC774 \uBE44\uBC00\uBC88\uD638\uB97C \uB2E4\uB978 \uACF3\uC5D0\uC11C \uC0AC\uC6A9\uD558\uB294 \uACBD\uC6B0 \uBCC0\uACBD\uD574\uC57C \uD569\uB2C8\uB2E4.",
        recentYears: "\uCD5C\uADFC \uC5F0\uB3C4\uB294 \uD53C\uD558\uC138\uC694.",
        repeated: "\uBC18\uBCF5\uB418\uB294 \uB2E8\uC5B4\uC640 \uBB38\uC790\uB97C \uD53C\uD558\uC138\uC694.",
        reverseWords: "\uC77C\uBC18\uC801\uC778 \uB2E8\uC5B4\uC758 \uCCA0\uC790\uB97C \uAC70\uAFB8\uB85C \uC4F0\uC9C0 \uB9C8\uC138\uC694.",
        sequences: "\uC77C\uBC18\uC801\uC778 \uBB38\uC790 \uC2DC\uD000\uC2A4\uB97C \uD53C\uD558\uC138\uC694.",
        useWords: "\uC5EC\uB7EC \uB2E8\uC5B4\uB97C \uC0AC\uC6A9\uD558\uB418 \uC77C\uBC18\uC801\uC778 \uBB38\uAD6C\uB294 \uD53C\uD558\uC138\uC694."
      },
      warnings: {
        common: "\uC77C\uBC18\uC801\uC73C\uB85C \uC0AC\uC6A9\uB418\uB294 \uBE44\uBC00\uBC88\uD638\uC785\uB2C8\uB2E4.",
        commonNames: "\uC77C\uBC18\uC801\uC778 \uC774\uB984\uACFC \uC131\uC740 \uCD94\uCE21\uD558\uAE30 \uC27D\uC2B5\uB2C8\uB2E4.",
        dates: "\uB0A0\uC9DC\uB294 \uC27D\uAC8C \uCD94\uCE21\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.",
        extendedRepeat: '"abcabcabc"\uC640 \uAC19\uC774 \uBC18\uBCF5\uB418\uB294 \uBB38\uC790 \uD328\uD134\uC740 \uC27D\uAC8C \uCD94\uCE21\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.',
        keyPattern: "\uC9E7\uC740 \uD328\uD134\uC740 \uCD94\uCE21\uD558\uAE30 \uC27D\uC2B5\uB2C8\uB2E4.",
        namesByThemselves: "\uB2E8\uC77C \uC774\uB984\uC774\uB098 \uC131\uC740 \uCD94\uCE21\uD558\uAE30 \uC27D\uC2B5\uB2C8\uB2E4.",
        pwned: "\uC778\uD130\uB137\uC5D0\uC11C \uB370\uC774\uD130 \uC720\uCD9C\uB85C \uC778\uD574 \uBE44\uBC00\uBC88\uD638\uAC00 \uB178\uCD9C\uB418\uC5C8\uC2B5\uB2C8\uB2E4.",
        recentYears: "\uCD5C\uADFC\uC5F0\uB3C4\uB294 \uC27D\uAC8C \uCD94\uCE21\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.",
        sequences: '"abc"\uC640 \uAC19\uC740 \uC77C\uBC18\uC801\uC778 \uBB38\uC790 \uC2DC\uD000\uC2A4\uB294 \uC27D\uAC8C \uCD94\uCE21\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.',
        similarToCommon: "\uC77C\uBC18\uC801\uC73C\uB85C \uC0AC\uC6A9\uB418\uB294 \uBE44\uBC00\uBC88\uD638\uC640 \uC720\uC0AC\uD569\uB2C8\uB2E4.",
        simpleRepeat: '"aaa"\uC640 \uAC19\uC774 \uBC18\uBCF5\uB418\uB294 \uBB38\uC790\uB294 \uC27D\uAC8C \uCD94\uCE21\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.',
        straightRow: "\uAC19\uC740 \uC904\uC5D0 \uC704\uCE58\uD55C \uD0A4\uBCF4\uB4DC\uB97C \uC0AC\uC6A9\uD558\uB294 \uAC83\uC740 \uCD94\uCE21\uD558\uAE30 \uC27D\uC2B5\uB2C8\uB2E4.",
        topHundred: "\uC790\uC8FC \uC0AC\uC6A9\uB418\uB294 \uBE44\uBC00\uBC88\uD638\uC785\uB2C8\uB2E4.",
        topTen: "\uC544\uC8FC \uB9CE\uC774 \uC0AC\uC6A9\uB418\uB294 \uBE44\uBC00\uBC88\uD638\uC785\uB2C8\uB2E4.",
        userInputs: "\uAC1C\uC778 \uC815\uBCF4\uB098 \uD398\uC774\uC9C0 \uAD00\uB828 \uB370\uC774\uD130\uAC00 \uC5C6\uC5B4\uC57C \uD569\uB2C8\uB2E4.",
        wordByItself: "\uD55C \uB2E8\uC5B4\uB294 \uC27D\uAC8C \uCD94\uCE21\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4."
      }
    }
  },
  userButton: {
    action__addAccount: "\uACC4\uC815 \uCD94\uAC00",
    action__manageAccount: "\uACC4\uC815 \uAD00\uB9AC",
    action__signOut: "\uB85C\uADF8\uC544\uC6C3",
    action__signOutAll: "\uBAA8\uB4E0 \uACC4\uC815\uC5D0\uC11C \uB85C\uADF8\uC544\uC6C3"
  },
  userProfile: {
    backupCodePage: {
      actionLabel__copied: "\uBCF5\uC0AC \uC644\uB8CC!",
      actionLabel__copy: "\uC804\uCCB4 \uBCF5\uC0AC",
      actionLabel__download: ".txt \uB2E4\uC6B4\uB85C\uB4DC",
      actionLabel__print: "\uC778\uC1C4",
      infoText1: "\uC774 \uACC4\uC815\uC5D0 \uB300\uD574 \uBC31\uC5C5 \uCF54\uB4DC\uAC00 \uD65C\uC131\uD654\uB429\uB2C8\uB2E4.",
      infoText2: "\uBC31\uC5C5 \uCF54\uB4DC\uB97C \uBE44\uBC00\uB85C \uC720\uC9C0\uD558\uACE0 \uC548\uC804\uD558\uAC8C \uBCF4\uAD00\uD558\uC138\uC694. \uBC31\uC5C5 \uCF54\uB4DC\uAC00 \uC190\uC0C1\uB41C \uAC83\uC73C\uB85C \uC758\uC2EC\uB418\uB294 \uACBD\uC6B0 \uBC31\uC5C5 \uCF54\uB4DC\uB97C \uB2E4\uC2DC \uC0DD\uC131\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.",
      subtitle__codelist: "\uC548\uC804\uD558\uAC8C \uC800\uC7A5\uD558\uACE0 \uBE44\uBC00\uB85C \uC720\uC9C0\uD558\uC138\uC694.",
      successMessage: "\uC774\uC81C \uBC31\uC5C5 \uCF54\uB4DC\uAC00 \uD65C\uC131\uD654\uB418\uC5C8\uC2B5\uB2C8\uB2E4. \uC778\uC99D \uC7A5\uCE58\uC5D0 \uC561\uC138\uC2A4\uD560 \uC218 \uC5C6\uB294 \uACBD\uC6B0 \uC774 \uC911 \uD558\uB098\uB97C \uC0AC\uC6A9\uD558\uC5EC \uACC4\uC815\uC5D0 \uB85C\uADF8\uC778\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4. \uAC01 \uCF54\uB4DC\uB294 \uD55C \uBC88\uB9CC \uC0AC\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.",
      successSubtitle: "\uC778\uC99D \uC7A5\uCE58\uC5D0 \uC561\uC138\uC2A4\uD560 \uC218 \uC5C6\uB294 \uACBD\uC6B0 \uC774 \uC911 \uD558\uB098\uB97C \uC0AC\uC6A9\uD558\uC5EC \uACC4\uC815\uC5D0 \uB85C\uADF8\uC778\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.",
      title: "\uBC31\uC5C5 \uCF54\uB4DC \uC778\uC99D \uCD94\uAC00",
      title__codelist: "\uBC31\uC5C5 \uCF54\uB4DC"
    },
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "\uACC4\uC815\uC744 \uC5F0\uACB0\uD560 \uC81C\uACF5\uC790\uB97C \uC120\uD0DD\uD558\uC138\uC694",
      formHint__noAccounts: "\uC0AC\uC6A9 \uAC00\uB2A5\uD55C \uC678\uBD80 \uACC4\uC815 \uC81C\uACF5\uC790\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4.",
      removeResource: {
        messageLine1: "{{identifier}}\uAC00 \uC774 \uACC4\uC815\uC5D0\uC11C \uC81C\uAC70 \uB420 \uC608\uC815\uC785\uB2C8\uB2E4.",
        messageLine2: "\uC774 \uC5F0\uACB0\uB41C \uACC4\uC815\uC744 \uB354 \uC774\uC0C1 \uC0AC\uC6A9\uD560 \uC218 \uC5C6\uC73C\uBA70 \uC885\uC18D\uB41C \uBAA8\uB4E0 \uAE30\uB2A5\uC774 \uB354 \uC774\uC0C1 \uC791\uB3D9\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.",
        successMessage: "{{connectedAccount}}\uAC00 \uB2F9\uC2E0\uC758 \uACC4\uC815\uC5D0\uC11C \uC81C\uAC70\uB418\uC5C8\uC2B5\uB2C8\uB2E4.",
        title: "\uC5F0\uACB0\uB41C \uACC4\uC815 \uC81C\uAC70"
      },
      socialButtonsBlockButton: "{{provider|titleize}} \uACC4\uC815 \uC5F0\uACB0",
      successMessage: "\uC774 \uC81C\uACF5\uC790\uAC00 \uACC4\uC815\uC5D0 \uCD94\uAC00\uB418\uC5C8\uC2B5\uB2C8\uB2E4.",
      title: "\uC5F0\uACB0\uB41C \uACC4\uC815 \uCD94\uAC00\uD558\uAE30"
    },
    deletePage: {
      actionDescription: "\uACC4\uC18D\uD558\uB824\uBA74 \uC544\uB798\uC5D0 \uACC4\uC815 \uC0AD\uC81C\uB97C \uC785\uB825\uD558\uC138\uC694.",
      confirm: "\uACC4\uC815 \uC0AD\uC81C",
      messageLine1: "\uC815\uB9D0\uB85C \uACC4\uC815\uC744 \uC0AD\uC81C\uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C?",
      messageLine2: "\uC774 \uC791\uC5C5\uC740 \uC601\uAD6C\uC801\uC774\uBA70 \uB418\uB3CC\uB9B4 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.",
      title: "\uACC4\uC815 \uC0AD\uC81C"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "\uC774 \uC774\uBA54\uC77C \uC8FC\uC18C\uB85C \uC778\uC99D \uCF54\uB4DC\uAC00 \uD3EC\uD568\uB41C \uC774\uBA54\uC77C\uC774 \uC804\uC1A1\uB429\uB2C8\uB2E4.",
        formSubtitle: "{{identifier}}\uC5D0\uAC8C \uC804\uC1A1\uB41C \uC778\uC99D \uCF54\uB4DC\uB97C \uC785\uB825\uD569\uB2C8\uB2E4",
        formTitle: "\uC778\uC99D \uCF54\uB4DC",
        resendButton: "\uCF54\uB4DC \uC7AC\uC804\uC1A1",
        successMessage: "{{identifier}} \uC774\uBA54\uC77C\uC774 \uB2F9\uC2E0\uC758 \uACC4\uC815\uC5D0 \uCD94\uAC00\uB418\uC5C8\uC2B5\uB2C8\uB2E4."
      },
      emailLink: {
        formHint: "\uC778\uC99D \uB9C1\uD06C\uAC00 \uD3EC\uD568\uB41C \uC774\uBA54\uC77C\uC774 \uC774 \uC774\uBA54\uC77C \uC8FC\uC18C\uB85C \uC804\uC1A1\uB429\uB2C8\uB2E4.",
        formSubtitle: "{{identifier}}\uC5D0\uAC8C \uC804\uC1A1\uB41C \uC774\uBA54\uC77C\uC758 \uB9C1\uD06C\uB97C \uD074\uB9AD\uD569\uB2C8\uB2E4.",
        formTitle: "\uC778\uC99D \uB9C1\uD06C",
        resendButton: "\uB9C1\uD06C \uC7AC\uC804\uC1A1",
        successMessage: "{{identifier}} \uC774\uBA54\uC77C\uC774 \uB2F9\uC2E0\uC758 \uACC4\uC815\uC5D0 \uCD94\uAC00\uB418\uC5C8\uC2B5\uB2C8\uB2E4."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} \uC774\uBA54\uC77C\uC774 \uC774 \uACC4\uC815\uC5D0\uC11C \uC81C\uAC70 \uB420 \uC5D0\uC815\uC785\uB2C8\uB2E4.",
        messageLine2: "\uB354 \uC774\uC0C1 \uC774 \uC774\uBA54\uC77C \uC8FC\uC18C\uB85C \uB85C\uADF8\uC778\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.",
        successMessage: "{{emailAddress}} \uC774\uBA54\uC77C\uC774 \uB2F9\uC2E0\uC758 \uACC4\uC815\uC5D0\uC11C \uC0AD\uC81C\uB418\uC5C8\uC2B5\uB2C8\uB2E4",
        title: "\uC774\uBA54\uC77C \uC81C\uAC70"
      },
      title: "\uC774\uBA54\uC77C \uC8FC\uC18C \uCD94\uAC00",
      verifyTitle: "Verify email address"
    },
    formButtonPrimary__add: "Add",
    formButtonPrimary__continue: "\uACC4\uC18D",
    formButtonPrimary__finish: "\uC644\uB8CC",
    formButtonPrimary__remove: "Remove",
    formButtonPrimary__save: "Save",
    formButtonReset: "\uCDE8\uC18C",
    mfaPage: {
      formHint: "\uCD94\uAC00\uD560 \uBC29\uBC95\uC744 \uC120\uD0DD\uD569\uB2C8\uB2E4.",
      title: "2\uB2E8\uACC4 \uC778\uC99D \uCD94\uAC00"
    },
    mfaPhoneCodePage: {
      backButton: "Use existing number",
      primaryButton__addPhoneNumber: "\uD734\uB300\uD3F0 \uBC88\uD638 \uCD94\uAC00",
      removeResource: {
        messageLine1: "{{identifier}}\uB294 \uB85C\uADF8\uC778\uD560 \uB54C \uB354 \uC774\uC0C1 \uC778\uC99D \uCF54\uB4DC\uB97C \uBC1B\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.",
        messageLine2: "\uACC4\uC815\uC774 \uC548\uC804\uD558\uC9C0 \uC54A\uC744 \uC218 \uC788\uC2B5\uB2C8\uB2E4. \uACC4\uC18D\uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C?",
        successMessage: "{{mfaPhoneCode}}\uC5D0 \uB300\uD55C SMS \uCF54\uB4DC 2\uB2E8\uACC4 \uC778\uC99D\uC774 \uC81C\uAC70\uB418\uC5C8\uC2B5\uB2C8\uB2E4",
        title: "2\uB2E8\uACC4 \uC778\uC99D \uC81C\uAC70"
      },
      subtitle__availablePhoneNumbers: "SMS \uCF54\uB4DC 2\uB2E8\uACC4 \uC778\uC99D\uC744 \uC704\uD574 \uB4F1\uB85D\uD560 \uD734\uB300\uD3F0 \uBC88\uD638\uB97C \uC120\uD0DD\uD558\uC138\uC694.",
      subtitle__unavailablePhoneNumbers: "SMS \uCF54\uB4DC 2\uB2E8\uACC4 \uC778\uC99D\uC5D0 \uB4F1\uB85D\uD560 \uC218 \uC788\uB294 \uD734\uB300\uD3F0 \uBC88\uD638\uAC00 \uC5C6\uC2B5\uB2C8\uB2E4.",
      successMessage1: "When signing in, you will need to enter a verification code sent to this phone number as an additional step.",
      successMessage2: "Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.",
      successTitle: "SMS code verification enabled",
      title: "SMS \uCF54\uB4DC \uC778\uC99D \uCD94\uAC00"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "\uB300\uC2E0 QR \uCF54\uB4DC \uC2A4\uCE94 \uD558\uC138\uC694",
        buttonUnableToScan__nonPrimary: "QR \uCF54\uB4DC\uB97C \uC2A4\uCE94\uD560 \uC218 \uC5C6\uB098\uC694?",
        infoText__ableToScan: "\uC778\uC99D \uC571\uC5D0\uC11C \uC0C8 \uB85C\uADF8\uC778 \uBC29\uBC95\uC744 \uC124\uC815\uD558\uACE0 \uB2E4\uC74C QR \uCF54\uB4DC\uB97C \uC2A4\uCE94\uD558\uC5EC \uACC4\uC815\uC5D0 \uC5F0\uACB0\uD569\uB2C8\uB2E4.",
        infoText__unableToScan: "\uC778\uC99D \uC7A5\uCE58\uC5D0\uC11C \uC0C8 \uB85C\uADF8\uC778 \uBC29\uBC95\uC744 \uC124\uC815\uD558\uACE0 \uC544\uB798\uC5D0 \uC81C\uACF5\uB41C \uD0A4\uB97C \uC785\uB825\uD569\uB2C8\uB2E4.",
        inputLabel__unableToScan1: "\uC2DC\uAC04 \uAE30\uBC18 \uB610\uB294 \uC77C\uD68C\uC6A9 \uBE44\uBC00\uBC88\uD638\uAC00 \uC0AC\uC6A9 \uC124\uC815\uB418\uC5B4 \uC788\uB294\uC9C0 \uD655\uC778\uD55C \uB2E4\uC74C \uACC4\uC815 \uC5F0\uACB0\uC744 \uC644\uB8CC\uD569\uB2C8\uB2E4.",
        inputLabel__unableToScan2: "\uB610\uB294 \uC778\uC99D\uC790\uAC00 TOTP URI\uB97C \uC9C0\uC6D0\uD558\uB294 \uACBD\uC6B0 \uC804\uCCB4 URI\uB97C \uBCF5\uC0AC\uD560 \uC218\uB3C4 \uC788\uC2B5\uB2C8\uB2E4."
      },
      removeResource: {
        messageLine1: "\uB85C\uADF8\uC778\uD560 \uB54C \uB354 \uC774\uC0C1 \uC774 \uC778\uC99D\uC790\uC758 \uC778\uC99D \uCF54\uB4DC\uAC00 \uD544\uC694\uD558\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.",
        messageLine2: "\uACC4\uC815\uC774 \uC548\uC804\uD558\uC9C0 \uC54A\uC744 \uC218 \uC788\uC2B5\uB2C8\uB2E4. \uACC4\uC18D \uC9C4\uD589\uD558\uC2DC\uACA0\uC2B5\uB2C8\uAE4C?",
        successMessage: "\uC778\uC99D\uC790 \uC560\uD50C\uB9AC\uCF00\uC774\uC158\uC744 \uD1B5\uD55C 2\uB2E8\uACC4 \uC778\uC99D\uC774 \uC81C\uAC70\uB418\uC5C8\uC2B5\uB2C8\uB2E4.",
        title: "2\uB2E8\uACC4 \uC778\uC99D \uC81C\uAC70"
      },
      successMessage: "\uC774\uC81C 2\uB2E8\uACC4 \uC778\uC99D\uC774 \uD65C\uC131\uD654\uB418\uC5C8\uC2B5\uB2C8\uB2E4. \uB85C\uADF8\uC778\uD560 \uB54C \uCD94\uAC00 \uB2E8\uACC4\uB85C \uC774 \uC778\uC99D\uC790\uC758 \uC778\uC99D \uCF54\uB4DC\uB97C \uC785\uB825\uD574\uC57C \uD569\uB2C8\uB2E4.",
      title: "\uC778\uC99D \uC560\uD50C\uB9AC\uCF00\uC774\uC158 \uCD94\uAC00",
      verifySubtitle: "\uC778\uC99D\uC790\uAC00 \uC0DD\uC131\uD55C \uC778\uC99D \uCF54\uB4DC\uB97C \uC785\uB825\uD569\uB2C8\uB2E4",
      verifyTitle: "\uC778\uC99D \uCF54\uB4DC"
    },
    mobileButton__menu: "\uBA54\uB274",
    navbar: {
      account: "\uD504\uB85C\uD544",
      billing: void 0,
      description: "\uACC4\uC815 \uC815\uBCF4\uB97C \uAD00\uB9AC\uD558\uC138\uC694.",
      security: "\uBCF4\uC548",
      title: "\uACC4\uC815"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: void 0,
        title: void 0
      },
      subtitle__rename: void 0,
      title__rename: void 0
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "It is recommended to sign out of all other devices which may have used your old password.",
      readonly: "Your password can currently not be edited because you can sign in only via the enterprise connection.",
      successMessage__set: "\uBE44\uBC00\uBC88\uD638\uAC00 \uC124\uC815\uB418\uC5C8\uC2B5\uB2C8\uB2E4.",
      successMessage__signOutOfOtherSessions: "\uB2E4\uB978 \uBAA8\uB4E0 \uAE30\uAE30\uB294 \uB85C\uADF8\uC544\uC6C3\uB418\uC5C8\uC2B5\uB2C8\uB2E4.",
      successMessage__update: "\uBE44\uBC00\uBC88\uD638\uAC00 \uC5C5\uB370\uC774\uD2B8\uB418\uC5C8\uC2B5\uB2C8\uB2E4.",
      title__set: "\uBE44\uBC00\uBC88\uD638 \uC124\uC815",
      title__update: "\uBE44\uBC00\uBC88\uD638 \uBCC0\uACBD"
    },
    phoneNumberPage: {
      infoText: "\uC778\uC99D \uB9C1\uD06C\uAC00 \uD3EC\uD568\uB41C \uBB38\uC790 \uBA54\uC2DC\uC9C0\uAC00 \uC774 \uD734\uB300\uD3F0 \uBC88\uD638\uB85C \uC804\uC1A1\uB429\uB2C8\uB2E4.",
      removeResource: {
        messageLine1: "{{identifier}}\uAC00 \uB2F9\uC2E0\uC758 \uACC4\uC815\uC5D0\uC11C \uC81C\uAC70 \uB420 \uC608\uC815\uC785\uB2C8\uB2E4.",
        messageLine2: "\uB354 \uC774\uC0C1 \uC774 \uD734\uB300\uD3F0 \uBC88\uD638\uB85C \uB85C\uADF8\uC778\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.",
        successMessage: "{{phoneNumber}}\uAC00 \uB2F9\uC2E0\uC758 \uACC4\uC815\uC5D0\uC11C \uC81C\uAC70\uB418\uC5C8\uC2B5\uB2C8\uB2E4.",
        title: "\uD734\uB300\uD3F0 \uBC88\uD638 \uC81C\uAC70"
      },
      successMessage: "{{identifier}}\uAC00 \uB2F9\uC2E0\uC758 \uACC4\uC815\uC5D0 \uCD94\uAC00\uB418\uC5C8\uC2B5\uB2C8\uB2E4.",
      title: "\uD734\uB300\uD3F0 \uBC88\uD638 \uCD94\uAC00",
      verifySubtitle: "Enter the verification code sent to {{identifier}}",
      verifyTitle: "Verify phone number"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "10MB\uBCF4\uB2E4 \uC791\uC740 JPG, PNG, GIF \uB610\uB294 WEBP \uC774\uBBF8\uC9C0\uB97C \uC5C5\uB85C\uB4DC\uD569\uB2C8\uB2E4",
      imageFormDestructiveActionSubtitle: "\uC774\uBBF8\uC9C0 \uC81C\uAC70",
      imageFormSubtitle: "\uC774\uBBF8\uC9C0 \uC5C5\uB85C\uB4DC",
      imageFormTitle: "\uD504\uB85C\uD544 \uC774\uBBF8\uC9C0",
      readonly: "Your profile information has been provided by the enterprise connection and cannot be edited.",
      successMessage: "\uD504\uB85C\uD544\uC774 \uC5C5\uB370\uC774\uD2B8\uB418\uC5C8\uC2B5\uB2C8\uB2E4.",
      title: "\uD504\uB85C\uD544 \uC5C5\uB370\uC774\uD2B8"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "\uC774 \uC7A5\uCE58\uC5D0\uC11C \uB85C\uADF8\uC544\uC6C3",
        title: "\uD65C\uC131\uD654\uB41C \uC7A5\uCE58"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "\uC7AC\uC2DC\uB3C4",
        actionLabel__reauthorize: "\uC9C0\uAE08 \uC778\uC99D\uD558\uAE30",
        destructiveActionTitle: "\uC81C\uAC70",
        primaryButton: "\uACC4\uC815 \uC5F0\uACB0\uD558\uAE30",
        subtitle__disconnected: void 0,
        subtitle__reauthorize: "The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues",
        title: "\uC5F0\uACB0\uB41C \uACC4\uC815"
      },
      dangerSection: {
        deleteAccountButton: "\uACC4\uC815 \uC0AD\uC81C",
        title: "\uC704\uD5D8"
      },
      emailAddressesSection: {
        destructiveAction: "\uC774\uBA54\uC77C \uC8FC\uC18C \uC81C\uAC70",
        detailsAction__nonPrimary: "\uAE30\uBCF8\uC73C\uB85C \uC124\uC815",
        detailsAction__primary: "\uC778\uC99D \uC644\uB8CC",
        detailsAction__unverified: "\uC778\uC99D \uC644\uB8CC",
        primaryButton: "\uC774\uBA54\uC77C \uC8FC\uC18C \uCD94\uAC00",
        title: "\uC774\uBA54\uC77C \uC8FC\uC18C"
      },
      enterpriseAccountsSection: {
        title: "\uAE30\uC5C5 \uACC4\uC815"
      },
      headerTitle__account: "\uD504\uB85C\uD544",
      headerTitle__security: "\uBCF4\uC548",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "\uCF54\uB4DC \uC7AC\uC0DD\uC131",
          headerTitle: "\uBC31\uC5C5 \uCF54\uB4DC",
          subtitle__regenerate: "\uC0C8\uB85C\uC6B4 \uBCF4\uC548 \uBC31\uC5C5 \uCF54\uB4DC \uC138\uD2B8\uB97C \uBC1B\uC73C\uC138\uC694. \uC774\uC804 \uBC31\uC5C5 \uCF54\uB4DC\uB294 \uC0AD\uC81C\uB418\uBA70 \uC0AC\uC6A9\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.",
          title__regenerate: "\uBC31\uC5C5 \uCF54\uB4DC \uC7AC\uC0DD\uC131"
        },
        phoneCode: {
          actionLabel__setDefault: "\uAE30\uBCF8\uC73C\uB85C \uC124\uC815",
          destructiveActionLabel: "\uD734\uB300\uD3F0 \uBC88\uD638 \uC81C\uAC70"
        },
        primaryButton: "2\uB2E8\uACC4 \uC778\uC99D \uCD94\uAC00",
        title: "2\uB2E8\uACC4 \uC778\uC99D",
        totp: {
          destructiveActionTitle: "\uC81C\uAC70",
          headerTitle: "\uC778\uC99D \uC560\uD50C\uB9AC\uCF00\uC774\uC158"
        }
      },
      passkeysSection: {
        menuAction__destructive: void 0,
        menuAction__rename: void 0,
        primaryButton: void 0,
        title: void 0
      },
      passwordSection: {
        primaryButton__setPassword: "\uBE44\uBC00\uBC88\uD638 \uC124\uC815",
        primaryButton__updatePassword: "\uBE44\uBC00\uBC88\uD638 \uBCC0\uACBD",
        title: "\uBE44\uBC00\uBC88\uD638"
      },
      phoneNumbersSection: {
        destructiveAction: "\uD734\uB300\uD3F0 \uBC88\uD638 \uC81C\uAC70",
        detailsAction__nonPrimary: "\uAE30\uBCF8\uC73C\uB85C \uC124\uC815",
        detailsAction__primary: "\uC778\uC99D \uC644\uB8CC",
        detailsAction__unverified: "\uC778\uC99D \uC644\uB8CC",
        primaryButton: "\uD734\uB300\uD3F0 \uBC88\uD638 \uCD94\uAC00",
        title: "\uD734\uB300\uD3F0 \uBC88\uD638"
      },
      profileSection: {
        primaryButton: "\uD504\uB85C\uD544 \uC218\uC815",
        title: "\uD504\uB85C\uD544"
      },
      usernameSection: {
        primaryButton__setUsername: "\uC0AC\uC6A9\uC790 \uC774\uB984 \uC124\uC815",
        primaryButton__updateUsername: "\uC0AC\uC6A9\uC790 \uC774\uB984 \uBCC0\uACBD",
        title: "\uC0AC\uC6A9\uC790 \uC774\uB984"
      },
      web3WalletsSection: {
        destructiveAction: "\uC9C0\uAC11 \uC81C\uAC70",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Web3 \uC9C0\uAC11",
        title: "Web3 \uC9C0\uAC11"
      }
    },
    usernamePage: {
      successMessage: "\uB2F9\uC2E0\uC76D \uC0AC\uC6A9\uC790 \uC774\uB984\uC774 \uC5C5\uB370\uC774\uD2B8\uB418\uC5C8\uC2B5\uB2C8\uB2E4.",
      title__set: "\uC0AC\uC6A9\uC790 \uC774\uB984 \uC5C5\uB370\uC774\uD2B8",
      title__update: "\uC0AC\uC6A9\uC790 \uC774\uB984 \uC5C5\uB370\uC774\uD2B8"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}}\uAC00 \uC774 \uACC4\uC815\uC5D0\uC11C \uC81C\uAC70 \uB420 \uC608\uC815\uC785\uB2C8\uB2E4.",
        messageLine2: "\uB354 \uC774\uC0C1 \uC774 web3 \uC9C0\uAC11\uC744 \uC0AC\uC6A9\uD558\uC5EC \uB85C\uADF8\uC778\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.",
        successMessage: "{{web3Wallet}}\uAC00 \uB2F9\uC2E0\uC758 \uACC4\uC815\uC5D0\uC11C \uC81C\uAC70\uB418\uC5C8\uC2B5\uB2C8\uB2E4.",
        title: "web3 \uC9C0\uAC11 \uC81C\uAC70"
      },
      subtitle__availableWallets: "\uACC4\uC815\uC5D0 \uC5F0\uACB0\uD560 web3 \uC9C0\uAC11\uC744 \uC120\uD0DD\uD558\uC138\uC694.",
      subtitle__unavailableWallets: "\uC0AC\uC6A9 \uAC00\uB2A5\uD55C web3 \uC9C0\uAC11\uC774 \uC5C6\uC2B5\uB2C8\uB2E4.",
      successMessage: "\uC9C0\uAC11\uC774 \uACC4\uC815\uC5D0 \uCD94\uAC00\uB418\uC5C8\uC2B5\uB2C8\uB2E4\xDF.",
      title: "web3 \uC9C0\uAC11 \uCD94\uAC00",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  koKR
};
//# sourceMappingURL=ko-KR.mjs.map