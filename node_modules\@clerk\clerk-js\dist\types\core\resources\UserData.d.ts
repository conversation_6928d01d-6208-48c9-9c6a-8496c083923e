import type { UserData as IUserD<PERSON>, UserDataJ<PERSON><PERSON>, UserDataJ<PERSON><PERSON>napshot } from '@clerk/types';
export declare class UserData implements IUserData {
    firstName?: string;
    lastName?: string;
    imageUrl?: string;
    hasImage?: boolean;
    constructor(data: UserDataJSON | UserDataJSONSnapshot | null);
    protected fromJSON(data: UserDataJSON | UserDataJSONSnapshot | null): this;
    __internal_toSnapshot(): UserDataJSONSnapshot;
}
