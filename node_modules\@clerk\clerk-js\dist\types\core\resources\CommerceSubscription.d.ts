import type { CancelSubscriptionParams, CommerceMoney, CommerceSubscriptionJSON, CommerceSubscriptionPlanPeriod, CommerceSubscriptionResource, CommerceSubscriptionStatus } from '@clerk/types';
import { BaseResource, CommercePlan, DeletedObject } from './internal';
export declare class CommerceSubscription extends BaseResource implements CommerceSubscriptionResource {
    id: string;
    paymentSourceId: string;
    plan: CommercePlan;
    planPeriod: CommerceSubscriptionPlanPeriod;
    status: CommerceSubscriptionStatus;
    periodStart: number;
    periodEnd: number;
    canceledAt: number | null;
    amount?: CommerceMoney;
    credit?: {
        amount: CommerceMoney;
    };
    constructor(data: CommerceSubscriptionJSON);
    protected fromJSON(data: CommerceSubscriptionJSON | null): this;
    cancel(params: CancelSubscriptionParams): Promise<DeletedObject>;
}
