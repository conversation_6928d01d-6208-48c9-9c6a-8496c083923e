import type { CommercePlanResource, CommerceSubscriptionPlanPeriod, PricingTableProps } from '@clerk/types';
interface PricingTableDefaultProps {
    plans?: CommercePlanResource[] | null;
    highlightedPlan?: CommercePlanResource['slug'];
    planPeriod: CommerceSubscriptionPlanPeriod;
    setPlanPeriod: (val: CommerceSubscriptionPlanPeriod) => void;
    onSelect: (plan: CommercePlanResource) => void;
    isCompact?: boolean;
    props: PricingTableProps;
}
export declare function PricingTableDefault({ plans, planPeriod, setPlanPeriod, onSelect, isCompact, props, }: PricingTableDefaultProps): import("@emotion/react/jsx-runtime").JSX.Element;
export {};
