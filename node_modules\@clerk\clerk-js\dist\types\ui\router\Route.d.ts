import type { LoadedClerk } from '@clerk/types';
import React from 'react';
interface RouteGuardProps {
    canActivate: (clerk: LoadedClerk) => boolean;
}
interface UnguardedRouteProps {
    path?: string;
    index?: boolean;
    flowStart?: boolean;
    canActivate?: never;
}
type GuardedRouteProps = {
    path?: string;
    index?: boolean;
    flowStart?: boolean;
} & RouteGuardProps;
export type RouteProps = React.PropsWithChildren<UnguardedRouteProps | GuardedRouteProps>;
export declare function Route(props: RouteProps): JSX.Element | null;
export {};
