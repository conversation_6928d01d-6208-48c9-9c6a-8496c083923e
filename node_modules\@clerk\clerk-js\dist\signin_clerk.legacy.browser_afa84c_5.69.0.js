"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["722"],{7389:function(e,t,o){o.d(t,{v:()=>a});var r=o(79109),n=o(24562),i=o(39541);let a=e=>{let{onBackLinkClick:t}=e;return(0,r.tZ)(n._,{cardTitle:(0,i.localizationKeys)("signIn.alternativeMethods.getHelp.title"),cardSubtitle:(0,i.localizationKeys)("signIn.alternativeMethods.getHelp.content"),onBackLinkClick:t})}},6154:function(e,t,o){o.r(t),o.d(t,{SignIn:()=>eH,SignInModal:()=>eG});var r=o(79109),n=o(83799),i=o(69144),a=o(15112),s=o(11576),l=o(39541),c=o(12464),d=o(4511),u=o(7508),p=o(24676),h=o(22073);o(50725);let f=()=>Promise.all([o.e("200"),o.e("573"),o.e("710")]).then(o.bind(o,85049)),m=(0,i.lazy)(()=>f().then(e=>({default:e.SignUpVerifyPhone}))),g=(0,i.lazy)(()=>f().then(e=>({default:e.SignUpVerifyEmail}))),v=(0,i.lazy)(()=>f().then(e=>({default:e.SignUpStart}))),I=(0,i.lazy)(()=>f().then(e=>({default:e.SignUpSSOCallback}))),S=(0,i.lazy)(()=>f().then(e=>({default:e.SignUpContinue}))),C=()=>Promise.all([o.e("200"),o.e("573"),o.e("710")]).then(o.bind(o,46052)).then(e=>e.completeSignUpFlow);o(45261),o(70957),o(24551),o(22349),o(65223);var y=o(44455),_=o(2672),B=o(70431),w=o(92654),b=o(24152),Z=o(93234),A=o(77623);let k=(0,_.withCardStateProvider)(()=>{let e=(0,s.useCoreSignIn)(),t=(0,_.useCardState)(),{navigate:o}=(0,p.useRouter)(),n=(0,Z.H)(),{userSettings:{passwordSettings:a}}=(0,s.useEnvironment)(),{t:d,locale:u}=(0,l.useLocalizations)(),h="needs_new_password"===e.status&&"reset_password_email_code"!==e.firstFactorVerification.strategy&&"reset_password_phone_code"!==e.firstFactorVerification.strategy;i.useEffect(()=>{h&&t.setError(d((0,l.localizationKeys)("signIn.resetPassword.requiredMessage")))},[]);let f=(0,A.Yp)("password","",{type:"password",label:(0,l.localizationKeys)("formFieldLabel__newPassword"),isRequired:!0,validatePassword:!0,buildErrorMessage:e=>(0,A.GM)(e,{t:d,locale:u,passwordSettings:a})}),m=(0,A.Yp)("confirmPassword","",{type:"password",label:(0,l.localizationKeys)("formFieldLabel__confirmPassword"),isRequired:!0}),g=(0,A.Yp)("signOutOfOtherSessions","",{type:"checkbox",label:(0,l.localizationKeys)("formFieldLabel__signOutOfOtherSessions"),defaultChecked:!0}),{setConfirmPasswordFeedback:v,isPasswordMatch:I}=(0,c.p5)({passwordField:f,confirmPasswordField:m}),S=async()=>{f.clearFeedback(),m.clearFeedback();try{let{status:t,createdSessionId:r}=await e.resetPassword({password:f.value,signOutOfOtherSessions:g.checked});switch(t){case"complete":if(r){let e=new URLSearchParams;return e.set("createdSessionId",r),o("../reset-password-success?".concat(e.toString()))}return console.error((0,b.Ws)(t,n));case"needs_second_factor":return o("../factor-two");default:return console.error((0,b.Ws)(t,n))}}catch(e){return(0,A.S3)(e,[f,m],t.setError)}};return(0,r.BX)(y.Z.Root,{children:[(0,r.BX)(y.Z.Content,{children:[(0,r.tZ)(w.h.Root,{showLogo:!0,children:(0,r.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.resetPassword.title")})}),(0,r.tZ)(y.Z.Alert,{children:t.error}),(0,r.tZ)(l.Col,{elementDescriptor:l.descriptors.main,gap:8,children:(0,r.BX)(B.l.Root,{onSubmit:S,onBlur:()=>{f.value&&v(m.value)},gap:8,children:[(0,r.BX)(l.Col,{gap:6,children:[(0,r.tZ)("input",{readOnly:!0,"data-testid":"hidden-identifier",id:"identifier-field",name:"identifier",value:e.identifier||"",style:{display:"none"}}),(0,r.tZ)(B.l.ControlRow,{elementId:f.id,children:(0,r.tZ)(B.l.PasswordInput,{...f.props,isRequired:!0,autoFocus:!0})}),(0,r.tZ)(B.l.ControlRow,{elementId:m.id,children:(0,r.tZ)(B.l.PasswordInput,{...m.props,onChange:e=>(e.target.value&&v(e.target.value),m.props.onChange(e))})}),!h&&(0,r.tZ)(B.l.ControlRow,{elementId:g.id,children:(0,r.tZ)(B.l.Checkbox,{...g.props})})]}),(0,r.BX)(l.Col,{gap:3,children:[(0,r.tZ)(B.l.SubmitButton,{isDisabled:!I,localizationKey:(0,l.localizationKeys)("signIn.resetPassword.formButtonPrimary")}),(0,r.tZ)(y.Z.Action,{elementId:"alternativeMethods",children:(0,r.tZ)(y.Z.ActionLink,{elementDescriptor:l.descriptors.backLink,localizationKey:(0,l.localizationKeys)("backButton"),onClick:()=>o("../")})})]})]})})]}),(0,r.tZ)(y.Z.Footer,{})]})});var P=o(44875),U=o(30215);let E=(0,_.withCardStateProvider)(()=>{let e=(0,_.useCardState)();return(0,P.E)(),(0,r.BX)(y.Z.Root,{children:[(0,r.BX)(y.Z.Content,{children:[(0,r.tZ)(w.h.Root,{showLogo:!0,children:(0,r.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.resetPassword.title")})}),(0,r.tZ)(y.Z.Alert,{children:e.error}),(0,r.BX)(l.Col,{elementDescriptor:l.descriptors.main,gap:8,children:[(0,r.tZ)(l.Text,{localizationKey:(0,l.localizationKeys)("signIn.resetPassword.successMessage")}),(0,r.tZ)(U.kC,{direction:"row",center:!0,children:(0,r.tZ)(l.Spinner,{size:"xl",colorScheme:"primary",elementDescriptor:l.descriptors.spinner})})]})]}),(0,r.tZ)(y.Z.Footer,{})]})});var z=o(42305),F=o(48774),R=o(97295),L=o(8969),K=o(96519),T=o(49327),x=o(35241);let M=(0,L.Hy)((0,_.withCardStateProvider)(()=>{let e=(0,_.useCardState)(),{userProfileUrl:t}=(0,s.useEnvironment)().displayConfig,{afterSignInUrl:o,path:n}=(0,s.useSignInContext)(),{navigateAfterSignOut:i}=(0,s.useSignOutContext)(),{handleSignOutAllClicked:a,handleSessionClicked:c,signedInSessions:d,handleAddAccountClicked:u}=(0,x.Z)({navigateAfterSignOut:i,afterSwitchSessionUrl:o,userProfileUrl:t,signInUrl:n,user:void 0});return(0,r.tZ)(l.Flow.Part,{part:"accountSwitcher",children:(0,r.BX)(y.Z.Root,{children:[(0,r.BX)(y.Z.Content,{sx:e=>({padding:"".concat(e.space.$8," ").concat(e.space.$none," ").concat(e.space.$none)}),children:[(0,r.BX)(w.h.Root,{children:[(0,r.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.accountSwitcher.title")}),(0,r.tZ)(w.h.Subtitle,{localizationKey:(0,l.localizationKeys)("signIn.accountSwitcher.subtitle")})]}),(0,r.tZ)(y.Z.Alert,{children:e.error}),(0,r.tZ)(l.Col,{elementDescriptor:l.descriptors.main,gap:8,sx:e=>({borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100}),children:(0,r.BX)(z.eX,{role:"menu",children:[d.map(e=>(0,r.tZ)(F.K,{onClick:c(e),sx:e=>({height:e.sizes.$16,justifyContent:"flex-start",borderRadius:0}),icon:K.nR,children:(0,r.tZ)(R.E,{user:e.user,sx:{width:"100%"}})},e.id)),(0,r.tZ)(z.aU,{elementDescriptor:l.descriptors.accountSwitcherActionButton,elementId:l.descriptors.accountSwitcherActionButton.setId("addAccount"),iconBoxElementDescriptor:l.descriptors.accountSwitcherActionButtonIconBox,iconBoxElementId:l.descriptors.accountSwitcherActionButtonIconBox.setId("addAccount"),iconElementDescriptor:l.descriptors.accountSwitcherActionButtonIcon,iconElementId:l.descriptors.accountSwitcherActionButtonIcon.setId("addAccount"),icon:K.mm,label:(0,l.localizationKeys)("signIn.accountSwitcher.action__addAccount"),onClick:u,iconSx:e=>({width:e.sizes.$9,height:e.sizes.$6}),iconBoxSx:e=>({minHeight:e.sizes.$9,minWidth:e.sizes.$6,alignItems:"center"}),spinnerSize:"md"})]})})]}),(0,r.tZ)(y.Z.Footer,{sx:e=>({">:first-of-type":{padding:"".concat(e.space.$1),width:"100%"}}),children:(0,r.tZ)(y.Z.Action,{sx:{width:"100%",">:first-of-type":{width:"100%",borderBottomWidth:0}},children:(0,r.tZ)(T.gW,{handleSignOutAllClicked:a,elementDescriptor:l.descriptors.accountSwitcherActionButton,elementId:l.descriptors.accountSwitcherActionButton.setId("signOutAll"),iconBoxElementDescriptor:l.descriptors.accountSwitcherActionButtonIconBox,iconBoxElementId:l.descriptors.accountSwitcherActionButtonIconBox.setId("signOutAll"),iconElementDescriptor:l.descriptors.accountSwitcherActionButtonIcon,iconElementId:l.descriptors.accountSwitcherActionButtonIcon.setId("signOutAll"),label:(0,l.localizationKeys)("signIn.accountSwitcher.action__signOutAll"),actionSx:e=>({padding:"".concat(e.space.$2," ").concat(e.space.$2)})})})})]})})}));var O=o(24562),D=o(21455),X=o(91752),W=o(91085);o(92037);var N=o(38246),$=o(5482),V=o(16735),H=o(56749),G=o(75747),j=o(80138);let Y=i.memo(e=>{let t=(0,n.cL)(),{navigate:o}=(0,p.useRouter)(),i=(0,_.useCardState)(),{displayConfig:a}=(0,G.O)(),l=(0,s.useSignInContext)(),c=(0,s.useCoreSignIn)(),d=(0,H.wT)(l,a.signInUrl),u=l.afterSignInUrl||"/",h="popup"===l.oauthFlow||"auto"===l.oauthFlow&&(0,A.tc)(),{onAlternativePhoneCodeProviderClick:f,...m}=e;return(0,r.tZ)(j.L,{...m,idleAfterDelay:!h,oauthCallback:e=>{if(h){let t=window.open("about:blank","","width=600,height=800"),o=setInterval(()=>{(!t||t.closed)&&(clearInterval(o),i.setIdle())},500);return c.authenticateWithPopup({strategy:e,redirectUrl:d,redirectUrlComplete:u,popup:t,oidcPrompt:l.oidcPrompt}).catch(e=>(0,A.S3)(e,[],i.setError))}return c.authenticateWithRedirect({strategy:e,redirectUrl:d,redirectUrlComplete:u,oidcPrompt:l.oidcPrompt}).catch(e=>(0,A.S3)(e,[],i.setError))},web3Callback:e=>t.authenticateWithWeb3({customNavigate:o,redirectUrl:u,signUpContinueUrl:l.isCombinedFlow?"create/continue":l.signUpContinueUrl,strategy:e,secondFactorUrl:"factor-two"}).catch(e=>(0,A.Ht)(e,i.setError)),alternativePhoneCodeCallback:e=>{null==f||f(e)}})});var q=o(95518);function Q(){var e;return null===(e=(0,s.useCoreSignIn)().supportedFirstFactors)||void 0===e?void 0:e.find(e=>{let{strategy:t}=e;return(0,q.Vh)(t)})}var J=o(7389);let ee=(e,t)=>{let[o,n]=i.useState(!1),a=i.useCallback(()=>n(e=>!e),[n]);return o?(0,r.tZ)(J.v,{onBackLinkClick:a}):(0,r.tZ)(e,{...t,onHavingTroubleClick:a})},et=e=>ee(eo,{...e}),eo=e=>{let{onBackLinkClick:t,onHavingTroubleClick:o,onFactorSelected:n,mode:i="default"}=e,a=(0,_.useCardState)(),c=Q(),{supportedFirstFactors:d}=(0,s.useCoreSignIn)(),{firstPartyFactors:u,hasAnyStrategy:p}=(0,X.l)({filterOutFactor:null==e?void 0:e.currentFactor,supportedFirstFactors:d}),h=function(e){switch(e){case"forgot":return"forgotPasswordMethods";case"pwned":return"passwordPwnedMethods";default:return"alternativeMethods"}}(i),f=function(e){switch(e){case"forgot":return(0,l.localizationKeys)("signIn.forgotPasswordAlternativeMethods.title");case"pwned":return(0,l.localizationKeys)("signIn.passwordPwned.title");default:return(0,l.localizationKeys)("signIn.alternativeMethods.title")}}(i),m=function(e){switch(e){case"forgot":case"pwned":return!0;default:return!1}}(i);return(0,r.tZ)(l.Flow.Part,{part:h,children:(0,r.BX)(y.Z.Root,{children:[(0,r.BX)(y.Z.Content,{children:[(0,r.BX)(w.h.Root,{showLogo:!0,children:[(0,r.tZ)(w.h.Title,{localizationKey:f}),!m&&(0,r.tZ)(w.h.Subtitle,{localizationKey:(0,l.localizationKeys)("signIn.alternativeMethods.subtitle")})]}),(0,r.tZ)(y.Z.Alert,{children:a.error}),(0,r.BX)(l.Flex,{direction:"col",elementDescriptor:l.descriptors.main,gap:6,children:[m&&c&&(0,r.tZ)(l.Button,{localizationKey:er(c),elementDescriptor:l.descriptors.alternativeMethodsBlockButton,isDisabled:a.isLoading,onClick:()=>{a.setError(void 0),n(c)}}),m&&p&&(0,r.tZ)(V.i,{dividerText:(0,l.localizationKeys)("signIn.forgotPasswordAlternativeMethods.label__alternativeMethods")}),(0,r.BX)(l.Col,{gap:4,children:[p&&(0,r.BX)(l.Flex,{elementDescriptor:l.descriptors.alternativeMethods,direction:"col",gap:2,children:[(0,r.tZ)(Y,{enableWeb3Providers:!0,enableOAuthProviders:!0,enableAlternativePhoneCodeProviders:!1}),u&&u.map((e,t)=>{var o;return(0,r.tZ)(N.$,{leftIcon:(o=e,({email_link:K.xP,email_code:K.GT,phone_code:K.iU,reset_password_email_code:K.ds,reset_password_phone_code:K.ds,password:K.kh,passkey:K.IG})[o.strategy]),textLocalizationKey:er(e),elementDescriptor:l.descriptors.alternativeMethodsBlockButton,textElementDescriptor:l.descriptors.alternativeMethodsBlockButtonText,arrowElementDescriptor:l.descriptors.alternativeMethodsBlockButtonArrow,textVariant:"buttonLarge",isDisabled:a.isLoading,onClick:()=>{a.setError(void 0),n(e)}},t)})]}),t&&(0,r.tZ)($.h,{boxElementDescriptor:l.descriptors.backRow,linkElementDescriptor:l.descriptors.backLink,onClick:t})]})]})]}),(0,r.tZ)(y.Z.Footer,{children:(0,r.BX)(y.Z.Action,{elementId:"havingTrouble",children:[(0,r.tZ)(y.Z.ActionText,{localizationKey:(0,l.localizationKeys)("signIn.alternativeMethods.actionText")}),(0,r.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)("signIn.alternativeMethods.actionLink"),onClick:o})]})})]})})};function er(e){switch(e.strategy){case"email_link":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__emailLink",{identifier:(0,A.HT)(e.safeIdentifier)||""});case"email_code":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__emailCode",{identifier:(0,A.HT)(e.safeIdentifier)||""});case"phone_code":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__phoneCode",{identifier:(0,A.HT)(e.safeIdentifier)||""});case"password":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__password");case"passkey":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__passkey");case"reset_password_email_code":case"reset_password_phone_code":return(0,l.localizationKeys)("signIn.forgotPasswordAlternativeMethods.blockButton__resetPassword");default:throw Error('Invalid sign in strategy: "'.concat(e.strategy,'"'))}}var en=o(36543),ei=o(73531),ea=o(23465);let es=e=>{let t=(0,s.useCoreSignIn)(),o=(0,_.useCardState)(),{navigate:i}=(0,p.useRouter)(),{afterSignInUrl:a}=(0,s.useSignInContext)(),{setActive:l}=(0,n.cL)(),c=(0,Z.H)(),d=(0,n.cL)(),u=e.factor.channel,h="verified"===t.firstFactorVerification.status&&e.factorAlreadyPrepared;return(0,r.tZ)(ea.U,{cardTitle:e.cardTitle,cardSubtitle:e.cardSubtitle,inputLabel:e.inputLabel,resendButton:e.resendButton,onCodeEntryFinishedAction:(o,r,n)=>{t.attemptFirstFactor({strategy:e.factor.strategy,code:o}).then(async e=>{switch(await r(),e.status){case"complete":return l({session:e.createdSessionId,redirectUrl:a});case"needs_second_factor":return i("../factor-two");case"needs_new_password":return i("../reset-password");default:return console.error((0,b.Ws)(e.status,c))}}).catch(e=>(0,ei.ay)(e)?d.__internal_navigateWithError("..",e.errors[0]):n(e))},onResendCodeClicked:()=>{!h&&t.prepareFirstFactor({...e.factor,channel:u}).then(()=>e.onFactorPrepare()).catch(e=>(0,A.S3)(e,[],o.setError))},safeIdentifier:e.factor.safeIdentifier,profileImageUrl:t.userData.imageUrl,alternativeMethodsLabel:(0,W.u1)("footerActionLink__alternativePhoneCodeProvider"),onShowAlternativeMethodsClicked:()=>{o.setError(void 0),e.onChangePhoneCodeChannel({...e.factor,channel:void 0})},showAlternativeMethods:!0,onIdentityPreviewEditClicked:()=>i("../"),onBackLinkClicked:e.onBackLinkClicked})},el=e=>{var t;return(0,r.tZ)(l.Flow.Part,{part:"phoneCode",children:(0,r.tZ)(es,{...e,cardTitle:(0,l.localizationKeys)("signIn.alternativePhoneCodeProvider.title",{provider:null===(t=(0,en.H)(e.factor.channel))||void 0===t?void 0:t.name}),cardSubtitle:(0,l.localizationKeys)("signIn.alternativePhoneCodeProvider.subtitle"),inputLabel:(0,l.localizationKeys)("signIn.alternativePhoneCodeProvider.formTitle"),resendButton:(0,l.localizationKeys)("signIn.alternativePhoneCodeProvider.resendButton")})})},ec=e=>{let t=(0,s.useCoreSignIn)(),o=(0,_.useCardState)(),{navigate:i}=(0,p.useRouter)(),{afterSignInUrl:a}=(0,s.useSignInContext)(),{setActive:l}=(0,n.cL)(),d=(0,Z.H)(),u=(0,n.cL)(),h="verified"===t.firstFactorVerification.status&&e.factorAlreadyPrepared;return(0,c.ib)(h?void 0:()=>null==t?void 0:t.prepareFirstFactor(e.factor).then(()=>e.onFactorPrepare()).catch(e=>(0,A.S3)(e,[],o.setError)),{name:"signIn.prepareFirstFactor",factor:e.factor,id:t.id},{staleTime:100}),(0,r.tZ)(ea.U,{cardTitle:e.cardTitle,cardSubtitle:e.cardSubtitle,inputLabel:e.inputLabel,resendButton:e.resendButton,onCodeEntryFinishedAction:(o,r,n)=>{t.attemptFirstFactor({strategy:e.factor.strategy,code:o}).then(async e=>{switch(await r(),e.status){case"complete":return l({session:e.createdSessionId,redirectUrl:a});case"needs_second_factor":return i("../factor-two");case"needs_new_password":return i("../reset-password");default:return console.error((0,b.Ws)(e.status,d))}}).catch(e=>(0,ei.ay)(e)?u.__internal_navigateWithError("..",e.errors[0]):n(e))},onResendCodeClicked:()=>{!h&&t.prepareFirstFactor(e.factor).then(()=>e.onFactorPrepare()).catch(e=>(0,A.S3)(e,[],o.setError))},safeIdentifier:e.factor.safeIdentifier,profileImageUrl:t.userData.imageUrl,onShowAlternativeMethodsClicked:e.onShowAlternativeMethodsClicked,showAlternativeMethods:e.showAlternativeMethods,onIdentityPreviewEditClicked:()=>i("../"),onBackLinkClicked:e.onBackLinkClicked})},ed=e=>(0,r.tZ)(l.Flow.Part,{part:"emailCode",children:(0,r.tZ)(ec,{...e,cardTitle:(0,l.localizationKeys)("signIn.emailCode.title"),cardSubtitle:(0,l.localizationKeys)("signIn.emailCode.subtitle"),inputLabel:(0,l.localizationKeys)("signIn.emailCode.formTitle"),resendButton:(0,l.localizationKeys)("signIn.emailCode.resendButton")})});var eu=o(65495),ep=o(14814),eh=o(85117);let ef=e=>{let{t}=(0,l.useLocalizations)(),o=(0,_.useCardState)(),a=(0,s.useCoreSignIn)(),c=(0,s.useSignInContext)(),{signInUrl:d}=c,{navigate:u}=(0,eh.t)(),{afterSignInUrl:p}=(0,s.useSignInContext)(),{setActive:h}=(0,n.cL)(),{startEmailLinkFlow:f,cancelEmailLinkFlow:m}=(0,ep.E)(a),[g,v]=i.useState(!1),I=(0,n.cL)();i.useEffect(()=>{S()},[]);let S=()=>{f({emailAddressId:e.factor.emailAddressId,redirectUrl:(0,H.Uu)({ctx:c,baseUrl:d,intent:"sign-in"})}).then(e=>C(e)).catch(e=>{if((0,ei.ay)(e))return I.__internal_navigateWithError("..",e.errors[0]);(0,A.S3)(e,[],o.setError)})},C=async e=>{let r=e.firstFactorVerification;"expired"===r.status?o.setError(t((0,l.localizationKeys)("formFieldError__verificationLinkExpired"))):r.verifiedFromTheSameClient()?v(!0):await y(e)},y=async e=>"complete"===e.status?h({session:e.createdSessionId,redirectUrl:p}):"needs_second_factor"===e.status?u("../factor-two"):void 0;return g?(0,r.tZ)(L.Ej,{title:(0,l.localizationKeys)("signIn.emailLink.verifiedSwitchTab.titleNewTab"),subtitle:(0,l.localizationKeys)("signIn.emailLink.verifiedSwitchTab.subtitleNewTab"),status:"verified_switch_tab"}):(0,r.tZ)(l.Flow.Part,{part:"emailLink",children:(0,r.tZ)(eu.J,{cardTitle:(0,l.localizationKeys)("signIn.emailLink.title"),cardSubtitle:(0,l.localizationKeys)("signIn.emailLink.subtitle"),formTitle:(0,l.localizationKeys)("signIn.emailLink.formTitle"),formSubtitle:(0,l.localizationKeys)("signIn.emailLink.formSubtitle"),resendButton:(0,l.localizationKeys)("signIn.emailLink.resendButton"),onResendCodeClicked:()=>{m(),S()},safeIdentifier:e.factor.safeIdentifier,profileImageUrl:a.userData.imageUrl,onShowAlternativeMethodsClicked:e.onShowAlternativeMethodsClicked})})},em=e=>(0,r.tZ)(l.Flow.Part,{part:"resetPassword",children:(0,r.tZ)(ec,{...e,showAlternativeMethods:!1,cardTitle:(0,l.localizationKeys)("signIn.forgotPassword.title"),inputLabel:(0,l.localizationKeys)("signIn.forgotPassword.formTitle"),resendButton:(0,l.localizationKeys)("signIn.forgotPassword.resendButton")})});var eg=o(384),ev=o(93367);function eI(e){let t=(0,_.useCardState)(),{setActive:o,__internal_navigateWithError:r}=(0,n.cL)(),a=(0,Z.H)(),{afterSignInUrl:l}=(0,s.useSignInContext)(),{authenticateWithPasskey:c}=(0,s.useCoreSignIn)();return(0,i.useEffect)(()=>()=>{ev.YI.abort()},[]),(0,i.useCallback)(async function(){for(var n=arguments.length,i=Array(n),s=0;s<n;s++)i[s]=arguments[s];try{let t=await c(...i);switch(t.status){case"complete":return o({session:t.createdSessionId,redirectUrl:l});case"needs_second_factor":return e();default:return console.error((0,b.Ws)(t.status,a))}}catch(o){let{flow:e}=i[0]||{};if((0,ei.uX)(o)&&("passkey_operation_aborted"===o.code||"autofill"===e&&"passkey_retrieval_cancelled"===o.code))return;if((0,ei.ay)(o))return r("..",o.errors[0]);(0,A.S3)(o,[],t.setError)}},[])}let eS=e=>{let{onShowAlternativeMethodsClick:t}=e,o=(0,_.useCardState)(),n=(0,s.useCoreSignIn)(),{navigate:a}=(0,eh.t)(),[c,d]=i.useState(!1),u=i.useCallback(()=>d(e=>!e),[d]),p=eI(()=>a("../factor-two"));return c?(0,r.tZ)(J.v,{onBackLinkClick:u}):(0,r.tZ)(l.Flow.Part,{part:"password",children:(0,r.BX)(y.Z.Root,{children:[(0,r.BX)(y.Z.Content,{children:[(0,r.BX)(w.h.Root,{showLogo:!0,children:[(0,r.tZ)(l.Icon,{elementDescriptor:l.descriptors.passkeyIcon,icon:K.IG,sx:e=>({color:e.colors.$neutralAlpha500,marginInline:"auto",paddingBottom:e.sizes.$1,width:e.sizes.$12,height:e.sizes.$12})}),(0,r.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.passkey.title")}),(0,r.tZ)(w.h.Subtitle,{localizationKey:(0,l.localizationKeys)("signIn.passkey.subtitle")}),(0,r.tZ)(eg.m,{identifier:n.identifier,avatarUrl:n.userData.imageUrl,onClick:()=>a("../")})]}),(0,r.tZ)(y.Z.Alert,{children:o.error}),(0,r.BX)(l.Flex,{direction:"col",elementDescriptor:l.descriptors.main,gap:4,children:[(0,r.tZ)(B.l.Root,{onSubmit:e=>(e.preventDefault(),p()),gap:8,children:(0,r.tZ)(B.l.SubmitButton,{hasArrow:!0})}),(0,r.tZ)(y.Z.Action,{elementId:t?"alternativeMethods":"havingTrouble",children:(0,r.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)(t?"footerActionLink__useAnotherMethod":"signIn.alternativeMethods.actionLink"),onClick:t||u})})]})]}),(0,r.tZ)(y.Z.Footer,{})]})})},eC=e=>{let{onForgotPasswordMethodClick:t,onShowAlternativeMethodsClick:o}=e,r=Q(),n=(0,A.Yp)("password","",{type:"password",label:(0,l.localizationKeys)("formFieldLabel__password"),placeholder:(0,l.localizationKeys)("formFieldInputPlaceholder__password")});return{...n,props:{...n.props,actionLabel:r||o?(0,l.localizationKeys)("formFieldAction__forgotPassword"):"",onActionClicked:t||o||(()=>null)}}},ey=e=>{let{onShowAlternativeMethodsClick:t,onPasswordPwned:o}=e,a=i.useRef(null),c=(0,_.useCardState)(),{setActive:d}=(0,n.cL)(),u=(0,s.useCoreSignIn)(),{afterSignInUrl:p}=(0,s.useSignInContext)(),h=(0,Z.H)(),f=eC(e),{navigate:m}=(0,eh.t)(),[g,v]=i.useState(!1),I=i.useCallback(()=>v(e=>!e),[v]),S=(0,n.cL)(),C=async e=>(e.preventDefault(),u.attemptFirstFactor({strategy:"password",password:f.value}).then(e=>{switch(e.status){case"complete":return d({session:e.createdSessionId,redirectUrl:p});case"needs_second_factor":return m("../factor-two");default:return console.error((0,b.Ws)(e.status,h))}}).catch(e=>{if((0,ei.ay)(e))return S.__internal_navigateWithError("..",e.errors[0]);if((0,ei.UZ)(e)&&o){c.setError({...e.errors[0],code:"form_password_pwned__sign_in"}),o();return}(0,A.S3)(e,[f],c.setError),setTimeout(()=>{var e;return null===(e=a.current)||void 0===e?void 0:e.focus()},0)}));return g?(0,r.tZ)(J.v,{onBackLinkClick:I}):(0,r.tZ)(l.Flow.Part,{part:"password",children:(0,r.BX)(y.Z.Root,{children:[(0,r.BX)(y.Z.Content,{children:[(0,r.BX)(w.h.Root,{showLogo:!0,children:[(0,r.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.password.title")}),(0,r.tZ)(w.h.Subtitle,{localizationKey:(0,l.localizationKeys)("signIn.password.subtitle")}),(0,r.tZ)(eg.m,{identifier:u.identifier,avatarUrl:u.userData.imageUrl,onClick:()=>m("../")})]}),(0,r.tZ)(y.Z.Alert,{children:c.error}),(0,r.BX)(l.Flex,{direction:"col",elementDescriptor:l.descriptors.main,gap:4,children:[(0,r.BX)(B.l.Root,{onSubmit:C,gap:8,children:[(0,r.tZ)("input",{readOnly:!0,id:"identifier-field",name:"identifier",value:u.identifier||"",style:{display:"none"}}),(0,r.tZ)(B.l.ControlRow,{elementId:f.id,children:(0,r.tZ)(B.l.PasswordInput,{...f.props,ref:a,autoFocus:!0})}),(0,r.tZ)(B.l.SubmitButton,{hasArrow:!0})]}),(0,r.tZ)(y.Z.Action,{elementId:t?"alternativeMethods":"havingTrouble",children:(0,r.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)(t?"signIn.password.actionLink":"signIn.alternativeMethods.actionLink"),onClick:t||I})})]})]}),(0,r.tZ)(y.Z.Footer,{})]})})},e_=e=>(0,r.tZ)(l.Flow.Part,{part:"phoneCode",children:(0,r.tZ)(ec,{...e,cardTitle:(0,l.localizationKeys)("signIn.phoneCode.title"),cardSubtitle:(0,l.localizationKeys)("signIn.phoneCode.subtitle"),inputLabel:(0,l.localizationKeys)("signIn.phoneCode.formTitle"),resendButton:(0,l.localizationKeys)("signIn.phoneCode.resendButton")})}),eB=e=>{if(!e)return"";let t=e.strategy;return"emailAddressId"in e&&(t+=e.emailAddressId),"phoneNumberId"in e&&(t+=e.phoneNumberId),"channel"in e&&(t+=e.channel),t},ew=(0,L.Hy)((0,_.withCardStateProvider)(function(){let e=(0,s.useCoreSignIn)(),{preferredSignInStrategy:t}=(0,s.useEnvironment)().displayConfig,o=e.supportedFirstFactors,n=(0,p.useRouter)(),a=(0,_.useCardState)(),{supportedFirstFactors:l,firstFactorVerification:c}=(0,s.useCoreSignIn)(),d=i.useRef(""),[{currentFactor:u},h]=i.useState(()=>{let r=(0,q.t3)(o,e.identifier,t);return(null==r?void 0:r.strategy)==="phone_code"&&c.channel&&"sms"!==c.channel&&(r.channel=c.channel),{currentFactor:r,prevCurrentFactor:void 0}}),{hasAnyStrategy:f}=(0,X.l)({filterOutFactor:u,supportedFirstFactors:l}),[m,g]=i.useState(()=>!u||!(0,q.xT)(u)),v=Q(),[I,S]=i.useState(!1),[C,y]=i.useState(!1);if(i.useEffect(()=>{("needs_identifier"===e.status||null===e.status)&&n.navigate("../")},[]),!u&&e.status)return(0,r.tZ)(O._,{cardTitle:(0,W.u1)("signIn.noAvailableMethods.title"),cardSubtitle:(0,W.u1)("signIn.noAvailableMethods.subtitle"),message:(0,W.u1)("signIn.noAvailableMethods.message")});let B=f?()=>g(e=>!e):void 0,w=()=>S(e=>!e),b=()=>{d.current=eB(u)},Z=e=>{h(t=>({currentFactor:e,prevCurrentFactor:t.currentFactor}))};if(m||I){let e=(0,q.xT)(u),t=m?B:w;return(0,r.tZ)(et,{mode:I?C?"pwned":"forgot":"default",onBackLinkClick:e?()=>{a.setError(void 0),y(!1),null==t||t()}:void 0,onFactorSelected:e=>{Z(e),null==t||t()},currentFactor:u})}if(!u)return(0,r.tZ)(D.W,{});switch(null==u?void 0:u.strategy){case"passkey":return(0,r.tZ)(eS,{onFactorPrepare:b,onShowAlternativeMethodsClick:B});case"password":return(0,r.tZ)(ey,{onForgotPasswordMethodClick:v?w:B,onShowAlternativeMethodsClick:B,onPasswordPwned:()=>{y(!0),w()}});case"email_code":return(0,r.tZ)(ed,{factorAlreadyPrepared:d.current===eB(u),onFactorPrepare:b,factor:u,onShowAlternativeMethodsClicked:B});case"phone_code":if(u.channel&&"sms"!==u.channel)return(0,r.tZ)(el,{factorAlreadyPrepared:d.current===eB(u),onFactorPrepare:b,factor:u,onChangePhoneCodeChannel:Z});return(0,r.tZ)(e_,{factorAlreadyPrepared:d.current===eB(u),onFactorPrepare:b,factor:u,onShowAlternativeMethodsClicked:B});case"email_link":return(0,r.tZ)(ef,{factorAlreadyPrepared:d.current===eB(u),onFactorPrepare:b,factor:u,onShowAlternativeMethodsClicked:B});case"reset_password_phone_code":return(0,r.tZ)(em,{factorAlreadyPrepared:d.current===eB(u),onFactorPrepare:b,factor:u,onShowAlternativeMethodsClicked:B,onBackLinkClicked:()=>{h(e=>({currentFactor:e.prevCurrentFactor,prevCurrentFactor:e.currentFactor})),w()},cardSubtitle:(0,W.u1)("signIn.forgotPassword.subtitle_phone")});case"reset_password_email_code":return(0,r.tZ)(em,{factorAlreadyPrepared:d.current===eB(u),onFactorPrepare:b,factor:u,onShowAlternativeMethodsClicked:B,onBackLinkClicked:()=>{h(e=>({currentFactor:e.prevCurrentFactor,prevCurrentFactor:e.currentFactor})),w()},cardSubtitle:(0,W.u1)("signIn.forgotPassword.subtitle_email")});default:return(0,r.tZ)(D.W,{})}})),eb=e=>{let[t,o]=i.useState(!1),n=i.useCallback(()=>o(e=>!e),[o]);return t?(0,r.tZ)(J.v,{onBackLinkClick:n}):(0,r.tZ)(eZ,{onBackLinkClick:e.onBackLinkClick,onFactorSelected:e.onFactorSelected,onHavingTroubleClick:n})},eZ=e=>{let{onHavingTroubleClick:t,onFactorSelected:o,onBackLinkClick:n}=e,i=(0,_.useCardState)(),{supportedSecondFactors:a}=(0,s.useCoreSignIn)();return(0,r.tZ)(l.Flow.Part,{part:"alternativeMethods",children:(0,r.BX)(y.Z.Root,{children:[(0,r.BX)(y.Z.Content,{children:[(0,r.BX)(w.h.Root,{showLogo:!0,children:[(0,r.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.alternativeMethods.title")}),(0,r.tZ)(w.h.Subtitle,{localizationKey:(0,l.localizationKeys)("signIn.alternativeMethods.subtitle")})]}),(0,r.tZ)(y.Z.Alert,{children:i.error}),(0,r.BX)(l.Col,{elementDescriptor:l.descriptors.main,gap:3,children:[(0,r.tZ)(l.Col,{gap:2,children:a&&a.sort(A.Q0).map((e,t)=>(0,r.tZ)(N.$,{textLocalizationKey:function(e){switch(e.strategy){case"phone_code":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__phoneCode",{identifier:(0,A.HT)(e.safeIdentifier)||""});case"totp":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__totp");case"backup_code":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__backupCode");default:throw Error('Invalid sign in strategy: "'.concat(e.strategy,'"'))}}(e),elementDescriptor:l.descriptors.alternativeMethodsBlockButton,textElementDescriptor:l.descriptors.alternativeMethodsBlockButtonText,arrowElementDescriptor:l.descriptors.alternativeMethodsBlockButtonArrow,isDisabled:i.isLoading,onClick:()=>o(e)},t))}),(0,r.tZ)(y.Z.Action,{elementId:"alternativeMethods",children:n&&(0,r.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)("backButton"),onClick:e.onBackLinkClick})})]})]}),(0,r.tZ)(y.Z.Footer,{children:(0,r.BX)(y.Z.Action,{elementId:"havingTrouble",children:[(0,r.tZ)(y.Z.ActionText,{localizationKey:(0,l.localizationKeys)("signIn.alternativeMethods.actionText")}),(0,r.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)("signIn.alternativeMethods.actionLink"),onClick:t})]})})]})})},eA=e=>{let{onShowAlternativeMethodsClicked:t}=e,o=(0,s.useCoreSignIn)(),{afterSignInUrl:i}=(0,s.useSignInContext)(),{setActive:a}=(0,n.cL)(),{navigate:c}=(0,p.useRouter)(),d=(0,Z.H)(),u=(0,_.useCardState)(),h=(0,A.Yp)("code","",{type:"text",label:(0,l.localizationKeys)("formFieldLabel__backupCode"),isRequired:!0}),f=(0,n.cL)(),m=e=>{var t,o;return(0,q.Vh)(null===(t=e.firstFactorVerification)||void 0===t?void 0:t.strategy)&&(null===(o=e.firstFactorVerification)||void 0===o?void 0:o.status)==="verified"};return(0,r.BX)(y.Z.Root,{children:[(0,r.BX)(y.Z.Content,{children:[(0,r.BX)(w.h.Root,{showLogo:!0,children:[(0,r.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.backupCodeMfa.title")}),(0,r.tZ)(w.h.Subtitle,{localizationKey:m(o)?(0,l.localizationKeys)("signIn.forgotPassword.subtitle"):(0,l.localizationKeys)("signIn.backupCodeMfa.subtitle")})]}),(0,r.tZ)(y.Z.Alert,{children:u.error}),(0,r.tZ)(l.Col,{elementDescriptor:l.descriptors.main,gap:8,children:(0,r.BX)(B.l.Root,{onSubmit:e=>(e.preventDefault(),o.attemptSecondFactor({strategy:"backup_code",code:h.value}).then(e=>{if("complete"===e.status){if(m(e)&&e.createdSessionId){let t=new URLSearchParams;return t.set("createdSessionId",e.createdSessionId),c("../reset-password-success?".concat(t.toString()))}return a({session:e.createdSessionId,redirectUrl:i})}return console.error((0,b.Ws)(e.status,d))}).catch(e=>{if((0,ei.ay)(e))return f.__internal_navigateWithError("..",e.errors[0]);(0,A.S3)(e,[h],u.setError)})),children:[(0,r.tZ)(B.l.ControlRow,{elementId:h.id,children:(0,r.tZ)(B.l.PlainInput,{...h.props,autoFocus:!0,onActionClicked:t})}),(0,r.BX)(l.Col,{gap:3,children:[(0,r.tZ)(B.l.SubmitButton,{hasArrow:!0}),(0,r.tZ)(y.Z.Action,{elementId:"alternativeMethods",children:t&&(0,r.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)("footerActionLink__useAnotherMethod"),onClick:t})})]})]})})]}),(0,r.tZ)(y.Z.Footer,{})]})},ek=e=>{let t=(0,s.useCoreSignIn)(),o=(0,_.useCardState)(),{afterSignInUrl:a}=(0,s.useSignInContext)(),{setActive:c}=(0,n.cL)(),{navigate:d}=(0,p.useRouter)(),u=(0,Z.H)(),h=(0,n.cL)();i.useEffect(()=>{!e.factorAlreadyPrepared&&(null==f||f())},[]);let f=e.prepare?()=>{var t;return null===(t=e.prepare)||void 0===t?void 0:t.call(e).then(()=>e.onFactorPrepare()).catch(e=>{if((0,ei.ay)(e))return h.__internal_navigateWithError("..",e.errors[0]);(0,A.S3)(e,[],o.setError)})}:void 0,m=e=>{var t,o;return(0,q.Vh)(null===(t=e.firstFactorVerification)||void 0===t?void 0:t.strategy)&&(null===(o=e.firstFactorVerification)||void 0===o?void 0:o.status)==="verified"};return(0,r.tZ)(ea.U,{cardTitle:e.cardTitle,cardSubtitle:m(t)?(0,l.localizationKeys)("signIn.forgotPassword.subtitle"):e.cardSubtitle,resendButton:e.resendButton,inputLabel:e.inputLabel,onCodeEntryFinishedAction:(o,r,n)=>{t.attemptSecondFactor({strategy:e.factor.strategy,code:o}).then(async e=>{if(await r(),"complete"===e.status){if(m(e)&&e.createdSessionId){let t=new URLSearchParams;return t.set("createdSessionId",e.createdSessionId),d("../reset-password-success?".concat(t.toString()))}return c({session:e.createdSessionId,redirectUrl:a})}return console.error((0,b.Ws)(e.status,u))}).catch(e=>(0,ei.ay)(e)?h.__internal_navigateWithError("..",e.errors[0]):n(e))},onResendCodeClicked:f,safeIdentifier:"safeIdentifier"in e.factor?e.factor.safeIdentifier:void 0,profileImageUrl:t.userData.imageUrl,onShowAlternativeMethodsClicked:e.onShowAlternativeMethodsClicked,children:m(t)&&(0,r.tZ)(l.Text,{localizationKey:(0,l.localizationKeys)("signIn.resetPasswordMfa.detailsLabel"),colorScheme:"secondary"})})},eP=e=>{let t=(0,s.useCoreSignIn)();return(0,r.tZ)(l.Flow.Part,{part:"phoneCode2Fa",children:(0,r.tZ)(ek,{...e,cardTitle:(0,l.localizationKeys)("signIn.phoneCodeMfa.title"),cardSubtitle:(0,l.localizationKeys)("signIn.phoneCodeMfa.subtitle"),inputLabel:(0,l.localizationKeys)("signIn.phoneCodeMfa.formTitle"),resendButton:(0,l.localizationKeys)("signIn.phoneCodeMfa.resendButton"),prepare:()=>{let{phoneNumberId:o,strategy:r}=e.factor;return t.prepareSecondFactor({phoneNumberId:o,strategy:r})}})})},eU=e=>(0,r.tZ)(l.Flow.Part,{part:"totp2Fa",children:(0,r.tZ)(ek,{...e,cardTitle:(0,l.localizationKeys)("signIn.totpMfa.title"),cardSubtitle:(0,l.localizationKeys)("signIn.totpMfa.subtitle"),inputLabel:(0,l.localizationKeys)("signIn.totpMfa.formTitle")})}),eE=e=>{if(!e)return"";let t=e.strategy;return"phoneNumberId"in e&&(t+=e.phoneNumberId),t},ez=(0,L.Hy)((0,_.withCardStateProvider)(function(){let e=(0,s.useCoreSignIn)().supportedSecondFactors,t=i.useRef(""),[o,n]=i.useState(()=>(0,q.bx)(e)),[a,l]=i.useState(!o),c=()=>l(e=>!e),d=()=>{t.current=eE(o)};if(!o)return(0,r.tZ)(D.W,{});if(a)return(0,r.tZ)(eb,{onBackLinkClick:c,onFactorSelected:e=>{n(e),c()}});switch(null==o?void 0:o.strategy){case"phone_code":return(0,r.tZ)(eP,{factorAlreadyPrepared:t.current===eE(o),onFactorPrepare:d,factor:o,onShowAlternativeMethodsClicked:c});case"totp":return(0,r.tZ)(eU,{factorAlreadyPrepared:t.current===eE(o),onFactorPrepare:d,factor:o,onShowAlternativeMethodsClicked:c});case"backup_code":return(0,r.tZ)(eA,{onShowAlternativeMethodsClicked:c});default:return(0,r.tZ)(D.W,{})}})),eF=(0,L.Hy)(L.L);o(28419),o(38062),o(79876);var eR=o(32208),eL=o(18350),eK=o(80753),eT=o(26917),ex=o(37321);let eM=e=>{var t;let{handleSubmit:o,phoneNumberFormState:n,onUseAnotherMethod:i,phoneCodeProvider:a}=e,{providerToDisplayData:s,strategyToDisplayData:d}=(0,c.vO)(),u=a.name,p=a.channel,h=(0,_.useCardState)();return(0,r.tZ)(y.Z.Root,{children:(0,r.BX)(y.Z.Content,{children:[(0,r.BX)(w.h.Root,{showLogo:!0,showDivider:!0,children:[(0,r.tZ)(l.Col,{center:!0,children:(0,r.tZ)(l.Image,{src:null===(t=s[p])||void 0===t?void 0:t.iconUrl,alt:"".concat(d[p].name," logo"),sx:e=>({width:e.sizes.$7,height:e.sizes.$7,maxWidth:"100%",marginBottom:e.sizes.$6})})}),(0,r.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.start.alternativePhoneCodeProvider.title",{provider:u})}),(0,r.tZ)(w.h.Subtitle,{localizationKey:(0,l.localizationKeys)("signIn.start.alternativePhoneCodeProvider.subtitle",{provider:u})})]}),(0,r.tZ)(y.Z.Alert,{children:h.error}),(0,r.tZ)(l.Flex,{direction:"col",elementDescriptor:l.descriptors.main,gap:6,children:(0,r.BX)(B.l.Root,{onSubmit:o,gap:8,children:[(0,r.tZ)(l.Col,{gap:6,children:(0,r.tZ)(B.l.ControlRow,{elementId:"phoneNumber",children:(0,r.tZ)(B.l.PhoneInput,{...n.props,label:(0,l.localizationKeys)("signIn.start.alternativePhoneCodeProvider.label",{provider:u}),isRequired:!0,isOptional:!1,actionLabel:void 0,onActionClicked:void 0})})}),(0,r.BX)(l.Col,{center:!0,children:[(0,r.tZ)(ex.S,{}),(0,r.tZ)(l.Col,{gap:6,sx:{width:"100%"},children:(0,r.tZ)(B.l.SubmitButton,{hasArrow:!0,localizationKey:(0,l.localizationKeys)("formButtonPrimary")})})]}),(0,r.tZ)(l.Col,{center:!0,children:(0,r.tZ)(l.Button,{variant:"link",colorScheme:"neutral",onClick:i,localizationKey:(0,l.localizationKeys)("signIn.start.alternativePhoneCodeProvider.actionLink")})})]})})]})})},eO=()=>{let[e,t]=(0,i.useState)(!1),{navigate:o}=(0,p.useRouter)(),r=eI(()=>o("factor-two")),{userSettings:n}=(0,s.useEnvironment)(),{passkeySettings:a,attributes:l}=n;return(0,i.useEffect)(()=>{var e;async function o(){let e=await (0,eR.h_)();t(e),e&&await r({flow:"autofill"})}a.allow_autofill&&(null===(e=l.passkey)||void 0===e?void 0:e.enabled)&&o()},[]),{isWebAuthnAutofillSupported:e}},eD=e=>{var t;return null!==(t=e.supportedFirstFactors)&&void 0!==t&&!!t.length&&e.supportedFirstFactors.every(e=>"enterprise_sso"===e.strategy)},eX=e=>{let{field:t}=e,[o,n]=(0,i.useState)(!1),a=(0,i.useRef)(null),s=!!(o||(null==t?void 0:t.value));return((0,i.useLayoutEffect)(()=>{let e=setInterval(()=>{if(null==a?void 0:a.current){var t;let o="onAutoFillStart"===window.getComputedStyle(a.current,":autofill").animationName||!!(null===(t=a.current)||void 0===t?void 0:t.matches("*:-webkit-autofill"));o&&(n(o),clearInterval(e))}},500);return()=>{clearInterval(e)}},[]),(0,i.useEffect)(()=>{(null==t?void 0:t.value)&&""!==t.value&&n(!1)},[null==t?void 0:t.value]),t)?(0,r.tZ)(B.l.ControlRow,{elementId:t.id,sx:s?void 0:{position:"absolute",opacity:0,height:0,pointerEvents:"none",marginTop:"-1rem"},children:(0,r.tZ)(B.l.PasswordInput,{...t.props,ref:a,tabIndex:s?void 0:-1})}):null},eW=(0,L.Hy)((0,_.withCardStateProvider)(function(){var e,t;let o=(0,_.useCardState)(),a=(0,n.cL)(),d=(0,c._m)(),{displayConfig:u,userSettings:h,authConfig:f}=(0,s.useEnvironment)(),m=(0,s.useCoreSignIn)(),{navigate:g}=(0,p.useRouter)(),v=(0,s.useSignInContext)(),{afterSignInUrl:I,signUpUrl:S,waitlistUrl:k,isCombinedFlow:P}=v,U=(0,Z.H)(),E=(0,i.useMemo)(()=>(0,L.vX)(h.enabledFirstFactorIdentifiers),[h.enabledFirstFactorIdentifiers]),z=h.alternativePhoneCodeChannels,{isWebAuthnAutofillSupported:F}=eO(),R=eI(()=>g("factor-two")),K=(0,eR.iW)(),T=!!(null===(e=v.initialValues)||void 0===e?void 0:e.phoneNumber)&&!(v.initialValues.emailAddress||v.initialValues.username)&&E.includes("phone_number"),[x,M]=(0,i.useState)(T?"phone_number":E[0]||""),[O,X]=(0,i.useState)(!1),W=(0,eT.XV)("__clerk_ticket")||"",N=(0,eT.XV)("__clerk_status")||"",$=h.enabledFirstFactorIdentifiers,V=h.web3FirstFactors,H=h.authenticatableSocialStrategies,G=h.instanceIsPasswordBased,{currentIdentifier:j,nextIdentifier:Q}=(0,L.vO)(E,x),J=(0,A.Yp)("password","",{type:"password",label:(0,l.localizationKeys)("formFieldLabel__password"),placeholder:(0,l.localizationKeys)("formFieldInputPlaceholder__password")}),[ee,et]=(0,i.useState)(null),eo=h.alternativePhoneCodeChannels.length>0,er=v.initialValues||{},ei=(0,i.useMemo)(()=>({email_address:er.emailAddress,email_address_username:er.emailAddress||er.username,username:er.username,phone_number:er.phoneNumber}),[v.initialValues]),ea=!!H.length||!!V.length||!!z.length,[es,el]=(0,i.useState)(!(0,A.s2)()&&!ea),ec=(0,A.Yp)("identifier",ei[x]||"",{...j,isRequired:!0,transformer:e=>e.trim()}),ed=(0,A.Yp)("identifier",ei.phone_number||"",{...j,isRequired:!0}),eu="phone_number"===x?ed:ec,ep=e=>{ec.setValue(ei[x]||""),ed.setValue(e),M("phone_number"),el(!0)};(0,i.useLayoutEffect)(()=>{eu.value.startsWith("+")&&E.includes("phone_number")&&"phone_number"!==x&&!O&&(ep(eu.value),X(!0))},[eu.value,E]),(0,i.useEffect)(()=>{if(W){if("sign_up"===N){let e=new URLSearchParams;W&&e.set("__clerk_ticket",W),g(P?"create":S,{searchParams:e});return}d.setLoading(),o.setLoading(),m.create({strategy:"ticket",ticket:W}).then(e=>{switch(e.status){case"needs_first_factor":if(eD(e))return eg();return g("factor-one");case"needs_second_factor":return g("factor-two");case"complete":return(0,eT.xy)("__clerk_ticket"),a.setActive({session:e.createdSessionId,redirectUrl:I});default:console.error((0,b.Ws)(e.status,U));return}}).catch(e=>ev(e)).finally(()=>{eD(m)||(d.setIdle(),o.setIdle())})}},[]),(0,i.useEffect)(()=>{(async function(){var e;let t=null==m?void 0:null===(e=m.firstFactorVerification)||void 0===e?void 0:e.error;if(t){switch(t.code){case eK.O1.NOT_ALLOWED_TO_SIGN_UP:case eK.O1.OAUTH_ACCESS_DENIED:case eK.O1.NOT_ALLOWED_ACCESS:case eK.O1.SAML_USER_ATTRIBUTE_MISSING:case eK.O1.OAUTH_EMAIL_DOMAIN_RESERVED_BY_SAML:case eK.O1.USER_LOCKED:case eK.O1.EXTERNAL_ACCOUNT_NOT_FOUND:case eK.O1.SIGN_UP_MODE_RESTRICTED:case eK.O1.SIGN_UP_MODE_RESTRICTED_WAITLIST:case eK.O1.ENTERPRISE_SSO_USER_ATTRIBUTE_MISSING:case eK.O1.ENTERPRISE_SSO_EMAIL_ADDRESS_DOMAIN_MISMATCH:case eK.O1.ENTERPRISE_SSO_HOSTED_DOMAIN_MISMATCH:case eK.O1.SAML_EMAIL_ADDRESS_DOMAIN_MISMATCH:case eK.O1.ORGANIZATION_MEMBERSHIP_QUOTA_EXCEEDED_FOR_SSO:case eK.O1.CAPTCHA_INVALID:case eK.O1.FRAUD_DEVICE_BLOCKED:case eK.O1.FRAUD_ACTION_BLOCKED:case eK.O1.SIGNUP_RATE_LIMIT_EXCEEDED:o.setError(t);break;default:o.setError("Unable to complete action at this time. If the problem persists please contact support.")}await m.create({})}})()},[]);let eh=e=>{let t=e.some(e=>"password"===e.name&&!!e.value);return(!t||h.enterpriseSSO.enabled)&&(e=e.filter(e=>"password"!==e.name)),{...(0,A.ni)(e),...t&&!h.enterpriseSSO.enabled&&{strategy:"password"}}},ef=(e,t)=>e.then(e=>{var o,r;if(!h.enterpriseSSO.enabled)return e;let n=null===(o=t.find(e=>"password"===e.name))||void 0===o?void 0:o.value;return!n||(null===(r=e.supportedFirstFactors)||void 0===r?void 0:r.some(e=>"saml"===e.strategy||"enterprise_sso"===e.strategy))?e:e.attemptFirstFactor({strategy:"password",password:n})}),em=async function(){for(var e,t=arguments.length,o=Array(t),r=0;r<t;r++)o[r]=arguments[r];let n=(null==ee?void 0:ee.channel)||(0,q.Vs)(o,f.preferredChannels,"identifier");if(n){let e=()=>{};o.push({id:"strategy",value:"phone_code",clearFeedback:e,setValue:e,onChange:e,setError:e}),o.push({id:"channel",value:n,clearFeedback:e,setValue:e,onChange:e,setError:e})}try{let t=await ef(m.create(eh(o)),o);switch(t.status){case"needs_identifier":(null===(e=t.supportedFirstFactors)||void 0===e?void 0:e.some(e=>"saml"===e.strategy||"enterprise_sso"===e.strategy))&&await eg();break;case"needs_first_factor":if(eD(t)){await eg();break}return g("factor-one");case"needs_second_factor":return g("factor-two");case"complete":return a.setActive({session:t.createdSessionId,redirectUrl:I});default:console.error((0,b.Ws)(t.status,U));return}}catch(e){return ev(e)}},eg=async()=>{let e=(0,L.wT)(v,u.signInUrl),t=v.afterSignInUrl||"/";return m.authenticateWithRedirect({strategy:"enterprise_sso",redirectUrl:e,redirectUrlComplete:t,oidcPrompt:v.oidcPrompt})},ev=async e=>{if(!e.errors)return;let t=e.errors.find(e=>e.code===eK.O1.INVALID_STRATEGY_FOR_USER||e.code===eK.O1.FORM_PASSWORD_INCORRECT||e.code===eK.O1.FORM_PASSWORD_PWNED),r=e.errors.find(e=>"identifier_already_signed_in"===e.code),n=e.errors.find(e=>e.code===eK.O1.INVITATION_ACCOUNT_NOT_EXISTS||e.code===eK.O1.FORM_IDENTIFIER_NOT_FOUND);if(t)await em(eu);else if(r){let e=r.meta.sessionId;await a.setActive({session:e,redirectUrl:I})}else if(P&&n){var i,s;let e=(0,q.s2)(eu);if(h.signUp.mode===eK.ci.WAITLIST)return g(a.buildWaitlistUrl("emailAddress"===e?{initialValues:{[e]:eu.value}}:{}));a.client.signUp[e]=eu.value;let t=(0,L.wT)(v,u.signUpUrl),r=v.afterSignUpUrl||"/";return function(e){var t,o;let{identifierAttribute:r,identifierValue:n,signUpMode:i,navigate:a,organizationTicket:s,afterSignUpUrl:l,clerk:c,handleError:d,redirectUrl:u,redirectUrlComplete:p,passwordEnabled:h,alternativePhoneCodeChannel:f}=e;if(i===eK.ci.WAITLIST)return a(c.buildWaitlistUrl("emailAddress"===r?{initialValues:{[r]:n}}:{}));c.client.signUp[r]=n;let m=new URLSearchParams;return(s&&m.set("__clerk_ticket",s),h||(t=c.client.signUp,o=r,t.optionalFields.filter(e=>!(e.startsWith("oauth_")||e.startsWith("web3_")||["enterprise_sso","saml"].includes(e))&&"password"!==e&&("phoneNumber"!==o||"phone_number"!==e)).length>0)||"emailAddress"!==r&&"phoneNumber"!==r)?a("create",{searchParams:m}):c.client.signUp.create({[r]:n,...f?{strategy:"phone_code",channel:f}:{}}).then(async e=>(await C())({signUp:e,verifyEmailPath:"create/verify-email-address",verifyPhonePath:"create/verify-phone-number",handleComplete:()=>c.setActive({session:e.createdSessionId,redirectUrl:l}),navigate:a,redirectUrl:u,redirectUrlComplete:p})).catch(e=>d(e))}({afterSignUpUrl:v.afterSignUpUrl||"/",clerk:a,handleError:e=>(0,A.S3)(e,[eu,J],o.setError),identifierAttribute:e,identifierValue:eu.value,navigate:g,organizationTicket:W,signUpMode:h.signUp.mode,redirectUrl:t,redirectUrlComplete:r,passwordEnabled:null!==(s=null===(i=h.attributes.password)||void 0===i?void 0:i.required)&&void 0!==s&&s,alternativePhoneCodeChannel:(null==ee?void 0:ee.channel)||(0,q.mQ)(f.preferredChannels,e,eu.value)})}else(0,A.S3)(e,[eu,J],o.setError)},eS=async e=>(e.preventDefault(),em(eu,J)),eC=(0,i.useMemo)(()=>({tel:B.l.PhoneInput,password:B.l.PasswordInput,text:B.l.PlainInput,email:B.l.PlainInput})[eu.type],[eu.type]);if(d.isLoading||"sign_up"===N)return(0,r.tZ)(D.W,{});let{action:ey,...e_}=eu.props;return(0,r.tZ)(l.Flow.Part,{part:"start",children:ee?(0,r.tZ)(eM,{handleSubmit:eS,phoneNumberFormState:ed,onUseAnotherMethod:()=>{et(null)},phoneCodeProvider:ee}):(0,r.BX)(y.Z.Root,{children:[(0,r.BX)(y.Z.Content,{children:[(0,r.BX)(w.h.Root,{showLogo:!0,children:[(0,r.tZ)(w.h.Title,{localizationKey:P?(0,l.localizationKeys)("signIn.start.titleCombined"):(0,l.localizationKeys)("signIn.start.title")}),(0,r.tZ)(w.h.Subtitle,{localizationKey:P?(0,l.localizationKeys)("signIn.start.subtitleCombined"):(0,l.localizationKeys)("signIn.start.subtitle"),sx:{"&:empty":{display:"none"}}})]}),(0,r.tZ)(y.Z.Alert,{children:o.error}),(0,r.BX)(l.Col,{elementDescriptor:l.descriptors.main,gap:6,children:[(0,r.BX)(eL.G,{children:[ea&&(0,r.tZ)(Y,{enableWeb3Providers:!0,enableOAuthProviders:!0,enableAlternativePhoneCodeProviders:eo,onAlternativePhoneCodeProviderClick:e=>{et((0,en.H)(e)||null)}}),$.length?(0,r.BX)(B.l.Root,{onSubmit:eS,gap:8,children:[(0,r.BX)(l.Col,{gap:6,children:[(0,r.tZ)(B.l.ControlRow,{elementId:eu.id,children:(0,r.tZ)(eC,{actionLabel:null==Q?void 0:Q.action,onActionClicked:()=>{M(e=>E[(E.indexOf(e)+1)%E.length]),el(!0),X(!1)},...e_,autoFocus:es,autoComplete:F?"webauthn":void 0})}),(0,r.tZ)(eX,{field:G?J:void 0})]}),(0,r.BX)(l.Col,{center:!0,children:[(0,r.tZ)(ex.S,{}),(0,r.tZ)(B.l.SubmitButton,{hasArrow:!0})]})]}):null]}),!$.length&&(0,r.tZ)(ex.S,{}),(null===(t=h.attributes.passkey)||void 0===t?void 0:t.enabled)&&h.passkeySettings.show_sign_in_button&&K&&(0,r.tZ)(y.Z.Action,{elementId:"usePasskey",children:(0,r.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)("signIn.start.actionLink__use_passkey"),onClick:()=>R({flow:"discoverable"})})})]})]}),(0,r.BX)(y.Z.Footer,{children:[h.signUp.mode===eK.ci.PUBLIC&&!P&&(0,r.BX)(y.Z.Action,{elementId:"signIn",children:[(0,r.tZ)(y.Z.ActionText,{localizationKey:(0,l.localizationKeys)("signIn.start.actionText")}),(0,r.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)("signIn.start.actionLink"),to:a.buildUrlWithAuth(S)})]}),h.signUp.mode===eK.ci.WAITLIST&&(0,r.BX)(y.Z.Action,{elementId:"signIn",children:[(0,r.tZ)(y.Z.ActionText,{localizationKey:(0,l.localizationKeys)("signIn.start.actionText__join_waitlist")}),(0,r.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)("signIn.start.actionLink__join_waitlist"),to:a.buildUrlWithAuth(k)})]})]})]})})}));function eN(){let e=(0,n.cL)();return i.useEffect(()=>{e.redirectToSignIn()},[]),null}function e$(){let e=(0,s.useSignInContext)(),t=(0,s.useSignUpContext)();return(0,r.tZ)(l.Flow.Root,{flow:"signIn",children:(0,r.BX)(p.Switch,{children:[(0,r.tZ)(p.Route,{path:"factor-one",children:(0,r.tZ)(ew,{})}),(0,r.tZ)(p.Route,{path:"factor-two",children:(0,r.tZ)(ez,{})}),(0,r.tZ)(p.Route,{path:"reset-password",children:(0,r.tZ)(k,{})}),(0,r.tZ)(p.Route,{path:"reset-password-success",children:(0,r.tZ)(E,{})}),(0,r.tZ)(p.Route,{path:"sso-callback",children:(0,r.tZ)(eF,{signUpUrl:e.signUpUrl,signInUrl:e.signInUrl,signInForceRedirectUrl:e.afterSignInUrl,signUpForceRedirectUrl:e.afterSignUpUrl,continueSignUpUrl:e.signUpContinueUrl,transferable:e.transferable,firstFactorUrl:"../factor-one",secondFactorUrl:"../factor-two",resetPasswordUrl:"../reset-password"})}),(0,r.tZ)(p.Route,{path:"choose",children:(0,r.tZ)(M,{})}),(0,r.tZ)(p.Route,{path:"verify",children:(0,r.tZ)(a.J,{redirectUrlComplete:e.afterSignInUrl,redirectUrl:"../factor-two"})}),e.isCombinedFlow&&(0,r.BX)(p.Route,{path:"create",children:[(0,r.tZ)(p.Route,{path:"verify-email-address",canActivate:e=>!!e.client.signUp.emailAddress,children:(0,r.tZ)(g,{})}),(0,r.tZ)(p.Route,{path:"verify-phone-number",canActivate:e=>!!e.client.signUp.phoneNumber,children:(0,r.tZ)(m,{})}),(0,r.tZ)(p.Route,{path:"sso-callback",children:(0,r.tZ)(I,{signUpUrl:t.signUpUrl,signInUrl:t.signInUrl,signUpForceRedirectUrl:t.afterSignUpUrl,signInForceRedirectUrl:t.afterSignInUrl,secondFactorUrl:t.secondFactorUrl,continueSignUpUrl:"../continue",verifyEmailAddressUrl:"../verify-email-address",verifyPhoneNumberUrl:"../verify-phone-number"})}),(0,r.tZ)(p.Route,{path:"verify",children:(0,r.tZ)(a.$,{redirectUrlComplete:t.afterSignUpUrl,verifyEmailPath:"../verify-email-address",verifyPhonePath:"../verify-phone-number",continuePath:"../continue"})}),(0,r.BX)(p.Route,{path:"continue",children:[(0,r.tZ)(p.Route,{path:"verify-email-address",canActivate:e=>!!e.client.signUp.emailAddress,children:(0,r.tZ)(g,{})}),(0,r.tZ)(p.Route,{path:"verify-phone-number",canActivate:e=>!!e.client.signUp.phoneNumber,children:(0,r.tZ)(m,{})}),(0,r.tZ)(p.Route,{path:"tasks",children:(0,r.tZ)(u.x7,{})}),(0,r.tZ)(p.Route,{index:!0,children:(0,r.tZ)(S,{})})]}),(0,r.tZ)(p.Route,{index:!0,children:(0,r.tZ)(v,{})})]}),(0,r.tZ)(p.Route,{path:"tasks",children:(0,r.tZ)(u.x7,{})}),(0,r.tZ)(p.Route,{index:!0,children:(0,r.tZ)(eW,{})}),(0,r.tZ)(p.Route,{children:(0,r.tZ)(eN,{})})]})})}let eV=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(0,c.ib)(e?f:void 0,"preloadComponent",{staleTime:1/0})};e$.displayName="SignIn";let eH=(0,s.withCoreSessionSwitchGuard)(function(){let{__internal_setComponentNavigationContext:e}=(0,n.cL)(),{navigate:t,indexPath:o}=(0,p.useRouter)(),a=(0,s.useSignInContext)(),l={componentName:"SignUp",emailLinkRedirectUrl:a.emailLinkRedirectUrl,ssoCallbackUrl:a.ssoCallbackUrl,forceRedirectUrl:a.signUpForceRedirectUrl,fallbackRedirectUrl:a.signUpFallbackRedirectUrl,signInUrl:a.signInUrl,unsafeMetadata:a.unsafeMetadata,...(0,h.L)({routing:null==a?void 0:a.routing,path:null==a?void 0:a.path})};return eV(a.isCombinedFlow),(0,d.z)(),i.useEffect(()=>null==e?void 0:e({indexPath:o,navigate:t}),[o,t]),(0,r.tZ)(s.SignUpContext.Provider,{value:l,children:(0,r.tZ)(e$,{})})}),eG=e=>{let t={signUpUrl:"/".concat(p.VIRTUAL_ROUTER_BASE_PATH,"/sign-up"),waitlistUrl:"/".concat(p.VIRTUAL_ROUTER_BASE_PATH,"/waitlist"),...e};return(0,r.tZ)(p.Route,{path:"sign-in",children:(0,r.tZ)(s.SignInContext.Provider,{value:{componentName:"SignIn",...t,routing:"virtual",mode:"modal"},children:(0,r.tZ)("div",{children:(0,r.tZ)(eH,{...t,routing:"virtual"})})})})}},95518:function(e,t,o){o.d(t,{Vh:()=>f,Vs:()=>v,bx:()=>p,mQ:()=>I,s2:()=>g,t3:()=>c,xT:()=>u}),o(28419),o(56113),o(65027);var r=o(32208),n=o(7772),i=o(77623),a=o(90577);let s=e=>t=>"safeIdentifier"in t&&t.safeIdentifier===e;function l(e){if((0,r.iW)()){let t=e.find(e=>{let{strategy:t}=e;return"passkey"===t});if(t)return t}return null}function c(e,t,o){return e&&0!==e.length?o===n.kJ.Password?function(e,t){let o=l(e);if(o)return o;let r=e.sort(a.sZ)[0];return"password"===r.strategy?r:e.find(s(t))||r||null}(e,t):function(e,t){let o=l(e);if(o)return o;let r=e.sort(a.b8),n=r.find(s(t));if(n)return n;let i=r[0];return"email_link"===i.strategy?i:e.find(s(t))||i||null}(e,t):null}let d=["passkey","email_code","password","phone_code","email_link"];function u(e){return!!e&&d.includes(e.strategy)}function p(e){if(!e||0===e.length)return null;let t=e.find(e=>"totp"===e.strategy);if(t)return t;let o=e.find(e=>"phone_code"===e.strategy);return o||e[0]}let h=["reset_password_phone_code","reset_password_email_code"],f=e=>!!e&&h.includes(e),m=e=>/^\S+@\S+\.\S+$/.test(e);function g(e){return"tel"===e.type?"phoneNumber":m(e.value)?"emailAddress":"username"}let v=(e,t,o)=>{var r,n;if(!t)return null;let a=null===(r=e.find(e=>"strategy"===e.id))||void 0===r?void 0:r.value;if(a&&"phone_code"!==a)return null;let s=null===(n=e.find(e=>e.id===o))||void 0===n?void 0:n.value;if(!s||!(null==s?void 0:s.startsWith("+")))return null;let l=(0,i.jR)(s,t);return"sms"===l?null:l},I=(e,t,o)=>{if(!e||!t||"phoneNumber"!==t||!o||!(null==o?void 0:o.startsWith("+")))return null;let r=(0,i.jR)(o,e);return"sms"===r?null:r}},49327:function(e,t,o){o.d(t,{fB:()=>p,gW:()=>f,uj:()=>h}),o(28419);var r=o(79109),n=o(42305),i=o(48774),a=o(97295),s=o(24676),l=o(31673),c=o(11576),d=o(39541),u=o(96519);let p=e=>{let{navigate:t}=(0,s.useRouter)(),{handleManageAccountClicked:o,handleSignOutSessionClicked:i,handleUserProfileActionClicked:a,session:u}=e,{menutItems:p}=(0,c.useUserButtonContext)(),h=e=>({borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100,padding:"".concat(e.space.$4," ").concat(e.space.$5)}),f=async r=>{var n;return(null==r?void 0:r.path)?(await t(r.path),null==e?void 0:e.completedCallback()):r.id===l.Zb.MANAGE_ACCOUNT?await o():(null==r?void 0:r.open)?a(r.open):(null===(n=r.onClick)||void 0===n||n.call(r),null==e?void 0:e.completedCallback())};return(0,r.tZ)(n.eX,{role:"menu",elementDescriptor:d.descriptors.userButtonPopoverActions,elementId:d.descriptors.userButtonPopoverActions.setId("singleSession"),sx:e=>({borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100}),children:null==p?void 0:p.map(e=>{let t;return t=Object.values(l.Zb).includes(e.id)?{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId(e.id)}:{elementDescriptor:d.descriptors.userButtonPopoverCustomItemButton,elementId:d.descriptors.userButtonPopoverCustomItemButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverCustomItemButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverCustomItemButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionItemButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionItemButtonIcon.setId(e.id)},(0,r.tZ)(n.aU,{...t,icon:e.icon,label:e.name,onClick:e.id===l.Zb.SIGN_OUT?i(u):()=>f(e),sx:h,iconSx:e=>({width:e.sizes.$4,height:e.sizes.$4})},e.id)})})},h=e=>{let{navigate:t}=(0,s.useRouter)(),{handleManageAccountClicked:o,handleSignOutSessionClicked:p,handleSessionClicked:h,handleAddAccountClicked:f,handleUserProfileActionClicked:m,session:g,otherSessions:v}=e,{menutItems:I}=(0,c.useUserButtonContext)(),S=async r=>{var n;return(null==r?void 0:r.path)?(await t(r.path),null==e?void 0:e.completedCallback()):r.id===l.Zb.MANAGE_ACCOUNT?await o():(null==r?void 0:r.open)?m(r.open):(null===(n=r.onClick)||void 0===n||n.call(r),null==e?void 0:e.completedCallback())},C=I.every(e=>Object.values(l.Zb).includes(e.id));return(0,r.BX)(r.HY,{children:[C?(0,r.tZ)(n.r5,{role:"menu",elementDescriptor:d.descriptors.userButtonPopoverActions,elementId:d.descriptors.userButtonPopoverActions.setId("multiSession"),children:(0,r.BX)(d.Flex,{justify:"between",sx:e=>({marginLeft:e.space.$12,padding:"0 ".concat(e.space.$5," ").concat(e.space.$4),gap:e.space.$2}),children:[(0,r.tZ)(n.U8,{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId("manageAccount"),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId("manageAccount"),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId("manageAccount"),icon:u.tc,label:(0,d.localizationKeys)("userButton.action__manageAccount"),onClick:o}),(0,r.tZ)(n.U8,{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId("signOut"),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId("signOut"),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId("signOut"),icon:u.lv,label:(0,d.localizationKeys)("userButton.action__signOut"),onClick:p(g)})]})}):(0,r.tZ)(n.r5,{role:"menu",elementDescriptor:d.descriptors.userButtonPopoverActions,elementId:d.descriptors.userButtonPopoverActions.setId("multiSession"),sx:e=>({gap:e.space.$1,paddingBottom:e.space.$2}),children:null==I?void 0:I.map(e=>{let t;return t=Object.values(l.Zb).includes(e.id)?{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId(e.id)}:{elementDescriptor:d.descriptors.userButtonPopoverCustomItemButton,elementId:d.descriptors.userButtonPopoverCustomItemButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverCustomItemButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverCustomItemButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionItemButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionItemButtonIcon.setId(e.id)},(0,r.tZ)(n.aU,{...t,icon:e.icon,label:e.name,onClick:e.id===l.Zb.SIGN_OUT?p(g):()=>S(e),sx:e=>({border:0,padding:"".concat(e.space.$2," ").concat(e.space.$5),gap:e.space.$3x5}),iconSx:e=>({width:e.sizes.$4,height:e.sizes.$4}),iconBoxSx:e=>({minHeight:e.sizes.$4,minWidth:e.sizes.$4,alignItems:"center"})},e.id)})}),(0,r.BX)(n.eX,{role:"menu",sx:e=>({borderTopStyle:e.borderStyles.$solid,borderTopWidth:e.borderWidths.$normal,borderTopColor:e.colors.$neutralAlpha100}),children:[v.map(e=>(0,r.tZ)(i.K,{icon:u.nR,onClick:h(e),role:"menuitem",children:(0,r.tZ)(a.E,{user:e.user})},e.id)),(0,r.tZ)(n.aU,{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId("addAccount"),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId("addAccount"),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId("addAccount"),icon:u.mm,label:(0,d.localizationKeys)("userButton.action__addAccount"),onClick:f,iconSx:e=>({width:e.sizes.$9,height:e.sizes.$6}),iconBoxSx:e=>({minHeight:e.sizes.$9,minWidth:e.sizes.$6,alignItems:"center"}),spinnerSize:"md"})]})]})},f=e=>{let{handleSignOutAllClicked:t,elementDescriptor:o,elementId:i,iconBoxElementDescriptor:a,iconBoxElementId:s,iconElementDescriptor:l,iconElementId:c,label:p,sx:h,actionSx:f}=e;return(0,r.tZ)(n.eX,{role:"menu",sx:[e=>({padding:e.space.$2}),h],children:(0,r.tZ)(n.aU,{elementDescriptor:o||d.descriptors.userButtonPopoverActionButton,elementId:i||d.descriptors.userButtonPopoverActionButton.setId("signOutAll"),iconBoxElementDescriptor:a||d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:s||d.descriptors.userButtonPopoverActionButtonIconBox.setId("signOutAll"),iconElementDescriptor:l||d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:c||d.descriptors.userButtonPopoverActionButtonIcon.setId("signOutAll"),icon:u.lv,label:p||(0,d.localizationKeys)("userButton.action__signOutAll"),onClick:t,variant:"ghost",colorScheme:"neutral",sx:[e=>({backgroundColor:e.colors.$transparent,padding:"".concat(e.space.$2," ").concat(e.space.$3),borderBottomWidth:0,borderRadius:e.radii.$lg}),f],spinnerSize:"md"})})}},35241:function(e,t,o){o.d(t,{Z:()=>c}),o(38062),o(50725);var r=o(83799),n=o(2672),i=o(55809),a=o(54264),s=o(24676),l=o(77623);let c=e=>{let{setActive:t,signOut:o,openUserProfile:c}=(0,r.cL)(),d=(0,n.useCardState)(),{signedInSessions:u,otherSessions:p}=(0,a.j)({user:e.user}),{navigate:h}=(0,s.useRouter)();return{handleSignOutSessionClicked:t=>()=>0===p.length?o(e.navigateAfterSignOut):o(e.navigateAfterMultiSessionSingleSignOut,{sessionId:t.id}).finally(()=>d.setIdle()),handleManageAccountClicked:()=>{var t;return"navigation"===e.userProfileMode?h(e.userProfileUrl||"").finally(()=>{(async()=>{var t;await (0,l._v)(300),null===(t=e.actionCompleteCallback)||void 0===t||t.call(e)})()}):(c(e.userProfileProps),null===(t=e.actionCompleteCallback)||void 0===t?void 0:t.call(e))},handleUserProfileActionClicked:t=>{var o;return"navigation"===e.userProfileMode?h(e.userProfileUrl||"").finally(()=>{(async()=>{var t;await (0,l._v)(300),null===(t=e.actionCompleteCallback)||void 0===t||t.call(e)})()}):(c({...e.userProfileProps,...t&&{__experimental_startPath:t}}),null===(o=e.actionCompleteCallback)||void 0===o?void 0:o.call(e))},handleSignOutAllClicked:()=>o(e.navigateAfterSignOut),handleSessionClicked:o=>async()=>(d.setLoading(),t({session:o,redirectUrl:e.afterSwitchSessionUrl}).finally(()=>{var t;d.setIdle(),null===(t=e.actionCompleteCallback)||void 0===t||t.call(e)})),handleAddAccountClicked:()=>((0,i.T7)(e.signInUrl||window.location.href),(0,l._v)(2e3)),otherSessions:p,signedInSessions:u}}}}]);