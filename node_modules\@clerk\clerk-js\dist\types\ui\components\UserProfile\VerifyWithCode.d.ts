import type { EmailAddressResource, PhoneNumberResource } from '@clerk/types';
type VerifyWithCodeProps = {
    nextStep: () => void;
    identification?: EmailAddressResource | PhoneNumberResource;
    identifier?: string;
    prepareVerification?: () => Promise<any> | undefined;
    onReset: () => void;
};
export declare const VerifyWithCode: (props: VerifyWithCodeProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
