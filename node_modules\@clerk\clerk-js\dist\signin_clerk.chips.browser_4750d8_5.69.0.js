"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["722"],{7389:function(e,t,r){r.d(t,{v:()=>s});var o=r(9109),n=r(4562),i=r(9541);let s=e=>{let{onBackLinkClick:t}=e;return(0,o.tZ)(n._,{cardTitle:(0,i.localizationKeys)("signIn.alternativeMethods.getHelp.title"),cardSubtitle:(0,i.localizationKeys)("signIn.alternativeMethods.getHelp.content"),onBackLinkClick:t})}},6154:function(e,t,r){r.r(t),r.d(t,{SignIn:()=>eH,SignInModal:()=>eG});var o=r(9109),n=r(3799),i=r(9144),s=r(5112),a=r(1576),l=r(9541),c=r(2464),d=r(4511),u=r(7508),p=r(4676),h=r(2073);let m=()=>Promise.all([r.e("200"),r.e("573"),r.e("710")]).then(r.bind(r,5049)),f=(0,i.lazy)(()=>m().then(e=>({default:e.SignUpVerifyPhone}))),g=(0,i.lazy)(()=>m().then(e=>({default:e.SignUpVerifyEmail}))),I=(0,i.lazy)(()=>m().then(e=>({default:e.SignUpStart}))),S=(0,i.lazy)(()=>m().then(e=>({default:e.SignUpSSOCallback}))),C=(0,i.lazy)(()=>m().then(e=>({default:e.SignUpContinue}))),v=()=>Promise.all([r.e("200"),r.e("573"),r.e("710")]).then(r.bind(r,6052)).then(e=>e.completeSignUpFlow);var y=r(4455),_=r(2672),B=r(431),w=r(2654),b=r(4152),Z=r(3234),A=r(7623);let k=(0,_.withCardStateProvider)(()=>{let e=(0,a.useCoreSignIn)(),t=(0,_.useCardState)(),{navigate:r}=(0,p.useRouter)(),n=(0,Z.H)(),{userSettings:{passwordSettings:s}}=(0,a.useEnvironment)(),{t:d,locale:u}=(0,l.useLocalizations)(),h="needs_new_password"===e.status&&"reset_password_email_code"!==e.firstFactorVerification.strategy&&"reset_password_phone_code"!==e.firstFactorVerification.strategy;i.useEffect(()=>{h&&t.setError(d((0,l.localizationKeys)("signIn.resetPassword.requiredMessage")))},[]);let m=(0,A.Yp)("password","",{type:"password",label:(0,l.localizationKeys)("formFieldLabel__newPassword"),isRequired:!0,validatePassword:!0,buildErrorMessage:e=>(0,A.GM)(e,{t:d,locale:u,passwordSettings:s})}),f=(0,A.Yp)("confirmPassword","",{type:"password",label:(0,l.localizationKeys)("formFieldLabel__confirmPassword"),isRequired:!0}),g=(0,A.Yp)("signOutOfOtherSessions","",{type:"checkbox",label:(0,l.localizationKeys)("formFieldLabel__signOutOfOtherSessions"),defaultChecked:!0}),{setConfirmPasswordFeedback:I,isPasswordMatch:S}=(0,c.p5)({passwordField:m,confirmPasswordField:f}),C=async()=>{m.clearFeedback(),f.clearFeedback();try{let{status:t,createdSessionId:o}=await e.resetPassword({password:m.value,signOutOfOtherSessions:g.checked});switch(t){case"complete":if(o){let e=new URLSearchParams;return e.set("createdSessionId",o),r(`../reset-password-success?${e.toString()}`)}return console.error((0,b.Ws)(t,n));case"needs_second_factor":return r("../factor-two");default:return console.error((0,b.Ws)(t,n))}}catch(e){return(0,A.S3)(e,[m,f],t.setError)}};return(0,o.BX)(y.Z.Root,{children:[(0,o.BX)(y.Z.Content,{children:[(0,o.tZ)(w.h.Root,{showLogo:!0,children:(0,o.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.resetPassword.title")})}),(0,o.tZ)(y.Z.Alert,{children:t.error}),(0,o.tZ)(l.Col,{elementDescriptor:l.descriptors.main,gap:8,children:(0,o.BX)(B.l.Root,{onSubmit:C,onBlur:()=>{m.value&&I(f.value)},gap:8,children:[(0,o.BX)(l.Col,{gap:6,children:[(0,o.tZ)("input",{readOnly:!0,"data-testid":"hidden-identifier",id:"identifier-field",name:"identifier",value:e.identifier||"",style:{display:"none"}}),(0,o.tZ)(B.l.ControlRow,{elementId:m.id,children:(0,o.tZ)(B.l.PasswordInput,{...m.props,isRequired:!0,autoFocus:!0})}),(0,o.tZ)(B.l.ControlRow,{elementId:f.id,children:(0,o.tZ)(B.l.PasswordInput,{...f.props,onChange:e=>(e.target.value&&I(e.target.value),f.props.onChange(e))})}),!h&&(0,o.tZ)(B.l.ControlRow,{elementId:g.id,children:(0,o.tZ)(B.l.Checkbox,{...g.props})})]}),(0,o.BX)(l.Col,{gap:3,children:[(0,o.tZ)(B.l.SubmitButton,{isDisabled:!S,localizationKey:(0,l.localizationKeys)("signIn.resetPassword.formButtonPrimary")}),(0,o.tZ)(y.Z.Action,{elementId:"alternativeMethods",children:(0,o.tZ)(y.Z.ActionLink,{elementDescriptor:l.descriptors.backLink,localizationKey:(0,l.localizationKeys)("backButton"),onClick:()=>r("../")})})]})]})})]}),(0,o.tZ)(y.Z.Footer,{})]})});var P=r(4875),U=r(215);let E=(0,_.withCardStateProvider)(()=>{let e=(0,_.useCardState)();return(0,P.E)(),(0,o.BX)(y.Z.Root,{children:[(0,o.BX)(y.Z.Content,{children:[(0,o.tZ)(w.h.Root,{showLogo:!0,children:(0,o.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.resetPassword.title")})}),(0,o.tZ)(y.Z.Alert,{children:e.error}),(0,o.BX)(l.Col,{elementDescriptor:l.descriptors.main,gap:8,children:[(0,o.tZ)(l.Text,{localizationKey:(0,l.localizationKeys)("signIn.resetPassword.successMessage")}),(0,o.tZ)(U.kC,{direction:"row",center:!0,children:(0,o.tZ)(l.Spinner,{size:"xl",colorScheme:"primary",elementDescriptor:l.descriptors.spinner})})]})]}),(0,o.tZ)(y.Z.Footer,{})]})});var z=r(2305),F=r(8774),R=r(7295),L=r(8969),K=r(4174),T=r(9327),x=r(5241);let M=(0,L.Hy)((0,_.withCardStateProvider)(()=>{let e=(0,_.useCardState)(),{userProfileUrl:t}=(0,a.useEnvironment)().displayConfig,{afterSignInUrl:r,path:n}=(0,a.useSignInContext)(),{navigateAfterSignOut:i}=(0,a.useSignOutContext)(),{handleSignOutAllClicked:s,handleSessionClicked:c,signedInSessions:d,handleAddAccountClicked:u}=(0,x.Z)({navigateAfterSignOut:i,afterSwitchSessionUrl:r,userProfileUrl:t,signInUrl:n,user:void 0});return(0,o.tZ)(l.Flow.Part,{part:"accountSwitcher",children:(0,o.BX)(y.Z.Root,{children:[(0,o.BX)(y.Z.Content,{sx:e=>({padding:`${e.space.$8} ${e.space.$none} ${e.space.$none}`}),children:[(0,o.BX)(w.h.Root,{children:[(0,o.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.accountSwitcher.title")}),(0,o.tZ)(w.h.Subtitle,{localizationKey:(0,l.localizationKeys)("signIn.accountSwitcher.subtitle")})]}),(0,o.tZ)(y.Z.Alert,{children:e.error}),(0,o.tZ)(l.Col,{elementDescriptor:l.descriptors.main,gap:8,sx:e=>({borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100}),children:(0,o.BX)(z.eX,{role:"menu",children:[d.map(e=>(0,o.tZ)(F.K,{onClick:c(e),sx:e=>({height:e.sizes.$16,justifyContent:"flex-start",borderRadius:0}),icon:K.nR,children:(0,o.tZ)(R.E,{user:e.user,sx:{width:"100%"}})},e.id)),(0,o.tZ)(z.aU,{elementDescriptor:l.descriptors.accountSwitcherActionButton,elementId:l.descriptors.accountSwitcherActionButton.setId("addAccount"),iconBoxElementDescriptor:l.descriptors.accountSwitcherActionButtonIconBox,iconBoxElementId:l.descriptors.accountSwitcherActionButtonIconBox.setId("addAccount"),iconElementDescriptor:l.descriptors.accountSwitcherActionButtonIcon,iconElementId:l.descriptors.accountSwitcherActionButtonIcon.setId("addAccount"),icon:K.mm,label:(0,l.localizationKeys)("signIn.accountSwitcher.action__addAccount"),onClick:u,iconSx:e=>({width:e.sizes.$9,height:e.sizes.$6}),iconBoxSx:e=>({minHeight:e.sizes.$9,minWidth:e.sizes.$6,alignItems:"center"}),spinnerSize:"md"})]})})]}),(0,o.tZ)(y.Z.Footer,{sx:e=>({">:first-of-type":{padding:`${e.space.$1}`,width:"100%"}}),children:(0,o.tZ)(y.Z.Action,{sx:{width:"100%",">:first-of-type":{width:"100%",borderBottomWidth:0}},children:(0,o.tZ)(T.gW,{handleSignOutAllClicked:s,elementDescriptor:l.descriptors.accountSwitcherActionButton,elementId:l.descriptors.accountSwitcherActionButton.setId("signOutAll"),iconBoxElementDescriptor:l.descriptors.accountSwitcherActionButtonIconBox,iconBoxElementId:l.descriptors.accountSwitcherActionButtonIconBox.setId("signOutAll"),iconElementDescriptor:l.descriptors.accountSwitcherActionButtonIcon,iconElementId:l.descriptors.accountSwitcherActionButtonIcon.setId("signOutAll"),label:(0,l.localizationKeys)("signIn.accountSwitcher.action__signOutAll"),actionSx:e=>({padding:`${e.space.$2} ${e.space.$2}`})})})})]})})}));var O=r(4562),D=r(1455),$=r(1752),X=r(1085),W=r(8246),N=r(5482),V=r(6735),H=r(6749),G=r(5747),j=r(138);let Y=i.memo(e=>{let t=(0,n.cL)(),{navigate:r}=(0,p.useRouter)(),i=(0,_.useCardState)(),{displayConfig:s}=(0,G.O)(),l=(0,a.useSignInContext)(),c=(0,a.useCoreSignIn)(),d=(0,H.wT)(l,s.signInUrl),u=l.afterSignInUrl||"/",h="popup"===l.oauthFlow||"auto"===l.oauthFlow&&(0,A.tc)(),{onAlternativePhoneCodeProviderClick:m,...f}=e;return(0,o.tZ)(j.L,{...f,idleAfterDelay:!h,oauthCallback:e=>{if(h){let t=window.open("about:blank","","width=600,height=800"),r=setInterval(()=>{(!t||t.closed)&&(clearInterval(r),i.setIdle())},500);return c.authenticateWithPopup({strategy:e,redirectUrl:d,redirectUrlComplete:u,popup:t,oidcPrompt:l.oidcPrompt}).catch(e=>(0,A.S3)(e,[],i.setError))}return c.authenticateWithRedirect({strategy:e,redirectUrl:d,redirectUrlComplete:u,oidcPrompt:l.oidcPrompt}).catch(e=>(0,A.S3)(e,[],i.setError))},web3Callback:e=>t.authenticateWithWeb3({customNavigate:r,redirectUrl:u,signUpContinueUrl:l.isCombinedFlow?"create/continue":l.signUpContinueUrl,strategy:e,secondFactorUrl:"factor-two"}).catch(e=>(0,A.Ht)(e,i.setError)),alternativePhoneCodeCallback:e=>{m?.(e)}})});var q=r(5518);function Q(){let e=(0,a.useCoreSignIn)();return e.supportedFirstFactors?.find(({strategy:e})=>q.Vh(e))}var J=r(7389);let ee=(e,t)=>{let[r,n]=i.useState(!1),s=i.useCallback(()=>n(e=>!e),[n]);return r?(0,o.tZ)(J.v,{onBackLinkClick:s}):(0,o.tZ)(e,{...t,onHavingTroubleClick:s})},et=e=>ee(er,{...e}),er=e=>{let{onBackLinkClick:t,onHavingTroubleClick:r,onFactorSelected:n,mode:i="default"}=e,s=(0,_.useCardState)(),c=Q(),{supportedFirstFactors:d}=(0,a.useCoreSignIn)(),{firstPartyFactors:u,hasAnyStrategy:p}=(0,$.l)({filterOutFactor:e?.currentFactor,supportedFirstFactors:d}),h=function(e){switch(e){case"forgot":return"forgotPasswordMethods";case"pwned":return"passwordPwnedMethods";default:return"alternativeMethods"}}(i),m=function(e){switch(e){case"forgot":return(0,l.localizationKeys)("signIn.forgotPasswordAlternativeMethods.title");case"pwned":return(0,l.localizationKeys)("signIn.passwordPwned.title");default:return(0,l.localizationKeys)("signIn.alternativeMethods.title")}}(i),f=function(e){switch(e){case"forgot":case"pwned":return!0;default:return!1}}(i);return(0,o.tZ)(l.Flow.Part,{part:h,children:(0,o.BX)(y.Z.Root,{children:[(0,o.BX)(y.Z.Content,{children:[(0,o.BX)(w.h.Root,{showLogo:!0,children:[(0,o.tZ)(w.h.Title,{localizationKey:m}),!f&&(0,o.tZ)(w.h.Subtitle,{localizationKey:(0,l.localizationKeys)("signIn.alternativeMethods.subtitle")})]}),(0,o.tZ)(y.Z.Alert,{children:s.error}),(0,o.BX)(l.Flex,{direction:"col",elementDescriptor:l.descriptors.main,gap:6,children:[f&&c&&(0,o.tZ)(l.Button,{localizationKey:eo(c),elementDescriptor:l.descriptors.alternativeMethodsBlockButton,isDisabled:s.isLoading,onClick:()=>{s.setError(void 0),n(c)}}),f&&p&&(0,o.tZ)(V.i,{dividerText:(0,l.localizationKeys)("signIn.forgotPasswordAlternativeMethods.label__alternativeMethods")}),(0,o.BX)(l.Col,{gap:4,children:[p&&(0,o.BX)(l.Flex,{elementDescriptor:l.descriptors.alternativeMethods,direction:"col",gap:2,children:[(0,o.tZ)(Y,{enableWeb3Providers:!0,enableOAuthProviders:!0,enableAlternativePhoneCodeProviders:!1}),u&&u.map((e,t)=>{var r;return(0,o.tZ)(W.$,{leftIcon:(r=e,({email_link:K.xP,email_code:K.GT,phone_code:K.iU,reset_password_email_code:K.ds,reset_password_phone_code:K.ds,password:K.kh,passkey:K.IG})[r.strategy]),textLocalizationKey:eo(e),elementDescriptor:l.descriptors.alternativeMethodsBlockButton,textElementDescriptor:l.descriptors.alternativeMethodsBlockButtonText,arrowElementDescriptor:l.descriptors.alternativeMethodsBlockButtonArrow,textVariant:"buttonLarge",isDisabled:s.isLoading,onClick:()=>{s.setError(void 0),n(e)}},t)})]}),t&&(0,o.tZ)(N.h,{boxElementDescriptor:l.descriptors.backRow,linkElementDescriptor:l.descriptors.backLink,onClick:t})]})]})]}),(0,o.tZ)(y.Z.Footer,{children:(0,o.BX)(y.Z.Action,{elementId:"havingTrouble",children:[(0,o.tZ)(y.Z.ActionText,{localizationKey:(0,l.localizationKeys)("signIn.alternativeMethods.actionText")}),(0,o.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)("signIn.alternativeMethods.actionLink"),onClick:r})]})})]})})};function eo(e){switch(e.strategy){case"email_link":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__emailLink",{identifier:(0,A.HT)(e.safeIdentifier)||""});case"email_code":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__emailCode",{identifier:(0,A.HT)(e.safeIdentifier)||""});case"phone_code":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__phoneCode",{identifier:(0,A.HT)(e.safeIdentifier)||""});case"password":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__password");case"passkey":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__passkey");case"reset_password_email_code":case"reset_password_phone_code":return(0,l.localizationKeys)("signIn.forgotPasswordAlternativeMethods.blockButton__resetPassword");default:throw Error(`Invalid sign in strategy: "${e.strategy}"`)}}var en=r(6543),ei=r(3531),es=r(3465);let ea=e=>{let t=(0,a.useCoreSignIn)(),r=(0,_.useCardState)(),{navigate:i}=(0,p.useRouter)(),{afterSignInUrl:s}=(0,a.useSignInContext)(),{setActive:l}=(0,n.cL)(),c=(0,Z.H)(),d=(0,n.cL)(),u=e.factor.channel,h="verified"===t.firstFactorVerification.status&&e.factorAlreadyPrepared;return(0,o.tZ)(es.U,{cardTitle:e.cardTitle,cardSubtitle:e.cardSubtitle,inputLabel:e.inputLabel,resendButton:e.resendButton,onCodeEntryFinishedAction:(r,o,n)=>{t.attemptFirstFactor({strategy:e.factor.strategy,code:r}).then(async e=>{switch(await o(),e.status){case"complete":return l({session:e.createdSessionId,redirectUrl:s});case"needs_second_factor":return i("../factor-two");case"needs_new_password":return i("../reset-password");default:return console.error((0,b.Ws)(e.status,c))}}).catch(e=>(0,ei.ay)(e)?d.__internal_navigateWithError("..",e.errors[0]):n(e))},onResendCodeClicked:()=>{!h&&t.prepareFirstFactor({...e.factor,channel:u}).then(()=>e.onFactorPrepare()).catch(e=>(0,A.S3)(e,[],r.setError))},safeIdentifier:e.factor.safeIdentifier,profileImageUrl:t.userData.imageUrl,alternativeMethodsLabel:(0,X.u1)("footerActionLink__alternativePhoneCodeProvider"),onShowAlternativeMethodsClicked:()=>{r.setError(void 0),e.onChangePhoneCodeChannel({...e.factor,channel:void 0})},showAlternativeMethods:!0,onIdentityPreviewEditClicked:()=>i("../"),onBackLinkClicked:e.onBackLinkClicked})},el=e=>(0,o.tZ)(l.Flow.Part,{part:"phoneCode",children:(0,o.tZ)(ea,{...e,cardTitle:(0,l.localizationKeys)("signIn.alternativePhoneCodeProvider.title",{provider:en.H(e.factor.channel)?.name}),cardSubtitle:(0,l.localizationKeys)("signIn.alternativePhoneCodeProvider.subtitle"),inputLabel:(0,l.localizationKeys)("signIn.alternativePhoneCodeProvider.formTitle"),resendButton:(0,l.localizationKeys)("signIn.alternativePhoneCodeProvider.resendButton")})}),ec=e=>{let t=(0,a.useCoreSignIn)(),r=(0,_.useCardState)(),{navigate:i}=(0,p.useRouter)(),{afterSignInUrl:s}=(0,a.useSignInContext)(),{setActive:l}=(0,n.cL)(),d=(0,Z.H)(),u=(0,n.cL)(),h="verified"===t.firstFactorVerification.status&&e.factorAlreadyPrepared;return(0,c.ib)(h?void 0:()=>t?.prepareFirstFactor(e.factor).then(()=>e.onFactorPrepare()).catch(e=>A.S3(e,[],r.setError)),{name:"signIn.prepareFirstFactor",factor:e.factor,id:t.id},{staleTime:100}),(0,o.tZ)(es.U,{cardTitle:e.cardTitle,cardSubtitle:e.cardSubtitle,inputLabel:e.inputLabel,resendButton:e.resendButton,onCodeEntryFinishedAction:(r,o,n)=>{t.attemptFirstFactor({strategy:e.factor.strategy,code:r}).then(async e=>{switch(await o(),e.status){case"complete":return l({session:e.createdSessionId,redirectUrl:s});case"needs_second_factor":return i("../factor-two");case"needs_new_password":return i("../reset-password");default:return console.error((0,b.Ws)(e.status,d))}}).catch(e=>(0,ei.ay)(e)?u.__internal_navigateWithError("..",e.errors[0]):n(e))},onResendCodeClicked:()=>{!h&&t.prepareFirstFactor(e.factor).then(()=>e.onFactorPrepare()).catch(e=>(0,A.S3)(e,[],r.setError))},safeIdentifier:e.factor.safeIdentifier,profileImageUrl:t.userData.imageUrl,onShowAlternativeMethodsClicked:e.onShowAlternativeMethodsClicked,showAlternativeMethods:e.showAlternativeMethods,onIdentityPreviewEditClicked:()=>i("../"),onBackLinkClicked:e.onBackLinkClicked})},ed=e=>(0,o.tZ)(l.Flow.Part,{part:"emailCode",children:(0,o.tZ)(ec,{...e,cardTitle:(0,l.localizationKeys)("signIn.emailCode.title"),cardSubtitle:(0,l.localizationKeys)("signIn.emailCode.subtitle"),inputLabel:(0,l.localizationKeys)("signIn.emailCode.formTitle"),resendButton:(0,l.localizationKeys)("signIn.emailCode.resendButton")})});var eu=r(5495),ep=r(4814),eh=r(5117);let em=e=>{let{t}=(0,l.useLocalizations)(),r=(0,_.useCardState)(),s=(0,a.useCoreSignIn)(),c=(0,a.useSignInContext)(),{signInUrl:d}=c,{navigate:u}=(0,eh.t)(),{afterSignInUrl:p}=(0,a.useSignInContext)(),{setActive:h}=(0,n.cL)(),{startEmailLinkFlow:m,cancelEmailLinkFlow:f}=(0,ep.E)(s),[g,I]=i.useState(!1),S=(0,n.cL)();i.useEffect(()=>{C()},[]);let C=()=>{m({emailAddressId:e.factor.emailAddressId,redirectUrl:(0,H.Uu)({ctx:c,baseUrl:d,intent:"sign-in"})}).then(e=>v(e)).catch(e=>{if((0,ei.ay)(e))return S.__internal_navigateWithError("..",e.errors[0]);(0,A.S3)(e,[],r.setError)})},v=async e=>{let o=e.firstFactorVerification;"expired"===o.status?r.setError(t((0,l.localizationKeys)("formFieldError__verificationLinkExpired"))):o.verifiedFromTheSameClient()?I(!0):await y(e)},y=async e=>"complete"===e.status?h({session:e.createdSessionId,redirectUrl:p}):"needs_second_factor"===e.status?u("../factor-two"):void 0;return g?(0,o.tZ)(L.Ej,{title:(0,l.localizationKeys)("signIn.emailLink.verifiedSwitchTab.titleNewTab"),subtitle:(0,l.localizationKeys)("signIn.emailLink.verifiedSwitchTab.subtitleNewTab"),status:"verified_switch_tab"}):(0,o.tZ)(l.Flow.Part,{part:"emailLink",children:(0,o.tZ)(eu.J,{cardTitle:(0,l.localizationKeys)("signIn.emailLink.title"),cardSubtitle:(0,l.localizationKeys)("signIn.emailLink.subtitle"),formTitle:(0,l.localizationKeys)("signIn.emailLink.formTitle"),formSubtitle:(0,l.localizationKeys)("signIn.emailLink.formSubtitle"),resendButton:(0,l.localizationKeys)("signIn.emailLink.resendButton"),onResendCodeClicked:()=>{f(),C()},safeIdentifier:e.factor.safeIdentifier,profileImageUrl:s.userData.imageUrl,onShowAlternativeMethodsClicked:e.onShowAlternativeMethodsClicked})})},ef=e=>(0,o.tZ)(l.Flow.Part,{part:"resetPassword",children:(0,o.tZ)(ec,{...e,showAlternativeMethods:!1,cardTitle:(0,l.localizationKeys)("signIn.forgotPassword.title"),inputLabel:(0,l.localizationKeys)("signIn.forgotPassword.formTitle"),resendButton:(0,l.localizationKeys)("signIn.forgotPassword.resendButton")})});var eg=r(9577),eI=r(3367);function eS(e){let t=(0,_.useCardState)(),{setActive:r,__internal_navigateWithError:o}=(0,n.cL)(),s=(0,Z.H)(),{afterSignInUrl:l}=(0,a.useSignInContext)(),{authenticateWithPasskey:c}=(0,a.useCoreSignIn)();return(0,i.useEffect)(()=>()=>{eI.YI.abort()},[]),(0,i.useCallback)(async(...n)=>{try{let t=await c(...n);switch(t.status){case"complete":return r({session:t.createdSessionId,redirectUrl:l});case"needs_second_factor":return e();default:return console.error((0,b.Ws)(t.status,s))}}catch(r){let{flow:e}=n[0]||{};if((0,ei.uX)(r)&&("passkey_operation_aborted"===r.code||"autofill"===e&&"passkey_retrieval_cancelled"===r.code))return;if((0,ei.ay)(r))return o("..",r.errors[0]);(0,A.S3)(r,[],t.setError)}},[])}let eC=e=>{let{onShowAlternativeMethodsClick:t}=e,r=(0,_.useCardState)(),n=(0,a.useCoreSignIn)(),{navigate:s}=(0,eh.t)(),[c,d]=i.useState(!1),u=i.useCallback(()=>d(e=>!e),[d]),p=eS(()=>s("../factor-two"));return c?(0,o.tZ)(J.v,{onBackLinkClick:u}):(0,o.tZ)(l.Flow.Part,{part:"password",children:(0,o.BX)(y.Z.Root,{children:[(0,o.BX)(y.Z.Content,{children:[(0,o.BX)(w.h.Root,{showLogo:!0,children:[(0,o.tZ)(l.Icon,{elementDescriptor:l.descriptors.passkeyIcon,icon:K.IG,sx:e=>({color:e.colors.$neutralAlpha500,marginInline:"auto",paddingBottom:e.sizes.$1,width:e.sizes.$12,height:e.sizes.$12})}),(0,o.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.passkey.title")}),(0,o.tZ)(w.h.Subtitle,{localizationKey:(0,l.localizationKeys)("signIn.passkey.subtitle")}),(0,o.tZ)(eg.m,{identifier:n.identifier,avatarUrl:n.userData.imageUrl,onClick:()=>s("../")})]}),(0,o.tZ)(y.Z.Alert,{children:r.error}),(0,o.BX)(l.Flex,{direction:"col",elementDescriptor:l.descriptors.main,gap:4,children:[(0,o.tZ)(B.l.Root,{onSubmit:e=>(e.preventDefault(),p()),gap:8,children:(0,o.tZ)(B.l.SubmitButton,{hasArrow:!0})}),(0,o.tZ)(y.Z.Action,{elementId:t?"alternativeMethods":"havingTrouble",children:(0,o.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)(t?"footerActionLink__useAnotherMethod":"signIn.alternativeMethods.actionLink"),onClick:t||u})})]})]}),(0,o.tZ)(y.Z.Footer,{})]})})},ev=e=>{let{onForgotPasswordMethodClick:t,onShowAlternativeMethodsClick:r}=e,o=Q(),n=(0,A.Yp)("password","",{type:"password",label:(0,l.localizationKeys)("formFieldLabel__password"),placeholder:(0,l.localizationKeys)("formFieldInputPlaceholder__password")});return{...n,props:{...n.props,actionLabel:o||r?(0,l.localizationKeys)("formFieldAction__forgotPassword"):"",onActionClicked:t||r||(()=>null)}}},ey=e=>{let{onShowAlternativeMethodsClick:t,onPasswordPwned:r}=e,s=i.useRef(null),c=(0,_.useCardState)(),{setActive:d}=(0,n.cL)(),u=(0,a.useCoreSignIn)(),{afterSignInUrl:p}=(0,a.useSignInContext)(),h=(0,Z.H)(),m=ev(e),{navigate:f}=(0,eh.t)(),[g,I]=i.useState(!1),S=i.useCallback(()=>I(e=>!e),[I]),C=(0,n.cL)(),v=async e=>(e.preventDefault(),u.attemptFirstFactor({strategy:"password",password:m.value}).then(e=>{switch(e.status){case"complete":return d({session:e.createdSessionId,redirectUrl:p});case"needs_second_factor":return f("../factor-two");default:return console.error((0,b.Ws)(e.status,h))}}).catch(e=>{if((0,ei.ay)(e))return C.__internal_navigateWithError("..",e.errors[0]);if((0,ei.UZ)(e)&&r){c.setError({...e.errors[0],code:"form_password_pwned__sign_in"}),r();return}(0,A.S3)(e,[m],c.setError),setTimeout(()=>s.current?.focus(),0)}));return g?(0,o.tZ)(J.v,{onBackLinkClick:S}):(0,o.tZ)(l.Flow.Part,{part:"password",children:(0,o.BX)(y.Z.Root,{children:[(0,o.BX)(y.Z.Content,{children:[(0,o.BX)(w.h.Root,{showLogo:!0,children:[(0,o.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.password.title")}),(0,o.tZ)(w.h.Subtitle,{localizationKey:(0,l.localizationKeys)("signIn.password.subtitle")}),(0,o.tZ)(eg.m,{identifier:u.identifier,avatarUrl:u.userData.imageUrl,onClick:()=>f("../")})]}),(0,o.tZ)(y.Z.Alert,{children:c.error}),(0,o.BX)(l.Flex,{direction:"col",elementDescriptor:l.descriptors.main,gap:4,children:[(0,o.BX)(B.l.Root,{onSubmit:v,gap:8,children:[(0,o.tZ)("input",{readOnly:!0,id:"identifier-field",name:"identifier",value:u.identifier||"",style:{display:"none"}}),(0,o.tZ)(B.l.ControlRow,{elementId:m.id,children:(0,o.tZ)(B.l.PasswordInput,{...m.props,ref:s,autoFocus:!0})}),(0,o.tZ)(B.l.SubmitButton,{hasArrow:!0})]}),(0,o.tZ)(y.Z.Action,{elementId:t?"alternativeMethods":"havingTrouble",children:(0,o.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)(t?"signIn.password.actionLink":"signIn.alternativeMethods.actionLink"),onClick:t||S})})]})]}),(0,o.tZ)(y.Z.Footer,{})]})})},e_=e=>(0,o.tZ)(l.Flow.Part,{part:"phoneCode",children:(0,o.tZ)(ec,{...e,cardTitle:(0,l.localizationKeys)("signIn.phoneCode.title"),cardSubtitle:(0,l.localizationKeys)("signIn.phoneCode.subtitle"),inputLabel:(0,l.localizationKeys)("signIn.phoneCode.formTitle"),resendButton:(0,l.localizationKeys)("signIn.phoneCode.resendButton")})}),eB=e=>{if(!e)return"";let t=e.strategy;return"emailAddressId"in e&&(t+=e.emailAddressId),"phoneNumberId"in e&&(t+=e.phoneNumberId),"channel"in e&&(t+=e.channel),t},ew=(0,L.Hy)((0,_.withCardStateProvider)(function(){let e=(0,a.useCoreSignIn)(),{preferredSignInStrategy:t}=(0,a.useEnvironment)().displayConfig,r=e.supportedFirstFactors,n=(0,p.useRouter)(),s=(0,_.useCardState)(),{supportedFirstFactors:l,firstFactorVerification:c}=(0,a.useCoreSignIn)(),d=i.useRef(""),[{currentFactor:u},h]=i.useState(()=>{let o=(0,q.t3)(r,e.identifier,t);return o?.strategy==="phone_code"&&c.channel&&"sms"!==c.channel&&(o.channel=c.channel),{currentFactor:o,prevCurrentFactor:void 0}}),{hasAnyStrategy:m}=(0,$.l)({filterOutFactor:u,supportedFirstFactors:l}),[f,g]=i.useState(()=>!u||!(0,q.xT)(u)),I=Q(),[S,C]=i.useState(!1),[v,y]=i.useState(!1);if(i.useEffect(()=>{("needs_identifier"===e.status||null===e.status)&&n.navigate("../")},[]),!u&&e.status)return(0,o.tZ)(O._,{cardTitle:(0,X.u1)("signIn.noAvailableMethods.title"),cardSubtitle:(0,X.u1)("signIn.noAvailableMethods.subtitle"),message:(0,X.u1)("signIn.noAvailableMethods.message")});let B=m?()=>g(e=>!e):void 0,w=()=>C(e=>!e),b=()=>{d.current=eB(u)},Z=e=>{h(t=>({currentFactor:e,prevCurrentFactor:t.currentFactor}))};if(f||S){let e=(0,q.xT)(u),t=f?B:w;return(0,o.tZ)(et,{mode:S?v?"pwned":"forgot":"default",onBackLinkClick:e?()=>{s.setError(void 0),y(!1),t?.()}:void 0,onFactorSelected:e=>{Z(e),t?.()},currentFactor:u})}if(!u)return(0,o.tZ)(D.W,{});switch(u?.strategy){case"passkey":return(0,o.tZ)(eC,{onFactorPrepare:b,onShowAlternativeMethodsClick:B});case"password":return(0,o.tZ)(ey,{onForgotPasswordMethodClick:I?w:B,onShowAlternativeMethodsClick:B,onPasswordPwned:()=>{y(!0),w()}});case"email_code":return(0,o.tZ)(ed,{factorAlreadyPrepared:d.current===eB(u),onFactorPrepare:b,factor:u,onShowAlternativeMethodsClicked:B});case"phone_code":if(u.channel&&"sms"!==u.channel)return(0,o.tZ)(el,{factorAlreadyPrepared:d.current===eB(u),onFactorPrepare:b,factor:u,onChangePhoneCodeChannel:Z});return(0,o.tZ)(e_,{factorAlreadyPrepared:d.current===eB(u),onFactorPrepare:b,factor:u,onShowAlternativeMethodsClicked:B});case"email_link":return(0,o.tZ)(em,{factorAlreadyPrepared:d.current===eB(u),onFactorPrepare:b,factor:u,onShowAlternativeMethodsClicked:B});case"reset_password_phone_code":return(0,o.tZ)(ef,{factorAlreadyPrepared:d.current===eB(u),onFactorPrepare:b,factor:u,onShowAlternativeMethodsClicked:B,onBackLinkClicked:()=>{h(e=>({currentFactor:e.prevCurrentFactor,prevCurrentFactor:e.currentFactor})),w()},cardSubtitle:(0,X.u1)("signIn.forgotPassword.subtitle_phone")});case"reset_password_email_code":return(0,o.tZ)(ef,{factorAlreadyPrepared:d.current===eB(u),onFactorPrepare:b,factor:u,onShowAlternativeMethodsClicked:B,onBackLinkClicked:()=>{h(e=>({currentFactor:e.prevCurrentFactor,prevCurrentFactor:e.currentFactor})),w()},cardSubtitle:(0,X.u1)("signIn.forgotPassword.subtitle_email")});default:return(0,o.tZ)(D.W,{})}})),eb=e=>{let[t,r]=i.useState(!1),n=i.useCallback(()=>r(e=>!e),[r]);return t?(0,o.tZ)(J.v,{onBackLinkClick:n}):(0,o.tZ)(eZ,{onBackLinkClick:e.onBackLinkClick,onFactorSelected:e.onFactorSelected,onHavingTroubleClick:n})},eZ=e=>{let{onHavingTroubleClick:t,onFactorSelected:r,onBackLinkClick:n}=e,i=(0,_.useCardState)(),{supportedSecondFactors:s}=(0,a.useCoreSignIn)();return(0,o.tZ)(l.Flow.Part,{part:"alternativeMethods",children:(0,o.BX)(y.Z.Root,{children:[(0,o.BX)(y.Z.Content,{children:[(0,o.BX)(w.h.Root,{showLogo:!0,children:[(0,o.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.alternativeMethods.title")}),(0,o.tZ)(w.h.Subtitle,{localizationKey:(0,l.localizationKeys)("signIn.alternativeMethods.subtitle")})]}),(0,o.tZ)(y.Z.Alert,{children:i.error}),(0,o.BX)(l.Col,{elementDescriptor:l.descriptors.main,gap:3,children:[(0,o.tZ)(l.Col,{gap:2,children:s&&s.sort(A.Q0).map((e,t)=>(0,o.tZ)(W.$,{textLocalizationKey:function(e){switch(e.strategy){case"phone_code":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__phoneCode",{identifier:(0,A.HT)(e.safeIdentifier)||""});case"totp":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__totp");case"backup_code":return(0,l.localizationKeys)("signIn.alternativeMethods.blockButton__backupCode");default:throw Error(`Invalid sign in strategy: "${e.strategy}"`)}}(e),elementDescriptor:l.descriptors.alternativeMethodsBlockButton,textElementDescriptor:l.descriptors.alternativeMethodsBlockButtonText,arrowElementDescriptor:l.descriptors.alternativeMethodsBlockButtonArrow,isDisabled:i.isLoading,onClick:()=>r(e)},t))}),(0,o.tZ)(y.Z.Action,{elementId:"alternativeMethods",children:n&&(0,o.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)("backButton"),onClick:e.onBackLinkClick})})]})]}),(0,o.tZ)(y.Z.Footer,{children:(0,o.BX)(y.Z.Action,{elementId:"havingTrouble",children:[(0,o.tZ)(y.Z.ActionText,{localizationKey:(0,l.localizationKeys)("signIn.alternativeMethods.actionText")}),(0,o.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)("signIn.alternativeMethods.actionLink"),onClick:t})]})})]})})},eA=e=>{let{onShowAlternativeMethodsClicked:t}=e,r=(0,a.useCoreSignIn)(),{afterSignInUrl:i}=(0,a.useSignInContext)(),{setActive:s}=(0,n.cL)(),{navigate:c}=(0,p.useRouter)(),d=(0,Z.H)(),u=(0,_.useCardState)(),h=(0,A.Yp)("code","",{type:"text",label:(0,l.localizationKeys)("formFieldLabel__backupCode"),isRequired:!0}),m=(0,n.cL)(),f=e=>(0,q.Vh)(e.firstFactorVerification?.strategy)&&e.firstFactorVerification?.status==="verified";return(0,o.BX)(y.Z.Root,{children:[(0,o.BX)(y.Z.Content,{children:[(0,o.BX)(w.h.Root,{showLogo:!0,children:[(0,o.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.backupCodeMfa.title")}),(0,o.tZ)(w.h.Subtitle,{localizationKey:f(r)?(0,l.localizationKeys)("signIn.forgotPassword.subtitle"):(0,l.localizationKeys)("signIn.backupCodeMfa.subtitle")})]}),(0,o.tZ)(y.Z.Alert,{children:u.error}),(0,o.tZ)(l.Col,{elementDescriptor:l.descriptors.main,gap:8,children:(0,o.BX)(B.l.Root,{onSubmit:e=>(e.preventDefault(),r.attemptSecondFactor({strategy:"backup_code",code:h.value}).then(e=>{if("complete"===e.status){if(f(e)&&e.createdSessionId){let t=new URLSearchParams;return t.set("createdSessionId",e.createdSessionId),c(`../reset-password-success?${t.toString()}`)}return s({session:e.createdSessionId,redirectUrl:i})}return console.error((0,b.Ws)(e.status,d))}).catch(e=>{if((0,ei.ay)(e))return m.__internal_navigateWithError("..",e.errors[0]);(0,A.S3)(e,[h],u.setError)})),children:[(0,o.tZ)(B.l.ControlRow,{elementId:h.id,children:(0,o.tZ)(B.l.PlainInput,{...h.props,autoFocus:!0,onActionClicked:t})}),(0,o.BX)(l.Col,{gap:3,children:[(0,o.tZ)(B.l.SubmitButton,{hasArrow:!0}),(0,o.tZ)(y.Z.Action,{elementId:"alternativeMethods",children:t&&(0,o.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)("footerActionLink__useAnotherMethod"),onClick:t})})]})]})})]}),(0,o.tZ)(y.Z.Footer,{})]})},ek=e=>{let t=(0,a.useCoreSignIn)(),r=(0,_.useCardState)(),{afterSignInUrl:s}=(0,a.useSignInContext)(),{setActive:c}=(0,n.cL)(),{navigate:d}=(0,p.useRouter)(),u=(0,Z.H)(),h=(0,n.cL)();i.useEffect(()=>{!e.factorAlreadyPrepared&&m?.()},[]);let m=e.prepare?()=>e.prepare?.().then(()=>e.onFactorPrepare()).catch(e=>{if(ei.ay(e))return h.__internal_navigateWithError("..",e.errors[0]);A.S3(e,[],r.setError)}):void 0,f=e=>(0,q.Vh)(e.firstFactorVerification?.strategy)&&e.firstFactorVerification?.status==="verified";return(0,o.tZ)(es.U,{cardTitle:e.cardTitle,cardSubtitle:f(t)?(0,l.localizationKeys)("signIn.forgotPassword.subtitle"):e.cardSubtitle,resendButton:e.resendButton,inputLabel:e.inputLabel,onCodeEntryFinishedAction:(r,o,n)=>{t.attemptSecondFactor({strategy:e.factor.strategy,code:r}).then(async e=>{if(await o(),"complete"===e.status){if(f(e)&&e.createdSessionId){let t=new URLSearchParams;return t.set("createdSessionId",e.createdSessionId),d(`../reset-password-success?${t.toString()}`)}return c({session:e.createdSessionId,redirectUrl:s})}return console.error((0,b.Ws)(e.status,u))}).catch(e=>(0,ei.ay)(e)?h.__internal_navigateWithError("..",e.errors[0]):n(e))},onResendCodeClicked:m,safeIdentifier:"safeIdentifier"in e.factor?e.factor.safeIdentifier:void 0,profileImageUrl:t.userData.imageUrl,onShowAlternativeMethodsClicked:e.onShowAlternativeMethodsClicked,children:f(t)&&(0,o.tZ)(l.Text,{localizationKey:(0,l.localizationKeys)("signIn.resetPasswordMfa.detailsLabel"),colorScheme:"secondary"})})},eP=e=>{let t=(0,a.useCoreSignIn)();return(0,o.tZ)(l.Flow.Part,{part:"phoneCode2Fa",children:(0,o.tZ)(ek,{...e,cardTitle:(0,l.localizationKeys)("signIn.phoneCodeMfa.title"),cardSubtitle:(0,l.localizationKeys)("signIn.phoneCodeMfa.subtitle"),inputLabel:(0,l.localizationKeys)("signIn.phoneCodeMfa.formTitle"),resendButton:(0,l.localizationKeys)("signIn.phoneCodeMfa.resendButton"),prepare:()=>{let{phoneNumberId:r,strategy:o}=e.factor;return t.prepareSecondFactor({phoneNumberId:r,strategy:o})}})})},eU=e=>(0,o.tZ)(l.Flow.Part,{part:"totp2Fa",children:(0,o.tZ)(ek,{...e,cardTitle:(0,l.localizationKeys)("signIn.totpMfa.title"),cardSubtitle:(0,l.localizationKeys)("signIn.totpMfa.subtitle"),inputLabel:(0,l.localizationKeys)("signIn.totpMfa.formTitle")})}),eE=e=>{if(!e)return"";let t=e.strategy;return"phoneNumberId"in e&&(t+=e.phoneNumberId),t},ez=(0,L.Hy)((0,_.withCardStateProvider)(function(){let e=(0,a.useCoreSignIn)().supportedSecondFactors,t=i.useRef(""),[r,n]=i.useState(()=>(0,q.bx)(e)),[s,l]=i.useState(!r),c=()=>l(e=>!e),d=()=>{t.current=eE(r)};if(!r)return(0,o.tZ)(D.W,{});if(s)return(0,o.tZ)(eb,{onBackLinkClick:c,onFactorSelected:e=>{n(e),c()}});switch(r?.strategy){case"phone_code":return(0,o.tZ)(eP,{factorAlreadyPrepared:t.current===eE(r),onFactorPrepare:d,factor:r,onShowAlternativeMethodsClicked:c});case"totp":return(0,o.tZ)(eU,{factorAlreadyPrepared:t.current===eE(r),onFactorPrepare:d,factor:r,onShowAlternativeMethodsClicked:c});case"backup_code":return(0,o.tZ)(eA,{onShowAlternativeMethodsClicked:c});default:return(0,o.tZ)(D.W,{})}})),eF=(0,L.Hy)(L.L);var eR=r(2208),eL=r(8350),eK=r(753),eT=r(6917),ex=r(7321);let eM=e=>{let{handleSubmit:t,phoneNumberFormState:r,onUseAnotherMethod:n,phoneCodeProvider:i}=e,{providerToDisplayData:s,strategyToDisplayData:a}=(0,c.vO)(),d=i.name,u=i.channel,p=(0,_.useCardState)();return(0,o.tZ)(y.Z.Root,{children:(0,o.BX)(y.Z.Content,{children:[(0,o.BX)(w.h.Root,{showLogo:!0,showDivider:!0,children:[(0,o.tZ)(l.Col,{center:!0,children:(0,o.tZ)(l.Image,{src:s[u]?.iconUrl,alt:`${a[u].name} logo`,sx:e=>({width:e.sizes.$7,height:e.sizes.$7,maxWidth:"100%",marginBottom:e.sizes.$6})})}),(0,o.tZ)(w.h.Title,{localizationKey:(0,l.localizationKeys)("signIn.start.alternativePhoneCodeProvider.title",{provider:d})}),(0,o.tZ)(w.h.Subtitle,{localizationKey:(0,l.localizationKeys)("signIn.start.alternativePhoneCodeProvider.subtitle",{provider:d})})]}),(0,o.tZ)(y.Z.Alert,{children:p.error}),(0,o.tZ)(l.Flex,{direction:"col",elementDescriptor:l.descriptors.main,gap:6,children:(0,o.BX)(B.l.Root,{onSubmit:t,gap:8,children:[(0,o.tZ)(l.Col,{gap:6,children:(0,o.tZ)(B.l.ControlRow,{elementId:"phoneNumber",children:(0,o.tZ)(B.l.PhoneInput,{...r.props,label:(0,l.localizationKeys)("signIn.start.alternativePhoneCodeProvider.label",{provider:d}),isRequired:!0,isOptional:!1,actionLabel:void 0,onActionClicked:void 0})})}),(0,o.BX)(l.Col,{center:!0,children:[(0,o.tZ)(ex.S,{}),(0,o.tZ)(l.Col,{gap:6,sx:{width:"100%"},children:(0,o.tZ)(B.l.SubmitButton,{hasArrow:!0,localizationKey:(0,l.localizationKeys)("formButtonPrimary")})})]}),(0,o.tZ)(l.Col,{center:!0,children:(0,o.tZ)(l.Button,{variant:"link",colorScheme:"neutral",onClick:n,localizationKey:(0,l.localizationKeys)("signIn.start.alternativePhoneCodeProvider.actionLink")})})]})})]})})},eO=()=>{let[e,t]=(0,i.useState)(!1),{navigate:r}=(0,p.useRouter)(),o=eS(()=>r("factor-two")),{userSettings:n}=(0,a.useEnvironment)(),{passkeySettings:s,attributes:l}=n;return(0,i.useEffect)(()=>{async function e(){let e=await (0,eR.h_)();t(e),e&&await o({flow:"autofill"})}s.allow_autofill&&l.passkey?.enabled&&e()},[]),{isWebAuthnAutofillSupported:e}},eD=e=>!!e.supportedFirstFactors?.length&&e.supportedFirstFactors.every(e=>"enterprise_sso"===e.strategy),e$=({field:e})=>{let[t,r]=(0,i.useState)(!1),n=(0,i.useRef)(null),s=!!(t||e?.value);return((0,i.useLayoutEffect)(()=>{let e=setInterval(()=>{if(n?.current){let t="onAutoFillStart"===window.getComputedStyle(n.current,":autofill").animationName||!!n.current?.matches("*:-webkit-autofill");t&&(r(t),clearInterval(e))}},500);return()=>{clearInterval(e)}},[]),(0,i.useEffect)(()=>{e?.value&&""!==e.value&&r(!1)},[e?.value]),e)?(0,o.tZ)(B.l.ControlRow,{elementId:e.id,sx:s?void 0:{position:"absolute",opacity:0,height:0,pointerEvents:"none",marginTop:"-1rem"},children:(0,o.tZ)(B.l.PasswordInput,{...e.props,ref:n,tabIndex:s?void 0:-1})}):null},eX=(0,L.Hy)((0,_.withCardStateProvider)(function(){let e=(0,_.useCardState)(),t=(0,n.cL)(),r=(0,c._m)(),{displayConfig:s,userSettings:d,authConfig:u}=(0,a.useEnvironment)(),h=(0,a.useCoreSignIn)(),{navigate:m}=(0,p.useRouter)(),f=(0,a.useSignInContext)(),{afterSignInUrl:g,signUpUrl:I,waitlistUrl:S,isCombinedFlow:C}=f,k=(0,Z.H)(),P=(0,i.useMemo)(()=>(0,L.vX)(d.enabledFirstFactorIdentifiers),[d.enabledFirstFactorIdentifiers]),U=d.alternativePhoneCodeChannels,{isWebAuthnAutofillSupported:E}=eO(),z=eS(()=>m("factor-two")),F=(0,eR.iW)(),R=!!f.initialValues?.phoneNumber&&!(f.initialValues.emailAddress||f.initialValues.username)&&P.includes("phone_number"),[K,T]=(0,i.useState)(R?"phone_number":P[0]||""),[x,M]=(0,i.useState)(!1),O=(0,eT.XV)("__clerk_ticket")||"",$=(0,eT.XV)("__clerk_status")||"",X=d.enabledFirstFactorIdentifiers,W=d.web3FirstFactors,N=d.authenticatableSocialStrategies,V=d.instanceIsPasswordBased,{currentIdentifier:H,nextIdentifier:G}=(0,L.vO)(P,K),j=(0,A.Yp)("password","",{type:"password",label:(0,l.localizationKeys)("formFieldLabel__password"),placeholder:(0,l.localizationKeys)("formFieldInputPlaceholder__password")}),[Q,J]=(0,i.useState)(null),ee=d.alternativePhoneCodeChannels.length>0,et=f.initialValues||{},er=(0,i.useMemo)(()=>({email_address:et.emailAddress,email_address_username:et.emailAddress||et.username,username:et.username,phone_number:et.phoneNumber}),[f.initialValues]),eo=!!N.length||!!W.length||!!U.length,[ei,es]=(0,i.useState)(!(0,A.s2)()&&!eo),ea=(0,A.Yp)("identifier",er[K]||"",{...H,isRequired:!0,transformer:e=>e.trim()}),el=(0,A.Yp)("identifier",er.phone_number||"",{...H,isRequired:!0}),ec="phone_number"===K?el:ea,ed=e=>{ea.setValue(er[K]||""),el.setValue(e),T("phone_number"),es(!0)};(0,i.useLayoutEffect)(()=>{ec.value.startsWith("+")&&P.includes("phone_number")&&"phone_number"!==K&&!x&&(ed(ec.value),M(!0))},[ec.value,P]),(0,i.useEffect)(()=>{if(O){if("sign_up"===$){let e=new URLSearchParams;O&&e.set("__clerk_ticket",O),m(C?"create":I,{searchParams:e});return}r.setLoading(),e.setLoading(),h.create({strategy:"ticket",ticket:O}).then(e=>{switch(e.status){case"needs_first_factor":if(eD(e))return em();return m("factor-one");case"needs_second_factor":return m("factor-two");case"complete":return(0,eT.xy)("__clerk_ticket"),t.setActive({session:e.createdSessionId,redirectUrl:g});default:console.error((0,b.Ws)(e.status,k));return}}).catch(e=>ef(e)).finally(()=>{eD(h)||(r.setIdle(),e.setIdle())})}},[]),(0,i.useEffect)(()=>{(async function(){let t=h?.firstFactorVerification?.error;if(t){switch(t.code){case eK.O1.NOT_ALLOWED_TO_SIGN_UP:case eK.O1.OAUTH_ACCESS_DENIED:case eK.O1.NOT_ALLOWED_ACCESS:case eK.O1.SAML_USER_ATTRIBUTE_MISSING:case eK.O1.OAUTH_EMAIL_DOMAIN_RESERVED_BY_SAML:case eK.O1.USER_LOCKED:case eK.O1.EXTERNAL_ACCOUNT_NOT_FOUND:case eK.O1.SIGN_UP_MODE_RESTRICTED:case eK.O1.SIGN_UP_MODE_RESTRICTED_WAITLIST:case eK.O1.ENTERPRISE_SSO_USER_ATTRIBUTE_MISSING:case eK.O1.ENTERPRISE_SSO_EMAIL_ADDRESS_DOMAIN_MISMATCH:case eK.O1.ENTERPRISE_SSO_HOSTED_DOMAIN_MISMATCH:case eK.O1.SAML_EMAIL_ADDRESS_DOMAIN_MISMATCH:case eK.O1.ORGANIZATION_MEMBERSHIP_QUOTA_EXCEEDED_FOR_SSO:case eK.O1.CAPTCHA_INVALID:case eK.O1.FRAUD_DEVICE_BLOCKED:case eK.O1.FRAUD_ACTION_BLOCKED:case eK.O1.SIGNUP_RATE_LIMIT_EXCEEDED:e.setError(t);break;default:e.setError("Unable to complete action at this time. If the problem persists please contact support.")}await h.create({})}})()},[]);let eu=e=>{let t=e.some(e=>"password"===e.name&&!!e.value);return(!t||d.enterpriseSSO.enabled)&&(e=e.filter(e=>"password"!==e.name)),{...(0,A.ni)(e),...t&&!d.enterpriseSSO.enabled&&{strategy:"password"}}},ep=(e,t)=>e.then(e=>{if(!d.enterpriseSSO.enabled)return e;let r=t.find(e=>"password"===e.name)?.value;return!r||e.supportedFirstFactors?.some(e=>"saml"===e.strategy||"enterprise_sso"===e.strategy)?e:e.attemptFirstFactor({strategy:"password",password:r})}),eh=async(...e)=>{let r=Q?.channel||(0,q.Vs)(e,u.preferredChannels,"identifier");if(r){let t=()=>{};e.push({id:"strategy",value:"phone_code",clearFeedback:t,setValue:t,onChange:t,setError:t}),e.push({id:"channel",value:r,clearFeedback:t,setValue:t,onChange:t,setError:t})}try{let r=await ep(h.create(eu(e)),e);switch(r.status){case"needs_identifier":r.supportedFirstFactors?.some(e=>"saml"===e.strategy||"enterprise_sso"===e.strategy)&&await em();break;case"needs_first_factor":if(eD(r)){await em();break}return m("factor-one");case"needs_second_factor":return m("factor-two");case"complete":return t.setActive({session:r.createdSessionId,redirectUrl:g});default:console.error((0,b.Ws)(r.status,k));return}}catch(e){return ef(e)}},em=async()=>{let e=(0,L.wT)(f,s.signInUrl),t=f.afterSignInUrl||"/";return h.authenticateWithRedirect({strategy:"enterprise_sso",redirectUrl:e,redirectUrlComplete:t,oidcPrompt:f.oidcPrompt})},ef=async r=>{if(!r.errors)return;let o=r.errors.find(e=>e.code===eK.O1.INVALID_STRATEGY_FOR_USER||e.code===eK.O1.FORM_PASSWORD_INCORRECT||e.code===eK.O1.FORM_PASSWORD_PWNED),n=r.errors.find(e=>"identifier_already_signed_in"===e.code),i=r.errors.find(e=>e.code===eK.O1.INVITATION_ACCOUNT_NOT_EXISTS||e.code===eK.O1.FORM_IDENTIFIER_NOT_FOUND);if(o)await eh(ec);else if(n){let e=n.meta.sessionId;await t.setActive({session:e,redirectUrl:g})}else if(C&&i){let r=(0,q.s2)(ec);if(d.signUp.mode===eK.ci.WAITLIST)return m(t.buildWaitlistUrl("emailAddress"===r?{initialValues:{[r]:ec.value}}:{}));t.client.signUp[r]=ec.value;let o=(0,L.wT)(f,s.signUpUrl),n=f.afterSignUpUrl||"/";return function({identifierAttribute:e,identifierValue:t,signUpMode:r,navigate:o,organizationTicket:n,afterSignUpUrl:i,clerk:s,handleError:a,redirectUrl:l,redirectUrlComplete:c,passwordEnabled:d,alternativePhoneCodeChannel:u}){var p,h;if(r===eK.ci.WAITLIST)return o(s.buildWaitlistUrl("emailAddress"===e?{initialValues:{[e]:t}}:{}));s.client.signUp[e]=t;let m=new URLSearchParams;return(n&&m.set("__clerk_ticket",n),d||(p=s.client.signUp,h=e,p.optionalFields.filter(e=>!(e.startsWith("oauth_")||e.startsWith("web3_")||["enterprise_sso","saml"].includes(e))&&"password"!==e&&("phoneNumber"!==h||"phone_number"!==e)).length>0)||"emailAddress"!==e&&"phoneNumber"!==e)?o("create",{searchParams:m}):s.client.signUp.create({[e]:t,...u?{strategy:"phone_code",channel:u}:{}}).then(async e=>(await v())({signUp:e,verifyEmailPath:"create/verify-email-address",verifyPhonePath:"create/verify-phone-number",handleComplete:()=>s.setActive({session:e.createdSessionId,redirectUrl:i}),navigate:o,redirectUrl:l,redirectUrlComplete:c})).catch(e=>a(e))}({afterSignUpUrl:f.afterSignUpUrl||"/",clerk:t,handleError:t=>(0,A.S3)(t,[ec,j],e.setError),identifierAttribute:r,identifierValue:ec.value,navigate:m,organizationTicket:O,signUpMode:d.signUp.mode,redirectUrl:o,redirectUrlComplete:n,passwordEnabled:d.attributes.password?.required??!1,alternativePhoneCodeChannel:Q?.channel||(0,q.mQ)(u.preferredChannels,r,ec.value)})}else(0,A.S3)(r,[ec,j],e.setError)},eg=async e=>(e.preventDefault(),eh(ec,j)),eI=(0,i.useMemo)(()=>({tel:B.l.PhoneInput,password:B.l.PasswordInput,text:B.l.PlainInput,email:B.l.PlainInput})[ec.type],[ec.type]);if(r.isLoading||"sign_up"===$)return(0,o.tZ)(D.W,{});let{action:eC,...ev}=ec.props;return(0,o.tZ)(l.Flow.Part,{part:"start",children:Q?(0,o.tZ)(eM,{handleSubmit:eg,phoneNumberFormState:el,onUseAnotherMethod:()=>{J(null)},phoneCodeProvider:Q}):(0,o.BX)(y.Z.Root,{children:[(0,o.BX)(y.Z.Content,{children:[(0,o.BX)(w.h.Root,{showLogo:!0,children:[(0,o.tZ)(w.h.Title,{localizationKey:C?(0,l.localizationKeys)("signIn.start.titleCombined"):(0,l.localizationKeys)("signIn.start.title")}),(0,o.tZ)(w.h.Subtitle,{localizationKey:C?(0,l.localizationKeys)("signIn.start.subtitleCombined"):(0,l.localizationKeys)("signIn.start.subtitle"),sx:{"&:empty":{display:"none"}}})]}),(0,o.tZ)(y.Z.Alert,{children:e.error}),(0,o.BX)(l.Col,{elementDescriptor:l.descriptors.main,gap:6,children:[(0,o.BX)(eL.G,{children:[eo&&(0,o.tZ)(Y,{enableWeb3Providers:!0,enableOAuthProviders:!0,enableAlternativePhoneCodeProviders:ee,onAlternativePhoneCodeProviderClick:e=>{J((0,en.H)(e)||null)}}),X.length?(0,o.BX)(B.l.Root,{onSubmit:eg,gap:8,children:[(0,o.BX)(l.Col,{gap:6,children:[(0,o.tZ)(B.l.ControlRow,{elementId:ec.id,children:(0,o.tZ)(eI,{actionLabel:G?.action,onActionClicked:()=>{T(e=>P[(P.indexOf(e)+1)%P.length]),es(!0),M(!1)},...ev,autoFocus:ei,autoComplete:E?"webauthn":void 0})}),(0,o.tZ)(e$,{field:V?j:void 0})]}),(0,o.BX)(l.Col,{center:!0,children:[(0,o.tZ)(ex.S,{}),(0,o.tZ)(B.l.SubmitButton,{hasArrow:!0})]})]}):null]}),!X.length&&(0,o.tZ)(ex.S,{}),d.attributes.passkey?.enabled&&d.passkeySettings.show_sign_in_button&&F&&(0,o.tZ)(y.Z.Action,{elementId:"usePasskey",children:(0,o.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)("signIn.start.actionLink__use_passkey"),onClick:()=>z({flow:"discoverable"})})})]})]}),(0,o.BX)(y.Z.Footer,{children:[d.signUp.mode===eK.ci.PUBLIC&&!C&&(0,o.BX)(y.Z.Action,{elementId:"signIn",children:[(0,o.tZ)(y.Z.ActionText,{localizationKey:(0,l.localizationKeys)("signIn.start.actionText")}),(0,o.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)("signIn.start.actionLink"),to:t.buildUrlWithAuth(I)})]}),d.signUp.mode===eK.ci.WAITLIST&&(0,o.BX)(y.Z.Action,{elementId:"signIn",children:[(0,o.tZ)(y.Z.ActionText,{localizationKey:(0,l.localizationKeys)("signIn.start.actionText__join_waitlist")}),(0,o.tZ)(y.Z.ActionLink,{localizationKey:(0,l.localizationKeys)("signIn.start.actionLink__join_waitlist"),to:t.buildUrlWithAuth(S)})]})]})]})})}));function eW(){let e=(0,n.cL)();return i.useEffect(()=>{e.redirectToSignIn()},[]),null}function eN(){let e=(0,a.useSignInContext)(),t=(0,a.useSignUpContext)();return(0,o.tZ)(l.Flow.Root,{flow:"signIn",children:(0,o.BX)(p.Switch,{children:[(0,o.tZ)(p.Route,{path:"factor-one",children:(0,o.tZ)(ew,{})}),(0,o.tZ)(p.Route,{path:"factor-two",children:(0,o.tZ)(ez,{})}),(0,o.tZ)(p.Route,{path:"reset-password",children:(0,o.tZ)(k,{})}),(0,o.tZ)(p.Route,{path:"reset-password-success",children:(0,o.tZ)(E,{})}),(0,o.tZ)(p.Route,{path:"sso-callback",children:(0,o.tZ)(eF,{signUpUrl:e.signUpUrl,signInUrl:e.signInUrl,signInForceRedirectUrl:e.afterSignInUrl,signUpForceRedirectUrl:e.afterSignUpUrl,continueSignUpUrl:e.signUpContinueUrl,transferable:e.transferable,firstFactorUrl:"../factor-one",secondFactorUrl:"../factor-two",resetPasswordUrl:"../reset-password"})}),(0,o.tZ)(p.Route,{path:"choose",children:(0,o.tZ)(M,{})}),(0,o.tZ)(p.Route,{path:"verify",children:(0,o.tZ)(s.J,{redirectUrlComplete:e.afterSignInUrl,redirectUrl:"../factor-two"})}),e.isCombinedFlow&&(0,o.BX)(p.Route,{path:"create",children:[(0,o.tZ)(p.Route,{path:"verify-email-address",canActivate:e=>!!e.client.signUp.emailAddress,children:(0,o.tZ)(g,{})}),(0,o.tZ)(p.Route,{path:"verify-phone-number",canActivate:e=>!!e.client.signUp.phoneNumber,children:(0,o.tZ)(f,{})}),(0,o.tZ)(p.Route,{path:"sso-callback",children:(0,o.tZ)(S,{signUpUrl:t.signUpUrl,signInUrl:t.signInUrl,signUpForceRedirectUrl:t.afterSignUpUrl,signInForceRedirectUrl:t.afterSignInUrl,secondFactorUrl:t.secondFactorUrl,continueSignUpUrl:"../continue",verifyEmailAddressUrl:"../verify-email-address",verifyPhoneNumberUrl:"../verify-phone-number"})}),(0,o.tZ)(p.Route,{path:"verify",children:(0,o.tZ)(s.$,{redirectUrlComplete:t.afterSignUpUrl,verifyEmailPath:"../verify-email-address",verifyPhonePath:"../verify-phone-number",continuePath:"../continue"})}),(0,o.BX)(p.Route,{path:"continue",children:[(0,o.tZ)(p.Route,{path:"verify-email-address",canActivate:e=>!!e.client.signUp.emailAddress,children:(0,o.tZ)(g,{})}),(0,o.tZ)(p.Route,{path:"verify-phone-number",canActivate:e=>!!e.client.signUp.phoneNumber,children:(0,o.tZ)(f,{})}),(0,o.tZ)(p.Route,{path:"tasks",children:(0,o.tZ)(u.x7,{})}),(0,o.tZ)(p.Route,{index:!0,children:(0,o.tZ)(C,{})})]}),(0,o.tZ)(p.Route,{index:!0,children:(0,o.tZ)(I,{})})]}),(0,o.tZ)(p.Route,{path:"tasks",children:(0,o.tZ)(u.x7,{})}),(0,o.tZ)(p.Route,{index:!0,children:(0,o.tZ)(eX,{})}),(0,o.tZ)(p.Route,{children:(0,o.tZ)(eW,{})})]})})}let eV=(e=!1)=>(0,c.ib)(e?m:void 0,"preloadComponent",{staleTime:1/0});eN.displayName="SignIn";let eH=(0,a.withCoreSessionSwitchGuard)(function(){let{__internal_setComponentNavigationContext:e}=(0,n.cL)(),{navigate:t,indexPath:r}=(0,p.useRouter)(),s=(0,a.useSignInContext)(),l={componentName:"SignUp",emailLinkRedirectUrl:s.emailLinkRedirectUrl,ssoCallbackUrl:s.ssoCallbackUrl,forceRedirectUrl:s.signUpForceRedirectUrl,fallbackRedirectUrl:s.signUpFallbackRedirectUrl,signInUrl:s.signInUrl,unsafeMetadata:s.unsafeMetadata,...(0,h.L)({routing:s?.routing,path:s?.path})};return eV(s.isCombinedFlow),(0,d.z)(),i.useEffect(()=>e?.({indexPath:r,navigate:t}),[r,t]),(0,o.tZ)(a.SignUpContext.Provider,{value:l,children:(0,o.tZ)(eN,{})})}),eG=e=>{let t={signUpUrl:`/${p.VIRTUAL_ROUTER_BASE_PATH}/sign-up`,waitlistUrl:`/${p.VIRTUAL_ROUTER_BASE_PATH}/waitlist`,...e};return(0,o.tZ)(p.Route,{path:"sign-in",children:(0,o.tZ)(a.SignInContext.Provider,{value:{componentName:"SignIn",...t,routing:"virtual",mode:"modal"},children:(0,o.tZ)("div",{children:(0,o.tZ)(eH,{...t,routing:"virtual"})})})})}},5518:function(e,t,r){r.d(t,{Vh:()=>m,Vs:()=>I,bx:()=>p,mQ:()=>S,s2:()=>g,t3:()=>c,xT:()=>u}),r(5027);var o=r(2208),n=r(7772),i=r(7623),s=r(577);let a=e=>t=>"safeIdentifier"in t&&t.safeIdentifier===e;function l(e){if((0,o.iW)()){let t=e.find(({strategy:e})=>"passkey"===e);if(t)return t}return null}function c(e,t,r){return e&&0!==e.length?r===n.kJ.Password?function(e,t){let r=l(e);if(r)return r;let o=e.sort(s.sZ)[0];return"password"===o.strategy?o:e.find(a(t))||o||null}(e,t):function(e,t){let r=l(e);if(r)return r;let o=e.sort(s.b8),n=o.find(a(t));if(n)return n;let i=o[0];return"email_link"===i.strategy?i:e.find(a(t))||i||null}(e,t):null}let d=["passkey","email_code","password","phone_code","email_link"];function u(e){return!!e&&d.includes(e.strategy)}function p(e){if(!e||0===e.length)return null;let t=e.find(e=>"totp"===e.strategy);if(t)return t;let r=e.find(e=>"phone_code"===e.strategy);return r||e[0]}let h=["reset_password_phone_code","reset_password_email_code"],m=e=>!!e&&h.includes(e),f=e=>/^\S+@\S+\.\S+$/.test(e);function g(e){return"tel"===e.type?"phoneNumber":f(e.value)?"emailAddress":"username"}let I=(e,t,r)=>{if(!t)return null;let o=e.find(e=>"strategy"===e.id)?.value;if(o&&"phone_code"!==o)return null;let n=e.find(e=>e.id===r)?.value;if(!n||!n?.startsWith("+"))return null;let s=(0,i.jR)(n,t);return"sms"===s?null:s},S=(e,t,r)=>{if(!e||!t||"phoneNumber"!==t||!r||!r?.startsWith("+"))return null;let o=(0,i.jR)(r,e);return"sms"===o?null:o}},9327:function(e,t,r){r.d(t,{fB:()=>p,gW:()=>m,uj:()=>h});var o=r(9109),n=r(2305),i=r(8774),s=r(7295),a=r(4676),l=r(1673),c=r(1576),d=r(9541),u=r(4174);let p=e=>{let{navigate:t}=(0,a.useRouter)(),{handleManageAccountClicked:r,handleSignOutSessionClicked:i,handleUserProfileActionClicked:s,session:u}=e,{menutItems:p}=(0,c.useUserButtonContext)(),h=e=>({borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100,padding:`${e.space.$4} ${e.space.$5}`}),m=async o=>o?.path?(await t(o.path),e?.completedCallback()):o.id===l.Zb.MANAGE_ACCOUNT?await r():o?.open?s(o.open):(o.onClick?.(),e?.completedCallback());return(0,o.tZ)(n.eX,{role:"menu",elementDescriptor:d.descriptors.userButtonPopoverActions,elementId:d.descriptors.userButtonPopoverActions.setId("singleSession"),sx:e=>({borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100}),children:p?.map(e=>{let t;return t=Object.values(l.Zb).includes(e.id)?{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId(e.id)}:{elementDescriptor:d.descriptors.userButtonPopoverCustomItemButton,elementId:d.descriptors.userButtonPopoverCustomItemButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverCustomItemButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverCustomItemButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionItemButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionItemButtonIcon.setId(e.id)},o.tZ(n.aU,{...t,icon:e.icon,label:e.name,onClick:e.id===l.Zb.SIGN_OUT?i(u):()=>m(e),sx:h,iconSx:e=>({width:e.sizes.$4,height:e.sizes.$4})},e.id)})})},h=e=>{let{navigate:t}=(0,a.useRouter)(),{handleManageAccountClicked:r,handleSignOutSessionClicked:p,handleSessionClicked:h,handleAddAccountClicked:m,handleUserProfileActionClicked:f,session:g,otherSessions:I}=e,{menutItems:S}=(0,c.useUserButtonContext)(),C=async o=>o?.path?(await t(o.path),e?.completedCallback()):o.id===l.Zb.MANAGE_ACCOUNT?await r():o?.open?f(o.open):(o.onClick?.(),e?.completedCallback()),v=S.every(e=>Object.values(l.Zb).includes(e.id));return(0,o.BX)(o.HY,{children:[v?(0,o.tZ)(n.r5,{role:"menu",elementDescriptor:d.descriptors.userButtonPopoverActions,elementId:d.descriptors.userButtonPopoverActions.setId("multiSession"),children:(0,o.BX)(d.Flex,{justify:"between",sx:e=>({marginLeft:e.space.$12,padding:`0 ${e.space.$5} ${e.space.$4}`,gap:e.space.$2}),children:[(0,o.tZ)(n.U8,{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId("manageAccount"),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId("manageAccount"),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId("manageAccount"),icon:u.tc,label:(0,d.localizationKeys)("userButton.action__manageAccount"),onClick:r}),(0,o.tZ)(n.U8,{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId("signOut"),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId("signOut"),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId("signOut"),icon:u.lv,label:(0,d.localizationKeys)("userButton.action__signOut"),onClick:p(g)})]})}):(0,o.tZ)(n.r5,{role:"menu",elementDescriptor:d.descriptors.userButtonPopoverActions,elementId:d.descriptors.userButtonPopoverActions.setId("multiSession"),sx:e=>({gap:e.space.$1,paddingBottom:e.space.$2}),children:S?.map(e=>{let t;return t=Object.values(l.Zb).includes(e.id)?{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId(e.id)}:{elementDescriptor:d.descriptors.userButtonPopoverCustomItemButton,elementId:d.descriptors.userButtonPopoverCustomItemButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverCustomItemButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverCustomItemButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionItemButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionItemButtonIcon.setId(e.id)},o.tZ(n.aU,{...t,icon:e.icon,label:e.name,onClick:e.id===l.Zb.SIGN_OUT?p(g):()=>C(e),sx:e=>({border:0,padding:`${e.space.$2} ${e.space.$5}`,gap:e.space.$3x5}),iconSx:e=>({width:e.sizes.$4,height:e.sizes.$4}),iconBoxSx:e=>({minHeight:e.sizes.$4,minWidth:e.sizes.$4,alignItems:"center"})},e.id)})}),(0,o.BX)(n.eX,{role:"menu",sx:e=>({borderTopStyle:e.borderStyles.$solid,borderTopWidth:e.borderWidths.$normal,borderTopColor:e.colors.$neutralAlpha100}),children:[I.map(e=>(0,o.tZ)(i.K,{icon:u.nR,onClick:h(e),role:"menuitem",children:(0,o.tZ)(s.E,{user:e.user})},e.id)),(0,o.tZ)(n.aU,{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId("addAccount"),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId("addAccount"),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId("addAccount"),icon:u.mm,label:(0,d.localizationKeys)("userButton.action__addAccount"),onClick:m,iconSx:e=>({width:e.sizes.$9,height:e.sizes.$6}),iconBoxSx:e=>({minHeight:e.sizes.$9,minWidth:e.sizes.$6,alignItems:"center"}),spinnerSize:"md"})]})]})},m=e=>{let{handleSignOutAllClicked:t,elementDescriptor:r,elementId:i,iconBoxElementDescriptor:s,iconBoxElementId:a,iconElementDescriptor:l,iconElementId:c,label:p,sx:h,actionSx:m}=e;return(0,o.tZ)(n.eX,{role:"menu",sx:[e=>({padding:e.space.$2}),h],children:(0,o.tZ)(n.aU,{elementDescriptor:r||d.descriptors.userButtonPopoverActionButton,elementId:i||d.descriptors.userButtonPopoverActionButton.setId("signOutAll"),iconBoxElementDescriptor:s||d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:a||d.descriptors.userButtonPopoverActionButtonIconBox.setId("signOutAll"),iconElementDescriptor:l||d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:c||d.descriptors.userButtonPopoverActionButtonIcon.setId("signOutAll"),icon:u.lv,label:p||(0,d.localizationKeys)("userButton.action__signOutAll"),onClick:t,variant:"ghost",colorScheme:"neutral",sx:[e=>({backgroundColor:e.colors.$transparent,padding:`${e.space.$2} ${e.space.$3}`,borderBottomWidth:0,borderRadius:e.radii.$lg}),m],spinnerSize:"md"})})}},5241:function(e,t,r){r.d(t,{Z:()=>c});var o=r(3799),n=r(2672),i=r(5809),s=r(4264),a=r(4676),l=r(7623);let c=e=>{let{setActive:t,signOut:r,openUserProfile:c}=(0,o.cL)(),d=(0,n.useCardState)(),{signedInSessions:u,otherSessions:p}=(0,s.j)({user:e.user}),{navigate:h}=(0,a.useRouter)();return{handleSignOutSessionClicked:t=>()=>0===p.length?r(e.navigateAfterSignOut):r(e.navigateAfterMultiSessionSingleSignOut,{sessionId:t.id}).finally(()=>d.setIdle()),handleManageAccountClicked:()=>"navigation"===e.userProfileMode?h(e.userProfileUrl||"").finally(()=>{(async()=>{await (0,l._v)(300),e.actionCompleteCallback?.()})()}):(c(e.userProfileProps),e.actionCompleteCallback?.()),handleUserProfileActionClicked:t=>"navigation"===e.userProfileMode?h(e.userProfileUrl||"").finally(()=>{(async()=>{await (0,l._v)(300),e.actionCompleteCallback?.()})()}):(c({...e.userProfileProps,...t&&{__experimental_startPath:t}}),e.actionCompleteCallback?.()),handleSignOutAllClicked:()=>r(e.navigateAfterSignOut),handleSessionClicked:r=>async()=>(d.setLoading(),t({session:r,redirectUrl:e.afterSwitchSessionUrl}).finally(()=>{d.setIdle(),e.actionCompleteCallback?.()})),handleAddAccountClicked:()=>((0,i.T7)(e.signInUrl||window.location.href),(0,l._v)(2e3)),otherSessions:p,signedInSessions:u}}}}]);