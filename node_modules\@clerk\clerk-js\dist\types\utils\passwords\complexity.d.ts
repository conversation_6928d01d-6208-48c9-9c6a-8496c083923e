import type { PasswordSettingsData } from '@clerk/types';
export type ComplexityErrors = {
    [key in keyof Partial<Omit<PasswordSettingsData, 'disable_hibp' | 'min_zxcvbn_strength' | 'show_zxcvbn'>>]?: boolean;
};
export type UsePasswordComplexityConfig = Omit<PasswordSettingsData, 'disable_hibp' | 'min_zxcvbn_strength' | 'show_zxcvbn'>;
export declare const validate: (password: string, config: UsePasswordComplexityConfig) => ComplexityErrors;
export declare const createValidateComplexity: (config: UsePasswordComplexityConfig) => (password: string) => ComplexityErrors;
