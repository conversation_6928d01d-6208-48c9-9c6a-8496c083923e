declare const dynamicSpaceUnits: Readonly<{
    readonly '0x25': "0.0625rem";
    readonly '0x5': "0.125rem";
    readonly '1': "0.25rem";
    readonly '1x5': "0.375rem";
    readonly '2': "0.5rem";
    readonly '2x5': "0.625rem";
    readonly '3': "0.75rem";
    readonly '3x25': "0.8125rem";
    readonly '3x5': "0.875rem";
    readonly '4': "1rem";
    readonly '4x25': "1.0625rem";
    readonly '5': "1.25rem";
    readonly '5x5': "1.375rem";
    readonly '6': "1.5rem";
    readonly '7': "1.75rem";
    readonly '7x5': "1.875rem";
    readonly '8': "2rem";
    readonly '8x5': "2.125rem";
    readonly '8x75': "2.1875rem";
    readonly '9': "2.25rem";
    readonly '10': "2.5rem";
    readonly '12': "3rem";
    readonly '13': "3.5rem";
    readonly '16': "4rem";
    readonly '17': "4.25rem";
    readonly '20': "5rem";
    readonly '24': "6rem";
    readonly '28': "7rem";
    readonly '32': "8rem";
    readonly '36': "9rem";
    readonly '40': "10rem";
    readonly '44': "11rem";
    readonly '48': "12rem";
    readonly '52': "13rem";
    readonly '56': "14rem";
    readonly '57': "14.25rem";
    readonly '60': "15rem";
    readonly '66': "16.5rem";
    readonly '94': "23.5rem";
    readonly '100': "25rem";
    readonly '108': "27rem";
    readonly '120': "30rem";
    readonly '140': "35rem";
    readonly '160': "40rem";
    readonly '176': "44rem";
    readonly '220': "55rem";
}>;
/**
 * Instead of generating these values with the helpers of parseVariables,
 * we hard code them in order to have better intellisense support while developing
 */
declare const space: Readonly<{
    readonly '0x25': "0.0625rem";
    readonly '0x5': "0.125rem";
    readonly '1': "0.25rem";
    readonly '1x5': "0.375rem";
    readonly '2': "0.5rem";
    readonly '2x5': "0.625rem";
    readonly '3': "0.75rem";
    readonly '3x25': "0.8125rem";
    readonly '3x5': "0.875rem";
    readonly '4': "1rem";
    readonly '4x25': "1.0625rem";
    readonly '5': "1.25rem";
    readonly '5x5': "1.375rem";
    readonly '6': "1.5rem";
    readonly '7': "1.75rem";
    readonly '7x5': "1.875rem";
    readonly '8': "2rem";
    readonly '8x5': "2.125rem";
    readonly '8x75': "2.1875rem";
    readonly '9': "2.25rem";
    readonly '10': "2.5rem";
    readonly '12': "3rem";
    readonly '13': "3.5rem";
    readonly '16': "4rem";
    readonly '17': "4.25rem";
    readonly '20': "5rem";
    readonly '24': "6rem";
    readonly '28': "7rem";
    readonly '32': "8rem";
    readonly '36': "9rem";
    readonly '40': "10rem";
    readonly '44': "11rem";
    readonly '48': "12rem";
    readonly '52': "13rem";
    readonly '56': "14rem";
    readonly '57': "14.25rem";
    readonly '60': "15rem";
    readonly '66': "16.5rem";
    readonly '94': "23.5rem";
    readonly '100': "25rem";
    readonly '108': "27rem";
    readonly '120': "30rem";
    readonly '140': "35rem";
    readonly '160': "40rem";
    readonly '176': "44rem";
    readonly '220': "55rem";
    readonly none: "0";
    readonly xxs: "0.5px";
    readonly px: "1px";
}>;
declare const sizes: Readonly<{
    readonly '0x25': "0.0625rem";
    readonly '0x5': "0.125rem";
    readonly '1': "0.25rem";
    readonly '1x5': "0.375rem";
    readonly '2': "0.5rem";
    readonly '2x5': "0.625rem";
    readonly '3': "0.75rem";
    readonly '3x25': "0.8125rem";
    readonly '3x5': "0.875rem";
    readonly '4': "1rem";
    readonly '4x25': "1.0625rem";
    readonly '5': "1.25rem";
    readonly '5x5': "1.375rem";
    readonly '6': "1.5rem";
    readonly '7': "1.75rem";
    readonly '7x5': "1.875rem";
    readonly '8': "2rem";
    readonly '8x5': "2.125rem";
    readonly '8x75': "2.1875rem";
    readonly '9': "2.25rem";
    readonly '10': "2.5rem";
    readonly '12': "3rem";
    readonly '13': "3.5rem";
    readonly '16': "4rem";
    readonly '17': "4.25rem";
    readonly '20': "5rem";
    readonly '24': "6rem";
    readonly '28': "7rem";
    readonly '32': "8rem";
    readonly '36': "9rem";
    readonly '40': "10rem";
    readonly '44': "11rem";
    readonly '48': "12rem";
    readonly '52': "13rem";
    readonly '56': "14rem";
    readonly '57': "14.25rem";
    readonly '60': "15rem";
    readonly '66': "16.5rem";
    readonly '94': "23.5rem";
    readonly '100': "25rem";
    readonly '108': "27rem";
    readonly '120': "30rem";
    readonly '140': "35rem";
    readonly '160': "40rem";
    readonly '176': "44rem";
    readonly '220': "55rem";
    readonly none: "0";
    readonly xxs: "0.5px";
    readonly px: "1px";
}>;
declare const radii: Readonly<{
    readonly none: "0px";
    readonly circle: "50%";
    readonly avatar: "0.375rem";
    readonly sm: "0.25rem";
    readonly md: "0.375rem";
    readonly lg: "0.5rem";
    readonly xl: "0.75rem";
    readonly halfHeight: "99999px";
}>;
/**
 * Used by the space scale generation helpers.
 * These keys should always match {@link space}
 */
declare const spaceScaleKeys: Array<keyof typeof dynamicSpaceUnits>;
export { sizes, space, radii, spaceScaleKeys };
