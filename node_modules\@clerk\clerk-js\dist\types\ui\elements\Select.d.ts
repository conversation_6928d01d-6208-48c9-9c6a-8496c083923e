import type { SelectId } from '@clerk/types';
import type { PropsWithChildren, ReactElement, ReactNode } from 'react';
import React from 'react';
import { Button, Flex, Text } from '../customizables';
import type { PropsOfComponent, ThemableCssProp } from '../styledSystem';
type Option = {
    value: string | null;
    label?: string;
};
type RenderOption<O extends Option> = (option: O, index?: number, isSelected?: boolean) => ReactNode;
type SelectProps<O extends Option> = {
    options: O[];
    value: string | null;
    onChange: (option: O) => void;
    searchPlaceholder?: string;
    placeholder?: string;
    comparator?: (term: string, option: O) => boolean;
    noResultsMessage?: string;
    renderOption?: RenderOption<O>;
    elementId?: SelectId;
    portal?: boolean;
    referenceElement?: React.RefObject<HTMLElement>;
};
export declare const Select: <O extends Option>(props: PropsWithChildren<SelectProps<O>>) => ReactElement;
export declare const SelectNoResults: (props: PropsOfComponent<typeof Text>) => import("@emotion/react/jsx-runtime").JSX.Element;
type SelectOptionListProps = PropsOfComponent<typeof Flex> & {
    containerSx?: ThemableCssProp;
};
export declare const SelectOptionList: (props: SelectOptionListProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export declare const SelectButton: (props: PropsOfComponent<typeof Button> & {
    icon?: React.ReactElement | React.ComponentType;
    iconSx?: ThemableCssProp;
}) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
