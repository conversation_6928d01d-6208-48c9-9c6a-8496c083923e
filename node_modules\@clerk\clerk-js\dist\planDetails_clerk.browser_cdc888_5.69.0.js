"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["33"],{1991:function(e,t,a){a.r(t),a.d(t,{PlanDetails:()=>h});var n=a(9109),i=a(3799),o=a(9144),l=a(9117),r=a(9629),c=a(1102),s=a(4142),p=a(8969),d=a(1576),m=a(9541),u=a(7623);let h=e=>(0,n.tZ)(d.SubscriberTypeContext.Provider,{value:e.subscriberType||"user",children:(0,n.tZ)(c.Drawer.Content,{children:(0,n.tZ)(g,{...e})})}),g=({plan:e,onSubscriptionCancel:t,portalRoot:a,initialPlanPeriod:s="month"})=>{let h=(0,i.cL)(),{organization:g}=(0,i.o8)(),[D,b]=(0,o.useState)(!1),[x,S]=(0,o.useState)(!1),[B,C]=(0,o.useState)(),[Z,z]=(0,o.useState)(s),{setIsOpen:T}=(0,c.useDrawerContext)(),{activeOrUpcomingSubscriptionBasedOnPlanPeriod:v,revalidateAll:K,buttonPropsForPlan:k,isDefaultPlanImplicitlyActiveOrUpcoming:f}=(0,d.usePlansContext)(),w=(0,d.useSubscriberTypeContext)(),$=(0,p.N2)(e=>e({permission:"org:sys_billing:manage"})||"user"===w);if(!e)return null;let P=v(e,Z),F=()=>{T&&T(!1)},L=e.features,_=L.length>0,X=async()=>{P&&(C(void 0),S(!0),await P.cancel({orgId:"org"===w?g?.id:void 0}).then(()=>{S(!1),t?.(),F()}).catch(e=>{(0,u.S3)(e,[],C),S(!1)}))},A=t=>{F();let n=t?.planPeriod||Z;"annual"===n&&0===e.annualMonthlyAmount&&(n="month"),h.__internal_openCheckout({planId:e.id,planPeriod:n,subscriberType:w,onSubscriptionComplete:()=>{K()},portalRoot:a})};return(0,n.BX)(n.HY,{children:[(0,n.tZ)(c.Drawer.Header,{sx:e=>_?null:{flex:1,borderBottomWidth:0,background:e.colors.$colorBackground},children:(0,n.tZ)(y,{plan:e,subscription:P,planPeriod:Z,setPlanPeriod:z,closeSlot:(0,n.tZ)(c.Drawer.Close,{})})}),_?(0,n.BX)(c.Drawer.Body,{children:[(0,n.tZ)(m.Text,{elementDescriptor:m.descriptors.planDetailCaption,variant:"caption",localizationKey:(0,m.localizationKeys)("commerce.availableFeatures"),colorScheme:"secondary",sx:e=>({padding:e.space.$4,paddingBottom:0})}),(0,n.tZ)(m.Box,{elementDescriptor:m.descriptors.planDetailFeaturesList,as:"ul",role:"list",sx:e=>({display:"grid",rowGap:e.space.$6,padding:e.space.$4,margin:0}),children:L.map(e=>(0,n.BX)(m.Box,{elementDescriptor:m.descriptors.planDetailFeaturesListItem,as:"li",sx:e=>({display:"flex",alignItems:"baseline",gap:e.space.$3}),children:[e.avatarUrl?(0,n.tZ)(r.q,{size:e=>24,title:e.name,initials:e.name[0],rounded:!1,imageUrl:e.avatarUrl}):null,(0,n.BX)(m.Span,{elementDescriptor:m.descriptors.planDetailFeaturesListItemContent,children:[(0,n.tZ)(m.Text,{elementDescriptor:m.descriptors.planDetailFeaturesListItemTitle,colorScheme:"body",sx:e=>({fontWeight:e.fontWeights.$medium}),children:e.name}),e.description?(0,n.tZ)(m.Text,{elementDescriptor:m.descriptors.planDetailFeaturesListItemDescription,colorScheme:"secondary",sx:e=>({marginBlockStart:e.space.$0x25}),children:e.description}):null]})]},e.id))})]}):null,(e.isDefault||f)&&P?null:(0,n.tZ)(c.Drawer.Footer,{children:P?P.canceledAt?(0,n.tZ)(m.Button,{block:!0,textVariant:"buttonLarge",...k({plan:e}),onClick:()=>A()}):(0,n.BX)(m.Col,{gap:4,children:[P&&"month"===P.planPeriod&&e.annualMonthlyAmount>0&&"annual"===Z?(0,n.tZ)(m.Button,{block:!0,variant:"bordered",colorScheme:"secondary",textVariant:"buttonLarge",isDisabled:!$,onClick:()=>A({planPeriod:"annual"}),localizationKey:(0,m.localizationKeys)("commerce.switchToAnnual")}):null,P&&"annual"===P.planPeriod&&"month"===Z?(0,n.tZ)(m.Button,{block:!0,variant:"bordered",colorScheme:"secondary",textVariant:"buttonLarge",isDisabled:!$,onClick:()=>A({planPeriod:"month"}),localizationKey:(0,m.localizationKeys)("commerce.switchToMonthly")}):null,(0,n.tZ)(m.Button,{block:!0,variant:"bordered",colorScheme:"danger",textVariant:"buttonLarge",isDisabled:!$,onClick:()=>b(!0),localizationKey:(0,m.localizationKeys)("commerce.cancelSubscription")})]}):(0,n.tZ)(m.Button,{block:!0,textVariant:"buttonLarge",...k({plan:e}),onClick:()=>A()})}),P?(0,n.BX)(c.Drawer.Confirmation,{open:D,onOpenChange:b,actionsSlot:(0,n.BX)(n.HY,{children:[!x&&(0,n.tZ)(m.Button,{variant:"ghost",size:"sm",textVariant:"buttonLarge",isDisabled:!$,onClick:()=>{C(void 0),b(!1)},localizationKey:(0,m.localizationKeys)("commerce.keepSubscription")}),(0,n.tZ)(m.Button,{variant:"solid",colorScheme:"danger",size:"sm",textVariant:"buttonLarge",isLoading:x,isDisabled:!$,onClick:()=>{C(void 0),b(!1),X()},localizationKey:(0,m.localizationKeys)("commerce.cancelSubscription")})]}),children:[(0,n.tZ)(m.Heading,{elementDescriptor:m.descriptors.drawerConfirmationTitle,as:"h2",textVariant:"h3",localizationKey:(0,m.localizationKeys)("commerce.cancelSubscriptionTitle",{plan:`${"upcoming"===P.status?"upcoming ":""}${P.plan.name}`})}),(0,n.tZ)(m.Text,{elementDescriptor:m.descriptors.drawerConfirmationDescription,colorScheme:"secondary",localizationKey:"upcoming"===P.status?(0,m.localizationKeys)("commerce.cancelSubscriptionNoCharge"):(0,m.localizationKeys)("commerce.cancelSubscriptionAccessUntil",{plan:P.plan.name,date:P.periodEnd})}),B&&(0,n.tZ)(l.b,{colorScheme:"danger",children:"string"==typeof B?B:B.message})]}):null]})},y=o.forwardRef((e,t)=>{let{plan:a,subscription:i,closeSlot:l,planPeriod:c,setPlanPeriod:p}=e,{captionForSubscription:u,isDefaultPlanImplicitlyActiveOrUpcoming:h}=(0,d.usePlansContext)(),{data:g}=(0,d.useSubscriptions)(),y=h&&a.isDefault,D=!!i,b=(0,o.useMemo)(()=>a.annualMonthlyAmount<=0?a.amountFormatted:"annual"===c?a.annualMonthlyAmountFormatted:a.amountFormatted,[a,c]);return(0,n.BX)(m.Box,{ref:t,elementDescriptor:m.descriptors.planDetailHeader,sx:e=>({width:"100%",padding:e.space.$4,position:"relative"}),children:[l?(0,n.tZ)(m.Box,{sx:e=>({position:"absolute",top:e.space.$2,insetInlineEnd:e.space.$2}),children:l}):null,(0,n.BX)(m.Col,{gap:3,elementDescriptor:m.descriptors.planDetailBadgeAvatarTitleDescriptionContainer,children:[D?(0,n.BX)(m.Flex,{align:"center",gap:3,elementDescriptor:m.descriptors.planDetailBadgeContainer,sx:e=>({paddingInlineEnd:e.space.$10}),children:[i?.status==="active"||y&&0===g.length?(0,n.tZ)(m.Badge,{elementDescriptor:m.descriptors.planDetailBadge,localizationKey:(0,m.localizationKeys)("badge__activePlan"),colorScheme:"secondary"}):(0,n.tZ)(m.Badge,{elementDescriptor:m.descriptors.planDetailBadge,localizationKey:(0,m.localizationKeys)("badge__upcomingPlan"),colorScheme:"primary"}),!!i&&(0,n.tZ)(m.Text,{elementDescriptor:m.descriptors.planDetailCaption,variant:"caption",localizationKey:u(i),colorScheme:"secondary"})]}):null,a.avatarUrl?(0,n.tZ)(r.q,{boxElementDescriptor:m.descriptors.planDetailAvatar,size:e=>40,title:a.name,initials:a.name[0],rounded:!1,imageUrl:a.avatarUrl,sx:e=>({marginBlockEnd:e.space.$3})}):null,(0,n.BX)(m.Col,{gap:1,elementDescriptor:m.descriptors.planDetailTitleDescriptionContainer,children:[(0,n.tZ)(m.Heading,{elementDescriptor:m.descriptors.planDetailTitle,as:"h2",textVariant:"h2",children:a.name}),a.description?(0,n.tZ)(m.Text,{elementDescriptor:m.descriptors.planDetailDescription,variant:"subtitle",colorScheme:"secondary",children:a.description}):null]})]}),(0,n.tZ)(m.Flex,{elementDescriptor:m.descriptors.planDetailFeeContainer,align:"center",wrap:"wrap",sx:e=>({marginTop:e.space.$3,columnGap:e.space.$1x5}),children:(0,n.BX)(n.HY,{children:[(0,n.BX)(m.Text,{elementDescriptor:m.descriptors.planDetailFee,variant:"h1",colorScheme:"body",children:[a.currencySymbol,b]}),(0,n.tZ)(m.Text,{elementDescriptor:m.descriptors.planDetailFeePeriod,variant:"caption",colorScheme:"secondary",sx:e=>({textTransform:"lowercase",":before":{content:'"/"',marginInlineEnd:e.space.$1}}),localizationKey:(0,m.localizationKeys)("commerce.month")})]})}),a.annualMonthlyAmount>0?(0,n.tZ)(m.Box,{elementDescriptor:m.descriptors.planDetailPeriodToggle,sx:e=>({display:"flex",marginTop:e.space.$3}),children:(0,n.tZ)(s.r,{isChecked:"annual"===c,onChange:e=>p(e?"annual":"month"),label:(0,m.localizationKeys)("commerce.billedAnnually")})}):(0,n.tZ)(m.Text,{elementDescriptor:m.descriptors.pricingTableCardFeePeriodNotice,variant:"caption",colorScheme:"secondary",localizationKey:a.isDefault?(0,m.localizationKeys)("commerce.alwaysFree"):(0,m.localizationKeys)("commerce.billedMonthlyOnly"),sx:e=>({justifySelf:"flex-start",alignSelf:"center",marginTop:e.space.$3})})]})})}}]);