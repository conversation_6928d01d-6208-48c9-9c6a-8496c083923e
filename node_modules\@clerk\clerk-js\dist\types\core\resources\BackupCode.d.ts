import type { BackupCodeJSON, BackupCodeResource } from '@clerk/types';
import { BaseResource } from './internal';
export declare class BackupCode extends BaseResource implements BackupCodeResource {
    pathRoot: string;
    id: string;
    codes: string[];
    updatedAt: Date | null;
    createdAt: Date | null;
    constructor(data: BackupCodeJSON);
    protected fromJSON(data: BackupCodeJSON | null): this;
}
