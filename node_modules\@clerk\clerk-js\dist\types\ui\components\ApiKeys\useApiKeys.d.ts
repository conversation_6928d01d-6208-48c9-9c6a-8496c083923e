export declare const useApiKeys: ({ subject, perPage }: {
    subject: string;
    perPage?: number;
}) => {
    apiKeys: import("@clerk/types").APIKeyResource[];
    cacheKey: {
        key: string;
        subject: string;
    };
    mutate: import("swr").KeyedMutator<import("@clerk/types").APIKeyResource[]>;
    isLoading: boolean;
    search: string;
    setSearch: import("react").Dispatch<import("react").SetStateAction<string>>;
    page: number;
    setPage: import("react").Dispatch<import("react").SetStateAction<number>>;
    pageCount: number;
    itemCount: number;
    startingRow: number;
    endingRow: number;
};
