import type { CommerceCheckoutTotals, CommerceCheckoutTotalsJSON, CommerceMoney, CommerceMoneyJSON, CommerceStatementTotals, CommerceStatementTotalsJSON } from '@clerk/types';
export declare const commerceMoneyFromJSON: (data: CommerceMoneyJSON) => CommerceMoney;
export declare const commerceTotalsFromJSON: <T extends CommerceStatementTotalsJSON | CommerceCheckoutTotalsJSON>(data: T) => T extends {
    total_due_now: CommerceMoneyJSON;
} ? CommerceCheckoutTotals : CommerceStatementTotals;
