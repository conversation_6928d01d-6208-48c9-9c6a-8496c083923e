import type { UserOrganizationInvitationResource } from '@clerk/types';
import type { PropsWithChildren } from 'react';
import { Button } from '../../customizables';
import type { ThemableCssProp } from '../../styledSystem';
export declare const PreviewListItems: (props: PropsWithChildren) => import("@emotion/react/jsx-runtime").JSX.Element;
export declare const sharedMainIdentifierSx: ThemableCssProp;
export declare const PreviewListItem: (props: PropsWithChildren<{
    organizationData: UserOrganizationInvitationResource["publicOrganizationData"];
}>) => import("@emotion/react/jsx-runtime").JSX.Element;
export declare const PreviewListSpinner: import("react").ForwardRefExoticComponent<import("react").RefAttributes<HTMLDivElement>>;
export declare const PreviewListItemButton: (props: Parameters<typeof Button>[0]) => import("@emotion/react/jsx-runtime").JSX.Element;
export declare const OrganizationListPreviewButton: (props: PropsWithChildren<{
    onClick: () => void | Promise<void>;
}>) => import("@emotion/react/jsx-runtime").JSX.Element;
