import type { ProfileSectionId } from '@clerk/types';
import type { LocalizationKey } from '../customizables';
import { Col, Flex } from '../customizables';
import type { ElementDescriptor, ElementId } from '../customizables/elementDescriptors';
import type { PropsOfComponent, ThemableCssProp } from '../styledSystem';
import { ArrowBlockButton } from './ArrowBlockButton';
import { MenuItem } from './Menu';
type ProfileSectionProps = Omit<PropsOfComponent<typeof Flex>, 'title'> & {
    title: LocalizationKey;
    centered?: boolean;
    id: ProfileSectionId;
};
type ProfileSectionItemListProps = PropsOfComponent<typeof Col> & {
    id: ProfileSectionId;
    disableAnimation?: boolean;
};
type ProfileSectionItemProps = Omit<PropsOfComponent<typeof Flex>, 'id'> & {
    id: ProfileSectionId;
    hoverable?: boolean;
};
type ProfileSectionButtonProps = PropsOfComponent<typeof ArrowBlockButton> & {
    id: ProfileSectionId;
};
export type ProfileSectionActionMenuItemProps = PropsOfComponent<typeof MenuItem> & {
    destructive?: boolean;
    leftIcon?: React.ComponentType | React.ReactElement;
    leftIconSx?: ThemableCssProp;
};
export declare const ProfileSectionActionMenuItem: (props: ProfileSectionActionMenuItemProps) => import("@emotion/react/jsx-runtime").JSX.Element;
type ProfileSectionActionMenuProps = {
    children: React.ReactNode;
    destructive?: boolean;
    triggerLocalizationKey?: LocalizationKey;
    triggerSx?: ThemableCssProp;
    id: ProfileSectionId;
    onClick?: () => void;
};
export declare const ProfileSectionActionMenu: (props: ProfileSectionActionMenuProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export declare const ProfileSection: {
    Root: (props: ProfileSectionProps) => import("@emotion/react/jsx-runtime").JSX.Element;
    ItemList: (props: ProfileSectionItemListProps) => import("@emotion/react/jsx-runtime").JSX.Element;
    Item: (props: ProfileSectionItemProps) => import("@emotion/react/jsx-runtime").JSX.Element;
    Button: (props: ProfileSectionButtonProps) => import("@emotion/react/jsx-runtime").JSX.Element;
    ArrowButton: import("react").ForwardRefExoticComponent<Omit<ProfileSectionButtonProps, "ref"> & import("react").RefAttributes<HTMLButtonElement>>;
    ActionMenu: (props: ProfileSectionActionMenuProps) => import("@emotion/react/jsx-runtime").JSX.Element;
    ActionMenuItem: (props: ProfileSectionActionMenuItemProps) => import("@emotion/react/jsx-runtime").JSX.Element;
};
type SectionHeaderProps = PropsOfComponent<typeof Flex> & {
    localizationKey: LocalizationKey;
    textElementDescriptor?: ElementDescriptor;
    textElementId?: ElementId;
};
export declare const SectionHeader: (props: SectionHeaderProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export declare const SectionSubHeader: (props: SectionHeaderProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
