import React from 'react';
import type { StyleVariants } from '../styledSystem';
declare const applyVariants: (props?: {
    size?: "sm" | "md" | "lg" | "xs" | undefined;
    colorScheme?: "danger" | "warning" | "success" | "neutral" | undefined;
} | undefined) => (theme: import("../foundations").InternalTheme<Readonly<{
    readonly colors: Readonly<{
        readonly successAlpha300: import("@clerk/types").HslaColorString;
        readonly successAlpha600: import("@clerk/types").HslaColorString;
        readonly successAlpha25: import("@clerk/types").HslaColorString;
        readonly successAlpha50: import("@clerk/types").HslaColorString;
        readonly successAlpha100: import("@clerk/types").HslaColorString;
        readonly successAlpha150: import("@clerk/types").HslaColorString;
        readonly successAlpha200: import("@clerk/types").HslaColorString;
        readonly successAlpha400: import("@clerk/types").HslaColorString;
        readonly successAlpha500: import("@clerk/types").HslaColorString;
        readonly successAlpha700: import("@clerk/types").HslaColorString;
        readonly successAlpha750: import("@clerk/types").HslaColorString;
        readonly successAlpha800: import("@clerk/types").HslaColorString;
        readonly successAlpha850: import("@clerk/types").HslaColorString;
        readonly successAlpha900: import("@clerk/types").HslaColorString;
        readonly successAlpha950: import("@clerk/types").HslaColorString;
        readonly success50: "#F0FDF2";
        readonly success100: "#DCFCE2";
        readonly success200: "#BBF7C6";
        readonly success300: "#86EF9B";
        readonly success400: "#4ADE68";
        readonly success500: "#22C543";
        readonly success600: "#16A332";
        readonly success700: "#15802A";
        readonly success800: "#166527";
        readonly success900: "#145323";
        readonly success950: "#052E0F";
        readonly warningAlpha300: import("@clerk/types").HslaColorString;
        readonly warningAlpha600: import("@clerk/types").HslaColorString;
        readonly warningAlpha25: import("@clerk/types").HslaColorString;
        readonly warningAlpha50: import("@clerk/types").HslaColorString;
        readonly warningAlpha100: import("@clerk/types").HslaColorString;
        readonly warningAlpha150: import("@clerk/types").HslaColorString;
        readonly warningAlpha200: import("@clerk/types").HslaColorString;
        readonly warningAlpha400: import("@clerk/types").HslaColorString;
        readonly warningAlpha500: import("@clerk/types").HslaColorString;
        readonly warningAlpha700: import("@clerk/types").HslaColorString;
        readonly warningAlpha750: import("@clerk/types").HslaColorString;
        readonly warningAlpha800: import("@clerk/types").HslaColorString;
        readonly warningAlpha850: import("@clerk/types").HslaColorString;
        readonly warningAlpha900: import("@clerk/types").HslaColorString;
        readonly warningAlpha950: import("@clerk/types").HslaColorString;
        readonly warning50: "#FFF6ED";
        readonly warning100: "#FFEBD5";
        readonly warning200: "#FED1AA";
        readonly warning300: "#FDB674";
        readonly warning400: "#F98C49";
        readonly warning500: "#F36B16";
        readonly warning600: "#EA520C";
        readonly warning700: "#C23A0C";
        readonly warning800: "#9A2F12";
        readonly warning900: "#7C2912";
        readonly warning950: "#431207";
        readonly dangerAlpha300: import("@clerk/types").HslaColorString;
        readonly dangerAlpha600: import("@clerk/types").HslaColorString;
        readonly dangerAlpha25: import("@clerk/types").HslaColorString;
        readonly dangerAlpha50: import("@clerk/types").HslaColorString;
        readonly dangerAlpha100: import("@clerk/types").HslaColorString;
        readonly dangerAlpha150: import("@clerk/types").HslaColorString;
        readonly dangerAlpha200: import("@clerk/types").HslaColorString;
        readonly dangerAlpha400: import("@clerk/types").HslaColorString;
        readonly dangerAlpha500: import("@clerk/types").HslaColorString;
        readonly dangerAlpha700: import("@clerk/types").HslaColorString;
        readonly dangerAlpha750: import("@clerk/types").HslaColorString;
        readonly dangerAlpha800: import("@clerk/types").HslaColorString;
        readonly dangerAlpha850: import("@clerk/types").HslaColorString;
        readonly dangerAlpha900: import("@clerk/types").HslaColorString;
        readonly dangerAlpha950: import("@clerk/types").HslaColorString;
        readonly danger50: "#FEF2F2";
        readonly danger100: "#FEE5E5";
        readonly danger200: "#FECACA";
        readonly danger300: "#FCA5A5";
        readonly danger400: "#F87171";
        readonly danger500: "#EF4444";
        readonly danger600: "#DC2626";
        readonly danger700: "#B91C1C";
        readonly danger800: "#991B1B";
        readonly danger900: "#7F1D1D";
        readonly danger950: "#450A0A";
        readonly primaryAlpha300: import("@clerk/types").HslaColorString;
        readonly primaryAlpha600: import("@clerk/types").HslaColorString;
        readonly primaryAlpha25: import("@clerk/types").HslaColorString;
        readonly primaryAlpha50: import("@clerk/types").HslaColorString;
        readonly primaryAlpha100: import("@clerk/types").HslaColorString;
        readonly primaryAlpha150: import("@clerk/types").HslaColorString;
        readonly primaryAlpha200: import("@clerk/types").HslaColorString;
        readonly primaryAlpha400: import("@clerk/types").HslaColorString;
        readonly primaryAlpha500: import("@clerk/types").HslaColorString;
        readonly primaryAlpha700: import("@clerk/types").HslaColorString;
        readonly primaryAlpha750: import("@clerk/types").HslaColorString;
        readonly primaryAlpha800: import("@clerk/types").HslaColorString;
        readonly primaryAlpha850: import("@clerk/types").HslaColorString;
        readonly primaryAlpha900: import("@clerk/types").HslaColorString;
        readonly primaryAlpha950: import("@clerk/types").HslaColorString;
        readonly colorBackground: "white";
        readonly colorInputBackground: "white";
        readonly colorText: "#212126";
        readonly colorTextSecondary: "#747686";
        readonly colorInputText: "#131316";
        readonly colorTextOnPrimaryBackground: "white";
        readonly colorShimmer: "rgba(255, 255, 255, 0.36)";
        readonly transparent: "transparent";
        readonly white: "white";
        readonly black: "black";
        readonly primary50: "#B9BDBC";
        readonly primary100: "#9EA1A2";
        readonly primary200: "#828687";
        readonly primary300: "#66696D";
        readonly primary400: "#4B4D52";
        readonly primary500: "#2F3037";
        readonly primary600: "#2A2930";
        readonly primary700: "#25232A";
        readonly primary800: "#201D23";
        readonly primary900: "#1B171C";
        readonly primaryHover: "#3B3C45";
        readonly whiteAlpha25: "hsla(0, 0%, 100%, 0.02)";
        readonly whiteAlpha50: "hsla(0, 0%, 100%, 0.03)";
        readonly whiteAlpha100: "hsla(0, 0%, 100%, 0.07)";
        readonly whiteAlpha150: "hsla(0, 0%, 100%, 0.11)";
        readonly whiteAlpha200: "hsla(0, 0%, 100%, 0.15)";
        readonly whiteAlpha300: "hsla(0, 0%, 100%, 0.28)";
        readonly whiteAlpha400: "hsla(0, 0%, 100%, 0.41)";
        readonly whiteAlpha500: "hsla(0, 0%, 100%, 0.53)";
        readonly whiteAlpha600: "hsla(0, 0%, 100%, 0.62)";
        readonly whiteAlpha700: "hsla(0, 0%, 100%, 0.73)";
        readonly whiteAlpha750: "hsla(0, 0%, 100%, 0.78)";
        readonly whiteAlpha800: "hsla(0, 0%, 100%, 0.81)";
        readonly whiteAlpha850: "hsla(0, 0%, 100%, 0.84)";
        readonly whiteAlpha900: "hsla(0, 0%, 100%, 0.87)";
        readonly whiteAlpha950: "hsla(0, 0%, 100%, 0.92)";
        readonly neutralAlpha25: "hsla(0, 0%, 0%, 0.02)";
        readonly neutralAlpha50: "hsla(0, 0%, 0%, 0.03)";
        readonly neutralAlpha100: "hsla(0, 0%, 0%, 0.07)";
        readonly neutralAlpha150: "hsla(0, 0%, 0%, 0.11)";
        readonly neutralAlpha200: "hsla(0, 0%, 0%, 0.15)";
        readonly neutralAlpha300: "hsla(0, 0%, 0%, 0.28)";
        readonly neutralAlpha400: "hsla(0, 0%, 0%, 0.41)";
        readonly neutralAlpha500: "hsla(0, 0%, 0%, 0.53)";
        readonly neutralAlpha600: "hsla(0, 0%, 0%, 0.62)";
        readonly neutralAlpha700: "hsla(0, 0%, 0%, 0.73)";
        readonly neutralAlpha750: "hsla(0, 0%, 0%, 0.78)";
        readonly neutralAlpha800: "hsla(0, 0%, 0%, 0.81)";
        readonly neutralAlpha850: "hsla(0, 0%, 0%, 0.84)";
        readonly neutralAlpha900: "hsla(0, 0%, 0%, 0.87)";
        readonly neutralAlpha950: "hsla(0, 0%, 0%, 0.92)";
        readonly avatarBorder: "hsla(0, 0%, 0%, 0.15)";
        readonly avatarBackground: "hsla(0, 0%, 0%, 0.41)";
        readonly modalBackdrop: "hsla(0, 0%, 0%, 0.73)";
    }>;
    readonly fonts: Readonly<{
        readonly main: "inherit";
        readonly buttons: "inherit";
    }>;
    readonly fontStyles: Readonly<{
        readonly normal: "normal";
    }>;
    readonly fontSizes: Readonly<{
        readonly xs: "0.6875rem";
        readonly sm: "0.75rem";
        readonly md: "0.8125rem";
        readonly lg: "1.0625rem";
        readonly xl: "1.5rem";
    }>;
    readonly fontWeights: Readonly<{
        readonly normal: 400;
        readonly medium: 500;
        readonly semibold: 600;
        readonly bold: 700;
    }>;
    readonly letterSpacings: Readonly<{
        readonly normal: "normal";
    }>;
    readonly lineHeights: Readonly<{
        readonly normal: "normal";
        readonly extraSmall: "1.33333";
        readonly small: "1.38462";
        readonly medium: "1.41176";
        readonly large: "1.45455";
    }>;
    readonly radii: Readonly<{
        readonly none: "0px";
        readonly circle: "50%";
        readonly avatar: "0.375rem";
        readonly sm: "0.25rem";
        readonly md: "0.375rem";
        readonly lg: "0.5rem";
        readonly xl: "0.75rem";
        readonly halfHeight: "99999px";
    }>;
    readonly sizes: Readonly<{
        readonly '0x25': "0.0625rem";
        readonly '0x5': "0.125rem";
        readonly '1': "0.25rem";
        readonly '1x5': "0.375rem";
        readonly '2': "0.5rem";
        readonly '2x5': "0.625rem";
        readonly '3': "0.75rem";
        readonly '3x25': "0.8125rem";
        readonly '3x5': "0.875rem";
        readonly '4': "1rem";
        readonly '4x25': "1.0625rem";
        readonly '5': "1.25rem";
        readonly '5x5': "1.375rem";
        readonly '6': "1.5rem";
        readonly '7': "1.75rem";
        readonly '7x5': "1.875rem";
        readonly '8': "2rem";
        readonly '8x5': "2.125rem";
        readonly '8x75': "2.1875rem";
        readonly '9': "2.25rem";
        readonly '10': "2.5rem";
        readonly '12': "3rem";
        readonly '13': "3.5rem";
        readonly '16': "4rem";
        readonly '17': "4.25rem";
        readonly '20': "5rem";
        readonly '24': "6rem";
        readonly '28': "7rem";
        readonly '32': "8rem";
        readonly '36': "9rem";
        readonly '40': "10rem";
        readonly '44': "11rem";
        readonly '48': "12rem";
        readonly '52': "13rem";
        readonly '56': "14rem";
        readonly '57': "14.25rem";
        readonly '60': "15rem";
        readonly '66': "16.5rem";
        readonly '94': "23.5rem";
        readonly '100': "25rem";
        readonly '108': "27rem";
        readonly '120': "30rem";
        readonly '140': "35rem";
        readonly '160': "40rem";
        readonly '176': "44rem";
        readonly '220': "55rem";
        readonly none: "0";
        readonly xxs: "0.5px";
        readonly px: "1px";
    }>;
    readonly space: Readonly<{
        readonly '0x25': "0.0625rem";
        readonly '0x5': "0.125rem";
        readonly '1': "0.25rem";
        readonly '1x5': "0.375rem";
        readonly '2': "0.5rem";
        readonly '2x5': "0.625rem";
        readonly '3': "0.75rem";
        readonly '3x25': "0.8125rem";
        readonly '3x5': "0.875rem";
        readonly '4': "1rem";
        readonly '4x25': "1.0625rem";
        readonly '5': "1.25rem";
        readonly '5x5': "1.375rem";
        readonly '6': "1.5rem";
        readonly '7': "1.75rem";
        readonly '7x5': "1.875rem";
        readonly '8': "2rem";
        readonly '8x5': "2.125rem";
        readonly '8x75': "2.1875rem";
        readonly '9': "2.25rem";
        readonly '10': "2.5rem";
        readonly '12': "3rem";
        readonly '13': "3.5rem";
        readonly '16': "4rem";
        readonly '17': "4.25rem";
        readonly '20': "5rem";
        readonly '24': "6rem";
        readonly '28': "7rem";
        readonly '32': "8rem";
        readonly '36': "9rem";
        readonly '40': "10rem";
        readonly '44': "11rem";
        readonly '48': "12rem";
        readonly '52': "13rem";
        readonly '56': "14rem";
        readonly '57': "14.25rem";
        readonly '60': "15rem";
        readonly '66': "16.5rem";
        readonly '94': "23.5rem";
        readonly '100': "25rem";
        readonly '108': "27rem";
        readonly '120': "30rem";
        readonly '140': "35rem";
        readonly '160': "40rem";
        readonly '176': "44rem";
        readonly '220': "55rem";
        readonly none: "0";
        readonly xxs: "0.5px";
        readonly px: "1px";
    }>;
    readonly shadows: Readonly<{
        readonly menuShadow: "0px 5px 15px 0px rgba(0, 0, 0, 0.08), 0px 15px 35px -5px rgba(25, 28, 33, 0.20)";
        readonly fabShadow: "0px 12px 24px rgba(0, 0, 0, 0.32)";
        readonly buttonShadow: "0px 1px 1px 0px rgba(255, 255, 255, 0.07) inset, 0px 2px 3px 0px rgba(34, 42, 53, 0.20), 0px 1px 1px 0px rgba(0, 0, 0, 0.24)";
        readonly cardBoxShadow: "0px 5px 15px 0px rgba(0, 0, 0, 0.08), 0px 15px 35px -5px rgba(25, 28, 33, 0.20)";
        readonly cardContentShadow: "0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 1px 2px 0px rgba(25, 28, 33, 0.06)";
        readonly actionCardShadow: "0px 1px 4px 0px rgba(0, 0, 0, 0.12), 0px 4px 8px 0px rgba(106, 115, 133, 0.12)";
        readonly outlineButtonShadow: "0px 2px 3px -1px rgba(0, 0, 0, 0.08), 0px 1px 0px 0px rgba(0, 0, 0, 0.02)";
        readonly input: "0px 0px 1px 0px {{color}}";
        readonly focusRing: "0px 0px 0px 4px {{color}}";
        readonly badge: "0px 2px 0px -1px rgba(0, 0, 0, 0.04)";
        readonly tableBodyShadow: "0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 1px 2px 0px rgba(25, 28, 33, 0.12), 0px 0px 0px 1px rgba(0, 0, 0, 0.06)";
        readonly segmentedControl: "0px 1px 2px 0px rgba(0, 0, 0, 0.08)";
        readonly switchControl: "0px 2px 2px -1px rgba(0, 0, 0, 0.06), 0px 0px 0px 1px rgba(0, 0, 0, 0.06), 0px 4px 4px -2px rgba(0, 0, 0, 0.06)";
    }>;
    readonly transitionProperty: Readonly<{
        readonly common: "background-color,background,border-color,color,fill,stroke,opacity,box-shadow,transform";
    }>;
    readonly transitionTiming: Readonly<{
        readonly common: "ease";
        readonly easeOut: "ease-out";
        readonly bezier: "cubic-bezier(0.32, 0.72, 0, 1)";
        readonly slowBezier: "cubic-bezier(0.16, 1, 0.3, 1)";
    }>;
    readonly transitionDuration: Readonly<Record<"focusRing" | "slowest" | "slower" | "slow" | "fast" | "controls" | "textField" | "drawer", string>>;
    readonly transitionDurationValues: Readonly<{
        readonly slowest: 600;
        readonly slower: 280;
        readonly slow: 200;
        readonly fast: 120;
        readonly focusRing: 200;
        readonly controls: 100;
        readonly textField: 450;
        readonly drawer: 500;
    }>;
    readonly opacity: Readonly<{
        readonly sm: "24%";
        readonly disabled: "50%";
        readonly inactive: "62%";
    }>;
    readonly borderStyles: Readonly<{
        readonly solid: "solid";
        readonly dashed: "dashed";
    }>;
    readonly borderWidths: Readonly<{
        normal: "1px";
        heavy: "2px";
    }>;
    readonly zIndices: Readonly<{
        readonly card: "10";
        readonly navbar: "100";
        readonly fab: "9000";
        readonly modal: "10000";
        readonly dropdown: "11000";
    }>;
}>>) => import("../styledSystem").StyleRule;
export type IconProps = StyleVariants<typeof applyVariants> & {
    icon: React.ComponentType;
};
export declare const Icon: (props: IconProps) => JSX.Element;
export {};
