import type { CommerceFeatureJSON, CommerceFeatureJSONSnapshot, CommerceFeatureResource } from '@clerk/types';
import { BaseResource } from './internal';
export declare class CommerceFeature extends BaseResource implements CommerceFeatureResource {
    id: string;
    name: string;
    description: string;
    slug: string;
    avatarUrl: string;
    constructor(data: CommerceFeatureJSON);
    protected fromJSON(data: CommerceFeatureJSON | null): this;
    __internal_toSnapshot(): CommerceFeatureJSONSnapshot;
}
