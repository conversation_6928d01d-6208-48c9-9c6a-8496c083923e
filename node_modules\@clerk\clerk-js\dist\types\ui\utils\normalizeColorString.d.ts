/**
 * Normalizes color format strings by removing alpha values if present
 * Handles conversions between:
 * - Hex: #RGB, #RGBA, #RRGGBB, #RRGGBBAA → #RGB or #RRGGBB
 * - RGB: rgb(r, g, b), rgba(r, g, b, a) → rgb(r, g, b)
 * - HSL: hsl(h, s%, l%), hsla(h, s%, l%, a) → hsl(h, s%, l%)
 *
 * @param colorString - The color string to normalize
 * @returns The normalized color string without alpha components, or the original string if invalid
 */
export declare function normalizeColorString(colorString: string): string;
