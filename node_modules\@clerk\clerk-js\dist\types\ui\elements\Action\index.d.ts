export declare const Action: {
    Root: (props: {
        animate?: boolean;
        value?: string | null;
        onChange?: (value: string | null) => void;
    } & {
        children?: import("react").ReactNode | undefined;
    }) => import("@emotion/react/jsx-runtime").JSX.Element;
    Card: (props: Omit<import("../../primitives").FlexProps, "ref"> & import("react").RefAttributes<HTMLDivElement> & {
        elementDescriptor?: import("../../customizables/elementDescriptors").ElementDescriptor | Array<import("../../customizables/elementDescriptors").ElementDescriptor | undefined>;
        elementId?: import("../../customizables/elementDescriptors").ElementId;
        css?: never;
        sx?: import("../../styledSystem").ThemableCssProp;
    } & {
        variant?: "neutral" | "destructive";
    }) => import("@emotion/react/jsx-runtime").JSX.Element;
    Trigger: (props: {
        value: string;
        hideOnActive?: boolean;
    } & {
        children?: import("react").ReactNode | undefined;
    }) => import("react").DetailedReactHTMLElement<import("react").HTMLAttributes<HTMLElement>, HTMLElement> | null;
    Open: ({ children, value }: {
        value: string;
    } & {
        children?: import("react").ReactNode | undefined;
    }) => import("@emotion/react/jsx-runtime").JSX.Element | null;
    Closed: (props: {
        value: string | string[];
    } & {
        children?: import("react").ReactNode | undefined;
    }) => import("react").ReactNode;
};
