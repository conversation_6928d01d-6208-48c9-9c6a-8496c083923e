{"name": "@clerk/clerk-js", "version": "5.69.0", "description": "Clerk JS library", "keywords": ["clerk", "auth", "authentication", "passwordless", "session", "jwt"], "homepage": "https://clerk.com/", "bugs": {"url": "https://github.com/clerk/javascript/issues"}, "repository": {"type": "git", "url": "git+https://github.com/clerk/javascript.git", "directory": "packages/clerk-js"}, "license": "MIT", "author": "Clerk", "main": "dist/clerk.js", "jsdelivr": "dist/clerk.browser.js", "module": "dist/clerk.mjs", "types": "dist/types/index.d.ts", "files": ["dist", "headless", "no-rhc"], "browserslist": "last 2 years", "dependencies": {"@coinbase/wallet-sdk": "4.3.0", "@emotion/cache": "11.11.0", "@emotion/react": "11.11.1", "@floating-ui/react": "0.27.12", "@floating-ui/react-dom": "^2.1.3", "@formkit/auto-animate": "^0.8.2", "@stripe/react-stripe-js": "3.1.1", "@stripe/stripe-js": "5.6.0", "@swc/helpers": "^0.5.17", "@zxcvbn-ts/core": "3.0.4", "@zxcvbn-ts/language-common": "3.0.4", "browser-tabs-lock": "1.3.0", "copy-to-clipboard": "3.3.3", "core-js": "3.41.0", "crypto-js": "^4.2.0", "dequal": "2.0.3", "qrcode.react": "4.2.0", "regenerator-runtime": "0.14.1", "swr": "2.3.3", "@clerk/localizations": "^3.16.5", "@clerk/shared": "^3.9.7", "@clerk/types": "^4.60.1"}, "devDependencies": {"@emotion/jest": "^11.13.0", "@rsdoctor/rspack-plugin": "^0.4.13", "@rspack/cli": "^1.2.8", "@rspack/core": "^1.2.8", "@rspack/plugin-react-refresh": "^1.0.1", "@svgr/webpack": "^6.5.1", "@swc/jest": "^0.2.38", "@types/cloudflare-turnstile": "^0.2.2", "@types/webpack-env": "^1.18.8", "jsdom": "^24.1.1", "webpack-merge": "^5.10.0"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0 || ^19.0.0-0", "react-dom": "^18.0.0 || ^19.0.0 || ^19.0.0-0"}, "engines": {"node": ">=18.17.0"}, "publishConfig": {"access": "public"}, "browserslistLegacy": "Chrome > 73, Firefox > 66, <PERSON><PERSON> > 12, <PERSON> > 12, <PERSON> > 18, <PERSON> > 58", "scripts": {"build": "pnpm build:bundle && pnpm build:declarations", "postbuild": "node ../../scripts/search-for-rhc.mjs file dist/clerk.no-rhc.mjs", "build:analyze": "rspack build --config rspack.config.js --env production --env variant=\"clerk.browser\" --env analysis --analyze", "build:bundle": "pnpm clean && rspack build --config rspack.config.js --env production", "build:declarations": "tsc -p tsconfig.declarations.json", "build:sandbox": "rspack build --config rspack.config.js --env production --env sandbox", "build:stats": "rspack build --config rspack.config.js --env production --json=stats.json --env variant=\"clerk.browser\"", "bundlewatch": "pnpm --package=bundlewatch -c dlx \"FORCE_COLOR=1 bundlewatch --config bundlewatch.config.json\"", "clean": "rimraf ./dist", "dev": "rspack serve --config rspack.config.js", "dev:headless": "rspack serve --config rspack.config.js --env variant=\"clerk.headless.browser\"", "dev:origin": "rspack serve --config rspack.config.js --env devOrigin=http://localhost:4000", "dev:sandbox": "rspack serve --config rspack.config.js --env devOrigin=http://localhost:4000 --env sandbox=1", "lint": "eslint src", "lint:attw": "attw --pack . --profile node16 --ignore-rules named-exports", "lint:publint": "publint || true", "test": "jest && vitest --watch=false", "test:cache:clear": "jest --clearCache --useStderr", "test:ci": "jest --maxWorkers=70%", "test:coverage": "jest --collectCoverage && open coverage/lcov-report/index.html", "test:jest": "jest", "test:vitest": "vitest", "watch": "rspack build --config rspack.config.js --env production --watch"}}