import type { Web3Provider } from '@clerk/types';
type GetWeb3IdentifierParams = {
    provider: Web3Provider;
};
export declare function getWeb3Identifier(params: GetWeb3IdentifierParams): Promise<string>;
type GenerateWeb3SignatureParams = GenerateSignatureParams & {
    provider: Web3Provider;
};
export declare function generateWeb3Signature(params: GenerateWeb3SignatureParams): Promise<string>;
export declare function getMetamaskIdentifier(): Promise<string>;
export declare function getCoinbaseWalletIdentifier(): Promise<string>;
export declare function getOKXWalletIdentifier(): Promise<string>;
type GenerateSignatureParams = {
    identifier: string;
    nonce: string;
};
export declare function generateSignatureWithMetamask(params: GenerateSignatureParams): Promise<string>;
export declare function generateSignatureWithCoinbaseWallet(params: GenerateSignatureParams): Promise<string>;
export declare function generateSignatureWithOKXWallet(params: GenerateSignatureParams): Promise<string>;
export {};
