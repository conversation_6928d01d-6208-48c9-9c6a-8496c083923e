import React from 'react';
export declare const InvisibleRootBox: React.FunctionComponent<{
    className: string;
} & {
    children?: React.ReactNode | undefined;
} & {
    elementDescriptor?: import("../customizables/elementDescriptors").ElementDescriptor | Array<import("../customizables/elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("../customizables/elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
