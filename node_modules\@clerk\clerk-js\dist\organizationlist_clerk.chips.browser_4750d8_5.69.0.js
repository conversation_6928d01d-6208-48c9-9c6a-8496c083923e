"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["158"],{1816:function(e,t,i){i.d(t,{$:()=>C});var a=i(9109),n=i(3799),r=i(9144),o=i(2672),l=i(431),s=i(9460),c=i(8487),d=i(2654),u=i(3412),g=i(7263),h=i(8969),p=i(9541),z=i(4174),m=i(1085),x=i(7623),Z=i(8493),S=i(5727),$=i(8181),b=i(7484);let C=(0,o.withCardStateProvider)(e=>{let t=(0,o.useCardState)(),i=(0,h.a2)({onNextStep:()=>t.setError(void 0)}),C=r.useRef(null),{createOrganization:f,isLoaded:v,setActive:w,userMemberships:y}=(0,n.eW)({userMemberships:b.AO.userMemberships}),{organization:L}=(0,n.o8)(),[P,T]=r.useState(),k=(0,x.Yp)("name","",{type:"text",label:(0,m.u1)("formFieldLabel__organizationName"),placeholder:(0,m.u1)("formFieldInputPlaceholder__organizationName")}),A=(0,x.Yp)("slug","",{type:"text",label:(0,m.u1)("formFieldLabel__organizationSlug"),placeholder:(0,m.u1)("formFieldInputPlaceholder__organizationSlug")}),B=!!k.value,I=async a=>{if(a.preventDefault(),B&&v)try{let t={name:k.value};e.hideSlug||(t.slug=A.value);let a=await f(t);if(P&&await a.setLogo({file:P}),C.current=a,await w({organization:a}),y.revalidate?.(),e.skipInvitationScreen??1===a.maxAllowedMemberships)return D();i.nextStep()}catch(e){(0,x.S3)(e,[k,A],t.setError)}},D=()=>{e.navigateAfterCreateOrganization(C.current),e.onComplete?.()},O=e=>{A.setValue(e)},_="organizationList"===e.flow?"subtitle":void 0;return(0,a.BX)(h.en,{...i.props,children:[(0,a.tZ)(c.Y,{headerTitle:e?.startPage?.headerTitle,headerSubtitle:e?.startPage?.headerSubtitle,headerTitleTextVariant:"h2",headerSubtitleTextVariant:_,sx:e=>({minHeight:e.sizes.$60,gap:e.space.$6,textAlign:"left"}),children:(0,a.BX)(l.l.Root,{onSubmit:I,sx:e=>({gap:e.space.$6}),children:[(0,a.tZ)(p.Col,{children:(0,a.tZ)($.D,{organization:{name:k.value},onAvatarChange:async e=>await T(e),onAvatarRemove:P?()=>(t.setIdle(),T(null)):null,avatarPreviewPlaceholder:(0,a.tZ)(u.h,{variant:"ghost","aria-label":"Upload organization logo",icon:(0,a.tZ)(p.Icon,{size:"md",icon:z.gq,sx:e=>({color:e.colors.$colorTextSecondary,transitionDuration:e.transitionDuration.$controls})}),sx:e=>({width:e.sizes.$16,height:e.sizes.$16,borderRadius:e.radii.$md,borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$dashed,borderColor:e.colors.$neutralAlpha200,backgroundColor:e.colors.$neutralAlpha50,":hover":{backgroundColor:e.colors.$neutralAlpha50,svg:{transform:"scale(1.2)"}}})})})}),(0,a.tZ)(l.l.ControlRow,{elementId:k.id,children:(0,a.tZ)(l.l.PlainInput,{...k.props,onChange:e=>{k.setValue(e.target.value),O((0,x.qi)(e.target.value))},isRequired:!0,autoFocus:!0,ignorePasswordManager:!0})}),!e.hideSlug&&(0,a.tZ)(l.l.ControlRow,{elementId:A.id,children:(0,a.tZ)(l.l.PlainInput,{...A.props,onChange:e=>{O(e.target.value)},isRequired:!0,pattern:"^(?=.*[a-z0-9])[a-z0-9\\-]+$",ignorePasswordManager:!0})}),(0,a.BX)(s.K,{sx:e=>({marginTop:e.space.$none}),children:[(0,a.tZ)(l.l.SubmitButton,{block:!1,isDisabled:!B,localizationKey:(0,m.u1)("createOrganization.formButtonSubmit")}),e.onCancel&&(0,a.tZ)(l.l.ResetButton,{localizationKey:(0,m.u1)("userProfile.formButtonReset"),block:!1,onClick:e.onCancel})]})]})}),(0,a.tZ)(c.Y,{headerTitle:(0,m.u1)("organizationProfile.invitePage.title"),headerTitleTextVariant:"h2",headerSubtitleTextVariant:_,sx:e=>({minHeight:e.sizes.$60,textAlign:"left"}),children:L&&(0,a.tZ)(Z.l,{resetButtonLabel:(0,m.u1)("createOrganization.invitePage.formButtonReset"),onSuccess:i.nextStep,onReset:D})}),(0,a.BX)(p.Col,{children:[(0,a.tZ)(d.h.Root,{children:(0,a.tZ)(d.h.Title,{localizationKey:(0,m.u1)("organizationProfile.invitePage.title"),sx:{textAlign:"left"}})}),(0,a.tZ)(g.I,{contents:(0,a.tZ)(S.a,{}),sx:e=>({minHeight:e.sizes.$60}),onFinish:D})]})]})})},683:function(e,t,i){i.r(t),i.d(t,{OrganizationList:()=>Y});var a=i(9109),n=i(1576),r=i(9541),o=i(4676),l=i(3799),s=i(9144),c=i(2305),d=i(4455),u=i(2672),g=i(2654),h=i(6988),p=i(2464),z=i(4174),m=i(1816),x=i(5973),Z=i(8774),S=i(1201);let $=e=>(0,a.tZ)(r.Col,{elementDescriptor:r.descriptors.organizationListPreviewItems,sx:e=>({maxHeight:`calc(8 * ${e.sizes.$12})`,overflowY:"auto",borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100,...S.common.unstyledScrollbar(e)}),children:e.children}),b=e=>({padding:`${e.space.$4} ${e.space.$5}`}),C=e=>({color:e.colors.$colorText,":hover":{color:e.colors.$colorText}}),f=e=>(0,a.BX)(r.Flex,{align:"center",gap:2,sx:[e=>({minHeight:"unset",justifyContent:"space-between",borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100}),b],elementDescriptor:r.descriptors.organizationListPreviewItem,children:[(0,a.tZ)(x.Z,{elementId:"organizationList",mainIdentifierSx:C,organization:e.organizationData}),e.children]}),v=(0,s.forwardRef)((e,t)=>(0,a.tZ)(r.Box,{ref:t,sx:e=>({width:"100%",height:e.space.$12,position:"relative"}),children:(0,a.tZ)(r.Box,{sx:{margin:"auto",position:"absolute",left:"50%",top:"50%",transform:"translateY(-50%) translateX(-50%)"},children:(0,a.tZ)(r.Spinner,{size:"sm",colorScheme:"primary",elementDescriptor:r.descriptors.spinner})})})),w=e=>(0,a.tZ)(r.Button,{elementDescriptor:r.descriptors.organizationListPreviewItemActionButton,textVariant:"buttonSmall",variant:"outline",size:"xs",...e}),y=e=>(0,a.tZ)(Z.K,{elementDescriptor:r.descriptors.organizationListPreviewButton,sx:[b],icon:z.nR,...e});var L=i(7623),P=i(7484),T=i(2915),k=i(1085);let A=(0,u.withCardStateProvider)(e=>{let t=(0,u.useCardState)(),{navigateAfterSelectOrganization:i}=(0,n.useOrganizationListContext)(),{isLoaded:r,setActive:o}=(0,l.eW)(),c=(0,s.useContext)(h.H);if(!r)return null;let d=e=>t.runAsync(async()=>{if(await o({organization:e}),c?.nextTask)return c?.nextTask();await i(e)});return(0,a.tZ)(y,{onClick:()=>d(e.organization),children:(0,a.tZ)(x.Z,{elementId:"organizationList",mainIdentifierSx:C,organization:e.organization})})}),B=(0,u.withCardStateProvider)(()=>{let e=(0,u.useCardState)(),{hidePersonal:t,navigateAfterSelectPersonal:i}=(0,n.useOrganizationListContext)(),{isLoaded:r,setActive:o}=(0,l.eW)(),{user:s}=(0,l.aF)();if(!s)return null;let{username:c,primaryEmailAddress:d,primaryPhoneNumber:g,...h}=s;return t?null:(0,a.tZ)(y,{onClick:()=>{if(r)return e.runAsync(async()=>{await o({organization:null}),await i(s)})},children:(0,a.tZ)(T.g,{user:h,mainIdentifierSx:C,title:(0,k.u1)("organizationSwitcher.personalWorkspace")})})}),I={userMemberships:{infinite:!0},userInvitations:{infinite:!0},userSuggestions:{infinite:!0,status:["pending","accepted"]}},D=e=>{let t=(0,u.useCardState)();return(0,a.tZ)(w,{isLoading:t.isLoading,onClick:e.onAccept,localizationKey:(0,r.localizationKeys)("organizationList.action__invitationAccept")})},O=(0,u.withCardStateProvider)(e=>{let t=(0,u.useCardState)(),{getOrganization:i}=(0,l.cL)(),[n,r]=(0,s.useState)(null),{userInvitations:o}=(0,l.eW)({userInvitations:I.userInvitations,userMemberships:I.userMemberships});return n?(0,a.tZ)(A,{organization:n}):(0,a.tZ)(f,{organizationData:e.publicOrganizationData,children:(0,a.tZ)(D,{onAccept:()=>t.runAsync(async()=>[await e.accept(),await i(e.publicOrganizationData.id)]).then(([e,t])=>{o?.setData?.(t=>P.Sz(e,t,"negative")),r(t)}).catch(e=>(0,L.S3)(e,[],t.setError))})})}),_=e=>{let t=(0,u.useCardState)(),{userSuggestions:i}=(0,l.eW)({userSuggestions:I.userSuggestions});return"accepted"===e.status?(0,a.tZ)(r.Text,{colorScheme:"secondary",localizationKey:(0,r.localizationKeys)("organizationList.suggestionsAcceptedLabel")}):(0,a.tZ)(w,{isLoading:t.isLoading,onClick:()=>t.runAsync(e.accept).then(e=>i?.setData?.(t=>P.Sz(e,t))).catch(e=>(0,L.S3)(e,[],t.setError)),localizationKey:(0,r.localizationKeys)("organizationList.action__suggestionsAccept")})},R=(0,u.withCardStateProvider)(e=>(0,a.tZ)(f,{organizationData:e.publicOrganizationData,children:(0,a.tZ)(_,{...e})})),K=()=>{let{userMemberships:e,userInvitations:t,userSuggestions:i}=(0,l.eW)(I),{ref:a}=(0,p.YD)({threshold:0,onChange:a=>{a&&(e.hasNextPage?e.fetchNext?.():t.hasNextPage?t.fetchNext?.():i.fetchNext?.())}});return{userMemberships:e,userInvitations:t,userSuggestions:i,ref:a}},N=({onCreateOrganizationClick:e})=>{let{user:t}=(0,l.aF)();return t?.createOrganizationEnabled?(0,a.tZ)(c.aU,{elementDescriptor:r.descriptors.organizationListCreateOrganizationActionButton,icon:z.mm,label:(0,r.localizationKeys)("organizationList.action__createOrganization"),onClick:e,sx:e=>({borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100,padding:`${e.space.$5} ${e.space.$5}`}),iconSx:e=>({width:e.sizes.$9,height:e.sizes.$6})}):null},W=(0,u.withCardStateProvider)(()=>{let e=(0,u.useCardState)(),{userMemberships:t,userSuggestions:i,userInvitations:o}=K(),l=t?.isLoading||o?.isLoading||i?.isLoading,s=!!(t?.count||o?.count||i?.count),{hidePersonal:c}=(0,n.useOrganizationListContext)();return(0,a.BX)(d.Z.Root,{children:[(0,a.BX)(d.Z.Content,{sx:e=>({padding:`${e.space.$8} ${e.space.$none} ${e.space.$none}`}),children:[(0,a.tZ)(d.Z.Alert,{sx:e=>({margin:`${e.space.$none} ${e.space.$5}`}),children:e.error}),l&&(0,a.tZ)(r.Flex,{direction:"row",align:"center",justify:"center",sx:e=>({height:"100%",minHeight:e.sizes.$60}),children:(0,a.tZ)(r.Spinner,{size:"lg",colorScheme:"primary",elementDescriptor:r.descriptors.spinner})}),!l&&(0,a.tZ)(F,{showListInitially:!(c&&!s)})]}),(0,a.tZ)(d.Z.Footer,{})]})}),F=({showListInitially:e})=>{let{navigateAfterCreateOrganization:t,skipInvitationScreen:i,hideSlug:o}=(0,n.useOrganizationListContext)(),[l,c]=(0,s.useState)(!e),d=(0,s.useContext)(h.H);return(0,a.BX)(a.HY,{children:[!l&&(0,a.tZ)(X,{onCreateOrganizationClick:()=>c(!0)}),l&&(0,a.tZ)(r.Box,{sx:e=>({padding:`${e.space.$none} ${e.space.$5} ${e.space.$5}`}),children:(0,a.tZ)(m.$,{flow:"organizationList",onComplete:d?.nextTask,startPage:{headerTitle:(0,r.localizationKeys)("organizationList.createOrganization")},skipInvitationScreen:i,navigateAfterCreateOrganization:e=>t(e).then(()=>c(!1)),onCancel:e&&l?()=>c(!1):void 0,hideSlug:o})})]})},X=e=>{let t=(0,n.useEnvironment)(),{ref:i,userMemberships:o,userSuggestions:l,userInvitations:s}=K(),{hidePersonal:d}=(0,n.useOrganizationListContext)(),u=o?.isLoading||s?.isLoading||l?.isLoading,h=o?.hasNextPage||s?.hasNextPage||l?.hasNextPage,p=s.data?.filter(e=>!!e),z=l.data?.filter(e=>!!e);return(0,a.BX)(a.HY,{children:[(0,a.BX)(g.h.Root,{sx:e=>({padding:`${e.space.$none} ${e.space.$8}`}),children:[(0,a.tZ)(g.h.Title,{localizationKey:(0,r.localizationKeys)(d?"organizationList.titleWithoutPersonal":"organizationList.title")}),(0,a.tZ)(g.h.Subtitle,{localizationKey:(0,r.localizationKeys)("organizationList.subtitle",{applicationName:t.displayConfig.applicationName})})]}),(0,a.tZ)(r.Col,{elementDescriptor:r.descriptors.main,children:(0,a.tZ)($,{children:(0,a.BX)(c.eX,{role:"menu",children:[(0,a.tZ)(B,{}),(o.count||0)>0&&o.data?.map(e=>a.tZ(A,{...e},e.id)),!o.hasNextPage&&p?.map(e=>a.tZ(O,{...e},e.id)),!o.hasNextPage&&!s.hasNextPage&&z?.map(e=>a.tZ(R,{...e},e.id)),(h||u)&&(0,a.tZ)(v,{ref:i}),(0,a.tZ)(N,{onCreateOrganizationClick:()=>{e.onCreateOrganizationClick()}})]})})})]})},H=(0,n.withCoreUserGuard)(W),Y=()=>(0,a.tZ)(r.Flow.Root,{flow:"organizationList",children:(0,a.tZ)(r.Flow.Part,{children:(0,a.tZ)(o.Switch,{children:(0,a.tZ)(o.Route,{children:(0,a.tZ)(H,{})})})})})}}]);