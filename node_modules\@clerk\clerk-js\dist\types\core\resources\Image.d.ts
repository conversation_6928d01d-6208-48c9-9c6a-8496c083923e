import type { ImageJSON, ImageResource } from '@clerk/types';
import { BaseResource } from './internal';
export declare class Image extends BaseResource implements ImageResource {
    id?: string;
    name: string | null;
    publicUrl: string | null;
    static create(path: string, body?: any): Promise<ImageResource>;
    static delete(path: string): Promise<ImageResource>;
    constructor(data: ImageJSON);
    protected fromJSON(data: ImageJSON | null): this;
}
