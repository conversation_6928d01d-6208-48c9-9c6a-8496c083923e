import type { CaptchaProvider, CaptchaWidgetType, DisplayConfigJSON, DisplayConfigJSONSnapshot, DisplayConfigResource, DisplayThemeJSON, OAuthStrategy, PreferredSignInStrategy } from '@clerk/types';
import { BaseResource } from './internal';
export declare class DisplayConfig extends BaseResource implements DisplayConfigResource {
    afterCreateOrganizationUrl: string;
    afterJoinWaitlistUrl: string;
    afterLeaveOrganizationUrl: string;
    afterSignInUrl: string;
    afterSignOutAllUrl: string;
    afterSignOutOneUrl: string;
    afterSignOutUrl: string;
    afterSignUpUrl: string;
    afterSwitchSessionUrl: string;
    applicationName: string;
    backendHost: string;
    branded: boolean;
    captchaHeartbeat: boolean;
    captchaHeartbeatIntervalMs?: number;
    captchaOauthBypass: OAuthStrategy[];
    captchaProvider: CaptchaProvider;
    captchaPublicKey: string | null;
    captchaPublicKeyInvisible: string | null;
    captchaWidgetType: CaptchaWidgetType;
    clerkJSVersion?: string;
    createOrganizationUrl: string;
    experimental__forceOauthFirst?: boolean;
    faviconImageUrl: string;
    googleOneTapClientId?: string;
    homeUrl: string;
    id: string;
    instanceEnvironmentType: string;
    logoImageUrl: string;
    organizationProfileUrl: string;
    preferredSignInStrategy: PreferredSignInStrategy;
    privacyPolicyUrl: string;
    showDevModeWarning: boolean;
    signInUrl: string;
    signUpUrl: string;
    supportEmail: string;
    termsUrl: string;
    theme: DisplayThemeJSON;
    userProfileUrl: string;
    waitlistUrl: string;
    constructor(data?: DisplayConfigJSON | DisplayConfigJSONSnapshot | null);
    protected fromJSON(data: DisplayConfigJSON | DisplayConfigJSONSnapshot | null): this;
    __internal_toSnapshot(): DisplayConfigJSONSnapshot;
}
