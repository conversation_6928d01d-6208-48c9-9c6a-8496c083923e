import type { OrganizationResource, UserOrganizationInvitationResource } from '@clerk/types';
import type { Dispatch, ReactNode, SetStateAction } from 'react';
type Value = {
    acceptedInvitations: {
        invitation: UserOrganizationInvitationResource;
        organization: OrganizationResource;
    }[];
    setAcceptedInvitations: Dispatch<SetStateAction<{
        invitation: UserOrganizationInvitationResource;
        organization: OrganizationResource;
    }[]>>;
};
interface InPlaceAcceptedInvitationsProps {
    children: ReactNode;
}
declare function AcceptedInvitationsProvider({ children }: InPlaceAcceptedInvitationsProps): JSX.Element;
declare function useAcceptedInvitations(): Value;
export { AcceptedInvitationsProvider, useAcceptedInvitations };
