"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["916"],{4961:function(i,e,t){t.r(e),t.d(e,{OrganizationBillingPage:()=>m});var a=t(9109),n=t(4455),l=t(2672),o=t(2654),s=t(708),r=t(8969),c=t(1576),d=t(9541),g=t(3746),h=t(2264),p=t(6054),u=t(5515),b=t(1800);let y={0:"subscriptions",1:"statements",2:"payments"},z=(0,l.withCardStateProvider)(()=>{let i=(0,l.useCardState)(),{selectedTab:e,handleTabChange:t}=(0,g.x)(y);return(0,a.tZ)(d.Col,{elementDescriptor:d.descriptors.page,sx:i=>({gap:i.space.$8,color:i.colors.$colorText}),children:(0,a.BX)(d.Col,{elementDescriptor:d.descriptors.profilePage,elementId:d.descriptors.profilePage.setId("billing"),gap:4,children:[(0,a.tZ)(o.h.Root,{children:(0,a.tZ)(o.h.Title,{localizationKey:(0,d.localizationKeys)("organizationProfile.billingPage.title"),textVariant:"h2"})}),(0,a.tZ)(n.Z.Alert,{children:i.error}),(0,a.BX)(s.mQ,{value:e,onChange:t,children:[(0,a.BX)(s.dr,{sx:i=>({gap:i.space.$6}),children:[(0,a.tZ)(s.OK,{localizationKey:(0,d.localizationKeys)("organizationProfile.billingPage.start.headerTitle__subscriptions")}),(0,a.tZ)(s.OK,{localizationKey:(0,d.localizationKeys)("organizationProfile.billingPage.start.headerTitle__statements")}),(0,a.tZ)(s.OK,{localizationKey:(0,d.localizationKeys)("organizationProfile.billingPage.start.headerTitle__payments")})]}),(0,a.BX)(s.nP,{children:[(0,a.BX)(s.x4,{sx:{width:"100%",flexDirection:"column"},children:[(0,a.tZ)(b.b,{title:(0,d.localizationKeys)("organizationProfile.billingPage.subscriptionsListSection.title"),arrowButtonText:(0,d.localizationKeys)("organizationProfile.billingPage.subscriptionsListSection.actionLabel__switchPlan"),arrowButtonEmptyText:(0,d.localizationKeys)("organizationProfile.billingPage.subscriptionsListSection.actionLabel__newSubscription")}),(0,a.tZ)(r.Cv,{condition:i=>i({permission:"org:sys_billing:manage"}),children:(0,a.tZ)(p.Hw,{})})]}),(0,a.tZ)(s.x4,{sx:{width:"100%"},children:(0,a.tZ)(u.f,{})}),(0,a.tZ)(s.x4,{sx:{width:"100%"},children:(0,a.tZ)(h.B,{})})]})]})]})})}),m=()=>(0,a.tZ)(c.SubscriberTypeContext.Provider,{value:"org",children:(0,a.tZ)(z,{})})},1800:function(i,e,t){t.d(e,{b:()=>d});var a=t(9109),n=t(9655),l=t(8969),o=t(1576),s=t(9541),r=t(4174),c=t(4676);function d({title:i,arrowButtonText:e,arrowButtonEmptyText:t}){let{handleSelectPlan:d,captionForSubscription:g,canManageSubscription:h}=(0,o.usePlansContext)(),p=(0,o.useSubscriberTypeLocalizationRoot)(),u=(0,o.useSubscriberTypeContext)(),{data:b}=(0,o.useSubscriptions)(),y=(0,l.N2)(i=>i({permission:"org:sys_billing:manage"})||"user"===u),{navigate:z}=(0,c.useRouter)(),m=(i,e)=>{d({mode:"modal",plan:i.plan,planPeriod:i.planPeriod,event:e})},x=b.sort((i,e)=>"active"===i.status&&"active"!==e.status?-1:("active"===e.status&&i.status,1));return(0,a.BX)(n.zd.Root,{id:"subscriptionsList",title:i,centered:!1,sx:i=>({borderTop:"none",paddingTop:i.space.$1}),children:[b.length>0&&(0,a.BX)(s.Table,{tableHeadVisuallyHidden:!0,children:[(0,a.tZ)(s.Thead,{children:(0,a.BX)(s.Tr,{children:[(0,a.tZ)(s.Th,{localizationKey:(0,s.localizationKeys)(`${p}.billingPage.subscriptionsListSection.tableHeader__plan`)}),(0,a.tZ)(s.Th,{localizationKey:(0,s.localizationKeys)(`${p}.billingPage.subscriptionsListSection.tableHeader__startDate`)}),(0,a.tZ)(s.Th,{localizationKey:(0,s.localizationKeys)(`${p}.billingPage.subscriptionsListSection.tableHeader__edit`)})]})}),(0,a.tZ)(s.Tbody,{children:x.map(i=>(0,a.BX)(s.Tr,{children:[(0,a.tZ)(s.Td,{children:(0,a.BX)(s.Col,{gap:1,children:[(0,a.BX)(s.Flex,{align:"center",gap:1,children:[(0,a.tZ)(s.Icon,{icon:r.ou,sx:i=>({width:i.sizes.$4,height:i.sizes.$4,opacity:i.opacity.$inactive})}),(0,a.tZ)(s.Text,{variant:"subtitle",sx:i=>({marginRight:i.sizes.$1}),children:i.plan.name}),x.length>1||i.canceledAt?(0,a.tZ)(s.Badge,{colorScheme:"active"===i.status?"secondary":"primary",localizationKey:"active"===i.status?(0,s.localizationKeys)("badge__activePlan"):(0,s.localizationKeys)("badge__upcomingPlan")}):null]}),(!i.plan.isDefault||"upcoming"===i.status)&&(0,a.tZ)(s.Text,{variant:"caption",colorScheme:"secondary",localizationKey:g(i)})]})}),(0,a.tZ)(s.Td,{sx:i=>({textAlign:"right"}),children:(0,a.BX)(s.Text,{variant:"subtitle",children:[i.plan.currencySymbol,"annual"===i.planPeriod?i.plan.annualAmountFormatted:i.plan.amountFormatted,(i.plan.amount>0||i.plan.annualAmount>0)&&(0,a.tZ)(s.Span,{sx:i=>({color:i.colors.$colorTextSecondary,textTransform:"lowercase",":before":{content:'"/"',marginInline:i.space.$1}}),localizationKey:"annual"===i.planPeriod?(0,s.localizationKeys)("commerce.year"):(0,s.localizationKeys)("commerce.month")})]})}),(0,a.tZ)(s.Td,{sx:i=>({textAlign:"right"}),children:h({subscription:i})&&i.id&&!i.plan.isDefault&&(0,a.tZ)(s.Button,{"aria-label":"Manage subscription",onClick:e=>m(i,e),variant:"bordered",colorScheme:"secondary",isDisabled:!y,sx:i=>({width:i.sizes.$6,height:i.sizes.$6}),children:(0,a.tZ)(s.Icon,{icon:r.tc,sx:i=>({width:i.sizes.$4,height:i.sizes.$4,opacity:i.opacity.$inactive})})})})]},i.id))})]}),(0,a.tZ)(n.zd.ArrowButton,{id:"subscriptionsList",textLocalizationKey:b.length>0?e:t,sx:[i=>({justifyContent:"start",height:i.sizes.$8})],leftIcon:b.length>0?r.Ic:r.v3,leftIconSx:i=>({width:i.sizes.$4,height:i.sizes.$4}),onClick:()=>void z("plans")})]})}}}]);