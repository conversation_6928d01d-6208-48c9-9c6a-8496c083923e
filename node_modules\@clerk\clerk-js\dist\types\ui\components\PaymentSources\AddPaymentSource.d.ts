import type { CommerceCheckoutResource } from '@clerk/types';
import type { SetupIntent } from '@stripe/stripe-js';
import type { PropsWithChildren } from 'react';
import type { LocalizationKey } from '../../localization';
type AddPaymentSourceProps = {
    onSuccess: (context: {
        stripeSetupIntent?: SetupIntent;
    }) => Promise<void>;
    checkout?: CommerceCheckoutResource;
    cancelAction?: () => void;
};
declare const Root: (props: PropsWithChildren<AddPaymentSourceProps>) => import("@emotion/react/jsx-runtime").JSX.Element;
declare const FormHeader: ({ text }: {
    text: LocalizationKey;
}) => null;
declare const FormSubtitle: ({ text }: {
    text: LocalizationKey;
}) => null;
declare const FormButton: ({ text }: {
    text: LocalizationKey;
}) => null;
export { Root, FormHeader, FormSubtitle, FormButton };
