{"version": 3, "sources": ["../src/th-TH.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const thTH: LocalizationResource = {\n  locale: 'th-TH',\n  backButton: 'กลับ',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'ค่าเริ่มต้น',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'อุปกรณ์ปลอมตัวอื่น',\n  badge__primary: 'หลัก',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'ต้องการการดำเนินการ',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'อุปกรณ์นี้',\n  badge__unverified: 'ยังไม่ได้ตรวจสอบ',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'อุปกรณ์ผู้ใช้',\n  badge__you: 'คุณ',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'สร้างองค์กร',\n    invitePage: {\n      formButtonReset: 'ข้าม',\n    },\n    title: 'สร้างองค์กร',\n  },\n  dates: {\n    lastDay: \"เมื่อวานนี้ เวลา {{ date | timeString('th-TH') }} น.\",\n    next6Days: \"{{ date | weekday('th-TH','long') }} เวลา {{ date | timeString('th-TH') }} น.\",\n    nextDay: \"พรุ่งนี้ เวลา {{ date | timeString('th-TH') }}\",\n    numeric: \"{{ date | numeric('th-TH') }}\",\n    previous6Days: \"{{ date | weekday('th-TH','long') }}ที่ผ่านมา เวลา {{ date | timeString('th-TH') }} น.\",\n    sameDay: \"วันนี้ เวลา {{ date | timeString('th-TH') }} น.\",\n  },\n  dividerText: 'หรือ',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'ใช้วิธีอื่น',\n  footerPageLink__help: 'ช่วยเหลือ',\n  footerPageLink__privacy: 'ความเป็นส่วนตัว',\n  footerPageLink__terms: 'ข้อกำหนด',\n  formButtonPrimary: 'ดำเนินการต่อ',\n  formButtonPrimary__verify: 'ตรวจสอบ',\n  formFieldAction__forgotPassword: 'ลืมรหัสผ่าน?',\n  formFieldError__matchingPasswords: 'รหัสผ่านตรงกัน',\n  formFieldError__notMatchingPasswords: 'รหัสผ่านไม่ตรงกัน',\n  formFieldError__verificationLinkExpired: 'ลิงก์การตรวจสอบหมดอายุแล้ว โปรดขอลิงก์ใหม่',\n  formFieldHintText__optional: 'ไม่จำเป็น',\n  formFieldHintText__slug: 'Slug เป็น ID ที่อ่านได้และต้องไม่ซ้ำกัน มักใช้ใน URLs',\n  formFieldInputPlaceholder__backupCode: 'ใส่รหัสสำรอง',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'ลบบัญชี',\n  formFieldInputPlaceholder__emailAddress: 'ใส่ที่อยู่อีเมลของคุณ',\n  formFieldInputPlaceholder__emailAddress_username: 'ใส่อีเมลหรือชื่อผู้ใช้',\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: 'ชื่อ',\n  formFieldInputPlaceholder__lastName: 'นามสกุล',\n  formFieldInputPlaceholder__organizationDomain: 'example.com',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: '<EMAIL>',\n  formFieldInputPlaceholder__organizationName: 'ชื่อองค์กร',\n  formFieldInputPlaceholder__organizationSlug: 'my-org',\n  formFieldInputPlaceholder__password: 'ใส่รหัสผ่านของคุณ',\n  formFieldInputPlaceholder__phoneNumber: 'ใส่หมายเลขโทรศัพท์ของคุณ',\n  formFieldInputPlaceholder__username: 'ใส่ชื่อผู้ใช้ของคุณ',\n  formFieldLabel__automaticInvitations: 'เปิดใช้งานคำเชิญอัตโนมัติสำหรับโดเมนนี้',\n  formFieldLabel__backupCode: 'รหัสสำรอง',\n  formFieldLabel__confirmDeletion: 'การยืนยัน',\n  formFieldLabel__confirmPassword: 'ยืนยันรหัสผ่าน',\n  formFieldLabel__currentPassword: 'รหัสผ่านปัจจุบัน',\n  formFieldLabel__emailAddress: 'ที่อยู่อีเมล',\n  formFieldLabel__emailAddress_username: 'ที่อยู่อีเมลหรือชื่อผู้ใช้',\n  formFieldLabel__emailAddresses: 'ที่อยู่อีเมล',\n  formFieldLabel__firstName: 'ชื่อ',\n  formFieldLabel__lastName: 'นามสกุล',\n  formFieldLabel__newPassword: 'รหัสผ่านใหม่',\n  formFieldLabel__organizationDomain: 'โดเมน',\n  formFieldLabel__organizationDomainDeletePending: 'ลบคำเชิญและข้อเสนอที่รอดำเนินการ',\n  formFieldLabel__organizationDomainEmailAddress: 'ที่อยู่อีเมลสำหรับการตรวจสอบ',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'กรอกที่อยู่อีเมลภายใต้โดเมนนี้เพื่อรับรหัสและตรวจสอบโดเมน',\n  formFieldLabel__organizationName: 'ชื่อ',\n  formFieldLabel__organizationSlug: 'Slug',\n  formFieldLabel__passkeyName: 'ชื่อของพาสคีย์',\n  formFieldLabel__password: 'รหัสผ่าน',\n  formFieldLabel__phoneNumber: 'หมายเลขโทรศัพท์',\n  formFieldLabel__role: 'บทบาท',\n  formFieldLabel__signOutOfOtherSessions: 'ออกจากระบบจากทุกอุปกรณ์',\n  formFieldLabel__username: 'ชื่อผู้ใช้',\n  impersonationFab: {\n    action__signOut: 'ออกจากระบบ',\n    title: 'เข้าสู่ระบบในฐานะ {{identifier}}',\n  },\n  maintenanceMode: 'ขณะนี้เรากำลังปรับปรุงระบบ แต่ไม่ต้องกังวลไป เพราะไม่น่าจะใช้เวลานานเกินกว่าสองสามนาที',\n  membershipRole__admin: 'ผู้ดูแลระบบ',\n  membershipRole__basicMember: 'สมาชิก',\n  membershipRole__guestMember: 'ผู้เยี่ยมชม',\n  organizationList: {\n    action__createOrganization: 'สร้างองค์กร',\n    action__invitationAccept: 'เข้าร่วม',\n    action__suggestionsAccept: 'ขอเข้าร่วม',\n    createOrganization: 'สร้างองค์กร',\n    invitationAcceptedLabel: 'ได้เข้าร่วมแล้ว',\n    subtitle: 'เพื่อดำเนินการต่อไปยัง {{applicationName}}',\n    suggestionsAcceptedLabel: 'รอการอนุมัติ',\n    title: 'เลือกบัญชี',\n    titleWithoutPersonal: 'เลือกองค์กร',\n  },\n  organizationProfile: {\n    badge__automaticInvitation: 'คำเชิญอัตโนมัติ',\n    badge__automaticSuggestion: 'ข้อเสนอแนะอัตโนมัติ',\n    badge__manualInvitation: 'ไม่มีการลงทะเบียนอัตโนมัติ',\n    badge__unverified: 'ยังไม่ได้ยืนยัน',\n    billingPage: {\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'เพิ่มโดเมนเพื่อยืนยัน ผู้ใช้ที่มีที่อยู่อีเมลในโดเมนนี้สามารถเข้าร่วมองค์กรโดยอัตโนมัติหรือขอเข้าร่วมได้',\n      title: 'เพิ่มโดเมน',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'ไม่สามารถส่งคำเชิญได้ มีคำเชิญที่กำลังรอดำเนินการสำหรับที่อยู่อีเมลต่อไปนี้: {{email_addresses}}',\n      formButtonPrimary__continue: 'ส่งคำเชิญ',\n      selectDropdown__role: 'เลือกบทบาท',\n      subtitle: 'ใส่หรือวางที่อยู่อีเมลหนึ่งหรือมากกว่า แยกด้วยช่องว่างหรือเครื่องหมายจุลภาค',\n      successMessage: 'คำเชิญถูกส่งเรียบร้อยแล้ว',\n      title: 'เชิญสมาชิกใหม่',\n    },\n    membersPage: {\n      action__invite: 'เชิญ',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'ลบสมาชิก',\n        tableHeader__actions: 'การดำเนินการ',\n        tableHeader__joined: 'เข้าร่วม',\n        tableHeader__role: 'บทบาท',\n        tableHeader__user: 'ผู้ใช้',\n      },\n      detailsTitle__emptyRow: 'ไม่มีสมาชิกที่แสดง',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'เชิญผู้ใช้โดยเชื่อมต่อโดเมนอีเมลกับองค์กรของคุณ ทุกคนที่สมัครสมาชิกด้วยโดเมนอีเมลที่ตรงกันจะสามารถเข้าร่วมองค์กรได้ทุกเมื่อ',\n          headerTitle: 'คำเชิญอัตโนมัติ',\n          primaryButton: 'จัดการโดเมนที่ได้รับการยืนยัน',\n        },\n        table__emptyRow: 'ไม่มีคำเชิญที่แสดง',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'ยกเลิกคำเชิญ',\n        tableHeader__invited: 'ได้รับเชิญ',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'ผู้ใช้ที่สมัครสมาชิกด้วยโดเมนอีเมลที่ตรงกัน จะสามารถเห็นข้อเสนอแนะในการขอเข้าร่วมองค์กรของคุณ',\n          headerTitle: 'ข้อเสนอแนะอัตโนมัติ',\n          primaryButton: 'จัดการโดเมนที่ได้รับการยืนยัน',\n        },\n        menuAction__approve: 'อนุมัติ',\n        menuAction__reject: 'ปฏิเสธ',\n        tableHeader__requested: 'ขอเข้าถึง',\n        table__emptyRow: 'ไม่มีคำขอที่แสดง',\n      },\n      start: {\n        headerTitle__invitations: 'คำเชิญ',\n        headerTitle__members: 'สมาชิก',\n        headerTitle__requests: 'คำขอ',\n      },\n    },\n    navbar: {\n      billing: undefined,\n      description: 'จัดการองค์กรของคุณ',\n      general: 'ทั่วไป',\n      members: 'สมาชิก',\n      title: 'องค์กร',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'พิมพ์ \"{{organizationName}}\" ด้านล่างเพื่อดำเนินการต่อ',\n          messageLine1: 'คุณแน่ใจหรือไม่ว่าต้องการลบองค์กรนี้',\n          messageLine2: 'การกระทำนี้ถาวรและไม่สามารถย้อนกลับได้',\n          successMessage: 'คุณได้ลบองค์กรแล้ว',\n          title: 'ลบองค์กร',\n        },\n        leaveOrganization: {\n          actionDescription: 'พิมพ์ \"{{organizationName}}\" ด้านล่างเพื่อดำเนินการต่อ',\n          messageLine1: 'คุณแน่ใจหรือไม่ว่าต้องการออกจากองค์กรนี้ คุณจะสูญเสียการเข้าถึงองค์กรและแอปพลิเคชันของมัน',\n          messageLine2: 'การกระทำนี้ถาวรและไม่สามารถย้อนกลับได้',\n          successMessage: 'คุณได้ออกจากองค์กรแล้ว',\n          title: 'ออกจากองค์กร',\n        },\n        title: 'คำเตือน',\n      },\n      domainSection: {\n        menuAction__manage: 'จัดการ',\n        menuAction__remove: 'ลบ',\n        menuAction__verify: 'ตรวจสอบ',\n        primaryButton: 'เพิ่มโดเมน',\n        subtitle: 'อนุญาตให้ผู้ใช้เข้าร่วมองค์กรโดยอัตโนมัติหรือขอเข้าร่วมตามโดเมนอีเมลที่ได้รับการตรวจสอบแล้ว',\n        title: 'โดเมนที่ได้รับการตรวจสอบ',\n      },\n      successMessage: 'องค์กรได้รับการอัปเดตแล้ว',\n      title: 'อัปเดตโปรไฟล์',\n    },\n    removeDomainPage: {\n      messageLine1: 'โดเมนอีเมล {{domain}} จะถูกลบออก',\n      messageLine2: 'ผู้ใช้จะไม่สามารถเข้าร่วมองค์กรโดยอัตโนมัติหลังจากนี้',\n      successMessage: '{{domain}} ได้ถูกลบออกแล้ว',\n      title: 'ลบโดเมน',\n    },\n    start: {\n      headerTitle__general: 'ทั่วไป',\n      headerTitle__members: 'สมาชิก',\n      profileSection: {\n        primaryButton: 'อัปเดตโปรไฟล์',\n        title: 'โปรไฟล์องค์กร',\n        uploadAction__title: 'โลโก้',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'การลบโดเมนนี้จะส่งผลกระทบต่อผู้ใช้ที่ได้รับเชิญ',\n        removeDomainActionLabel__remove: 'ลบโดเมน',\n        removeDomainSubtitle: 'ลบโดเมนนี้ออกจากโดเมนที่ได้รับการตรวจสอบของคุณ',\n        removeDomainTitle: 'ลบโดเมน',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'ผู้ใช้จะได้รับเชิญโดยอัตโนมัติเมื่อสมัครสมาชิกและสามารถเข้าร่วมได้ทุกเมื่อ',\n        automaticInvitationOption__label: 'เชิญอัตโนมัติ',\n        automaticSuggestionOption__description:\n          'ผู้ใช้จะได้รับข้อเสนอแนะให้ขอเข้าร่วม แต่ต้องได้รับการอนุมัติจากผู้ดูแลระบบก่อนที่จะสามารถเข้าร่วมองค์กรได้',\n        automaticSuggestionOption__label: 'ข้อเสนอแนะอัตโนมัติ',\n        calloutInfoLabel: 'การเปลี่ยนโหมดการเข้าร่วมจะส่งผลเฉพาะผู้ใช้ใหม่เท่านั้น',\n        calloutInvitationCountLabel: 'คำเชิญที่รอดำเนินการส่งไปยังผู้ใช้: {{count}}',\n        calloutSuggestionCountLabel: 'ข้อเสนอแนะที่รอดำเนินการส่งไปยังผู้ใช้: {{count}}',\n        manualInvitationOption__description: 'ผู้ใช้สามารถได้รับเชิญเข้าร่วมองค์กรได้โดยต้องผ่านการเชิญเท่านั้น',\n        manualInvitationOption__label: 'ไม่มีการเข้าร่วมอัตโนมัติ',\n        subtitle: 'เลือกวิธีที่ผู้ใช้จากโดเมนนี้สามารถเข้าร่วมองค์กรได้',\n      },\n      start: {\n        headerTitle__danger: 'อันตราย',\n        headerTitle__enrollment: 'ตัวเลือกการเข้าร่วม',\n      },\n      subtitle: 'โดเมน {{domain}} ได้รับการตรวจสอบแล้ว ดำเนินการต่อโดยการเลือกโหมดการเข้าร่วม',\n      title: 'อัปเดต {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'ป้อนรหัสการตรวจสอบที่ส่งไปยังที่อยู่อีเมลของคุณ',\n      formTitle: 'รหัสการตรวจสอบ',\n      resendButton: 'ไม่ได้รับรหัสใช่หรือไม่ ส่งรหัสใหม่อีกครั้ง',\n      subtitle: 'โดเมน {{domainName}} ต้องได้รับการตรวจสอบผ่านทางอีเมล',\n      subtitleVerificationCodeScreen: 'รหัสการตรวจสอบถูกส่งไปยัง {{emailAddress}} ป้อนรหัสเพื่อดำเนินการต่อ',\n      title: 'ตรวจสอบโดเมน',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'สร้างองค์กร',\n    action__invitationAccept: 'เข้าร่วม',\n    action__manageOrganization: 'จัดการ',\n    action__suggestionsAccept: 'ขอเข้าร่วม',\n    notSelected: 'ไม่มีองค์กรที่เลือก',\n    personalWorkspace: 'บัญชีส่วนบุคคล',\n    suggestionsAcceptedLabel: 'รอการอนุมัติ',\n  },\n  paginationButton__next: 'ถัดไป',\n  paginationButton__previous: 'ก่อนหน้า',\n  paginationRowText__displaying: 'แสดง',\n  paginationRowText__of: 'จาก',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'ขอความช่วยเหลือ',\n      actionText: 'ไม่มีวิธีใดที่กล่าวมาหรือ?',\n      blockButton__backupCode: 'ใช้รหัสสำรอง',\n      blockButton__emailCode: 'ส่งรหัสไปที่อีเมล {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'ดำเนินการต่อด้วยรหัสผ่านของคุณ',\n      blockButton__phoneCode: 'ส่งรหัส SMS ไปยัง {{identifier}}',\n      blockButton__totp: 'ใช้แอปยืนยันตัวตนของคุณ',\n      getHelp: {\n        blockButton__emailSupport: 'อีเมลฝ่ายสนับสนุน',\n        content: 'หากคุณมีปัญหาในการยืนยันบัญชีของคุณ ส่งอีเมลถึงเราและเราจะช่วยคุณเรียกคืนการเข้าถึงโดยเร็วที่สุด',\n        title: 'ขอความช่วยเหลือ',\n      },\n      subtitle: 'มีปัญหาหรือ? คุณสามารถใช้วิธีใดวิธีหนึ่งนี้สำหรับการยืนยัน',\n      title: 'ใช้วิธีอื่น',\n    },\n    backupCodeMfa: {\n      subtitle: 'ใส่รหัสสำรองที่คุณได้รับเมื่อตั้งค่าการยืนยันสองขั้นตอน',\n      title: 'ใส่รหัสสำรอง',\n    },\n    emailCode: {\n      formTitle: 'รหัสยืนยัน',\n      resendButton: 'ไม่ได้รับรหัสใช่หรือไม่? ส่งใหม่',\n      subtitle: 'ใส่รหัสที่ส่งไปยังอีเมลของคุณเพื่อดำเนินการต่อ',\n      title: 'ต้องการการยืนยัน',\n    },\n    noAvailableMethods: {\n      message: 'ไม่สามารถดำเนินการยืนยันได้ ไม่มีปัจจัยการยืนยันตัวตนที่เหมาะสมที่ถูกกำหนดค่า',\n      subtitle: 'เกิดข้อผิดพลาด',\n      title: 'ไม่สามารถยืนยันบัญชีของคุณได้',\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'ใช้วิธีอื่น',\n      subtitle: 'ใส่รหัสผ่านของคุณเพื่อดำเนินการต่อ',\n      title: 'ต้องการการยืนยัน',\n    },\n    phoneCode: {\n      formTitle: 'รหัสยืนยัน',\n      resendButton: 'ไม่ได้รับรหัสใช่หรือไม่? ส่งใหม่',\n      subtitle: 'ใส่รหัสที่ส่งไปยังโทรศัพท์ของคุณเพื่อดำเนินการต่อ',\n      title: 'ต้องการการยืนยัน',\n    },\n    phoneCodeMfa: {\n      formTitle: 'รหัสยืนยัน',\n      resendButton: 'ไม่ได้รับรหัสใช่หรือไม่? ส่งใหม่',\n      subtitle: 'ใส่รหัสที่ส่งไปยังโทรศัพท์ของคุณเพื่อดำเนินการต่อ',\n      title: 'ต้องการการยืนยัน',\n    },\n    totpMfa: {\n      formTitle: 'รหัสยืนยัน',\n      subtitle: 'ใส่รหัสที่สร้างโดยแอปยืนยันตัวตนของคุณเพื่อดำเนินการต่อ',\n      title: 'ต้องการการยืนยัน',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'เพิ่มบัญชี',\n      action__signOutAll: 'ออกจากบัญชีทั้งหมด',\n      subtitle: 'เลือกบัญชีที่คุณต้องการดำเนินการต่อ',\n      title: 'เลือกบัญชี',\n    },\n    alternativeMethods: {\n      actionLink: 'ขอรับความช่วยเหลือ',\n      actionText: 'ไม่มีวิธีใดที่กล่าวมาหรือ?',\n      blockButton__backupCode: 'ใช้รหัสสำรอง',\n      blockButton__emailCode: 'ส่งรหัสไปที่อีเมล {{identifier}}',\n      blockButton__emailLink: 'ส่งลิงก์ไปที่อีเมล {{identifier}}',\n      blockButton__passkey: 'ลงชื่อเข้าใช้ด้วยพาสคีย์ของคุณ',\n      blockButton__password: 'เข้าสู่ระบบด้วยรหัสผ่านของคุณ',\n      blockButton__phoneCode: 'ส่งรหัส SMS ไปยัง {{identifier}}',\n      blockButton__totp: 'ใช้แอปยืนยันตัวตน',\n      getHelp: {\n        blockButton__emailSupport: 'สนับสนุนทางอีเมล',\n        content:\n          'หากคุณพบปัญหาในการเข้าสู่ระบบบัญชีของคุณ โปรดอีเมลถึงเราและเราจะช่วยคุณเรียกคืนการเข้าถึงโดยเร็วที่สุด',\n        title: 'ขอรับความช่วยเหลือ',\n      },\n      subtitle: 'มีปัญหาหรือ? คุณสามารถใช้วิธีใดวิธีหนึ่งนี้เพื่อเข้าสู่ระบบได้',\n      title: 'ใช้วิธีอื่น',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'รหัสสำรองของคุณคือรหัสที่คุณได้รับเมื่อตั้งค่าการยืนยันสองขั้นตอน',\n      title: 'ใส่รหัสสำรอง',\n    },\n    emailCode: {\n      formTitle: 'รหัสการตรวจสอบ',\n      resendButton: 'ไม่ได้รับรหัสใช่หรือไม่ ส่งรหัสใหม่อีกครั้ง',\n      subtitle: 'เพื่อดำเนินการต่อไปยัง {{applicationName}}',\n      title: 'ตรวจสอบอีเมลของคุณ',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'หากต้องการดำเนินการต่อ ให้เปิดลิงก์การยืนยันบนอุปกรณ์และเบราว์เซอร์ที่คุณใช้ในการเริ่มลงชื่อเข้าใช้',\n        title: 'ลิงก์ตรวจสอบไม่ถูกต้องสำหรับอุปกรณ์นี้',\n      },\n      expired: {\n        subtitle: 'กลับไปที่แท็บเดิมเพื่อดำเนินการต่อ',\n        title: 'ลิงก์การตรวจสอบนี้หมดอายุ',\n      },\n      failed: {\n        subtitle: 'กลับไปที่แท็บเดิมเพื่อดำเนินการต่อ',\n        title: 'ลิงก์การตรวจสอบนี้ไม่ถูกต้อง',\n      },\n      formSubtitle: 'ใช้ลิงก์การตรวจสอบที่ส่งไปยังอีเมลของคุณ',\n      formTitle: 'ลิงก์การตรวจสอบ',\n      loading: {\n        subtitle: 'คุณจะถูกเปลี่ยนเส้นทางเร็ว ๆ นี้',\n        title: 'กำลังเข้าสู่ระบบ...',\n      },\n      resendButton: 'ไม่ได้รับลิงก์ใช่หรือไม่ ส่งลิงก์ใหม่อีกครั้ง',\n      subtitle: 'เพื่อดำเนินการต่อไปยัง {{applicationName}}',\n      title: 'ตรวจสอบอีเมลของคุณ',\n      unusedTab: {\n        title: 'คุณอาจปิดแท็บนี้ได้',\n      },\n      verified: {\n        subtitle: 'คุณจะถูกเปลี่ยนเส้นทางเร็ว ๆ นี้',\n        title: 'เข้าสู่ระบบสำเร็จ',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'กลับไปที่แท็บเดิมเพื่อดำเนินการต่อ',\n        subtitleNewTab: 'กลับไปที่แท็บที่เปิดใหม่เพื่อดำเนินการต่อ',\n        titleNewTab: 'เข้าสู่ระบบในแท็บอื่น',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'รหัสรีเซ็ตรหัสผ่าน',\n      resendButton: 'ไม่ได้รับรหัสใช่หรือไม่ ส่งรหัสใหม่อีกครั้ง',\n      subtitle: 'เพื่อรีเซ็ตรหัสผ่านของคุณ',\n      subtitle_email: 'ขั้นแรก ใส่รหัสที่ส่งไปยัง ID อีเมลของคุณ',\n      subtitle_phone: 'ขั้นแรก ใส่รหัสที่ส่งไปยังโทรศัพท์ของคุณ',\n      title: 'รีเซ็ตรหัสผ่าน',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'รีเซ็ตรหัสผ่านของคุณ',\n      label__alternativeMethods: 'หรือ เข้าสู่ระบบด้วยวิธีอื่น',\n      title: 'ลืมรหัสผ่าน?',\n    },\n    noAvailableMethods: {\n      message: 'ไม่สามารถดำเนินการเข้าสู่ระบบได้ ไม่มีปัจจัยการตรวจสอบที่ใช้งานได้',\n      subtitle: 'เกิดข้อผิดพลาด',\n      title: 'ไม่สามารถเข้าสู่ระบบได้',\n    },\n    passkey: {\n      subtitle: 'การใช้พาสคีย์ของคุณเพื่อยืนยันว่าเป็นคุณ อุปกรณ์ของคุณอาจขอลายนิ้วมือ ใบหน้า หรือการล็อกหน้าจอ',\n      title: 'ใช้พาสคีย์ของคุณ',\n    },\n    password: {\n      actionLink: 'ใช้วิธีอื่น',\n      subtitle: 'ใส่รหัสผ่านที่เชื่อมโยงกับบัญชีของคุณ',\n      title: 'ใส่รหัสผ่านของคุณ',\n    },\n    passwordPwned: {\n      title: 'รหัสผ่านเคยถูกโจรกรรม',\n    },\n    phoneCode: {\n      formTitle: 'รหัสการตรวจสอบ',\n      resendButton: 'ไม่ได้รับรหัสใช่หรือไม่ ส่งรหัสใหม่อีกครั้ง',\n      subtitle: 'เพื่อดำเนินการต่อไปยัง {{applicationName}}',\n      title: 'ตรวจสอบโทรศัพท์ของคุณ',\n    },\n    phoneCodeMfa: {\n      formTitle: 'รหัสการตรวจสอบ',\n      resendButton: 'ไม่ได้รับรหัสใช่หรือไม่ ส่งรหัสใหม่อีกครั้ง',\n      subtitle: 'เพื่อดำเนินการต่อ โปรดใส่รหัสการตรวจสอบที่ส่งไปยังโทรศัพท์ของคุณ',\n      title: 'ตรวจสอบโทรศัพท์ของคุณ',\n    },\n    resetPassword: {\n      formButtonPrimary: 'รีเซ็ตรหัสผ่าน',\n      requiredMessage: 'มีบัญชีอยู่แล้วที่มีอีเมลที่ยังไม่ได้ยืนยัน โปรดรีเซ็ตรหัสผ่านของคุณเพื่อความปลอดภัย',\n      successMessage: 'รหัสผ่านของคุณถูกรีเซ็ตเรียบร้อยแล้ว กำลังเข้าสู่ระบบ โปรดรอสักครู่',\n      title: 'ตั้งรหัสผ่านใหม่',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'เราต้องตรวจสอบตัวตนของคุณก่อนที่จะรีเซ็ตรหัสผ่าน',\n    },\n    start: {\n      actionLink: 'สมัครสมาชิก',\n      actionLink__join_waitlist: 'เข้าร่วมรายชื่อผู้รอ',\n      actionLink__use_email: 'ใช้อีเมล',\n      actionLink__use_email_username: 'ใช้อีเมลหรือชื่อผู้ใช้',\n      actionLink__use_passkey: 'ใช้พาสคีย์แทน',\n      actionLink__use_phone: 'ใช้โทรศัพท์',\n      actionLink__use_username: 'ใช้ชื่อผู้ใช้',\n      actionText: 'ไม่มีบัญชีหรือ?',\n      actionText__join_waitlist: 'ต้องการเข้าถึงก่อนใช่หรือไม่?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'ยินดีต้อนรับกลับ! โปรดเข้าสู่ระบบเพื่อดำเนินการต่อ',\n      subtitleCombined: undefined,\n      title: 'เข้าสู่ระบบ {{applicationName}}',\n      titleCombined: 'ดำเนินการต่อไปยัง {{applicationName}}',\n    },\n    totpMfa: {\n      formTitle: 'รหัสการตรวจสอบ',\n      subtitle: 'เพื่อดำเนินการต่อ โปรดใส่รหัสการตรวจสอบที่สร้างโดยแอปยืนยันตัวตนของคุณ',\n      title: 'การตรวจสอบสองขั้นตอน',\n    },\n  },\n  signInEnterPasswordTitle: 'ใส่รหัสผ่านของคุณ',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'เข้าสู่ระบบ',\n      actionText: 'มีบัญชีอยู่แล้วใช่หรือไม่?',\n      subtitle: 'โปรดกรอกข้อมูลที่เหลือเพื่อดำเนินการต่อ',\n      title: 'กรอกข้อมูลที่ขาดหาย',\n    },\n    emailCode: {\n      formSubtitle: 'ใส่รหัสยืนยันที่ส่งไปยังที่อยู่อีเมลของคุณ',\n      formTitle: 'รหัสยืนยัน',\n      resendButton: 'ไม่ได้รับรหัส? ส่งใหม่',\n      subtitle: 'ใส่รหัสยืนยันที่ส่งไปยังอีเมลของคุณ',\n      title: 'ยืนยันอีเมลของคุณ',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'หากต้องการดำเนินการต่อ ให้เปิดลิงก์ยืนยันบนอุปกรณ์และเบราว์เซอร์ที่คุณใช้เริ่มต้นการสมัคร',\n        title: 'ลิงก์ตรวจสอบไม่ถูกต้องสำหรับอุปกรณ์นี้',\n      },\n      formSubtitle: 'ใช้ลิงก์ยืนยันที่ส่งไปยังที่อยู่อีเมลของคุณ',\n      formTitle: 'ลิงก์ยืนยัน',\n      loading: {\n        title: 'กำลังสมัครสมาชิก...',\n      },\n      resendButton: 'ไม่ได้รับลิงก์? ส่งใหม่',\n      subtitle: 'เพื่อดำเนินการต่อไปยัง {{applicationName}}',\n      title: 'ยืนยันอีเมลของคุณ',\n      verified: {\n        title: 'สมัครสมาชิกสำเร็จ',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'กลับไปยังแท็บที่เปิดใหม่เพื่อดำเนินการต่อ',\n        subtitleNewTab: 'กลับไปยังแท็บก่อนหน้าเพื่อดำเนินการต่อ',\n        title: 'ยืนยันอีเมลสำเร็จ',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: 'ฉันยอมรับ {{ privacyPolicyLink || link(\"นโยบายความเป็นส่วนตัว\") }}',\n        label__onlyTermsOfService: 'ฉันยอมรับ {{ termsOfServiceLink || link(\"ข้อกำหนดการใช้งาน\") }}',\n        label__termsOfServiceAndPrivacyPolicy:\n          'ฉันยอมรับ {{ termsOfServiceLink || link(\"ข้อกำหนดการใช้งาน\") }} และ {{ privacyPolicyLink || link(\"นโยบายความเป็นส่วนตัว\") }}',\n      },\n      continue: {\n        subtitle: 'โปรดอ่านและยอมรับข้อกำหนดเพื่อดำเนินการต่อ',\n        title: 'การยินยอมทางกฎหมาย',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'ใส่รหัสยืนยันที่ส่งไปยังหมายเลขโทรศัพท์ของคุณ',\n      formTitle: 'รหัสยืนยัน',\n      resendButton: 'ไม่ได้รับรหัส? ส่งใหม่',\n      subtitle: 'ใส่รหัสยืนยันที่ส่งไปยังโทรศัพท์ของคุณ',\n      title: 'ยืนยันโทรศัพท์ของคุณ',\n    },\n    restrictedAccess: {\n      actionLink: 'เข้าสู่ระบบ',\n      actionText: 'มีบัญชีอยู่แล้วใช่หรือไม่?',\n      blockButton__emailSupport: 'อีเมลฝ่ายสนับสนุน',\n      blockButton__joinWaitlist: 'เข้าร่วมรายชื่อผู้รอ',\n      subtitle: 'การสมัครสมาชิกถูกปิดใช้งานในขณะนี้ หากคุณเชื่อว่าคุณควรมีสิทธิ์เข้าถึง โปรดติดต่อฝ่ายสนับสนุน',\n      subtitleWaitlist:\n        'การสมัครสมาชิกถูกปิดใช้งานในขณะนี้ หากต้องการเป็นคนแรกที่รู้เมื่อเราเปิดตัว เข้าร่วมรายชื่อผู้รอ',\n      title: 'การเข้าถึงถูกจำกัด',\n    },\n    start: {\n      actionLink: 'เข้าสู่ระบบ',\n      actionLink__use_email: 'ใช้อีเมลแทน',\n      actionLink__use_phone: 'ใช้โทรศัพท์แทน',\n      actionText: 'มีบัญชีอยู่แล้วใช่หรือไม่?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'ยินดีต้อนรับ! โปรดกรอกข้อมูลเพื่อเริ่มต้น',\n      subtitleCombined: 'ยินดีต้อนรับ! โปรดกรอกข้อมูลเพื่อเริ่มต้น',\n      title: 'สร้างบัญชีของคุณ',\n      titleCombined: 'สร้างบัญชีของคุณ',\n    },\n  },\n  socialButtonsBlockButton: 'ดำเนินการต่อด้วย {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: '{{email}} เป็นสมาชิกขององค์กรอยู่แล้ว',\n    captcha_invalid:\n      'การสมัครไม่สำเร็จเนื่องจากการตรวจสอบความปลอดภัยไม่ผ่าน โปรดรีเฟรชหน้าเว็บเพื่อลองใหม่หรือติดต่อฝ่ายสนับสนุนเพื่อขอความช่วยเหลือเพิ่มเติม',\n    captcha_unavailable:\n      'การสมัครไม่สำเร็จเนื่องจากการตรวจสอบบอทไม่ผ่าน โปรดรีเฟรชหน้าเว็บเพื่อลองใหม่หรือติดต่อฝ่ายสนับสนุนเพื่อขอความช่วยเหลือเพิ่มเติม',\n    form_code_incorrect: 'รหัสไม่ถูกต้อง',\n    form_identifier_exists__email_address: 'ที่อยู่อีเมลนี้ถูกนำไปใช้แล้ว โปรดลองอันอื่น',\n    form_identifier_exists__phone_number: 'หมายเลขโทรศัพท์นี้ถูกนำไปใช้แล้ว โปรดลองอันอื่น',\n    form_identifier_exists__username: 'ชื่อผู้ใช้นี้ถูกนำไปใช้แล้ว โปรดลองอันอื่น',\n    form_identifier_not_found: 'ไม่พบบัญชีที่มีตัวระบุนี้ โปรดตรวจสอบและลองอีกครั้ง',\n    form_param_format_invalid: 'ค่าที่ป้อนอยู่ในรูปแบบที่ไม่ถูกต้อง โปรดตรวจสอบและแก้ไข',\n    form_param_format_invalid__email_address: 'ที่อยู่อีเมลต้องเป็นที่อยู่อีเมลที่ถูกต้อง',\n    form_param_format_invalid__phone_number: 'หมายเลขโทรศัพท์ต้องอยู่ในรูปแบบสากลที่ถูกต้อง',\n    form_param_max_length_exceeded__first_name: 'ชื่อต้นไม่ควรเกิน 256 ตัวอักษร',\n    form_param_max_length_exceeded__last_name: 'นามสกุลไม่ควรเกิน 256 ตัวอักษร',\n    form_param_max_length_exceeded__name: 'ชื่อไม่ควรเกิน 256 ตัวอักษร',\n    form_param_nil: 'ช่องนี้จำเป็นและไม่สามารถเว้นว่างได้',\n    form_param_value_invalid: 'ค่าที่ป้อนไม่ถูกต้อง โปรดแก้ไขอีกครั้ง',\n    form_password_incorrect: 'รหัสผ่านที่คุณป้อนไม่ถูกต้อง โปรดลองอีกครั้ง',\n    form_password_length_too_short: 'รหัสผ่านของคุณสั้นเกินไป ต้องมีความยาวอย่างน้อย 8 ตัวอักษร',\n    form_password_not_strong_enough: 'รหัสผ่านของคุณไม่เพียงพอต่อความปลอดภัย',\n    form_password_pwned: 'รหัสผ่านนี้ถูกพบว่าเป็นส่วนหนึ่งของข้อมูลที่รั่วไหลและไม่สามารถใช้ได้ โปรดลองรหัสผ่านอื่นแทน',\n    form_password_pwned__sign_in:\n      'รหัสผ่านนี้ถูกพบว่าเป็นส่วนหนึ่งของข้อมูลที่รั่วไหลและไม่สามารถใช้งานได้ โปรดรีเซ็ตรหัสผ่านของคุณ',\n    form_password_size_in_bytes_exceeded:\n      'รหัสผ่านของคุณเกินจำนวนไบต์สูงสุดที่อนุญาต โปรดลดความยาวหรือลบอักขระพิเศษบางตัว',\n    form_password_validation_failed: 'รหัสผ่านไม่ถูกต้อง',\n    form_username_invalid_character:\n      'ชื่อผู้ใช้ของคุณมีอักขระที่ไม่ถูกต้อง โปรดใช้เฉพาะตัวอักษร ตัวเลข และขีดล่างเท่านั้น',\n    form_username_invalid_length: 'ชื่อผู้ใช้ของคุณต้องมีความยาวระหว่าง {{min_length}} ถึง {{max_length}} ตัวอักษร',\n    identification_deletion_failed: 'คุณไม่สามารถลบรูปแบบการยืนยันตัวตนสุดท้ายของคุณได้',\n    not_allowed_access:\n      'ไม่อนุญาตให้ใช้ที่อยู่อีเมลหรือหมายเลขโทรศัพท์ในการลงทะเบียน สาเหตุอาจเกิดจากการใช้เครื่องหมาย \"+\" \"=\" \"#\" หรือ \".\" ในที่อยู่อีเมลของคุณ การใช้โดเมนที่เชื่อมต่อกับบริการอีเมลชั่วคราว หรือการถูกบล็อกโดยชัดเจน หากคุณเชื่อว่านี่คือข้อผิดพลาด โปรดติดต่อฝ่ายสนับสนุน',\n    organization_domain_blocked: 'นี่เป็นโดเมนผู้ให้บริการอีเมลที่ถูกบล็อก โปรดใช้โดเมนอื่น',\n    organization_domain_common: 'นี่เป็นโดเมนผู้ให้บริการอีเมลทั่วไป โปรดใช้โดเมนอื่น',\n    organization_domain_exists_for_enterprise_connection: 'โดเมนนี้ถูกใช้สำหรับ SSO ขององค์กรของคุณแล้ว',\n    organization_membership_quota_exceeded: 'คุณได้ถึงขีดจำกัดการเป็นสมาชิกองค์กรแล้ว รวมถึงคำเชิญที่รอดำเนินการ',\n    organization_minimum_permissions_needed: 'ต้องมีสมาชิกองค์กรอย่างน้อยหนึ่งคนที่มีสิทธิ์ขั้นต่ำที่จำเป็น',\n    passkey_already_exists: 'พาสคีย์ถูกลงทะเบียนกับอุปกรณ์นี้แล้ว',\n    passkey_not_supported: 'อุปกรณ์นี้ไม่รองรับพาสคีย์',\n    passkey_pa_not_supported: 'การลงทะเบียนต้องใช้ระบบยืนยันตัวตนของแพลตฟอร์ม แต่อุปกรณ์ไม่รองรับ',\n    passkey_registration_cancelled: 'การลงทะเบียนพาสคีย์ถูกยกเลิกหรือหมดเวลา',\n    passkey_retrieval_cancelled: 'การยืนยันพาสคีย์ถูกยกเลิกหรือหมดเวลา',\n    passwordComplexity: {\n      maximumLength: 'น้อยกว่า {{length}} ตัวอักษร',\n      minimumLength: '{{length}} ตัวอักษรหรือมากกว่า',\n      requireLowercase: 'ตัวอักษรพิมพ์เล็ก',\n      requireNumbers: 'ตัวเลข',\n      requireSpecialCharacter: 'อักขระพิเศษ',\n      requireUppercase: 'ตัวอักษรพิมพ์ใหญ่',\n      sentencePrefix: 'รหัสผ่านของคุณต้องมี',\n    },\n    phone_number_exists: 'หมายเลขโทรศัพท์นี้ถูกใช้แล้ว โปรดลองหมายเลขอื่น',\n    session_exists: 'คุณเข้าสู่ระบบอยู่แล้ว',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'รหัสผ่านของคุณใช้งานได้ แต่ควรจะแข็งแกร่งกว่านี้ ลองเพิ่มอักขระเพิ่มเติม',\n      goodPassword: 'รหัสผ่านของคุณตรงตามข้อกำหนดที่จำเป็นทั้งหมด',\n      notEnough: 'รหัสผ่านของคุณไม่เพียงพอต่อความปลอดภัย',\n      suggestions: {\n        allUppercase: 'ใช้ตัวพิมพ์ใหญ่บางตัว แต่ไม่ใช่ทั้งหมด',\n        anotherWord: 'เพิ่มคำที่ไม่ค่อยมีคนใช้มากขึ้น',\n        associatedYears: 'หลีกเลี่ยงปีที่เกี่ยวข้องกับคุณ',\n        capitalization: 'ใช้ตัวพิมพ์ใหญ่มากกว่าตัวแรก',\n        dates: 'หลีกเลี่ยงวันที่และปีที่เกี่ยวข้องกับคุณ',\n        l33t: \"หลีกเลี่ยงการใช้ตัวแทนอักขระที่คาดเดาได้ง่าย เช่น '@' แทน 'a'\",\n        longerKeyboardPattern: 'ใช้รูปแบบการกดคีย์บอร์ดที่ยาวขึ้นและเปลี่ยนทิศทางการพิมพ์หลายครั้ง',\n        noNeed: 'คุณสามารถสร้างรหัสผ่านที่แข็งแกร่งได้โดยไม่ต้องใช้อักขระพิเศษ ตัวเลข หรือตัวพิมพ์ใหญ่',\n        pwned: 'หากคุณใช้รหัสผ่านนี้ที่อื่น คุณควรเปลี่ยนมัน',\n        recentYears: 'หลีกเลี่ยงปีที่ใกล้เคียง',\n        repeated: 'หลีกเลี่ยงคำและอักขระที่ซ้ำกัน',\n        reverseWords: 'หลีกเลี่ยงการสะกดคำทั่วไปในทางกลับกัน',\n        sequences: 'หลีกเลี่ยงลำดับอักขระทั่วไป',\n        useWords: 'ใช้หลายคำ แต่หลีกเลี่ยงวลีทั่วไป',\n      },\n      warnings: {\n        common: 'นี่เป็นรหัสผ่านที่ใช้กันอย่างแพร่หลาย',\n        commonNames: 'ชื่อทั่วไปและนามสกุลเดาได้ง่าย',\n        dates: 'วันที่เดาได้ง่าย',\n        extendedRepeat: 'รูปแบบการซ้ำอักขระเช่น \"abcabcabc\" เดาได้ง่าย',\n        keyPattern: 'รูปแบบการกดคีย์บอร์ดสั้น ๆ เดาได้ง่าย',\n        namesByThemselves: 'ชื่อเดี่ยวหรือนามสกุลเดาได้ง่าย',\n        pwned: 'รหัสผ่านของคุณถูกเปิดเผยโดยการรั่วไหลข้อมูลบนอินเทอร์เน็ต',\n        recentYears: 'ปีที่ใกล้เคียงเดาได้ง่าย',\n        sequences: 'ลำดับอักขระทั่วไปเช่น \"abc\" เดาได้ง่าย',\n        similarToCommon: 'นี้คล้ายกับรหัสผ่านที่ใช้กันอย่างแพร่หลาย',\n        simpleRepeat: 'อักขระที่ซ้ำเช่น \"aaa\" เดาได้ง่าย',\n        straightRow: 'แถวตรงของคีย์บนคีย์บอร์ดของคุณเดาได้ง่าย',\n        topHundred: 'นี่เป็นรหัสผ่านที่ใช้บ่อย',\n        topTen: 'นี่เป็นรหัสผ่านที่ใช้มาก',\n        userInputs: 'ไม่ควรมีข้อมูลส่วนบุคคลหรือข้อมูลที่เกี่ยวข้องกับหน้า',\n        wordByItself: 'คำเดียวเดาได้ง่าย',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'เพิ่มบัญชี',\n    action__manageAccount: 'จัดการบัญชี',\n    action__signOut: 'ออกจากระบบ',\n    action__signOutAll: 'ออกจากระบบทุกบัญชี',\n  },\n  userProfile: {\n    backupCodePage: {\n      actionLabel__copied: 'คัดลอกแล้ว!',\n      actionLabel__copy: 'คัดลอกทั้งหมด',\n      actionLabel__download: 'ดาวน์โหลด .txt',\n      actionLabel__print: 'พิมพ์',\n      infoText1: 'จะเปิดใช้งานรหัสสำรองสำหรับบัญชีนี้',\n      infoText2: 'เก็บรหัสสำรองไว้เป็นความลับและเก็บไว้อย่างปลอดภัย คุณอาจสร้างรหัสสำรองใหม่หากคุณสงสัยว่ามีการเปิดเผย',\n      subtitle__codelist: 'เก็บไว้อย่างปลอดภัยและเก็บไว้เป็นความลับ',\n      successMessage:\n        'ตอนนี้ได้เปิดใช้งานรหัสสำรองแล้ว คุณสามารถใช้หนึ่งในรหัสเหล่านี้เพื่อเข้าสู่บัญชีของคุณหากคุณไม่สามารถเข้าถึงอุปกรณ์ตรวจสอบสิทธิ์ได้ แต่ละรหัสสามารถใช้ได้เพียงครั้งเดียว',\n      successSubtitle:\n        'คุณสามารถใช้หนึ่งในรหัสเหล่านี้เพื่อเข้าสู่บัญชีของคุณหากคุณไม่สามารถเข้าถึงอุปกรณ์ตรวจสอบสิทธิ์ได้',\n      title: 'เพิ่มการยืนยันรหัสสำรอง',\n      title__codelist: 'รหัสสำรอง',\n    },\n    billingPage: {\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'เลือกผู้ให้บริการเพื่อเชื่อมต่อบัญชีของคุณ',\n      formHint__noAccounts: 'ไม่มีผู้ให้บริการบัญชีภายนอกที่ใช้งานได้',\n      removeResource: {\n        messageLine1: '{{identifier}} จะถูกนำออกจากบัญชีนี้',\n        messageLine2: 'คุณจะไม่สามารถใช้บัญชีที่เชื่อมต่อนี้และฟีเจอร์ที่ขึ้นอยู่กับบัญชีนี้จะไม่สามารถใช้งานได้',\n        successMessage: '{{connectedAccount}} ได้ถูกนำออกจากบัญชีของคุณ',\n        title: 'นำบัญชีที่เชื่อมต่อออก',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}}',\n      successMessage: 'ผู้ให้บริการได้ถูกเพิ่มเข้าไปในบัญชีของคุณ',\n      title: 'เพิ่มบัญชีที่เชื่อมต่อ',\n    },\n    deletePage: {\n      actionDescription: 'พิมพ์ \"Delete account\" ด้านล่างเพื่อดำเนินการต่อ',\n      confirm: 'ลบบัญชี',\n      messageLine1: 'คุณแน่ใจหรือไม่ว่าต้องการลบบัญชีของคุณ?',\n      messageLine2: 'การกระทำนี้เป็นการถาวรและไม่สามารถย้อนกลับได้',\n      title: 'ลบบัญชี',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'อีเมลที่มีรหัสยืนยันจะถูกส่งไปยังที่อยู่อีเมลนี้',\n        formSubtitle: 'ป้อนรหัสยืนยันที่ถูกส่งไปยัง {{identifier}}',\n        formTitle: 'รหัสยืนยัน',\n        resendButton: 'ไม่ได้รับรหัสใช่หรือไม่ ส่งรหัสใหม่อีกครั้ง',\n        successMessage: 'อีเมล {{identifier}} ได้ถูกเพิ่มเข้าในบัญชีของคุณแล้ว',\n      },\n      emailLink: {\n        formHint: 'อีเมลที่มีลิงก์ยืนยันจะถูกส่งไปยังที่อยู่อีเมลนี้',\n        formSubtitle: 'คลิกที่ลิงก์ยืนยันในอีเมลที่ถูกส่งไปยัง {{identifier}}',\n        formTitle: 'ลิงก์ยืนยัน',\n        resendButton: 'ไม่ได้รับลิงก์ใช่หรือไม่ ส่งลิงก์ใหม่อีกครั้ง',\n        successMessage: 'อีเมล {{identifier}} ได้ถูกเพิ่มเข้าในบัญชีของคุณแล้ว',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} จะถูกนำออกจากบัญชีนี้',\n        messageLine2: 'คุณจะไม่สามารถเข้าสู่ระบบโดยใช้อีเมลที่อยู่นี้ได้อีกต่อไป',\n        successMessage: 'อีเมล {{emailAddress}} ได้ถูกนำออกจากบัญชีของคุณแล้ว',\n        title: 'นำที่อยู่อีเมลออก',\n      },\n      title: 'เพิ่มที่อยู่อีเมล',\n      verifyTitle: 'ยืนยันที่อยู่อีเมล',\n    },\n    formButtonPrimary__add: 'เพิ่ม',\n    formButtonPrimary__continue: 'ดำเนินการต่อ',\n    formButtonPrimary__finish: 'เสร็จสิ้น',\n    formButtonPrimary__remove: 'นำออก',\n    formButtonPrimary__save: 'บันทึก',\n    formButtonReset: 'ยกเลิก',\n    mfaPage: {\n      formHint: 'เลือกวิธีเพื่อเพิ่ม',\n      title: 'เพิ่มการยืนยันสองขั้นตอน',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'ใช้หมายเลขที่มีอยู่',\n      primaryButton__addPhoneNumber: 'เพิ่มหมายเลขโทรศัพท์',\n      removeResource: {\n        messageLine1: '{{identifier}} จะไม่ได้รับรหัสยืนยันเมื่อเข้าสู่ระบบอีกต่อไป',\n        messageLine2: 'บัญชีของคุณอาจไม่ปลอดภัยเท่าที่ควร คุณแน่ใจหรือว่าต้องการดำเนินการต่อ',\n        successMessage: 'การยืนยันสองขั้นตอนด้วยรหัส SMS ได้ถูกนำออกสำหรับ {{mfaPhoneCode}}',\n        title: 'นำการยืนยันสองขั้นตอนออก',\n      },\n      subtitle__availablePhoneNumbers:\n        'เลือกหมายเลขโทรศัพท์ที่มีอยู่เพื่อลงทะเบียนสำหรับการยืนยันสองขั้นตอนด้วยรหัส SMS หรือเพิ่มหมายเลขใหม่',\n      subtitle__unavailablePhoneNumbers:\n        'ไม่มีหมายเลขโทรศัพท์ที่สามารถใช้ลงทะเบียนสำหรับการยืนยันสองขั้นตอนด้วยรหัส SMS โปรดเพิ่มหมายเลขใหม่',\n      successMessage1: 'เมื่อเข้าสู่ระบบ คุณจะต้องป้อนรหัสยืนยันที่ถูกส่งไปยังหมายเลขโทรศัพท์นี้เป็นขั้นตอนเพิ่มเติม',\n      successMessage2:\n        'บันทึกรหัสสำรองและเก็บไว้ในที่ปลอดภัย หากคุณสูญเสียการเข้าถึงอุปกรณ์การตรวจสอบของคุณ คุณสามารถใช้รหัสสำรองเพื่อเข้าสู่ระบบได้',\n      successTitle: 'เปิดใช้งานการยืนยันด้วยรหัส SMS',\n      title: 'เพิ่มการยืนยันด้วยรหัส SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'สแกนคิวอาร์โค้ดแทน',\n        buttonUnableToScan__nonPrimary: 'ไม่สามารถสแกนคิวอาร์โค้ดใช่หรือไม่',\n        infoText__ableToScan:\n          'ตั้งค่าวิธีการเข้าสู่ระบบใหม่ในแอปยืนยันตัวตนของคุณและสแกนคิวอาร์โค้ดต่อไปนี้เพื่อเชื่อมโยงกับบัญชีของคุณ',\n        infoText__unableToScan: 'ตั้งค่าวิธีการเข้าสู่ระบบใหม่ในตัวตรวจสอบของคุณและป้อนคีย์ที่ให้ไว้ด้านล่าง',\n        inputLabel__unableToScan1:\n          'ตรวจสอบให้แน่ใจว่าเปิดใช้งานรหัสผ่านตามเวลาหรือรหัสผ่านครั้งเดียว จากนั้นจึงเชื่อมโยงบัญชีของคุณ',\n        inputLabel__unableToScan2: 'หรือหากตัวตรวจสอบของคุณรองรับ TOTP URIs คุณยังสามารถคัดลอก URI ทั้งหมดได้',\n      },\n      removeResource: {\n        messageLine1: 'ไม่จำเป็นต้องใช้รหัสยืนยันจากระบบยืนยันตัวตนนี้เมื่อลงชื่อเข้าใช้อีกต่อไป',\n        messageLine2: 'บัญชีของคุณอาจไม่ปลอดภัยเท่าที่ควร คุณแน่ใจหรือว่าต้องการดำเนินการต่อ',\n        successMessage: 'การยืนยันสองขั้นตอนผ่านแอปพลิเคชันยืนยันตัวตนได้ถูกนำออก',\n        title: 'นำการยืนยันสองขั้นตอนออก',\n      },\n      successMessage:\n        'การยืนยันสองขั้นตอนเปิดใช้งานแล้ว เมื่อลงชื่อเข้าใช้ คุณจะต้องป้อนรหัสยืนยันจากระบบยืนยันตัวตนนี้เป็นขั้นตอนเพิ่มเติม',\n      title: 'เพิ่มแอปพลิเคชันยืนยันตัวตน',\n      verifySubtitle: 'ป้อนรหัสยืนยันที่สร้างโดยระบบยืนยันตัวตนของคุณ',\n      verifyTitle: 'รหัสยืนยัน',\n    },\n    mobileButton__menu: 'เมนู',\n    navbar: {\n      account: 'โปรไฟล์',\n      billing: undefined,\n      description: 'จัดการข้อมูลบัญชีของคุณ',\n      security: 'ความปลอดภัย',\n      title: 'บัญชี',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} จะถูกลบออกจากบัญชีนี้',\n        title: 'ลบพาสคีย์',\n      },\n      subtitle__rename: 'คุณสามารถเปลี่ยนชื่อพาสคีย์เพื่อให้สามารถค้นหาได้ง่ายขึ้น',\n      title__rename: 'เปลี่ยนชื่อพาสคีย์',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions: 'ขอแนะนำให้ลงชื่อออกจากอุปกรณ์อื่น ๆ ที่อาจใช้รหัสผ่านเก่าของคุณ',\n      readonly:\n        'คุณไม่สามารถแก้ไขรหัสผ่านได้ในขณะนี้เนื่องจากคุณสามารถลงชื่อเข้าใช้ได้ผ่านการเชื่อมต่อกับองค์กรเท่านั้น',\n      successMessage__set: 'รหัสผ่านของคุณได้รับการตั้งค่า',\n      successMessage__signOutOfOtherSessions: 'อุปกรณ์อื่น ๆ ทั้งหมดได้ลงชื่อออก',\n      successMessage__update: 'รหัสผ่านของคุณได้รับการอัปเดต',\n      title__set: 'ตั้งรหัสผ่าน',\n      title__update: 'อัปเดตรหัสผ่าน',\n    },\n    phoneNumberPage: {\n      infoText: 'ข้อความที่มีรหัสยืนยันจะถูกส่งไปยังหมายเลขโทรศัพท์นี้ อาจมีการเรียกเก็บค่าบริการข้อความและข้อมูล',\n      removeResource: {\n        messageLine1: '{{identifier}} จะถูกนำออกจากบัญชีนี้',\n        messageLine2: 'คุณจะไม่สามารถลงชื่อเข้าใช้โดยใช้หมายเลขโทรศัพท์นี้อีกต่อไป',\n        successMessage: '{{phoneNumber}} ได้ถูกนำออกจากบัญชีของคุณ',\n        title: 'นำหมายเลขโทรศัพท์ออก',\n      },\n      successMessage: '{{identifier}} ได้ถูกเพิ่มเข้าในบัญชีของคุณ',\n      title: 'เพิ่มหมายเลขโทรศัพท์',\n      verifySubtitle: 'ป้อนรหัสยืนยันที่ถูกส่งไปยัง {{identifier}}',\n      verifyTitle: 'ยืนยันหมายเลขโทรศัพท์',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'แนะนำสัดส่วน 1:1 และ สูงสุด 10MB',\n      imageFormDestructiveActionSubtitle: 'ลบ',\n      imageFormSubtitle: 'อัปโหลด',\n      imageFormTitle: 'รูปโปรไฟล์',\n      readonly: 'ข้อมูลโปรไฟล์ของคุณได้รับจากการเชื่อมต่อกับองค์กรและไม่สามารถแก้ไขได้',\n      successMessage: 'โปรไฟล์ของคุณได้รับการอัปเดตแล้ว',\n      title: 'อัปเดตโปรไฟล์',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'ลงชื่อออกจากอุปกรณ์',\n        title: 'อุปกรณ์ที่ใช้งานอยู่',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'ลองอีกครั้ง',\n        actionLabel__reauthorize: 'อนุญาตตอนนี้',\n        destructiveActionTitle: 'ลบ',\n        primaryButton: 'เชื่อมต่อบัญชี',\n        subtitle__disconnected: 'บัญชีนี้ถูกตัดการเชื่อมต่อแล้ว',\n        subtitle__reauthorize:\n          'ขอบเขตที่ต้องการได้รับการอัปเดตและคุณอาจประสบปัญหาการใช้งานจำกัด โปรดอนุญาตแอปพลิเคชันนี้อีกครั้งเพื่อหลีกเลี่ยงปัญหา',\n        title: 'บัญชีที่เชื่อมต่อ',\n      },\n      dangerSection: {\n        deleteAccountButton: 'ลบบัญชี',\n        title: 'ลบบัญชี',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'ลบอีเมล',\n        detailsAction__nonPrimary: 'ตั้งเป็นหลัก',\n        detailsAction__primary: 'เสร็จสิ้นการยืนยัน',\n        detailsAction__unverified: 'ยืนยัน',\n        primaryButton: 'เพิ่มที่อยู่อีเมล',\n        title: 'ที่อยู่อีเมล',\n      },\n      enterpriseAccountsSection: {\n        title: 'บัญชีองค์กร',\n      },\n      headerTitle__account: 'รายละเอียดโปรไฟล์',\n      headerTitle__security: 'ความปลอดภัย',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'สร้างใหม่',\n          headerTitle: 'รหัสสำรอง',\n          subtitle__regenerate: 'รับชุดใหม่ของรหัสสำรองที่ปลอดภัย รหัสสำรองก่อนหน้าจะถูกลบและไม่สามารถใช้งานได้',\n          title__regenerate: 'สร้างรหัสสำรองใหม่',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'ตั้งเป็นค่าเริ่มต้น',\n          destructiveActionLabel: 'ลบ',\n        },\n        primaryButton: 'เพิ่มการยืนยันสองขั้นตอน',\n        title: 'การยืนยันสองขั้นตอน',\n        totp: {\n          destructiveActionTitle: 'ลบ',\n          headerTitle: 'แอปพลิเคชันยืนยันตัวตน',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'ลบ',\n        menuAction__rename: 'เปลี่ยนชื่อ',\n        primaryButton: undefined,\n        title: 'พาสคีย์',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'ตั้งรหัสผ่าน',\n        primaryButton__updatePassword: 'อัปเดตรหัสผ่าน',\n        title: 'รหัสผ่าน',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'ลบหมายเลขโทรศัพท์',\n        detailsAction__nonPrimary: 'ตั้งเป็นหลัก',\n        detailsAction__primary: 'เสร็จสิ้นการยืนยัน',\n        detailsAction__unverified: 'ยืนยันหมายเลขโทรศัพท์',\n        primaryButton: 'เพิ่มหมายเลขโทรศัพท์',\n        title: 'หมายเลขโทรศัพท์',\n      },\n      profileSection: {\n        primaryButton: 'อัปเดตโปรไฟล์',\n        title: 'โปรไฟล์',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'ตั้งชื่อผู้ใช้',\n        primaryButton__updateUsername: 'อัปเดตชื่อผู้ใช้',\n        title: 'ชื่อผู้ใช้',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'ลบวอลเล็ต',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'วอลเล็ต Web3',\n        title: 'วอลเล็ต Web3',\n      },\n    },\n    usernamePage: {\n      successMessage: 'ชื่อผู้ใช้ของคุณได้รับการอัปเดตแล้ว',\n      title__set: 'ตั้งชื่อผู้ใช้',\n      title__update: 'อัปเดตชื่อผู้ใช้',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} จะถูกลบออกจากบัญชีนี้',\n        messageLine2: 'คุณจะไม่สามารถเข้าสู่ระบบโดยใช้วอลเล็ต Web3 นี้ได้อีก',\n        successMessage: '{{web3Wallet}} ได้ถูกลบออกจากบัญชีของคุณแล้ว',\n        title: 'ลบวอลเล็ต Web3',\n      },\n      subtitle__availableWallets: 'เลือกวอลเล็ต Web3 เพื่อเชื่อมต่อกับบัญชีของคุณ',\n      subtitle__unavailableWallets: 'ไม่มีวอลเล็ต Web3 ที่พร้อมใช้งาน',\n      successMessage: 'วอลเล็ตได้ถูกเพิ่มเข้าไปในบัญชีของคุณแล้ว',\n      title: 'เพิ่มวอลเล็ต Web3',\n      web3WalletButtonsBlockButton: '{{provider|titleize}}',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: 'เข้าสู่ระบบ',\n      actionText: 'มีสิทธิ์เข้าถึงแล้วใช่หรือไม่?',\n      formButton: 'เข้าร่วมรายชื่อผู้รอ',\n      subtitle: 'ใส่ที่อยู่อีเมลของคุณและเราจะแจ้งให้คุณทราบเมื่อที่ของคุณพร้อม',\n      title: 'เข้าร่วมรายชื่อผู้รอ',\n    },\n    success: {\n      message: 'คุณจะถูกเปลี่ยนเส้นทางในไม่ช้า...',\n      subtitle: 'เราจะติดต่อคุณเมื่อที่ของคุณพร้อม',\n      title: 'ขอบคุณที่เข้าร่วมรายชื่อผู้รอ!',\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCACE;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BACE;AAAA,IACF,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCACE;AAAA,IACF,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WAAW;AAAA,MACX,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCACE;AAAA,MACF,mCACE;AAAA,MACF,iBAAiB;AAAA,MACjB,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBAAwB;AAAA,QACxB,2BACE;AAAA,QACF,2BAA2B;AAAA,MAC7B;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CAA0C;AAAA,MAC1C,UACE;AAAA,MACF,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBAAsB;AAAA,UACtB,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}