import type { CreateEmailLinkFlowReturn, EmailAddressResource, SignInResource, SignInStartEmailLinkFlowParams, SignUpResource, StartEmailLinkFlowParams } from '@clerk/types';
type UseEmailLinkSignInReturn = CreateEmailLinkFlowReturn<SignInStartEmailLinkFlowParams, SignInResource>;
type UseEmailLinkSignUpReturn = CreateEmailLinkFlowReturn<StartEmailLinkFlowParams, SignUpResource>;
type UseEmailLinkEmailAddressReturn = CreateEmailLinkFlowReturn<StartEmailLinkFlowParams, EmailAddressResource>;
declare function useEmailLink(resource: SignInResource): UseEmailLinkSignInReturn;
declare function useEmailLink(resource: SignUpResource): UseEmailLinkSignUpReturn;
declare function useEmailLink(resource: EmailAddressResource): UseEmailLinkEmailAddressReturn;
export { useEmailLink };
