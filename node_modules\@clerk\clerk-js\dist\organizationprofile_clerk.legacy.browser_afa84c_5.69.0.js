"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["554"],{79743:function(e,i,t){t.r(i),t.d(i,{OrganizationProfileModal:()=>eU,OrganizationProfile:()=>eW});var a=t(79109),o=t(83799),n=t(69144),l=t(2672),r=t(15579),s=t(23394),d=t(31673),c=t(11576),u=t(39541),g=t(24676),m=t(8969),p=t(91085);let h=e=>{let{organization:i}=(0,o.o8)(),{pages:t}=(0,c.useOrganizationProfileContext)(),n=(0,m.N2)(e=>e({permission:"org:sys_memberships:read"})||e({permission:"org:sys_memberships:manage"})),l=(0,m.N2)(e=>e({permission:"org:sys_billing:read"})||e({permission:"org:sys_billing:manage"})),s=t.routes.filter(e=>e.id!==d.bm.MEMBERS||e.id===d.bm.MEMBERS&&n).filter(e=>e.id!==d.bm.BILLING||e.id===d.bm.BILLING&&l);return i?(0,a.BX)(r.Uh,{contentRef:e.contentRef,children:[(0,a.tZ)(r.l2,{title:(0,p.u1)("organizationProfile.navbar.title"),description:(0,p.u1)("organizationProfile.navbar.description"),routes:s,contentRef:e.contentRef}),e.children]}):null};t(50725);var v=t(94995),f=t(92654),z=t(35973),b=t(19655),Z=t(99805),P=t(44709),y=t(70431),S=t(19460),C=t(68487),x=t(67263),_=t(77623),K=t(57484);let B=e=>{let i=(0,l.useCardState)(),{navigateAfterLeaveOrganization:t}=(0,c.useOrganizationProfileContext)(),{userMemberships:a,userInvitations:n}=(0,o.eW)({userMemberships:K.AO.userMemberships,userInvitations:K.AO.userInvitations});return()=>i.runAsync(async()=>{await (null==e?void 0:e())}).then(()=>{var e,i;null===(e=a.revalidate)||void 0===e||e.call(a),null===(i=n.revalidate)||void 0===i||i.call(n),t()})},D=e=>{let{organization:i}=(0,o.o8)(),{user:t}=(0,o.aF)(),n=B(()=>t.leaveOrganization(i.id));return i&&t?(0,a.tZ)(T,{organizationName:null==i?void 0:i.name,title:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.title"),messageLine1:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.messageLine1"),messageLine2:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.messageLine2"),actionDescription:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.actionDescription",{organizationName:null==i?void 0:i.name}),submitLabel:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.title"),successMessage:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.successMessage"),onConfirmation:n,...e}):null},R=e=>{let{organization:i,membership:t}=(0,o.o8)(),n=B(null==i?void 0:i.destroy);return i&&t?(0,a.tZ)(T,{organizationName:null==i?void 0:i.name,title:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.title"),messageLine1:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.messageLine1"),messageLine2:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.messageLine2"),actionDescription:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.actionDescription",{organizationName:null==i?void 0:i.name}),submitLabel:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.title"),successMessage:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.successMessage"),onConfirmation:n,...e}):null},T=(0,l.withCardStateProvider)(e=>{let{title:i,messageLine1:t,messageLine2:o,actionDescription:n,organizationName:r,successMessage:s,submitLabel:d,onSuccess:c,onReset:g,onConfirmation:p,colorScheme:h="danger"}=e,v=(0,m.a2)(),f=(0,l.useCardState)(),z=(0,_.Yp)("deleteOrganizationConfirmation","",{type:"text",label:n||(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.actionDescription"),isRequired:!0,placeholder:r}),b=!n||z.value===r,Z=async()=>{if(b)try{await p().then(()=>v.nextStep())}catch(e){(0,_.S3)(e,[],f.setError)}};return(0,a.BX)(m.en,{...v.props,children:[(0,a.tZ)(C.Y,{headerTitle:i,gap:1,children:(0,a.BX)(y.l.Root,{onSubmit:Z,children:[(0,a.BX)(u.Col,{children:[(0,a.tZ)(u.Text,{localizationKey:t,colorScheme:"secondary"}),(0,a.tZ)(u.Text,{localizationKey:o,colorScheme:"danger"})]}),(0,a.tZ)(y.l.ControlRow,{elementId:z.id,children:(0,a.tZ)(y.l.PlainInput,{...z.props})}),(0,a.BX)(S.K,{children:[(0,a.tZ)(y.l.SubmitButton,{block:!1,colorScheme:h,localizationKey:d,isDisabled:!b}),(0,a.tZ)(y.l.ResetButton,{localizationKey:(0,u.localizationKeys)("userProfile.formButtonReset"),block:!1,onClick:g})]})]})}),(0,a.tZ)(x.I,{title:i,text:s,onFinish:c})]})});t(65223);var w=t(12667),I=t(12464);t(28419),t(79876);var L=t(96519);let X=(e,i)=>{let{infoLabel:t}=i,a=(null==e?void 0:e.totalPendingInvitations)||0;return 0===((null==e?void 0:e.totalPendingSuggestions)||0)+a?[]:[t,(0,u.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.calloutInvitationCountLabel",{count:a}),(0,u.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.calloutSuggestionCountLabel",{count:a})]},k=e=>{let i=[];return e.domains.enrollmentModes.includes("manual_invitation")&&i.push({value:"manual_invitation",label:(0,u.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.manualInvitationOption__label"),description:(0,u.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.manualInvitationOption__description")}),e.domains.enrollmentModes.includes("automatic_invitation")&&i.push({value:"automatic_invitation",label:(0,u.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.automaticInvitationOption__label"),description:(0,u.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.automaticInvitationOption__description")}),e.domains.enrollmentModes.includes("automatic_suggestion")&&i.push({value:"automatic_suggestion",label:(0,u.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.automaticSuggestionOption__label"),description:(0,u.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.automaticSuggestionOption__description")}),i},O=()=>{let{organizationSettings:e}=(0,c.useEnvironment)();return k(e)},$=(0,l.withCardStateProvider)(e=>{let{domainId:i,mode:t="edit",onSuccess:r,onReset:s}=e,d=(0,l.useCardState)(),{organizationSettings:g}=(0,c.useEnvironment)(),{membership:p,organization:h,domains:v}=(0,o.o8)({domains:{infinite:!0}}),z="edit"===t,b=O(),Z=(0,_.Yp)("enrollmentMode","",{type:"radio",radioOptions:b,isRequired:!0}),P=(0,_.Yp)("deleteExistingInvitationsSuggestions","",{label:(0,u.localizationKeys)("formFieldLabel__organizationDomainDeletePending"),type:"checkbox"}),{data:x,isLoading:K}=(0,I.ib)(null==h?void 0:h.getDomain,{domainId:i});(0,n.useEffect)(()=>{x&&Z.setValue(x.enrollmentMode)},[null==x?void 0:x.id]);let B=(0,u.localizationKeys)("organizationProfile.verifiedDomainPage.title",{domain:null==x?void 0:x.name}),D=(0,u.localizationKeys)("organizationProfile.verifiedDomainPage.subtitle",{domain:null==x?void 0:x.name}),R=X(x,{infoLabel:(0,u.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.calloutInfoLabel")}),T=async()=>{if(x&&h&&p&&v)try{await x.updateEnrollmentMode({enrollmentMode:Z.value,deletePending:P.checked}),await v.revalidate(),r()}catch(e){(0,_.S3)(e,[Z],d.setError)}};return h&&g?K||!x?(0,a.tZ)(u.Flex,{direction:"row",align:"center",justify:"center",sx:{height:"100%"},children:(0,a.tZ)(u.Spinner,{size:"lg",colorScheme:"primary",elementDescriptor:u.descriptors.spinner})}):(x.verification&&"verified"===x.verification.status||s(),(0,a.tZ)(C.Y,{headerTitle:B,headerSubtitle:z?void 0:D,gap:4,children:(0,a.BX)(u.Col,{gap:6,children:[R.length>0&&(0,a.tZ)(m.aX,{icon:L.I$,children:R.map((e,i)=>(0,a.tZ)(u.Text,{as:"span",sx:{display:"block"},localizationKey:e},i))}),(0,a.tZ)(f.h.Root,{children:(0,a.tZ)(f.h.Subtitle,{localizationKey:(0,u.localizationKeys)("organizationProfile.verifiedDomainPage.enrollmentTab.subtitle"),variant:"subtitle"})}),(0,a.BX)(y.l.Root,{onSubmit:T,gap:6,children:[(0,a.tZ)(y.l.ControlRow,{elementId:Z.id,children:(0,a.tZ)(y.l.RadioGroup,{...Z.props})}),z&&"manual_invitation"===Z.value&&(0,a.tZ)(y.l.ControlRow,{elementId:P.id,children:(0,a.tZ)(y.l.Checkbox,{...P.props})}),(0,a.tZ)(S.A,{isDisabled:K||!x,onReset:s})]})]})})):null}),A=(0,l.withCardStateProvider)(e=>{var i;let{domainId:t,onSuccess:r,onReset:s,skipToVerified:d}=e,g=(0,l.useCardState)(),{organizationSettings:p}=(0,c.useEnvironment)(),{organization:h}=(0,o.o8)(),{data:v,isLoading:f}=(0,I.ib)(d?void 0:null==h?void 0:h.getDomain,{domainId:t}),z=(0,u.localizationKeys)("organizationProfile.verifyDomainPage.title"),b=(0,u.localizationKeys)("organizationProfile.verifyDomainPage.subtitle",{domainName:null!==(i=null==v?void 0:v.name)&&void 0!==i?i:""}),Z=(0,m.a2)({defaultStep:2*!!d,onNextStep:()=>g.setError(void 0)}),P=(0,_.Yp)("affiliationEmailAddress","",{type:"text",label:(0,u.localizationKeys)("formFieldLabel__organizationDomainEmailAddress"),placeholder:(0,u.localizationKeys)("formFieldInputPlaceholder__organizationDomainEmailAddress"),infoText:(0,u.localizationKeys)("formFieldLabel__organizationDomainEmailAddressDescription"),isRequired:!0}),x=(0,n.useRef)(),K=(0,u.localizationKeys)("organizationProfile.verifyDomainPage.subtitleVerificationCodeScreen",{emailAddress:x.current}),B=(e,i,t)=>{var a;null==v||null===(a=v.attemptAffiliationVerification)||void 0===a||a.call(v,{code:e}).then(async e=>{var t;await i(),(null===(t=e.verification)||void 0===t?void 0:t.status)==="verified"?Z.nextStep():null==r||r()}).catch(e=>t(e))},D=(0,w.e3)({onCodeEntryFinished:(e,i,t)=>{B(e,i,t)},onResendCodeClicked:()=>{null==v||v.prepareAffiliationVerification({affiliationEmailAddress:P.value}).catch(e=>{(0,_.S3)(e,[P],g.setError)})}});if(!h||!p)return null;let R=h.name!==P.value,T="@".concat(null==v?void 0:v.name);return!f&&v||d?(0,a.BX)(m.en,{...Z.props,children:[(0,a.tZ)(C.Y,{headerTitle:z,headerSubtitle:b,children:(0,a.BX)(y.l.Root,{onSubmit:e=>{if(e.preventDefault(),v)return x.current="".concat(P.value).concat(T),v.prepareAffiliationVerification({affiliationEmailAddress:x.current}).then(Z.nextStep).catch(e=>{(0,_.S3)(e,[P],g.setError)})},children:[(0,a.tZ)(y.l.ControlRow,{elementId:P.id,children:(0,a.tZ)(y.l.InputGroup,{...P.props,autoFocus:!0,groupSuffix:T,ignorePasswordManager:!0})}),(0,a.tZ)(S.A,{isDisabled:!R,onReset:s})]})}),(0,a.BX)(C.Y,{headerTitle:z,headerSubtitle:K,children:[(0,a.tZ)(y.l.OTPInput,{...D,label:(0,u.localizationKeys)("organizationProfile.verifyDomainPage.formTitle"),description:(0,u.localizationKeys)("organizationProfile.verifyDomainPage.formSubtitle"),resendButton:(0,u.localizationKeys)("organizationProfile.verifyDomainPage.resendButton")}),(0,a.tZ)(S.K,{children:(0,a.tZ)(u.Button,{elementDescriptor:u.descriptors.formButtonReset,block:!1,variant:"ghost",textVariant:"buttonSmall",type:"reset",isDisabled:D.isLoading||"success"===D.otpControl.otpInputProps.feedbackType,onClick:()=>{D.otpControl.otpInputProps.clearFeedback(),D.otpControl.reset(),Z.prevStep()},localizationKey:(0,u.localizationKeys)("userProfile.formButtonReset")})})]}),(0,a.tZ)($,{domainId:t,mode:"select",onSuccess:r,onReset:s})]}):(0,a.tZ)(u.Flex,{direction:"row",align:"center",justify:"center",children:(0,a.tZ)(u.Spinner,{size:"lg",colorScheme:"primary",elementDescriptor:u.descriptors.spinner})})}),E=(0,l.withCardStateProvider)(e=>{let{onSuccess:i,onReset:t}=e,{organizationSettings:r}=(0,c.useEnvironment)(),{domains:s}=(0,o.o8)({domains:{infinite:!0}}),d=(0,m.a2)({onNextStep:()=>b.setError(void 0)}),[g,p]=(0,n.useState)(""),[h,v]=(0,n.useState)(!1),f=(0,u.localizationKeys)("organizationProfile.createDomainPage.title"),z=(0,u.localizationKeys)("organizationProfile.createDomainPage.subtitle"),b=(0,l.useCardState)(),{organization:Z}=(0,o.o8)(),P=(0,_.Yp)("name","",{type:"text",label:(0,u.localizationKeys)("formFieldLabel__organizationDomain"),placeholder:(0,u.localizationKeys)("formFieldInputPlaceholder__organizationDomain")});if(!Z||!r)return null;let x=""!==P.value.trim();return(0,a.BX)(m.en,{...d.props,children:[(0,a.tZ)(C.Y,{headerTitle:f,headerSubtitle:z,children:(0,a.BX)(y.l.Root,{onSubmit:e=>(P.clearFeedback(),e.preventDefault(),Z.createDomain(P.value).then(async e=>{var i;p(e.id),null==s||null===(i=s.revalidate)||void 0===i||i.call(s),e.verification&&"verified"===e.verification.status&&v(!0),d.nextStep()}).catch(e=>{(0,_.S3)(e,[P],b.setError)})),children:[(0,a.tZ)(y.l.ControlRow,{elementId:P.id,children:(0,a.tZ)(y.l.PlainInput,{...P.props,autoFocus:!0,ignorePasswordManager:!0,isRequired:!0})}),(0,a.tZ)(S.A,{isDisabled:!x,onReset:t})]})}),(0,a.tZ)(A,{domainId:g,onSuccess:()=>{var e;null==s||null===(e=s.revalidate)||void 0===e||e.call(s),null==i||i()},skipToVerified:h,onReset:t})]})});var M=t(33009);let F={manual_invitation:(0,u.localizationKeys)("organizationProfile.badge__manualInvitation"),automatic_invitation:(0,u.localizationKeys)("organizationProfile.badge__automaticInvitation"),automatic_suggestion:(0,u.localizationKeys)("organizationProfile.badge__automaticSuggestion")},q=e=>{let{organizationDomain:i}=e;return i?i.verification&&"verified"===i.verification.status?(0,a.tZ)(u.Badge,{localizationKey:F[i.enrollmentMode],colorScheme:"manual_invitation"===i.enrollmentMode?"primary":"success"}):(0,a.tZ)(u.Badge,{localizationKey:(0,u.localizationKeys)("organizationProfile.badge__unverified"),colorScheme:"warning"}):null},N=e=>{var i;let{organizationSettings:t}=(0,c.useEnvironment)(),{organization:l}=(0,o.o8)(),{domainId:r,onSuccess:s,onReset:d}=e,g=n.useRef(),{data:h,isLoading:v}=(0,I.ib)(null==l?void 0:l.getDomain,{domainId:r},{onSuccess(e){g.current={...e}}}),{domains:f}=(0,o.o8)({domains:{infinite:!0}});return l&&t?v||!h?(0,a.tZ)(u.Flex,{direction:"row",align:"center",justify:"center",children:(0,a.tZ)(u.Spinner,{size:"lg",colorScheme:"primary",elementDescriptor:u.descriptors.spinner})}):(0,a.tZ)(m.LE,{title:(0,p.u1)("organizationProfile.removeDomainPage.title"),messageLine1:(0,p.u1)("organizationProfile.removeDomainPage.messageLine1",{domain:null===(i=g.current)||void 0===i?void 0:i.name}),messageLine2:(0,p.u1)("organizationProfile.removeDomainPage.messageLine2"),deleteResource:()=>null==h?void 0:h.delete().then(async()=>{var e;await (null==f?void 0:null===(e=f.revalidate)||void 0===e?void 0:e.call(f)),s()}),onSuccess:s,onReset:d}):null},V=e=>{let{close:i}=(0,P.XC)();return(0,a.tZ)(N,{onSuccess:i,onReset:i,...e})},Y=e=>{let{close:i}=(0,P.XC)();return(0,a.tZ)($,{onSuccess:i,onReset:i,...e})},j=e=>{let{close:i}=(0,P.XC)();return(0,a.tZ)(A,{onSuccess:i,onReset:i,skipToVerified:!1,...e})},H=e=>{let{open:i}=(0,P.XC)(),t=[];return e.verification&&"verified"===e.verification.status?t.push({label:(0,u.localizationKeys)("organizationProfile.profilePage.domainSection.menuAction__manage"),onClick:()=>i("manage")}):t.push({label:(0,u.localizationKeys)("organizationProfile.profilePage.domainSection.menuAction__verify"),onClick:()=>i("verify")}),t.push({label:(0,u.localizationKeys)("organizationProfile.profilePage.domainSection.menuAction__remove"),isDestructive:!0,onClick:()=>i("remove")}),t},W=e=>{let{domain:i}=e,t=H(i);return(0,a.tZ)(M.a,{actions:t})},U=(0,m.Ci)(e=>{let{verificationStatus:i,enrollmentMode:t,fallback:l,...r}=e,{organization:s,domains:d}=(0,o.o8)({domains:{infinite:!0,...r}}),{ref:c}=(0,I.YD)({threshold:0,onChange:e=>{if(e){var i;null==d||null===(i=d.fetchNext)||void 0===i||i.call(d)}}}),g=(0,n.useMemo)(()=>(null==d?void 0:d.data)?d.data.filter(e=>{let a=!0,o=!0;return i&&(a=!!e.verification&&e.verification.status===i),t&&(o=e.enrollmentMode===t),a&&o}):[],[null==d?void 0:d.data]);if(!s)return null;let p=(null==d?void 0:d.hasNextPage)||(null==d?void 0:d.isFetching);return 0!==g.length||(null==d?void 0:d.isLoading)||l?(0,a.BX)(b.zd.ItemList,{id:"organizationDomains",children:[0===g.length&&!(null==d?void 0:d.isLoading)&&l,g.map(e=>(0,a.BX)(Z.a.Root,{children:[(0,a.BX)(b.zd.Item,{id:"organizationDomains",hoverable:!0,children:[(0,a.BX)(u.Flex,{sx:e=>({gap:e.space.$1}),children:[(0,a.tZ)(u.Text,{children:e.name}),(0,a.tZ)(q,{organizationDomain:e})]}),(0,a.tZ)(m.Cv,{permission:"org:sys_domains:manage",children:(0,a.tZ)(W,{domain:e})})]}),(0,a.tZ)(Z.a.Open,{value:"remove",children:(0,a.tZ)(Z.a.Card,{variant:"destructive",children:(0,a.tZ)(V,{domainId:e.id})})}),(0,a.tZ)(Z.a.Open,{value:"verify",children:(0,a.tZ)(Z.a.Card,{children:(0,a.tZ)(j,{domainId:e.id})})}),(0,a.tZ)(Z.a.Open,{value:"manage",children:(0,a.tZ)(Z.a.Card,{children:(0,a.tZ)(Y,{domainId:e.id})})})]},e.id)),(0,a.tZ)(u.Box,{ref:(null==d?void 0:d.hasNextPage)&&!d.isFetching?c:void 0,sx:{visibility:"hidden"}}),p&&0===d.data.length&&(0,a.tZ)(u.Box,{sx:[e=>({width:"100%",height:e.space.$10,position:"relative"})],children:(0,a.tZ)(u.Box,{sx:{display:"flex",margin:"auto",position:"absolute",left:"50%",top:"50%",transform:"translateY(-50%) translateX(-50%)"},children:(0,a.tZ)(u.Spinner,{size:"sm",colorScheme:"primary",elementDescriptor:u.descriptors.spinner})})})]}):null},{permission:"org:sys_domains:read"});var Q=t(26917),G=t(8181);let J=(0,l.withCardStateProvider)(e=>{let{onSuccess:i,onReset:t}=e,n=(0,u.localizationKeys)("organizationProfile.profilePage.title"),r=(0,l.useCardState)(),{organization:s}=(0,o.o8)(),d=(0,_.Yp)("name",(null==s?void 0:s.name)||"",{type:"text",label:(0,u.localizationKeys)("formFieldLabel__organizationName"),placeholder:(0,u.localizationKeys)("formFieldInputPlaceholder__organizationName")}),c=(0,_.Yp)("slug",(null==s?void 0:s.slug)||"",{type:"text",label:(0,u.localizationKeys)("formFieldLabel__organizationSlug"),placeholder:(0,u.localizationKeys)("formFieldInputPlaceholder__organizationSlug")});if(!s)return null;let g=(s.name!==d.value||s.slug!==c.value)&&"error"!==c.feedbackType,m=async e=>(e.preventDefault(),(g?s.update({name:d.value,slug:c.value}):Promise.resolve()).then(i).catch(e=>{(0,_.S3)(e,[d,c],r.setError)})),p=e=>{c.setValue(e),c.clearFeedback()};return(0,a.tZ)(C.Y,{headerTitle:n,children:(0,a.BX)(y.l.Root,{onSubmit:m,children:[(0,a.tZ)(G.D,{organization:s,onAvatarChange:e=>s.setLogo({file:e}).then(()=>{r.setIdle()}).catch(e=>(0,_.S3)(e,[],r.setError)),onAvatarRemove:(0,Q.QO)(s.imageUrl)?null:()=>{s.setLogo({file:null}).then(()=>{r.setIdle()}).catch(e=>(0,_.S3)(e,[],r.setError))}}),(0,a.tZ)(y.l.ControlRow,{elementId:d.id,children:(0,a.tZ)(y.l.PlainInput,{...d.props,autoFocus:!0,isRequired:!0,ignorePasswordManager:!0})}),(0,a.tZ)(y.l.ControlRow,{elementId:c.id,children:(0,a.tZ)(y.l.PlainInput,{...c.props,onChange:e=>{p(e.target.value)},ignorePasswordManager:!0})}),(0,a.tZ)(S.A,{isDisabled:!g,onReset:t})]})})}),ee=()=>{let{close:e}=(0,P.XC)();return(0,a.tZ)(J,{onSuccess:e,onReset:e})},ei=()=>{let{close:e}=(0,P.XC)();return(0,a.tZ)(E,{onSuccess:e,onReset:e})},et=()=>{let{close:e}=(0,P.XC)();return(0,a.tZ)(D,{onSuccess:e,onReset:e})},ea=()=>{let{close:e}=(0,P.XC)();return(0,a.tZ)(R,{onSuccess:e,onReset:e})},eo=()=>(0,a.tZ)(u.Col,{elementDescriptor:u.descriptors.page,sx:e=>({gap:e.space.$8}),children:(0,a.BX)(u.Col,{elementDescriptor:u.descriptors.profilePage,elementId:u.descriptors.profilePage.setId("organizationGeneral"),children:[(0,a.tZ)(f.h.Root,{children:(0,a.tZ)(f.h.Title,{localizationKey:(0,u.localizationKeys)("organizationProfile.start.headerTitle__general"),sx:e=>({marginBottom:e.space.$4}),textVariant:"h2"})}),(0,a.tZ)(en,{}),(0,a.tZ)(m.Cv,{permission:"org:sys_domains:read",children:(0,a.tZ)(el,{})}),(0,a.tZ)(er,{}),(0,a.tZ)(es,{})]})}),en=()=>{let{organization:e}=(0,o.o8)();if(!e)return null;let i=(0,a.tZ)(z.Z,{size:"lg",mainIdentifierVariant:"subtitle",organization:e});return(0,a.tZ)(b.zd.Root,{title:(0,u.localizationKeys)("organizationProfile.start.profileSection.title"),id:"organizationProfile",children:(0,a.BX)(Z.a.Root,{children:[(0,a.tZ)(m.Cv,{permission:"org:sys_profile:manage",fallback:i,children:(0,a.tZ)(Z.a.Closed,{value:"edit",children:(0,a.BX)(b.zd.Item,{id:"organizationProfile",children:[i,(0,a.tZ)(Z.a.Trigger,{value:"edit",children:(0,a.tZ)(b.zd.Button,{id:"organizationProfile",localizationKey:(0,u.localizationKeys)("organizationProfile.start.profileSection.primaryButton")})})]})})}),(0,a.tZ)(Z.a.Open,{value:"edit",children:(0,a.tZ)(Z.a.Card,{children:(0,a.tZ)(ee,{})})})]})})},el=()=>{let{organizationSettings:e}=(0,c.useEnvironment)(),{organization:i}=(0,o.o8)();return e&&i&&e.domains.enabled?(0,a.tZ)(b.zd.Root,{title:(0,u.localizationKeys)("organizationProfile.profilePage.domainSection.title"),id:"organizationDomains",centered:!1,children:(0,a.BX)(Z.a.Root,{children:[(0,a.tZ)(U,{}),(0,a.BX)(m.Cv,{permission:"org:sys_domains:manage",children:[(0,a.tZ)(Z.a.Trigger,{value:"add",children:(0,a.BX)(u.Col,{children:[(0,a.tZ)(b.zd.ArrowButton,{localizationKey:(0,u.localizationKeys)("organizationProfile.profilePage.domainSection.primaryButton"),id:"organizationDomains"}),(0,a.tZ)(u.Text,{localizationKey:(0,u.localizationKeys)("organizationProfile.profilePage.domainSection.subtitle"),sx:e=>({paddingLeft:e.space.$9}),colorScheme:"secondary"})]})}),(0,a.tZ)(Z.a.Open,{value:"add",children:(0,a.tZ)(Z.a.Card,{children:(0,a.tZ)(ei,{})})})]})]})}):null},er=()=>{let{organization:e}=(0,o.o8)();return e?(0,a.tZ)(b.zd.Root,{id:"organizationDanger",title:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.title"),children:(0,a.BX)(Z.a.Root,{children:[(0,a.tZ)(Z.a.Closed,{value:"leave",children:(0,a.tZ)(b.zd.Item,{sx:e=>({paddingTop:0,paddingBottom:0,paddingLeft:e.space.$1}),id:"organizationDanger",children:(0,a.tZ)(Z.a.Trigger,{value:"leave",children:(0,a.tZ)(b.zd.Button,{id:"organizationDanger",variant:"ghost",colorScheme:"danger",textVariant:"buttonLarge",localizationKey:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.leaveOrganization.title")})})})}),(0,a.tZ)(Z.a.Open,{value:"leave",children:(0,a.tZ)(Z.a.Card,{variant:"destructive",children:(0,a.tZ)(et,{})})})]})}):null},es=()=>{let{organization:e}=(0,o.o8)(),i=(0,m.N2)({permission:"org:sys_profile:delete"});if(!e)return null;let t=e.adminDeleteEnabled;return i&&t?(0,a.tZ)(b.zd.Root,{id:"organizationDanger",title:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.title"),sx:e=>({marginBottom:e.space.$4}),children:(0,a.BX)(Z.a.Root,{children:[(0,a.tZ)(Z.a.Closed,{value:"delete",children:(0,a.tZ)(b.zd.Item,{sx:e=>({paddingTop:0,paddingBottom:0,paddingLeft:e.space.$1}),id:"organizationDanger",children:(0,a.tZ)(Z.a.Trigger,{value:"delete",children:(0,a.tZ)(b.zd.Button,{id:"organizationDanger",variant:"ghost",colorScheme:"danger",textVariant:"buttonLarge",localizationKey:(0,u.localizationKeys)("organizationProfile.profilePage.dangerSection.deleteOrganization.title")})})})}),(0,a.tZ)(Z.a.Open,{value:"delete",children:(0,a.tZ)(Z.a.Card,{variant:"destructive",children:(0,a.tZ)(ea,{})})})]})}):null};var ed=t(36781),ec=t(44455),eu=t(90708),eg=t(81201),em=t(97295),ep=t(13559),eh=t(85872),ev=t(90408);let ef=e=>{let{memberships:i,pageSize:t}=e,n=(0,l.useCardState)(),{organization:r}=(0,o.o8)(),{options:s,isLoading:d}=(0,eh.e)();if(!r)return null;let c=e=>i=>n.runAsync(()=>e.update({role:i})).catch(e=>(0,_.S3)(e,[],n.setError)),g=e=>async()=>n.runAsync(async()=>{var t;let a=await e.destroy();return await (null==i?void 0:null===(t=i.revalidate)||void 0===t?void 0:t.call(i)),a}).catch(e=>(0,_.S3)(e,[],n.setError));return(0,a.tZ)(ev.wQ,{page:(null==i?void 0:i.page)||1,onPageChange:e=>{var t;return null==i?void 0:null===(t=i.fetchPage)||void 0===t?void 0:t.call(i,e)},itemCount:(null==i?void 0:i.count)||0,pageCount:(null==i?void 0:i.pageCount)||0,itemsPerPage:t,isLoading:(null==i?void 0:i.isLoading)&&!(null==i?void 0:i.data.length)||d,emptyStateLocalizationKey:(0,u.localizationKeys)("organizationProfile.membersPage.detailsTitle__emptyRow"),headers:[(0,u.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__user"),(0,u.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__joined"),(0,u.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__role"),(0,u.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__actions")],rows:((null==i?void 0:i.data)||[]).map(e=>(0,a.tZ)(ez,{membership:e,options:s,onRoleChange:c(e),onRemove:g(e)},e.id))})},ez=e=>{var i,t,n;let{membership:r,onRemove:s,onRoleChange:d,options:c}=e,{localizeCustomRole:g}=(0,eh.q)(),m=(0,l.useCardState)(),{user:p}=(0,o.aF)(),h=(null==p?void 0:p.id)===(null===(i=r.publicUserData)||void 0===i?void 0:i.userId),v=null==c?void 0:null===(t=c.find(e=>e.value===r.role))||void 0===t?void 0:t.label;return(0,a.BX)(ev.RN,{children:[(0,a.tZ)(u.Td,{children:(0,a.tZ)(em.E,{sx:{maxWidth:"30ch"},user:r.publicUserData,subtitle:null===(n=r.publicUserData)||void 0===n?void 0:n.identifier,badge:h&&(0,a.tZ)(u.Badge,{localizationKey:(0,u.localizationKeys)("badge__you")})})}),(0,a.tZ)(u.Td,{children:(0,a.tZ)(u.Box,{as:"span",elementDescriptor:u.descriptors.formattedDate,elementId:u.descriptors.formattedDate.setId("tableCell"),children:r.createdAt.toLocaleDateString()})}),(0,a.tZ)(u.Td,{children:(0,a.tZ)(ep.Cv,{permission:"org:sys_memberships:manage",fallback:(0,a.tZ)(u.Text,{sx:e=>({opacity:e.opacity.$inactive}),children:g(r.role)||v}),children:(0,a.tZ)(ev.DQ,{isDisabled:m.isLoading||!d,value:r.role,fallbackLabel:r.roleName,onChange:d,roles:c})})}),(0,a.tZ)(u.Td,{children:(0,a.tZ)(ep.Cv,{permission:"org:sys_memberships:manage",children:(0,a.tZ)(M.a,{actions:[{label:(0,u.localizationKeys)("organizationProfile.membersPage.activeMembersTab.menuAction__remove"),isDestructive:!0,onClick:s,isDisabled:h}],elementId:"member"})})})]})};var eb=t(45727);let eZ=e=>{let{actionSlot:i}=e,t=(0,m.N2)({permission:"org:sys_memberships:manage"});return(0,a.BX)(Z.a.Root,{animate:!1,children:[(0,a.tZ)(ed.f,{asChild:!0,children:(0,a.BX)(u.Flex,{justify:i?"between":"end",sx:e=>({width:"100%",marginLeft:"auto",padding:"".concat(e.space.$none," ").concat(e.space.$1)}),gap:i?2:void 0,children:[i,t&&(0,a.tZ)(Z.a.Trigger,{value:"invite",hideOnActive:!i,children:(0,a.tZ)(u.Button,{elementDescriptor:u.descriptors.membersPageInviteButton,"aria-label":"Invite",localizationKey:(0,u.localizationKeys)("organizationProfile.membersPage.action__invite")})})]})}),t&&(0,a.tZ)(ed.f,{children:(0,a.tZ)(Z.a.Open,{value:"invite",children:(0,a.tZ)(u.Flex,{sx:e=>({paddingBottom:e.space.$6,padding:"".concat(e.space.$none," ").concat(e.space.$1," ").concat(e.space.$6," ").concat(e.space.$1)}),children:(0,a.tZ)(Z.a.Card,{sx:{width:"100%"},children:(0,a.tZ)(eb.N,{})})})})})]})};var eP=t(932);let ey=()=>{let{organization:e}=(0,o.o8)(),{__unstable_manageBillingUrl:i,__unstable_manageBillingLabel:t,__unstable_manageBillingMembersLimit:n}=(0,c.useOrganizationProfileContext)(),l=(0,g.useRouter)();if(!e)return null;let r=(null==e?void 0:e.membersCount)+(null==e?void 0:e.pendingInvitationsCount),s=(0,Q.OR)(n);return(0,a.tZ)(u.Flex,{sx:e=>({background:e.colors.$neutralAlpha50,padding:e.space.$2,borderRadius:e.radii.$md,gap:e.space.$4}),children:(0,a.BX)(u.Flex,{align:"center",sx:e=>({[eg.mqu.sm]:{gap:e.space.$3},gap:e.space.$2}),children:[s>0&&(0,a.tZ)(eP.a,{limit:s,value:r,size:"xs"}),(0,a.BX)(u.Flex,{sx:e=>({[eg.mqu.sm]:{flexDirection:"column"},gap:e.space.$0x5}),children:[(0,a.BX)(u.Text,{children:["You can invite ",n?"up to ".concat(s):"unlimited"," members."]}),s>0&&(0,a.BX)(u.Link,{sx:e=>({fontWeight:e.fontWeights.$medium}),variant:"body",onClick:()=>l.navigate((0,Q.OR)(i)),children:[(0,Q.OR)(t)||"Manage billing",(0,a.tZ)(u.Icon,{icon:L.LZ})]})]})]})})};var eS=t(81319),eC=t(30215);let ex=e=>{var i;let{query:t,value:o,memberships:l,onSearchChange:r,onQueryTrigger:s}=e,{t:d}=(0,u.useLocalizations)(),c=(0,n.useRef)(null);(0,n.useEffect)(()=>{var e,i;t&&(null==l?void 0:l.data)&&(null!==(e=null==l?void 0:l.count)&&void 0!==e?e:0)<=eX&&(null==l||null===(i=l.fetchPage)||void 0===i||i.call(l,1))},[t,l]);let g=o&&!!(null==l?void 0:l.isLoading)&&!!(null===(i=l.data)||void 0===i?void 0:i.length);return(0,a.tZ)(ed.f,{asChild:!0,children:(0,a.tZ)(u.Flex,{sx:{width:"50%",[eg.mqu.sm]:{width:"auto"}},children:(0,a.tZ)(eS.W,{value:o,type:"search",autoCapitalize:"none",spellCheck:!1,"aria-label":"Search",placeholder:d((0,u.localizationKeys)("organizationProfile.membersPage.action__search")),leftIcon:g?(0,a.tZ)(eC.$j,{size:"xs"}):(0,a.tZ)(u.Icon,{icon:L.Yt,elementDescriptor:u.descriptors.organizationProfileMembersSearchInputIcon}),onKeyUp:function(){c.current&&clearTimeout(c.current),c.current=setTimeout(()=>{s(o.trim())},500)},onChange:e=>{let i=e.target.value;r(i),""===i&&s(i)},elementDescriptor:u.descriptors.organizationProfileMembersSearchInput})})})},e_={invitations:{pageSize:10,keepPreviousData:!0}},eK=()=>{let e=(0,l.useCardState)(),{organization:i,invitations:t}=(0,o.o8)(e_),{options:n,isLoading:r}=(0,eh.e)();if(!i)return null;let s=i=>async()=>e.runAsync(async()=>{var e;return await i.revoke(),await (null==t?void 0:null===(e=t.revalidate)||void 0===e?void 0:e.call(t)),i}).catch(i=>(0,_.S3)(i,[],e.setError));return(0,a.tZ)(ev.wQ,{page:(null==t?void 0:t.page)||1,onPageChange:(null==t?void 0:t.fetchPage)||(()=>null),itemCount:(null==t?void 0:t.count)||0,pageCount:(null==t?void 0:t.pageCount)||0,itemsPerPage:e_.invitations.pageSize,isLoading:(null==t?void 0:t.isLoading)||r,emptyStateLocalizationKey:(0,u.localizationKeys)("organizationProfile.membersPage.invitationsTab.table__emptyRow"),headers:[(0,u.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__user"),(0,u.localizationKeys)("organizationProfile.membersPage.invitedMembersTab.tableHeader__invited"),(0,u.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__role"),(0,u.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__actions")],rows:((null==t?void 0:t.data)||[]).map(e=>(0,a.tZ)(eB,{options:n,invitation:e,onRevoke:s(e)},e.id))})},eB=e=>{var i;let{invitation:t,onRevoke:o,options:n}=e,{localizeCustomRole:l}=(0,eh.q)(),r=null==n?void 0:null===(i=n.find(e=>e.value===t.role))||void 0===i?void 0:i.label;return(0,a.BX)(ev.RN,{children:[(0,a.tZ)(u.Td,{children:(0,a.tZ)(em.E,{sx:{maxWidth:"30ch"},user:{primaryEmailAddress:{emailAddress:t.emailAddress}}})}),(0,a.tZ)(u.Td,{children:(0,a.tZ)(u.Box,{as:"span",elementDescriptor:u.descriptors.formattedDate,elementId:u.descriptors.formattedDate.setId("tableCell"),children:t.createdAt.toLocaleDateString()})}),(0,a.tZ)(u.Td,{children:(0,a.tZ)(u.Text,{colorScheme:"secondary",localizationKey:l(t.role)||r})}),(0,a.tZ)(u.Td,{children:(0,a.tZ)(M.a,{actions:[{label:(0,u.localizationKeys)("organizationProfile.membersPage.invitedMembersTab.menuAction__revoke"),isDestructive:!0,onClick:o}],elementId:"invitation"})})]})},eD=(0,l.withCardStateProvider)(()=>{var e;let{organizationSettings:i}=(0,c.useEnvironment)(),{__unstable_manageBillingUrl:t,navigateToGeneralPageRoot:o}=(0,c.useOrganizationProfileContext)(),n=null==i?void 0:null===(e=i.domains)||void 0===e?void 0:e.enabled;return(0,a.BX)(u.Col,{gap:4,sx:{width:"100%"},children:[t&&(0,a.tZ)(ey,{}),n&&(0,a.tZ)(m.Cv,{permission:"org:sys_domains:manage",children:(0,a.BX)(u.Flex,{sx:e=>({width:"100%",gap:e.space.$8,paddingBottom:e.space.$4,paddingLeft:e.space.$1,paddingRight:e.space.$1,borderBottomWidth:e.borderWidths.$normal,borderBottomStyle:e.borderStyles.$solid,borderBottomColor:e.colors.$neutralAlpha100,[eg.mqu.md]:{flexDirection:"column",gap:e.space.$2}}),children:[(0,a.tZ)(u.Col,{sx:e=>({width:e.space.$66,marginTop:e.space.$2}),children:(0,a.tZ)(f.h.Root,{children:(0,a.tZ)(f.h.Title,{localizationKey:(0,u.localizationKeys)("organizationProfile.membersPage.invitationsTab.autoInvitations.headerTitle"),textVariant:"h3"})})}),(0,a.tZ)(u.Col,{sx:{width:"100%"},children:(0,a.tZ)(U,{fallback:(0,a.BX)(a.HY,{children:[(0,a.tZ)(b.zd.ArrowButton,{localizationKey:(0,u.localizationKeys)("organizationProfile.membersPage.invitationsTab.autoInvitations.primaryButton"),id:"manageVerifiedDomains",sx:e=>({gap:e.space.$2}),onClick:o}),(0,a.tZ)(u.Text,{localizationKey:(0,u.localizationKeys)("organizationProfile.membersPage.invitationsTab.autoInvitations.headerSubtitle"),sx:e=>({paddingLeft:e.space.$10,color:e.colors.$colorTextSecondary,[eg.mqu.md]:{paddingLeft:0}})})]}),verificationStatus:"verified",enrollmentMode:"automatic_invitation"})})]})}),(0,a.BX)(u.Flex,{direction:"col",gap:2,sx:{width:"100%"},children:[(0,a.tZ)(eZ,{}),(0,a.tZ)(eK,{})]})]})}),eR={membershipRequests:{pageSize:10,keepPreviousData:!0}},eT=()=>{var e;let i=(0,l.useCardState)(),{organization:t,membershipRequests:n}=(0,o.o8)(eR);return t?(0,a.tZ)(ev.wQ,{page:(null==n?void 0:n.page)||1,onPageChange:null!==(e=null==n?void 0:n.fetchPage)&&void 0!==e?e:()=>null,itemCount:(null==n?void 0:n.count)||0,pageCount:(null==n?void 0:n.pageCount)||0,itemsPerPage:eR.membershipRequests.pageSize,isLoading:null==n?void 0:n.isLoading,emptyStateLocalizationKey:(0,u.localizationKeys)("organizationProfile.membersPage.requestsTab.table__emptyRow"),headers:[(0,u.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__user"),(0,u.localizationKeys)("organizationProfile.membersPage.requestsTab.tableHeader__requested"),(0,u.localizationKeys)("organizationProfile.membersPage.activeMembersTab.tableHeader__actions")],rows:((null==n?void 0:n.data)||[]).map(e=>(0,a.tZ)(ew,{request:e,onError:i.setError},e.id))}):null},ew=(0,l.withCardStateProvider)(e=>{let{request:i,onError:t}=e,n=(0,l.useCardState)(),{membership:r,membershipRequests:s}=(0,o.o8)(eR);return(0,a.BX)(ev.RN,{children:[(0,a.tZ)(u.Td,{children:(0,a.tZ)(em.E,{sx:{maxWidth:"30ch"},showAvatar:!1,user:{primaryEmailAddress:{emailAddress:i.publicUserData.identifier}}})}),(0,a.tZ)(u.Td,{children:(0,a.tZ)(u.Box,{as:"span",elementDescriptor:u.descriptors.formattedDate,elementId:u.descriptors.formattedDate.setId("tableCell"),children:i.createdAt.toLocaleDateString()})}),(0,a.tZ)(u.Td,{children:(0,a.tZ)(eI,{onAccept:()=>{if(r&&s)return n.runAsync(async()=>{await i.accept(),await s.revalidate()},"accept").catch(e=>(0,_.S3)(e,[],t))},onReject:()=>{if(r&&s)return n.runAsync(async()=>{await i.reject(),await s.revalidate()},"reject").catch(e=>(0,_.S3)(e,[],t))}})})]})}),eI=e=>{let i=(0,l.useCardState)();return(0,a.BX)(u.Flex,{gap:2,children:[(0,a.tZ)(u.Button,{textVariant:"buttonSmall",variant:"ghost",isLoading:i.isLoading&&"reject"===i.loadingMetadata,isDisabled:i.isLoading&&"reject"!==i.loadingMetadata,onClick:e.onReject,localizationKey:(0,u.localizationKeys)("organizationProfile.membersPage.requestsTab.menuAction__reject")}),(0,a.tZ)(u.Button,{textVariant:"buttonSmall",isLoading:i.isLoading&&"accept"===i.loadingMetadata,isDisabled:i.isLoading&&"accept"!==i.loadingMetadata,onClick:e.onAccept,localizationKey:(0,u.localizationKeys)("organizationProfile.membersPage.requestsTab.menuAction__approve")})]})},eL=()=>{var e;let{organizationSettings:i}=(0,c.useEnvironment)(),{__unstable_manageBillingUrl:t,navigateToGeneralPageRoot:o}=(0,c.useOrganizationProfileContext)(),n=null==i?void 0:null===(e=i.domains)||void 0===e?void 0:e.enabled;return(0,a.BX)(u.Col,{gap:4,sx:{width:"100%"},children:[t&&(0,a.tZ)(ey,{}),n&&(0,a.tZ)(m.Cv,{permission:"org:sys_domains:manage",children:(0,a.BX)(u.Flex,{sx:e=>({width:"100%",gap:e.space.$8,paddingBottom:e.space.$4,paddingLeft:e.space.$1,paddingRight:e.space.$1,borderBottomWidth:e.borderWidths.$normal,borderBottomStyle:e.borderStyles.$solid,borderBottomColor:e.colors.$neutralAlpha100,[eg.mqu.md]:{flexDirection:"column",gap:e.space.$2}}),children:[(0,a.tZ)(u.Col,{sx:e=>({width:e.space.$66,marginTop:e.space.$2}),children:(0,a.tZ)(f.h.Root,{children:(0,a.tZ)(f.h.Title,{localizationKey:(0,u.localizationKeys)("organizationProfile.membersPage.requestsTab.autoSuggestions.headerTitle"),textVariant:"h3"})})}),(0,a.tZ)(u.Col,{sx:{width:"100%"},children:(0,a.tZ)(U,{fallback:(0,a.BX)(a.HY,{children:[(0,a.tZ)(b.zd.ArrowButton,{localizationKey:(0,u.localizationKeys)("organizationProfile.membersPage.requestsTab.autoSuggestions.primaryButton"),sx:e=>({gap:e.space.$2}),id:"manageVerifiedDomains",onClick:o}),(0,a.tZ)(u.Text,{localizationKey:(0,u.localizationKeys)("organizationProfile.membersPage.requestsTab.autoSuggestions.headerSubtitle"),sx:e=>({paddingLeft:e.space.$10,color:e.colors.$colorTextSecondary,[eg.mqu.md]:{paddingLeft:0}})})]}),verificationStatus:"verified",enrollmentMode:"automatic_suggestion"})})]})}),(0,a.BX)(u.Flex,{direction:"col",gap:2,sx:{width:"100%"},children:[(0,a.tZ)(eZ,{}),(0,a.tZ)(eT,{})]})]})},eX=10,ek=(0,l.withCardStateProvider)(()=>{var e;let{organizationSettings:i}=(0,c.useEnvironment)(),t=(0,l.useCardState)(),r=(0,m.N2)({permission:"org:sys_memberships:manage"}),s=(0,m.N2)({permission:"org:sys_memberships:read"}),d=(null==i?void 0:null===(e=i.domains)||void 0===e?void 0:e.enabled)&&r,[g,p]=(0,n.useState)(""),[h,v]=(0,n.useState)(""),{membershipRequests:z,memberships:b,invitations:P}=(0,o.o8)({membershipRequests:d||void 0,invitations:r||void 0,memberships:s?{keepPreviousData:!0,query:g||void 0}:void 0}),{__unstable_manageBillingUrl:y}=(0,c.useOrganizationProfileContext)();return null===r?null:(0,a.tZ)(u.Col,{elementDescriptor:u.descriptors.page,gap:2,children:(0,a.tZ)(u.Col,{elementDescriptor:u.descriptors.profilePage,elementId:u.descriptors.profilePage.setId("organizationMembers"),gap:4,children:(0,a.BX)(Z.a.Root,{animate:!1,children:[(0,a.tZ)(ed.f,{asChild:!0,children:(0,a.tZ)(f.h.Root,{contentSx:{[eg.mqu.md]:{flexDirection:"row",width:"100%",justifyContent:"space-between"}},children:(0,a.tZ)(f.h.Title,{localizationKey:(0,u.localizationKeys)("organizationProfile.start.headerTitle__members"),textVariant:"h2"})})}),(0,a.tZ)(ec.Z.Alert,{children:t.error}),(0,a.BX)(eu.mQ,{children:[(0,a.BX)(eu.dr,{sx:e=>({gap:e.space.$2}),children:[s&&(0,a.tZ)(eu.OK,{localizationKey:(0,u.localizationKeys)("organizationProfile.membersPage.start.headerTitle__members"),children:!!(null==b?void 0:b.count)&&(0,a.tZ)(m.dN,{shouldAnimate:!g,notificationCount:b.count,colorScheme:"outline"})}),r&&(0,a.tZ)(eu.OK,{localizationKey:(0,u.localizationKeys)("organizationProfile.membersPage.start.headerTitle__invitations"),children:(null==P?void 0:P.data)&&!P.isLoading&&(0,a.tZ)(m.dN,{notificationCount:P.count,colorScheme:"outline"})}),r&&d&&(0,a.tZ)(eu.OK,{localizationKey:(0,u.localizationKeys)("organizationProfile.membersPage.start.headerTitle__requests"),children:(null==z?void 0:z.data)&&!z.isLoading&&(0,a.tZ)(m.dN,{notificationCount:z.count,colorScheme:"outline"})})]}),(0,a.BX)(eu.nP,{children:[s&&(0,a.tZ)(eu.x4,{sx:{width:"100%"},children:(0,a.BX)(u.Flex,{gap:4,direction:"col",sx:{width:"100%"},children:[r&&y&&(0,a.tZ)(ey,{}),(0,a.BX)(u.Flex,{gap:2,direction:"col",sx:{width:"100%"},children:[(0,a.tZ)(eZ,{actionSlot:(0,a.tZ)(ex,{query:g,value:h,memberships:b,onSearchChange:e=>v(e),onQueryTrigger:e=>p(e)})}),(0,a.tZ)(ef,{pageSize:eX,memberships:b})]})]})}),r&&(0,a.tZ)(eu.x4,{sx:{width:"100%"},children:(0,a.tZ)(eD,{})}),r&&d&&(0,a.tZ)(eu.x4,{sx:{width:"100%"},children:(0,a.tZ)(eL,{})})]})]})]})})})});var eO=t(12264);let e$=()=>(0,a.tZ)(c.SubscriberTypeContext.Provider,{value:"org",children:(0,a.tZ)(eO.r,{})});t(45261),t(70957),t(24551),t(22349);var eA=t(9117),eE=t(65006);let eM=()=>{let{navigate:e}=(0,g.useRouter)();return(0,a.BX)(a.HY,{children:[(0,a.tZ)(f.h.Root,{sx:e=>({borderBottomWidth:e.borderWidths.$normal,borderBottomStyle:e.borderStyles.$solid,borderBottomColor:e.colors.$neutralAlpha100,marginBlockEnd:e.space.$4,paddingBlockEnd:e.space.$4}),children:(0,a.tZ)(f.h.BackLink,{onClick:()=>void e("../",{searchParams:new URLSearchParams("tab=subscriptions")}),children:(0,a.tZ)(f.h.Title,{localizationKey:(0,p.u1)("organizationProfile.plansPage.title"),textVariant:"h2"})})}),(0,a.BX)(u.Flex,{direction:"col",gap:4,children:[(0,a.tZ)(m.Cv,{condition:e=>!e({permission:"org:sys_billing:manage"}),children:(0,a.tZ)(eA.b,{variant:"info",colorScheme:"info",title:(0,p.u1)("organizationProfile.plansPage.alerts.noPermissionsToManageBilling")})}),(0,a.tZ)(c.PricingTableContext.Provider,{value:{componentName:"PricingTable",mode:"modal"},children:(0,a.tZ)(eE.b,{})})]})]})},eF=()=>(0,a.tZ)(c.SubscriberTypeContext.Provider,{value:"org",children:(0,a.tZ)(eM,{})});var eq=t(15515);let eN=()=>(0,a.tZ)(c.SubscriberTypeContext.Provider,{value:"org",children:(0,a.tZ)(eq.j,{})}),eV=(0,n.lazy)(()=>Promise.all([t.e("507"),t.e("200"),t.e("573"),t.e("916")]).then(t.bind(t,14961)).then(e=>({default:e.OrganizationBillingPage}))),eY=(0,n.lazy)(()=>Promise.all([t.e("200"),t.e("573"),t.e("616"),t.e("150")]).then(t.bind(t,53879)).then(e=>({default:e.OrganizationAPIKeysPage}))),ej=()=>{var e;let{pages:i,isMembersPageRoot:t,isGeneralPageRoot:o,isBillingPageRoot:l,isApiKeysPageRoot:r}=(0,c.useOrganizationProfileContext)(),{apiKeysSettings:s,commerceSettings:d}=(0,c.useEnvironment)(),u=null===(e=i.contents)||void 0===e?void 0:e.map((e,i)=>{let n=!o&&!t&&0===i;return(0,a.tZ)(g.Route,{index:n,path:n?void 0:e.url,children:(0,a.tZ)(v.O,{mount:e.mount,unmount:e.unmount})},"custom-page-".concat(e.url))});return(0,a.BX)(g.Switch,{children:[u,(0,a.BX)(g.Route,{children:[(0,a.tZ)(g.Route,{path:o?void 0:"organization-general",children:(0,a.tZ)(g.Switch,{children:(0,a.tZ)(g.Route,{index:!0,children:(0,a.tZ)(eo,{})})})}),(0,a.tZ)(g.Route,{path:t?void 0:"organization-members",children:(0,a.tZ)(g.Switch,{children:(0,a.tZ)(g.Route,{index:!0,children:(0,a.tZ)(m.Cv,{condition:e=>e({permission:"org:sys_memberships:read"})||e({permission:"org:sys_memberships:manage"}),redirectTo:o?"../":"./organization-general",children:(0,a.tZ)(ek,{})})})})}),d.billing.enabled&&d.billing.hasPaidOrgPlans&&(0,a.tZ)(m.Cv,{condition:e=>e({permission:"org:sys_billing:read"})||e({permission:"org:sys_billing:manage"}),children:(0,a.tZ)(g.Route,{path:l?void 0:"organization-billing",children:(0,a.BX)(g.Switch,{children:[(0,a.tZ)(g.Route,{index:!0,children:(0,a.tZ)(n.Suspense,{fallback:"",children:(0,a.tZ)(eV,{})})}),(0,a.tZ)(g.Route,{path:"plans",children:(0,a.tZ)(n.Suspense,{fallback:"",children:(0,a.tZ)(eF,{})})}),(0,a.tZ)(g.Route,{path:"statement/:statementId",children:(0,a.tZ)(n.Suspense,{fallback:"",children:(0,a.tZ)(eN,{})})}),(0,a.tZ)(g.Route,{path:"payment-attempt/:paymentAttemptId",children:(0,a.tZ)(n.Suspense,{fallback:"",children:(0,a.tZ)(e$,{})})})]})})}),s.enabled&&(0,a.tZ)(g.Route,{path:r?void 0:"organization-api-keys",children:(0,a.tZ)(g.Switch,{children:(0,a.tZ)(g.Route,{index:!0,children:(0,a.tZ)(n.Suspense,{fallback:"",children:(0,a.tZ)(eY,{})})})})})]})]})},eH=(0,c.withCoreUserGuard)(()=>{let e=n.useRef(null);return(0,a.tZ)(s.P.Root,{sx:e=>({display:"grid",gridTemplateColumns:"1fr 3fr",height:e.sizes.$176,overflow:"hidden"}),children:(0,a.BX)(h,{contentRef:e,children:[(0,a.tZ)(r.ap,{navbarTitleLocalizationKey:(0,u.localizationKeys)("organizationProfile.navbar.title")}),(0,a.tZ)(s.P.Content,{contentRef:e,scrollBoxId:d.aw,children:(0,a.tZ)(ej,{})})]})})}),eW=(0,l.withCardStateProvider)(e=>{let{organization:i}=(0,o.o8)();return i?(0,a.tZ)(u.Flow.Root,{flow:"organizationProfile",children:(0,a.tZ)(u.Flow.Part,{children:(0,a.tZ)(g.Switch,{children:(0,a.tZ)(g.Route,{children:(0,a.tZ)(eH,{})})})})}):null}),eU=e=>{let i={...e,routing:"virtual",componentName:"OrganizationProfile",mode:"modal"};return(0,a.tZ)(g.Route,{path:"organizationProfile",children:(0,a.tZ)(c.OrganizationProfileContext.Provider,{value:i,children:(0,a.tZ)("div",{children:(0,a.tZ)(eW,{...i})})})})}}}]);