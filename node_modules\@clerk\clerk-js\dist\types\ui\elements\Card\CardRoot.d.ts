import React from 'react';
import type { ElementDescriptor } from '../../customizables/elementDescriptors';
export declare const CardRoot: React.ForwardRefExoticComponent<Omit<Omit<import("../../primitives").FlexProps, "ref"> & React.RefAttributes<HTMLDivElement> & {
    elementDescriptor?: ElementDescriptor | Array<ElementDescriptor | undefined>;
    elementId?: import("../../customizables/elementDescriptors").ElementId;
    css?: never;
    sx?: import("../../styledSystem").ThemableCssProp;
}, "ref"> & React.RefAttributes<HTMLDivElement>>;
