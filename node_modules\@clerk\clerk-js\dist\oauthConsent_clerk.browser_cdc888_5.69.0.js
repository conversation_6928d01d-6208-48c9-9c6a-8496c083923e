"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["776"],{429:function(e,t,o){o.r(t),o.d(t,{OAuthConsent:()=>C,OAuthConsentInternal:()=>y});var r=o(9109),n=o(3799),i=o(9144),l=o(1576),a=o(9541),c=o(3174),s=o(9629),d=o(4455),u=o(2672),h=o(2654),p=o(6459),g=o(9732),x=o(4174),b=o(215),$=o(1201),Z=o(7623);function y(){let{scopes:e,oAuthApplicationName:t,oAuthApplicationLogoUrl:o,redirectUrl:u,onDeny:p,onAllow:x}=(0,l.useOAuthConsentContext)(),{user:b}=(0,n.aF)(),{applicationName:y,logoImageUrl:C}=(0,l.useEnvironment)().displayConfig,[w,R]=(0,i.useState)(!1),S=b?.emailAddresses.find(e=>e.id===b.primaryEmailAddress?.id);function T(){try{let{hostname:e}=new URL(u);return e.split(".").slice(-2).join(".")}catch{return""}}return(0,r.BX)(a.Flow.Root,{flow:"oauthConsent",children:[(0,r.BX)(d.Z.Root,{children:[(0,r.BX)(d.Z.Content,{children:[(0,r.BX)(h.h.Root,{children:[o&&C&&(0,r.BX)(B,{children:[(0,r.tZ)(s.q,{imageUrl:o,size:e=>e.space.$12,rounded:!1}),(0,r.tZ)(f,{}),(0,r.tZ)(c.u,{})]}),o&&!C&&(0,r.tZ)(B,{children:(0,r.BX)(a.Box,{sx:{position:"relative"},children:[(0,r.tZ)(s.q,{imageUrl:o,size:e=>e.space.$12,rounded:!1}),(0,r.tZ)(k,{size:"sm",sx:e=>({position:"absolute",bottom:`calc(${e.space.$3} * -1)`,right:`calc(${e.space.$3} * -1)`})})]})}),!o&&C&&(0,r.BX)(a.Flex,{justify:"center",align:"center",gap:4,sx:e=>({marginBlockEnd:e.space.$6}),children:[(0,r.tZ)(k,{}),(0,r.tZ)(f,{}),(0,r.tZ)(c.u,{})]}),!o&&!C&&(0,r.tZ)(a.Flex,{justify:"center",align:"center",gap:4,sx:e=>({marginBlockEnd:e.space.$6}),children:(0,r.tZ)(k,{})}),(0,r.tZ)(h.h.Title,{localizationKey:t}),(0,r.tZ)(h.h.Subtitle,{localizationKey:`wants to access ${y} on behalf of ${S}`})]}),(0,r.BX)(a.Box,{sx:e=>({textAlign:"left",borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha100,borderRadius:e.radii.$lg,overflow:"hidden"}),children:[(0,r.tZ)(a.Box,{sx:e=>({padding:e.space.$3,background:$.common.mergedColorsBackground(Z.O9.setAlpha(e.colors.$colorBackground,1),e.colors.$neutralAlpha50)}),children:(0,r.tZ)(a.Text,{variant:"subtitle",localizationKey:`This will allow ${t} access to:`})}),(0,r.tZ)(a.Box,{as:"ul",sx:e=>({margin:e.sizes.$none,padding:e.sizes.$none}),children:(e||[]).map(e=>(0,r.tZ)(a.Box,{sx:e=>({display:"flex",alignItems:"baseline",paddingInline:e.space.$3,paddingBlock:e.space.$2,borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100,"&::before":{content:'""',display:"inline-block",width:e.space.$1,height:e.space.$1,background:e.colors.$colorTextSecondary,borderRadius:e.radii.$circle,transform:"translateY(-0.1875rem)",marginRight:e.space.$2,flexShrink:0}}),as:"li",children:(0,r.tZ)(a.Text,{variant:"subtitle",localizationKey:e.description||e.scope||""})},e.scope))})]}),(0,r.tZ)(a.Box,{sx:e=>({background:"rgba(243, 107, 22, 0.12)",padding:e.space.$4,borderRadius:e.radii.$lg}),children:(0,r.BX)(a.Text,{colorScheme:"warning",variant:"caption",children:["Make sure that you trust ",t," ","",(0,r.BX)(g.u.Root,{children:[(0,r.tZ)(g.u.Trigger,{children:(0,r.BX)(a.Text,{as:"span",role:"button",tabIndex:0,"aria-label":"View full URL",variant:"caption",sx:{textDecoration:"underline",textDecorationStyle:"dotted",cursor:"pointer",outline:"none",display:"inline-block"},onClick:()=>R(!0),children:["(",T(),")"]})}),(0,r.tZ)(g.u.Content,{text:"View full URL"})]}),"",". You may be sharing sensitive data with this site or app."]})}),(0,r.BX)(a.Grid,{columns:2,gap:3,children:[(0,r.tZ)(a.Button,{colorScheme:"secondary",variant:"outline",localizationKey:"Deny",onClick:p}),(0,r.tZ)(a.Button,{localizationKey:"Allow",onClick:x}),(0,r.BX)(a.Text,{sx:{gridColumn:"span 2"},colorScheme:"secondary",variant:"caption",children:["If you allow access, this app will redirect you to"," ",(0,r.BX)(g.u.Root,{children:[(0,r.tZ)(g.u.Trigger,{children:(0,r.tZ)(a.Text,{as:"span",role:"button",tabIndex:0,"aria-label":"View full URL",variant:"caption",sx:{textDecoration:"underline",textDecorationStyle:"dotted",cursor:"pointer",outline:"none",display:"inline-block"},onClick:()=>R(!0),children:T()})}),(0,r.tZ)(g.u.Content,{text:"View full URL"})]})]})]})]}),(0,r.tZ)(d.Z.Footer,{})]}),(0,r.tZ)(m,{isOpen:w,onOpen:()=>R(!0),onClose:()=>R(!1),redirectUri:u,oAuthApplicationName:t})]})}function m({onOpen:e,onClose:t,isOpen:o,redirectUri:n,oAuthApplicationName:i}){return o?(0,r.tZ)(p.Modal,{handleOpen:e,handleClose:t,children:(0,r.tZ)(d.Z.Root,{children:(0,r.BX)(d.Z.Content,{children:[(0,r.BX)(h.h.Root,{children:[(0,r.tZ)(h.h.Title,{localizationKey:"Redirect URL"}),(0,r.tZ)(h.h.Subtitle,{localizationKey:`Make sure you trust ${i} and that this URL belongs to ${i}.`})]}),(0,r.tZ)(b.gx,{style:{maxHeight:"none"},cols:50,rows:6,defaultValue:n,readOnly:!0})]})})}):null}function B({children:e}){return(0,r.tZ)(a.Flex,{justify:"center",align:"center",gap:4,sx:e=>({marginBlockEnd:e.space.$6}),children:e})}function k({size:e="md",sx:t}){let o=t=>{let o="sm"===e?t.space.$6:t.space.$12;return{width:o,height:o}};return(0,r.tZ)(a.Box,{sx:e=>[{background:$.common.mergedColorsBackground(Z.O9.setAlpha(e.colors.$colorBackground,1),e.colors.$neutralAlpha50),borderRadius:e.radii.$circle,borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha100,display:"flex",alignItems:"center",justifyContent:"center"},o,t],children:(0,r.tZ)(a.Icon,{icon:x.LG,sx:e=>({color:e.colors.$primary500})})})}function f(){return(0,r.tZ)(a.Box,{as:"svg",fill:"none",viewBox:"0 0 16 2",height:2,"aria-hidden":!0,sx:e=>({color:e.colors.$colorTextSecondary}),children:(0,r.tZ)("path",{stroke:"currentColor",strokeDasharray:"0.1 4",strokeLinecap:"round",strokeWidth:"2",d:"M1 1h14"})})}let C=(0,u.withCardStateProvider)(y)}}]);