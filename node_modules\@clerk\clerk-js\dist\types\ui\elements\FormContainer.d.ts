import type { LocalizationKey } from '../customizables';
import { Col } from '../customizables';
import type { PropsOfComponent } from '../styledSystem';
import { Header } from './Header';
export type FormProps = {
    onSuccess: () => void;
    onReset: () => void;
};
type PageProps = PropsOfComponent<typeof Col> & {
    headerTitle?: LocalizationKey | string;
    headerTitleTextVariant?: PropsOfComponent<typeof Header.Title>['textVariant'];
    headerSubtitle?: LocalizationKey;
    headerSubtitleTextVariant?: PropsOfComponent<typeof Header.Subtitle>['variant'];
};
export declare const FormContainer: (props: PageProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
