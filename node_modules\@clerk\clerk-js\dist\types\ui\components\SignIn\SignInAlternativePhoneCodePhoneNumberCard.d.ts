import type { PhoneCodeChannelData } from '@clerk/types';
import type { FormControlState } from '../../utils';
type SignUpAlternativePhoneCodePhoneNumberCardProps = {
    handleSubmit: React.FormEventHandler;
    phoneNumberFormState: FormControlState<any>;
    onUseAnotherMethod: () => void;
    phoneCodeProvider: PhoneCodeChannelData;
};
export declare const SignInAlternativePhoneCodePhoneNumberCard: (props: SignUpAlternativePhoneCodePhoneNumberCardProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
