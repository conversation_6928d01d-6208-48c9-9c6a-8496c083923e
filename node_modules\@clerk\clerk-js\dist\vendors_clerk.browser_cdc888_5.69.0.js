(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["200"],{5582:function(e,t,n){"use strict";n.d(t,{F4:()=>i,iv:()=>o}),n(9144),n(4191);var r=n(7021);function o(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.O)(t)}n(1025),n(3772);var i=function(){var e=o.apply(void 0,arguments),t="animation-"+e.name;return{name:t,styles:"@keyframes "+t+"{"+e.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}}},1720:function(e,t,n){"use strict";function r(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=function e(t){var n,r,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t))for(n=0;n<t.length;n++)t[n]&&(r=e(t[n]))&&(o&&(o+=" "),o+=r);else for(n in t)t[n]&&(o&&(o+=" "),o+=n)}return o}(e))&&(r&&(r+=" "),r+=t);return r}n.d(t,{W:()=>r})},472:function(e,t,n){"use strict";var r=n(5683),o={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var n,i,l,u,a,s,c,f,d=!1;t||(t={}),l=t.debug||!1;try{if(a=r(),s=document.createRange(),c=document.getSelection(),(f=document.createElement("span")).textContent=e,f.ariaHidden="true",f.style.all="unset",f.style.position="fixed",f.style.top=0,f.style.clip="rect(0, 0, 0, 0)",f.style.whiteSpace="pre",f.style.webkitUserSelect="text",f.style.MozUserSelect="text",f.style.msUserSelect="text",f.style.userSelect="text",f.addEventListener("copy",function(n){if(n.stopPropagation(),t.format){if(n.preventDefault(),void 0===n.clipboardData){l&&console.warn("unable to use e.clipboardData"),l&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var r=o[t.format]||o.default;window.clipboardData.setData(r,e)}else n.clipboardData.clearData(),n.clipboardData.setData(t.format,e)}t.onCopy&&(n.preventDefault(),t.onCopy(n.clipboardData))}),document.body.appendChild(f),s.selectNodeContents(f),c.addRange(s),!document.execCommand("copy"))throw Error("copy command was unsuccessful");d=!0}catch(r){l&&console.error("unable to copy using execCommand: ",r),l&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),d=!0}catch(r){l&&console.error("unable to copy using clipboardData: ",r),l&&console.error("falling back to prompt"),n="message"in t?t.message:"Copy to clipboard: #{key}, Enter",i=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",u=n.replace(/#{\s*key\s*}/g,i),window.prompt(u,e)}}finally{c&&("function"==typeof c.removeRange?c.removeRange(s):c.removeAllRanges()),f&&document.body.removeChild(f),a()}return d}},7383:function(e,t,n){var r;r=function(){var e=e||function(e,t){if("undefined"!=typeof window&&window.crypto&&(r=window.crypto),"undefined"!=typeof self&&self.crypto&&(r=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(r=globalThis.crypto),!r&&"undefined"!=typeof window&&window.msCrypto&&(r=window.msCrypto),!r&&void 0!==n.g&&n.g.crypto&&(r=n.g.crypto),!r)try{r=n(9532)}catch(e){}var r,o=function(){if(r){if("function"==typeof r.getRandomValues)try{return r.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof r.randomBytes)try{return r.randomBytes(4).readInt32LE()}catch(e){}}throw Error("Native crypto module could not be used to get secure random number.")},i=Object.create||function(){function e(){}return function(t){var n;return e.prototype=t,n=new e,e.prototype=null,n}}(),l={},u=l.lib={},a=u.Base={extend:function(e){var t=i(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},s=u.WordArray=a.extend({init:function(e,n){e=this.words=e||[],t!=n?this.sigBytes=n:this.sigBytes=4*e.length},toString:function(e){return(e||f).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes,o=e.sigBytes;if(this.clamp(),r%4)for(var i=0;i<o;i++){var l=n[i>>>2]>>>24-i%4*8&255;t[r+i>>>2]|=l<<24-(r+i)%4*8}else for(var u=0;u<o;u+=4)t[r+u>>>2]=n[u>>>2];return this.sigBytes+=o,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=0xffffffff<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=a.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],n=0;n<e;n+=4)t.push(o());return new s.init(t,e)}}),c=l.enc={},f=c.Hex={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o++){var i=t[o>>>2]>>>24-o%4*8&255;r.push((i>>>4).toString(16)),r.push((15&i).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new s.init(n,t/2)}},d=c.Latin1={stringify:function(e){for(var t=e.words,n=e.sigBytes,r=[],o=0;o<n;o++){var i=t[o>>>2]>>>24-o%4*8&255;r.push(String.fromCharCode(i))}return r.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new s.init(n,t)}},h=c.Utf8={stringify:function(e){try{return decodeURIComponent(escape(d.stringify(e)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function(e){return d.parse(unescape(encodeURIComponent(e)))}},p=u.BufferedBlockAlgorithm=a.extend({reset:function(){this._data=new s.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=h.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n,r=this._data,o=r.words,i=r.sigBytes,l=this.blockSize,u=i/(4*l),a=(u=t?e.ceil(u):e.max((0|u)-this._minBufferSize,0))*l,c=e.min(4*a,i);if(a){for(var f=0;f<a;f+=l)this._doProcessBlock(o,f);n=o.splice(0,a),r.sigBytes-=c}return new s.init(n,c)},clone:function(){var e=a.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});u.Hasher=p.extend({cfg:a.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new m.HMAC.init(e,n).finalize(t)}}});var m=l.algo={};return l}(Math);return e},e.exports=r()},7202:function(e,t,n){var r;r=function(e){var t;return t=e.lib.WordArray,e.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp();for(var o=[],i=0;i<n;i+=3)for(var l=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,u=0;u<4&&i+.75*u<n;u++)o.push(r.charAt(l>>>6*(3-u)&63));var a=r.charAt(64);if(a)for(;o.length%4;)o.push(a);return o.join("")},parse:function(e){var n=e.length,r=this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var i=0;i<r.length;i++)o[r.charCodeAt(i)]=i}var l=r.charAt(64);if(l){var u=e.indexOf(l);-1!==u&&(n=u)}return function(e,n,r){for(var o=[],i=0,l=0;l<n;l++)if(l%4){var u=r[e.charCodeAt(l-1)]<<l%4*2|r[e.charCodeAt(l)]>>>6-l%4*2;o[i>>>2]|=u<<24-i%4*8,i++}return t.create(o,i)}(e,n,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},e.enc.Base64},e.exports=r(n(7383))},394:function(e,t,n){var r;r=function(e){var t,n,r,o,i,l;return n=(t=e.lib).WordArray,r=t.Hasher,o=e.algo,i=[],l=o.SHA1=r.extend({_doReset:function(){this._hash=new n.init([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],o=n[1],l=n[2],u=n[3],a=n[4],s=0;s<80;s++){if(s<16)i[s]=0|e[t+s];else{var c=i[s-3]^i[s-8]^i[s-14]^i[s-16];i[s]=c<<1|c>>>31}var f=(r<<5|r>>>27)+a+i[s];s<20?f+=(o&l|~o&u)+0x5a827999:s<40?f+=(o^l^u)+0x6ed9eba1:s<60?f+=(o&l|o&u|l&u)-0x70e44324:f+=(o^l^u)-0x359d3e2a,a=u,u=l,l=o<<30|o>>>2,o=r,r=f}n[0]=n[0]+r|0,n[1]=n[1]+o|0,n[2]=n[2]+l|0,n[3]=n[3]+u|0,n[4]=n[4]+a|0},_doFinalize:function(){var e=this._data,t=e.words,n=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[(r+64>>>9<<4)+14]=Math.floor(n/0x100000000),t[(r+64>>>9<<4)+15]=n,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}}),e.SHA1=r._createHelper(l),e.HmacSHA1=r._createHmacHelper(l),e.SHA1},e.exports=r(n(7383))},5683:function(e){e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],r=0;r<e.rangeCount;r++)n.push(e.getRangeAt(r));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach(function(t){e.addRange(t)}),t&&t.focus()}}},716:function(e,t,n){"use strict";var r=n(9144),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,l=r.useEffect,u=r.useLayoutEffect,a=r.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,c=r[1];return u(function(){o.value=n,o.getSnapshot=t,s(o)&&c({inst:o})},[e,n,t]),l(function(){return s(o)&&c({inst:o}),e(function(){s(o)&&c({inst:o})})},[e]),a(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},6753:function(e,t,n){"use strict";e.exports=n(716)},2390:function(e,t,n){"use strict";n.d(t,{RR:()=>u,cv:()=>s,dp:()=>f,oo:()=>i,uY:()=>c});var r=n(1601);function o(e,t,n){let o,{reference:i,floating:l}=e,u=(0,r.Qq)(t),a=(0,r.Wh)(t),s=(0,r.I4)(a),c=(0,r.k3)(t),f="y"===u,d=i.x+i.width/2-l.width/2,h=i.y+i.height/2-l.height/2,p=i[s]/2-l[s]/2;switch(c){case"top":o={x:d,y:i.y-l.height};break;case"bottom":o={x:d,y:i.y+i.height};break;case"right":o={x:i.x+i.width,y:h};break;case"left":o={x:i.x-l.width,y:h};break;default:o={x:i.x,y:i.y}}switch((0,r.hp)(t)){case"start":o[a]-=p*(n&&f?-1:1);break;case"end":o[a]+=p*(n&&f?-1:1)}return o}let i=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:l=[],platform:u}=n,a=l.filter(Boolean),s=await (null==u.isRTL?void 0:u.isRTL(t)),c=await u.getElementRects({reference:e,floating:t,strategy:i}),{x:f,y:d}=o(c,r,s),h=r,p={},m=0;for(let n=0;n<a.length;n++){let{name:l,fn:g}=a[n],{x:v,y:y,data:w,reset:b}=await g({x:f,y:d,initialPlacement:r,placement:h,strategy:i,middlewareData:p,rects:c,platform:u,elements:{reference:e,floating:t}});if(f=null!=v?v:f,d=null!=y?y:d,p={...p,[l]:{...p[l],...w}},b&&m<=50){m++,"object"==typeof b&&(b.placement&&(h=b.placement),b.rects&&(c=!0===b.rects?await u.getElementRects({reference:e,floating:t,strategy:i}):b.rects),{x:f,y:d}=o(c,h,s)),n=-1;continue}}return{x:f,y:d,placement:h,strategy:i,middlewareData:p}};async function l(e,t){var n;void 0===t&&(t={});let{x:o,y:i,platform:l,rects:u,elements:a,strategy:s}=e,{boundary:c="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:h=!1,padding:p=0}=(0,r.ku)(t,e),m=(0,r.yd)(p),g=a[h?"floating"===d?"reference":"floating":d],v=(0,r.JB)(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(g)))||n?g:g.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(a.floating)),boundary:c,rootBoundary:f,strategy:s})),y="floating"===d?{...u.floating,x:o,y:i}:u.reference,w=await (null==l.getOffsetParent?void 0:l.getOffsetParent(a.floating)),b=await (null==l.isElement?void 0:l.isElement(w))&&await (null==l.getScale?void 0:l.getScale(w))||{x:1,y:1},E=(0,r.JB)(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({rect:y,offsetParent:w,strategy:s}):y);return{top:(v.top-E.top+m.top)/b.y,bottom:(E.bottom-v.bottom+m.bottom)/b.y,left:(v.left-E.left+m.left)/b.x,right:(E.right-v.right+m.right)/b.x}}let u=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,o,i,u;let{placement:a,middlewareData:s,rects:c,initialPlacement:f,platform:d,elements:h}=t,{mainAxis:p=!0,crossAxis:m=!0,fallbackPlacements:g,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:w=!0,...b}=(0,r.ku)(e,t),E=(0,r.k3)(a),R=(0,r.k3)(f)===f,x=await (null==d.isRTL?void 0:d.isRTL(h.floating)),C=g||(R||!w?[(0,r.pw)(f)]:(0,r.gy)(f));g||"none"===y||C.push(...(0,r.KX)(f,w,y,x));let S=[f,...C],M=await l(t,b),k=[],A=(null==(n=s.flip)?void 0:n.overflows)||[];if(p&&k.push(M[E]),m){let e=(0,r.i8)(a,c,x);k.push(M[e[0]],M[e[1]])}if(A=[...A,{placement:a,overflows:k}],!k.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=S[e];if(t)return{data:{index:e,overflows:A},reset:{placement:t}};let n=null==(i=A.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(u=A.map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:u[0];e&&(n=e);break}case"initialPlacement":n=f}if(a!==n)return{reset:{placement:n}}}return{}}}};async function a(e,t){let{placement:n,platform:o,elements:i}=e,l=await (null==o.isRTL?void 0:o.isRTL(i.floating)),u=(0,r.k3)(n),a=(0,r.hp)(n),s="y"===(0,r.Qq)(n),c=["left","top"].includes(u)?-1:1,f=l&&s?-1:1,d=(0,r.ku)(t,e),{mainAxis:h,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...d};return a&&"number"==typeof m&&(p="end"===a?-1*m:m),s?{x:p*f,y:h*c}:{x:h*c,y:p*f}}let s=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){let{x:n,y:r}=t,o=await a(t,e);return{x:n+o.x,y:r+o.y,data:o}}}},c=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:o,placement:i}=t,{mainAxis:u=!0,crossAxis:a=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=(0,r.ku)(e,t),f={x:n,y:o},d=await l(t,c),h=(0,r.Qq)((0,r.k3)(i)),p=(0,r.Rn)(h),m=f[p],g=f[h];if(u){let e="y"===p?"top":"left",t="y"===p?"bottom":"right",n=m+d[e],o=m-d[t];m=(0,r.uZ)(n,m,o)}if(a){let e="y"===h?"top":"left",t="y"===h?"bottom":"right",n=g+d[e],o=g-d[t];g=(0,r.uZ)(n,g,o)}let v=s.fn({...t,[p]:m,[h]:g});return{...v,data:{x:v.x-n,y:v.y-o}}}}},f=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){let n,o;let{placement:i,rects:u,platform:a,elements:s}=t,{apply:c=()=>{},...f}=(0,r.ku)(e,t),d=await l(t,f),h=(0,r.k3)(i),p=(0,r.hp)(i),m="y"===(0,r.Qq)(i),{width:g,height:v}=u.floating;"top"===h||"bottom"===h?(n=h,o=p===(await (null==a.isRTL?void 0:a.isRTL(s.floating))?"start":"end")?"left":"right"):(o=h,n="end"===p?"top":"bottom");let y=v-d[n],w=g-d[o],b=!t.middlewareData.shift,E=y,R=w;if(m){let e=g-d.left-d.right;R=p||b?(0,r.VV)(w,e):e}else{let e=v-d.top-d.bottom;E=p||b?(0,r.VV)(y,e):e}if(b&&!p){let e=(0,r.Fp)(d.left,0),t=(0,r.Fp)(d.right,0),n=(0,r.Fp)(d.top,0),o=(0,r.Fp)(d.bottom,0);m?R=g-2*(0!==e||0!==t?e+t:(0,r.Fp)(d.left,d.right)):E=v-2*(0!==n||0!==o?n+o:(0,r.Fp)(d.top,d.bottom))}await c({...t,availableWidth:R,availableHeight:E});let x=await a.getDimensions(s.floating);return g!==x.width||v!==x.height?{reset:{rects:!0}}:{}}}}},6089:function(e,t,n){"use strict";n.d(t,{Me:()=>y,oo:()=>w});var r=n(1601),o=n(2390),i=n(6164);function l(e){let t=(0,i.Dx)(e),n=parseFloat(t.width)||0,o=parseFloat(t.height)||0,l=(0,i.Re)(e),u=l?e.offsetWidth:n,a=l?e.offsetHeight:o,s=(0,r.NM)(n)!==u||(0,r.NM)(o)!==a;return s&&(n=u,o=a),{width:n,height:o,$:s}}function u(e){return(0,i.kK)(e)?e:e.contextElement}function a(e){let t=u(e);if(!(0,i.Re)(t))return(0,r.ze)(1);let n=t.getBoundingClientRect(),{width:o,height:a,$:s}=l(t),c=(s?(0,r.NM)(n.width):n.width)/o,f=(s?(0,r.NM)(n.height):n.height)/a;return c&&Number.isFinite(c)||(c=1),f&&Number.isFinite(f)||(f=1),{x:c,y:f}}let s=(0,r.ze)(0);function c(e){let t=(0,i.Jj)(e);return(0,i.Pf)()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:s}function f(e,t,n,o){var l;void 0===t&&(t=!1),void 0===n&&(n=!1);let s=e.getBoundingClientRect(),f=u(e),d=(0,r.ze)(1);t&&(o?(0,i.kK)(o)&&(d=a(o)):d=a(e));let h=(void 0===(l=n)&&(l=!1),o&&(!l||o===(0,i.Jj)(f))&&l)?c(f):(0,r.ze)(0),p=(s.left+h.x)/d.x,m=(s.top+h.y)/d.y,g=s.width/d.x,v=s.height/d.y;if(f){let e=(0,i.Jj)(f),t=o&&(0,i.kK)(o)?(0,i.Jj)(o):o,n=e.frameElement;for(;n&&o&&t!==e;){let e=a(n),t=n.getBoundingClientRect(),r=(0,i.Dx)(n),o=t.left+(n.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(n.clientTop+parseFloat(r.paddingTop))*e.y;p*=e.x,m*=e.y,g*=e.x,v*=e.y,p+=o,m+=l,n=(0,i.Jj)(n).frameElement}}return(0,r.JB)({width:g,height:v,x:p,y:m})}function d(e){return f((0,i.tF)(e)).left+(0,i.Lw)(e).scrollLeft}function h(e,t,n){let o;if("viewport"===t)o=function(e,t){let n=(0,i.Jj)(e),r=(0,i.tF)(e),o=n.visualViewport,l=r.clientWidth,u=r.clientHeight,a=0,s=0;if(o){l=o.width,u=o.height;let e=(0,i.Pf)();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:l,height:u,x:a,y:s}}(e,n);else if("document"===t)o=function(e){let t=(0,i.tF)(e),n=(0,i.Lw)(e),o=e.ownerDocument.body,l=(0,r.Fp)(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),u=(0,r.Fp)(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight),a=-n.scrollLeft+d(e),s=-n.scrollTop;return"rtl"===(0,i.Dx)(o).direction&&(a+=(0,r.Fp)(t.clientWidth,o.clientWidth)-l),{width:l,height:u,x:a,y:s}}((0,i.tF)(e));else if((0,i.kK)(t))o=function(e,t){let n=f(e,!0,"fixed"===t),o=n.top+e.clientTop,l=n.left+e.clientLeft,u=(0,i.Re)(e)?a(e):(0,r.ze)(1),s=e.clientWidth*u.x,c=e.clientHeight*u.y;return{width:s,height:c,x:l*u.x,y:o*u.y}}(t,n);else{let n=c(e);o={...t,x:t.x-n.x,y:t.y-n.y}}return(0,r.JB)(o)}function p(e,t){return(0,i.Re)(e)&&"fixed"!==(0,i.Dx)(e).position?t?t(e):e.offsetParent:null}function m(e,t){let n=(0,i.Jj)(e);if(!(0,i.Re)(e))return n;let r=p(e,t);for(;r&&(0,i.Ze)(r)&&"static"===(0,i.Dx)(r).position;)r=p(r,t);return r&&("html"===(0,i.wk)(r)||"body"===(0,i.wk)(r)&&"static"===(0,i.Dx)(r).position&&!(0,i.hT)(r))?n:r||(0,i.gQ)(e)||n}let g=async function(e){let{reference:t,floating:n,strategy:o}=e,l=this.getOffsetParent||m,u=this.getDimensions;return{reference:function(e,t,n){let o=(0,i.Re)(t),l=(0,i.tF)(t),u="fixed"===n,a=f(e,!0,u,t),s={scrollLeft:0,scrollTop:0},c=(0,r.ze)(0);if(o||!o&&!u){if(("body"!==(0,i.wk)(t)||(0,i.ao)(l))&&(s=(0,i.Lw)(t)),o){let e=f(t,!0,u,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else l&&(c.x=d(l))}return{x:a.left+s.scrollLeft-c.x,y:a.top+s.scrollTop-c.y,width:a.width,height:a.height}}(t,await l(n),o),floating:{x:0,y:0,...await u(n)}}},v={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{rect:t,offsetParent:n,strategy:o}=e,l=(0,i.Re)(n),u=(0,i.tF)(n);if(n===u)return t;let s={scrollLeft:0,scrollTop:0},c=(0,r.ze)(1),d=(0,r.ze)(0);if((l||!l&&"fixed"!==o)&&(("body"!==(0,i.wk)(n)||(0,i.ao)(u))&&(s=(0,i.Lw)(n)),(0,i.Re)(n))){let e=f(n);c=a(n),d.x=e.x+n.clientLeft,d.y=e.y+n.clientTop}return{width:t.width*c.x,height:t.height*c.y,x:t.x*c.x-s.scrollLeft*c.x+d.x,y:t.y*c.y-s.scrollTop*c.y+d.y}},getDocumentElement:i.tF,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:o,strategy:l}=e,u=[..."clippingAncestors"===n?function(e,t){let n=t.get(e);if(n)return n;let r=(0,i.Kx)(e).filter(e=>(0,i.kK)(e)&&"body"!==(0,i.wk)(e)),o=null,l="fixed"===(0,i.Dx)(e).position,u=l?(0,i.Ow)(e):e;for(;(0,i.kK)(u)&&!(0,i.Py)(u);){let t=(0,i.Dx)(u),n=(0,i.hT)(u);n||"fixed"!==t.position||(o=null),(l?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||(0,i.ao)(u)&&!n&&function e(t,n){let r=(0,i.Ow)(t);return!(r===n||!(0,i.kK)(r)||(0,i.Py)(r))&&("fixed"===(0,i.Dx)(r).position||e(r,n))}(e,u))?r=r.filter(e=>e!==u):o=t,u=(0,i.Ow)(u)}return t.set(e,r),r}(t,this._c):[].concat(n),o],a=u[0],s=u.reduce((e,n)=>{let o=h(t,n,l);return e.top=(0,r.Fp)(o.top,e.top),e.right=(0,r.VV)(o.right,e.right),e.bottom=(0,r.VV)(o.bottom,e.bottom),e.left=(0,r.Fp)(o.left,e.left),e},h(t,a,l));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:m,getElementRects:g,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){return l(e)},getScale:a,isElement:i.kK,isRTL:function(e){return"rtl"===(0,i.Dx)(e).direction}};function y(e,t,n,o){let l;void 0===o&&(o={});let{ancestorScroll:a=!0,ancestorResize:s=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:h=!1}=o,p=u(e),m=a||s?[...p?(0,i.Kx)(p):[],...(0,i.Kx)(t)]:[];m.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let g=p&&d?function(e,t){let n,o=null,l=(0,i.tF)(e);function u(){clearTimeout(n),o&&o.disconnect(),o=null}return!function i(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),u();let{left:c,top:f,width:d,height:h}=e.getBoundingClientRect();if(a||t(),!d||!h)return;let p=(0,r.GW)(f),m=(0,r.GW)(l.clientWidth-(c+d)),g={rootMargin:-p+"px "+-m+"px "+-(0,r.GW)(l.clientHeight-(f+h))+"px "+-(0,r.GW)(c)+"px",threshold:(0,r.Fp)(0,(0,r.VV)(1,s))||1},v=!0;function y(e){let t=e[0].intersectionRatio;if(t!==s){if(!v)return i();t?i(!1,t):n=setTimeout(()=>{i(!1,1e-7)},100)}v=!1}try{o=new IntersectionObserver(y,{...g,root:l.ownerDocument})}catch(e){o=new IntersectionObserver(y,g)}o.observe(e)}(!0),u}(p,n):null,v=-1,y=null;c&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{y&&y.observe(t)})),n()}),p&&!h&&y.observe(p),y.observe(t));let w=h?f(e):null;return h&&function t(){let r=f(e);w&&(r.x!==w.x||r.y!==w.y||r.width!==w.width||r.height!==w.height)&&n(),w=r,l=requestAnimationFrame(t)}(),n(),()=>{m.forEach(e=>{a&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),g&&g(),y&&y.disconnect(),y=null,h&&cancelAnimationFrame(l)}}let w=(e,t,n)=>{let r=new Map,i={platform:v,...n},l={...i.platform,_c:r};return(0,o.oo)(e,t,{...i,platform:l})}},3986:function(e,t,n){"use strict";n.d(t,{RR:()=>m,YF:()=>d,cv:()=>h,dp:()=>g,uY:()=>p});var r=n(6089),o=n(2390),i=n(9144),l=n(8315),u="undefined"!=typeof document?i.useLayoutEffect:function(){};function a(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!a(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!a(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function s(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function c(e,t){let n=s(e);return Math.round(t*n)/n}function f(e){let t=i.useRef(e);return u(()=>{t.current=e}),t}function d(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:d,elements:{reference:h,floating:p}={},transform:m=!0,whileElementsMounted:g,open:v}=e,[y,w]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[b,E]=i.useState(o);a(b,o)||E(o);let[R,x]=i.useState(null),[C,S]=i.useState(null),M=i.useCallback(e=>{e!==O.current&&(O.current=e,x(e))},[]),k=i.useCallback(e=>{e!==T.current&&(T.current=e,S(e))},[]),A=h||R,L=p||C,O=i.useRef(null),T=i.useRef(null),N=i.useRef(y),_=null!=g,I=f(g),P=f(d),D=f(v),F=i.useCallback(()=>{if(!O.current||!T.current)return;let e={placement:t,strategy:n,middleware:b};P.current&&(e.platform=P.current),(0,r.oo)(O.current,T.current,e).then(e=>{let t={...e,isPositioned:!1!==D.current};B.current&&!a(N.current,t)&&(N.current=t,l.flushSync(()=>{w(t)}))})},[b,t,n,P,D]);u(()=>{!1===v&&N.current.isPositioned&&(N.current.isPositioned=!1,w(e=>({...e,isPositioned:!1})))},[v]);let B=i.useRef(!1);u(()=>(B.current=!0,()=>{B.current=!1}),[]),u(()=>{if(A&&(O.current=A),L&&(T.current=L),A&&L){if(I.current)return I.current(A,L,F);F()}},[A,L,F,I,_]);let z=i.useMemo(()=>({reference:O,floating:T,setReference:M,setFloating:k}),[M,k]),j=i.useMemo(()=>({reference:A,floating:L}),[A,L]),U=i.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=c(j.floating,y.x),r=c(j.floating,y.y);return m?{...e,transform:"translate("+t+"px, "+r+"px)",...s(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,m,j.floating,y.x,y.y]);return i.useMemo(()=>({...y,update:F,refs:z,elements:j,floatingStyles:U}),[y,F,z,j,U])}let h=(e,t)=>({...(0,o.cv)(e),options:[e,t]}),p=(e,t)=>({...(0,o.uY)(e),options:[e,t]}),m=(e,t)=>({...(0,o.RR)(e),options:[e,t]}),g=(e,t)=>({...(0,o.dp)(e),options:[e,t]})},7126:function(e,t,n){"use strict";n.d(t,{wD:()=>tw,eS:()=>tE,A8:()=>eU,XI:()=>e6,mN:()=>eZ,qq:()=>eS,Zm:()=>eX,YF:()=>tM,KK:()=>tA,qs:()=>tN,bQ:()=>tS,ll:()=>td,jV:()=>eG,NI:()=>tO,Y_:()=>tP,RB:()=>e0,bU:()=>ej});var r=n(9144),o=n.t(r,2);function i(){return"undefined"!=typeof window}function l(e){return a(e)?(e.nodeName||"").toLowerCase():"#document"}function u(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function a(e){return!!i()&&(e instanceof Node||e instanceof u(e).Node)}function s(e){return!!i()&&(e instanceof Element||e instanceof u(e).Element)}function c(e){return!!i()&&(e instanceof HTMLElement||e instanceof u(e).HTMLElement)}function f(e){return!!i()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof u(e).ShadowRoot)}function d(e){return["html","body","#document"].includes(l(e))}let h=Math.floor;var p='input:not([inert]),select:not([inert]),textarea:not([inert]),a[href]:not([inert]),button:not([inert]),[tabindex]:not(slot):not([inert]),audio[controls]:not([inert]),video[controls]:not([inert]),[contenteditable]:not([contenteditable="false"]):not([inert]),details>summary:first-of-type:not([inert]),details:not([inert])',m="undefined"==typeof Element,g=m?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,v=!m&&Element.prototype.getRootNode?function(e){var t;return null==e?void 0:null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},y=function e(t,n){void 0===n&&(n=!0);var r,o=null==t?void 0:null===(r=t.getAttribute)||void 0===r?void 0:r.call(t,"inert");return""===o||"true"===o||n&&t&&e(t.parentNode)},w=function(e){var t,n=null==e?void 0:null===(t=e.getAttribute)||void 0===t?void 0:t.call(e,"contenteditable");return""===n||"true"===n},b=function(e,t,n){if(y(e))return[];var r=Array.prototype.slice.apply(e.querySelectorAll(p));return t&&g.call(e,p)&&r.unshift(e),r=r.filter(n)},E=function e(t,n,r){for(var o=[],i=Array.from(t);i.length;){var l=i.shift();if(!y(l,!1)){if("SLOT"===l.tagName){var u=l.assignedElements(),a=e(u.length?u:l.children,!0,r);r.flatten?o.push.apply(o,a):o.push({scopeParent:l,candidates:a})}else{g.call(l,p)&&r.filter(l)&&(n||!t.includes(l))&&o.push(l);var s=l.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(l),c=!y(s,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(l));if(s&&c){var f=e(!0===s?l.children:s.children,!0,r);r.flatten?o.push.apply(o,f):o.push({scopeParent:l,candidates:f})}else i.unshift.apply(i,l.children)}}}return o},R=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},x=function(e){if(!e)throw Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||w(e))&&!R(e)?0:e.tabIndex},C=function(e,t){var n=x(e);return n<0&&t&&!R(e)?0:n},S=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},M=function(e){return"INPUT"===e.tagName},k=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]},A=function(e){if(!e.name)return!0;var t,n=e.form||v(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(e){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",e.message),!1}var o=k(t,e.form);return!o||o===e},L=function(e){return M(e)&&"radio"===e.type&&!A(e)},O=function(e){var t,n,r,o,i,l,u,a=e&&v(e),s=null===(t=a)||void 0===t?void 0:t.host,c=!1;if(a&&a!==e)for(c=!!(null!==(n=s)&&void 0!==n&&null!==(r=n.ownerDocument)&&void 0!==r&&r.contains(s)||null!=e&&null!==(o=e.ownerDocument)&&void 0!==o&&o.contains(e));!c&&s;)c=!!(null!==(l=s=null===(i=a=v(s))||void 0===i?void 0:i.host)&&void 0!==l&&null!==(u=l.ownerDocument)&&void 0!==u&&u.contains(s));return c},T=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},N=function(e,t){var n=t.displayCheck,r=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var o=g.call(e,"details>summary:first-of-type")?e.parentElement:e;if(g.call(o,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return T(e)}else{if("function"==typeof r){for(var i=e;e;){var l=e.parentElement,u=v(e);if(l&&!l.shadowRoot&&!0===r(l))return T(e);e=e.assignedSlot?e.assignedSlot:l||u===e.ownerDocument?l:u.host}e=i}if(O(e))return!e.getClientRects().length;if("legacy-full"!==n)return!0}return!1},_=function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if("LEGEND"===r.tagName)return!!g.call(t,"fieldset[disabled] *")||!r.contains(e)}return!0}t=t.parentElement}return!1},I=function(e,t){return!(t.disabled||y(t)||M(t)&&"hidden"===t.type||N(t,e)||"DETAILS"===t.tagName&&Array.prototype.slice.apply(t.children).some(function(e){return"SUMMARY"===e.tagName})||_(t))},P=function(e,t){return!(L(t)||0>x(t))&&!!I(e,t)},D=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!isNaN(t)||!!(t>=0)},F=function e(t){var n=[],r=[];return t.forEach(function(t,o){var i=!!t.scopeParent,l=i?t.scopeParent:t,u=C(l,i),a=i?e(t.candidates):l;0===u?i?n.push.apply(n,a):n.push(l):r.push({documentOrder:o,tabIndex:u,item:t,isScope:i,content:a})}),r.sort(S).reduce(function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e},[]).concat(n)},B=function(e,t){var n;return F((t=t||{}).getShadowRoot?E([e],t.includeContainer,{filter:P.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:D}):b(e,t.includeContainer,P.bind(null,t)))},z=function(e,t){var n;return(t=t||{}).getShadowRoot?E([e],t.includeContainer,{filter:I.bind(null,t),flatten:!0,getShadowRoot:t.getShadowRoot}):b(e,t.includeContainer,I.bind(null,t))},j=function(e,t){if(t=t||{},!e)throw Error("No node provided");return!1!==g.call(e,p)&&P(t,e)};function U(){let e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}function W(){let e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(e=>{let{brand:t,version:n}=e;return t+"/"+n}).join(" "):navigator.userAgent}function H(){return/apple/i.test(navigator.vendor)}function V(){let e=/android/i;return e.test(U())||e.test(W())}function K(){return W().includes("jsdom/")}let $="data-floating-ui-focusable",Y="ArrowLeft",Q="ArrowRight";function q(e){let t=e.activeElement;for(;(null==(n=t)||null==(n=n.shadowRoot)?void 0:n.activeElement)!=null;){var n;t=t.shadowRoot.activeElement}return t}function X(e,t){if(!e||!t)return!1;let n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&f(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}function J(e){return"composedPath"in e?e.composedPath()[0]:e.target}function G(e,t){return null!=t&&("composedPath"in e?e.composedPath().includes(t):null!=e.target&&t.contains(e.target))}function Z(e){return(null==e?void 0:e.ownerDocument)||document}function ee(e){return c(e)&&e.matches("input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])")}function et(e){return!!e&&"combobox"===e.getAttribute("role")&&ee(e)}function en(e){return e?e.hasAttribute($)?e:e.querySelector("["+$+"]")||e:null}function er(e,t){let n=e.filter(e=>{var n;return e.parentId===t&&(null==(n=e.context)?void 0:n.open)}),r=n;for(;r.length;)r=e.filter(e=>{var t;return null==(t=r)?void 0:t.some(t=>{var n;return e.parentId===t.id&&(null==(n=e.context)?void 0:n.open)})}),n=n.concat(r);return n}function eo(e,t){var n;let r=[],o=null==(n=e.find(e=>e.id===t))?void 0:n.parentId;for(;o;){let t=e.find(e=>e.id===o);o=null==t?void 0:t.parentId,t&&(r=r.concat(t))}return r}function ei(e){e.preventDefault(),e.stopPropagation()}function el(e,t){let n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}var eu="undefined"!=typeof document?r.useLayoutEffect:function(){};function ea(e){let t=r.useRef(e);return eu(()=>{t.current=e}),t}let es={...o}.useInsertionEffect||(e=>e());function ec(e){let t=r.useRef(()=>{});return es(()=>{t.current=e}),r.useCallback(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function ef(e,t,n){return Math.floor(e/t)!==n}function ed(e,t){return t<0||t>=e.current.length}function eh(e,t){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:o,amount:i=1}=void 0===t?{}:t,l=n;do l+=r?-i:i;while(l>=0&&l<=e.current.length-1&&ep(e,l,o));return l}function ep(e,t,n){if("function"==typeof n)return n(t);if(n)return n.includes(t);let r=e.current[t];return null==r||r.hasAttribute("disabled")||"true"===r.getAttribute("aria-disabled")}let em=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function eg(e,t){let n=B(e,em()),r=n.length;if(0===r)return;let o=q(Z(e)),i=n.indexOf(o);return n[-1===i?1===t?0:r-1:i+t]}function ev(e){return eg(Z(e).body,1)||e}function ey(e){return eg(Z(e).body,-1)||e}function ew(e,t){let n=t||e.currentTarget,r=e.relatedTarget;return!r||!X(n,r)}function eb(e){e.querySelectorAll("[data-tabindex]").forEach(e=>{let t=e.dataset.tabindex;delete e.dataset.tabindex,t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex")})}var eE=n(836),eR=n(8315),ex=n(6164),eC=n(3986);function eS(e){let t=r.useRef(void 0),n=r.useCallback(t=>{let n=e.map(e=>{if(null!=e){if("function"==typeof e){let n=e(t);return"function"==typeof n?n:()=>{e(null)}}return e.current=t,()=>{e.current=null}}});return()=>{n.forEach(e=>null==e?void 0:e())}},e);return r.useMemo(()=>e.every(e=>null==e)?null:e=>{t.current&&(t.current(),t.current=void 0),null!=e&&(t.current=n(e))},e)}function eM(e,t){let n=e.compareDocumentPosition(t);return n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS?1:0}let ek=r.createContext({register:()=>{},unregister:()=>{},map:new Map,elementsRef:{current:[]}});function eA(e){let{children:t,elementsRef:n,labelsRef:o}=e,[i,l]=r.useState(()=>new Set),u=r.useCallback(e=>{l(t=>new Set(t).add(e))},[]),a=r.useCallback(e=>{l(t=>{let n=new Set(t);return n.delete(e),n})},[]),s=r.useMemo(()=>{let e=new Map;return Array.from(i.keys()).sort(eM).forEach((t,n)=>{e.set(t,n)}),e},[i]);return(0,eE.jsx)(ek.Provider,{value:r.useMemo(()=>({register:u,unregister:a,map:s,elementsRef:n,labelsRef:o}),[u,a,s,n,o]),children:t})}let eL="active",eO="selected",eT="ArrowLeft",eN="ArrowRight",e_="ArrowUp",eI="ArrowDown";function eP(e,t){return"function"==typeof e?e(t):e?r.cloneElement(e,t):(0,eE.jsx)("div",{...t})}let eD=r.createContext({activeIndex:0,onNavigate:()=>{}}),eF=[eT,eN],eB=[e_,eI],ez=[...eF,...eB],ej=r.forwardRef(function(e,t){let{render:n,orientation:o="both",loop:i=!0,rtl:l=!1,cols:u=1,disabledIndices:a,activeIndex:s,onNavigate:c,itemSizes:f,dense:d=!1,...p}=e,[m,g]=r.useState(0),v=null!=s?s:m,y=ec(null!=c?c:g),w=r.useRef([]),b=n&&"function"!=typeof n?n.props:{},E=r.useMemo(()=>({activeIndex:v,onNavigate:y}),[v,y]),R=u>1,x={...p,...b,ref:t,"aria-orientation":"both"===o?void 0:o,onKeyDown(e){null==p.onKeyDown||p.onKeyDown(e),null==b.onKeyDown||b.onKeyDown(e),function(e){var t,n;if(!ez.includes(e.key))return;let r=v,s=eh(w,{disabledIndices:a}),c=eh(w,{decrement:!0,startingIndex:w.current.length,disabledIndices:a}),p=l?eT:eN,m=l?eN:eT;if(R){let n=f||Array.from({length:w.current.length},()=>({width:1,height:1})),m=function(e,t,n){let r=[],o=0;return e.forEach((e,i)=>{let{width:l,height:u}=e,a=!1;for(n&&(o=0);!a;){let e=[];for(let n=0;n<l;n++)for(let r=0;r<u;r++)e.push(o+n+r*t);o%t+l<=t&&e.every(e=>null==r[e])?(e.forEach(e=>{r[e]=i}),a=!0):o++}}),[...r]}(n,u,d),g=m.findIndex(e=>null!=e&&!ep(w,e,a)),y=m.reduce((e,t,n)=>null==t||ep(w,t,a)?e:n,-1),b=m[function(e,t){let{event:n,orientation:r,loop:o,rtl:i,cols:l,disabledIndices:u,minIndex:a,maxIndex:s,prevIndex:c,stopEvent:f=!1}=t,d=c;if("ArrowUp"===n.key){if(f&&ei(n),-1===c)d=s;else if(d=eh(e,{startingIndex:d,amount:l,decrement:!0,disabledIndices:u}),o&&(c-l<a||d<0)){let e=c%l,t=s%l,n=s-(t-e);d=t===e?s:t>e?n:n-l}ed(e,d)&&(d=c)}if("ArrowDown"===n.key&&(f&&ei(n),-1===c?d=a:(d=eh(e,{startingIndex:c,amount:l,disabledIndices:u}),o&&c+l>s&&(d=eh(e,{startingIndex:c%l-l,amount:l,disabledIndices:u}))),ed(e,d)&&(d=c)),"both"===r){let t=h(c/l);n.key===(i?Y:Q)&&(f&&ei(n),c%l!=l-1?(d=eh(e,{startingIndex:c,disabledIndices:u}),o&&ef(d,l,t)&&(d=eh(e,{startingIndex:c-c%l-1,disabledIndices:u}))):o&&(d=eh(e,{startingIndex:c-c%l-1,disabledIndices:u})),ef(d,l,t)&&(d=c)),n.key===(i?Q:Y)&&(f&&ei(n),c%l!=0?(d=eh(e,{startingIndex:c,decrement:!0,disabledIndices:u}),o&&ef(d,l,t)&&(d=eh(e,{startingIndex:c+(l-c%l),decrement:!0,disabledIndices:u}))):o&&(d=eh(e,{startingIndex:c+(l-c%l),decrement:!0,disabledIndices:u})),ef(d,l,t)&&(d=c));let r=h(s/l)===t;ed(e,d)&&(d=o&&r?n.key===(i?Q:Y)?s:eh(e,{startingIndex:c-c%l-1,disabledIndices:u}):c)}return d}({current:m.map(e=>e?w.current[e]:null)},{event:e,orientation:o,loop:i,rtl:l,cols:u,disabledIndices:(t=[...("function"!=typeof a?a:null)||w.current.map((e,t)=>ep(w,t,a)?t:void 0),void 0],m.flatMap((e,n)=>t.includes(e)?[n]:[])),minIndex:g,maxIndex:y,prevIndex:function(e,t,n,r,o){if(-1===e)return -1;let i=n.indexOf(e),l=t[e];switch(o){case"tl":return i;case"tr":if(!l)return i;return i+l.width-1;case"bl":if(!l)return i;return i+(l.height-1)*r;case"br":return n.lastIndexOf(e)}}(v>c?s:v,n,m,u,e.key===eI?"bl":e.key===p?"tr":"tl")})];null!=b&&(r=b)}let g={horizontal:[p],vertical:[eI],both:[p,eI]}[o],b={horizontal:[m],vertical:[e_],both:[m,e_]}[o],E=R?ez:({horizontal:eF,vertical:eB,both:ez})[o];r===v&&[...g,...b].includes(e.key)&&(r=i&&r===c&&g.includes(e.key)?s:i&&r===s&&b.includes(e.key)?c:eh(w,{startingIndex:r,decrement:b.includes(e.key),disabledIndices:a})),r!==v&&!ed(w,r)&&(e.stopPropagation(),E.includes(e.key)&&e.preventDefault(),y(r),null==(n=w.current[r])||n.focus())}(e)}};return(0,eE.jsx)(eD.Provider,{value:E,children:(0,eE.jsx)(eA,{elementsRef:w,children:eP(n,x)})})}),eU=r.forwardRef(function(e,t){let{render:n,...o}=e,i=n&&"function"!=typeof n?n.props:{},{activeIndex:l,onNavigate:u}=r.useContext(eD),{ref:a,index:s}=function(e){void 0===e&&(e={});let{label:t}=e,{register:n,unregister:o,map:i,elementsRef:l,labelsRef:u}=r.useContext(ek),[a,s]=r.useState(null),c=r.useRef(null),f=r.useCallback(e=>{if(c.current=e,null!==a&&(l.current[a]=e,u)){var n;let r=void 0!==t;u.current[a]=r?t:null!=(n=null==e?void 0:e.textContent)?n:null}},[a,l,u,t]);return eu(()=>{let e=c.current;if(e)return n(e),()=>{o(e)}},[n,o]),eu(()=>{let e=c.current?i.get(c.current):null;null!=e&&s(e)},[i]),r.useMemo(()=>({ref:f,index:null==a?-1:a}),[a,f])}(),c=eS([a,t,i.ref]),f=l===s;return eP(n,{...o,...i,ref:c,tabIndex:f?0:-1,"data-active":f?"":void 0,onFocus(e){null==o.onFocus||o.onFocus(e),null==i.onFocus||i.onFocus(e),u(s)}})}),eW={...o},eH=!1,eV=0,eK=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+eV++,e$=eW.useId||function(){let[e,t]=r.useState(()=>eH?eK():void 0);return eu(()=>{null==e&&t(eK())},[]),r.useEffect(()=>{eH=!0},[]),e};function eY(){let e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach(e=>e(n))},on(t,n){e.has(t)||e.set(t,new Set),e.get(t).add(n)},off(t,n){var r;null==(r=e.get(t))||r.delete(n)}}}let eQ=r.createContext(null),eq=r.createContext(null),eX=()=>{var e;return(null==(e=r.useContext(eQ))?void 0:e.id)||null},eJ=()=>r.useContext(eq);function eG(e){let t=e$(),n=eJ(),r=eX(),o=e||r;return eu(()=>{if(!t)return;let e={id:t,parentId:o};return null==n||n.addNode(e),()=>{null==n||n.removeNode(e)}},[n,t,o]),t}function eZ(e){let{children:t,id:n}=e,o=eX();return(0,eE.jsx)(eQ.Provider,{value:r.useMemo(()=>({id:n,parentId:o}),[n,o]),children:t})}function e0(e){let{children:t}=e,n=r.useRef([]),o=r.useCallback(e=>{n.current=[...n.current,e]},[]),i=r.useCallback(e=>{n.current=n.current.filter(t=>t!==e)},[]),[l]=r.useState(()=>eY());return(0,eE.jsx)(eq.Provider,{value:r.useMemo(()=>({nodesRef:n,addNode:o,removeNode:i,events:l}),[o,i,l]),children:t})}function e1(e){return"data-floating-ui-"+e}function e2(e){-1!==e.current&&(clearTimeout(e.current),e.current=-1)}let e3=e1("safe-polygon");function e4(e,t,n){if(n&&!el(n))return 0;if("number"==typeof e)return e;if("function"==typeof e){let n=e();return"number"==typeof n?n:null==n?void 0:n[t]}return null==e?void 0:e[t]}function e8(e){return"function"==typeof e?e():e}function e6(e,t){void 0===t&&(t={});let{open:n,onOpenChange:o,dataRef:i,events:l,elements:u}=e,{enabled:a=!0,delay:c=0,handleClose:f=null,mouseOnly:d=!1,restMs:h=0,move:p=!0}=t,m=eJ(),g=eX(),v=ea(f),y=ea(c),w=ea(n),b=ea(h),E=r.useRef(),R=r.useRef(-1),x=r.useRef(),C=r.useRef(-1),S=r.useRef(!0),M=r.useRef(!1),k=r.useRef(()=>{}),A=r.useRef(!1),L=ec(()=>{var e;let t=null==(e=i.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t});r.useEffect(()=>{if(a)return l.on("openchange",e),()=>{l.off("openchange",e)};function e(e){let{open:t}=e;t||(e2(R),e2(C),S.current=!0,A.current=!1)}},[a,l]),r.useEffect(()=>{if(!a||!v.current||!n)return;function e(e){L()&&o(!1,e,"hover")}let t=Z(u.floating).documentElement;return t.addEventListener("mouseleave",e),()=>{t.removeEventListener("mouseleave",e)}},[u.floating,n,o,a,v,L]);let O=r.useCallback(function(e,t,n){void 0===t&&(t=!0),void 0===n&&(n="hover");let r=e4(y.current,"close",E.current);r&&!x.current?(e2(R),R.current=window.setTimeout(()=>o(!1,e,n),r)):t&&(e2(R),o(!1,e,n))},[y,o]),T=ec(()=>{k.current(),x.current=void 0}),N=ec(()=>{if(M.current){let e=Z(u.floating).body;e.style.pointerEvents="",e.removeAttribute(e3),M.current=!1}}),_=ec(()=>!!i.current.openEvent&&["click","mousedown"].includes(i.current.openEvent.type));r.useEffect(()=>{if(a&&s(u.domReference)){let o=u.domReference,i=u.floating;return n&&o.addEventListener("mouseleave",r),p&&o.addEventListener("mousemove",e,{once:!0}),o.addEventListener("mouseenter",e),o.addEventListener("mouseleave",t),i&&(i.addEventListener("mouseleave",r),i.addEventListener("mouseenter",l),i.addEventListener("mouseleave",c)),()=>{n&&o.removeEventListener("mouseleave",r),p&&o.removeEventListener("mousemove",e),o.removeEventListener("mouseenter",e),o.removeEventListener("mouseleave",t),i&&(i.removeEventListener("mouseleave",r),i.removeEventListener("mouseenter",l),i.removeEventListener("mouseleave",c))}}function e(e){if(e2(R),S.current=!1,d&&!el(E.current)||e8(b.current)>0&&!e4(y.current,"open"))return;let t=e4(y.current,"open",E.current);t?R.current=window.setTimeout(()=>{w.current||o(!0,e,"hover")},t):n||o(!0,e,"hover")}function t(e){if(_()){N();return}k.current();let t=Z(u.floating);if(e2(C),A.current=!1,v.current&&i.current.floatingContext){n||e2(R),x.current=v.current({...i.current.floatingContext,tree:m,x:e.clientX,y:e.clientY,onClose(){N(),T(),_()||O(e,!0,"safe-polygon")}});let r=x.current;t.addEventListener("mousemove",r),k.current=()=>{t.removeEventListener("mousemove",r)};return}"touch"===E.current&&X(u.floating,e.relatedTarget)||O(e)}function r(e){!_()&&i.current.floatingContext&&(null==v.current||v.current({...i.current.floatingContext,tree:m,x:e.clientX,y:e.clientY,onClose(){N(),T(),_()||O(e)}})(e))}function l(){e2(R)}function c(e){_()||O(e,!1)}},[u,a,e,d,p,O,T,N,o,n,w,m,y,v,i,_,b]),eu(()=>{var e,t;if(a&&n&&null!=(e=v.current)&&null!=(e=e.__options)&&e.blockPointerEvents&&L()){M.current=!0;let e=u.floating;if(s(u.domReference)&&e){let n=Z(u.floating).body;n.setAttribute(e3,"");let r=u.domReference,o=null==m||null==(t=m.nodesRef.current.find(e=>e.id===g))||null==(t=t.context)?void 0:t.elements.floating;return o&&(o.style.pointerEvents=""),n.style.pointerEvents="none",r.style.pointerEvents="auto",e.style.pointerEvents="auto",()=>{n.style.pointerEvents="",r.style.pointerEvents="",e.style.pointerEvents=""}}}},[a,n,g,u,m,v,L]),eu(()=>{n||(E.current=void 0,A.current=!1,T(),N())},[n,T,N]),r.useEffect(()=>()=>{T(),e2(R),e2(C),N()},[a,u.domReference,T,N]);let I=r.useMemo(()=>{function e(e){E.current=e.pointerType}return{onPointerDown:e,onPointerEnter:e,onMouseMove(e){let{nativeEvent:t}=e;function r(){S.current||w.current||o(!0,t,"hover")}(!d||el(E.current))&&!n&&0!==e8(b.current)&&(!A.current||!(e.movementX**2+e.movementY**2<2))&&(e2(C),"touch"===E.current?r():(A.current=!0,C.current=window.setTimeout(r,e8(b.current))))}}},[d,o,n,w,b]);return r.useMemo(()=>a?{reference:I}:{},[a,I])}let e5=0;function e7(e,t){void 0===t&&(t={});let{preventScroll:n=!1,cancelPrevious:r=!0,sync:o=!1}=t;r&&cancelAnimationFrame(e5);let i=()=>null==e?void 0:e.focus({preventScroll:n});o?i():e5=requestAnimationFrame(i)}let e9={inert:new WeakMap,"aria-hidden":new WeakMap,none:new WeakMap};function te(e){return"inert"===e?e9.inert:"aria-hidden"===e?e9["aria-hidden"]:e9.none}let tt=new WeakSet,tn={},tr=0,to=()=>"undefined"!=typeof HTMLElement&&"inert"in HTMLElement.prototype,ti=e=>e&&(e.host||ti(e.parentNode)),tl=(e,t)=>t.map(t=>{if(e.contains(t))return t;let n=ti(t);return e.contains(n)?n:null}).filter(e=>null!=e);function tu(e,t,n){var r;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=((null==(r=e[0])?void 0:r.ownerDocument)||document).body;return function(e,t,n,r){let o="data-floating-ui-inert",i=r?"inert":n?"aria-hidden":null,u=tl(t,e),a=new Set,s=new Set(u),c=[];tn[o]||(tn[o]=new WeakMap);let f=tn[o];return u.forEach(function e(t){!(!t||a.has(t))&&(a.add(t),t.parentNode&&e(t.parentNode))}),function e(t){!(!t||s.has(t))&&[].forEach.call(t.children,t=>{if("script"!==l(t)){if(a.has(t))e(t);else{let e=i?t.getAttribute(i):null,n=null!==e&&"false"!==e,r=te(i),l=(r.get(t)||0)+1,u=(f.get(t)||0)+1;r.set(t,l),f.set(t,u),c.push(t),1===l&&n&&tt.add(t),1===u&&t.setAttribute(o,""),!n&&i&&t.setAttribute(i,"inert"===i?"":"true")}}})}(t),a.clear(),tr++,()=>{c.forEach(e=>{let t=te(i),n=(t.get(e)||0)-1,r=(f.get(e)||0)-1;t.set(e,n),f.set(e,r),n||(!tt.has(e)&&i&&e.removeAttribute(i),tt.delete(e)),r||e.removeAttribute(o)}),--tr||(e9.inert=new WeakMap,e9["aria-hidden"]=new WeakMap,e9.none=new WeakMap,tt=new WeakSet,tn={})}}(e.concat(Array.from(o.querySelectorAll("[aria-live]"))),o,t,n)}let ta={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0},ts=r.forwardRef(function(e,t){let[n,o]=r.useState();eu(()=>{H()&&o("button")},[]);let i={ref:t,tabIndex:0,role:n,"aria-hidden":!n||void 0,[e1("focus-guard")]:"",style:ta};return(0,eE.jsx)("span",{...e,...i})}),tc=r.createContext(null),tf=e1("portal");function td(e){let{children:t,id:n,root:o,preserveTabOrder:i=!0}=e,l=function(e){void 0===e&&(e={});let{id:t,root:n}=e,o=e$(),i=th(),[l,u]=r.useState(null),a=r.useRef(null);return eu(()=>()=>{null==l||l.remove(),queueMicrotask(()=>{a.current=null})},[l]),eu(()=>{if(!o||a.current)return;let e=t?document.getElementById(t):null;if(!e)return;let n=document.createElement("div");n.id=o,n.setAttribute(tf,""),e.appendChild(n),a.current=n,u(n)},[t,o]),eu(()=>{if(null===n||!o||a.current)return;let e=n||(null==i?void 0:i.portalNode);e&&!s(e)&&(e=e.current),e=e||document.body;let r=null;t&&((r=document.createElement("div")).id=t,e.appendChild(r));let l=document.createElement("div");l.id=o,l.setAttribute(tf,""),(e=r||e).appendChild(l),a.current=l,u(l)},[t,n,o,i]),l}({id:n,root:o}),[u,a]=r.useState(null),c=r.useRef(null),f=r.useRef(null),d=r.useRef(null),h=r.useRef(null),p=null==u?void 0:u.modal,m=null==u?void 0:u.open,g=!!u&&!u.modal&&u.open&&i&&!!(o||l);return r.useEffect(()=>{if(l&&i&&!p)return l.addEventListener("focusin",e,!0),l.addEventListener("focusout",e,!0),()=>{l.removeEventListener("focusin",e,!0),l.removeEventListener("focusout",e,!0)};function e(e){l&&ew(e)&&("focusin"===e.type?eb:function(e){B(e,em()).forEach(e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")})})(l)}},[l,i,p]),r.useEffect(()=>{l&&(m||eb(l))},[m,l]),(0,eE.jsxs)(tc.Provider,{value:r.useMemo(()=>({preserveTabOrder:i,beforeOutsideRef:c,afterOutsideRef:f,beforeInsideRef:d,afterInsideRef:h,portalNode:l,setFocusManagerState:a}),[i,l]),children:[g&&l&&(0,eE.jsx)(ts,{"data-type":"outside",ref:c,onFocus:e=>{if(ew(e,l)){var t;null==(t=d.current)||t.focus()}else{let e=ey(u?u.domReference:null);null==e||e.focus()}}}),g&&l&&(0,eE.jsx)("span",{"aria-owns":l.id,style:ta}),l&&eR.createPortal(t,l),g&&l&&(0,eE.jsx)(ts,{"data-type":"outside",ref:f,onFocus:e=>{if(ew(e,l)){var t;null==(t=h.current)||t.focus()}else{let t=ev(u?u.domReference:null);null==t||t.focus(),(null==u?void 0:u.closeOnFocusOut)&&(null==u||u.onOpenChange(!1,e.nativeEvent,"focus-out"))}}})]})}let th=()=>r.useContext(tc);function tp(e){return r.useMemo(()=>t=>{e.forEach(e=>{e&&(e.current=t)})},e)}let tm=[];function tg(){return tm.slice().reverse().find(e=>e.isConnected)}function tv(e,t){var n;if(!t.current.includes("floating")&&!(null!=(n=e.getAttribute("role"))&&n.includes("dialog")))return;let r=em(),o=z(e,r).filter(e=>{let t=e.getAttribute("data-tabindex")||"";return j(e,r)||e.hasAttribute("data-tabindex")&&!t.startsWith("-")}),i=e.getAttribute("tabindex");t.current.includes("floating")||0===o.length?"0"!==i&&e.setAttribute("tabindex","0"):("-1"!==i||e.hasAttribute("data-tabindex")&&"-1"!==e.getAttribute("data-tabindex"))&&(e.setAttribute("tabindex","-1"),e.setAttribute("data-tabindex","-1"))}let ty=r.forwardRef(function(e,t){return(0,eE.jsx)("button",{...e,type:"button",ref:t,tabIndex:-1,style:ta})});function tw(e){let{context:t,children:n,disabled:o=!1,order:i=["content"],guards:u=!0,initialFocus:a=0,returnFocus:s=!0,restoreFocus:f=!1,modal:d=!0,visuallyHiddenDismiss:h=!1,closeOnFocusOut:p=!0,outsideElementsInert:m=!1,getInsideElements:g=()=>[]}=e,{open:v,onOpenChange:y,events:w,dataRef:b,elements:{domReference:E,floating:R}}=t,x=ec(()=>{var e;return null==(e=b.current.floatingContext)?void 0:e.nodeId}),C=ec(g),S="number"==typeof a&&a<0,M=et(E)&&S,k=to(),A=!k||u,L=!A||k&&m,O=ea(i),T=ea(a),N=ea(s),_=eJ(),I=th(),P=r.useRef(null),D=r.useRef(null),F=r.useRef(!1),z=r.useRef(!1),U=r.useRef(-1),W=null!=I,H=en(R),$=ec(function(e){return void 0===e&&(e=H),e?B(e,em()):[]}),Y=ec(e=>{let t=$(e);return O.current.map(e=>E&&"reference"===e?E:H&&"floating"===e?H:t).filter(Boolean).flat()});r.useEffect(()=>{if(o||!d)return;function e(e){if("Tab"===e.key){X(H,q(Z(H)))&&0===$().length&&!M&&ei(e);let t=Y(),n=J(e);"reference"===O.current[0]&&n===E&&(ei(e),e.shiftKey?e7(t[t.length-1]):e7(t[1])),"floating"===O.current[1]&&n===H&&e.shiftKey&&(ei(e),e7(t[0]))}}let t=Z(H);return t.addEventListener("keydown",e),()=>{t.removeEventListener("keydown",e)}},[o,E,H,d,O,M,$,Y]),r.useEffect(()=>{if(!o&&R)return R.addEventListener("focusin",e),()=>{R.removeEventListener("focusin",e)};function e(e){let t=J(e),n=$().indexOf(t);-1!==n&&(U.current=n)}},[o,R,$]),r.useEffect(()=>{if(!o&&p&&R&&c(E))return E.addEventListener("focusout",t),E.addEventListener("pointerdown",e),R.addEventListener("focusout",t),()=>{E.removeEventListener("focusout",t),E.removeEventListener("pointerdown",e),R.removeEventListener("focusout",t)};function e(){z.current=!0,setTimeout(()=>{z.current=!1})}function t(e){let t=e.relatedTarget,n=e.currentTarget,r=J(e);queueMicrotask(()=>{let o=x(),i=!(X(E,t)||X(R,t)||X(t,R)||X(null==I?void 0:I.portalNode,t)||null!=t&&t.hasAttribute(e1("focus-guard"))||_&&(er(_.nodesRef.current,o).find(e=>{var n,r;return X(null==(n=e.context)?void 0:n.elements.floating,t)||X(null==(r=e.context)?void 0:r.elements.domReference,t)})||eo(_.nodesRef.current,o).find(e=>{var n,r,o;return[null==(n=e.context)?void 0:n.elements.floating,en(null==(r=e.context)?void 0:r.elements.floating)].includes(t)||(null==(o=e.context)?void 0:o.elements.domReference)===t})));if(n===E&&H&&tv(H,O),f&&n!==E&&!(null!=r&&r.isConnected)&&q(Z(H))===Z(H).body){c(H)&&H.focus();let e=U.current,t=$(),n=t[e]||t[t.length-1]||H;c(n)&&n.focus()}if(b.current.insideReactTree){b.current.insideReactTree=!1;return}(M||!d)&&t&&i&&!z.current&&t!==tg()&&(F.current=!0,y(!1,e,"focus-out"))})}},[o,E,R,H,d,_,I,y,p,f,$,M,x,O,b]);let Q=r.useRef(null),G=r.useRef(null),ee=tp([Q,null==I?void 0:I.beforeInsideRef]),el=tp([G,null==I?void 0:I.afterInsideRef]);function es(e){return!o&&h&&d?(0,eE.jsx)(ty,{ref:"start"===e?P:D,onClick:e=>y(!1,e.nativeEvent),children:"string"==typeof h?h:"Dismiss"}):null}r.useEffect(()=>{var e,t;if(o||!R)return;let n=Array.from((null==I||null==(e=I.portalNode)?void 0:e.querySelectorAll("["+e1("portal")+"]"))||[]),r=_?eo(_.nodesRef.current,x()):[],i=_&&!d?r.map(e=>{var t;return null==(t=e.context)?void 0:t.elements.floating}):[],l=[R,null==(t=r.find(e=>{var t;return et((null==(t=e.context)?void 0:t.elements.domReference)||null)}))||null==(t=t.context)?void 0:t.elements.domReference,...n,...i,...C(),P.current,D.current,Q.current,G.current,null==I?void 0:I.beforeOutsideRef.current,null==I?void 0:I.afterOutsideRef.current,O.current.includes("reference")||M?E:null].filter(e=>null!=e),u=d||M?tu(l,!L,L):tu(l);return()=>{u()}},[o,E,R,d,O,I,M,A,L,_,x,C]),eu(()=>{if(o||!c(H))return;let e=q(Z(H));queueMicrotask(()=>{let t=Y(H),n=T.current,r=("number"==typeof n?t[n]:n.current)||H,o=X(H,e);S||o||!v||e7(r,{preventScroll:r===H})})},[o,v,H,S,Y,T]),eu(()=>{var e;if(o||!H)return;let t=Z(H);function n(e){let{reason:t,event:n,nested:r}=e;if(["hover","safe-polygon"].includes(t)&&"mouseleave"===n.type&&(F.current=!0),"outside-press"===t){if(r)F.current=!1;else if(0===n.mozInputSource&&n.isTrusted||(V()&&n.pointerType?"click"===n.type&&1===n.buttons:0===n.detail&&!n.pointerType)||!K()&&(!V()&&0===n.width&&0===n.height||V()&&1===n.width&&1===n.height&&0===n.pressure&&0===n.detail&&"mouse"===n.pointerType||n.width<1&&n.height<1&&0===n.pressure&&0===n.detail&&"touch"===n.pointerType))F.current=!1;else{let e=!1;document.createElement("div").focus({get preventScroll(){return e=!0,!1}}),e?F.current=!1:F.current=!0}}}e=q(t),tm=tm.filter(e=>e.isConnected),e&&"body"!==l(e)&&(tm.push(e),tm.length>20&&(tm=tm.slice(-20))),w.on("openchange",n);let r=t.createElement("span");return r.setAttribute("tabindex","-1"),r.setAttribute("aria-hidden","true"),Object.assign(r.style,ta),W&&E&&E.insertAdjacentElement("afterend",r),()=>{w.off("openchange",n);let e=q(t),o=X(R,e)||_&&er(_.nodesRef.current,x()).some(t=>{var n;return X(null==(n=t.context)?void 0:n.elements.floating,e)}),i=function(){if("boolean"==typeof N.current){let e=E||tg();return e&&e.isConnected?e:r}return N.current.current||r}();queueMicrotask(()=>{let n=function(e){let t=em();return j(e,t)?e:B(e,t)[0]||e}(i);N.current&&!F.current&&c(n)&&(n===e||e===t.body||o)&&n.focus({preventScroll:!0}),r.remove()})}},[o,R,H,N,b,w,_,W,E,x]),r.useEffect(()=>{queueMicrotask(()=>{F.current=!1})},[o]),eu(()=>{if(!o&&I)return I.setFocusManagerState({modal:d,closeOnFocusOut:p,open:v,onOpenChange:y,domReference:E}),()=>{I.setFocusManagerState(null)}},[o,I,d,v,y,p,E]),eu(()=>{!o&&H&&tv(H,O)},[o,H,O]);let ef=!o&&A&&(!d||!M)&&(W||d);return(0,eE.jsxs)(eE.Fragment,{children:[ef&&(0,eE.jsx)(ts,{"data-type":"inside",ref:ee,onFocus:e=>{if(d){let e=Y();e7("reference"===i[0]?e[0]:e[e.length-1])}else if(null!=I&&I.preserveTabOrder&&I.portalNode){if(F.current=!1,ew(e,I.portalNode)){let e=ev(E);null==e||e.focus()}else{var t;null==(t=I.beforeOutsideRef.current)||t.focus()}}}}),!M&&es("start"),n,es("end"),ef&&(0,eE.jsx)(ts,{"data-type":"inside",ref:el,onFocus:e=>{if(d)e7(Y()[0]);else if(null!=I&&I.preserveTabOrder&&I.portalNode){if(p&&(F.current=!0),ew(e,I.portalNode)){let e=ey(E);null==e||e.focus()}else{var t;null==(t=I.afterOutsideRef.current)||t.focus()}}}})]})}function tb(e){return c(e.target)&&"BUTTON"===e.target.tagName}function tE(e,t){void 0===t&&(t={});let{open:n,onOpenChange:o,dataRef:i,elements:{domReference:l}}=e,{enabled:u=!0,event:a="click",toggle:s=!0,ignoreMouse:f=!1,keyboardHandlers:d=!0,stickIfOpen:h=!0}=t,p=r.useRef(),m=r.useRef(!1),g=r.useMemo(()=>({onPointerDown(e){p.current=e.pointerType},onMouseDown(e){let t=p.current;0===e.button&&"click"!==a&&(el(t,!0)&&f||(n&&s&&(!i.current.openEvent||!h||"mousedown"===i.current.openEvent.type)?o(!1,e.nativeEvent,"click"):(e.preventDefault(),o(!0,e.nativeEvent,"click"))))},onClick(e){let t=p.current;if("mousedown"===a&&p.current){p.current=void 0;return}el(t,!0)&&f||(n&&s&&(!i.current.openEvent||!h||"click"===i.current.openEvent.type)?o(!1,e.nativeEvent,"click"):o(!0,e.nativeEvent,"click"))},onKeyDown(e){if(p.current=void 0,!(e.defaultPrevented||!d||tb(e)))" "===e.key&&!ee(l)&&(e.preventDefault(),m.current=!0),(!c(e.target)||"A"!==e.target.tagName)&&"Enter"===e.key&&(n&&s?o(!1,e.nativeEvent,"click"):o(!0,e.nativeEvent,"click"))},onKeyUp(e){!(e.defaultPrevented||!d||tb(e)||ee(l))&&" "===e.key&&m.current&&(m.current=!1,n&&s?o(!1,e.nativeEvent,"click"):o(!0,e.nativeEvent,"click"))}}),[i,l,a,f,d,o,n,h,s]);return r.useMemo(()=>u?{reference:g}:{},[u,g])}let tR={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},tx={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},tC=e=>{var t,n;return{escapeKey:"boolean"==typeof e?e:null!=(t=null==e?void 0:e.escapeKey)&&t,outsidePress:"boolean"==typeof e?e:null==(n=null==e?void 0:e.outsidePress)||n}};function tS(e,t){void 0===t&&(t={});let{open:n,onOpenChange:o,elements:i,dataRef:h}=e,{enabled:p=!0,escapeKey:m=!0,outsidePress:g=!0,outsidePressEvent:v="pointerdown",referencePress:y=!1,referencePressEvent:w="pointerdown",ancestorScroll:b=!1,bubbles:E,capture:R}=t,x=eJ(),C=ec("function"==typeof g?g:()=>!1),S="function"==typeof g?C:g,M=r.useRef(!1),{escapeKey:k,outsidePress:A}=tC(E),{escapeKey:L,outsidePress:O}=tC(R),T=r.useRef(!1),N=r.useRef(-1),_=ec(e=>{var t;if(!n||!p||!m||"Escape"!==e.key||T.current)return;let r=null==(t=h.current.floatingContext)?void 0:t.nodeId,i=x?er(x.nodesRef.current,r):[];if(!k&&(e.stopPropagation(),i.length>0)){let e=!0;if(i.forEach(t=>{var n;if(null!=(n=t.context)&&n.open&&!t.context.dataRef.current.__escapeKeyBubbles){e=!1;return}}),!e)return}o(!1,"nativeEvent"in e?e.nativeEvent:e,"escape-key")}),I=ec(e=>{var t;let n=()=>{var t;_(e),null==(t=J(e))||t.removeEventListener("keydown",n)};null==(t=J(e))||t.addEventListener("keydown",n)}),P=ec(e=>{var t;let n=h.current.insideReactTree;h.current.insideReactTree=!1;let r=M.current;if(M.current=!1,"click"===v&&r||n||"function"==typeof S&&!S(e))return;let p=J(e),m="["+e1("inert")+"]",g=Z(i.floating).querySelectorAll(m),y=s(p)?p:null;for(;y&&!d(y);){let e=function(e){var t;if("html"===l(e))return e;let n=e.assignedSlot||e.parentNode||f(e)&&e.host||(null==(t=(a(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement);return f(n)?n.host:n}(y);if(d(e)||!s(e))break;y=e}if(g.length&&s(p)&&!p.matches("html,body")&&!X(p,i.floating)&&Array.from(g).every(e=>!X(y,e)))return;if(c(p)&&B){let t=d(p),n=u(p).getComputedStyle(p),r=/auto|scroll/,o=t||r.test(n.overflowX),i=t||r.test(n.overflowY),l=o&&p.clientWidth>0&&p.scrollWidth>p.clientWidth,a=i&&p.clientHeight>0&&p.scrollHeight>p.clientHeight,s="rtl"===n.direction,c=a&&(s?e.offsetX<=p.offsetWidth-p.clientWidth:e.offsetX>p.clientWidth),f=l&&e.offsetY>p.clientHeight;if(c||f)return}let w=null==(t=h.current.floatingContext)?void 0:t.nodeId,b=x&&er(x.nodesRef.current,w).some(t=>{var n;return G(e,null==(n=t.context)?void 0:n.elements.floating)});if(G(e,i.floating)||G(e,i.domReference)||b)return;let E=x?er(x.nodesRef.current,w):[];if(E.length>0){let e=!0;if(E.forEach(t=>{var n;if(null!=(n=t.context)&&n.open&&!t.context.dataRef.current.__outsidePressBubbles){e=!1;return}}),!e)return}o(!1,e,"outside-press")}),D=ec(e=>{var t;let n=()=>{var t;P(e),null==(t=J(e))||t.removeEventListener(v,n)};null==(t=J(e))||t.addEventListener(v,n)});r.useEffect(()=>{if(!n||!p)return;h.current.__escapeKeyBubbles=k,h.current.__outsidePressBubbles=A;let e=-1;function t(e){o(!1,e,"ancestor-scroll")}function r(){window.clearTimeout(e),T.current=!0}function l(){e=window.setTimeout(()=>{T.current=!1},5*!!("undefined"!=typeof CSS&&CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")))}let u=Z(i.floating);m&&(u.addEventListener("keydown",L?I:_,L),u.addEventListener("compositionstart",r),u.addEventListener("compositionend",l)),S&&u.addEventListener(v,O?D:P,O);let a=[];return b&&(s(i.domReference)&&(a=(0,ex.Kx)(i.domReference)),s(i.floating)&&(a=a.concat((0,ex.Kx)(i.floating))),!s(i.reference)&&i.reference&&i.reference.contextElement&&(a=a.concat((0,ex.Kx)(i.reference.contextElement)))),(a=a.filter(e=>{var t;return e!==(null==(t=u.defaultView)?void 0:t.visualViewport)})).forEach(e=>{e.addEventListener("scroll",t,{passive:!0})}),()=>{m&&(u.removeEventListener("keydown",L?I:_,L),u.removeEventListener("compositionstart",r),u.removeEventListener("compositionend",l)),S&&u.removeEventListener(v,O?D:P,O),a.forEach(e=>{e.removeEventListener("scroll",t)}),window.clearTimeout(e)}},[h,i,m,S,v,n,o,b,p,k,A,_,L,I,P,O,D]),r.useEffect(()=>{h.current.insideReactTree=!1},[h,S,v]);let F=r.useMemo(()=>({onKeyDown:_,...y&&{[tR[w]]:e=>{o(!1,e.nativeEvent,"reference-press")},..."click"!==w&&{onClick(e){o(!1,e.nativeEvent,"reference-press")}}}}),[_,o,y,w]),B=r.useMemo(()=>({onKeyDown:_,onMouseDown(){M.current=!0},onMouseUp(){M.current=!0},[tx[v]]:()=>{h.current.insideReactTree=!0},onBlurCapture(){x||(e2(N),h.current.insideReactTree=!0,N.current=window.setTimeout(()=>{h.current.insideReactTree=!1}))}}),[_,v,h,x]);return r.useMemo(()=>p?{reference:F,floating:B}:{},[p,F,B])}function tM(e){void 0===e&&(e={});let{nodeId:t}=e,n=function(e){let{open:t=!1,onOpenChange:n,elements:o}=e,i=e$(),l=r.useRef({}),[u]=r.useState(()=>eY()),a=null!=eX(),[s,c]=r.useState(o.reference),f=ec((e,t,r)=>{l.current.openEvent=e?t:void 0,u.emit("openchange",{open:e,event:t,reason:r,nested:a}),null==n||n(e,t,r)}),d=r.useMemo(()=>({setPositionReference:c}),[]),h=r.useMemo(()=>({reference:s||o.reference||null,floating:o.floating||null,domReference:o.reference}),[s,o.reference,o.floating]);return r.useMemo(()=>({dataRef:l,open:t,onOpenChange:f,elements:h,events:u,floatingId:i,refs:d}),[t,f,h,u,i,d])}({...e,elements:{reference:null,floating:null,...e.elements}}),o=e.rootContext||n,i=o.elements,[l,u]=r.useState(null),[a,c]=r.useState(null),f=(null==i?void 0:i.domReference)||l,d=r.useRef(null),h=eJ();eu(()=>{f&&(d.current=f)},[f]);let p=(0,eC.YF)({...e,elements:{...i,...a&&{reference:a}}}),m=r.useCallback(e=>{let t=s(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),getClientRects:()=>e.getClientRects(),contextElement:e}:e;c(t),p.refs.setReference(t)},[p.refs]),g=r.useCallback(e=>{(s(e)||null===e)&&(d.current=e,u(e)),(s(p.refs.reference.current)||null===p.refs.reference.current||null!==e&&!s(e))&&p.refs.setReference(e)},[p.refs]),v=r.useMemo(()=>({...p.refs,setReference:g,setPositionReference:m,domReference:d}),[p.refs,g,m]),y=r.useMemo(()=>({...p.elements,domReference:f}),[p.elements,f]),w=r.useMemo(()=>({...p,...o,refs:v,elements:y,nodeId:t}),[p,v,y,t,o]);return eu(()=>{o.dataRef.current.floatingContext=w;let e=null==h?void 0:h.nodesRef.current.find(e=>e.id===t);e&&(e.context=w)}),r.useMemo(()=>({...p,context:w,refs:v,elements:y}),[p,v,y,w])}function tk(){return U().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints&&H()}function tA(e,t){void 0===t&&(t={});let{open:n,onOpenChange:o,events:i,dataRef:l,elements:a}=e,{enabled:f=!0,visibleOnly:d=!0}=t,h=r.useRef(!1),p=r.useRef(-1),m=r.useRef(!0);r.useEffect(()=>{if(!f)return;let e=u(a.domReference);function t(){!n&&c(a.domReference)&&a.domReference===q(Z(a.domReference))&&(h.current=!0)}function r(){m.current=!0}function o(){m.current=!1}return e.addEventListener("blur",t),tk()&&(e.addEventListener("keydown",r,!0),e.addEventListener("pointerdown",o,!0)),()=>{e.removeEventListener("blur",t),tk()&&(e.removeEventListener("keydown",r,!0),e.removeEventListener("pointerdown",o,!0))}},[a.domReference,n,f]),r.useEffect(()=>{if(f)return i.on("openchange",e),()=>{i.off("openchange",e)};function e(e){let{reason:t}=e;("reference-press"===t||"escape-key"===t)&&(h.current=!0)}},[i,f]),r.useEffect(()=>()=>{e2(p)},[]);let g=r.useMemo(()=>({onMouseLeave(){h.current=!1},onFocus(e){if(h.current)return;let t=J(e.nativeEvent);if(d&&s(t)){if(tk()&&!e.relatedTarget){if(!m.current&&!ee(t))return}else if(!function(e){if(!e||K())return!0;try{return e.matches(":focus-visible")}catch(e){return!0}}(t))return}o(!0,e.nativeEvent,"focus")},onBlur(e){h.current=!1;let t=e.relatedTarget,n=e.nativeEvent,r=s(t)&&t.hasAttribute(e1("focus-guard"))&&"outside"===t.getAttribute("data-type");p.current=window.setTimeout(()=>{var e;let i=q(a.domReference?a.domReference.ownerDocument:document);if(t||i!==a.domReference){if(X(null==(e=l.current.floatingContext)?void 0:e.refs.floating.current,i)||X(a.domReference,i)||r)return;o(!1,n,"focus")}})}}),[l,a.domReference,o,d]);return r.useMemo(()=>f?{reference:g}:{},[f,g])}function tL(e,t,n){let r=new Map,o="item"===n,i=e;if(o&&e){let{[eL]:t,[eO]:n,...r}=e;i=r}return{..."floating"===n&&{tabIndex:-1,"data-floating-ui-focusable":""},...i,...t.map(t=>{let r=t?t[n]:null;return"function"==typeof r?e?r(e):null:r}).concat(e).reduce((e,t)=>(t&&Object.entries(t).forEach(t=>{let[n,i]=t;if(!(o&&[eL,eO].includes(n))){if(0===n.indexOf("on")){if(r.has(n)||r.set(n,[]),"function"==typeof i){var l;null==(l=r.get(n))||l.push(i),e[n]=function(){for(var e,t=arguments.length,o=Array(t),i=0;i<t;i++)o[i]=arguments[i];return null==(e=r.get(n))?void 0:e.map(e=>e(...o)).find(e=>void 0!==e)}}}else e[n]=i}}),e),{})}}function tO(e){void 0===e&&(e=[]);let t=e.map(e=>null==e?void 0:e.reference),n=e.map(e=>null==e?void 0:e.floating),o=e.map(e=>null==e?void 0:e.item),i=r.useCallback(t=>tL(t,e,"reference"),t),l=r.useCallback(t=>tL(t,e,"floating"),n),u=r.useCallback(t=>tL(t,e,"item"),o);return r.useMemo(()=>({getReferenceProps:i,getFloatingProps:l,getItemProps:u}),[i,l,u])}let tT=new Map([["select","listbox"],["combobox","listbox"],["label",!1]]);function tN(e,t){var n,o;void 0===t&&(t={});let{open:i,elements:l,floatingId:u}=e,{enabled:a=!0,role:s="dialog"}=t,c=e$(),f=(null==(n=l.domReference)?void 0:n.id)||c,d=r.useMemo(()=>{var e;return(null==(e=en(l.floating))?void 0:e.id)||u},[l.floating,u]),h=null!=(o=tT.get(s))?o:s,p=null!=eX(),m=r.useMemo(()=>"tooltip"===h||"label"===s?{["aria-"+("label"===s?"labelledby":"describedby")]:i?d:void 0}:{"aria-expanded":i?"true":"false","aria-haspopup":"alertdialog"===h?"dialog":h,"aria-controls":i?d:void 0,..."listbox"===h&&{role:"combobox"},..."menu"===h&&{id:f},..."menu"===h&&p&&{role:"menuitem"},..."select"===s&&{"aria-autocomplete":"none"},..."combobox"===s&&{"aria-autocomplete":"list"}},[h,d,p,i,f,s]),g=r.useMemo(()=>{let e={id:d,...h&&{role:h}};return"tooltip"===h||"label"===s?e:{...e,..."menu"===h&&{"aria-labelledby":f}}},[h,d,f,s]),v=r.useCallback(e=>{let{active:t,selected:n}=e,r={role:"option",...t&&{id:d+"-fui-option"}};switch(s){case"select":return{...r,"aria-selected":t&&n};case"combobox":return{...r,"aria-selected":n}}return{}},[d,s]);return r.useMemo(()=>a?{reference:m,floating:g,item:v}:{},[a,m,g,v])}let t_=e=>e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,(e,t)=>(t?"-":"")+e.toLowerCase());function tI(e,t){return"function"==typeof e?e(t):e}function tP(e,t){void 0===t&&(t={});let{initial:n={opacity:0},open:o,close:i,common:l,duration:u=250}=t,a=e.placement,s=a.split("-")[0],c=r.useMemo(()=>({side:s,placement:a}),[s,a]),f="number"==typeof u,d=(f?u:u.open)||0,h=(f?u:u.close)||0,[p,m]=r.useState(()=>({...tI(l,c),...tI(n,c)})),{isMounted:g,status:v}=function(e,t){void 0===t&&(t={});let{open:n,elements:{floating:o}}=e,{duration:i=250}=t,l=("number"==typeof i?i:i.close)||0,[u,a]=r.useState("unmounted"),s=function(e,t){let[n,o]=r.useState(e);return e&&!n&&o(!0),r.useEffect(()=>{if(!e&&n){let e=setTimeout(()=>o(!1),t);return()=>clearTimeout(e)}},[e,n,t]),n}(n,l);return s||"close"!==u||a("unmounted"),eu(()=>{if(o){if(n){a("initial");let e=requestAnimationFrame(()=>{eR.flushSync(()=>{a("open")})});return()=>{cancelAnimationFrame(e)}}a("close")}},[n,o]),{isMounted:s,status:u}}(e,{duration:u}),y=ea(n),w=ea(o),b=ea(i),E=ea(l);return eu(()=>{let e=tI(y.current,c),t=tI(b.current,c),n=tI(E.current,c),r=tI(w.current,c)||Object.keys(e).reduce((e,t)=>(e[t]="",e),{});if("initial"===v&&m(t=>({transitionProperty:t.transitionProperty,...n,...e})),"open"===v&&m({transitionProperty:Object.keys(r).map(t_).join(","),transitionDuration:d+"ms",...n,...r}),"close"===v){let r=t||e;m({transitionProperty:Object.keys(r).map(t_).join(","),transitionDuration:h+"ms",...n,...r})}},[h,b,y,w,E,d,v,c]),{isMounted:g,styles:p}}},1601:function(e,t,n){"use strict";n.d(t,{Fp:()=>o,GW:()=>l,I4:()=>m,JB:()=>C,KX:()=>E,NM:()=>i,Qq:()=>g,Rn:()=>p,VV:()=>r,Wh:()=>v,gy:()=>w,hp:()=>h,i8:()=>y,k3:()=>d,ku:()=>f,pw:()=>R,uZ:()=>c,yd:()=>x,ze:()=>u});let r=Math.min,o=Math.max,i=Math.round,l=Math.floor,u=e=>({x:e,y:e}),a={left:"right",right:"left",bottom:"top",top:"bottom"},s={start:"end",end:"start"};function c(e,t,n){return o(e,r(t,n))}function f(e,t){return"function"==typeof e?e(t):e}function d(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function p(e){return"x"===e?"y":"x"}function m(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(d(e))?"y":"x"}function v(e){return p(g(e))}function y(e,t,n){void 0===n&&(n=!1);let r=h(e),o=v(e),i=m(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=R(l)),[l,R(l)]}function w(e){let t=R(e);return[b(e),t,b(t)]}function b(e){return e.replace(/start|end/g,e=>s[e])}function E(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(d(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(b)))),i}function R(e){return e.replace(/left|right|bottom|top/g,e=>a[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function C(e){return{...e,top:e.y,left:e.x,right:e.x+e.width,bottom:e.y+e.height}}},6164:function(e,t,n){"use strict";function r(e){return l(e)?(e.nodeName||"").toLowerCase():"#document"}function o(e){var t;return(null==e?void 0:null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function i(e){var t;return null==(t=(l(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function l(e){return e instanceof Node||e instanceof o(e).Node}function u(e){return e instanceof Element||e instanceof o(e).Element}function a(e){return e instanceof HTMLElement||e instanceof o(e).HTMLElement}function s(e){return"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof o(e).ShadowRoot)}function c(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=g(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function f(e){return["table","td","th"].includes(r(e))}function d(e){let t=p(),n=g(e);return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function h(e){let t=y(e);for(;a(t)&&!m(t);){if(d(t))return t;t=y(t)}return null}function p(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function m(e){return["html","body","#document"].includes(r(e))}function g(e){return o(e).getComputedStyle(e)}function v(e){return u(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function y(e){if("html"===r(e))return e;let t=e.assignedSlot||e.parentNode||s(e)&&e.host||i(e);return s(t)?t.host:t}n.d(t,{Dx:()=>g,Jj:()=>o,Kx:()=>function e(t,n){var r;void 0===n&&(n=[]);let i=function e(t){let n=y(t);return m(n)?t.ownerDocument?t.ownerDocument.body:t.body:a(n)&&c(n)?n:e(n)}(t),l=i===(null==(r=t.ownerDocument)?void 0:r.body),u=o(i);return l?n.concat(u,u.visualViewport||[],c(i)?i:[],u.frameElement?e(u.frameElement):[]):n.concat(i,e(i))},Lw:()=>v,Ow:()=>y,Pf:()=>p,Py:()=>m,Re:()=>a,Ze:()=>f,ao:()=>c,gQ:()=>h,hT:()=>d,kK:()=>u,tF:()=>i,wk:()=>r})},7627:function(e,t,n){"use strict";let r,o,i;n.d(t,{u:()=>D});var l=n(9144);let u=new Set,a=new WeakMap,s=new WeakMap,c=new WeakMap,f=new WeakMap,d=new WeakMap,h=new WeakMap,p=new WeakMap,m=new WeakSet,g=0,v=0,y="__aa_tgt",w="__aa_del",b="__aa_new";function E(e){clearTimeout(p.get(e));let t=A(e),n=_(t)?500:t.duration;p.set(e,setTimeout(async()=>{let t=c.get(e);try{await (null==t?void 0:t.finished),a.set(e,M(e)),function(e){let t=f.get(e);null==t||t.disconnect();let n=a.get(e),o=0;n||(n=M(e),a.set(e,n));let{offsetWidth:i,offsetHeight:l}=r,u=[n.top-5,i-(n.left+5+n.width),l-(n.top+5+n.height),n.left-5].map(e=>`${-1*Math.floor(e)}px`).join(" "),s=new IntersectionObserver(()=>{++o>1&&E(e)},{root:r,threshold:1,rootMargin:u});s.observe(e),f.set(e,s)}(e)}catch{}},n))}function R(e){setTimeout(()=>{d.set(e,setInterval(()=>x(E.bind(null,e)),2e3))},Math.round(2e3*Math.random()))}function x(e){"function"==typeof requestIdleCallback?requestIdleCallback(()=>e()):requestAnimationFrame(()=>e())}function C(e,t){t||y in e?!t||y in t||Object.defineProperty(t,y,{value:e}):Object.defineProperty(e,y,{value:e})}function S(e){return Number(e.replace(/[^0-9.\-]/g,""))}function M(e){let t=e.getBoundingClientRect(),{x:n,y:r}=function(e){let t=e.parentElement;for(;t;){if(t.scrollLeft||t.scrollTop)return{x:t.scrollLeft,y:t.scrollTop};t=t.parentElement}return{x:0,y:0}}(e);return{top:t.top+r,left:t.left+n,width:t.width,height:t.height}}function k(e,t,n){let r=t.width,o=t.height,i=n.width,l=n.height,u=getComputedStyle(e);if("content-box"===u.getPropertyValue("box-sizing")){let e=S(u.paddingTop)+S(u.paddingBottom)+S(u.borderTopWidth)+S(u.borderBottomWidth),t=S(u.paddingLeft)+S(u.paddingRight)+S(u.borderRightWidth)+S(u.borderLeftWidth);r-=t,i-=t,o-=e,l-=e}return[r,i,o,l].map(Math.round)}function A(e){return y in e&&h.has(e[y])?h.get(e[y]):{duration:250,easing:"ease-in-out"}}function L(e){if(y in e)return e[y]}function O(e){let t=L(e);return!!t&&m.has(t)}function T(e,...t){t.forEach(t=>t(e,h.has(e)));for(let n=0;n<e.children.length;n++){let r=e.children.item(n);r&&t.forEach(e=>e(r,h.has(r)))}}function N(e){return Array.isArray(e)?e:[e]}function _(e){return"function"==typeof e}function I(e){let t;b in e&&delete e[b];let n=M(e);a.set(e,n);let r=A(e);if(O(e)){if("function"!=typeof r)t=e.animate([{transform:"scale(.98)",opacity:0},{transform:"scale(0.98)",opacity:0,offset:.5},{transform:"scale(1)",opacity:1}],{duration:1.5*r.duration,easing:"ease-in"});else{let[o]=N(r(e,"add",n));(t=new Animation(o)).play()}c.set(e,t),t.addEventListener("finish",E.bind(null,e))}}function P(e,t){var n;e.remove(),a.delete(e),s.delete(e),c.delete(e),null===(n=f.get(e))||void 0===n||n.disconnect(),setTimeout(()=>{if(w in e&&delete e[w],Object.defineProperty(e,b,{value:!0,configurable:!0}),t&&e instanceof HTMLElement)for(let n in t)e.style[n]=""},0)}function D(e){let[t,n]=(0,l.useState)(),r=(0,l.useMemo)(()=>e,[]);return[(0,l.useCallback)(e=>{e instanceof HTMLElement?n(function(e,t={}){return o&&i&&!(window.matchMedia("(prefers-reduced-motion: reduce)").matches&&!_(t)&&!t.disrespectUserMotionPreference)&&(m.add(e),"static"===getComputedStyle(e).position&&Object.assign(e.style,{position:"relative"}),T(e,E,R,e=>null==i?void 0:i.observe(e)),_(t)?h.set(e,t):h.set(e,{duration:250,easing:"ease-in-out",...t}),o.observe(e,{childList:!0}),u.add(e)),Object.freeze({parent:e,enable:()=>{m.add(e)},disable:()=>{m.delete(e)},isEnabled:()=>m.has(e)})}(e,r)):n(void 0)},[r]),(0,l.useCallback)(e=>{t&&(e?t.enable():t.disable())},[t])]}"undefined"!=typeof window&&"ResizeObserver"in window&&(r=document.documentElement,o=new MutationObserver(e=>{var t;let n=!(t=e).reduce((e,t)=>[...e,...Array.from(t.addedNodes),...Array.from(t.removedNodes)],[]).every(e=>"#comment"===e.nodeName)&&t.reduce((e,t)=>{if(!1===e)return!1;if(t.target instanceof Element){if(C(t.target),!e.has(t.target)){e.add(t.target);for(let n=0;n<t.target.children.length;n++){let r=t.target.children.item(n);if(r){if(w in r)return!1;C(t.target,r),e.add(r)}}}if(t.removedNodes.length)for(let n=0;n<t.removedNodes.length;n++){let r=t.removedNodes[n];if(w in r)return!1;r instanceof Element&&(e.add(r),C(t.target,r),s.set(r,[t.previousSibling,t.nextSibling]))}}return e},new Set);n&&n.forEach(e=>(function(e){var t;let n=e.isConnected,o=a.has(e);n&&s.has(e)&&s.delete(e),c.has(e)&&(null===(t=c.get(e))||void 0===t||t.cancel()),b in e?I(e):o&&n?function(e){let t;let n=a.get(e),r=M(e);if(!O(e))return a.set(e,r);if(!n)return;let o=A(e);if("function"!=typeof o){let i=n.left-r.left,l=n.top-r.top,[u,a,s,c]=k(e,n,r),f={transform:`translate(${i}px, ${l}px)`},d={transform:"translate(0, 0)"};u!==a&&(f.width=`${u}px`,d.width=`${a}px`),s!==c&&(f.height=`${s}px`,d.height=`${c}px`),t=e.animate([f,d],{duration:o.duration,easing:o.easing})}else{let[i]=N(o(e,"remain",n,r));(t=new Animation(i)).play()}c.set(e,t),a.set(e,r),t.addEventListener("finish",E.bind(null,e))}(e):o&&!n?function(e){var t;let n;if(!s.has(e)||!a.has(e))return;let[o,i]=s.get(e);Object.defineProperty(e,w,{value:!0,configurable:!0});let l=window.scrollX,u=window.scrollY;if(i&&i.parentNode&&i.parentNode instanceof Element?i.parentNode.insertBefore(e,i):o&&o.parentNode?o.parentNode.appendChild(e):null===(t=L(e))||void 0===t||t.appendChild(e),!O(e))return P(e);let[f,d,h,p]=function(e){let t=a.get(e),[n,,r]=k(e,t,M(e)),o=e.parentElement;for(;o&&("static"===getComputedStyle(o).position||o instanceof HTMLBodyElement);)o=o.parentElement;o||(o=document.body);let i=getComputedStyle(o),l=a.get(o)||M(o);return[Math.round(t.top-l.top)-S(i.borderTopWidth),Math.round(t.left-l.left)-S(i.borderLeftWidth),n,r]}(e),m=A(e),y=a.get(e);(l!==g||u!==v)&&function(e,t,n,o){let i=g-t,l=v-n,u=document.documentElement.style.scrollBehavior;if("smooth"===getComputedStyle(r).scrollBehavior&&(document.documentElement.style.scrollBehavior="auto"),window.scrollTo(window.scrollX+i,window.scrollY+l),!e.parentElement)return;let a=e.parentElement,s=a.clientHeight,c=a.clientWidth,f=performance.now();!function e(){requestAnimationFrame(()=>{if(!_(o)){let t=s-a.clientHeight,n=c-a.clientWidth;f+o.duration>performance.now()?(window.scrollTo({left:window.scrollX-n,top:window.scrollY-t}),s=a.clientHeight,c=a.clientWidth,e()):document.documentElement.style.scrollBehavior=u}})}()}(e,l,u,m);let b={position:"absolute",top:`${f}px`,left:`${d}px`,width:`${h}px`,height:`${p}px`,margin:"0",pointerEvents:"none",transformOrigin:"center",zIndex:"100"};if(_(m)){let[t,r]=N(m(e,"remove",y));(null==r?void 0:r.styleReset)!==!1&&(b=(null==r?void 0:r.styleReset)||b,Object.assign(e.style,b)),(n=new Animation(t)).play()}else Object.assign(e.style,b),n=e.animate([{transform:"scale(1)",opacity:1},{transform:"scale(.98)",opacity:0}],{duration:m.duration,easing:"ease-out"});c.set(e,n),n.addEventListener("finish",P.bind(null,e,b))}(e):I(e)})(e))}),i=new ResizeObserver(e=>{e.forEach(e=>{e.target===r&&(clearTimeout(p.get(r)),p.set(r,setTimeout(()=>{u.forEach(e=>T(e,e=>x(()=>E(e))))},100))),a.has(e.target)&&E(e.target)})}),window.addEventListener("scroll",()=>{v=window.scrollY,g=window.scrollX}),i.observe(r))},4662:function(e,t,n){"use strict";n.d(t,{J:()=>i});var r=Object.prototype.hasOwnProperty;function o(e,t,n){for(n of e.keys())if(i(n,t))return n}function i(e,t){var n,l,u;if(e===t)return!0;if(e&&t&&(n=e.constructor)===t.constructor){if(n===Date)return e.getTime()===t.getTime();if(n===RegExp)return e.toString()===t.toString();if(n===Array){if((l=e.length)===t.length)for(;l--&&i(e[l],t[l]););return -1===l}if(n===Set){if(e.size!==t.size)return!1;for(l of e)if((u=l)&&"object"==typeof u&&!(u=o(t,u))||!t.has(u))return!1;return!0}if(n===Map){if(e.size!==t.size)return!1;for(l of e)if((u=l[0])&&"object"==typeof u&&!(u=o(t,u))||!i(l[1],t.get(u)))return!1;return!0}if(n===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(n===DataView){if((l=e.byteLength)===t.byteLength)for(;l--&&e.getInt8(l)===t.getInt8(l););return -1===l}if(ArrayBuffer.isView(e)){if((l=e.byteLength)===t.byteLength)for(;l--&&e[l]===t[l];);return -1===l}if(!n||"object"==typeof e){for(n in l=0,e)if(r.call(e,n)&&++l&&!r.call(t,n)||!(n in t)||!i(e[n],t[n]))return!1;return Object.keys(t).length===l}}return e!=e&&t!=t}},4718:function(e,t,n){"use strict";n.d(t,{t:()=>R});var r,o,i,l=n(9144),u=Object.defineProperty,a=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,f=(e,t,n)=>t in e?u(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,d=(e,t)=>{for(var n in t||(t={}))s.call(t,n)&&f(e,n,t[n]);if(a)for(var n of a(t))c.call(t,n)&&f(e,n,t[n]);return e},h=(e,t)=>{var n={};for(var r in e)s.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&a)for(var r of a(e))0>t.indexOf(r)&&c.call(e,r)&&(n[r]=e[r]);return n};(e=>{let t=class t{constructor(e,n,r,i){if(this.version=e,this.errorCorrectionLevel=n,this.modules=[],this.isFunction=[],e<t.MIN_VERSION||e>t.MAX_VERSION)throw RangeError("Version value out of range");if(i<-1||i>7)throw RangeError("Mask value out of range");this.size=4*e+17;let l=[];for(let e=0;e<this.size;e++)l.push(!1);for(let e=0;e<this.size;e++)this.modules.push(l.slice()),this.isFunction.push(l.slice());this.drawFunctionPatterns();let u=this.addEccAndInterleave(r);if(this.drawCodewords(u),-1==i){let e=1e9;for(let t=0;t<8;t++){this.applyMask(t),this.drawFormatBits(t);let n=this.getPenaltyScore();n<e&&(i=t,e=n),this.applyMask(t)}}o(0<=i&&i<=7),this.mask=i,this.applyMask(i),this.drawFormatBits(i),this.isFunction=[]}static encodeText(n,r){let o=e.QrSegment.makeSegments(n);return t.encodeSegments(o,r)}static encodeBinary(n,r){let o=e.QrSegment.makeBytes(n);return t.encodeSegments([o],r)}static encodeSegments(e,r,i=1,u=40,a=-1,s=!0){let c,f;if(!(t.MIN_VERSION<=i&&i<=u&&u<=t.MAX_VERSION)||a<-1||a>7)throw RangeError("Invalid value");for(c=i;;c++){let n=8*t.getNumDataCodewords(c,r),o=l.getTotalBits(e,c);if(o<=n){f=o;break}if(c>=u)throw RangeError("Data too long")}for(let e of[t.Ecc.MEDIUM,t.Ecc.QUARTILE,t.Ecc.HIGH])s&&f<=8*t.getNumDataCodewords(c,e)&&(r=e);let d=[];for(let t of e)for(let e of(n(t.mode.modeBits,4,d),n(t.numChars,t.mode.numCharCountBits(c),d),t.getData()))d.push(e);o(d.length==f);let h=8*t.getNumDataCodewords(c,r);o(d.length<=h),n(0,Math.min(4,h-d.length),d),n(0,(8-d.length%8)%8,d),o(d.length%8==0);for(let e=236;d.length<h;e^=253)n(e,8,d);let p=[];for(;8*p.length<d.length;)p.push(0);return d.forEach((e,t)=>p[t>>>3]|=e<<7-(7&t)),new t(c,r,p,a)}getModule(e,t){return 0<=e&&e<this.size&&0<=t&&t<this.size&&this.modules[t][e]}getModules(){return this.modules}drawFunctionPatterns(){for(let e=0;e<this.size;e++)this.setFunctionModule(6,e,e%2==0),this.setFunctionModule(e,6,e%2==0);this.drawFinderPattern(3,3),this.drawFinderPattern(this.size-4,3),this.drawFinderPattern(3,this.size-4);let e=this.getAlignmentPatternPositions(),t=e.length;for(let n=0;n<t;n++)for(let r=0;r<t;r++)(0!=n||0!=r)&&(0!=n||r!=t-1)&&(n!=t-1||0!=r)&&this.drawAlignmentPattern(e[n],e[r]);this.drawFormatBits(0),this.drawVersion()}drawFormatBits(e){let t=this.errorCorrectionLevel.formatBits<<3|e,n=t;for(let e=0;e<10;e++)n=n<<1^(n>>>9)*1335;let i=(t<<10|n)^21522;o(i>>>15==0);for(let e=0;e<=5;e++)this.setFunctionModule(8,e,r(i,e));this.setFunctionModule(8,7,r(i,6)),this.setFunctionModule(8,8,r(i,7)),this.setFunctionModule(7,8,r(i,8));for(let e=9;e<15;e++)this.setFunctionModule(14-e,8,r(i,e));for(let e=0;e<8;e++)this.setFunctionModule(this.size-1-e,8,r(i,e));for(let e=8;e<15;e++)this.setFunctionModule(8,this.size-15+e,r(i,e));this.setFunctionModule(8,this.size-8,!0)}drawVersion(){if(this.version<7)return;let e=this.version;for(let t=0;t<12;t++)e=e<<1^(e>>>11)*7973;let t=this.version<<12|e;o(t>>>18==0);for(let e=0;e<18;e++){let n=r(t,e),o=this.size-11+e%3,i=Math.floor(e/3);this.setFunctionModule(o,i,n),this.setFunctionModule(i,o,n)}}drawFinderPattern(e,t){for(let n=-4;n<=4;n++)for(let r=-4;r<=4;r++){let o=Math.max(Math.abs(r),Math.abs(n)),i=e+r,l=t+n;0<=i&&i<this.size&&0<=l&&l<this.size&&this.setFunctionModule(i,l,2!=o&&4!=o)}}drawAlignmentPattern(e,t){for(let n=-2;n<=2;n++)for(let r=-2;r<=2;r++)this.setFunctionModule(e+r,t+n,1!=Math.max(Math.abs(r),Math.abs(n)))}setFunctionModule(e,t,n){this.modules[t][e]=n,this.isFunction[t][e]=!0}addEccAndInterleave(e){let n=this.version,r=this.errorCorrectionLevel;if(e.length!=t.getNumDataCodewords(n,r))throw RangeError("Invalid argument");let i=t.NUM_ERROR_CORRECTION_BLOCKS[r.ordinal][n],l=t.ECC_CODEWORDS_PER_BLOCK[r.ordinal][n],u=Math.floor(t.getNumRawDataModules(n)/8),a=i-u%i,s=Math.floor(u/i),c=[],f=t.reedSolomonComputeDivisor(l);for(let n=0,r=0;n<i;n++){let o=e.slice(r,r+s-l+(n<a?0:1));r+=o.length;let i=t.reedSolomonComputeRemainder(o,f);n<a&&o.push(0),c.push(o.concat(i))}let d=[];for(let e=0;e<c[0].length;e++)c.forEach((t,n)=>{(e!=s-l||n>=a)&&d.push(t[e])});return o(d.length==u),d}drawCodewords(e){if(e.length!=Math.floor(t.getNumRawDataModules(this.version)/8))throw RangeError("Invalid argument");let n=0;for(let t=this.size-1;t>=1;t-=2){6==t&&(t=5);for(let o=0;o<this.size;o++)for(let i=0;i<2;i++){let l=t-i,u=(t+1&2)==0?this.size-1-o:o;!this.isFunction[u][l]&&n<8*e.length&&(this.modules[u][l]=r(e[n>>>3],7-(7&n)),n++)}}o(n==8*e.length)}applyMask(e){if(e<0||e>7)throw RangeError("Mask value out of range");for(let t=0;t<this.size;t++)for(let n=0;n<this.size;n++){let r;switch(e){case 0:r=(n+t)%2==0;break;case 1:r=t%2==0;break;case 2:r=n%3==0;break;case 3:r=(n+t)%3==0;break;case 4:r=(Math.floor(n/3)+Math.floor(t/2))%2==0;break;case 5:r=n*t%2+n*t%3==0;break;case 6:r=(n*t%2+n*t%3)%2==0;break;case 7:r=((n+t)%2+n*t%3)%2==0;break;default:throw Error("Unreachable")}!this.isFunction[t][n]&&r&&(this.modules[t][n]=!this.modules[t][n])}}getPenaltyScore(){let e=0;for(let n=0;n<this.size;n++){let r=!1,o=0,i=[0,0,0,0,0,0,0];for(let l=0;l<this.size;l++)this.modules[n][l]==r?5==++o?e+=t.PENALTY_N1:o>5&&e++:(this.finderPenaltyAddHistory(o,i),r||(e+=this.finderPenaltyCountPatterns(i)*t.PENALTY_N3),r=this.modules[n][l],o=1);e+=this.finderPenaltyTerminateAndCount(r,o,i)*t.PENALTY_N3}for(let n=0;n<this.size;n++){let r=!1,o=0,i=[0,0,0,0,0,0,0];for(let l=0;l<this.size;l++)this.modules[l][n]==r?5==++o?e+=t.PENALTY_N1:o>5&&e++:(this.finderPenaltyAddHistory(o,i),r||(e+=this.finderPenaltyCountPatterns(i)*t.PENALTY_N3),r=this.modules[l][n],o=1);e+=this.finderPenaltyTerminateAndCount(r,o,i)*t.PENALTY_N3}for(let n=0;n<this.size-1;n++)for(let r=0;r<this.size-1;r++){let o=this.modules[n][r];o==this.modules[n][r+1]&&o==this.modules[n+1][r]&&o==this.modules[n+1][r+1]&&(e+=t.PENALTY_N2)}let n=0;for(let e of this.modules)n=e.reduce((e,t)=>e+ +!!t,n);let r=this.size*this.size,i=Math.ceil(Math.abs(20*n-10*r)/r)-1;return o(0<=i&&i<=9),o(0<=(e+=i*t.PENALTY_N4)&&e<=2568888),e}getAlignmentPatternPositions(){if(1==this.version)return[];{let e=Math.floor(this.version/7)+2,t=32==this.version?26:2*Math.ceil((4*this.version+4)/(2*e-2)),n=[6];for(let r=this.size-7;n.length<e;r-=t)n.splice(1,0,r);return n}}static getNumRawDataModules(e){if(e<t.MIN_VERSION||e>t.MAX_VERSION)throw RangeError("Version number out of range");let n=(16*e+128)*e+64;if(e>=2){let t=Math.floor(e/7)+2;n-=(25*t-10)*t-55,e>=7&&(n-=36)}return o(208<=n&&n<=29648),n}static getNumDataCodewords(e,n){return Math.floor(t.getNumRawDataModules(e)/8)-t.ECC_CODEWORDS_PER_BLOCK[n.ordinal][e]*t.NUM_ERROR_CORRECTION_BLOCKS[n.ordinal][e]}static reedSolomonComputeDivisor(e){if(e<1||e>255)throw RangeError("Degree out of range");let n=[];for(let t=0;t<e-1;t++)n.push(0);n.push(1);let r=1;for(let o=0;o<e;o++){for(let e=0;e<n.length;e++)n[e]=t.reedSolomonMultiply(n[e],r),e+1<n.length&&(n[e]^=n[e+1]);r=t.reedSolomonMultiply(r,2)}return n}static reedSolomonComputeRemainder(e,n){let r=n.map(e=>0);for(let o of e){let e=o^r.shift();r.push(0),n.forEach((n,o)=>r[o]^=t.reedSolomonMultiply(n,e))}return r}static reedSolomonMultiply(e,t){if(e>>>8!=0||t>>>8!=0)throw RangeError("Byte out of range");let n=0;for(let r=7;r>=0;r--)n=n<<1^(n>>>7)*285^(t>>>r&1)*e;return o(n>>>8==0),n}finderPenaltyCountPatterns(e){let t=e[1];o(t<=3*this.size);let n=t>0&&e[2]==t&&e[3]==3*t&&e[4]==t&&e[5]==t;return(n&&e[0]>=4*t&&e[6]>=t?1:0)+(n&&e[6]>=4*t&&e[0]>=t?1:0)}finderPenaltyTerminateAndCount(e,t,n){return e&&(this.finderPenaltyAddHistory(t,n),t=0),t+=this.size,this.finderPenaltyAddHistory(t,n),this.finderPenaltyCountPatterns(n)}finderPenaltyAddHistory(e,t){0==t[0]&&(e+=this.size),t.pop(),t.unshift(e)}};function n(e,t,n){if(t<0||t>31||e>>>t!=0)throw RangeError("Value out of range");for(let r=t-1;r>=0;r--)n.push(e>>>r&1)}function r(e,t){return(e>>>t&1)!=0}function o(e){if(!e)throw Error("Assertion error")}t.MIN_VERSION=1,t.MAX_VERSION=40,t.PENALTY_N1=3,t.PENALTY_N2=3,t.PENALTY_N3=40,t.PENALTY_N4=10,t.ECC_CODEWORDS_PER_BLOCK=[[-1,7,10,15,20,26,18,20,24,30,18,20,24,26,30,22,24,28,30,28,28,28,28,30,30,26,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,10,16,26,18,24,16,18,22,22,26,30,22,22,24,24,28,28,26,26,26,26,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28],[-1,13,22,18,26,18,24,18,22,20,24,28,26,24,20,30,24,28,28,26,30,28,30,30,30,30,28,30,30,30,30,30,30,30,30,30,30,30,30,30,30],[-1,17,28,22,16,22,28,26,26,24,28,24,28,22,24,24,30,28,28,26,28,30,24,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30,30]],t.NUM_ERROR_CORRECTION_BLOCKS=[[-1,1,1,1,1,1,2,2,2,2,4,4,4,4,4,6,6,6,6,7,8,8,9,9,10,12,12,12,13,14,15,16,17,18,19,19,20,21,22,24,25],[-1,1,1,1,2,2,4,4,4,5,5,5,8,9,9,10,10,11,13,14,16,17,17,18,20,21,23,25,26,28,29,31,33,35,37,38,40,43,45,47,49],[-1,1,1,2,2,4,4,6,6,8,8,8,10,12,16,12,17,16,18,21,20,23,23,25,27,29,34,34,35,38,40,43,45,48,51,53,56,59,62,65,68],[-1,1,1,2,4,4,4,5,6,8,8,11,11,16,16,18,16,19,21,25,25,25,34,30,32,35,37,40,42,45,48,51,54,57,60,63,66,70,74,77,81]],e.QrCode=t;let i=class e{constructor(e,t,n){if(this.mode=e,this.numChars=t,this.bitData=n,t<0)throw RangeError("Invalid argument");this.bitData=n.slice()}static makeBytes(t){let r=[];for(let e of t)n(e,8,r);return new e(e.Mode.BYTE,t.length,r)}static makeNumeric(t){if(!e.isNumeric(t))throw RangeError("String contains non-numeric characters");let r=[];for(let e=0;e<t.length;){let o=Math.min(t.length-e,3);n(parseInt(t.substring(e,e+o),10),3*o+1,r),e+=o}return new e(e.Mode.NUMERIC,t.length,r)}static makeAlphanumeric(t){let r;if(!e.isAlphanumeric(t))throw RangeError("String contains unencodable characters in alphanumeric mode");let o=[];for(r=0;r+2<=t.length;r+=2){let i=45*e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(r));n(i+=e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(r+1)),11,o)}return r<t.length&&n(e.ALPHANUMERIC_CHARSET.indexOf(t.charAt(r)),6,o),new e(e.Mode.ALPHANUMERIC,t.length,o)}static makeSegments(t){return""==t?[]:e.isNumeric(t)?[e.makeNumeric(t)]:e.isAlphanumeric(t)?[e.makeAlphanumeric(t)]:[e.makeBytes(e.toUtf8ByteArray(t))]}static makeEci(t){let r=[];if(t<0)throw RangeError("ECI assignment value out of range");if(t<128)n(t,8,r);else if(t<16384)n(2,2,r),n(t,14,r);else if(t<1e6)n(6,3,r),n(t,21,r);else throw RangeError("ECI assignment value out of range");return new e(e.Mode.ECI,0,r)}static isNumeric(t){return e.NUMERIC_REGEX.test(t)}static isAlphanumeric(t){return e.ALPHANUMERIC_REGEX.test(t)}getData(){return this.bitData.slice()}static getTotalBits(e,t){let n=0;for(let r of e){let e=r.mode.numCharCountBits(t);if(r.numChars>=1<<e)return 1/0;n+=4+e+r.bitData.length}return n}static toUtf8ByteArray(e){e=encodeURI(e);let t=[];for(let n=0;n<e.length;n++)"%"!=e.charAt(n)?t.push(e.charCodeAt(n)):(t.push(parseInt(e.substring(n+1,n+3),16)),n+=2);return t}};i.NUMERIC_REGEX=/^[0-9]*$/,i.ALPHANUMERIC_REGEX=/^[A-Z0-9 $%*+.\/:-]*$/,i.ALPHANUMERIC_CHARSET="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:";let l=i;e.QrSegment=i})(i||(i={})),(e=>{let t=class{constructor(e,t){this.ordinal=e,this.formatBits=t}};t.LOW=new t(0,1),t.MEDIUM=new t(1,0),t.QUARTILE=new t(2,3),t.HIGH=new t(3,2),e.Ecc=t})((r=i||(i={})).QrCode||(r.QrCode={})),(e=>{let t=class{constructor(e,t){this.modeBits=e,this.numBitsCharCount=t}numCharCountBits(e){return this.numBitsCharCount[Math.floor((e+7)/17)]}};t.NUMERIC=new t(1,[10,12,14]),t.ALPHANUMERIC=new t(2,[9,11,13]),t.BYTE=new t(4,[8,16,16]),t.KANJI=new t(8,[8,10,12]),t.ECI=new t(7,[0,0,0]),e.Mode=t})((o=i||(i={})).QrSegment||(o.QrSegment={}));var p=i,m={L:p.QrCode.Ecc.LOW,M:p.QrCode.Ecc.MEDIUM,Q:p.QrCode.Ecc.QUARTILE,H:p.QrCode.Ecc.HIGH},g="#FFFFFF",v="#000000";function y(e,t=0){let n=[];return e.forEach(function(e,r){let o=null;e.forEach(function(i,l){if(!i&&null!==o){n.push(`M${o+t} ${r+t}h${l-o}v1H${o+t}z`),o=null;return}if(l===e.length-1){if(!i)return;null===o?n.push(`M${l+t},${r+t} h1v1H${l+t}z`):n.push(`M${o+t},${r+t} h${l+1-o}v1H${o+t}z`);return}i&&null===o&&(o=l)})}),n.join("")}function w(e,t){return e.slice().map((e,n)=>n<t.y||n>=t.y+t.h?e:e.map((e,n)=>(n<t.x||n>=t.x+t.w)&&e))}function b({value:e,level:t,minVersion:n,includeMargin:r,marginSize:o,imageSettings:i,size:u,boostLevel:a}){let s=l.useMemo(()=>{let r=(Array.isArray(e)?e:[e]).reduce((e,t)=>(e.push(...p.QrSegment.makeSegments(t)),e),[]);return p.QrCode.encodeSegments(r,m[t],n,void 0,void 0,a)},[e,t,n,a]),{cells:c,margin:f,numCells:d,calculatedImageSettings:h}=l.useMemo(()=>{let e=s.getModules(),t=null!=o?Math.max(Math.floor(o),0):4*!!r,n=e.length+2*t,l=function(e,t,n,r){if(null==r)return null;let o=e.length+2*n,i=Math.floor(.1*t),l=o/t,u=(r.width||i)*l,a=(r.height||i)*l,s=null==r.x?e.length/2-u/2:r.x*l,c=null==r.y?e.length/2-a/2:r.y*l,f=null==r.opacity?1:r.opacity,d=null;if(r.excavate){let e=Math.floor(s),t=Math.floor(c),n=Math.ceil(u+s-e),r=Math.ceil(a+c-t);d={x:e,y:t,w:n,h:r}}return{x:s,y:c,h:a,w:u,excavation:d,opacity:f,crossOrigin:r.crossOrigin}}(e,u,t,i);return{cells:e,margin:t,numCells:n,calculatedImageSettings:l}},[s,u,i,r,o]);return{qrcode:s,margin:f,cells:c,numCells:d,calculatedImageSettings:h}}var E=function(){try{new Path2D().addPath(new Path2D)}catch(e){return!1}return!0}();l.forwardRef(function(e,t){let{value:n,size:r=128,level:o="L",bgColor:i=g,fgColor:u=v,includeMargin:a=!1,minVersion:s=1,boostLevel:c,marginSize:f,imageSettings:p}=e,m=h(e,["value","size","level","bgColor","fgColor","includeMargin","minVersion","boostLevel","marginSize","imageSettings"]),{style:R}=m,x=h(m,["style"]),C=null==p?void 0:p.src,S=l.useRef(null),M=l.useRef(null),k=l.useCallback(e=>{S.current=e,"function"==typeof t?t(e):t&&(t.current=e)},[t]),[A,L]=l.useState(!1),{margin:O,cells:T,numCells:N,calculatedImageSettings:_}=b({value:n,level:o,minVersion:s,boostLevel:c,includeMargin:a,marginSize:f,imageSettings:p,size:r});l.useEffect(()=>{if(null!=S.current){let e=S.current,t=e.getContext("2d");if(!t)return;let n=T,o=M.current,l=null!=_&&null!==o&&o.complete&&0!==o.naturalHeight&&0!==o.naturalWidth;l&&null!=_.excavation&&(n=w(T,_.excavation));let a=window.devicePixelRatio||1;e.height=e.width=r*a;let s=r/N*a;t.scale(s,s),t.fillStyle=i,t.fillRect(0,0,N,N),t.fillStyle=u,E?t.fill(new Path2D(y(n,O))):T.forEach(function(e,n){e.forEach(function(e,r){e&&t.fillRect(r+O,n+O,1,1)})}),_&&(t.globalAlpha=_.opacity),l&&t.drawImage(o,_.x+O,_.y+O,_.w,_.h)}}),l.useEffect(()=>{L(!1)},[C]);let I=d({height:r,width:r},R),P=null;return null!=C&&(P=l.createElement("img",{src:C,key:C,style:{display:"none"},onLoad:()=>{L(!0)},ref:M,crossOrigin:null==_?void 0:_.crossOrigin})),l.createElement(l.Fragment,null,l.createElement("canvas",d({style:I,height:r,width:r,ref:k,role:"img"},x)),P)}).displayName="QRCodeCanvas";var R=l.forwardRef(function(e,t){let{value:n,size:r=128,level:o="L",bgColor:i=g,fgColor:u=v,includeMargin:a=!1,minVersion:s=1,boostLevel:c,title:f,marginSize:p,imageSettings:m}=e,E=h(e,["value","size","level","bgColor","fgColor","includeMargin","minVersion","boostLevel","title","marginSize","imageSettings"]),{margin:R,cells:x,numCells:C,calculatedImageSettings:S}=b({value:n,level:o,minVersion:s,boostLevel:c,includeMargin:a,marginSize:p,imageSettings:m,size:r}),M=x,k=null;null!=m&&null!=S&&(null!=S.excavation&&(M=w(x,S.excavation)),k=l.createElement("image",{href:m.src,height:S.h,width:S.w,x:S.x+R,y:S.y+R,preserveAspectRatio:"none",opacity:S.opacity,crossOrigin:S.crossOrigin}));let A=y(M,R);return l.createElement("svg",d({height:r,width:r,viewBox:`0 0 ${C} ${C}`,ref:t,role:"img"},E),!!f&&l.createElement("title",null,f),l.createElement("path",{fill:i,d:`M0,0 h${C}v${C}H0z`,shapeRendering:"crispEdges"}),l.createElement("path",{fill:u,d:A,shapeRendering:"crispEdges"}),k)});R.displayName="QRCodeSVG"},5715:function(e,t,n){"use strict";n.d(t,{j:()=>H,u:()=>L,s:()=>D,f:()=>K,d:()=>V,o:()=>B,b:()=>l,m:()=>d,U:()=>a,z:()=>E,S:()=>$,i:()=>v,t:()=>A,O:()=>s,g:()=>Y,B:()=>h,e:()=>c,r:()=>k,c:()=>W,n:()=>z,a:()=>f,I:()=>M});var r=n(9144),o=n(8523),i=Object.prototype.hasOwnProperty;let l=new WeakMap,u=()=>{},a=u(),s=Object,c=e=>e===a,f=e=>"function"==typeof e,d=(e,t)=>({...e,...t}),h=e=>f(e.then),p={},m={},g="undefined",v=typeof window!=g,y=typeof document!=g,w=v&&"Deno"in window,b=()=>v&&typeof window.requestAnimationFrame!=g,E=(e,t)=>{let n=l.get(e);return[()=>!c(t)&&e.get(t)||p,r=>{if(!c(t)){let o=e.get(t);t in m||(m[t]=o),n[5](t,d(o,r),o||p)}},n[6],()=>!c(t)&&t in m?m[t]:!c(t)&&e.get(t)||p]},R=!0,[x,C]=v&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[u,u],S={initFocus:e=>(y&&document.addEventListener("visibilitychange",e),x("focus",e),()=>{y&&document.removeEventListener("visibilitychange",e),C("focus",e)}),initReconnect:e=>{let t=()=>{R=!0,e()},n=()=>{R=!1};return x("online",t),x("offline",n),()=>{C("online",t),C("offline",n)}}},M=!r.useId,k=!v||w,A=e=>b()?window.requestAnimationFrame(e):setTimeout(e,1),L=k?r.useEffect:r.useLayoutEffect,O="undefined"!=typeof navigator&&navigator.connection,T=!k&&O&&(["slow-2g","2g"].includes(O.effectiveType)||O.saveData),N=new WeakMap,_=(e,t)=>s.prototype.toString.call(e)===`[object ${t}]`,I=0,P=e=>{let t,n;let r=typeof e,o=_(e,"Date"),i=_(e,"RegExp"),l=_(e,"Object");if(s(e)!==e||o||i)t=o?e.toJSON():"symbol"==r?e.toString():"string"==r?JSON.stringify(e):""+e;else{if(t=N.get(e))return t;if(t=++I+"~",N.set(e,t),Array.isArray(e)){for(n=0,t="@";n<e.length;n++)t+=P(e[n])+",";N.set(e,t)}if(l){t="#";let r=s.keys(e).sort();for(;!c(n=r.pop());)c(e[n])||(t+=n+":"+P(e[n])+",");N.set(e,t)}}return t},D=e=>{if(f(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?P(e):"",t]},F=0,B=()=>++F;async function z(...e){let[t,n,r,i]=e,u=d({populateCache:!0,throwOnError:!0},"boolean"==typeof i?{revalidate:i}:i||{}),s=u.populateCache,p=u.rollbackOnError,m=u.optimisticData,g=e=>"function"==typeof p?p(e):!1!==p,v=u.throwOnError;if(f(n)){let e=[];for(let r of t.keys())!/^\$(inf|sub)\$/.test(r)&&n(t.get(r)._k)&&e.push(r);return Promise.all(e.map(y))}return y(n);async function y(n){let i;let[d]=D(n);if(!d)return;let[p,y]=E(t,d),[w,b,R,x]=l.get(t),C=()=>{let e=w[d];return(f(u.revalidate)?u.revalidate(p().data,n):!1!==u.revalidate)&&(delete R[d],delete x[d],e&&e[0])?e[0](o.QQ).then(()=>p().data):p().data};if(e.length<3)return C();let S=r,M=B();b[d]=[M,0];let k=!c(m),A=p(),L=A.data,O=A._c,T=c(O)?L:O;if(k&&y({data:m=f(m)?m(T,L):m,_c:T}),f(S))try{S=S(T)}catch(e){i=e}if(S&&h(S)){if(S=await S.catch(e=>{i=e}),M!==b[d][0]){if(i)throw i;return S}i&&k&&g(i)&&(s=!0,y({data:T,_c:a}))}if(s&&!i&&(f(s)?y({data:s(S,T),error:a,_c:a}):y({data:S,error:a,_c:a})),b[d][1]=B(),Promise.resolve(C()).then(()=>{y({_c:a})}),i){if(v)throw i;return}return S}}let j=(e,t)=>{for(let n in e)e[n][0]&&e[n][0](t)},U=(e,t)=>{if(!l.has(e)){let n=d(S,t),r=Object.create(null),i=z.bind(a,e),s=u,c=Object.create(null),f=(e,t)=>{let n=c[e]||[];return c[e]=n,n.push(t),()=>n.splice(n.indexOf(t),1)},h=(t,n,r)=>{e.set(t,n);let o=c[t];if(o)for(let e of o)e(n,r)},p=()=>{if(!l.has(e)&&(l.set(e,[r,Object.create(null),Object.create(null),Object.create(null),i,h,f]),!k)){let t=n.initFocus(setTimeout.bind(a,j.bind(a,r,o.N4))),i=n.initReconnect(setTimeout.bind(a,j.bind(a,r,o.l2)));s=()=>{t&&t(),i&&i(),l.delete(e)}}};return p(),[e,i,p,s]}return[e,l.get(e)[4]]},[W,H]=U(new Map),V=d({onLoadingSlow:u,onSuccess:u,onError:u,onErrorRetry:(e,t,n,r,o)=>{let i=n.errorRetryCount,l=o.retryCount,u=~~((Math.random()+.5)*(1<<(l<8?l:8)))*n.errorRetryInterval;(c(i)||!(l>i))&&setTimeout(r,u,o)},onDiscarded:u,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:T?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:T?5e3:3e3,compare:function e(t,n){var r,o;if(t===n)return!0;if(t&&n&&(r=t.constructor)===n.constructor){if(r===Date)return t.getTime()===n.getTime();if(r===RegExp)return t.toString()===n.toString();if(r===Array){if((o=t.length)===n.length)for(;o--&&e(t[o],n[o]););return -1===o}if(!r||"object"==typeof t){for(r in o=0,t)if(i.call(t,r)&&++o&&!i.call(n,r)||!(r in n)||!e(t[r],n[r]))return!1;return Object.keys(n).length===o}}return t!=t&&n!=n},isPaused:()=>!1,cache:W,mutate:H,fallback:{}},{isOnline:()=>R,isVisible:()=>{let e=y&&document.visibilityState;return c(e)||"hidden"!==e}}),K=(e,t)=>{let n=d(e,t);if(t){let{use:r,fallback:o}=e,{use:i,fallback:l}=t;r&&i&&(n.use=r.concat(i)),o&&l&&(n.fallback=d(o,l))}return n},$=(0,r.createContext)({}),Y=e=>{let{value:t}=e,n=(0,r.useContext)($),o=f(t),i=(0,r.useMemo)(()=>o?t(n):t,[o,n,t]),l=(0,r.useMemo)(()=>o?i:K(n,i),[o,n,i]),u=i&&i.provider,s=(0,r.useRef)(a);u&&!s.current&&(s.current=U(u(l.cache||W),i));let c=s.current;return c&&(l.cache=c[0],l.mutate=c[1]),L(()=>{if(c)return c[2]&&c[2](),c[3]},[]),(0,r.createElement)($.Provider,d(e,{value:l}))}},761:function(e,t,n){"use strict";n.d(t,{U:()=>r});let r="$inf$"},8523:function(e,t,n){"use strict";n.d(t,{N4:()=>r,QQ:()=>i,aU:()=>l,l2:()=>o});let r=0,o=1,i=2,l=3},4126:function(e,t,n){"use strict";n.d(t,{MA:()=>c,kY:()=>s,ko:()=>h,s6:()=>d,xD:()=>p});var r=n(5715),o=n(761),i=n(9144);let l=r.i&&window.__SWR_DEVTOOLS_USE__,u=l?window.__SWR_DEVTOOLS_USE__:[],a=e=>(0,r.a)(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],s=()=>(0,r.m)(r.d,(0,i.useContext)(r.S)),c=(e,t)=>{let[n,o]=(0,r.s)(e),[,,,i]=r.b.get(r.c);if(i[n])return i[n];let l=t(o);return i[n]=l,l},f=u.concat(e=>(t,n,i)=>{let l=n&&((...e)=>{let[i]=(0,r.s)(t),[,,,l]=r.b.get(r.c);if(i.startsWith(o.U))return n(...e);let u=l[i];return(0,r.e)(u)?n(...e):(delete l[i],u)});return e(t,l,i)}),d=e=>function(...t){let n=s(),[o,i,l]=a(t),u=(0,r.f)(n,l),c=e,{use:d}=u,h=(d||[]).concat(f);for(let e=h.length;e--;)c=h[e](c);return c(o,i||u.fetcher||null,u)},h=(e,t,n)=>{let r=t[e]||(t[e]=[]);return r.push(n),()=>{let e=r.indexOf(n);e>=0&&(r[e]=r[r.length-1],r.pop())}},p=(e,t)=>(...n)=>{let[r,o,i]=a(n),l=(i.use||[]).concat(t);return e(r,o,{...i,use:l})};l&&(window.__SWR_DEVTOOLS_REACT__=i)},9626:function(e,t,n){"use strict";n.r(t),n.d(t,{SWRConfig:()=>E,default:()=>R,mutate:()=>i.j,preload:()=>u.MA,unstable_serialize:()=>y,useSWRConfig:()=>u.kY});var r=n(9144),o=n(6753),i=n(5715),l=n(8523),u=n(4126);let a=()=>{},s=a(),c=Object,f=e=>e===s,d=e=>"function"==typeof e,h=new WeakMap,p=(e,t)=>c.prototype.toString.call(e)===`[object ${t}]`,m=0,g=e=>{let t,n;let r=typeof e,o=p(e,"Date"),i=p(e,"RegExp"),l=p(e,"Object");if(c(e)!==e||o||i)t=o?e.toJSON():"symbol"==r?e.toString():"string"==r?JSON.stringify(e):""+e;else{if(t=h.get(e))return t;if(t=++m+"~",h.set(e,t),Array.isArray(e)){for(n=0,t="@";n<e.length;n++)t+=g(e[n])+",";h.set(e,t)}if(l){t="#";let r=c.keys(e).sort();for(;!f(n=r.pop());)f(e[n])||(t+=n+":"+g(e[n])+",");h.set(e,t)}}return t},v=e=>{if(d(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?g(e):"",t]},y=e=>v(e)[0],w=r.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),b={dedupe:!0},E=i.O.defineProperty(i.g,"defaultValue",{value:i.d}),R=(0,u.s6)((e,t,n)=>{let{cache:a,compare:s,suspense:c,fallbackData:f,revalidateOnMount:d,revalidateIfStale:h,refreshInterval:p,refreshWhenHidden:m,refreshWhenOffline:g,keepPreviousData:v}=n,[y,E,R,x]=i.b.get(a),[C,S]=(0,i.s)(e),M=(0,r.useRef)(!1),k=(0,r.useRef)(!1),A=(0,r.useRef)(C),L=(0,r.useRef)(t),O=(0,r.useRef)(n),T=()=>O.current,N=()=>T().isVisible()&&T().isOnline(),[_,I,P,D]=(0,i.z)(a,C),F=(0,r.useRef)({}).current,B=(0,i.e)(f)?(0,i.e)(n.fallback)?i.U:n.fallback[C]:f,z=(e,t)=>{for(let n in F)if("data"===n){if(!s(e[n],t[n])&&(!(0,i.e)(e[n])||!s(Q,t[n])))return!1}else if(t[n]!==e[n])return!1;return!0},j=(0,r.useMemo)(()=>{let e=!!C&&!!t&&((0,i.e)(d)?!T().isPaused()&&!c&&!1!==h:d),n=t=>{let n=(0,i.m)(t);return(delete n._k,e)?{isValidating:!0,isLoading:!0,...n}:n},r=_(),o=D(),l=n(r),u=r===o?l:n(o),a=l;return[()=>{let e=n(_());return z(e,a)?(a.data=e.data,a.isLoading=e.isLoading,a.isValidating=e.isValidating,a.error=e.error,a):(a=e,e)},()=>u]},[a,C]),U=(0,o.useSyncExternalStore)((0,r.useCallback)(e=>P(C,(t,n)=>{z(n,t)||e()}),[a,C]),j[0],j[1]),W=!M.current,H=y[C]&&y[C].length>0,V=U.data,K=(0,i.e)(V)?B&&(0,i.B)(B)?w(B):B:V,$=U.error,Y=(0,r.useRef)(K),Q=v?(0,i.e)(V)?(0,i.e)(Y.current)?K:Y.current:V:K,q=(!H||!!(0,i.e)($))&&(W&&!(0,i.e)(d)?d:!T().isPaused()&&(c?!(0,i.e)(K)&&h:(0,i.e)(K)||h)),X=!!(C&&t&&W&&q),J=(0,i.e)(U.isValidating)?X:U.isValidating,G=(0,i.e)(U.isLoading)?X:U.isLoading,Z=(0,r.useCallback)(async e=>{let t,r;let o=L.current;if(!C||!o||k.current||T().isPaused())return!1;let u=!0,a=e||{},c=!R[C]||!a.dedupe,f=()=>i.I?!k.current&&C===A.current&&M.current:C===A.current,d={isValidating:!1,isLoading:!1},h=()=>{I(d)},p=()=>{let e=R[C];e&&e[1]===r&&delete R[C]},m={isValidating:!0};(0,i.e)(_().data)&&(m.isLoading=!0);try{if(c&&(I(m),n.loadingTimeout&&(0,i.e)(_().data)&&setTimeout(()=>{u&&f()&&T().onLoadingSlow(C,n)},n.loadingTimeout),R[C]=[o(S),(0,i.o)()]),[t,r]=R[C],t=await t,c&&setTimeout(p,n.dedupingInterval),!R[C]||R[C][1]!==r)return c&&f()&&T().onDiscarded(C),!1;d.error=i.U;let e=E[C];if(!(0,i.e)(e)&&(r<=e[0]||r<=e[1]||0===e[1]))return h(),c&&f()&&T().onDiscarded(C),!1;let l=_().data;d.data=s(l,t)?l:t,c&&f()&&T().onSuccess(t,C,n)}catch(n){p();let e=T(),{shouldRetryOnError:t}=e;!e.isPaused()&&(d.error=n,c&&f()&&(e.onError(n,C,e),(!0===t||(0,i.a)(t)&&t(n))&&(!T().revalidateOnFocus||!T().revalidateOnReconnect||N())&&e.onErrorRetry(n,C,e,e=>{let t=y[C];t&&t[0]&&t[0](l.aU,e)},{retryCount:(a.retryCount||0)+1,dedupe:!0})))}return u=!1,h(),!0},[C,a]),ee=(0,r.useCallback)((...e)=>(0,i.n)(a,A.current,...e),[]);if((0,i.u)(()=>{L.current=t,O.current=n,(0,i.e)(V)||(Y.current=V)}),(0,i.u)(()=>{if(!C)return;let e=Z.bind(i.U,b),t=0;T().revalidateOnFocus&&(t=Date.now()+T().focusThrottleInterval);let n=(0,u.ko)(C,y,(n,r={})=>{if(n==l.N4){let n=Date.now();T().revalidateOnFocus&&n>t&&N()&&(t=n+T().focusThrottleInterval,e())}else if(n==l.l2)T().revalidateOnReconnect&&N()&&e();else if(n==l.QQ)return Z();else if(n==l.aU)return Z(r)});return k.current=!1,A.current=C,M.current=!0,I({_k:S}),q&&((0,i.e)(K)||i.r?e():(0,i.t)(e)),()=>{k.current=!0,n()}},[C]),(0,i.u)(()=>{let e;function t(){let t=(0,i.a)(p)?p(_().data):p;t&&-1!==e&&(e=setTimeout(n,t))}function n(){!_().error&&(m||T().isVisible())&&(g||T().isOnline())?Z(b).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[p,m,g,C]),(0,r.useDebugValue)(Q),c&&(0,i.e)(K)&&C){if(!i.I&&i.r)throw Error("Fallback data is required when using Suspense in SSR.");L.current=t,O.current=n,k.current=!1;let e=x[C];if((0,i.e)(e)||w(ee(e)),(0,i.e)($)){let e=Z(b);(0,i.e)(Q)||(e.status="fulfilled",e.value=!0),w(e)}else throw $}return{mutate:ee,get data(){return F.data=!0,Q},get error(){return F.error=!0,$},get isValidating(){return F.isValidating=!0,J},get isLoading(){return F.isLoading=!0,G}}})},762:function(e,t,n){"use strict";n.d(t,{ZP:()=>E});var r=n(9144),o=n(9626),i=n(5715),l=n(761),u=n(4126),a=n(6753);let s=()=>{},c=s(),f=Object,d=e=>e===c,h=e=>"function"==typeof e,p=new WeakMap,m=(e,t)=>f.prototype.toString.call(e)===`[object ${t}]`,g=0,v=e=>{let t,n;let r=typeof e,o=m(e,"Date"),i=m(e,"RegExp"),l=m(e,"Object");if(f(e)!==e||o||i)t=o?e.toJSON():"symbol"==r?e.toString():"string"==r?JSON.stringify(e):""+e;else{if(t=p.get(e))return t;if(t=++g+"~",p.set(e,t),Array.isArray(e)){for(n=0,t="@";n<e.length;n++)t+=v(e[n])+",";p.set(e,t)}if(l){t="#";let r=f.keys(e).sort();for(;!d(n=r.pop());)d(e[n])||(t+=n+":"+v(e[n])+",");p.set(e,t)}}return t},y=e=>{if(h(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?v(e):"",t]},w=e=>y(e?e(0,null):null)[0],b=Promise.resolve(),E=(0,u.xD)(o.default,e=>(t,n,o)=>{let u;let s=(0,r.useRef)(!1),{cache:c,initialSize:f=1,revalidateAll:d=!1,persistSize:h=!1,revalidateFirstPage:p=!0,revalidateOnMount:m=!1,parallel:g=!1}=o,[,,,v]=i.b.get(i.c);try{(u=w(t))&&(u=l.U+u)}catch(e){}let[y,E,R]=(0,i.z)(c,u),x=(0,r.useCallback)(()=>(0,i.e)(y()._l)?f:y()._l,[c,u,f]);(0,a.useSyncExternalStore)((0,r.useCallback)(e=>u?R(u,()=>{e()}):()=>{},[c,u]),x,x);let C=(0,r.useCallback)(()=>{let e=y()._l;return(0,i.e)(e)?f:e},[u,f]),S=(0,r.useRef)(C());(0,i.u)(()=>{if(!s.current){s.current=!0;return}u&&E({_l:h?S.current:C()})},[u,c]);let M=m&&!s.current,k=e(u,async e=>{let r=y()._i,l=y()._r;E({_r:i.U});let u=[],a=C(),[s]=(0,i.z)(c,e),f=s().data,h=[],m=null;for(let e=0;e<a;++e){let[a,s]=(0,i.s)(t(e,g?null:m));if(!a)break;let[y,w]=(0,i.z)(c,a),b=y().data,E=d||r||(0,i.e)(b)||p&&!e&&!(0,i.e)(f)||M||f&&!(0,i.e)(f[e])&&!o.compare(f[e],b);if(n&&("function"==typeof l?l(b,s):E)){let t=async()=>{if(a in v){let e=v[a];delete v[a],b=await e}else b=await n(s);w({data:b,_k:s}),u[e]=b};g?h.push(t):await t()}else u[e]=b;g||(m=b)}return g&&await Promise.all(h.map(e=>e())),E({_i:i.U}),u},o),A=(0,r.useCallback)(function(e,t){let n="boolean"==typeof t?{revalidate:t}:t||{},r=!1!==n.revalidate;return u?(r&&((0,i.e)(e)?E({_i:!0,_r:n.revalidate}):E({_i:!1,_r:n.revalidate})),arguments.length?k.mutate(e,{...n,revalidate:r}):k.mutate()):b},[u,c]),L=(0,r.useCallback)(e=>{let n;if(!u)return b;let[,r]=(0,i.z)(c,u);if((0,i.a)(e)?n=e(C()):"number"==typeof e&&(n=e),"number"!=typeof n)return b;r({_l:n}),S.current=n;let o=[],[l]=(0,i.z)(c,u),a=null;for(let e=0;e<n;++e){let[n]=(0,i.s)(t(e,a)),[r]=(0,i.z)(c,n),u=n?r().data:i.U;if((0,i.e)(u))return A(l().data);o.push(u),a=u}return A(o)},[u,c,A,C]);return{size:C(),setSize:L,mutate:A,get data(){return k.data},get error(){return k.error},get isValidating(){return k.isValidating},get isLoading(){return k.isLoading}}})},903:function(e,t,n){"use strict";n.d(t,{Z:()=>s});var r=n(9144),o=n(4126),i=n(9626),l=n(5715);let u=l.I?e=>{e()}:r.startTransition,a=e=>{let[,t]=(0,r.useState)({}),n=(0,r.useRef)(!1),o=(0,r.useRef)(e),i=(0,r.useRef)({data:!1,error:!1,isValidating:!1}),u=(0,r.useCallback)(e=>{let r=!1,l=o.current;for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&l[t]!==e[t]&&(l[t]=e[t],i.current[t]&&(r=!0));r&&!n.current&&t({})},[]);return(0,l.u)(()=>(n.current=!1,()=>{n.current=!0})),[o,i.current,u]},s=(0,o.xD)(i.default,()=>(e,t,n={})=>{let{mutate:i}=(0,o.kY)(),s=(0,r.useRef)(e),c=(0,r.useRef)(t),f=(0,r.useRef)(n),d=(0,r.useRef)(0),[h,p,m]=a({data:l.U,error:l.U,isMutating:!1}),g=h.current,v=(0,r.useCallback)(async(e,t)=>{let[n,r]=(0,l.s)(s.current);if(!c.current)throw Error("Can’t trigger the mutation: missing fetcher.");if(!n)throw Error("Can’t trigger the mutation: missing key.");let o=(0,l.m)((0,l.m)({populateCache:!1,throwOnError:!0},f.current),t),a=(0,l.o)();d.current=a,m({isMutating:!0});try{let t=await i(n,c.current(r,{arg:e}),(0,l.m)(o,{throwOnError:!0}));return d.current<=a&&(u(()=>m({data:t,isMutating:!1,error:void 0})),null==o.onSuccess||o.onSuccess.call(o,t,n,o)),t}catch(e){if(d.current<=a&&(u(()=>m({error:e,isMutating:!1})),null==o.onError||o.onError.call(o,e,n,o),o.throwOnError))throw e}},[]),y=(0,r.useCallback)(()=>{d.current=(0,l.o)(),m({data:l.U,error:l.U,isMutating:!1})},[]);return(0,l.u)(()=>{s.current=e,c.current=t,f.current=n}),{trigger:v,reset:y,get data(){return p.data=!0,g.data},get error(){return p.error=!0,g.error},get isMutating(){return p.isMutating=!0,g.isMutating}}})}}]);