import React from 'react';
import type { FeedbackType } from '../../utils';
import type { CountryIso } from './countryCodeData';
export declare const PhoneInput: React.ForwardRefExoticComponent<Omit<Omit<import("../../primitives").InputProps, "ref"> & React.RefAttributes<HTMLInputElement> & {
    elementDescriptor?: import("../../customizables/elementDescriptors").ElementDescriptor | Array<import("../../customizables/elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("../../customizables/elementDescriptors").ElementId;
    css?: never;
    sx?: import("../../styledSystem").ThemableCssProp;
} & {
    locationBasedCountryIso?: CountryIso;
} & {
    feedbackType?: FeedbackType;
}, "ref"> & React.RefAttributes<HTMLInputElement>>;
