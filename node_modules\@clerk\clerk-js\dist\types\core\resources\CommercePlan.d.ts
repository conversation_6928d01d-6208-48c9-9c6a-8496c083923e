import type { CommercePlanJSON, CommercePlanJSONSnapshot, CommercePlanResource } from '@clerk/types';
import { BaseResource, CommerceFeature } from './internal';
export declare class CommercePlan extends BaseResource implements CommercePlanResource {
    id: string;
    name: string;
    amount: number;
    amountFormatted: string;
    annualAmount: number;
    annualAmountFormatted: string;
    annualMonthlyAmount: number;
    annualMonthlyAmountFormatted: string;
    currencySymbol: string;
    currency: string;
    description: string;
    isDefault: boolean;
    isRecurring: boolean;
    hasBaseFee: boolean;
    payerType: string[];
    publiclyVisible: boolean;
    slug: string;
    avatarUrl: string;
    features: CommerceFeature[];
    constructor(data: CommercePlanJSON);
    protected fromJSON(data: CommercePlanJSON | null): this;
    __internal_toSnapshot(): CommercePlanJSONSnapshot;
}
