import type { CustomMenuItem, LoadedClerk } from '@clerk/types';
import type { LocalizationKey } from '../customizables';
export type DefaultItemIds = 'manageAccount' | 'addAccount' | 'signOut' | 'signOutAll';
export type MenuItem = {
    id: string;
    name: LocalizationKey | string;
    icon: React.ComponentType;
    onClick?: () => void;
    open?: string;
    path?: string;
};
export declare const createUserButtonCustomMenuItems: (customMenuItems: CustomMenuItem[], clerk: LoadedClerk) => MenuItem[];
