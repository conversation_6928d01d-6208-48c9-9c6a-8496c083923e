export declare const shadows: Readonly<{
    readonly menuShadow: "0px 5px 15px 0px rgba(0, 0, 0, 0.08), 0px 15px 35px -5px rgba(25, 28, 33, 0.20)";
    readonly fabShadow: "0px 12px 24px rgba(0, 0, 0, 0.32)";
    readonly buttonShadow: "0px 1px 1px 0px rgba(255, 255, 255, 0.07) inset, 0px 2px 3px 0px rgba(34, 42, 53, 0.20), 0px 1px 1px 0px rgba(0, 0, 0, 0.24)";
    readonly cardBoxShadow: "0px 5px 15px 0px rgba(0, 0, 0, 0.08), 0px 15px 35px -5px rgba(25, 28, 33, 0.20)";
    readonly cardContentShadow: "0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 1px 2px 0px rgba(25, 28, 33, 0.06)";
    readonly actionCardShadow: "0px 1px 4px 0px rgba(0, 0, 0, 0.12), 0px 4px 8px 0px rgba(106, 115, 133, 0.12)";
    readonly outlineButtonShadow: "0px 2px 3px -1px rgba(0, 0, 0, 0.08), 0px 1px 0px 0px rgba(0, 0, 0, 0.02)";
    readonly input: "0px 0px 1px 0px {{color}}";
    readonly focusRing: "0px 0px 0px 4px {{color}}";
    readonly badge: "0px 2px 0px -1px rgba(0, 0, 0, 0.04)";
    readonly tableBodyShadow: "0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 1px 2px 0px rgba(25, 28, 33, 0.12), 0px 0px 0px 1px rgba(0, 0, 0, 0.06)";
    readonly segmentedControl: "0px 1px 2px 0px rgba(0, 0, 0, 0.08)";
    readonly switchControl: "0px 2px 2px -1px rgba(0, 0, 0, 0.06), 0px 0px 0px 1px rgba(0, 0, 0, 0.06), 0px 4px 4px -2px rgba(0, 0, 0, 0.06)";
}>;
