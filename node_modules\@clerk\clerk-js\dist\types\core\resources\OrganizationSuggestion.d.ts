import type { ClerkPaginatedResponse, GetUserOrganizationSuggestionsParams, OrganizationSuggestionJSON, OrganizationSuggestionResource, OrganizationSuggestionStatus, UserOrganizationInvitationResource } from '@clerk/types';
import { BaseResource } from './Base';
export declare class OrganizationSuggestion extends BaseResource implements OrganizationSuggestionResource {
    id: string;
    publicOrganizationData: UserOrganizationInvitationResource['publicOrganizationData'];
    status: OrganizationSuggestionStatus;
    createdAt: Date;
    updatedAt: Date;
    constructor(data: OrganizationSuggestionJSON);
    static retrieve(params?: GetUserOrganizationSuggestionsParams): Promise<ClerkPaginatedResponse<OrganizationSuggestion>>;
    accept: () => Promise<OrganizationSuggestionResource>;
    protected fromJSON(data: OrganizationSuggestionJSON | null): this;
}
