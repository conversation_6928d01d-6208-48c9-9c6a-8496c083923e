import type { CheckAuthorization, OrganizationCustomRoleKey, OrganizationPermissionKey } from '@clerk/types';
import type { ComponentType, PropsWithChildren, ReactNode } from 'react';
import React from 'react';
type ProtectParams = Parameters<CheckAuthorization>[0] | ((has: CheckAuthorization) => boolean);
type ProtectProps = PropsWithChildren<({
    condition?: never;
    role: OrganizationCustomRoleKey;
    permission?: never;
} | {
    condition?: never;
    role?: never;
    permission: OrganizationPermissionKey;
} | {
    condition: (has: CheckAuthorization) => boolean;
    role?: never;
    permission?: never;
}) & {
    fallback?: ReactNode;
    redirectTo?: string;
}>;
/**
 * `useProtect` is just an abstraction on top of `useSession` that improves the internal DX for authorization
 * @param params
 */
export declare const useProtect: (params: ProtectParams) => boolean;
export declare const Protect: (protectProps: ProtectProps) => import("@emotion/react/jsx-runtime").JSX.Element | null;
export declare function withProtect<P>(Component: ComponentType<P>, protectProps: ProtectProps): React.ComponentType<P>;
export {};
