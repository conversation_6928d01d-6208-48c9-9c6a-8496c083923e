import type { CommerceProductJSON, CommerceProductResource } from '@clerk/types';
import { BaseResource, CommercePlan } from './internal';
export declare class CommerceProduct extends BaseResource implements CommerceProductResource {
    id: string;
    slug: string;
    currency: string;
    isDefault: boolean;
    plans: CommercePlan[];
    constructor(data: CommerceProductJSON);
    protected fromJSON(data: CommerceProductJSON | null): this;
}
