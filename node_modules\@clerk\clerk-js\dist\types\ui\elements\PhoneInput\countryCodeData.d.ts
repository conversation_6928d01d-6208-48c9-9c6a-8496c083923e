export type CountryCodeData = typeof data;
export type CountryName = CountryCodeData[number][0];
export type CountryIso = CountryCodeData[number][1];
export type DialingCode = CountryCodeData[number][2];
export type PhonePattern = CountryCodeData[number][3] | '';
declare const data: readonly [readonly ["United States", "us", "1", "(...) ...-....", 100], readonly ["United Kingdom", "gb", "44", ".... ......", 100], readonly ["India", "in", "91", ".....-.....", 100], readonly ["Canada", "ca", "1", "(...) ...-....", 100], readonly ["Germany", "de", "49", "... .......", 100], readonly ["France", "fr", "33", ". .. .. .. ..", 100], readonly ["Russia", "ru", "7", "... ...-..-..", 100], readonly ["Afghanistan", "af", "93"], readonly ["Albania", "al", "355"], readonly ["Algeria ", "dz", "213"], readonly ["American Samoa", "as", "1684"], readonly ["Andorra", "ad", "376"], readonly ["Angola", "ao", "244"], readonly ["<PERSON>uilla", "ai", "1264"], readonly ["<PERSON>gua and <PERSON>buda", "ag", "1268"], readonly ["Argentina", "ar", "54"], readonly ["Armenia", "am", "374"], readonly ["Aruba", "aw", "297"], readonly ["Australia", "au", "61", "... ... ..."], readonly ["Austria", "at", "43"], readonly ["Azerbaijan", "az", "994"], readonly ["Bahamas", "bs", "1242"], readonly ["Bahrain", "bh", "973"], readonly ["Bangladesh", "bd", "880"], readonly ["Barbados", "bb", "1246"], readonly ["Belarus", "by", "375"], readonly ["Belgium", "be", "32", "... .. .. .."], readonly ["Belize", "bz", "501"], readonly ["Benin", "bj", "229"], readonly ["Bermuda", "bm", "1441"], readonly ["Bhutan", "bt", "975"], readonly ["Bolivia", "bo", "591"], readonly ["Bosnia and Herzegovina", "ba", "387"], readonly ["Botswana", "bw", "267"], readonly ["Brazil", "br", "55"], readonly ["British Indian Ocean Territory", "io", "246"], readonly ["British Virgin Islands", "vg", "1284"], readonly ["Brunei", "bn", "673"], readonly ["Bulgaria", "bg", "359"], readonly ["Burkina Faso", "bf", "226"], readonly ["Burundi", "bi", "257"], readonly ["Cambodia", "kh", "855"], readonly ["Cameroon", "cm", "237"], readonly ["Cape Verde", "cv", "238"], readonly ["Caribbean Netherlands", "bq", "599"], readonly ["Cayman Islands", "ky", "1345"], readonly ["Central African Republic", "cf", "236"], readonly ["Chad", "td", "235"], readonly ["Chile", "cl", "56"], readonly ["China", "cn", "86", "...-....-...."], readonly ["Colombia", "co", "57"], readonly ["Comoros", "km", "269"], readonly ["Congo", "cd", "243"], readonly ["Congo", "cg", "242"], readonly ["Cook Islands", "ck", "682"], readonly ["Costa Rica", "cr", "506", "....-...."], readonly ["Côte d’Ivoire", "ci", "225"], readonly ["Croatia", "hr", "385"], readonly ["Cuba", "cu", "53"], readonly ["Curaçao", "cw", "599"], readonly ["Cyprus", "cy", "357"], readonly ["Czech Republic", "cz", "420"], readonly ["Denmark", "dk", "45", ".. .. .. .."], readonly ["Djibouti", "dj", "253"], readonly ["Dominica", "dm", "1767"], readonly ["Dominican Republic", "do", "1"], readonly ["Ecuador", "ec", "593"], readonly ["Egypt", "eg", "20"], readonly ["El Salvador", "sv", "503", "....-...."], readonly ["Equatorial Guinea", "gq", "240"], readonly ["Eritrea", "er", "291"], readonly ["Estonia", "ee", "372"], readonly ["Ethiopia", "et", "251"], readonly ["Falkland Islands", "fk", "500"], readonly ["Faroe Islands", "fo", "298"], readonly ["Fiji", "fj", "679"], readonly ["Finland", "fi", "358", ".. ... .. .."], readonly ["French Guiana", "gf", "594"], readonly ["French Polynesia", "pf", "689"], readonly ["Gabon", "ga", "241"], readonly ["Gambia", "gm", "220"], readonly ["Georgia", "ge", "995"], readonly ["Ghana", "gh", "233"], readonly ["Gibraltar", "gi", "350"], readonly ["Greece", "gr", "30", "... ......."], readonly ["Greenland", "gl", "299"], readonly ["Grenada", "gd", "1473"], readonly ["Guadeloupe", "gp", "590"], readonly ["Guam", "gu", "1671"], readonly ["Guatemala", "gt", "502", "....-...."], readonly ["Guinea", "gn", "224"], readonly ["Guinea-Bissau", "gw", "245"], readonly ["Guyana", "gy", "592"], readonly ["Haiti", "ht", "509", "....-...."], readonly ["Honduras", "hn", "504"], readonly ["Hong Kong", "hk", "852", ".... ...."], readonly ["Hungary", "hu", "36"], readonly ["Iceland", "is", "354", "... ...."], readonly ["Indonesia", "id", "62"], readonly ["Iran", "ir", "98"], readonly ["Iraq", "iq", "964"], readonly ["Ireland", "ie", "353", ".. ......."], readonly ["Israel", "il", "972"], readonly ["Italy", "it", "39", "... ......"], readonly ["Jamaica", "jm", "1876"], readonly ["Japan", "jp", "81", "... .. ...."], readonly ["Jordan", "jo", "962"], readonly ["Kazakhstan", "kz", "7", "... ...-..-.."], readonly ["Kenya", "ke", "254"], readonly ["Kiribati", "ki", "686"], readonly ["Kuwait", "kw", "965"], readonly ["Kyrgyzstan", "kg", "996"], readonly ["Laos", "la", "856"], readonly ["Latvia", "lv", "371"], readonly ["Lebanon", "lb", "961"], readonly ["Lesotho", "ls", "266"], readonly ["Liberia", "lr", "231"], readonly ["Libya", "ly", "218"], readonly ["Liechtenstein", "li", "423"], readonly ["Lithuania", "lt", "370"], readonly ["Luxembourg", "lu", "352"], readonly ["Macau", "mo", "853"], readonly ["Macedonia", "mk", "389"], readonly ["Madagascar", "mg", "261"], readonly ["Malawi", "mw", "265"], readonly ["Malaysia", "my", "60", "..-....-...."], readonly ["Maldives", "mv", "960"], readonly ["Mali", "ml", "223"], readonly ["Malta", "mt", "356"], readonly ["Marshall Islands", "mh", "692"], readonly ["Martinique", "mq", "596"], readonly ["Mauritania", "mr", "222"], readonly ["Mauritius", "mu", "230"], readonly ["Mexico", "mx", "52"], readonly ["Micronesia", "fm", "691"], readonly ["Moldova", "md", "373"], readonly ["Monaco", "mc", "377"], readonly ["Mongolia", "mn", "976"], readonly ["Montenegro", "me", "382"], readonly ["Montserrat", "ms", "1664"], readonly ["Morocco", "ma", "212"], readonly ["Mozambique", "mz", "258"], readonly ["Myanmar", "mm", "95"], readonly ["Namibia", "na", "264"], readonly ["Nauru", "nr", "674"], readonly ["Nepal", "np", "977"], readonly ["Netherlands", "nl", "31", ".. ........"], readonly ["New Caledonia", "nc", "687"], readonly ["New Zealand", "nz", "64", "...-...-...."], readonly ["Nicaragua", "ni", "505"], readonly ["Niger", "ne", "227"], readonly ["Nigeria", "ng", "234"], readonly ["Niue", "nu", "683"], readonly ["Norfolk Island", "nf", "672"], readonly ["North Korea", "kp", "850"], readonly ["Northern Mariana Islands", "mp", "1670"], readonly ["Norway", "no", "47", "... .. ..."], readonly ["Oman", "om", "968"], readonly ["Pakistan", "pk", "92", "...-......."], readonly ["Palau", "pw", "680"], readonly ["Palestine", "ps", "970"], readonly ["Panama", "pa", "507"], readonly ["Papua New Guinea", "pg", "675"], readonly ["Paraguay", "py", "595"], readonly ["Peru", "pe", "51"], readonly ["Philippines", "ph", "63", "... ...."], readonly ["Poland", "pl", "48", "...-...-..."], readonly ["Portugal", "pt", "351"], readonly ["Puerto Rico", "pr", "1"], readonly ["Qatar", "qa", "974"], readonly ["Réunion", "re", "262"], readonly ["Romania", "ro", "40"], readonly ["Rwanda", "rw", "250"], readonly ["Saint Barthélemy", "bl", "590"], readonly ["Saint Helena", "sh", "290"], readonly ["Saint Kitts and Nevis", "kn", "1869"], readonly ["Saint Lucia", "lc", "1758"], readonly ["Saint Martin", "mf", "590"], readonly ["Saint Pierre and Miquelon", "pm", "508"], readonly ["Saint Vincent and the Grenadines", "vc", "1784"], readonly ["Samoa", "ws", "685"], readonly ["San Marino", "sm", "378"], readonly ["São Tomé and Príncipe", "st", "239"], readonly ["Saudi Arabia", "sa", "966"], readonly ["Senegal", "sn", "221"], readonly ["Serbia", "rs", "381"], readonly ["Seychelles", "sc", "248"], readonly ["Sierra Leone", "sl", "232"], readonly ["Singapore", "sg", "65", "....-...."], readonly ["Sint Maarten", "sx", "1721"], readonly ["Slovakia", "sk", "421"], readonly ["Slovenia", "si", "386"], readonly ["Solomon Islands", "sb", "677"], readonly ["Somalia", "so", "252"], readonly ["South Africa", "za", "27"], readonly ["South Korea", "kr", "82"], readonly ["South Sudan", "ss", "211"], readonly ["Spain", "es", "34", "... ... ..."], readonly ["Sri Lanka", "lk", "94"], readonly ["Sudan", "sd", "249"], readonly ["Suriname", "sr", "597"], readonly ["Swaziland", "sz", "268"], readonly ["Sweden", "se", "46", ".. ... .. .."], readonly ["Switzerland", "ch", "41", ".. ... .. .."], readonly ["Syria", "sy", "963"], readonly ["Taiwan", "tw", "886"], readonly ["Tajikistan", "tj", "992"], readonly ["Tanzania", "tz", "255"], readonly ["Thailand", "th", "66"], readonly ["Timor-Leste", "tl", "670"], readonly ["Togo", "tg", "228"], readonly ["Tokelau", "tk", "690"], readonly ["Tonga", "to", "676"], readonly ["Trinidad and Tobago", "tt", "1868"], readonly ["Tunisia", "tn", "216"], readonly ["Turkey", "tr", "90", "... ... .. .."], readonly ["Turkmenistan", "tm", "993"], readonly ["Turks and Caicos Islands", "tc", "1649"], readonly ["Tuvalu", "tv", "688"], readonly ["U.S. Virgin Islands", "vi", "1340"], readonly ["Uganda", "ug", "256"], readonly ["Ukraine", "ua", "380"], readonly ["United Arab Emirates", "ae", "971"], readonly ["Uruguay", "uy", "598"], readonly ["Uzbekistan", "uz", "998"], readonly ["Vanuatu", "vu", "678"], readonly ["Vatican City", "va", "39", ".. .... ...."], readonly ["Venezuela", "ve", "58"], readonly ["Vietnam", "vn", "84"], readonly ["Wallis and Futuna", "wf", "681"], readonly ["Yemen", "ye", "967"], readonly ["Zambia", "zm", "260"], readonly ["Zimbabwe", "zw", "263"]];
export declare const SubAreaCodeSets: {
    us: ReadonlySet<string>;
    ca: ReadonlySet<string>;
};
export interface CountryEntry {
    name: CountryName;
    iso: CountryIso;
    code: DialingCode;
    pattern: PhonePattern;
    priority: number;
}
export type IsoToCountryMapType = ReadonlyMap<CountryIso, CountryEntry>;
export declare const IsoToCountryMap: IsoToCountryMapType;
export type CodeToCountryIsoMapType = ReadonlyMap<DialingCode, CountryEntry[]>;
export declare const CodeToCountriesMap: CodeToCountryIsoMapType;
export {};
