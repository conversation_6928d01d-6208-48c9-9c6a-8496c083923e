import type { Clerk, ClerkOptions, EnvironmentResource } from '@clerk/types';
import type { ComponentType } from 'react';
import type { ComponentGuard } from '../../utils';
import type { AvailableComponentProps } from '../types';
type RedirectUrl = (opts: {
    clerk: Clerk;
    environment: EnvironmentResource;
    options: ClerkOptions;
}) => string;
export declare function withRedirect<P extends AvailableComponentProps>(Component: ComponentType<P>, condition: ComponentGuard, redirectUrl: RedirectUrl, warning?: string): (props: P) => null | JSX.Element;
export declare const withRedirectToAfterSignIn: <P extends AvailableComponentProps>(Component: ComponentType<P>) => {
    (props: P): JSX.Element | null;
    displayName: string;
};
export declare const withRedirectToAfterSignUp: <P extends AvailableComponentProps>(Component: ComponentType<P>) => {
    (props: P): JSX.Element | null;
    displayName: string;
};
export {};
