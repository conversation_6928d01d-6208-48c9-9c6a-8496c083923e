{"version": 3, "sources": ["../src/tr-TR.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const trTR: LocalizationResource = {\n  locale: 'tr-TR',\n  backButton: 'Geri',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'Varsayılan',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Diğer taklit eden cihaz',\n  badge__primary: 'Birincil',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Eylem gerekli',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Bu cihaz',\n  badge__unverified: 'Doğrulanmamış',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Kullanıcı cihazı',\n  badge__you: 'Siz',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Oluştur',\n    invitePage: {\n      formButtonReset: 'Atla',\n    },\n    title: 'Organizasyon oluştur',\n  },\n  dates: {\n    lastDay: \"Dün saat {{ date | timeString('tr-TR') }}\",\n    next6Days: \"{{ date | weekday('tr-TR','long') }} saat {{ date | timeString('tr-TR') }}\",\n    nextDay: \"Yarın saat {{ date | timeString('tr-TR') }}\",\n    numeric: \"{{ date | numeric('tr-TR') }}\",\n    previous6Days: \"Geçen hafta {{ date | weekday('tr-TR','long') }} saat {{ date | timeString('tr-TR') }}\",\n    sameDay: \"Bugün saat {{ date | timeString('tr-TR') }}\",\n  },\n  dividerText: 'veya',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Başka bir yöntem kullan',\n  footerPageLink__help: 'Yardım',\n  footerPageLink__privacy: 'Gizlilik',\n  footerPageLink__terms: 'Şartlar',\n  formButtonPrimary: 'İleri',\n  formButtonPrimary__verify: 'Doğrula',\n  formFieldAction__forgotPassword: 'Şifremi unuttum',\n  formFieldError__matchingPasswords: 'Şifreler eşleşmeli.',\n  formFieldError__notMatchingPasswords: 'Şifreler eşleşmiyor.',\n  formFieldError__verificationLinkExpired: 'Doğrulama bağlantısının süresi doldu. Lütfen yeni bir bağlantı isteyin.',\n  formFieldHintText__optional: 'İsteğe bağlı',\n  formFieldHintText__slug: \"Slug, okunabilir bir ID'dir ve benzersiz olmalıdır. Genellikle URL'lerde kullanılır.\",\n  formFieldInputPlaceholder__backupCode: 'Yedek kodu girin',\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Hesabı sil',\n  formFieldInputPlaceholder__emailAddress: '<EMAIL>',\n  formFieldInputPlaceholder__emailAddress_username: 'email veya kullanıcı adı',\n  formFieldInputPlaceholder__emailAddresses: '<EMAIL>, <EMAIL>',\n  formFieldInputPlaceholder__firstName: 'Adınızı girin',\n  formFieldInputPlaceholder__lastName: 'Soyadınızı girin',\n  formFieldInputPlaceholder__organizationDomain: 'Kuruluş alan adını girin',\n  formFieldInputPlaceholder__organizationDomainEmailAddress: 'Kuruluş e-posta adresini girin',\n  formFieldInputPlaceholder__organizationName: 'Kuruluş adını girin',\n  formFieldInputPlaceholder__organizationSlug: 'kurulus-adi',\n  formFieldInputPlaceholder__password: 'Şifrenizi girin',\n  formFieldInputPlaceholder__phoneNumber: 'Telefon numaranızı girin',\n  formFieldInputPlaceholder__username: 'Kullanıcı adınızı girin',\n  formFieldLabel__automaticInvitations: 'Bu alan adı için otomatik davetleri etkinleştir',\n  formFieldLabel__backupCode: 'Yedekleme kodu',\n  formFieldLabel__confirmDeletion: 'Onayla',\n  formFieldLabel__confirmPassword: 'Şifreyi onayla',\n  formFieldLabel__currentPassword: 'Mevcut şifre',\n  formFieldLabel__emailAddress: 'E-posta adresi',\n  formFieldLabel__emailAddress_username: 'E-posta adresi veya kullanıcı adı',\n  formFieldLabel__emailAddresses: 'E-posta adresleri',\n  formFieldLabel__firstName: 'Ad',\n  formFieldLabel__lastName: 'Soyad',\n  formFieldLabel__newPassword: 'Yeni şifre',\n  formFieldLabel__organizationDomain: 'Alan adı',\n  formFieldLabel__organizationDomainDeletePending: 'Bekleyen davetiyeleri ve önerileri sil',\n  formFieldLabel__organizationDomainEmailAddress: 'Doğrulama e-posta adresi',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Bu alan adı için bir e-posta adresi girin, doğrulama kodunu alın ve alan adını doğrulayın.',\n  formFieldLabel__organizationName: 'Organizasyon adı',\n  formFieldLabel__organizationSlug: 'Organizasyon bağlantı metni',\n  formFieldLabel__passkeyName: 'Geçiş anahtarının adı',\n  formFieldLabel__password: 'Şifre',\n  formFieldLabel__phoneNumber: 'Telefon numarası',\n  formFieldLabel__role: 'Rol',\n  formFieldLabel__signOutOfOtherSessions: 'Diğer cihazlardaki oturumlardan çık',\n  formFieldLabel__username: 'Kullanıcı adı',\n  impersonationFab: {\n    action__signOut: 'Çıkış yap',\n    title: '{{identifier}} olarak giriş yapıldı',\n  },\n  maintenanceMode: 'Şu anda bakımdayız, ancak endişelenmeyin, kısa bir süre içinde tamamlanacaktır.',\n  membershipRole__admin: 'Yönetici',\n  membershipRole__basicMember: 'Üye',\n  membershipRole__guestMember: 'Misafir',\n  organizationList: {\n    action__createOrganization: 'Organizasyon oluştur',\n    action__invitationAccept: 'Katıl',\n    action__suggestionsAccept: 'Katılmak için talepte bulun',\n    createOrganization: 'Organizasyon Oluştur',\n    invitationAcceptedLabel: 'Katılım başarılı',\n    subtitle: '{{applicationName}} ile devam edin',\n    suggestionsAcceptedLabel: 'Onay bekleniyor',\n    title: 'Bir hesap seçin',\n    titleWithoutPersonal: 'Bir organizasyon seçin',\n  },\n  organizationProfile: {\n    badge__automaticInvitation: 'Otomatik davetler',\n    badge__automaticSuggestion: 'Otomatik öneriler',\n    badge__manualInvitation: 'Davetler',\n    badge__unverified: 'Doğrulanmamış',\n    billingPage: {\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Alan adını ekleyerek doğrulama yapın. Bu alana sahip e-posta adresleri ile kaydolan kullanıcılar organizasyona otomatik olarak katılabilir veya katılma talebi gönderebilir.',\n      title: 'Alan adı ekle',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed:\n        'Davetiyeler gönderilemedi. Aşağıdaki e-posta adresleri için zaten bekleyen davetler var: {{email_addresses}}.',\n      formButtonPrimary__continue: 'Davetiye gönder',\n      selectDropdown__role: 'Rol seçin',\n      subtitle: 'Boşluk veya virgülle ayırarak bir veya daha fazla e-posta adresi girin veya yapıştırın.',\n      successMessage: 'Davetler başarıyla gönderildi',\n      title: 'Yeni üyeler davet edin',\n    },\n    membersPage: {\n      action__invite: 'Davet et',\n      action__search: 'Ara',\n      activeMembersTab: {\n        menuAction__remove: 'Üyeyi kaldır',\n        tableHeader__actions: 'İşlemler',\n        tableHeader__joined: 'Katılma tarihi',\n        tableHeader__role: 'Rol',\n        tableHeader__user: 'Kullanıcı',\n      },\n      detailsTitle__emptyRow: 'Görüntülenecek üye yok',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Organizasyonunuzla ilişkili bir alan adı uzantısına sahip e-posta adresini girerek kullanıcıları otomatik olarak davet edin. Bu alana sahip e-posta ile kaydolan herkes, istediği zaman organizasyona katılabilir.',\n          headerTitle: 'Otomatik davetler',\n          primaryButton: 'Doğrulanmış alan adlarını yönet',\n        },\n        table__emptyRow: 'Gösterilecek davetiye yok',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Daveti iptal et',\n        tableHeader__invited: 'Davet Edilenler',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Eşleşen alan adı uzantısına sahip e-posta ile kaydolan kullanıcılar, katılma talebi gönderebilmek için otomatik öneri alacaklar.',\n          headerTitle: 'Otomatik öneriler',\n          primaryButton: 'Doğrulanmış alan adlarını yönet',\n        },\n        menuAction__approve: 'Onayla',\n        menuAction__reject: 'Reddet',\n        tableHeader__requested: 'Katılma isteği',\n        table__emptyRow: 'Görüntülenecek herhangi bir istek yok',\n      },\n      start: {\n        headerTitle__invitations: 'Davetler',\n        headerTitle__members: 'Üyeler',\n        headerTitle__requests: 'İstekler',\n      },\n    },\n    navbar: {\n      billing: undefined,\n      description: 'Organizasyonunuzu yönetin.',\n      general: 'Genel',\n      members: 'Üyeler',\n      title: 'Organizasyon',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Devam etmek için aşağıya “{{organizationName}}” yazın.',\n          messageLine1: 'Bu organizasyonu silmek istediğinizden emin misiniz?',\n          messageLine2: 'Bu işlem kalıcıdır ve geri alınamaz.',\n          successMessage: 'Organizasyonu sildiniz.',\n          title: 'Organizasyonu sil',\n        },\n        leaveOrganization: {\n          actionDescription: 'Devam etmek için aşağıya “{{organizationName}}” yazın.',\n          messageLine1:\n            'Bu organizasyondan ayrılmak istediğinizden emin misiniz? Bu organizasyona ve uygulamalarına erişiminizi kaybedeceksiniz.',\n          messageLine2: 'Bu işlem kalıcıdır ve geri alınamaz.',\n          successMessage: 'Organizasyondan ayrıldınız.',\n          title: 'Organizasyondan ayrıl',\n        },\n        title: 'Tehlike',\n      },\n      domainSection: {\n        menuAction__manage: 'Yönet',\n        menuAction__remove: 'Sil',\n        menuAction__verify: 'Doğrula',\n        primaryButton: 'Alan adı ekle',\n        subtitle:\n          'Kullanıcıların organizasyona otomatik olarak katılmasına veya doğrulanmış bir alan adı uzantılı e-posta ile katılma isteğinde bulunmasına izin verin.',\n        title: 'Doğrulanmış alan adları',\n      },\n      successMessage: 'Organizasyon güncellendi.',\n      title: 'Organizasyon profili',\n    },\n    removeDomainPage: {\n      messageLine1: '{{domain}} Alan adı kaldırılacaktır.',\n      messageLine2: 'Kullanıcılar bundan sonra organizasyona otomatik olarak katılamayacaklar.',\n      successMessage: '{{domain}} kaldırıldı.',\n      title: 'Alan adını kaldır',\n    },\n    start: {\n      headerTitle__general: 'Genel',\n      headerTitle__members: 'Üyeler',\n      profileSection: {\n        primaryButton: 'Güncelle',\n        title: 'Organizasyon Profili',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Bu alan adını kaldırmak, davet edilmiş kullanıcıları etkileyecektir.',\n        removeDomainActionLabel__remove: 'Alan adını kaldır',\n        removeDomainSubtitle: 'Bu alan adını doğrulanmış alan adlarınızdan kaldırın',\n        removeDomainTitle: 'Alan adını kaldır',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Kullanıcılar, kayıt olduklarında otomatik olarak organizasyona davet edilir ve istedikleri zaman katılabilirler.',\n        automaticInvitationOption__label: 'Otomatik davetler',\n        automaticSuggestionOption__description:\n          'Kullanıcılar, katılma talebinde bulunmak için otomatik öneri alırlar; ancak organizasyona katılabilmeleri için bir yönetici onayı gereklidir.',\n        automaticSuggestionOption__label: 'Otomatik öneriler',\n        calloutInfoLabel: 'Kayıt türünün değiştirilmesi yalnızca yeni kullanıcıları etkileyecektir.',\n        calloutInvitationCountLabel: 'Kullanıcılara gönderilen ve bekleyen davetler: {{count}}',\n        calloutSuggestionCountLabel: 'Kullanıcılara gönderilen ve bekleyen öneriler: {{count}}',\n        manualInvitationOption__description: 'Kullanıcılar organizasyona sadece manuel olarak davet edilebilir.',\n        manualInvitationOption__label: 'Manuel davet',\n        subtitle: 'Bu alan adı altındaki kullanıcıların organizasyona nasıl katılacaklarını seçin.',\n      },\n      start: {\n        headerTitle__danger: 'Tehlike',\n        headerTitle__enrollment: 'Kayıt seçenekleri',\n      },\n      subtitle: 'Alan adı {{domain}} şimdi doğrulandı. Devam etmek için kayıt modunu seçin.',\n      title: '{{domain}} adlı alan adını güncelle',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'E-posta adresinize gönderilen doğrulama kodunu girin',\n      formTitle: 'Doğrulama kodu',\n      resendButton: 'Kod almadınız mı? Tekrar gönderin',\n      subtitle: '{{domainName}} Alan adının e-posta ile doğrulanması gerekiyor.',\n      subtitleVerificationCodeScreen:\n        '{{emailAddress}} adresine bir doğrulama kodu gönderildi. Devam etmek için kodu giriniz.',\n      title: 'Alan adını doğrula',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Organizasyon oluştur',\n    action__invitationAccept: 'Katıl',\n    action__manageOrganization: 'Organizasyonu yönet',\n    action__suggestionsAccept: 'Katılmak için talepte bulun',\n    notSelected: 'Organizasyon seçilmedi',\n    personalWorkspace: 'Kişisel Çalışma Alanı',\n    suggestionsAcceptedLabel: 'Onay bekleniyor',\n  },\n  paginationButton__next: 'İleri',\n  paginationButton__previous: 'Geri',\n  paginationRowText__displaying: 'Sayfa bilgisi:',\n  paginationRowText__of: '-',\n  reverification: {\n    alternativeMethods: {\n      actionLink: 'Yardım almak için buraya tıklayın',\n      actionText: 'Alternatif doğrulama yöntemlerini kullanmak ister misiniz?',\n      blockButton__backupCode: 'Yedek kodu kullan',\n      blockButton__emailCode: 'E-posta kodu gönder',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Şifreyi gir',\n      blockButton__phoneCode: 'Telefon kodu gönder',\n      blockButton__totp: 'TOTP kodu kullan',\n      getHelp: {\n        blockButton__emailSupport: 'E-posta ile destek alın',\n        content: 'Hala yardıma ihtiyacınız varsa, destek ekibimizle iletişime geçin.',\n        title: 'Yardım Al',\n      },\n      subtitle: 'Doğrulama işlemi için alternatif yöntemler.',\n      title: 'Alternatif Doğrulama Yöntemleri',\n    },\n    backupCodeMfa: {\n      subtitle: 'Yedek kodu girerek doğrulamanızı tamamlayabilirsiniz.',\n      title: 'Yedek Kod ile Doğrulama',\n    },\n    emailCode: {\n      formTitle: 'E-posta ile Doğrulama',\n      resendButton: 'Tekrar gönder',\n      subtitle: 'E-posta adresinize gönderilen doğrulama kodunu girin.',\n      title: 'E-posta Kodunu Girin',\n    },\n    noAvailableMethods: {\n      message: 'Alternatif doğrulama yöntemleri mevcut değil.',\n      subtitle: 'Lütfen farklı bir doğrulama yöntemi deneyin.',\n      title: 'Doğrulama Yöntemleri Yok',\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Şifremi unuttum',\n      subtitle: 'Şifrenizi girerek devam edebilirsiniz.',\n      title: 'Şifre ile Giriş Yapın',\n    },\n    phoneCode: {\n      formTitle: 'Telefon Kodu ile Doğrulama',\n      resendButton: 'Telefon kodunu tekrar gönder',\n      subtitle: 'Telefonunuza gönderilen doğrulama kodunu girin.',\n      title: 'Telefon Kodu Girin',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Telefon Koduyla MFA Doğrulaması',\n      resendButton: 'Telefon kodunu tekrar gönder',\n      subtitle: 'Telefonunuza gönderilen doğrulama kodunu girin.',\n      title: 'Telefon Koduyla Doğrulama',\n    },\n    totpMfa: {\n      formTitle: 'TOTP ile MFA Doğrulaması',\n      subtitle: 'Mobil uygulamanızdan TOTP kodunu girin.',\n      title: 'TOTP Kodunu Girin',\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Hesap ekle',\n      action__signOutAll: 'Mevcut tüm oturumları kapatın',\n      subtitle: 'Devam etmek istediğiniz hesabı seçin.',\n      title: 'Bir hesap seçiniz',\n    },\n    alternativeMethods: {\n      actionLink: 'Yardım al',\n      actionText: 'Bunlardan hiçbiri yok mu?',\n      blockButton__backupCode: 'Yedekleme kodu kullan',\n      blockButton__emailCode: '{{identifier}} adresine doğrulama kodu gönder',\n      blockButton__emailLink: '{{identifier}} adresine doğrulama bağlantısı gönder',\n      blockButton__passkey: 'Geçiş anahtarınızla oturum açın',\n      blockButton__password: 'Şifreyle giriş yap',\n      blockButton__phoneCode: '{{identifier}} numarasına doğrulama kodu gönder',\n      blockButton__totp: 'Authenticator uygulaması kullan',\n      getHelp: {\n        blockButton__emailSupport: 'E-posta desteği',\n        content:\n          'Eğer hesabınıza giriş yapmakta zorluk yaşıyorsanız, hesabınıza erişiminizi sağlayabilmemiz için bize bir e-posta gönderin ve size yardımcı olalım.',\n        title: 'Yardım al',\n      },\n      subtitle: 'Sorunlarla mı karşılaşıyorsunuz? Oturum açmak için bu yöntemlerden birini deneyebilirsiniz.',\n      title: 'Farklı bir yöntem kullan',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: '{{applicationName}} ile devam etmek için',\n      title: 'Yedekleme kodu girişi',\n    },\n    emailCode: {\n      formTitle: 'Doğrulama kodu',\n      resendButton: 'Kodu tekrar gönder',\n      subtitle: '{{applicationName}} ile devam etmek için',\n      title: 'E-posta kutunuzu kontrol edin',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle:\n          'Devam etmek için, oturum açma işlemini başlattığınız cihaz ve tarayıcıdaki doğrulama bağlantısını açın',\n        title: 'Doğrulama bağlantısı bu cihaz için geçersiz',\n      },\n      expired: {\n        subtitle: 'Devam etmek için en baştaki sekmeye dönün',\n        title: 'Bu doğrulama bağlantısının süresi dolmuş',\n      },\n      failed: {\n        subtitle: 'Devam etmek için en baştaki sekmeye dönün',\n        title: 'Bu doğrulama bağlantısı geçersiz',\n      },\n      formSubtitle: 'E-posta adresinize gönderdiğimiz doğrulama bağlantısına tıklayın',\n      formTitle: 'Doğrulama bağlantısı',\n      loading: {\n        subtitle: 'Birazdan yeniden yönlendirileceksiniz',\n        title: 'Giriş yapılıyor...',\n      },\n      resendButton: 'Tekrar gönder',\n      subtitle: '{{applicationName}} ile devam etmek için',\n      title: 'E-posta kutunuzu kontrol edin',\n      unusedTab: {\n        title: 'Bu sekmeyi kapatabilirsiniz',\n      },\n      verified: {\n        subtitle: 'Birazdan yeniden yönlendirileceksiniz',\n        title: 'Başarıyla giriş yapıldı',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Devam etmek için en baştaki sekmeye dönün',\n        subtitleNewTab: 'Devam etmek için yeni açılmış sekmeye dönün',\n        titleNewTab: 'Farklı bir sekmede giriş yapıldı',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Şifre sıfırlama kodu',\n      resendButton: 'Tekrar gönder',\n      subtitle: 'Şifrenizi sıfırlamak için',\n      subtitle_email: 'İlk olarak, e-posta kimliğinize gönderilen kodu girin',\n      subtitle_phone: 'İlk olarak, telefonunuza gönderilen kodu girin',\n      title: 'Şifreyi sıfırla',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Şifremi sıfırla',\n      label__alternativeMethods: 'Veya başka bir yöntem kullanın:',\n      title: 'Şifremi unuttum',\n    },\n    noAvailableMethods: {\n      message: 'Hesabınızda giriş yapmak için kullanabileceğiniz bir yöntem bulunmuyor.',\n      subtitle: 'Bir hata oluştu',\n      title: 'Giriş yapılamıyor',\n    },\n    passkey: {\n      subtitle:\n        'Geçiş anahtarınızı kullanarak siz olduğunuzu onaylayın. Cihazınız parmak izinizi, yüzünüzü veya ekran kilidinizi isteyebilir.',\n      title: 'Geçiş anahtarınızı kullanın',\n    },\n    password: {\n      actionLink: 'Başka bir yöntem kullan',\n      subtitle: '{{applicationName}} ile devam etmek için',\n      title: 'Şifrenizi girin',\n    },\n    passwordPwned: {\n      title: 'Şifre ele geçirildi',\n    },\n    phoneCode: {\n      formTitle: 'Doğrulama kodu',\n      resendButton: 'Kod almadınız mı? Tekrar gönderin',\n      subtitle: '{{applicationName}} ile devam etmek için',\n      title: 'Telefonuza gönderilen kodu girin',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Doğrulama kodu',\n      resendButton: 'Kod almadınız mı? Tekrar gönderin',\n      subtitle: 'Devam etmek için lütfen telefonunuza gönderilen doğrulama kodunu girin',\n      title: 'Telefonuza gönderilen kodu girin',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Şifremi sıfırla',\n      requiredMessage: 'Güvenlik nedeniyle şifrenizi sıfırlamanız gerekmektedir.',\n      successMessage: 'Şifreniz başarıyla değiştirildi. Oturumunuz açılıyor, lütfen biraz bekleyin.',\n      title: 'Şifre sıfırlama',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Şifrenizi sıfırlamadan önce kimliğinizi doğrulamamız gerekiyor.',\n    },\n    start: {\n      actionLink: 'Kayıt ol',\n      actionLink__join_waitlist: 'Bekleme listesine katıl',\n      actionLink__use_email: 'E-posta kullan',\n      actionLink__use_email_username: 'E-posta veya kullanıcı adı kullan',\n      actionLink__use_passkey: 'Bunun yerine geçiş anahtarını kullanın',\n      actionLink__use_phone: 'Telefon kullan',\n      actionLink__use_username: 'Kullanıcı adı kullan',\n      actionText: 'Hesabınız yok mu?',\n      actionText__join_waitlist: 'Bekleme listesine katılın',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: '{{applicationName}} ile devam etmek için',\n      subtitleCombined: undefined,\n      title: 'Giriş yap',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Doğrulama kodu',\n      subtitle: 'Devam etmek için lütfen kimlik doğrulayıcı uygulamanız tarafından oluşturulan doğrulama kodunu girin',\n      title: 'İki aşamalı doğrulama',\n    },\n  },\n  signInEnterPasswordTitle: 'Şifrenizi girin',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Giriş yap',\n      actionText: 'Hesabınız var mı?',\n      subtitle: '{{applicationName}} ile devam etmek için',\n      title: 'Eksik bilgileri tamamlayın',\n    },\n    emailCode: {\n      formSubtitle: 'E-posta adresinize gönderdiğimiz doğrulama kodunu giriniz',\n      formTitle: 'Doğrulama kodu',\n      resendButton: 'Kodu tekrar gönder',\n      subtitle: '{{applicationName}} ile devam etmek için',\n      title: 'E-posta adresinizi doğrulayın',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: 'Devam etmek için, kayıt işlemini başlattığınız cihazda ve tarayıcıda doğrulama bağlantısını açın',\n        title: 'Doğrulama bağlantısı bu cihaz için geçersiz',\n      },\n      formSubtitle: 'E-posta adresinize gönderdiğimiz doğrulama bağlantısına tıklayın',\n      formTitle: 'Doğrulama bağlantısı',\n      loading: {\n        title: 'Giriş yapılıyor...',\n      },\n      resendButton: 'Bağlantı almadınız mı? Tekrar gönder',\n      subtitle: '{{applicationName}} ile devam etmek için',\n      title: 'E-posta adresinizi doğrulayın',\n      verified: {\n        title: 'Başarıyla doğrulandı',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Devam etmek için yeni açılmış sekmeye dönün',\n        subtitleNewTab: 'Devam etmek için önceki sekmeye dönün',\n        title: 'E-posta adresiniz başarıyla doğrulandı',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: \"Gizlilik Politikası'nı kabul ediyorum\",\n        label__onlyTermsOfService: \"Hizmet Şartları'nı kabul ediyorum\",\n        label__termsOfServiceAndPrivacyPolicy: \"Hizmet Şartları ve Gizlilik Politikası'nı kabul ediyorum\",\n      },\n      continue: {\n        subtitle: 'Devam etmek için lütfen gerekli adımları takip edin.',\n        title: 'Devam Et',\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Telefon numaranıza gönderdiğimiz doğrulama kodunu giriniz',\n      formTitle: 'Doğrulama kodu',\n      resendButton: 'Kodu tekrar gönder',\n      subtitle: '{{applicationName}} ile devam etmek için',\n      title: 'Telefon numaranızı doğrulayın',\n    },\n    restrictedAccess: {\n      actionLink: 'Yardım almak için destekle iletişime geçin',\n      actionText: 'Erişiminiz kısıtlandı mı?',\n      blockButton__emailSupport: 'E-posta ile destek al',\n      blockButton__joinWaitlist: 'Bekleme listesine katıl',\n      subtitle: 'Erişim kısıtlaması nedeniyle bu sayfaya erişim sağlanamıyor.',\n      subtitleWaitlist: 'Erişim için bekleme listesine kaydınız alındı.',\n      title: 'Kısıtlı Erişim',\n    },\n    start: {\n      actionLink: 'Giriş yap',\n      actionLink__use_email: 'Bunun yerine e-posta kullanın',\n      actionLink__use_phone: 'Bunun yerine telefon kullanın',\n      actionText: 'Hesabınız var mı?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: '{{applicationName}} ile devam etmek için',\n      subtitleCombined: '{{applicationName}} ile devam etmek için',\n      title: 'Hesap oluştur',\n      titleCombined: 'Hesap oluştur',\n    },\n  },\n  socialButtonsBlockButton: '{{provider|titleize}} ile giriş yapın',\n  socialButtonsBlockButtonManyInView: '{{provider|titleize}}',\n  unstable__errors: {\n    already_a_member_in_organization: 'Bu organizasyonda zaten üyesiniz.',\n    captcha_invalid:\n      'Güvenlik doğrulamalarındaki hatalar nedeniyle kayıt yapılamadı. Lütfen tekrar denemek için sayfayı yenileyin veya daha fazla yardım için destek ekibi ile iletişime geçin.',\n    captcha_unavailable:\n      'Bot doğrulaması başarısız olduğu için kayıt yapılamadı. Lütfen tekrar denemek için sayfayı yenileyin veya daha fazla yardım için destek ekibi ile iletişime geçin.',\n    form_code_incorrect: 'Hatalı kod.',\n    form_identifier_exists__email_address: 'Bu e-posta adresi zaten kullanılıyor.',\n    form_identifier_exists__phone_number: 'Bu telefon numarası zaten kullanılıyor.',\n    form_identifier_exists__username: 'Bu kullanıcı adı zaten kullanılıyor.',\n    form_identifier_not_found: 'Bu bilgilere sahip bir hesap bulunamadı.',\n    form_param_format_invalid: 'Parametre formatı geçersiz.',\n    form_param_format_invalid__email_address: 'E-posta adresi geçerli olmalıdır.',\n    form_param_format_invalid__phone_number: 'Telefon numarası geçerli olmalıdır.',\n    form_param_max_length_exceeded__first_name: 'Ad 256 karakteri aşmamalıdır.',\n    form_param_max_length_exceeded__last_name: 'Soyad 256 karakteri aşmamalıdır.',\n    form_param_max_length_exceeded__name: 'İsim 256 karakteri aşmamalıdır.',\n    form_param_nil: 'Parametre boş olamaz.',\n    form_param_value_invalid: 'Parametre değeri geçersiz.',\n    form_password_incorrect: 'Şifre yanlış.',\n    form_password_length_too_short: 'Şifre çok kısa.',\n    form_password_not_strong_enough: 'Şifreniz yeterince güçlü değil.',\n    form_password_pwned: 'Bu şifre bir veri ihlalinde tespit edildi ve kullanılamaz. Lütfen başka bir şifre deneyin.',\n    form_password_pwned__sign_in:\n      'Bu şifre bir veri ihlalinde tespit edildi ve oturum açmak için kullanılamaz. Lütfen başka bir şifre seçin.',\n    form_password_size_in_bytes_exceeded:\n      'Şifreniz izin verilen maksimum byte sayısını aştı, lütfen kısaltın veya bazı özel karakterleri çıkarın.',\n    form_password_validation_failed: 'Şifre doğrulaması başarısız.',\n    form_username_invalid_character: 'Kullanıcı adı geçersiz karakterler içeriyor.',\n    form_username_invalid_length: 'Kullanıcı adı 3 ile 50 karakter arasında olmalıdır.',\n    identification_deletion_failed: 'Son kimliğinizi silemezsiniz.',\n    not_allowed_access:\n      \"E-posta adresiniz veya telefon numaranız kayıt için izin verilmiyor. Bu, e-posta adresinizde '+', '=', '#' veya '.' kullanmanız, geçici e-posta hizmetiyle ilişkilendirilmiş bir alan adı kullanmanız veya açık bir engellemeyle ilgili olabilir.\",\n    organization_domain_blocked: 'Organizasyon alan adı engellendi.',\n    organization_domain_common: 'Organizasyon alan adı çok yaygın.',\n    organization_domain_exists_for_enterprise_connection: 'Kurumsal bağlantı için bu alan adı zaten mevcut.',\n    organization_membership_quota_exceeded: 'Organizasyon üye kotası aşıldı.',\n    organization_minimum_permissions_needed:\n      'Bu işlemi gerçekleştirmek için gerekli asgari izinlere sahip olmalısınız.',\n    passkey_already_exists: 'Bu hesaba zaten bir geçiş anahtarı bağlı.',\n    passkey_not_supported: 'Geçiş anahtarları şu anda desteklenmiyor.',\n    passkey_pa_not_supported: 'Bu platform için geçiş anahtarları desteklenmiyor.',\n    passkey_registration_cancelled: 'Geçiş anahtarı kaydı iptal edildi.',\n    passkey_retrieval_cancelled: 'Geçiş anahtarını geri yükleme işlemi iptal edildi.',\n    passwordComplexity: {\n      maximumLength: 'En fazla {{length}} karakter olmalı',\n      minimumLength: 'En az {{length}} karakter olmalı',\n      requireLowercase: 'bir küçük harf içermeli',\n      requireNumbers: 'bir sayı içermeli',\n      requireSpecialCharacter: 'bir özel karakter içermeli',\n      requireUppercase: 'bir büyük harf içermeli',\n      sentencePrefix: 'Şifreniz;',\n    },\n    phone_number_exists: 'Bu telefon numarası zaten kullanılıyor. Lütfen başka bir numara deneyin.',\n    session_exists: 'Zaten giriş yapmışsınız.',\n    web3_missing_identifier: 'Web3 için tanımlayıcı eksik.',\n    zxcvbn: {\n      couldBeStronger:\n        'Şifreniz kriterleri karşılıyor; fakat birkaç karakter daha ekleyerek daha güçlü bir şifre oluşturabilirsiniz.',\n      goodPassword: 'Harika! Parolanız gerekli tüm gereksinimleri karşılıyor.',\n      notEnough: 'Şifreniz yeterince güçlü değil.',\n      suggestions: {\n        allUppercase: 'Tüm harfleri büyük yazmak yerine rastgele büyük harfler kullanın.',\n        anotherWord: 'Şifreniz daha az bilinen kelimeler içermeli.',\n        associatedYears: 'Kendinizle alakası olan yılları kullanmayın.',\n        capitalization: 'Sadece ilk harfi büyük yazmak yerine rastgele büyük harfler kullanın.',\n        dates: 'Kendinizle alakası olan tarihleri kullanmayın.',\n        l33t: '\"a\" yerine \"@\" kullanmak gibi tahmin edilebilir harf ikamelerinden kaçının.',\n        longerKeyboardPattern: 'Daha uzun ve karmaşık klavye desenleri kullanın.',\n        noNeed: 'Özel karakter, sayı veya büyük harf kullanmadan da güçlü bir şifre oluşturabilirsiniz.',\n        pwned: 'Bu şifre bir veri ihlalinde tespit edildi. Başka bir yerde kullanıyorsanız, değiştirmeniz önerilir.',\n        recentYears: 'Günümüze yakın yılları kullanmayın.',\n        repeated: 'Kelime veya karakter tekrarlarını azaltın.',\n        reverseWords: 'Yaygın kelimelerin tersini kullanmaktan kaçının.',\n        sequences: 'Yaygın desenleri kullanmayın.',\n        useWords: 'Birden fazla sözcük kullanın ama yaygın deyişlerden kaçının.',\n      },\n      warnings: {\n        common: 'Bu yaygın bir şifre.',\n        commonNames: 'Yaygın adlar ve soyadlar kolay tahmin edilir.',\n        dates: 'Tarihleri tahmin etmek kolaydır.',\n        extendedRepeat: '\"abcabcabc\" gibi tekrarlanan desenler kolay tahmin edilir.',\n        keyPattern: 'Kısa klavye desenleri kolay tahmin edilir.',\n        namesByThemselves: 'Adlar ya da soyadlar kolay tahmin edilir.',\n        pwned: 'Bu şifre bir veri ihlalinde tespit edilmiş.',\n        recentYears: 'Yakın yıllar kolay tahmin edilir.',\n        sequences: '\"abc\" gibi yaygın desenlerin tahmini kolaydır.',\n        similarToCommon: 'Bu yaygın kullanılan şifrelere çok benziyor.',\n        simpleRepeat: 'Tekrarlanan karakterler kolay tahmin edilir.',\n        straightRow: 'Klavyede ardışık karakterler kolay tahmin edilir.',\n        topHundred: 'Bu çok yaygın bir şifre.',\n        topTen: 'Bu epey yaygın bir şifre.',\n        userInputs: 'Şifreniz kişisel bilgiler içermemeli.',\n        wordByItself: 'Tek sözcükler kolay tahmin edilir.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Hesap ekle',\n    action__manageAccount: 'Hesabı yönet',\n    action__signOut: 'Çıkış yap',\n    action__signOutAll: 'Tüm hesaplardan çıkış yap',\n  },\n  userProfile: {\n    backupCodePage: {\n      actionLabel__copied: 'Kopyalandı!',\n      actionLabel__copy: 'Hepsini kopyala',\n      actionLabel__download: '.txt olarak indir',\n      actionLabel__print: 'Yazdır',\n      infoText1: 'Yedekleme kodları bu hesap için etkinleştirilecektir.',\n      infoText2:\n        'Yedekleme kodlarınızı güvenli bir yerde saklayın. Eğer bu kodlarınızın başkasının eline geçtiğini düşünürseniz, yenilerini oluşturabilirsiniz.',\n      subtitle__codelist: 'Yedekleme kodlarınızı güvenli bir yerde saklayın.',\n      successMessage:\n        'Yedekleme kodları başarıyla eklendi. Eğer Authenticator uygulamanızın olduğu cihaza erişiminizi kaybettiyseniz, oturum açarken bu kodlardan birini girebilirsiniz. Her kod en fazla bir kez kullanılabilir.',\n      successSubtitle:\n        'Eğer Authenticator uygulamanızın olduğu cihazınıza erişiminizi kaybederseniz, bu kodlardan birini kullanarak hesabınıza giriş yapabilirsiniz.',\n      title: 'Yedekleme kodu doğrulaması ekle',\n      title__codelist: 'Yedekleme kodları',\n    },\n    billingPage: {\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Yeni bir hesap bağlamak için bir sağlayıcı seçiniz.',\n      formHint__noAccounts: 'Kullanılabilir bir sağlayıcı yok.',\n      removeResource: {\n        messageLine1: '{{identifier}} hesabınızdan kaldırılacaktır.',\n        messageLine2:\n          'Artık bu bağlı hesabı kullanarak oturum açmanız mümkün olmayacaktır ve buna bağlı özellikler çalışmayacaktır.',\n        successMessage: '{{connectedAccount}} hesabınızdan kaldırıldı.',\n        title: 'Bağlı hesabı kaldır',\n      },\n      socialButtonsBlockButton: '{{provider|titleize}} hesabı bağla',\n      successMessage: 'Sağlayıcı hesabınıza bağlandı.',\n      title: 'Hesap bağla',\n    },\n    deletePage: {\n      actionDescription: 'Devam etmek için aşağıya “Hesabı sil” yazın.',\n      confirm: 'Hesabı sil',\n      messageLine1: 'Hesabınızı silmek istediğinizden emin misiniz?',\n      messageLine2: 'Bu işlem kalıcıdır ve geri alınamaz.',\n      title: 'Hesabı sil',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'Doğrulama kodunu içeren bir e-posta belirttiğiniz adrese gönderilecektir.',\n        formSubtitle: '{{identifier}} adresine gönderilen doğrulama kodunu giriniz',\n        formTitle: 'Doğrulama kodu',\n        resendButton: 'Yeniden gönder',\n        successMessage: '{{identifier}} adresi hesabınıza eklendi.',\n      },\n      emailLink: {\n        formHint: 'Doğrulama bağlantısını içeren bir e-posta belirttiğiniz adrese gönderilecektir.',\n        formSubtitle: '{{identifier}} adresine gönderilen doğrulama bağlantısını tıklayınız',\n        formTitle: 'Doğrulama bağlantısı',\n        resendButton: 'Yeniden gönder',\n        successMessage: '{{identifier}} adresi hesabınıza eklendi.',\n      },\n      enterpriseSSOLink: {\n        formButton: 'Bağlan',\n        formSubtitle: 'Kurumsal SSO kullanın',\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} adresi hesabınızdan kaldırılacaktır.',\n        messageLine2: 'Artık bu e-posta adresini kullanarak oturum açmanız mümkün olmayacaktır.',\n        successMessage: '{{emailAddress}} adresi hesabınızdan kaldırıldı.',\n        title: 'E-posta adresini kaldır',\n      },\n      title: 'E-posta adresi ekle',\n      verifyTitle: 'E-posta adresini doğrulayın',\n    },\n    formButtonPrimary__add: 'Ekle',\n    formButtonPrimary__continue: 'İlerle',\n    formButtonPrimary__finish: 'Bitir',\n    formButtonPrimary__remove: 'Kaldır',\n    formButtonPrimary__save: 'Kaydet',\n    formButtonReset: 'İptal',\n    mfaPage: {\n      formHint: 'Eklemek için bir yöntem seçiniz.',\n      title: 'İki aşamalı doğrulama yöntemi ekle',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Mevcut numarayı kullan',\n      primaryButton__addPhoneNumber: 'Telefon numarası ekle',\n      removeResource: {\n        messageLine1: 'Giriş yaparken artık {{identifier}} numarasına SMS kodu gönderilmeyecektir.',\n        messageLine2: 'Hesabınızın güvenliği azalabilir. Devam etmek istediğinizden emin misiniz?',\n        successMessage: 'İki aşamalı SMS kodu doğrulaması {{mfaPhoneCode}} numarasından kaldırıldı.',\n        title: 'İki aşamalı doğrulamayı kaldır',\n      },\n      subtitle__availablePhoneNumbers: 'İki aşamalı SMS kodu doğrulaması için bir telefon numarası seçin.',\n      subtitle__unavailablePhoneNumbers:\n        'İki aşamalı SMS kodu doğrulaması için kullanılabilir bir telefon numarası yok.',\n      successMessage1:\n        'Oturum açarken, ek bir adım olarak bu telefon numarasına gönderilen bir doğrulama kodunu girmeniz gerekecektir.',\n      successMessage2:\n        'Bu yedek kodları kaydedin ve güvenli bir yerde saklayın. Kimlik doğrulama cihazınıza erişiminizi kaybederseniz oturum açmak için yedek kodları kullanabilirsiniz.',\n      successTitle: 'SMS kodu doğrulaması etkin',\n      title: 'SMS kodu doğrulaması ekle',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Veya QR kodunu tara',\n        buttonUnableToScan__nonPrimary: 'QR kodunu tarayamıyorum',\n        infoText__ableToScan:\n          'Authenticator uygulamanızda yeni bir giriş yöntemi ayarlayın ve hesabınızla bağlamak için aşağıdaki QR kodunu tarayın.',\n        infoText__unableToScan:\n          'Authenticator uygulamanızda yeni bir giriş yöntemi ekleme seçeneğini bulun ve aşağıda verilen değeri girin:',\n        inputLabel__unableToScan1:\n          'Zaman bazlı veya tek seferlik şifrelerin etkinleştirildiğinden emin olun, ardından hesabınızı bağlamayı tamamlayın.',\n        inputLabel__unableToScan2:\n          'Alternatif olarak doğrulayıcınız TOTP URI’leri destekliyorsa, tam URI’yi de kopyalayabilirsiniz.',\n      },\n      removeResource: {\n        messageLine1: \"Artık giriş yaparken authenticator'dan gelecek doğrulama kodları gerekmeyecektir.\",\n        messageLine2: 'Hesabınızın güvenliği azalabilir. Devam etmek istediğinizden emin misiniz?',\n        successMessage: 'İki aşamalı doğrulama yöntemi kaldırıldı.',\n        title: 'İki aşamalı doğrulamayı kaldır',\n      },\n      successMessage:\n        'İki aşamalı doğrulama yöntemi başarıyla eklendi. Oturum açarken, ek bir adım olarak bu doğrulayıcıdan bir doğrulama kodu girmeniz gerekecektir.',\n      title: 'Authenticator uygulaması ekle',\n      verifySubtitle: 'Authenticator uygulamanızda oluşturulan doğrulama kodunu giriniz',\n      verifyTitle: 'Doğrulama kodu',\n    },\n    mobileButton__menu: 'Menü',\n    navbar: {\n      account: 'Profil',\n      billing: undefined,\n      description: 'Hesap bilgilerinizi yönetin.',\n      security: 'Güvenlik',\n      title: 'Hesap',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: '{{name}} bu hesaptan kaldırılacaktır.',\n        title: 'Geçiş anahtarını kaldır',\n      },\n      subtitle__rename: 'Bulmayı kolaylaştırmak için geçiş anahtarı adını değiştirebilirsiniz.',\n      title__rename: 'Geçiş Anahtarını Yeniden Adlandır',\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'Eski şifrenizi kullanmış olabilecek diğer tüm cihazlardan çıkış yapmanız önerilir.',\n      readonly: 'Profil bilgileriniz kurumsal bağlantı üzerinden sağlandığı için düzenlenemez.',\n      successMessage__set: 'Şifreniz başarıyla değiştirildi.',\n      successMessage__signOutOfOtherSessions: 'Diğer tüm cihazlardaki oturumlarınız sonlandırıldı.',\n      successMessage__update: 'Şifreniz güncellendi.',\n      title__set: 'Şifreyi değiştir',\n      title__update: 'Yeni şifre girin',\n    },\n    phoneNumberPage: {\n      infoText: 'Belirtilen numaraya doğrulama kodunu içeren bir SMS gönderilecektir.',\n      removeResource: {\n        messageLine1: '{{identifier}} numarası hesabınızdan kaldırılacaktır.',\n        messageLine2: 'Artık bu telefon numarasını kullanarak oturum açmanız mümkün olmayacaktır.',\n        successMessage: '{{phoneNumber}} numarası hesabınızdan kaldırıldı.',\n        title: 'Telefon numarasını kaldır',\n      },\n      successMessage: '{{identifier}} numarası hesabınıza eklendi.',\n      title: 'Telefon numarası ekle',\n      verifySubtitle: '{{identifier}} numarasına gönderilen doğrulama kodunu girin',\n      verifyTitle: 'Telefon numarasını doğrulayın',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: \"10 MB'tan küçük boyutta bir JPG, PNG, GIF, veya WEBP dosyası yükle\",\n      imageFormDestructiveActionSubtitle: 'Görseli kaldır',\n      imageFormSubtitle: 'Görsel yükle',\n      imageFormTitle: 'Profil görseli',\n      readonly: 'Profil bilgileriniz kurumsal bağlantı üzerinden sağlandığı için düzenlenemez.',\n      successMessage: 'Profiliniz güncellendi.',\n      title: 'Profili güncelle',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Cihaz oturumunu sonlandır',\n        title: 'Aktif cihazlar',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Yeniden dene',\n        actionLabel__reauthorize: 'Yetkilendir',\n        destructiveActionTitle: 'Kaldır',\n        primaryButton: 'Hesap bağla',\n        subtitle__disconnected: 'Bu hesabın bağlantısı kesildi.',\n        subtitle__reauthorize:\n          'Gerekli kapsamlar güncellendi ve sınırlı işlevsellik yaşayabilirsiniz. Sorunları önlemek için lütfen bu uygulamayı yeniden yetkilendirin',\n        title: 'Bağlı hesaplar',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Hesabı sil',\n        title: 'Tehlike',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'E-posta adresini kaldır',\n        detailsAction__nonPrimary: 'Birincil e-posta adresi yap',\n        detailsAction__primary: 'Doğrulamayı tamamla',\n        detailsAction__unverified: 'Doğrulamayı tamamla',\n        primaryButton: 'E-posta adresi ekle',\n        title: 'E-posta adresleri',\n      },\n      enterpriseAccountsSection: {\n        title: 'Kurumsal hesaplar',\n      },\n      headerTitle__account: 'Hesap',\n      headerTitle__security: 'Güvenlik',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Kodları yenile',\n          headerTitle: 'Yedekleme kodları',\n          subtitle__regenerate:\n            'Yeni bir dizi güvenli yedekleme kodu alın. Önceki yedekleme kodları silinecek ve kullanılamayacaktır.',\n          title__regenerate: 'Yedekleme kodlarını yenile',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Varsayılan olarak ayarla',\n          destructiveActionLabel: 'Telefon numarasını kaldır',\n        },\n        primaryButton: 'İki aşamalı doğrulama ekle',\n        title: 'İki aşamalı doğrulama',\n        totp: {\n          destructiveActionTitle: 'Kaldır',\n          headerTitle: 'Authenticator uygulaması',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: 'Kaldır',\n        menuAction__rename: 'Yeniden Adlandır',\n        primaryButton: undefined,\n        title: 'Geçiş Anahtarları',\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Şifreyi güncelle',\n        primaryButton__updatePassword: 'Şifreyi değiştir',\n        title: 'Şifre',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Telefon numarasını kaldır',\n        detailsAction__nonPrimary: 'Birincil yap',\n        detailsAction__primary: 'Doğrulamayı tamamla',\n        detailsAction__unverified: 'Doğrulamayı tamamla',\n        primaryButton: 'Telefon numarası ekle',\n        title: 'Telefon numaraları',\n      },\n      profileSection: {\n        primaryButton: 'Profili güncelle',\n        title: 'Profil',\n      },\n      usernameSection: {\n        primaryButton__setUsername: 'Kullanıcı adını ayarla',\n        primaryButton__updateUsername: 'Kullanıcı adını değiştir',\n        title: 'Kullanıcı adı',\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Cüzdanı kaldır',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 cüzdanları',\n        title: 'Web3 cüzdanları',\n      },\n    },\n    usernamePage: {\n      successMessage: 'Kullanıcı adınız güncellendi.',\n      title__set: 'Kullanıcı adını güncelle',\n      title__update: 'Kullanıcı adını güncelle',\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} cüzdanı hesabınızdan kaldırılacaktır.',\n        messageLine2: 'Artık bu cüzdanı kullanarak oturum açmanız mümkün olmayacaktır.',\n        successMessage: '{{web3Wallet}} cüzdanı hesabınızdan kaldırıldı.',\n        title: 'Web3 cüzdanını kaldır',\n      },\n      subtitle__availableWallets: 'Hesabınıza eklemek için bir web3 cüzdanı seçiniz.',\n      subtitle__unavailableWallets: 'Kullanılabilir bir web3 cüzdanı yok.',\n      successMessage: 'Web3 cüzdanınız hesabınıza eklendi.',\n      title: 'Web3 cüzdanı ekle',\n      web3WalletButtonsBlockButton: 'Web3 cüzdanınızı bağlamak için tıklayın',\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: 'Bekleme listesine katıl',\n      actionText: 'Hala bir hesabınız yok mu?',\n      formButton: 'Kayıt Ol',\n      subtitle: 'Kaydolduktan sonra erken erişim kazanabilirsiniz.',\n      title: 'Bekleme Listesine Katılın',\n    },\n    success: {\n      message: 'Başarıyla bekleme listesine eklendiniz. Erken erişim için sizi bilgilendireceğiz.',\n      subtitle: 'Bekleme listesinde olduğunuz için teşekkür ederiz.',\n      title: 'Bekleme Listesine Katıldınız',\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BACE;AAAA,MACF,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BACE;AAAA,IACF,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCACE;AAAA,IACF,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBACE;AAAA,MACF,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCAAiC;AAAA,MACjC,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}