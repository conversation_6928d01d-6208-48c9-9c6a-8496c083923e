import type { OrganizationResource, OrganizationSuggestionResource } from '@clerk/types';
export declare const SuggestionPreview: (props: OrganizationSuggestionResource) => import("@emotion/react/jsx-runtime").JSX.Element;
type UserInvitationSuggestionListProps = {
    onOrganizationClick: (org: OrganizationResource) => unknown;
};
export declare const UserInvitationSuggestionList: (props: UserInvitationSuggestionListProps) => import("@emotion/react/jsx-runtime").JSX.Element | null;
export {};
