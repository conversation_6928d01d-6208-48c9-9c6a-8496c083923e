import * as Primitives from '../primitives';
export * from './Flow';
export { AppearanceProvider, useAppearance } from './AppearanceContext';
export { descriptors } from './elementDescriptors';
export { localizationKeys, useLocalizations } from '../localization';
export type { LocalizationKey } from '../localization';
export { generateFlowPartClassname } from './classGeneration';
export declare const Box: import("react").FunctionComponent<Omit<Primitives.BoxProps, "ref"> & import("react").RefAttributes<HTMLDivElement> & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Flex: import("react").FunctionComponent<Omit<Primitives.FlexProps, "ref"> & import("react").RefAttributes<HTMLDivElement> & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Col: import("react").FunctionComponent<Omit<Primitives.FlexProps, "ref"> & import("react").RefAttributes<HTMLDivElement> & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Grid: import("react").FunctionComponent<Omit<Primitives.GridProps, "ref"> & import("react").RefAttributes<HTMLDivElement> & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Button: import("react").FunctionComponent<Omit<import("react").ClassAttributes<HTMLButtonElement> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    css?: import("../styledSystem").ThemableCssProp;
} & {
    isLoading?: boolean;
    loadingText?: string;
    isDisabled?: boolean;
    isActive?: boolean;
    hoverAsFocus?: boolean;
    hasArrow?: boolean;
} & {
    textVariant?: "body" | "caption" | "h1" | "h2" | "h3" | "subtitle" | "buttonLarge" | "buttonSmall" | undefined;
    size?: "sm" | "md" | "xs" | "iconLg" | undefined;
    colorScheme?: "primary" | "secondary" | "danger" | "neutral" | undefined;
    variant?: "link" | "solid" | "outline" | "bordered" | "ghost" | "linkDanger" | "unstyled" | "roundWrapper" | undefined;
    block?: boolean | undefined;
    focusRing?: boolean | undefined;
}, "ref"> & import("react").RefAttributes<HTMLButtonElement> & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const SimpleButton: import("react").FunctionComponent<Omit<import("react").ClassAttributes<HTMLButtonElement> & import("react").ButtonHTMLAttributes<HTMLButtonElement> & {
    css?: import("../styledSystem").ThemableCssProp;
} & {
    isLoading?: boolean;
    loadingText?: string;
    isDisabled?: boolean;
    isActive?: boolean;
    hoverAsFocus?: boolean;
    hasArrow?: boolean;
} & {
    textVariant?: "body" | "caption" | "h1" | "h2" | "h3" | "subtitle" | "buttonLarge" | "buttonSmall" | undefined;
    size?: "sm" | "md" | "xs" | "iconLg" | undefined;
    colorScheme?: "primary" | "secondary" | "danger" | "neutral" | undefined;
    variant?: "link" | "solid" | "outline" | "bordered" | "ghost" | "linkDanger" | "unstyled" | "roundWrapper" | undefined;
    block?: boolean | undefined;
    focusRing?: boolean | undefined;
}, "ref"> & import("react").RefAttributes<HTMLButtonElement> & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Heading: import("react").FunctionComponent<import("react").ClassAttributes<HTMLDivElement> & import("react").HTMLAttributes<HTMLDivElement> & {
    css?: import("../styledSystem").ThemableCssProp;
} & {
    textVariant?: "body" | "caption" | "h1" | "h2" | "h3" | "subtitle" | "buttonLarge" | "buttonSmall" | undefined;
} & {
    as?: "h1" | "h2" | "h3";
} & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Link: import("react").FunctionComponent<import("react").ClassAttributes<HTMLAnchorElement> & import("react").AnchorHTMLAttributes<HTMLAnchorElement> & {
    css?: import("../styledSystem").ThemableCssProp;
} & {
    isExternal?: boolean;
    isDisabled?: boolean;
} & {
    variant?: "body" | "caption" | "h1" | "h2" | "h3" | "subtitle" | "buttonLarge" | "buttonSmall" | undefined;
    colorScheme?: "primary" | "inherit" | "danger" | "neutral" | undefined;
} & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Text: import("react").FunctionComponent<Omit<Primitives.TextProps, "ref"> & import("react").RefAttributes<HTMLElement> & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Image: import("react").FunctionComponent<Omit<Primitives.ImageProps, "ref"> & import("react").RefAttributes<HTMLImageElement> & {
    size?: number;
    xDescriptors?: number[];
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Alert: import("react").FunctionComponent<Partial<Record<"isDisabled" | "hasError" | "isLoading" | "isOpen" | "isActive", any>> & import("react").ClassAttributes<HTMLDivElement> & import("react").HTMLAttributes<HTMLDivElement> & {
    css?: import("../styledSystem").ThemableCssProp;
} & import("../styledSystem").AsProp & {} & {
    direction?: "col" | "row" | "rowReverse" | "columnReverse" | undefined;
    align?: "center" | "end" | "start" | "baseline" | "stretch" | undefined;
    justify?: "center" | "end" | "start" | "between" | undefined;
    wrap?: "wrap" | "noWrap" | "wrapReverse" | undefined;
    gap?: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | undefined;
    center?: boolean | undefined;
} & {
    colorScheme?: "danger" | "warning" | "info" | undefined;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const AlertIcon: import("react").FunctionComponent<{
    variant: "danger" | "warning" | "info";
} & {
    colorScheme?: "primary" | "danger" | "warning" | "info" | "success" | undefined;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Input: import("react").FunctionComponent<Omit<Primitives.InputProps, "ref"> & import("react").RefAttributes<HTMLInputElement> & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const CheckboxInput: import("react").FunctionComponent<Omit<Primitives.InputProps, "ref"> & import("react").RefAttributes<HTMLInputElement> & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const RadioInput: import("react").FunctionComponent<Omit<Primitives.InputProps, "ref"> & import("react").RefAttributes<HTMLInputElement> & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const FormLabel: import("react").FunctionComponent<import("react").ClassAttributes<HTMLLabelElement> & import("react").LabelHTMLAttributes<HTMLLabelElement> & {
    css?: import("../styledSystem").ThemableCssProp;
} & {} & Partial<Record<"isDisabled" | "hasError" | "isLoading" | "isOpen" | "isActive", any>> & {
    children?: import("react").ReactNode | undefined;
} & Partial<Record<"isRequired", boolean>> & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const FormErrorText: import("react").FunctionComponent<{} & {
    children?: import("react").ReactNode | undefined;
} & import("react").RefAttributes<HTMLElement> & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const FormSuccessText: import("react").FunctionComponent<{} & {
    children?: import("react").ReactNode | undefined;
} & import("react").RefAttributes<HTMLElement> & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const FormWarningText: import("react").FunctionComponent<{} & {
    children?: import("react").ReactNode | undefined;
} & import("react").RefAttributes<HTMLElement> & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const FormInfoText: import("react").FunctionComponent<{} & {
    children?: import("react").ReactNode | undefined;
} & import("react").RefAttributes<HTMLElement> & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Form: import("react").FunctionComponent<Omit<Primitives.FormProps, "ref"> & import("react").RefAttributes<HTMLFormElement> & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Icon: import("react").FunctionComponent<{
    size?: "sm" | "md" | "lg" | "xs" | undefined;
    colorScheme?: "danger" | "warning" | "success" | "neutral" | undefined;
} & {
    icon: React.ComponentType;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Spinner: import("react").FunctionComponent<import("react").ClassAttributes<HTMLDivElement> & import("react").HTMLAttributes<HTMLDivElement> & {
    css?: import("../styledSystem").ThemableCssProp;
} & {
    colorScheme?: "primary" | "neutral" | undefined;
    thickness?: "sm" | "md" | undefined;
    size?: "sm" | "md" | "lg" | "xl" | "xs" | undefined;
    speed?: "normal" | "slow" | undefined;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Badge: import("react").FunctionComponent<Omit<Primitives.FlexProps, "ref"> & import("react").RefAttributes<HTMLDivElement> & {
    textVariant?: "body" | "caption" | "h1" | "h2" | "h3" | "subtitle" | "buttonLarge" | "buttonSmall" | undefined;
    colorScheme?: "primary" | "secondary" | "danger" | "warning" | "success" | undefined;
} & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const NotificationBadge: import("react").FunctionComponent<Omit<Primitives.FlexProps, "ref"> & import("react").RefAttributes<HTMLDivElement> & {
    textVariant?: "body" | "caption" | "h1" | "h2" | "h3" | "subtitle" | "buttonLarge" | "buttonSmall" | undefined;
    colorScheme?: "primary" | "outline" | undefined;
} & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Table: import("react").FunctionComponent<Omit<Primitives.TableProps, "ref"> & import("react").RefAttributes<HTMLTableCellElement> & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Thead: import("react").FunctionComponent<Omit<Primitives.TheadProps, "ref"> & import("react").RefAttributes<HTMLTableSectionElement> & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Tbody: import("react").FunctionComponent<Omit<Primitives.TbodyProps, "ref"> & import("react").RefAttributes<HTMLTableSectionElement> & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Tr: import("react").FunctionComponent<Omit<Primitives.TrProps, "ref"> & import("react").RefAttributes<HTMLTableCellElement> & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Th: import("react").FunctionComponent<Omit<Primitives.ThProps, "ref"> & import("react").RefAttributes<HTMLTableCellElement> & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Td: import("react").FunctionComponent<Omit<Primitives.TdProps, "ref"> & import("react").RefAttributes<HTMLTableCellElement> & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Dl: import("react").FunctionComponent<Omit<Primitives.DlProps, "ref"> & import("react").RefAttributes<HTMLTableCellElement> & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Dd: import("react").FunctionComponent<Omit<Primitives.DdProps, "ref"> & import("react").RefAttributes<HTMLTableCellElement> & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Dt: import("react").FunctionComponent<Omit<Primitives.DtProps, "ref"> & import("react").RefAttributes<HTMLTableCellElement> & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Span: import("react").FunctionComponent<Omit<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, "ref"> & import("react").RefAttributes<HTMLSpanElement> & {
    localizationKey?: import(".").LocalizationKey | string;
} & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
export declare const Hr: import("react").FunctionComponent<Omit<Primitives.HrProps, "ref"> & import("react").RefAttributes<HTMLHRElement> & {
    elementDescriptor?: import("./elementDescriptors").ElementDescriptor | Array<import("./elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("./elementDescriptors").ElementId;
    css?: never;
    sx?: import("../styledSystem").ThemableCssProp;
}>;
