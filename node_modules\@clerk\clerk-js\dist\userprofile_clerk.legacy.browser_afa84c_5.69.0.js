"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["470"],{99140:function(e,t,i){i.d(t,{_:()=>b}),i(92037),i(28419);var r=i(79109),o=i(83799),l=i(69144),a=i(2672),n=i(70431),s=i(19460),c=i(68487),d=i(8969),u=i(11576),h=i(91085),m=i(77623),p=i(10207);i(50725);var v=i(24676),f=i(26917),g=i(39541),y=i(12464);let P=e=>{let{navigate:t}=(0,v.useRouter)(),{email:i,nextStep:o,onReset:n}=e,c=(0,a.useCardState)(),d=(0,u.useUserProfileContext)(),{startEnterpriseSSOLinkFlow:h}=(0,y.eq)(i);async function p(){var e;await t((null===(e=i.verification.externalVerificationRedirectURL)||void 0===e?void 0:e.href)||"")}return l.useEffect(()=>{!function(){let{mode:e,componentName:t}=d;h({redirectUrl:"modal"===e?(0,f.bX)({url:window.location.href,componentName:t}):window.location.href}).then(()=>o()).catch(e=>(0,m.S3)(e,[],c.setError))}()},[]),(0,r.BX)(r.HY,{children:[(0,r.tZ)(g.Flex,{justify:"center",children:(0,r.tZ)(g.Button,{variant:"link",onClick:p,localizationKey:(0,g.localizationKeys)("userProfile.emailAddressPage.enterpriseSSOLink.formButton")})}),(0,r.tZ)(s.K,{children:(0,r.tZ)(g.Button,{variant:"ghost",localizationKey:(0,g.localizationKeys)("userProfile.formButtonReset"),elementDescriptor:g.descriptors.formButtonReset,onClick:n})})]})};var Z=i(12786);let b=(0,a.withCardStateProvider)(e=>{var t,i;let{emailId:v,onSuccess:f,onReset:g,disableAutoFocus:y=!1}=e,b=(0,a.useCardState)(),{user:C}=(0,o.aF)(),K=(0,u.useEnvironment)(),x=(0,o.WZ)(e=>null==C?void 0:C.createEmailAddress({email:e})),R=l.useRef(null==C?void 0:C.emailAddresses.find(e=>e.id===v)),_=S(R.current,K),B=(0,d.a2)({defaultStep:+!!R.current,onNextStep:()=>b.setError(void 0)}),w=(0,m.Yp)("emailAddress","",{type:"email",label:(0,h.u1)("formFieldLabel__emailAddress"),placeholder:(0,h.u1)("formFieldInputPlaceholder__emailAddress"),isRequired:!0}),k=w.value.length>1&&(null==C?void 0:C.username)!==w.value,I=async e=>{if(e.preventDefault(),C)return x(w.value).then(e=>{R.current=e,B.nextStep()}).catch(e=>(0,m.S3)(e,[w],b.setError))},T=z(_);return(0,r.BX)(d.en,{...B.props,children:[(0,r.tZ)(c.Y,{headerTitle:e.title||(0,h.u1)("userProfile.emailAddressPage.title"),headerSubtitle:e.subtitle||(0,h.u1)("userProfile.emailAddressPage.formHint"),children:(0,r.BX)(n.l.Root,{onSubmit:I,children:[(0,r.tZ)(n.l.ControlRow,{elementId:w.id,children:(0,r.tZ)(n.l.PlainInput,{...w.props,autoFocus:!y})}),(0,r.tZ)(s.A,{submitLabel:(0,h.u1)("userProfile.formButtonPrimary__add"),isDisabled:!k,onReset:g})]})}),(0,r.BX)(c.Y,{headerTitle:(0,h.u1)("userProfile.emailAddressPage.verifyTitle"),headerSubtitle:(0,h.u1)("".concat(T,".formSubtitle"),{identifier:null===(t=R.current)||void 0===t?void 0:t.emailAddress}),children:["email_link"===_&&(0,r.tZ)(Z.K,{nextStep:f,email:R.current,onReset:g}),"email_code"===_&&(0,r.tZ)(p.H,{nextStep:f,identification:R.current,identifier:null===(i=R.current)||void 0===i?void 0:i.emailAddress,prepareVerification:()=>{var e;return null===(e=R.current)||void 0===e?void 0:e.prepareVerification({strategy:"email_code"})},onReset:g}),"enterprise_sso"===_&&(0,r.tZ)(P,{nextStep:f,email:R.current,onReset:g})]})]})}),z=e=>{switch(e){case"email_code":return"userProfile.emailAddressPage.emailCode";case"enterprise_sso":return"userProfile.emailAddressPage.enterpriseSSOLink";case"email_link":return"userProfile.emailAddressPage.emailLink";default:throw Error("Unsupported strategy for email verification: ".concat(e))}},S=(e,t)=>(null==e?void 0:e.matchesSsoConnection)?"enterprise_sso":!function(e){let{userSettings:t}=e,{email_address:i}=t.attributes;return!!((null==i?void 0:i.enabled)&&(null==i?void 0:i.verifications.includes("email_link")))}(t)?"email_code":"email_link"},10207:function(e,t,i){i.d(t,{H:()=>u});var r=i(79109),o=i(69144),l=i(12667),a=i(2672),n=i(70431),s=i(19460),c=i(39541),d=i(77623);let u=e=>{let t=(0,a.useCardState)(),{nextStep:i,identification:u,identifier:h,onReset:m,prepareVerification:p}=e,v=()=>{var e;return null==p?void 0:null===(e=p())||void 0===e?void 0:e.catch(e=>(0,d.S3)(e,[],t.setError))},f=(0,l.e3)({onCodeEntryFinished:(e,t,i)=>{null==u||u.attemptVerification({code:e}).then(()=>t()).catch(i)},onResendCodeClicked:v,onResolve:i});return o.useEffect(()=>{v()},[]),(0,r.BX)(r.HY,{children:[(0,r.tZ)(n.l.OTPInput,{...f,label:(0,c.localizationKeys)("userProfile.emailAddressPage.emailCode.formTitle"),description:(0,c.localizationKeys)("userProfile.emailAddressPage.emailCode.formSubtitle",{identifier:h}),resendButton:(0,c.localizationKeys)("userProfile.emailAddressPage.emailCode.resendButton"),centerAlign:!1}),(0,r.BX)(s.K,{children:[(0,r.tZ)(c.Button,{isLoading:f.isLoading,localizationKey:(0,c.localizationKeys)("formButtonPrimary__verify"),elementDescriptor:c.descriptors.formButtonPrimary,onClick:f.onFakeContinue}),(0,r.tZ)(c.Button,{variant:"ghost",isDisabled:f.isLoading,localizationKey:(0,c.localizationKeys)("userProfile.formButtonReset"),elementDescriptor:c.descriptors.formButtonReset,onClick:m})]})]})}},12786:function(e,t,i){i.d(t,{K:()=>p,M:()=>v});var r=i(79109),o=i(69144),l=i(2672),a=i(19460),n=i(65495),s=i(8969),c=i(56749),d=i(11576),u=i(39541),h=i(12464),m=i(77623);let p=e=>{let{email:t,nextStep:i,onReset:s}=e,p=(0,l.useCardState)(),v=(0,d.useUserProfileContext)(),{startEmailLinkFlow:f}=(0,h.E2)(t),{displayConfig:g}=(0,d.useEnvironment)();function y(){let{routing:e}=v,t="virtual"===e?g.userProfileUrl:"";f({redirectUrl:(0,c.Uu)({ctx:v,baseUrl:t,intent:"profile"})}).then(()=>i()).catch(e=>(0,m.S3)(e,[],p.setError))}return o.useEffect(()=>{y()},[]),(0,r.BX)(r.HY,{children:[(0,r.tZ)(n.V,{resendButton:(0,u.localizationKeys)("userProfile.emailAddressPage.emailLink.resendButton"),onResendCodeClicked:y}),(0,r.tZ)(a.K,{children:(0,r.tZ)(u.Button,{variant:"ghost",localizationKey:(0,u.localizationKeys)("userProfile.formButtonReset"),elementDescriptor:u.descriptors.formButtonReset,onClick:s})})]})},v=()=>(0,r.tZ)(s.Ej,{title:(0,u.localizationKeys)("signUp.emailLink.verifiedSwitchTab.title"),subtitle:(0,u.localizationKeys)("signUp.emailLink.verifiedSwitchTab.subtitle"),status:"verified"})},36871:function(e,t,i){i.r(t),i.d(t,{UserProfile:()=>tB,UserProfileModal:()=>tw});var r=i(79109),o=i(69144),l=i(2672),a=i(15579),n=i(23394),s=i(31673),c=i(11576),d=i(39541),u=i(24676),h=i(91085);let m=e=>{let{pages:t}=(0,c.useUserProfileContext)();return(0,r.BX)(a.Uh,{contentRef:e.contentRef,children:[(0,r.tZ)(a.l2,{title:(0,h.u1)("userProfile.navbar.title"),description:(0,h.u1)("userProfile.navbar.description"),routes:t.routes,contentRef:e.contentRef}),e.children]})};i(50725);var p=i(94995),v=i(12264),f=i(15515),g=i(83799),y=i(44455),P=i(92654);i(65223),i(28419),i(56113);var Z=i(19655),b=i(33009),z=i(26917),S=i(8969),C=i(99805),K=i(44709),x=i(12464),R=i(77623);i(87945);let _=e=>{let{strategy:t}=e,i=(0,l.useCardState)(),{user:o}=(0,g.aF)(),{navigate:a}=(0,u.useRouter)(),{strategyToDisplayData:n}=(0,x.vO)(),{additionalOAuthScopes:s,componentName:h,mode:m}=(0,c.useUserProfileContext)(),p="modal"===m,v=(0,g.WZ)(()=>{let e=t.replace("oauth_",""),i=p?(0,z.bX)({url:window.location.href,componentName:h,socialProvider:e}):window.location.href,r=s?s[e]:[];return null==o?void 0:o.createExternalAccount({strategy:t,redirectUrl:i,additionalScopes:r})}),f=n[t].iconUrl?(0,r.tZ)(d.Image,{isLoading:i.loadingMetadata===t,isDisabled:i.isLoading,elementDescriptor:d.descriptors.providerIcon,elementId:d.descriptors.providerIcon.setId(n[t].id),src:n[t].iconUrl,alt:"Connect ".concat(n[t].name," account"),sx:e=>({width:e.sizes.$4})}):(0,r.tZ)(S.e_,{id:n[t].id,value:n[t].name,isLoading:i.loadingMetadata===t,isDisabled:i.isLoading});return(0,r.tZ)(Z.zd.ActionMenuItem,{id:n[t].id,onClick:()=>{if(o)return i.setLoading(t),v().then(e=>{var r;e&&(null===(r=e.verification)||void 0===r?void 0:r.externalVerificationRedirectURL)&&((0,R._v)(2e3).then(()=>i.setIdle(t)),a(e.verification.externalVerificationRedirectURL.href))}).catch(e=>{(0,R.S3)(e,[],i.setError),i.setIdle(t)})},isDisabled:i.isLoading,variant:"ghost",isLoading:i.loadingMetadata===t,focusRing:!1,closeAfterClick:!1,localizationKey:(0,d.localizationKeys)("userProfile.connectedAccountPage.socialButtonsBlockButton",{provider:n[t].name}),sx:e=>({justifyContent:"start",gap:e.space.$2}),leftIcon:f},t)},B=e=>{let{onClick:t}=e,{user:i}=(0,g.aF)(),{strategies:o}=(0,x.vO)(),l=o.filter(e=>e.startsWith("oauth")),a=null==i?void 0:i.verifiedExternalAccounts.map(e=>"oauth_".concat(e.provider)),n=l.filter(e=>!a.includes(e));return 0===n.length?null:(0,r.tZ)(Z.zd.ActionMenu,{triggerLocalizationKey:(0,d.localizationKeys)("userProfile.start.connectedAccountsSection.primaryButton"),id:"connectedAccounts",onClick:t,children:n.map(e=>(0,r.tZ)(_,{strategy:e},e))})},w=e=>{var t;let{onSuccess:i,onReset:l}=e,{user:a}=(0,g.aF)(),{emailId:n}=e,s=null==a?void 0:a.emailAddresses.find(e=>e.id===n),c=o.useRef(null==s?void 0:s.emailAddress),u=(null==s?void 0:null===(t=s.verification)||void 0===t?void 0:t.status)==="verified"?(0,d.localizationKeys)("userProfile.emailAddressPage.removeResource.messageLine2"):void 0;return c.current?(0,r.tZ)(S.LE,{title:(0,d.localizationKeys)("userProfile.emailAddressPage.removeResource.title"),messageLine1:(0,d.localizationKeys)("userProfile.emailAddressPage.removeResource.messageLine1",{identifier:c.current}),messageLine2:u,successMessage:(0,d.localizationKeys)("userProfile.emailAddressPage.removeResource.successMessage",{emailAddress:c.current}),deleteResource:()=>Promise.resolve(null==s?void 0:s.destroy()),onSuccess:i,onReset:l}):null},k=e=>{var t;let{phoneId:i,onSuccess:l,onReset:a}=e,{user:n}=(0,g.aF)(),s=null==n?void 0:n.phoneNumbers.find(e=>e.id===i),c=o.useRef(null==s?void 0:s.phoneNumber),u=(null==s?void 0:null===(t=s.verification)||void 0===t?void 0:t.status)==="verified"?(0,d.localizationKeys)("userProfile.phoneNumberPage.removeResource.messageLine2"):void 0;return c.current?(0,r.tZ)(S.LE,{title:(0,d.localizationKeys)("userProfile.phoneNumberPage.removeResource.title"),messageLine1:(0,d.localizationKeys)("userProfile.phoneNumberPage.removeResource.messageLine1",{identifier:c.current}),messageLine2:u,successMessage:(0,d.localizationKeys)("userProfile.phoneNumberPage.removeResource.successMessage",{phoneNumber:c.current}),deleteResource:()=>Promise.resolve(null==s?void 0:s.destroy()),onSuccess:l,onReset:a}):null},I=e=>{var t,i;let{accountId:l,onSuccess:a,onReset:n}=e,{user:s}=(0,g.aF)(),c=null==s?void 0:s.externalAccounts.find(e=>e.id===l),u=o.useRef(null==c?void 0:c.provider),{providerToDisplayData:h}=(0,x.vO)();return u.current?(0,r.tZ)(S.LE,{title:(0,d.localizationKeys)("userProfile.connectedAccountPage.removeResource.title"),messageLine1:(0,d.localizationKeys)("userProfile.connectedAccountPage.removeResource.messageLine1",{identifier:null===(t=h[u.current])||void 0===t?void 0:t.name}),messageLine2:(0,d.localizationKeys)("userProfile.connectedAccountPage.removeResource.messageLine2"),successMessage:(0,d.localizationKeys)("userProfile.connectedAccountPage.removeResource.successMessage",{connectedAccount:null===(i=h[u.current])||void 0===i?void 0:i.name}),deleteResource:()=>Promise.resolve(null==c?void 0:c.destroy()),onSuccess:a,onReset:n}):null},T=e=>{var t;let{user:i}=(0,g.aF)(),{walletId:l,onSuccess:a,onReset:n}=e,s=null==i?void 0:i.web3Wallets.find(e=>e.id===l),c=o.useRef(null==s?void 0:s.web3Wallet),u=(null==s?void 0:null===(t=s.verification)||void 0===t?void 0:t.status)==="verified"?(0,d.localizationKeys)("userProfile.web3WalletPage.removeResource.messageLine2"):void 0;return c.current?(0,r.tZ)(S.LE,{title:(0,d.localizationKeys)("userProfile.web3WalletPage.removeResource.title"),messageLine1:(0,d.localizationKeys)("userProfile.web3WalletPage.removeResource.messageLine1",{identifier:c.current}),messageLine2:u,successMessage:(0,d.localizationKeys)("userProfile.web3WalletPage.removeResource.successMessage",{web3Wallet:c.current}),deleteResource:()=>Promise.resolve(null==s?void 0:s.destroy()),onSuccess:a,onReset:n}):null},A=e=>{let{user:t}=(0,g.aF)(),{phoneId:i,onSuccess:l,onReset:a}=e,n=null==t?void 0:t.phoneNumbers.find(e=>e.id===i),s=o.useRef(null==n?void 0:n.phoneNumber);return s.current?(0,r.tZ)(S.LE,{title:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.removeResource.title"),messageLine1:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.removeResource.messageLine1",{identifier:s.current}),messageLine2:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.removeResource.messageLine2"),successMessage:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.removeResource.successMessage",{mfaPhoneCode:s.current}),deleteResource:()=>Promise.resolve(null==n?void 0:n.setReservedForSecondFactor({reserved:!1})),onSuccess:l,onReset:a}):null},X=e=>{let{onSuccess:t,onReset:i}=e,{user:o}=(0,g.aF)();return o?(0,r.tZ)(S.LE,{title:(0,d.localizationKeys)("userProfile.mfaTOTPPage.removeResource.title"),messageLine1:(0,d.localizationKeys)("userProfile.mfaTOTPPage.removeResource.messageLine1"),messageLine2:(0,d.localizationKeys)("userProfile.mfaTOTPPage.removeResource.messageLine2"),successMessage:(0,d.localizationKeys)("userProfile.mfaTOTPPage.removeResource.successMessage"),deleteResource:o.disableTOTP,onSuccess:t,onReset:i}):null},L=e=>{let{onSuccess:t,onReset:i,passkey:o}=e;return(0,r.tZ)(S.LE,{title:(0,d.localizationKeys)("userProfile.passkeyScreen.removeResource.title"),messageLine1:(0,d.localizationKeys)("userProfile.passkeyScreen.removeResource.messageLine1",{name:o.name}),deleteResource:o.delete,onSuccess:t,onReset:i})},F=e=>{let{close:t}=(0,K.XC)();return(0,r.tZ)(I,{onSuccess:t,onReset:t,...e})},E=["external_account_missing_refresh_token","oauth_fetch_user_error","oauth_token_exchange_error","external_account_email_address_verification_required"],D=(0,l.withCardStateProvider)(e=>{var t;let{shouldAllowCreation:i=!0}=e,{user:a}=(0,g.aF)(),n=(0,l.useCardState)(),s=!!(null==a?void 0:null===(t=a.externalAccounts)||void 0===t?void 0:t.length),[c,u]=(0,o.useState)(null);if(!a||!i&&!s)return null;let h=[...a.verifiedExternalAccounts,...a.unverifiedExternalAccounts.filter(e=>{var t;return null===(t=e.verification)||void 0===t?void 0:t.error})];return(0,r.BX)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.connectedAccountsSection.title"),centered:!1,id:"connectedAccounts",children:[(0,r.tZ)(y.Z.Alert,{children:n.error}),(0,r.BX)(C.a.Root,{value:c,onChange:u,children:[(0,r.tZ)(Z.zd.ItemList,{id:"connectedAccounts",children:h.map(e=>(0,r.tZ)($,{account:e},e.id))}),i&&(0,r.tZ)(B,{onClick:()=>u(null)})]})]})}),$=e=>{var t,i,a,n,s,h;let{account:m}=e,{additionalOAuthScopes:p,componentName:v,mode:f}=(0,c.useUserProfileContext)(),{navigate:y}=(0,u.useRouter)(),{user:P}=(0,g.aF)(),b=(0,l.useCardState)(),K=m.id,_="modal"===f,B=_?(0,z.bX)({url:window.location.href,componentName:v}):window.location.href,w=(0,g.WZ)(()=>null==P?void 0:P.createExternalAccount({strategy:m.verification.strategy,redirectUrl:B,additionalScopes:A}));if(!P)return null;let{providerToDisplayData:k}=(0,x.vO)(),I=m.username||m.emailAddress,T=null===(i=m.verification)||void 0===i?void 0:null===(t=i.error)||void 0===t?void 0:t.longMessage,A=function(e,t){if(!t)return[];let i=t[e.provider]||[],r=e.approvedScopes.split(" ");return 0===i.filter(e=>!r.includes(e)).length?[]:i}(m,p),X=A.length>0&&""!=m.approvedScopes,L=E.includes((null===(n=m.verification)||void 0===n?void 0:null===(a=n.error)||void 0===a?void 0:a.code)||"")||X,D=L?(0,d.localizationKeys)("userProfile.start.connectedAccountsSection.subtitle__disconnected"):T,$=async()=>{let e=_?(0,z.bX)({url:window.location.href,componentName:v}):window.location.href;try{let i;if(i=X?await m.reauthorize({additionalScopes:A,redirectUrl:e}):await w()){var t;await y((null===(t=i.verification.externalVerificationRedirectURL)||void 0===t?void 0:t.href)||"")}}catch(e){(0,R.S3)(e,[],b.setError)}};return(0,r.BX)(o.Fragment,{children:[(0,r.BX)(Z.zd.Item,{id:"connectedAccounts",children:[(0,r.BX)(d.Flex,{sx:e=>({overflow:"hidden",gap:e.space.$2}),children:[(0,r.tZ)(()=>k[m.provider].iconUrl?(0,r.tZ)(d.Image,{elementDescriptor:[d.descriptors.providerIcon],elementId:d.descriptors.socialButtonsProviderIcon.setId(m.provider),alt:k[m.provider].name,src:k[m.provider].iconUrl,sx:e=>({width:e.sizes.$4,flexShrink:0})}):(0,r.tZ)(S.e_,{id:m.provider,value:k[m.provider].name}),{}),(0,r.tZ)(d.Box,{sx:{whiteSpace:"nowrap",overflow:"hidden"},children:(0,r.BX)(d.Flex,{gap:1,center:!0,children:[(0,r.tZ)(d.Text,{sx:e=>({color:e.colors.$colorText}),children:"".concat(k[m.provider].name)}),(0,r.tZ)(d.Text,{truncate:!0,as:"span",colorScheme:"secondary",children:I?"• ".concat(I):""})]})})]}),(0,r.tZ)(O,{account:m})]}),L&&(0,r.BX)(d.Box,{sx:e=>({padding:"".concat(e.sizes.$none," ").concat(e.sizes.$none," ").concat(e.sizes.$1x5," ").concat(e.sizes.$8x5)}),children:[(0,r.tZ)(d.Text,{colorScheme:"secondary",sx:e=>({paddingRight:e.sizes.$1x5,display:"inline-block"}),localizationKey:D}),(0,r.tZ)(d.Button,{sx:{display:"inline-block"},onClick:$,variant:"link",localizationKey:(0,d.localizationKeys)("userProfile.start.connectedAccountsSection.actionLabel__connectionFailed")})]}),(null===(h=m.verification)||void 0===h?void 0:null===(s=h.error)||void 0===s?void 0:s.code)&&!L&&(0,r.tZ)(d.Text,{colorScheme:"danger",sx:e=>({padding:"".concat(e.sizes.$none," ").concat(e.sizes.$1x5," ").concat(e.sizes.$1x5," ").concat(e.sizes.$8x5)}),children:T}),(0,r.tZ)(C.a.Open,{value:"remove-".concat(K),children:(0,r.tZ)(C.a.Card,{variant:"destructive",children:(0,r.tZ)(F,{accountId:m.id})})})]},m.id)},O=e=>{let{account:t}=e,{open:i}=(0,K.XC)(),o=t.id,l=[{label:(0,d.localizationKeys)("userProfile.start.connectedAccountsSection.destructiveActionTitle"),isDestructive:!0,onClick:()=>i("remove-".concat(o))}].filter(e=>null!==e);return(0,r.tZ)(b.a,{actions:l})};i(79876);let N=e=>t=>t.id===e?-1:1,W=e=>e.defaultSecondFactor?-1:1;function M(e){let t=[];return Object.entries(e).forEach(e=>{let[,i]=e;i.used_for_second_factor&&t.push(...i.second_factors)}),t}function U(e,t){let i=M(e);return t.totpEnabled&&(i=i.filter(e=>"totp"!==e)),(t.backupCodeEnabled||!t.twoFactorEnabled)&&(i=i.filter(e=>"backup_code"!==e)),i}function Y(e,t){if(!e)return[];let i=e.filter(e=>e.id===t),r=e.filter(e=>e.id!==t),o=r.filter(e=>{var t;return(null===(t=e.verification)||void 0===t?void 0:t.status)==="verified"}),l=r.filter(e=>{var t,i;return!!(null===(t=e.verification)||void 0===t?void 0:t.status)&&(null===(i=e.verification)||void 0===i?void 0:i.status)!=="verified"}),a=r.filter(e=>!e.verification.status);return o.sort((e,t)=>e.id.localeCompare(t.id)),l.sort((e,t)=>{var i,r;return(null===(i=e.verification)||void 0===i?void 0:i.expireAt)&&(null===(r=t.verification)||void 0===r?void 0:r.expireAt)?e.verification.expireAt.getTime()-t.verification.expireAt.getTime():0}),[...i,...o,...l,...a]}var q=i(99140);let j=e=>{let{close:t}=(0,K.XC)();return(0,r.tZ)(w,{onSuccess:t,onReset:t,...e})},V=e=>{let{close:t}=(0,K.XC)();return(0,r.tZ)(q._,{onSuccess:t,onReset:t,...e})},H=e=>{let{shouldAllowCreation:t=!0}=e,{user:i}=(0,g.aF)();return(0,r.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.emailAddressesSection.title"),centered:!1,id:"emailAddresses",children:(0,r.tZ)(C.a.Root,{children:(0,r.BX)(Z.zd.ItemList,{id:"emailAddresses",children:[Y(null==i?void 0:i.emailAddresses,null==i?void 0:i.primaryEmailAddressId).map(e=>{let t=e.id;return(0,r.BX)(o.Fragment,{children:[(0,r.BX)(Z.zd.Item,{id:"emailAddresses",children:[(0,r.BX)(d.Flex,{sx:e=>({overflow:"hidden",gap:e.space.$1}),children:[(0,r.tZ)(d.Text,{sx:e=>({color:e.colors.$colorText}),truncate:!0,children:e.emailAddress}),(null==i?void 0:i.primaryEmailAddressId)===t&&(0,r.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__primary")}),"verified"!==e.verification.status&&(0,r.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__unverified")})]}),(0,r.tZ)(Q,{email:e})]}),(0,r.tZ)(C.a.Open,{value:"remove-".concat(t),children:(0,r.tZ)(C.a.Card,{variant:"destructive",children:(0,r.tZ)(j,{emailId:t})})}),(0,r.tZ)(C.a.Open,{value:"verify-".concat(t),children:(0,r.tZ)(C.a.Card,{children:(0,r.tZ)(V,{emailId:t})})})]},e.emailAddress)}),t&&(0,r.BX)(r.HY,{children:[(0,r.tZ)(C.a.Trigger,{value:"add",children:(0,r.tZ)(Z.zd.ArrowButton,{id:"emailAddresses",localizationKey:(0,d.localizationKeys)("userProfile.start.emailAddressesSection.primaryButton")})}),(0,r.tZ)(C.a.Open,{value:"add",children:(0,r.tZ)(C.a.Card,{children:(0,r.tZ)(V,{})})})]})]})})})},Q=e=>{let{email:t}=e,i=(0,l.useCardState)(),{user:o}=(0,g.aF)(),{open:a}=(0,K.XC)(),n=t.id,s=(null==o?void 0:o.primaryEmailAddressId)===n,c="verified"===t.verification.status,u=(0,g.WZ)(()=>null==o?void 0:o.update({primaryEmailAddressId:n})),h=[s&&!c?{label:(0,d.localizationKeys)("userProfile.start.emailAddressesSection.detailsAction__primary"),onClick:()=>a("verify-".concat(n))}:null,!s&&c?{label:(0,d.localizationKeys)("userProfile.start.emailAddressesSection.detailsAction__nonPrimary"),onClick:()=>{u().catch(e=>(0,R.S3)(e,[],i.setError))}}:null,s||c?null:{label:(0,d.localizationKeys)("userProfile.start.emailAddressesSection.detailsAction__unverified"),onClick:()=>a("verify-".concat(n))},{label:(0,d.localizationKeys)("userProfile.start.emailAddressesSection.destructiveAction"),isDestructive:!0,onClick:()=>a("remove-".concat(n))}].filter(e=>null!==e);return(0,r.tZ)(b.a,{actions:h})},G=()=>{let{user:e}=(0,g.aF)(),t=null==e?void 0:e.enterpriseAccounts.filter(e=>{let{enterpriseConnection:t}=e;return null==t?void 0:t.active});return(null==t?void 0:t.length)?(0,r.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.enterpriseAccountsSection.title"),id:"enterpriseAccounts",centered:!1,children:(0,r.tZ)(Z.zd.ItemList,{id:"enterpriseAccounts",children:t.map(e=>(0,r.tZ)(J,{account:e},e.id))})}):null},J=e=>{var t,i,o;let{account:l}=e,a=l.emailAddress,n=null==l?void 0:null===(t=l.enterpriseConnection)||void 0===t?void 0:t.name,s=null===(o=l.verification)||void 0===o?void 0:null===(i=o.error)||void 0===i?void 0:i.longMessage;return(0,r.BX)(Z.zd.Item,{id:"enterpriseAccounts",sx:e=>({gap:e.space.$2,justifyContent:"start"}),children:[(0,r.tZ)(ee,{account:l}),(0,r.tZ)(d.Box,{sx:{whiteSpace:"nowrap",overflow:"hidden"},children:(0,r.BX)(d.Flex,{gap:1,center:!0,children:[(0,r.tZ)(d.Text,{truncate:!0,colorScheme:"body",children:n}),(0,r.tZ)(d.Text,{truncate:!0,as:"span",colorScheme:"secondary",children:a?"• ".concat(a):""}),s&&(0,r.tZ)(d.Badge,{colorScheme:"danger",localizationKey:(0,d.localizationKeys)("badge__requiresAction")})]})})]},l.id)},ee=e=>{var t;let{account:i}=e,{provider:o,enterpriseConnection:l}=i,a=o.replace(/(oauth_|saml_)/,"").trim(),n=null!==(t=null==l?void 0:l.name)&&void 0!==t?t:a,s={elementDescriptor:[d.descriptors.providerIcon],alt:n,sx:e=>({width:e.sizes.$4}),elementId:d.descriptors.enterpriseButtonsProviderIcon.setId(i.provider)};return(null==l?void 0:l.logoPublicUrl)?(0,r.tZ)(d.Image,{...s,src:l.logoPublicUrl}):(0,r.tZ)(S.e_,{id:a,value:n,"aria-label":"".concat(n,"'s icon"),elementDescriptor:[d.descriptors.providerIcon,d.descriptors.providerInitialIcon],elementId:d.descriptors.providerInitialIcon.setId(a)})};var et=i(70431),ei=i(19460),er=i(68487),eo=i(10207);let el=(0,l.withCardStateProvider)(e=>{let{phoneId:t,onSuccess:i,onReset:l}=e,{user:a}=(0,g.aF)(),n=o.useRef(null==a?void 0:a.phoneNumbers.find(e=>e.id===t)),s=(0,S.a2)({defaultStep:+!!n.current});return(0,r.BX)(S.en,{...s.props,children:[(0,r.tZ)(ea,{resourceRef:n,title:(0,d.localizationKeys)("userProfile.phoneNumberPage.title"),onSuccess:s.nextStep,onReset:l}),(0,r.tZ)(en,{resourceRef:n,title:(0,d.localizationKeys)("userProfile.phoneNumberPage.verifyTitle"),onSuccess:i,onReset:l})]})}),ea=e=>{var t;let{title:i,onSuccess:o,onReset:a,onUseExistingNumberClick:n,resourceRef:s}=e,c=(0,l.useCardState)(),{user:u}=(0,g.aF)(),h=(0,g.WZ)((e,t)=>e.createPhoneNumber(t)),m=(0,R.Yp)("phoneNumber","",{type:"tel",label:(0,d.localizationKeys)("formFieldLabel__phoneNumber"),isRequired:!0}),p=m.value.length>1&&(null==u?void 0:u.username)!==m.value,v=!!(null==u?void 0:null===(t=u.phoneNumbers)||void 0===t?void 0:t.length)&&n,f=async e=>{if(e.preventDefault(),u)return h(u,{phoneNumber:m.value}).then(e=>{s.current=e,o()}).catch(e=>(0,R.S3)(e,[m],c.setError))};return(0,r.tZ)(er.Y,{headerTitle:i,gap:1,children:(0,r.BX)(et.l.Root,{gap:4,onSubmit:f,children:[(0,r.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)("userProfile.phoneNumberPage.infoText"),colorScheme:"secondary"}),(0,r.tZ)(et.l.ControlRow,{elementId:m.id,children:(0,r.tZ)(et.l.PhoneInput,{...m.props,autoFocus:!0})}),(0,r.BX)(d.Flex,{justify:v?"between":"end",children:[v&&(0,r.tZ)(d.Button,{variant:"ghost",localizationKey:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.backButton"),onClick:n}),(0,r.tZ)(ei.A,{submitLabel:(0,d.localizationKeys)("userProfile.formButtonPrimary__add"),isDisabled:!p,onReset:a})]})]})})},en=e=>{var t,i,o;let{title:l,onSuccess:a,resourceRef:n,onReset:s}=e;return(0,r.tZ)(er.Y,{headerTitle:l,headerSubtitle:(0,d.localizationKeys)("userProfile.phoneNumberPage.verifySubtitle",{identifier:null===(t=n.current)||void 0===t?void 0:t.phoneNumber}),children:(0,r.tZ)(eo.H,{nextStep:a,identification:n.current,identifier:null===(i=n.current)||void 0===i?void 0:i.phoneNumber,prepareVerification:null===(o=n.current)||void 0===o?void 0:o.prepareVerification,onReset:s})})},es=e=>{let{close:t}=(0,K.XC)();return(0,r.tZ)(k,{onSuccess:t,onReset:t,...e})},ec=e=>{let{close:t}=(0,K.XC)();return(0,r.tZ)(el,{onSuccess:t,onReset:t,...e})},ed=e=>{var t;let{shouldAllowCreation:i=!0}=e,{user:l}=(0,g.aF)(),a=!!(null==l?void 0:null===(t=l.phoneNumbers)||void 0===t?void 0:t.length);return i||a?(0,r.tZ)(Z.zd.Root,{centered:!1,title:(0,d.localizationKeys)("userProfile.start.phoneNumbersSection.title"),id:"phoneNumbers",children:(0,r.tZ)(C.a.Root,{children:(0,r.BX)(Z.zd.ItemList,{id:"phoneNumbers",children:[Y(null==l?void 0:l.phoneNumbers,null==l?void 0:l.primaryPhoneNumberId).map(e=>{let t=e.id;return(0,r.BX)(o.Fragment,{children:[(0,r.BX)(Z.zd.Item,{id:"phoneNumbers",children:[(0,r.tZ)(d.Box,{sx:{whiteSpace:"nowrap",overflow:"hidden"},children:(0,r.BX)(d.Flex,{gap:2,center:!0,children:[(0,r.tZ)(d.Text,{sx:e=>({color:e.colors.$colorText}),children:(0,R.L_)(e.phoneNumber)}),(null==l?void 0:l.primaryPhoneNumberId)===t&&(0,r.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__primary")}),"verified"!==e.verification.status&&(0,r.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__unverified")})]})}),(0,r.tZ)(eu,{phone:e})]}),(0,r.tZ)(C.a.Open,{value:"remove-".concat(t),children:(0,r.tZ)(C.a.Card,{variant:"destructive",children:(0,r.tZ)(es,{phoneId:t})})}),(0,r.tZ)(C.a.Open,{value:"verify-".concat(t),children:(0,r.tZ)(C.a.Card,{children:(0,r.tZ)(ec,{phoneId:t})})})]},t)}),i&&(0,r.BX)(r.HY,{children:[(0,r.tZ)(C.a.Trigger,{value:"add",children:(0,r.tZ)(Z.zd.ArrowButton,{id:"phoneNumbers",localizationKey:(0,d.localizationKeys)("userProfile.start.phoneNumbersSection.primaryButton")})}),(0,r.tZ)(C.a.Open,{value:"add",children:(0,r.tZ)(C.a.Card,{children:(0,r.tZ)(ec,{})})})]})]})})}):null},eu=e=>{let{phone:t}=e,i=(0,l.useCardState)(),{open:o}=(0,K.XC)(),{user:a}=(0,g.aF)(),n=t.id,s=(0,g.WZ)(e=>e.update({primaryPhoneNumberId:t.id}));if(!a)return null;let c=a.primaryPhoneNumberId===t.id,u="verified"===t.verification.status,h=[c&&!u?{label:(0,d.localizationKeys)("userProfile.start.phoneNumbersSection.detailsAction__primary"),onClick:()=>o("verify-".concat(n))}:null,!c&&u?{label:(0,d.localizationKeys)("userProfile.start.phoneNumbersSection.detailsAction__nonPrimary"),onClick:()=>s(a).catch(e=>(0,R.S3)(e,[],i.setError))}:null,c||u?null:{label:(0,d.localizationKeys)("userProfile.start.phoneNumbersSection.detailsAction__unverified"),onClick:()=>o("verify-".concat(n))},{label:(0,d.localizationKeys)("userProfile.start.phoneNumbersSection.destructiveAction"),isDestructive:!0,onClick:()=>o("remove-".concat(n))}].filter(e=>null!==e);return(0,r.tZ)(b.a,{actions:h})};var eh=i(81201);let em=(0,l.withCardStateProvider)(e=>{var t;let{onSuccess:i,onReset:o}=e,{user:a}=(0,g.aF)(),n=(0,g.WZ)(e=>null==a?void 0:a.update({username:e})),{userSettings:s}=(0,c.useEnvironment)(),u=(0,l.useCardState)(),{t:h,locale:m}=(0,d.useLocalizations)(),{usernameSettings:p}=s,v=(0,R.Yp)("username",(null==a?void 0:a.username)||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__username"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__username"),buildErrorMessage:e=>(0,R.fq)(e,{t:h,locale:m,usernameSettings:p})});if(!a)return null;let f=null===(t=s.attributes.username)||void 0===t?void 0:t.required,y=(!f||v.value.length>0)&&a.username!==v.value,P=async()=>{try{await n(v.value),i()}catch(e){(0,R.S3)(e,[v],u.setError)}};return(0,r.tZ)(er.Y,{headerTitle:a.username?(0,d.localizationKeys)("userProfile.usernamePage.title__update"):(0,d.localizationKeys)("userProfile.usernamePage.title__set"),children:(0,r.BX)(et.l.Root,{onSubmit:P,children:[(0,r.tZ)(et.l.ControlRow,{elementId:v.id,children:(0,r.tZ)(et.l.PlainInput,{...v.props,autoFocus:!0,isRequired:f})}),(0,r.tZ)(ei.A,{isDisabled:!y,onReset:o})]})})}),ep=()=>{let{close:e}=(0,K.XC)();return(0,r.tZ)(em,{onSuccess:e,onReset:e})},ev=()=>{let{user:e}=(0,g.aF)();return e?(0,r.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.usernameSection.title"),id:"username",sx:{alignItems:"center",[eh.mqu.md]:{alignItems:"flex-start"}},children:(0,r.BX)(C.a.Root,{children:[(0,r.tZ)(C.a.Closed,{value:"edit",children:(0,r.BX)(Z.zd.Item,{id:"username",sx:{paddingLeft:e.username?void 0:"0"},children:[e.username&&(0,r.tZ)(d.Text,{truncate:!0,sx:e=>({color:e.colors.$colorText}),children:e.username}),(0,r.tZ)(C.a.Trigger,{value:"edit",children:(0,r.tZ)(Z.zd.Button,{id:"username",localizationKey:e.username?(0,d.localizationKeys)("userProfile.start.usernameSection.primaryButton__updateUsername"):(0,d.localizationKeys)("userProfile.start.usernameSection.primaryButton__setUsername")})})]})}),(0,r.tZ)(C.a.Open,{value:"edit",children:(0,r.tZ)(C.a.Card,{children:(0,r.tZ)(ep,{})})})]})}):null};var ef=i(97295),eg=i(43300),ey=i(6654),eP=i(5472);let eZ=e=>{let{user:t,...i}=e;return(0,r.tZ)(ey.C,{...i,title:(0,h.u1)("userProfile.profilePage.imageFormTitle"),avatarPreview:(0,r.tZ)(eP.Y,{size:e=>e.sizes.$12,...t})})},eb=(0,l.withCardStateProvider)(e=>{let{onSuccess:t,onReset:i}=e,o=(0,l.useCardState)(),{user:a}=(0,g.aF)();if(!a)return null;let{first_name:n,last_name:s}=(0,c.useEnvironment)().userSettings.attributes,u=null==n?void 0:n.enabled,h=null==s?void 0:s.enabled,m=a.firstName||"",p=a.lastName||"",v=(0,R.Yp)("firstName",a.firstName||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__firstName"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__firstName"),isRequired:null==s?void 0:s.required}),f=(0,R.Yp)("lastName",a.lastName||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__lastName"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__lastName"),isRequired:null==s?void 0:s.required}),y=u&&v.value!==m||h&&f.value!==p,P=u&&n.required||h&&s.required,Z=P&&!!f.value&&!!v.value&&y,b=a.enterpriseAccounts.some(e=>e.active),S=async e=>(e.preventDefault(),(y?a.update({firstName:v.value,lastName:f.value}):Promise.resolve()).then(t).catch(e=>{(0,R.S3)(e,[v,f],o.setError)}));return(0,r.BX)(er.Y,{headerTitle:(0,d.localizationKeys)("userProfile.profilePage.title"),children:[b&&(0,r.tZ)(eg.e,{message:(0,d.localizationKeys)("userProfile.profilePage.readonly")}),(0,r.BX)(et.l.Root,{onSubmit:S,sx:e=>({gap:e.space.$6}),children:[(0,r.tZ)(eZ,{user:a,onAvatarChange:e=>a.setProfileImage({file:e}).then(()=>{o.setIdle()}).catch(e=>(0,R.S3)(e,[],o.setError)),onAvatarRemove:(0,z.QO)(a.imageUrl)?null:()=>{a.setProfileImage({file:null}).then(()=>{o.setIdle()}).catch(e=>(0,R.S3)(e,[],o.setError))}}),(u||h)&&(0,r.BX)(et.l.ControlRow,{elementId:"name",children:[u&&(0,r.tZ)(et.l.PlainInput,{...v.props,isDisabled:b,autoFocus:!0}),h&&(0,r.tZ)(et.l.PlainInput,{...f.props,isDisabled:b,autoFocus:!u})]}),(0,r.tZ)(ei.A,{isDisabled:P?!Z:!y,onReset:i})]})]})}),ez=()=>{let{close:e}=(0,K.XC)();return(0,r.tZ)(eb,{onSuccess:e,onReset:e})},eS=()=>{let{user:e}=(0,g.aF)();if(!e)return null;let{username:t,primaryEmailAddress:i,primaryPhoneNumber:o,primaryWeb3Wallet:l,...a}=e;return(0,r.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.profileSection.title"),id:"profile",sx:{[eh.mqu.md]:{alignItems:"flex-start"}},children:(0,r.BX)(C.a.Root,{children:[(0,r.tZ)(C.a.Closed,{value:"edit",children:(0,r.BX)(Z.zd.Item,{id:"profile",children:[(0,r.tZ)(ef.E,{user:a,size:"lg",mainIdentifierVariant:"subtitle",sx:e=>({color:e.colors.$colorText})}),(0,r.tZ)(C.a.Trigger,{value:"edit",children:(0,r.tZ)(Z.zd.Button,{id:"profile",localizationKey:(0,d.localizationKeys)("userProfile.start.profileSection.primaryButton")})})]})}),(0,r.tZ)(C.a.Open,{value:"edit",children:(0,r.tZ)(C.a.Card,{children:(0,r.tZ)(ez,{})})})]})})};i(92037);var eC=i(35274);let eK=(0,l.withCardStateProvider)(e=>{let{onClick:t}=e,i=(0,l.useCardState)(),{user:o}=(0,g.aF)(),{strategies:a,strategyToDisplayData:n}=(0,x.vO)(),s=a.filter(e=>e.startsWith("web3")),c=null==o?void 0:o.verifiedWeb3Wallets.map(e=>e.verification.strategy),u=s.filter(e=>!c.includes(e)),h=(0,g.WZ)(e=>null==o?void 0:o.createWeb3Wallet({web3Wallet:e})),m=async e=>{let t=e.replace("web3_","").replace("_signature","");i.setError(void 0);try{i.setLoading(e);let r=await (0,eC.Ly)({provider:t});if(!o)throw Error("user is not defined");let l=await h(r),a=null==(l=await (null==l?void 0:l.prepareVerification({strategy:e})))?void 0:l.verification.message,n=await (0,eC.bQ)({identifier:r,nonce:a,provider:t});await (null==l?void 0:l.attemptVerification({signature:n})),i.setIdle()}catch(t){i.setIdle();let e=(0,R.zQ)(t);e?i.setError(e.longMessage):(0,R.S3)(t,[],i.setError)}};return 0===u.length?null:(0,r.BX)(r.HY,{children:[(0,r.tZ)(Z.zd.ActionMenu,{id:"web3Wallets",triggerLocalizationKey:(0,d.localizationKeys)("userProfile.start.web3WalletsSection.primaryButton"),onClick:t,children:u.map(e=>(0,r.tZ)(Z.zd.ActionMenuItem,{id:n[e].id,onClick:()=>m(e),isLoading:i.loadingMetadata===e,isDisabled:i.isLoading,localizationKey:(0,d.localizationKeys)("userProfile.web3WalletPage.web3WalletButtonsBlockButton",{provider:n[e].name}),sx:e=>({justifyContent:"start",gap:e.space.$2}),leftIcon:(0,r.tZ)(d.Image,{elementDescriptor:d.descriptors.providerIcon,elementId:d.descriptors.providerIcon.setId(n[e].id),isLoading:i.loadingMetadata===e,isDisabled:i.isLoading,src:n[e].iconUrl,alt:"Connect ".concat(n[e].name),sx:e=>({width:e.sizes.$5})})},e))}),i.error&&(0,r.tZ)(d.Text,{colorScheme:"danger",sx:e=>({padding:e.sizes.$1x5,paddingLeft:e.sizes.$8x5}),children:i.error})]})}),ex=e=>{let{close:t}=(0,K.XC)();return(0,r.tZ)(T,{onSuccess:t,onReset:t,...e})},eR=e=>e.length<=10?e:e.slice(0,6)+"..."+e.slice(-4),e_=(0,l.withCardStateProvider)(e=>{var t;let{shouldAllowCreation:i=!0}=e,{user:a}=(0,g.aF)(),n=(0,l.useCardState)(),{strategyToDisplayData:s}=(0,x.vO)(),c=!!(null==a?void 0:null===(t=a.web3Wallets)||void 0===t?void 0:t.length),[u,h]=(0,o.useState)(null);return i||c?(0,r.BX)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.web3WalletsSection.title"),centered:!1,id:"web3Wallets",children:[(0,r.tZ)(y.Z.Alert,{children:n.error}),(0,r.BX)(C.a.Root,{value:u,onChange:h,children:[(0,r.tZ)(Z.zd.ItemList,{id:"web3Wallets",children:Y(null==a?void 0:a.web3Wallets,null==a?void 0:a.primaryWeb3WalletId).map(e=>{let t=e.verification.strategy,i=e.id;return s[t]&&(0,r.BX)(o.Fragment,{children:[(0,r.BX)(Z.zd.Item,{id:"web3Wallets",align:"start",children:[(0,r.BX)(d.Flex,{sx:e=>({alignItems:"center",gap:e.space.$2,width:"100%"}),children:[s[t].iconUrl&&(0,r.tZ)(d.Image,{src:s[t].iconUrl,alt:s[t].name,sx:e=>({width:e.sizes.$4})}),(0,r.tZ)(d.Box,{sx:{whiteSpace:"nowrap",overflow:"hidden"},children:(0,r.BX)(d.Flex,{gap:2,justify:"start",children:[(0,r.BX)(d.Text,{children:[s[t].name," (",eR(e.web3Wallet),")"]}),(null==a?void 0:a.primaryWeb3WalletId)===i&&(0,r.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__primary")}),"verified"!==e.verification.status&&(0,r.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__unverified")})]})})]}),(0,r.tZ)(eB,{walletId:i})]},i),(0,r.tZ)(C.a.Open,{value:"remove-".concat(i),children:(0,r.tZ)(C.a.Card,{variant:"destructive",children:(0,r.tZ)(ex,{walletId:e.id})})})]},e.id)})}),i&&(0,r.tZ)(eK,{onClick:()=>h(null)})]})]}):null}),eB=e=>{let{walletId:t}=e,i=(0,l.useCardState)(),{open:o}=(0,K.XC)(),{user:a}=(0,g.aF)(),n=(null==a?void 0:a.primaryWeb3WalletId)===t,s=(0,g.WZ)(()=>null==a?void 0:a.update({primaryWeb3WalletId:t})),c=[n?null:{label:(0,d.localizationKeys)("userProfile.start.web3WalletsSection.detailsAction__nonPrimary"),onClick:()=>{s().catch(e=>(0,R.S3)(e,[],i.setError))}},{label:(0,d.localizationKeys)("userProfile.start.web3WalletsSection.destructiveAction"),isDestructive:!0,onClick:()=>o("remove-".concat(t))}].filter(e=>null!==e);return(0,r.tZ)(b.a,{actions:c})},ew=(0,l.withCardStateProvider)(()=>{var e,t,i,o;let{attributes:a,social:n,enterpriseSSO:s}=(0,c.useEnvironment)().userSettings,u=(0,l.useCardState)(),{user:h}=(0,g.aF)(),m=null===(e=a.username)||void 0===e?void 0:e.enabled,p=null===(t=a.email_address)||void 0===t?void 0:t.enabled,v=null===(i=a.phone_number)||void 0===i?void 0:i.enabled,f=n&&Object.values(n).filter(e=>e.enabled).length>0,Z=h&&s.enabled,b=null===(o=a.web3_wallet)||void 0===o?void 0:o.enabled,z=!Z||!h.enterpriseAccounts.some(e=>{var t;return e.active&&(null===(t=e.enterpriseConnection)||void 0===t?void 0:t.disableAdditionalIdentifications)});return(0,r.tZ)(d.Col,{elementDescriptor:d.descriptors.page,sx:e=>({gap:e.space.$8,color:e.colors.$colorText}),children:(0,r.BX)(d.Col,{elementDescriptor:d.descriptors.profilePage,elementId:d.descriptors.profilePage.setId("account"),children:[(0,r.tZ)(P.h.Root,{children:(0,r.tZ)(P.h.Title,{localizationKey:(0,d.localizationKeys)("userProfile.start.headerTitle__account"),sx:e=>({marginBottom:e.space.$4}),textVariant:"h2"})}),(0,r.tZ)(y.Z.Alert,{children:u.error}),(0,r.tZ)(eS,{}),m&&(0,r.tZ)(ev,{}),p&&(0,r.tZ)(H,{shouldAllowCreation:z}),v&&(0,r.tZ)(ed,{shouldAllowCreation:z}),f&&(0,r.tZ)(D,{shouldAllowCreation:z}),Z&&(0,r.tZ)(G,{}),b&&(0,r.tZ)(e_,{shouldAllowCreation:z})]})})});i(45261),i(70957),i(24551),i(22349);var ek=i(65006);let eI=()=>{let{navigate:e}=(0,u.useRouter)();return(0,r.BX)(r.HY,{children:[(0,r.tZ)(P.h.Root,{sx:e=>({borderBottomWidth:e.borderWidths.$normal,borderBottomStyle:e.borderStyles.$solid,borderBottomColor:e.colors.$neutralAlpha100,marginBlockEnd:e.space.$4,paddingBlockEnd:e.space.$4}),children:(0,r.tZ)(P.h.BackLink,{onClick:()=>void e("../",{searchParams:new URLSearchParams("tab=subscriptions")}),children:(0,r.tZ)(P.h.Title,{localizationKey:(0,h.u1)("userProfile.plansPage.title"),textVariant:"h2"})})}),(0,r.tZ)(c.PricingTableContext.Provider,{value:{componentName:"PricingTable",mode:"modal"},children:(0,r.tZ)(ek.b,{})})]})},eT=()=>(0,r.tZ)(c.SubscriberTypeContext.Provider,{value:"user",children:(0,r.tZ)(eI,{})});i(38062);var eA=i(58104),eX=i(96519);let eL=()=>{let{user:e}=(0,g.aF)(),{session:t}=(0,g.kP)(),{data:i,isLoading:o}=(0,x.ib)(null==e?void 0:e.getSessions,"user-sessions");return(0,r.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.activeDevicesSection.title"),centered:!1,id:"activeDevices",children:(0,r.tZ)(Z.zd.ItemList,{id:"activeDevices",disableAnimation:!0,children:o?(0,r.tZ)(eA.m,{}):null==i?void 0:i.sort(N(t.id)).map(e=>eF(e.status)?(0,r.tZ)(eE,{session:e},e.id):null)})})},eF=e=>["active","pending"].includes(e),eE=e=>{var t;let{session:i}=e,o=(null===(t=(0,g.kP)().session)||void 0===t?void 0:t.id)===i.id,l=(0,x._m)(),a=(0,g.WZ)(i.revoke.bind(i)),n=async()=>{if(!o&&i)return l.setLoading(),a().catch(e=>(0,R.S3)(e,[],l.setError)).finally(()=>l.setIdle())};return(0,r.tZ)(Z.zd.Item,{id:"activeDevices",elementDescriptor:d.descriptors.activeDeviceListItem,elementId:o?d.descriptors.activeDeviceListItem.setId("current"):void 0,sx:{alignItems:"flex-start",opacity:l.isLoading?.5:1},isDisabled:l.isLoading,children:(0,r.BX)(r.HY,{children:[(0,r.tZ)(eD,{session:i}),!o&&(0,r.tZ)(e$,{revoke:n})]})})},eD=e=>{let{session:t}=(0,g.kP)(),i=(null==t?void 0:t.id)===e.session.id,o=!!(null==t?void 0:t.actor),l=!!e.session.actor,{city:a,country:n,browserName:s,browserVersion:c,deviceType:u,ipAddress:h,isMobile:m}=e.session.latestActivity,p="".concat(s||""," ").concat(c||"").trim()||"Web browser",v=[a||"",n||""].filter(Boolean).join(", ").trim()||null,{t:f}=(0,d.useLocalizations)();return(0,r.BX)(d.Flex,{elementDescriptor:d.descriptors.activeDevice,elementId:i?d.descriptors.activeDevice.setId("current"):void 0,sx:e=>({width:"100%",overflow:"hidden",gap:e.space.$4,[eh.mqu.sm]:{gap:e.space.$2}}),children:[(0,r.tZ)(d.Flex,{sx:e=>({[eh.mqu.sm]:{padding:"0"},borderRadius:e.radii.$md}),children:(0,r.tZ)(d.Icon,{elementDescriptor:d.descriptors.activeDeviceIcon,elementId:d.descriptors.activeDeviceIcon.setId(m?"mobile":"desktop"),icon:m?eX.Fh:eX.eu,sx:e=>({"--cl-chassis-bottom":"#444444","--cl-chassis-back":"#343434","--cl-chassis-screen":"#575757","--cl-screen":"#000000",width:e.space.$8,height:e.space.$8})})}),(0,r.BX)(d.Col,{align:"start",gap:1,children:[(0,r.BX)(d.Flex,{center:!0,gap:2,children:[(0,r.tZ)(d.Text,{children:u||(m?"Mobile device":"Desktop device")}),i&&(0,r.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__thisDevice"),colorScheme:o?"danger":"primary"}),o&&!l&&(0,r.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__userDevice")}),!i&&l&&(0,r.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__otherImpersonatorDevice"),colorScheme:"danger"})]}),(0,r.tZ)(d.Text,{colorScheme:"secondary",children:p}),(0,r.BX)(d.Text,{colorScheme:"secondary",children:[h," (",v,")"]}),(0,r.tZ)(d.Text,{colorScheme:"secondary",children:f((0,R.Qg)(e.session.lastActiveAt))})]})]})},e$=e=>{let{revoke:t}=e,i=[{label:(0,d.localizationKeys)("userProfile.start.activeDevicesSection.destructiveAction"),isDestructive:!0,onClick:t}].filter(e=>null!==e);return(0,r.tZ)(b.a,{actions:i})};var eO=i(54264);let eN=(0,l.withCardStateProvider)(e=>{let{onReset:t}=e,i=(0,l.useCardState)(),{afterSignOutUrl:o,afterMultiSessionSingleSignOutUrl:a}=(0,c.useSignOutContext)(),{user:n}=(0,g.aF)(),{t:s}=(0,d.useLocalizations)(),{otherSessions:u}=(0,eO.j)({user:n}),{setActive:h}=(0,g.cL)(),m=(0,g.WZ)(()=>null==n?void 0:n.delete()),p=(0,R.Yp)("deleteConfirmation","",{type:"text",label:(0,d.localizationKeys)("userProfile.deletePage.actionDescription"),isRequired:!0,placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__confirmDeletionUserAccount")}),v=p.value===(s((0,d.localizationKeys)("formFieldInputPlaceholder__confirmDeletionUserAccount"))||"Delete account"),f=async()=>{if(v)try{await m();let e=0===u.length?o:a;return await h({session:null,redirectUrl:e})}catch(e){(0,R.S3)(e,[],i.setError)}};return(0,r.tZ)(er.Y,{headerTitle:(0,d.localizationKeys)("userProfile.deletePage.title"),sx:e=>({gap:e.space.$0x5}),children:(0,r.BX)(et.l.Root,{onSubmit:f,children:[(0,r.BX)(d.Col,{gap:1,children:[(0,r.tZ)(d.Text,{colorScheme:"secondary",localizationKey:(0,d.localizationKeys)("userProfile.deletePage.messageLine1")}),(0,r.tZ)(d.Text,{colorScheme:"danger",localizationKey:(0,d.localizationKeys)("userProfile.deletePage.messageLine2")})]}),(0,r.tZ)(et.l.ControlRow,{elementId:p.id,children:(0,r.tZ)(et.l.PlainInput,{...p.props,ignorePasswordManager:!0})}),(0,r.tZ)(ei.A,{submitLabel:(0,d.localizationKeys)("userProfile.deletePage.confirm"),colorScheme:"danger",isDisabled:!v,onReset:t})]})})}),eW=()=>{let{close:e}=(0,K.XC)();return(0,r.tZ)(eN,{onSuccess:e,onReset:e})},eM=()=>(0,r.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.dangerSection.title"),id:"danger",sx:{alignItems:"center",[eh.mqu.md]:{alignItems:"flex-start"}},children:(0,r.BX)(C.a.Root,{children:[(0,r.tZ)(C.a.Closed,{value:"delete",children:(0,r.tZ)(Z.zd.Item,{id:"danger",sx:e=>({paddingLeft:e.space.$1}),children:(0,r.tZ)(C.a.Trigger,{value:"delete",children:(0,r.tZ)(Z.zd.Button,{id:"danger",variant:"ghost",colorScheme:"danger",localizationKey:(0,d.localizationKeys)("userProfile.start.dangerSection.deleteAccountButton")})})})}),(0,r.tZ)(C.a.Open,{value:"delete",children:(0,r.tZ)(C.a.Card,{variant:"destructive",children:(0,r.tZ)(eW,{})})})]})});var eU=i(5994),eY=i(73531);i(91634),i(98383);var eq=i(95878);let ej=e=>{let{code:t}=e;return(0,r.tZ)(d.Flex,{center:!0,sx:e=>({padding:"".concat(e.space.$1," ").concat(e.space.$4)}),children:(0,r.tZ)(d.Text,{children:t})})},eV=e=>{let{subtitle:t,backupCodes:i}=e,{applicationName:o}=(0,c.useEnvironment)().displayConfig,{user:l}=(0,g.aF)(),{print:a,printableProps:n}=(0,S.Qz)(),{onCopy:s,hasCopied:u}=(0,x.VP)((null==i?void 0:i.join(","))||"");if(!l)return null;let h=(0,eq.xC)(l);return i?(0,r.BX)(r.HY,{children:[(0,r.BX)(d.Col,{gap:1,children:[(0,r.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)("userProfile.backupCodePage.title__codelist"),variant:"subtitle"}),(0,r.tZ)(d.Text,{localizationKey:t,variant:"caption",colorScheme:"secondary"})]}),(0,r.BX)(d.Box,{sx:e=>({borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha100,borderRadius:e.radii.$lg}),children:[(0,r.tZ)(d.Grid,{gap:2,sx:e=>({gridTemplateColumns:"repeat(2, minmax(".concat(e.sizes.$12,", 1fr))"),padding:"".concat(e.space.$4," ").concat(e.space.$6)}),children:i.map((e,t)=>(0,r.tZ)(ej,{code:e},t))}),(0,r.BX)(d.Grid,{sx:e=>({borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100,gridTemplateColumns:"repeat(3, minmax(0, 1fr))",">:not([hidden])~:not([hidden])":{borderRightWidth:"0px",borderLeftWidth:"1px",borderStyle:"solid",borderColor:e.colors.$neutralAlpha100},">:first-child":{borderBottomLeftRadius:e.radii.$lg},">:last-child":{borderBottomRightRadius:e.radii.$lg}}),children:[(0,r.tZ)(d.Button,{variant:"ghost",sx:e=>({width:"100%",padding:"".concat(e.space.$2," 0"),borderRadius:0}),onClick:()=>{let e=document.createElement("a"),t=new Blob([function(e,t,i){let r=null==e?void 0:e.join("\n");return"These are your backup codes for ".concat(t," account ").concat(i,".\nStore them securely and keep them secret. Each code can only be used once.\n\n").concat(r)}(i,o,h)],{type:"text/plain"});e.href=URL.createObjectURL(t),e.download="".concat(o,"_backup_codes.txt"),document.body.appendChild(e),e.click()},children:(0,r.tZ)(d.Icon,{icon:eX.UW})}),(0,r.tZ)(d.Button,{variant:"ghost",sx:e=>({width:"100%",padding:"".concat(e.space.$2," 0"),borderRadius:0}),onClick:a,children:(0,r.tZ)(d.Icon,{icon:eX.Kh})}),(0,r.tZ)(d.Button,{variant:"ghost",onClick:s,sx:e=>({width:"100%",padding:"".concat(e.space.$2," 0"),borderRadius:0}),children:(0,r.tZ)(d.Icon,{icon:u?eX.Jr:eX.CK})})]})]}),(0,r.BX)(S.o4,{...n,children:[(0,r.BX)(d.Heading,{children:["Your backup codes for ",o," account ",h,":"]}),(0,r.tZ)(d.Col,{gap:2,children:i.map((e,t)=>(0,r.tZ)(ej,{code:e},t))})]})]}):null},eH=(0,l.withCardStateProvider)(e=>{let{onSuccess:t,onReset:i}=e,{user:a}=(0,g.aF)(),n=(0,l.useCardState)(),s=(0,g.WZ)(()=>null==a?void 0:a.createBackupCode()),[c,u]=o.useState(void 0);return(o.useEffect(()=>{!c&&a&&s().then(e=>u(e)).catch(e=>{if((0,eY.mh)(e))return i();(0,R.S3)(e,[],n.setError)})},[]),n.error)?(0,r.tZ)(er.Y,{headerTitle:(0,d.localizationKeys)("userProfile.backupCodePage.title")}):(0,r.tZ)(er.Y,{headerTitle:(0,d.localizationKeys)("userProfile.backupCodePage.title"),children:c?(0,r.BX)(r.HY,{children:[(0,r.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)("userProfile.backupCodePage.successMessage")}),(0,r.tZ)(eV,{subtitle:(0,d.localizationKeys)("userProfile.backupCodePage.subtitle__codelist"),backupCodes:c.codes}),(0,r.tZ)(ei.K,{children:(0,r.tZ)(d.Button,{autoFocus:!0,onClick:t,localizationKey:(0,d.localizationKeys)("userProfile.formButtonPrimary__finish"),elementDescriptor:d.descriptors.formButtonPrimary})})]}):(0,r.tZ)(eA.m,{})})}),eQ=(0,l.withCardStateProvider)(e=>{let{onSuccess:t,onReset:i}=e,o=(0,S.a2)();return(0,r.BX)(S.en,{...o.props,children:[(0,r.tZ)(eG,{onContinue:o.nextStep}),(0,r.tZ)(eH,{onSuccess:t,onReset:i})]})}),eG=e=>{let{onContinue:t}=e,{close:i}=(0,K.XC)();return(0,r.BX)(er.Y,{headerTitle:(0,d.localizationKeys)("userProfile.backupCodePage.title"),children:[(0,r.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)("userProfile.backupCodePage.infoText1")}),(0,r.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)("userProfile.backupCodePage.infoText2")}),(0,r.BX)(ei.K,{sx:{marginTop:0},children:[(0,r.tZ)(d.Button,{textVariant:"buttonSmall",onClick:t,localizationKey:(0,d.localizationKeys)("userProfile.formButtonPrimary__finish"),elementDescriptor:d.descriptors.formButtonPrimary}),(0,r.tZ)(d.Button,{variant:"ghost",onClick:i,localizationKey:(0,d.localizationKeys)("userProfile.formButtonReset"),elementDescriptor:d.descriptors.formButtonReset})]})]})};var eJ=i(53412),e0=i(67263);let e1=(0,l.withCardStateProvider)(e=>{var t,i,l,a;let{onReset:n,onSuccess:s}=e,u=o.useRef(),h=(0,S.a2)({defaultStep:2}),m=null===(t=(0,c.useEnvironment)().userSettings.attributes.backup_code)||void 0===t?void 0:t.enabled;return(0,r.BX)(S.en,{...h.props,children:[(0,r.tZ)(ea,{title:(0,d.localizationKeys)("userProfile.phoneNumberPage.title"),resourceRef:u,onSuccess:h.nextStep,onUseExistingNumberClick:()=>h.goToStep(2),onReset:n}),(0,r.tZ)(e3,{title:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.title"),resourceRef:u,onSuccess:()=>m?h.goToStep(3):s(),onReset:()=>h.goToStep(2)}),(0,r.tZ)(e4,{onSuccess:m?h.nextStep:s,onReset:n,onAddPhoneClick:()=>h.goToStep(0),onUnverifiedPhoneClick:e=>{u.current=e,h.goToStep(1)},title:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.title"),resourceRef:u}),m&&(0,r.tZ)(e0.I,{title:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.successTitle"),text:(null===(i=u.current)||void 0===i?void 0:i.backupCodes)&&(null===(l=u.current)||void 0===l?void 0:l.backupCodes.length)>0?[(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.successMessage1"),(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.successMessage2")]:[(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.successMessage1")],onFinish:n,contents:(0,r.tZ)(eV,{backupCodes:null===(a=u.current)||void 0===a?void 0:a.backupCodes})})]})}),e2=e=>{let{phone:t,onSuccess:i,onUnverifiedPhoneClick:o,resourceRef:a}=e,n=(0,l.useCardState)(),s=(0,g.WZ)(()=>t.setReservedForSecondFactor({reserved:!0})),{country:c}=(0,R.Z_)(t.phoneNumber),u=(0,R.L_)(t.phoneNumber),h=async()=>{if("verified"!==t.verification.status)return o(t);n.setLoading(t.id);try{await s(),a.current=t,i()}catch(e){(0,R.S3)(e,[],n.setError)}finally{n.setIdle()}};return(0,r.BX)(d.Button,{variant:"outline",colorScheme:"neutral",sx:{justifyContent:"start"},onClick:h,isLoading:n.loadingMetadata===t.id,isDisabled:n.isLoading,children:[c.iso.toUpperCase()," ",u]},t.id)},e3=e=>{var t,i,o;let{title:a,onSuccess:n,resourceRef:s,onReset:c}=e,u=(0,l.useCardState)(),h=s.current,m=(0,g.WZ)(()=>null==h?void 0:h.setReservedForSecondFactor({reserved:!0})),p=async()=>{u.setLoading(null==h?void 0:h.id);try{await m(),s.current=h,n()}catch(e){(0,R.S3)(e,[],u.setError)}finally{u.setIdle()}};return(0,r.tZ)(er.Y,{headerTitle:a,headerSubtitle:(0,d.localizationKeys)("userProfile.phoneNumberPage.verifySubtitle",{identifier:null===(t=s.current)||void 0===t?void 0:t.phoneNumber}),children:(0,r.tZ)(eo.H,{nextStep:()=>{p()},identification:s.current,identifier:null===(i=s.current)||void 0===i?void 0:i.phoneNumber,prepareVerification:null===(o=s.current)||void 0===o?void 0:o.prepareVerification,onReset:c})})},e4=e=>{let{onSuccess:t,onReset:i,title:o,onAddPhoneClick:l,onUnverifiedPhoneClick:a,resourceRef:n}=e,{user:s}=(0,g.aF)();if(!s)return null;let c=s.phoneNumbers.filter(e=>!e.reservedForSecondFactor);return(0,r.BX)(er.Y,{headerTitle:o,gap:1,children:[(0,r.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)(c.length?"userProfile.mfaPhoneCodePage.subtitle__availablePhoneNumbers":"userProfile.mfaPhoneCodePage.subtitle__unavailablePhoneNumbers"),colorScheme:"secondary"}),c.length>0&&(0,r.tZ)(d.Col,{gap:2,children:c.map(e=>(0,r.tZ)(e2,{onSuccess:t,phone:e,onUnverifiedPhoneClick:a,resourceRef:n},e.id))}),(0,r.BX)(ei.K,{sx:{flexDirection:"row",justifyContent:"space-between"},children:[(0,r.tZ)(eJ.h,{variant:"ghost","aria-label":"Add phone number",icon:(0,r.tZ)(d.Icon,{icon:eX.v3,sx:e=>({marginRight:e.space.$2})}),localizationKey:(0,d.localizationKeys)("userProfile.mfaPhoneCodePage.primaryButton__addPhoneNumber"),onClick:l}),(0,r.tZ)(d.Button,{variant:"ghost",localizationKey:(0,d.localizationKeys)("userProfile.formButtonReset"),onClick:i})]})]})};var e6=i(68864);let e7=(0,l.withCardStateProvider)(e=>{let{title:t,onSuccess:i,onReset:a}=e,{user:n}=(0,g.aF)(),s=(0,l.useCardState)(),c=(0,g.WZ)(()=>null==n?void 0:n.createTOTP()),{close:u}=(0,K.XC)(),[h,m]=o.useState(void 0),[p,v]=o.useState("qr");return(o.useEffect(()=>{n&&c().then(e=>m(e)).catch(e=>(0,eY.uX)(e)&&"reverification_cancelled"===e.code?u():(0,R.S3)(e,[],s.setError))},[]),s.error)?(0,r.tZ)(er.Y,{headerTitle:t}):(0,r.BX)(er.Y,{headerTitle:t,headerSubtitle:"qr"==p?(0,d.localizationKeys)("userProfile.mfaTOTPPage.authenticatorApp.infoText__ableToScan"):(0,d.localizationKeys)("userProfile.mfaTOTPPage.authenticatorApp.infoText__unableToScan"),children:[!h&&(0,r.tZ)(eA.m,{}),h&&(0,r.BX)(r.HY,{children:[(0,r.BX)(d.Col,{gap:4,children:["qr"==p&&(0,r.tZ)(S.s_,{justify:"center",url:h.uri||""}),"uri"==p&&(0,r.BX)(r.HY,{children:[(0,r.tZ)(d.Text,{colorScheme:"secondary",localizationKey:(0,d.localizationKeys)("userProfile.mfaTOTPPage.authenticatorApp.inputLabel__unableToScan1")}),(0,r.tZ)(e6.D,{value:h.secret}),(0,r.tZ)(d.Text,{colorScheme:"secondary",localizationKey:(0,d.localizationKeys)("userProfile.mfaTOTPPage.authenticatorApp.inputLabel__unableToScan2")}),(0,r.tZ)(e6.D,{value:h.uri})]})]}),(0,r.BX)(d.Flex,{justify:"between",align:"center",sx:{width:"100%"},children:["qr"==p&&(0,r.tZ)(d.Button,{variant:"link",textVariant:"buttonLarge",onClick:()=>v("uri"),localizationKey:(0,d.localizationKeys)("userProfile.mfaTOTPPage.authenticatorApp.buttonUnableToScan__nonPrimary")}),"uri"==p&&(0,r.tZ)(d.Button,{variant:"link",textVariant:"buttonLarge",onClick:()=>v("qr"),localizationKey:(0,d.localizationKeys)("userProfile.mfaTOTPPage.authenticatorApp.buttonAbleToScan__nonPrimary")}),(0,r.BX)(ei.K,{children:[(0,r.tZ)(d.Button,{onClick:i,localizationKey:(0,d.localizationKeys)("userProfile.formButtonPrimary__continue"),elementDescriptor:d.descriptors.formButtonPrimary}),(0,r.tZ)(d.Button,{variant:"ghost",onClick:a,localizationKey:(0,d.localizationKeys)("userProfile.formButtonReset"),elementDescriptor:d.descriptors.formButtonReset})]})]})]})]})});var e9=i(12667);let e5=(0,l.withCardStateProvider)(e=>{let{onSuccess:t,onReset:i,resourceRef:o}=e,{user:l}=(0,g.aF)(),a=(0,e9.e3)({onCodeEntryFinished:(e,t,i)=>{null==l||l.verifyTOTP({code:e}).then(e=>t(e)).catch(i)},onResolve:e=>{o.current=e,t()}});return(0,r.BX)(er.Y,{headerTitle:(0,d.localizationKeys)("userProfile.mfaTOTPPage.title"),children:[(0,r.tZ)(d.Col,{children:(0,r.tZ)(et.l.OTPInput,{...a,label:(0,d.localizationKeys)("userProfile.mfaTOTPPage.verifyTitle"),description:(0,d.localizationKeys)("userProfile.mfaTOTPPage.verifySubtitle")})}),(0,r.tZ)(ei.K,{sx:{marginTop:0},children:(0,r.tZ)(d.Button,{onClick:i,variant:"ghost",isDisabled:a.isLoading,localizationKey:(0,d.localizationKeys)("userProfile.formButtonReset"),elementDescriptor:d.descriptors.formButtonReset})})]})}),e8=(0,l.withCardStateProvider)(e=>{var t;let{onReset:i}=e,l=(0,S.a2)(),a=o.useRef();return(0,r.BX)(S.en,{...l.props,children:[(0,r.tZ)(e7,{title:(0,d.localizationKeys)("userProfile.mfaTOTPPage.title"),onSuccess:l.nextStep,onReset:i}),(0,r.tZ)(e5,{onSuccess:l.nextStep,onReset:i,resourceRef:a}),(0,r.tZ)(e0.I,{title:(0,d.localizationKeys)("userProfile.mfaTOTPPage.title"),text:(0,d.localizationKeys)("userProfile.mfaTOTPPage.successMessage"),onFinish:i,contents:(0,r.tZ)(eV,{subtitle:(0,d.localizationKeys)("userProfile.backupCodePage.successSubtitle"),backupCodes:null===(t=a.current)||void 0===t?void 0:t.backupCodes})})]})}),te=(0,l.withCardStateProvider)(e=>{let{onSuccess:t,onReset:i,selectedStrategy:a}=e,n=(0,l.useCardState)(),{userSettings:{attributes:s}}=(0,c.useEnvironment)(),{user:u}=(0,g.aF)();if(!u)return null;let h=(0,d.localizationKeys)("userProfile.mfaPage.title"),m=o.useMemo(()=>U(s,u),[]);return(o.useEffect(()=>{0===m.length&&n.setError("There are no second factors available to add")},[]),n.error)?(0,r.tZ)(er.Y,{headerTitle:h}):0!==m.length||a?(0,r.tZ)(tt,{onSuccess:t,onReset:i,method:a||m[0]}):null}),tt=e=>{let{method:t,onSuccess:i,onReset:o}=e;switch(t){case"phone_code":return(0,r.tZ)(e1,{onSuccess:i,onReset:o});case"totp":return(0,r.tZ)(e8,{onSuccess:i,onReset:o});case"backup_code":return(0,r.tZ)(eQ,{onSuccess:i,onReset:o});default:return null}},ti=()=>{let{close:e}=(0,K.XC)();return(0,r.tZ)(X,{onSuccess:e,onReset:e})},tr=e=>{let{close:t}=(0,K.XC)();return(0,r.tZ)(A,{onSuccess:t,onReset:t,...e})},to=()=>{let{close:e}=(0,K.XC)();return(0,r.tZ)(eH,{onSuccess:e,onReset:e})},tl=e=>{let{close:t}=(0,K.XC)();return(0,r.tZ)(te,{onSuccess:t,onReset:t,selectedStrategy:e.selectedStrategy})},ta=()=>{let{userSettings:{attributes:e}}=(0,c.useEnvironment)(),{user:t}=(0,g.aF)(),[i,l]=(0,o.useState)(null);if(!t)return null;let a=M(e),n=U(e,t),s=a.includes("totp")&&t.totpEnabled,u=a.includes("backup_code")&&t.backupCodeEnabled,h=t.phoneNumbers.filter(e=>"verified"===e.verification.status).filter(e=>e.reservedForSecondFactor).sort(W);return(0,r.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.mfaSection.title"),centered:!1,id:"mfa",children:(0,r.tZ)(C.a.Root,{value:i,onChange:l,children:(0,r.BX)(Z.zd.ItemList,{id:"mfa",children:[s&&(0,r.BX)(r.HY,{children:[(0,r.BX)(Z.zd.Item,{id:"mfa",hoverable:!0,children:[(0,r.BX)(d.Flex,{sx:e=>({gap:e.space.$2,alignItems:"center"}),children:[(0,r.tZ)(d.Icon,{icon:eX.hc,sx:e=>({color:e.colors.$neutralAlpha700})}),(0,r.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)("userProfile.start.mfaSection.totp.headerTitle")}),(0,r.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__default")})]}),(0,r.tZ)(tc,{})]}),(0,r.tZ)(C.a.Open,{value:"remove-totp",children:(0,r.tZ)(C.a.Card,{variant:"destructive",children:(0,r.tZ)(ti,{})})})]}),a.includes("phone_code")&&h.map(e=>{let t=!s&&e.defaultSecondFactor,i=e.id;return(0,r.BX)(o.Fragment,{children:[(0,r.BX)(Z.zd.Item,{id:"mfa",hoverable:!0,children:[(0,r.BX)(d.Flex,{sx:e=>({gap:e.space.$2,alignItems:"center"}),children:[(0,r.tZ)(d.Icon,{icon:eX.ij,sx:e=>({color:e.colors.$neutralAlpha700})}),(0,r.BX)(d.Text,{children:["SMS Code ",(0,r.tZ)(eU.q,{value:e.phoneNumber})]}),t&&(0,r.tZ)(d.Badge,{localizationKey:(0,d.localizationKeys)("badge__default")})]}),(0,r.tZ)(tn,{phone:e,showTOTP:s})]}),(0,r.tZ)(C.a.Open,{value:"remove-".concat(i),children:(0,r.tZ)(C.a.Card,{variant:"destructive",children:(0,r.tZ)(tr,{phoneId:i})})})]},i)}),u&&(0,r.BX)(r.HY,{children:[(0,r.BX)(Z.zd.Item,{id:"mfa",hoverable:!0,children:[(0,r.BX)(d.Flex,{sx:e=>({gap:e.space.$2,alignItems:"center"}),children:[(0,r.tZ)(d.Icon,{icon:eX.Qi,sx:e=>({color:e.colors.$neutralAlpha700})}),(0,r.tZ)(d.Text,{localizationKey:(0,d.localizationKeys)("userProfile.start.mfaSection.backupCodes.headerTitle")})]}),(0,r.tZ)(ts,{})]}),(0,r.tZ)(C.a.Open,{value:"regenerate",children:(0,r.tZ)(C.a.Card,{children:(0,r.tZ)(to,{})})})]}),(0,r.tZ)(td,{secondFactorsAvailableToAdd:n,onClick:()=>l(null)})]})})})},tn=e=>{let{phone:t,showTOTP:i}=e,{open:o}=(0,K.XC)(),a=(0,l.useCardState)(),n=t.id,s=[i||t.defaultSecondFactor?null:{label:(0,d.localizationKeys)("userProfile.start.mfaSection.phoneCode.actionLabel__setDefault"),onClick:()=>t.makeDefaultSecondFactor().catch(e=>(0,R.S3)(e,[],a.setError))},{label:(0,d.localizationKeys)("userProfile.start.mfaSection.phoneCode.destructiveActionLabel"),isDestructive:!0,onClick:()=>o("remove-".concat(n))}].filter(e=>null!==e);return(0,r.tZ)(b.a,{actions:s})},ts=()=>{let{open:e}=(0,K.XC)(),t=[{label:(0,d.localizationKeys)("userProfile.start.mfaSection.backupCodes.actionLabel__regenerate"),onClick:()=>e("regenerate")}].filter(e=>null!==e);return(0,r.tZ)(b.a,{actions:t})},tc=()=>{let{open:e}=(0,K.XC)(),t=[{label:(0,d.localizationKeys)("userProfile.start.mfaSection.totp.destructiveActionTitle"),isDestructive:!0,onClick:()=>e("remove-totp")}].filter(e=>null!==e);return(0,r.tZ)(b.a,{actions:t})},td=e=>{let{open:t}=(0,K.XC)(),{secondFactorsAvailableToAdd:i,onClick:l}=e,[a,n]=(0,o.useState)(),s=o.useMemo(()=>i.map(e=>"phone_code"===e?{icon:eX.ij,text:"SMS code",key:"phone_code"}:"totp"===e?{icon:eX.hc,text:"Authenticator application",key:"totp"}:"backup_code"===e?{icon:eX.Qi,text:"Backup code",key:"backup_code"}:null).filter(e=>null!==e),[i]);return(0,r.BX)(r.HY,{children:[i.length>0&&(0,r.tZ)(C.a.Closed,{value:"multi-factor",children:(0,r.tZ)(Z.zd.ActionMenu,{id:"mfa",triggerLocalizationKey:(0,d.localizationKeys)("userProfile.start.mfaSection.primaryButton"),onClick:l,children:s.map(e=>e&&(0,r.tZ)(Z.zd.ActionMenuItem,{id:e.key,localizationKey:e.text,leftIcon:e.icon,onClick:()=>{n(e.key),t("multi-factor")}},e.key))})}),(0,r.tZ)(C.a.Open,{value:"multi-factor",children:(0,r.tZ)(C.a.Card,{children:a&&(0,r.tZ)(tl,{selectedStrategy:a})})})]})},tu=e=>{let{close:t}=(0,K.XC)();return(0,r.tZ)(L,{onSuccess:t,onReset:t,...e})},th=e=>{let{close:t}=(0,K.XC)();return(0,r.tZ)(tm,{onSuccess:t,onReset:t,passkey:e.passkey})},tm=(0,l.withCardStateProvider)(e=>{let{onSuccess:t,onReset:i,passkey:o}=e,a=(0,l.useCardState)(),n=(0,R.Yp)("passkeyName",o.name||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__passkeyName"),isRequired:!0}),s=n.value.length>1&&o.name!==n.value,c=async e=>(e.preventDefault(),o.update({name:n.value}).then(t).catch(e=>(0,R.S3)(e,[n],a.setError)));return(0,r.tZ)(er.Y,{headerTitle:(0,d.localizationKeys)("userProfile.passkeyScreen.title__rename"),headerSubtitle:(0,d.localizationKeys)("userProfile.passkeyScreen.subtitle__rename"),children:(0,r.BX)(et.l.Root,{onSubmit:c,children:[(0,r.tZ)(et.l.ControlRow,{elementId:n.id,children:(0,r.tZ)(et.l.PlainInput,{...n.props,autoComplete:"off"})}),(0,r.tZ)(ei.A,{submitLabel:(0,d.localizationKeys)("userProfile.formButtonPrimary__save"),isDisabled:!s,onReset:i})]})})}),tp=()=>{let{user:e}=(0,g.aF)(),[t,i]=(0,o.useState)(null);return e?(0,r.tZ)(Z.zd.Root,{title:(0,d.localizationKeys)("userProfile.start.passkeysSection.title"),centered:!1,id:"passkeys",children:(0,r.tZ)(C.a.Root,{value:t,onChange:i,children:(0,r.BX)(Z.zd.ItemList,{id:"passkeys",children:[e.passkeys.map(e=>{let t=e.id;return(0,r.BX)(o.Fragment,{children:[(0,r.tZ)(tv,{...e},t),(0,r.tZ)(C.a.Open,{value:"remove-".concat(t),children:(0,r.tZ)(C.a.Card,{variant:"destructive",children:(0,r.tZ)(tu,{passkey:e})})}),(0,r.tZ)(C.a.Open,{value:"rename-".concat(t),children:(0,r.tZ)(C.a.Card,{children:(0,r.tZ)(th,{passkey:e})})})]},t)}),(0,r.tZ)(ty,{onClick:()=>i(null)})]})})}):null},tv=e=>(0,r.BX)(Z.zd.Item,{id:"passkeys",hoverable:!0,sx:{alignItems:"flex-start"},children:[(0,r.tZ)(tf,{...e}),(0,r.tZ)(tg,{passkey:e})]}),tf=e=>{let{name:t,createdAt:i,lastUsedAt:o}=e,{t:l}=(0,d.useLocalizations)();return(0,r.tZ)(d.Flex,{sx:e=>({width:"100%",overflow:"hidden",gap:e.space.$4,[eh.mqu.sm]:{gap:e.space.$2}}),children:(0,r.BX)(d.Col,{align:"start",gap:1,children:[(0,r.tZ)(d.Text,{children:t}),(0,r.BX)(d.Text,{colorScheme:"secondary",children:["Created: ",l((0,R.Qg)(i))]}),o&&(0,r.BX)(d.Text,{colorScheme:"secondary",children:["Last used: ",l((0,R.Qg)(o))]})]})})},tg=e=>{let{passkey:t}=e,{open:i}=(0,K.XC)(),o=t.id,l=[{label:(0,d.localizationKeys)("userProfile.start.passkeysSection.menuAction__rename"),onClick:()=>i("rename-".concat(o))},{label:(0,d.localizationKeys)("userProfile.start.passkeysSection.menuAction__destructive"),isDestructive:!0,onClick:()=>i("remove-".concat(o))}];return(0,r.tZ)(b.a,{actions:l})},ty=e=>{let{onClick:t}=e,i=(0,l.useCardState)(),{isSatellite:o}=(0,g.cL)(),{user:a}=(0,g.aF)(),n=(0,g.WZ)(()=>null==a?void 0:a.createPasskey()),s=async()=>{if(null==t||t(),a)try{await n()}catch(e){(0,R.S3)(e,[],i.setError)}};return o?null:(0,r.tZ)(Z.zd.ArrowButton,{id:"passkeys",localizationKey:(0,d.localizationKeys)("userProfile.start.passkeysSection.primaryButton"),onClick:s})},tP=(e,t)=>{let i=[];return e?i.push((0,d.localizationKeys)("userProfile.passwordPage.successMessage__update")):i.push((0,d.localizationKeys)("userProfile.passwordPage.successMessage__set")),t&&i.push((0,d.localizationKeys)("userProfile.passwordPage.successMessage__signOutOfOtherSessions")),i},tZ=(0,l.withCardStateProvider)(e=>{let{onSuccess:t,onReset:i}=e,{user:a}=(0,g.aF)(),n=(0,g.WZ)((e,t)=>e.updatePassword(...t));if(!a)return null;let{userSettings:{passwordSettings:s},authConfig:{reverification:u}}=(0,c.useEnvironment)(),{session:h}=(0,g.kP)(),m=a.passwordEnabled?(0,d.localizationKeys)("userProfile.passwordPage.title__update"):(0,d.localizationKeys)("userProfile.passwordPage.title__set"),p=(0,l.useCardState)(),v=a.enterpriseAccounts.some(e=>e.active),f=a.passwordEnabled&&!u,y=(0,o.useRef)({title:(0,d.localizationKeys)("userProfile.passwordPage.title__set")}),P=(0,R.Yp)("currentPassword","",{type:"password",label:(0,d.localizationKeys)("formFieldLabel__currentPassword"),isRequired:!0}),Z=(0,R.Yp)("newPassword","",{type:"password",label:(0,d.localizationKeys)("formFieldLabel__newPassword"),isRequired:!0,validatePassword:!0,buildErrorMessage:e=>(0,R.GM)(e,{t:K,locale:_,passwordSettings:s})}),b=(0,R.Yp)("confirmPassword","",{type:"password",label:(0,d.localizationKeys)("formFieldLabel__confirmPassword"),isRequired:!0}),z=(0,R.Yp)("signOutOfOtherSessions","",{type:"checkbox",label:(0,d.localizationKeys)("formFieldLabel__signOutOfOtherSessions"),defaultChecked:!0}),{setConfirmPasswordFeedback:S,isPasswordMatch:C}=(0,x.p5)({passwordField:Z,confirmPasswordField:b}),{t:K,locale:_}=(0,d.useLocalizations)(),B=(f?P.value&&C:C)&&Z.value&&b.value,w=async()=>{try{y.current={title:a.passwordEnabled?(0,d.localizationKeys)("userProfile.passwordPage.title__update"):(0,d.localizationKeys)("userProfile.passwordPage.title__set"),text:tP(a.passwordEnabled,!!z.checked)};let e={newPassword:Z.value,signOutOfOtherSessions:z.checked,currentPassword:f?P.value:void 0};await n(a,[e]),t()}catch(e){(0,R.S3)(e,[P,Z,b],p.setError)}};return(0,r.BX)(er.Y,{headerTitle:m,children:[v&&(0,r.tZ)(eg.e,{message:(0,d.localizationKeys)("userProfile.passwordPage.readonly")}),(0,r.BX)(et.l.Root,{onSubmit:w,onBlur:()=>{Z.value&&S(b.value)},children:[(0,r.tZ)("input",{readOnly:!0,"data-testid":"hidden-identifier",id:"identifier-field",name:"identifier",value:(null==h?void 0:h.publicUserData.identifier)||"",style:{display:"none"}}),f&&(0,r.tZ)(et.l.ControlRow,{elementId:P.id,children:(0,r.tZ)(et.l.PasswordInput,{...P.props,minLength:6,isRequired:!0,autoFocus:!0,isDisabled:v})}),(0,r.tZ)(et.l.ControlRow,{elementId:Z.id,children:(0,r.tZ)(et.l.PasswordInput,{...Z.props,minLength:6,isRequired:!0,autoFocus:!a.passwordEnabled,isDisabled:v})}),(0,r.tZ)(et.l.ControlRow,{elementId:b.id,children:(0,r.tZ)(et.l.PasswordInput,{...b.props,onChange:e=>(e.target.value&&S(e.target.value),b.props.onChange(e)),isRequired:!0,isDisabled:v})}),(0,r.tZ)(et.l.ControlRow,{elementId:z.id,children:(0,r.tZ)(et.l.Checkbox,{...z.props,description:(0,d.localizationKeys)("userProfile.passwordPage.checkboxInfoText__signOutOfOtherSessions"),isDisabled:v})}),v?(0,r.tZ)(ei.K,{children:(0,r.tZ)(et.l.ResetButton,{localizationKey:(0,d.localizationKeys)("userProfile.formButtonReset"),block:!1,onClick:i})}):(0,r.tZ)(ei.A,{isDisabled:!B,onReset:i})]})]})}),tb=()=>{let{close:e}=(0,K.XC)();return(0,r.tZ)(tZ,{onSuccess:e,onReset:e})},tz=()=>{let{user:e}=(0,g.aF)();if(!e)return null;let{passwordEnabled:t}=e;return(0,r.tZ)(Z.zd.Root,{centered:!1,title:(0,d.localizationKeys)("userProfile.start.passwordSection.title"),id:"password",children:(0,r.BX)(C.a.Root,{children:[(0,r.tZ)(C.a.Closed,{value:"edit",children:(0,r.BX)(Z.zd.Item,{id:"password",sx:e=>({paddingLeft:t?void 0:"0",paddingTop:e.space.$0x25,paddingBottom:e.space.$0x25}),children:[t&&(0,r.tZ)(d.Text,{variant:"h2",children:"••••••••••"}),(0,r.tZ)(C.a.Trigger,{value:"edit",children:(0,r.tZ)(Z.zd.Button,{id:"password",localizationKey:t?(0,d.localizationKeys)("userProfile.start.passwordSection.primaryButton__updatePassword"):(0,d.localizationKeys)("userProfile.start.passwordSection.primaryButton__setPassword")})})]})}),(0,r.tZ)(C.a.Open,{value:"edit",children:(0,r.tZ)(C.a.Card,{children:(0,r.tZ)(tb,{})})})]})})},tS=(0,l.withCardStateProvider)(()=>{var e;let{attributes:t,instanceIsPasswordBased:i}=(0,c.useEnvironment)().userSettings,o=(0,l.useCardState)(),{user:a}=(0,g.aF)(),n=null===(e=t.passkey)||void 0===e?void 0:e.enabled,s=M(t).length>0,u=null==a?void 0:a.deleteSelfEnabled;return(0,r.tZ)(d.Col,{elementDescriptor:d.descriptors.page,sx:e=>({gap:e.space.$8}),children:(0,r.BX)(d.Col,{elementDescriptor:d.descriptors.profilePage,elementId:d.descriptors.profilePage.setId("security"),children:[(0,r.tZ)(P.h.Root,{children:(0,r.tZ)(P.h.Title,{localizationKey:(0,d.localizationKeys)("userProfile.start.headerTitle__security"),sx:e=>({marginBottom:e.space.$4}),textVariant:"h2"})}),(0,r.tZ)(y.Z.Alert,{children:o.error}),i&&(0,r.tZ)(tz,{}),n&&(0,r.tZ)(tp,{}),s&&(0,r.tZ)(ta,{}),(0,r.tZ)(eL,{}),u&&(0,r.tZ)(eM,{})]})})}),tC=(0,o.lazy)(()=>Promise.all([i.e("507"),i.e("200"),i.e("573"),i.e("82")]).then(i.bind(i,41470)).then(e=>({default:e.BillingPage}))),tK=(0,o.lazy)(()=>Promise.all([i.e("200"),i.e("573"),i.e("616"),i.e("809")]).then(i.bind(i,37984)).then(e=>({default:e.APIKeysPage}))),tx=()=>{var e;let{pages:t}=(0,c.useUserProfileContext)(),{apiKeysSettings:i,commerceSettings:l}=(0,c.useEnvironment)(),a=t.routes[0].id===s.xM.ACCOUNT,n=t.routes[0].id===s.xM.SECURITY,d=t.routes[0].id===s.xM.BILLING,h=t.routes[0].id===s.xM.API_KEYS,m=null===(e=t.contents)||void 0===e?void 0:e.map((e,t)=>{let i=!a&&!n&&0===t;return(0,r.tZ)(u.Route,{index:i,path:i?void 0:e.url,children:(0,r.tZ)(p.O,{mount:e.mount,unmount:e.unmount})},"custom-page-".concat(e.url))});return(0,r.BX)(u.Switch,{children:[m,(0,r.BX)(u.Route,{children:[(0,r.tZ)(u.Route,{path:a?void 0:"account",children:(0,r.tZ)(u.Switch,{children:(0,r.tZ)(u.Route,{index:!0,children:(0,r.tZ)(ew,{})})})}),(0,r.tZ)(u.Route,{path:n?void 0:"security",children:(0,r.tZ)(u.Switch,{children:(0,r.tZ)(u.Route,{index:!0,children:(0,r.tZ)(tS,{})})})}),l.billing.enabled&&l.billing.hasPaidUserPlans&&(0,r.tZ)(u.Route,{path:d?void 0:"billing",children:(0,r.BX)(u.Switch,{children:[(0,r.tZ)(u.Route,{index:!0,children:(0,r.tZ)(o.Suspense,{fallback:"",children:(0,r.tZ)(tC,{})})}),(0,r.tZ)(u.Route,{path:"plans",children:(0,r.tZ)(o.Suspense,{fallback:"",children:(0,r.tZ)(eT,{})})}),(0,r.tZ)(u.Route,{path:"statement/:statementId",children:(0,r.tZ)(o.Suspense,{fallback:"",children:(0,r.tZ)(f.j,{})})}),(0,r.tZ)(u.Route,{path:"payment-attempt/:paymentAttemptId",children:(0,r.tZ)(o.Suspense,{fallback:"",children:(0,r.tZ)(v.r,{})})})]})}),i.enabled&&(0,r.tZ)(u.Route,{path:h?void 0:"api-keys",children:(0,r.tZ)(u.Switch,{children:(0,r.tZ)(u.Route,{index:!0,children:(0,r.tZ)(o.Suspense,{fallback:"",children:(0,r.tZ)(tK,{})})})})})]})]})};var tR=i(12786);let t_=(0,c.withCoreUserGuard)(()=>{let e=o.useRef(null);return(0,r.tZ)(n.P.Root,{children:(0,r.BX)(m,{contentRef:e,children:[(0,r.tZ)(a.ap,{navbarTitleLocalizationKey:(0,d.localizationKeys)("userProfile.navbar.title")}),(0,r.tZ)(n.P.Content,{contentRef:e,scrollBoxId:s.g8,children:(0,r.tZ)(tx,{})})]})})}),tB=(0,l.withCardStateProvider)(e=>(0,r.tZ)(d.Flow.Root,{flow:"userProfile",children:(0,r.tZ)(d.Flow.Part,{children:(0,r.BX)(u.Switch,{children:[(0,r.tZ)(u.Route,{path:"verify",children:(0,r.tZ)(tR.M,{})}),(0,r.tZ)(u.Route,{children:(0,r.tZ)(t_,{})})]})})})),tw=e=>{let t={...e,routing:"virtual",componentName:"UserProfile",mode:"modal"};return(0,r.tZ)(u.Route,{path:"user",children:(0,r.tZ)(c.UserProfileContext.Provider,{value:t,children:(0,r.tZ)("div",{children:(0,r.tZ)(tB,{...t})})})})}}}]);