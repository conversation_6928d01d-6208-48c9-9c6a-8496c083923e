"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["809"],{37984:function(e,l,t){t.r(l),t.d(l,{APIKeysPage:()=>h});var a=t(79109),i=t(83799),o=t(11576),r=t(39541),c=t(92654),n=t(15579),s=t(52010);let h=()=>{let{user:e}=(0,i.aF)(),{contentRef:l}=(0,n.jh)();return e?(0,a.BX)(r.Col,{gap:4,children:[(0,a.tZ)(c.h.Root,{children:(0,a.tZ)(c.h.Title,{localizationKey:(0,r.localizationKeys)("userProfile.apiKeysPage.title"),textVariant:"h2"})}),(0,a.tZ)(o.ApiKeysContext.Provider,{value:{componentName:"APIKeys"},children:(0,a.tZ)(s.APIKeysPage,{subject:e.id,revokeModalRoot:l})})]}):null}}}]);