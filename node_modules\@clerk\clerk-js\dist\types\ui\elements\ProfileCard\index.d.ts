export declare const ProfileCard: {
    Root: import("react").ForwardRefExoticComponent<Omit<Omit<Omit<import("../../primitives").FlexProps, "ref"> & import("react").RefAttributes<HTMLDivElement> & {
        elementDescriptor?: import("../../customizables/elementDescriptors").ElementDescriptor | Array<import("../../customizables/elementDescriptors").ElementDescriptor | undefined>;
        elementId?: import("../../customizables/elementDescriptors").ElementId;
        css?: never;
        sx?: import("../../styledSystem").ThemableCssProp;
    }, "ref"> & import("react").RefAttributes<HTMLDivElement>, "ref"> & import("react").RefAttributes<HTMLDivElement>>;
    Content: (props: {
        contentRef?: React.RefObject<HTMLDivElement>;
        scrollBoxId?: string;
    } & {
        children?: import("react").ReactNode | undefined;
    }) => import("@emotion/react/jsx-runtime").JSX.Element;
};
