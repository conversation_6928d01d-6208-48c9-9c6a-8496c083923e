import React from 'react';
import type { ThemableCssProp } from '../../styledSystem';
export declare const CardClerkAndPagesTag: React.MemoExoticComponent<React.ForwardRefExoticComponent<Omit<Omit<import("../../primitives").FlexProps, "ref"> & React.RefAttributes<HTMLDivElement> & {
    elementDescriptor?: import("../../customizables/elementDescriptors").ElementDescriptor | Array<import("../../customizables/elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("../../customizables/elementDescriptors").ElementId;
    css?: never;
    sx?: ThemableCssProp;
} & {
    withFooterPages?: boolean;
    devModeNoticeSx?: ThemableCssProp;
    outerSx?: ThemableCssProp;
    withDevOverlay?: boolean;
}, "ref"> & React.RefAttributes<HTMLDivElement>>>;
