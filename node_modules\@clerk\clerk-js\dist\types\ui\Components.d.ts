import type { __internal_CheckoutProps, __internal_PlanDetailsProps, __internal_UserVerificationProps, Appearance, Clerk, ClerkOptions, EnvironmentResource, SignInProps, SignUpProps, UserProfileProps, WaitlistProps } from '@clerk/types';
import React from 'react';
import type { AppearanceCascade } from './customizables/parseAppearance';
import type { ClerkComponentName } from './lazyModules/components';
import type { AvailableComponentProps } from './types';
/**
 * Avoid importing from `@clerk/shared/react` to prevent extra dependencies being added to the bundle.
 */
export declare const useSafeLayoutEffect: typeof React.useLayoutEffect;
export type ComponentControls = {
    mountComponent: (params: {
        appearanceKey: Uncapitalize<AppearanceCascade['appearanceKey']>;
        name: ClerkComponentName;
        node: HTMLDivElement;
        props?: AvailableComponentProps;
    }) => void;
    unmountComponent: (params: {
        node: HTMLDivElement;
    }) => void;
    updateProps: (params: {
        appearance?: Appearance | undefined;
        options?: ClerkOptions | undefined;
        node?: HTMLDivElement;
        props?: unknown;
    }) => void;
    openModal: <T extends 'googleOneTap' | 'signIn' | 'signUp' | 'userProfile' | 'organizationProfile' | 'createOrganization' | 'userVerification' | 'waitlist' | 'blankCaptcha'>(modal: T, props: T extends 'signIn' ? SignInProps : T extends 'signUp' ? SignUpProps : T extends 'userVerification' ? __internal_UserVerificationProps : T extends 'waitlist' ? WaitlistProps : UserProfileProps) => void;
    closeModal: (modal: 'googleOneTap' | 'signIn' | 'signUp' | 'userProfile' | 'organizationProfile' | 'createOrganization' | 'userVerification' | 'waitlist' | 'blankCaptcha', options?: {
        notify?: boolean;
    }) => void;
    openDrawer: <T extends 'checkout' | 'planDetails'>(drawer: T, props: T extends 'checkout' ? __internal_CheckoutProps : T extends 'planDetails' ? __internal_PlanDetailsProps : never) => void;
    closeDrawer: (drawer: 'checkout' | 'planDetails', options?: {
        notify?: boolean;
    }) => void;
    prefetch: (component: 'organizationSwitcher') => void;
    mountImpersonationFab: () => void;
};
export declare const mountComponentRenderer: (clerk: Clerk, environment: EnvironmentResource, options: ClerkOptions) => {
    ensureMounted: (opts?: {
        preloadHint: ClerkComponentName;
    }) => Promise<ComponentControls>;
};
export type MountComponentRenderer = typeof mountComponentRenderer;
