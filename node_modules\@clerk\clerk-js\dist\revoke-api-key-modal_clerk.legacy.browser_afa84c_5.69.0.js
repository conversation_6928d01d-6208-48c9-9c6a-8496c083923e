"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["642"],{3250:function(e,t,o){o.r(t),o.d(t,{RevokeAPIKeyConfirmationModal:()=>k});var i=o(79109),n=o(83799),l=o(74126),r=o(44455),a=o(70431),s=o(19460),c=o(68487),d=o(56459),p=o(91085),h=o(77623);let k=e=>{let{subject:t,isOpen:o,onOpen:k,onClose:u,apiKeyId:m,apiKeyName:b,modalRoot:y}=e,f=(0,n.cL)(),{mutate:v}=(0,l.kY)(),C=async e=>{e.preventDefault(),m&&(await f.apiKeys.revoke({apiKeyID:m}),v({key:"api-keys",subject:t}),u())},g=(0,h.Yp)("apiKeyRevokeConfirmation","",{type:"text",label:'Type "Revoke" to confirm',placeholder:"Revoke",isRequired:!0}),R="Revoke"===g.value;return o?(0,i.tZ)(d.Modal,{handleOpen:k,handleClose:u,canCloseModal:!1,portalRoot:y,containerSx:[{alignItems:"center"},y?e=>({position:"absolute",right:0,bottom:0,backgroundColor:"inherit",backdropFilter:"blur(".concat(e.sizes.$2,")"),display:"flex",justifyContent:"center",minHeight:"100%",height:"100%",width:"100%"}):{}],children:(0,i.tZ)(r.Z.Root,{role:"alertdialog",children:(0,i.tZ)(r.Z.Content,{sx:e=>({textAlign:"left",padding:"".concat(e.sizes.$4," ").concat(e.sizes.$5," ").concat(e.sizes.$4," ").concat(e.sizes.$6)}),children:(0,i.tZ)(c.Y,{headerTitle:(0,p.u1)("apiKeys.revokeConfirmation.formTitle",{apiKeyName:b}),headerSubtitle:(0,p.u1)("apiKeys.revokeConfirmation.formHint"),children:(0,i.BX)(a.l.Root,{onSubmit:C,children:[(0,i.tZ)(a.l.ControlRow,{elementId:g.id,children:(0,i.tZ)(a.l.PlainInput,{...g.props})}),(0,i.tZ)(s.A,{submitLabel:(0,p.u1)("apiKeys.revokeConfirmation.formButtonPrimary__revoke"),colorScheme:"danger",isDisabled:!R,onReset:u})]})})})})}):null}}}]);