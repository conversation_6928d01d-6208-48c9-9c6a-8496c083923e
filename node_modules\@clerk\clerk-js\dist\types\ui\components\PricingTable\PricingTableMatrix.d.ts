import type { CommercePlanResource, CommerceSubscriptionPlanPeriod } from '@clerk/types';
import * as React from 'react';
interface PricingTableMatrixProps {
    plans: CommercePlanResource[] | undefined;
    highlightedPlan?: CommercePlanResource['slug'];
    planPeriod: CommerceSubscriptionPlanPeriod;
    setPlanPeriod: (val: CommerceSubscriptionPlanPeriod) => void;
    onSelect: (plan: CommercePlanResource, event?: React.MouseEvent<HTMLElement>) => void;
}
export declare function PricingTableMatrix({ plans, planPeriod, setPlanPeriod, onSelect, highlightedPlan, }: PricingTableMatrixProps): import("@emotion/react/jsx-runtime").JSX.Element | null;
export {};
