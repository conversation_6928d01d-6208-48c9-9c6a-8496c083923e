import type { CardActionId } from '@clerk/types';
import React from 'react';
import { Flex } from '../../customizables';
import type { PropsOfComponent } from '../../styledSystem';
import { RouterLink } from '../RouterLink';
type CardActionProps = Omit<PropsOfComponent<typeof Flex>, 'elementId'> & {
    elementId?: CardActionId;
};
export declare const CardAction: (props: CardActionProps) => JSX.Element;
export declare const CardActionText: (props: React.PropsWithChildren<any>) => JSX.Element;
export declare const CardActionLink: (props: PropsOfComponent<typeof RouterLink>) => JSX.Element;
export {};
