import type { TokenResource } from '@clerk/types';
export declare const events: {
    readonly TokenUpdate: "token:update";
    readonly UserSignOut: "user:signOut";
    readonly EnvironmentUpdate: "environment:update";
    readonly SessionTokenResolved: "session:tokenResolved";
};
type TokenUpdatePayload = {
    token: TokenResource | null;
};
type InternalEvents = {
    [events.TokenUpdate]: TokenUpdatePayload;
    [events.UserSignOut]: null;
    [events.EnvironmentUpdate]: null;
    [events.SessionTokenResolved]: null;
};
export declare const eventBus: {
    on: <Event extends keyof InternalEvents>(event: Event, handler: (payload: InternalEvents[Event]) => void, opts?: {
        notify?: boolean;
    }) => void;
    prioritizedOn: <Event extends keyof InternalEvents>(event: Event, handler: (payload: InternalEvents[Event]) => void) => void;
    emit: <Event extends keyof InternalEvents>(event: Event, payload: InternalEvents[Event]) => void;
    off: <Event extends keyof InternalEvents>(event: Event, handler?: ((payload: InternalEvents[Event]) => void) | undefined) => void;
    prioritizedOff: <Event extends keyof InternalEvents>(event: Event, handler?: ((payload: InternalEvents[Event]) => void) | undefined) => void;
    internal: {
        retrieveListeners: <Event extends keyof InternalEvents>(event: Event) => Array<(...args: any[]) => void>;
    };
};
export {};
