import type { SessionVerificationLevel } from '@clerk/types';
declare const useUserVerificationSessionKey: () => {
    level: SessionVerificationLevel;
};
declare const useUserVerificationSession: () => {
    setCache: (state: import("../../hooks").State<import("@clerk/types").SessionVerificationResource, any> | ((params: import("../../hooks").State<import("@clerk/types").SessionVerificationResource, any>) => import("../../hooks").State<import("@clerk/types").SessionVerificationResource, any>)) => void;
    invalidate: () => void;
    revalidate: () => void;
    data?: import("@clerk/types").SessionVerificationResource | null | undefined;
    error?: any;
    isLoading?: boolean | undefined;
    isValidating?: boolean | undefined;
    cachedAt?: number;
};
declare function withUserVerificationSessionGuard<P>(Component: React.ComponentType<P>): React.ComponentType<P>;
export { useUserVerificationSessionKey, useUserVerificationSession, withUserVerificationSessionGuard };
