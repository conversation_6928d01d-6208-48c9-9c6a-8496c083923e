import type { DeepPartial } from '@clerk/types';
export declare const useParsedLocalizationResource: () => DeepPartial<{
    locale: string;
    maintenanceMode: import("@clerk/types").LocalizationValue;
    roles: {
        [r: string]: import("@clerk/types").LocalizationValue;
    };
    socialButtonsBlockButton: import("@clerk/types").LocalizationValue;
    socialButtonsBlockButtonManyInView: `${string}{{provider|titleize}}${string}`;
    dividerText: import("@clerk/types").LocalizationValue;
    formFieldLabel__emailAddress: import("@clerk/types").LocalizationValue;
    formFieldLabel__emailAddresses: import("@clerk/types").LocalizationValue;
    formFieldLabel__phoneNumber: import("@clerk/types").LocalizationValue;
    formFieldLabel__username: import("@clerk/types").LocalizationValue;
    formFieldLabel__emailAddress_username: import("@clerk/types").LocalizationValue;
    formFieldLabel__password: import("@clerk/types").LocalizationValue;
    formFieldLabel__currentPassword: import("@clerk/types").LocalizationValue;
    formFieldLabel__newPassword: import("@clerk/types").LocalizationValue;
    formFieldLabel__confirmPassword: import("@clerk/types").LocalizationValue;
    formFieldLabel__signOutOfOtherSessions: import("@clerk/types").LocalizationValue;
    formFieldLabel__automaticInvitations: import("@clerk/types").LocalizationValue;
    formFieldLabel__firstName: import("@clerk/types").LocalizationValue;
    formFieldLabel__lastName: import("@clerk/types").LocalizationValue;
    formFieldLabel__backupCode: import("@clerk/types").LocalizationValue;
    formFieldLabel__organizationName: import("@clerk/types").LocalizationValue;
    formFieldLabel__organizationSlug: import("@clerk/types").LocalizationValue;
    formFieldLabel__organizationDomain: import("@clerk/types").LocalizationValue;
    formFieldLabel__organizationDomainEmailAddress: import("@clerk/types").LocalizationValue;
    formFieldLabel__organizationDomainEmailAddressDescription: import("@clerk/types").LocalizationValue;
    formFieldLabel__organizationDomainDeletePending: import("@clerk/types").LocalizationValue;
    formFieldLabel__confirmDeletion: import("@clerk/types").LocalizationValue;
    formFieldLabel__role: import("@clerk/types").LocalizationValue;
    formFieldLabel__passkeyName: import("@clerk/types").LocalizationValue;
    formFieldLabel__apiKeyName: import("@clerk/types").LocalizationValue;
    formFieldLabel__apiKeyDescription: import("@clerk/types").LocalizationValue;
    formFieldLabel__apiKeyExpiration: import("@clerk/types").LocalizationValue;
    formFieldLabel__apiKeyExpirationDate: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__emailAddress: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__emailAddresses: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__phoneNumber: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__username: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__emailAddress_username: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__password: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__firstName: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__lastName: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__backupCode: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__organizationName: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__organizationSlug: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__organizationDomain: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__organizationDomainEmailAddress: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__confirmDeletionUserAccount: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__apiKeyName: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__apiKeyDescription: import("@clerk/types").LocalizationValue;
    formFieldInputPlaceholder__apiKeyExpirationDate: import("@clerk/types").LocalizationValue;
    formFieldError__notMatchingPasswords: import("@clerk/types").LocalizationValue;
    formFieldError__matchingPasswords: import("@clerk/types").LocalizationValue;
    formFieldError__verificationLinkExpired: import("@clerk/types").LocalizationValue;
    formFieldAction__forgotPassword: import("@clerk/types").LocalizationValue;
    formFieldHintText__optional: import("@clerk/types").LocalizationValue;
    formFieldHintText__slug: import("@clerk/types").LocalizationValue;
    formButtonPrimary: import("@clerk/types").LocalizationValue;
    formButtonPrimary__verify: import("@clerk/types").LocalizationValue;
    signInEnterPasswordTitle: import("@clerk/types").LocalizationValue;
    backButton: import("@clerk/types").LocalizationValue;
    footerActionLink__useAnotherMethod: import("@clerk/types").LocalizationValue;
    footerActionLink__alternativePhoneCodeProvider: import("@clerk/types").LocalizationValue;
    badge__primary: import("@clerk/types").LocalizationValue;
    badge__thisDevice: import("@clerk/types").LocalizationValue;
    badge__userDevice: import("@clerk/types").LocalizationValue;
    badge__otherImpersonatorDevice: import("@clerk/types").LocalizationValue;
    badge__default: import("@clerk/types").LocalizationValue;
    badge__unverified: import("@clerk/types").LocalizationValue;
    badge__requiresAction: import("@clerk/types").LocalizationValue;
    badge__you: import("@clerk/types").LocalizationValue;
    badge__currentPlan: import("@clerk/types").LocalizationValue;
    badge__upcomingPlan: import("@clerk/types").LocalizationValue;
    badge__activePlan: import("@clerk/types").LocalizationValue;
    badge__startsAt: import("@clerk/types").LocalizationValue;
    badge__endsAt: import("@clerk/types").LocalizationValue;
    badge__expired: import("@clerk/types").LocalizationValue;
    badge__canceledEndsAt: import("@clerk/types").LocalizationValue;
    badge__renewsAt: import("@clerk/types").LocalizationValue;
    footerPageLink__help: import("@clerk/types").LocalizationValue;
    footerPageLink__privacy: import("@clerk/types").LocalizationValue;
    footerPageLink__terms: import("@clerk/types").LocalizationValue;
    paginationButton__previous: import("@clerk/types").LocalizationValue;
    paginationButton__next: import("@clerk/types").LocalizationValue;
    paginationRowText__displaying: import("@clerk/types").LocalizationValue;
    paginationRowText__of: import("@clerk/types").LocalizationValue;
    membershipRole__admin: import("@clerk/types").LocalizationValue;
    membershipRole__basicMember: import("@clerk/types").LocalizationValue;
    membershipRole__guestMember: import("@clerk/types").LocalizationValue;
    commerce: {
        month: import("@clerk/types").LocalizationValue;
        year: import("@clerk/types").LocalizationValue;
        free: import("@clerk/types").LocalizationValue;
        getStarted: import("@clerk/types").LocalizationValue;
        manage: import("@clerk/types").LocalizationValue;
        manageSubscription: import("@clerk/types").LocalizationValue;
        cancelSubscription: import("@clerk/types").LocalizationValue;
        keepSubscription: import("@clerk/types").LocalizationValue;
        reSubscribe: import("@clerk/types").LocalizationValue;
        subscribe: import("@clerk/types").LocalizationValue;
        switchPlan: import("@clerk/types").LocalizationValue;
        switchToMonthly: import("@clerk/types").LocalizationValue;
        switchToAnnual: import("@clerk/types").LocalizationValue;
        billedAnnually: import("@clerk/types").LocalizationValue;
        billedMonthlyOnly: import("@clerk/types").LocalizationValue;
        alwaysFree: import("@clerk/types").LocalizationValue;
        accountFunds: import("@clerk/types").LocalizationValue;
        defaultFreePlanActive: import("@clerk/types").LocalizationValue;
        viewFeatures: import("@clerk/types").LocalizationValue;
        seeAllFeatures: import("@clerk/types").LocalizationValue;
        availableFeatures: import("@clerk/types").LocalizationValue;
        subtotal: import("@clerk/types").LocalizationValue;
        credit: import("@clerk/types").LocalizationValue;
        creditRemainder: import("@clerk/types").LocalizationValue;
        totalDue: import("@clerk/types").LocalizationValue;
        totalDueToday: import("@clerk/types").LocalizationValue;
        pastDue: import("@clerk/types").LocalizationValue;
        paymentMethods: import("@clerk/types").LocalizationValue;
        addPaymentMethod: import("@clerk/types").LocalizationValue;
        pay: import("@clerk/types").LocalizationValue;
        cancelSubscriptionTitle: import("@clerk/types").LocalizationValue;
        cancelSubscriptionNoCharge: import("@clerk/types").LocalizationValue;
        cancelSubscriptionAccessUntil: import("@clerk/types").LocalizationValue;
        popular: import("@clerk/types").LocalizationValue;
        monthly: import("@clerk/types").LocalizationValue;
        annually: import("@clerk/types").LocalizationValue;
        cannotSubscribeMonthly: import("@clerk/types").LocalizationValue;
        pricingTable: {
            billingCycle: import("@clerk/types").LocalizationValue;
            included: import("@clerk/types").LocalizationValue;
        };
        paymentSource: {
            dev: {
                testCardInfo: import("@clerk/types").LocalizationValue;
                developmentMode: import("@clerk/types").LocalizationValue;
                cardNumber: import("@clerk/types").LocalizationValue;
                expirationDate: import("@clerk/types").LocalizationValue;
                cvcZip: import("@clerk/types").LocalizationValue;
                anyNumbers: import("@clerk/types").LocalizationValue;
            };
            applePayDescription: {
                monthly: import("@clerk/types").LocalizationValue;
                annual: import("@clerk/types").LocalizationValue;
            };
        };
        checkout: {
            title: import("@clerk/types").LocalizationValue;
            title__paymentSuccessful: import("@clerk/types").LocalizationValue;
            title__subscriptionSuccessful: import("@clerk/types").LocalizationValue;
            description__paymentSuccessful: import("@clerk/types").LocalizationValue;
            description__subscriptionSuccessful: import("@clerk/types").LocalizationValue;
            lineItems: {
                title__totalPaid: import("@clerk/types").LocalizationValue;
                title__paymentMethod: import("@clerk/types").LocalizationValue;
                title__statementId: import("@clerk/types").LocalizationValue;
                title__subscriptionBegins: import("@clerk/types").LocalizationValue;
            };
            emailForm: {
                title: import("@clerk/types").LocalizationValue;
                subtitle: import("@clerk/types").LocalizationValue;
            };
            downgradeNotice: import("@clerk/types").LocalizationValue;
            pastDueNotice: import("@clerk/types").LocalizationValue;
            perMonth: import("@clerk/types").LocalizationValue;
        };
    };
    signUp: {
        start: {
            title: import("@clerk/types").LocalizationValue;
            titleCombined: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            subtitleCombined: import("@clerk/types").LocalizationValue;
            actionText: import("@clerk/types").LocalizationValue;
            actionLink: import("@clerk/types").LocalizationValue;
            actionLink__use_phone: import("@clerk/types").LocalizationValue;
            actionLink__use_email: import("@clerk/types").LocalizationValue;
            alternativePhoneCodeProvider: {
                actionLink: import("@clerk/types").LocalizationValue;
                label: import("@clerk/types").LocalizationValue;
                subtitle: import("@clerk/types").LocalizationValue;
                title: import("@clerk/types").LocalizationValue;
            };
        };
        emailLink: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            formTitle: import("@clerk/types").LocalizationValue;
            formSubtitle: import("@clerk/types").LocalizationValue;
            resendButton: import("@clerk/types").LocalizationValue;
            verified: {
                title: import("@clerk/types").LocalizationValue;
            };
            loading: {
                title: import("@clerk/types").LocalizationValue;
            };
            verifiedSwitchTab: {
                title: import("@clerk/types").LocalizationValue;
                subtitle: import("@clerk/types").LocalizationValue;
                subtitleNewTab: import("@clerk/types").LocalizationValue;
            };
            clientMismatch: {
                title: import("@clerk/types").LocalizationValue;
                subtitle: import("@clerk/types").LocalizationValue;
            };
        };
        emailCode: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            formTitle: import("@clerk/types").LocalizationValue;
            formSubtitle: import("@clerk/types").LocalizationValue;
            resendButton: import("@clerk/types").LocalizationValue;
        };
        phoneCode: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            formTitle: import("@clerk/types").LocalizationValue;
            formSubtitle: import("@clerk/types").LocalizationValue;
            resendButton: import("@clerk/types").LocalizationValue;
        };
        alternativePhoneCodeProvider: {
            formSubtitle: import("@clerk/types").LocalizationValue;
            formTitle: import("@clerk/types").LocalizationValue;
            resendButton: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            title: import("@clerk/types").LocalizationValue;
        };
        continue: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            actionText: import("@clerk/types").LocalizationValue;
            actionLink: import("@clerk/types").LocalizationValue;
        };
        restrictedAccess: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            subtitleWaitlist: import("@clerk/types").LocalizationValue;
            actionLink: import("@clerk/types").LocalizationValue;
            actionText: import("@clerk/types").LocalizationValue;
            blockButton__emailSupport: import("@clerk/types").LocalizationValue;
            blockButton__joinWaitlist: import("@clerk/types").LocalizationValue;
        };
        legalConsent: {
            continue: {
                title: import("@clerk/types").LocalizationValue;
                subtitle: import("@clerk/types").LocalizationValue;
            };
            checkbox: {
                label__termsOfServiceAndPrivacyPolicy: import("@clerk/types").LocalizationValue;
                label__onlyPrivacyPolicy: import("@clerk/types").LocalizationValue;
                label__onlyTermsOfService: import("@clerk/types").LocalizationValue;
            };
        };
    };
    signIn: {
        start: {
            title: import("@clerk/types").LocalizationValue;
            titleCombined: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            subtitleCombined: import("@clerk/types").LocalizationValue;
            actionText: import("@clerk/types").LocalizationValue;
            actionLink: import("@clerk/types").LocalizationValue;
            actionLink__use_email: import("@clerk/types").LocalizationValue;
            actionLink__use_phone: import("@clerk/types").LocalizationValue;
            actionLink__use_username: import("@clerk/types").LocalizationValue;
            actionLink__use_email_username: import("@clerk/types").LocalizationValue;
            actionLink__use_passkey: import("@clerk/types").LocalizationValue;
            actionText__join_waitlist: import("@clerk/types").LocalizationValue;
            actionLink__join_waitlist: import("@clerk/types").LocalizationValue;
            alternativePhoneCodeProvider: {
                actionLink: import("@clerk/types").LocalizationValue;
                label: import("@clerk/types").LocalizationValue;
                subtitle: import("@clerk/types").LocalizationValue;
                title: import("@clerk/types").LocalizationValue;
            };
        };
        password: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            actionLink: import("@clerk/types").LocalizationValue;
        };
        passwordPwned: {
            title: import("@clerk/types").LocalizationValue;
        };
        passkey: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
        };
        forgotPasswordAlternativeMethods: {
            title: import("@clerk/types").LocalizationValue;
            label__alternativeMethods: import("@clerk/types").LocalizationValue;
            blockButton__resetPassword: import("@clerk/types").LocalizationValue;
        };
        forgotPassword: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            subtitle_email: import("@clerk/types").LocalizationValue;
            subtitle_phone: import("@clerk/types").LocalizationValue;
            formTitle: import("@clerk/types").LocalizationValue;
            resendButton: import("@clerk/types").LocalizationValue;
        };
        resetPassword: {
            title: import("@clerk/types").LocalizationValue;
            formButtonPrimary: import("@clerk/types").LocalizationValue;
            successMessage: import("@clerk/types").LocalizationValue;
            requiredMessage: import("@clerk/types").LocalizationValue;
        };
        resetPasswordMfa: {
            detailsLabel: import("@clerk/types").LocalizationValue;
        };
        emailCode: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            formTitle: import("@clerk/types").LocalizationValue;
            resendButton: import("@clerk/types").LocalizationValue;
        };
        emailLink: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            formTitle: import("@clerk/types").LocalizationValue;
            formSubtitle: import("@clerk/types").LocalizationValue;
            resendButton: import("@clerk/types").LocalizationValue;
            unusedTab: {
                title: import("@clerk/types").LocalizationValue;
            };
            verified: {
                title: import("@clerk/types").LocalizationValue;
                subtitle: import("@clerk/types").LocalizationValue;
            };
            verifiedSwitchTab: {
                subtitle: import("@clerk/types").LocalizationValue;
                titleNewTab: import("@clerk/types").LocalizationValue;
                subtitleNewTab: import("@clerk/types").LocalizationValue;
            };
            loading: {
                title: import("@clerk/types").LocalizationValue;
                subtitle: import("@clerk/types").LocalizationValue;
            };
            failed: {
                title: import("@clerk/types").LocalizationValue;
                subtitle: import("@clerk/types").LocalizationValue;
            };
            expired: {
                title: import("@clerk/types").LocalizationValue;
                subtitle: import("@clerk/types").LocalizationValue;
            };
            clientMismatch: {
                title: import("@clerk/types").LocalizationValue;
                subtitle: import("@clerk/types").LocalizationValue;
            };
        };
        phoneCode: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            formTitle: import("@clerk/types").LocalizationValue;
            resendButton: import("@clerk/types").LocalizationValue;
        };
        alternativePhoneCodeProvider: {
            formTitle: import("@clerk/types").LocalizationValue;
            resendButton: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            title: import("@clerk/types").LocalizationValue;
        };
        phoneCodeMfa: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            formTitle: import("@clerk/types").LocalizationValue;
            resendButton: import("@clerk/types").LocalizationValue;
        };
        totpMfa: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            formTitle: import("@clerk/types").LocalizationValue;
        };
        backupCodeMfa: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
        };
        alternativeMethods: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            actionLink: import("@clerk/types").LocalizationValue;
            actionText: import("@clerk/types").LocalizationValue;
            blockButton__emailLink: import("@clerk/types").LocalizationValue;
            blockButton__emailCode: import("@clerk/types").LocalizationValue;
            blockButton__phoneCode: import("@clerk/types").LocalizationValue;
            blockButton__password: import("@clerk/types").LocalizationValue;
            blockButton__passkey: import("@clerk/types").LocalizationValue;
            blockButton__totp: import("@clerk/types").LocalizationValue;
            blockButton__backupCode: import("@clerk/types").LocalizationValue;
            getHelp: {
                title: import("@clerk/types").LocalizationValue;
                content: import("@clerk/types").LocalizationValue;
                blockButton__emailSupport: import("@clerk/types").LocalizationValue;
            };
        };
        noAvailableMethods: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            message: import("@clerk/types").LocalizationValue;
        };
        accountSwitcher: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            action__addAccount: import("@clerk/types").LocalizationValue;
            action__signOutAll: import("@clerk/types").LocalizationValue;
        };
    };
    reverification: {
        password: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            actionLink: import("@clerk/types").LocalizationValue;
        };
        emailCode: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            formTitle: import("@clerk/types").LocalizationValue;
            resendButton: import("@clerk/types").LocalizationValue;
        };
        phoneCode: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            formTitle: import("@clerk/types").LocalizationValue;
            resendButton: import("@clerk/types").LocalizationValue;
        };
        phoneCodeMfa: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            formTitle: import("@clerk/types").LocalizationValue;
            resendButton: import("@clerk/types").LocalizationValue;
        };
        totpMfa: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            formTitle: import("@clerk/types").LocalizationValue;
        };
        backupCodeMfa: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
        };
        passkey: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            blockButton__passkey: import("@clerk/types").LocalizationValue;
        };
        alternativeMethods: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            actionLink: import("@clerk/types").LocalizationValue;
            actionText: import("@clerk/types").LocalizationValue;
            blockButton__emailCode: import("@clerk/types").LocalizationValue;
            blockButton__phoneCode: import("@clerk/types").LocalizationValue;
            blockButton__password: import("@clerk/types").LocalizationValue;
            blockButton__totp: import("@clerk/types").LocalizationValue;
            blockButton__passkey: import("@clerk/types").LocalizationValue;
            blockButton__backupCode: import("@clerk/types").LocalizationValue;
            getHelp: {
                title: import("@clerk/types").LocalizationValue;
                content: import("@clerk/types").LocalizationValue;
                blockButton__emailSupport: import("@clerk/types").LocalizationValue;
            };
        };
        noAvailableMethods: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            message: import("@clerk/types").LocalizationValue;
        };
    };
    userProfile: {
        mobileButton__menu: import("@clerk/types").LocalizationValue;
        formButtonPrimary__continue: import("@clerk/types").LocalizationValue;
        formButtonPrimary__save: import("@clerk/types").LocalizationValue;
        formButtonPrimary__finish: import("@clerk/types").LocalizationValue;
        formButtonPrimary__remove: import("@clerk/types").LocalizationValue;
        formButtonPrimary__add: import("@clerk/types").LocalizationValue;
        formButtonReset: import("@clerk/types").LocalizationValue;
        navbar: {
            title: import("@clerk/types").LocalizationValue;
            description: import("@clerk/types").LocalizationValue;
            account: import("@clerk/types").LocalizationValue;
            security: import("@clerk/types").LocalizationValue;
            billing: import("@clerk/types").LocalizationValue;
            apiKeys: import("@clerk/types").LocalizationValue;
        };
        start: {
            headerTitle__account: import("@clerk/types").LocalizationValue;
            headerTitle__security: import("@clerk/types").LocalizationValue;
            profileSection: {
                title: import("@clerk/types").LocalizationValue;
                primaryButton: import("@clerk/types").LocalizationValue;
            };
            usernameSection: {
                title: import("@clerk/types").LocalizationValue;
                primaryButton__updateUsername: import("@clerk/types").LocalizationValue;
                primaryButton__setUsername: import("@clerk/types").LocalizationValue;
            };
            emailAddressesSection: {
                title: import("@clerk/types").LocalizationValue;
                primaryButton: import("@clerk/types").LocalizationValue;
                detailsAction__primary: import("@clerk/types").LocalizationValue;
                detailsAction__nonPrimary: import("@clerk/types").LocalizationValue;
                detailsAction__unverified: import("@clerk/types").LocalizationValue;
                destructiveAction: import("@clerk/types").LocalizationValue;
            };
            phoneNumbersSection: {
                title: import("@clerk/types").LocalizationValue;
                primaryButton: import("@clerk/types").LocalizationValue;
                detailsAction__primary: import("@clerk/types").LocalizationValue;
                detailsAction__nonPrimary: import("@clerk/types").LocalizationValue;
                detailsAction__unverified: import("@clerk/types").LocalizationValue;
                destructiveAction: import("@clerk/types").LocalizationValue;
            };
            connectedAccountsSection: {
                title: import("@clerk/types").LocalizationValue;
                primaryButton: import("@clerk/types").LocalizationValue;
                actionLabel__connectionFailed: import("@clerk/types").LocalizationValue;
                actionLabel__reauthorize: import("@clerk/types").LocalizationValue;
                subtitle__reauthorize: import("@clerk/types").LocalizationValue;
                subtitle__disconnected: import("@clerk/types").LocalizationValue;
                destructiveActionTitle: import("@clerk/types").LocalizationValue;
            };
            enterpriseAccountsSection: {
                title: import("@clerk/types").LocalizationValue;
            };
            passwordSection: {
                title: import("@clerk/types").LocalizationValue;
                primaryButton__updatePassword: import("@clerk/types").LocalizationValue;
                primaryButton__setPassword: import("@clerk/types").LocalizationValue;
            };
            passkeysSection: {
                title: import("@clerk/types").LocalizationValue;
                primaryButton: import("@clerk/types").LocalizationValue;
                menuAction__rename: import("@clerk/types").LocalizationValue;
                menuAction__destructive: import("@clerk/types").LocalizationValue;
            };
            mfaSection: {
                title: import("@clerk/types").LocalizationValue;
                primaryButton: import("@clerk/types").LocalizationValue;
                phoneCode: {
                    destructiveActionLabel: import("@clerk/types").LocalizationValue;
                    actionLabel__setDefault: import("@clerk/types").LocalizationValue;
                };
                backupCodes: {
                    headerTitle: import("@clerk/types").LocalizationValue;
                    title__regenerate: import("@clerk/types").LocalizationValue;
                    subtitle__regenerate: import("@clerk/types").LocalizationValue;
                    actionLabel__regenerate: import("@clerk/types").LocalizationValue;
                };
                totp: {
                    headerTitle: import("@clerk/types").LocalizationValue;
                    destructiveActionTitle: import("@clerk/types").LocalizationValue;
                };
            };
            activeDevicesSection: {
                title: import("@clerk/types").LocalizationValue;
                destructiveAction: import("@clerk/types").LocalizationValue;
            };
            web3WalletsSection: {
                title: import("@clerk/types").LocalizationValue;
                primaryButton: import("@clerk/types").LocalizationValue;
                destructiveAction: import("@clerk/types").LocalizationValue;
                detailsAction__nonPrimary: import("@clerk/types").LocalizationValue;
            };
            dangerSection: {
                title: import("@clerk/types").LocalizationValue;
                deleteAccountButton: import("@clerk/types").LocalizationValue;
            };
        };
        profilePage: {
            title: import("@clerk/types").LocalizationValue;
            imageFormTitle: import("@clerk/types").LocalizationValue;
            imageFormSubtitle: import("@clerk/types").LocalizationValue;
            imageFormDestructiveActionSubtitle: import("@clerk/types").LocalizationValue;
            fileDropAreaHint: import("@clerk/types").LocalizationValue;
            readonly: import("@clerk/types").LocalizationValue;
            successMessage: import("@clerk/types").LocalizationValue;
        };
        usernamePage: {
            successMessage: import("@clerk/types").LocalizationValue;
            title__set: import("@clerk/types").LocalizationValue;
            title__update: import("@clerk/types").LocalizationValue;
        };
        emailAddressPage: {
            title: import("@clerk/types").LocalizationValue;
            verifyTitle: import("@clerk/types").LocalizationValue;
            formHint: import("@clerk/types").LocalizationValue;
            emailCode: {
                formHint: import("@clerk/types").LocalizationValue;
                formTitle: import("@clerk/types").LocalizationValue;
                formSubtitle: import("@clerk/types").LocalizationValue;
                resendButton: import("@clerk/types").LocalizationValue;
                successMessage: import("@clerk/types").LocalizationValue;
            };
            emailLink: {
                formHint: import("@clerk/types").LocalizationValue;
                formTitle: import("@clerk/types").LocalizationValue;
                formSubtitle: import("@clerk/types").LocalizationValue;
                resendButton: import("@clerk/types").LocalizationValue;
                successMessage: import("@clerk/types").LocalizationValue;
            };
            enterpriseSSOLink: {
                formSubtitle: import("@clerk/types").LocalizationValue;
                formButton: import("@clerk/types").LocalizationValue;
            };
            removeResource: {
                title: import("@clerk/types").LocalizationValue;
                messageLine1: import("@clerk/types").LocalizationValue;
                messageLine2: import("@clerk/types").LocalizationValue;
                successMessage: import("@clerk/types").LocalizationValue;
            };
        };
        apiKeysPage: {
            title: import("@clerk/types").LocalizationValue;
            detailsTitle__emptyRow: import("@clerk/types").LocalizationValue;
        };
        passkeyScreen: {
            title__rename: import("@clerk/types").LocalizationValue;
            subtitle__rename: import("@clerk/types").LocalizationValue;
            removeResource: {
                title: import("@clerk/types").LocalizationValue;
                messageLine1: import("@clerk/types").LocalizationValue;
            };
        };
        phoneNumberPage: {
            title: import("@clerk/types").LocalizationValue;
            verifyTitle: import("@clerk/types").LocalizationValue;
            verifySubtitle: import("@clerk/types").LocalizationValue;
            successMessage: import("@clerk/types").LocalizationValue;
            infoText: import("@clerk/types").LocalizationValue;
            removeResource: {
                title: import("@clerk/types").LocalizationValue;
                messageLine1: import("@clerk/types").LocalizationValue;
                messageLine2: import("@clerk/types").LocalizationValue;
                successMessage: import("@clerk/types").LocalizationValue;
            };
        };
        connectedAccountPage: {
            title: import("@clerk/types").LocalizationValue;
            formHint: import("@clerk/types").LocalizationValue;
            formHint__noAccounts: import("@clerk/types").LocalizationValue;
            socialButtonsBlockButton: import("@clerk/types").LocalizationValue;
            successMessage: import("@clerk/types").LocalizationValue;
            removeResource: {
                title: import("@clerk/types").LocalizationValue;
                messageLine1: import("@clerk/types").LocalizationValue;
                messageLine2: import("@clerk/types").LocalizationValue;
                successMessage: import("@clerk/types").LocalizationValue;
            };
        };
        web3WalletPage: {
            title: import("@clerk/types").LocalizationValue;
            subtitle__availableWallets: import("@clerk/types").LocalizationValue;
            subtitle__unavailableWallets: import("@clerk/types").LocalizationValue;
            web3WalletButtonsBlockButton: import("@clerk/types").LocalizationValue;
            successMessage: import("@clerk/types").LocalizationValue;
            removeResource: {
                title: import("@clerk/types").LocalizationValue;
                messageLine1: import("@clerk/types").LocalizationValue;
                messageLine2: import("@clerk/types").LocalizationValue;
                successMessage: import("@clerk/types").LocalizationValue;
            };
        };
        passwordPage: {
            successMessage__set: import("@clerk/types").LocalizationValue;
            successMessage__update: import("@clerk/types").LocalizationValue;
            successMessage__signOutOfOtherSessions: import("@clerk/types").LocalizationValue;
            checkboxInfoText__signOutOfOtherSessions: import("@clerk/types").LocalizationValue;
            readonly: import("@clerk/types").LocalizationValue;
            title__set: import("@clerk/types").LocalizationValue;
            title__update: import("@clerk/types").LocalizationValue;
        };
        mfaPage: {
            title: import("@clerk/types").LocalizationValue;
            formHint: import("@clerk/types").LocalizationValue;
        };
        mfaTOTPPage: {
            title: import("@clerk/types").LocalizationValue;
            verifyTitle: import("@clerk/types").LocalizationValue;
            verifySubtitle: import("@clerk/types").LocalizationValue;
            successMessage: import("@clerk/types").LocalizationValue;
            authenticatorApp: {
                infoText__ableToScan: import("@clerk/types").LocalizationValue;
                infoText__unableToScan: import("@clerk/types").LocalizationValue;
                inputLabel__unableToScan1: import("@clerk/types").LocalizationValue;
                inputLabel__unableToScan2: import("@clerk/types").LocalizationValue;
                buttonAbleToScan__nonPrimary: import("@clerk/types").LocalizationValue;
                buttonUnableToScan__nonPrimary: import("@clerk/types").LocalizationValue;
            };
            removeResource: {
                title: import("@clerk/types").LocalizationValue;
                messageLine1: import("@clerk/types").LocalizationValue;
                messageLine2: import("@clerk/types").LocalizationValue;
                successMessage: import("@clerk/types").LocalizationValue;
            };
        };
        mfaPhoneCodePage: {
            title: import("@clerk/types").LocalizationValue;
            primaryButton__addPhoneNumber: import("@clerk/types").LocalizationValue;
            backButton: import("@clerk/types").LocalizationValue;
            subtitle__availablePhoneNumbers: import("@clerk/types").LocalizationValue;
            subtitle__unavailablePhoneNumbers: import("@clerk/types").LocalizationValue;
            successTitle: import("@clerk/types").LocalizationValue;
            successMessage1: import("@clerk/types").LocalizationValue;
            successMessage2: import("@clerk/types").LocalizationValue;
            removeResource: {
                title: import("@clerk/types").LocalizationValue;
                messageLine1: import("@clerk/types").LocalizationValue;
                messageLine2: import("@clerk/types").LocalizationValue;
                successMessage: import("@clerk/types").LocalizationValue;
            };
        };
        backupCodePage: {
            title: import("@clerk/types").LocalizationValue;
            title__codelist: import("@clerk/types").LocalizationValue;
            subtitle__codelist: import("@clerk/types").LocalizationValue;
            infoText1: import("@clerk/types").LocalizationValue;
            infoText2: import("@clerk/types").LocalizationValue;
            successSubtitle: import("@clerk/types").LocalizationValue;
            successMessage: import("@clerk/types").LocalizationValue;
            actionLabel__copy: import("@clerk/types").LocalizationValue;
            actionLabel__copied: import("@clerk/types").LocalizationValue;
            actionLabel__download: import("@clerk/types").LocalizationValue;
            actionLabel__print: import("@clerk/types").LocalizationValue;
        };
        deletePage: {
            title: import("@clerk/types").LocalizationValue;
            messageLine1: import("@clerk/types").LocalizationValue;
            messageLine2: import("@clerk/types").LocalizationValue;
            actionDescription: import("@clerk/types").LocalizationValue;
            confirm: import("@clerk/types").LocalizationValue;
        };
        billingPage: {
            title: import("@clerk/types").LocalizationValue;
            start: {
                headerTitle__payments: import("@clerk/types").LocalizationValue;
                headerTitle__plans: import("@clerk/types").LocalizationValue;
                headerTitle__subscriptions: import("@clerk/types").LocalizationValue;
                headerTitle__statements: import("@clerk/types").LocalizationValue;
            };
            statementsSection: {
                empty: import("@clerk/types").LocalizationValue;
                itemCaption__paidForPlan: import("@clerk/types").LocalizationValue;
                itemCaption__proratedCredit: import("@clerk/types").LocalizationValue;
                itemCaption__subscribedAndPaidForPlan: import("@clerk/types").LocalizationValue;
                notFound: import("@clerk/types").LocalizationValue;
                tableHeader__date: import("@clerk/types").LocalizationValue;
                tableHeader__amount: import("@clerk/types").LocalizationValue;
                title: import("@clerk/types").LocalizationValue;
                totalPaid: import("@clerk/types").LocalizationValue;
            };
            switchPlansSection: {
                title: import("@clerk/types").LocalizationValue;
            };
            subscriptionsListSection: {
                tableHeader__plan: import("@clerk/types").LocalizationValue;
                tableHeader__startDate: import("@clerk/types").LocalizationValue;
                tableHeader__edit: import("@clerk/types").LocalizationValue;
                title: import("@clerk/types").LocalizationValue;
                actionLabel__newSubscription: import("@clerk/types").LocalizationValue;
                actionLabel__switchPlan: import("@clerk/types").LocalizationValue;
            };
            paymentHistorySection: {
                empty: import("@clerk/types").LocalizationValue;
                notFound: import("@clerk/types").LocalizationValue;
                tableHeader__date: import("@clerk/types").LocalizationValue;
                tableHeader__amount: import("@clerk/types").LocalizationValue;
                tableHeader__status: import("@clerk/types").LocalizationValue;
            };
            paymentSourcesSection: {
                title: import("@clerk/types").LocalizationValue;
                add: import("@clerk/types").LocalizationValue;
                addSubtitle: import("@clerk/types").LocalizationValue;
                cancelButton: import("@clerk/types").LocalizationValue;
                actionLabel__default: import("@clerk/types").LocalizationValue;
                actionLabel__remove: import("@clerk/types").LocalizationValue;
                formButtonPrimary__add: import("@clerk/types").LocalizationValue;
                formButtonPrimary__pay: import("@clerk/types").LocalizationValue;
                removeResource: {
                    title: import("@clerk/types").LocalizationValue;
                    messageLine1: import("@clerk/types").LocalizationValue;
                    messageLine2: import("@clerk/types").LocalizationValue;
                    successMessage: import("@clerk/types").LocalizationValue;
                };
                payWithTestCardButton: import("@clerk/types").LocalizationValue;
            };
            subscriptionsSection: {
                actionLabel__default: import("@clerk/types").LocalizationValue;
            };
        };
        plansPage: {
            title: import("@clerk/types").LocalizationValue;
            alerts: {
                noPermissionsToManageBilling: import("@clerk/types").LocalizationValue;
            };
        };
    };
    userButton: {
        action__manageAccount: import("@clerk/types").LocalizationValue;
        action__signOut: import("@clerk/types").LocalizationValue;
        action__signOutAll: import("@clerk/types").LocalizationValue;
        action__addAccount: import("@clerk/types").LocalizationValue;
    };
    organizationSwitcher: {
        personalWorkspace: import("@clerk/types").LocalizationValue;
        notSelected: import("@clerk/types").LocalizationValue;
        action__createOrganization: import("@clerk/types").LocalizationValue;
        action__manageOrganization: import("@clerk/types").LocalizationValue;
        action__invitationAccept: import("@clerk/types").LocalizationValue;
        action__suggestionsAccept: import("@clerk/types").LocalizationValue;
        suggestionsAcceptedLabel: import("@clerk/types").LocalizationValue;
    };
    impersonationFab: {
        title: import("@clerk/types").LocalizationValue;
        action__signOut: import("@clerk/types").LocalizationValue;
    };
    organizationProfile: {
        navbar: {
            title: import("@clerk/types").LocalizationValue;
            description: import("@clerk/types").LocalizationValue;
            general: import("@clerk/types").LocalizationValue;
            members: import("@clerk/types").LocalizationValue;
            billing: import("@clerk/types").LocalizationValue;
            apiKeys: import("@clerk/types").LocalizationValue;
        };
        badge__unverified: import("@clerk/types").LocalizationValue;
        badge__automaticInvitation: import("@clerk/types").LocalizationValue;
        badge__automaticSuggestion: import("@clerk/types").LocalizationValue;
        badge__manualInvitation: import("@clerk/types").LocalizationValue;
        start: {
            headerTitle__members: import("@clerk/types").LocalizationValue;
            headerTitle__general: import("@clerk/types").LocalizationValue;
            profileSection: {
                title: import("@clerk/types").LocalizationValue;
                primaryButton: import("@clerk/types").LocalizationValue;
                uploadAction__title: import("@clerk/types").LocalizationValue;
            };
        };
        profilePage: {
            title: import("@clerk/types").LocalizationValue;
            successMessage: import("@clerk/types").LocalizationValue;
            dangerSection: {
                title: import("@clerk/types").LocalizationValue;
                leaveOrganization: {
                    title: import("@clerk/types").LocalizationValue;
                    messageLine1: import("@clerk/types").LocalizationValue;
                    messageLine2: import("@clerk/types").LocalizationValue;
                    successMessage: import("@clerk/types").LocalizationValue;
                    actionDescription: import("@clerk/types").LocalizationValue;
                };
                deleteOrganization: {
                    title: import("@clerk/types").LocalizationValue;
                    messageLine1: import("@clerk/types").LocalizationValue;
                    messageLine2: import("@clerk/types").LocalizationValue;
                    actionDescription: import("@clerk/types").LocalizationValue;
                    successMessage: import("@clerk/types").LocalizationValue;
                };
            };
            domainSection: {
                title: import("@clerk/types").LocalizationValue;
                subtitle: import("@clerk/types").LocalizationValue;
                primaryButton: import("@clerk/types").LocalizationValue;
                menuAction__verify: import("@clerk/types").LocalizationValue;
                menuAction__remove: import("@clerk/types").LocalizationValue;
                menuAction__manage: import("@clerk/types").LocalizationValue;
            };
        };
        createDomainPage: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
        };
        verifyDomainPage: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            subtitleVerificationCodeScreen: import("@clerk/types").LocalizationValue;
            formTitle: import("@clerk/types").LocalizationValue;
            formSubtitle: import("@clerk/types").LocalizationValue;
            resendButton: import("@clerk/types").LocalizationValue;
        };
        verifiedDomainPage: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            start: {
                headerTitle__enrollment: import("@clerk/types").LocalizationValue;
                headerTitle__danger: import("@clerk/types").LocalizationValue;
            };
            enrollmentTab: {
                subtitle: import("@clerk/types").LocalizationValue;
                manualInvitationOption__label: import("@clerk/types").LocalizationValue;
                manualInvitationOption__description: import("@clerk/types").LocalizationValue;
                automaticInvitationOption__label: import("@clerk/types").LocalizationValue;
                automaticInvitationOption__description: import("@clerk/types").LocalizationValue;
                automaticSuggestionOption__label: import("@clerk/types").LocalizationValue;
                automaticSuggestionOption__description: import("@clerk/types").LocalizationValue;
                calloutInfoLabel: import("@clerk/types").LocalizationValue;
                calloutInvitationCountLabel: import("@clerk/types").LocalizationValue;
                calloutSuggestionCountLabel: import("@clerk/types").LocalizationValue;
            };
            dangerTab: {
                removeDomainTitle: import("@clerk/types").LocalizationValue;
                removeDomainSubtitle: import("@clerk/types").LocalizationValue;
                removeDomainActionLabel__remove: import("@clerk/types").LocalizationValue;
                calloutInfoLabel: import("@clerk/types").LocalizationValue;
            };
        };
        invitePage: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            successMessage: import("@clerk/types").LocalizationValue;
            detailsTitle__inviteFailed: import("@clerk/types").LocalizationValue;
            formButtonPrimary__continue: import("@clerk/types").LocalizationValue;
            selectDropdown__role: import("@clerk/types").LocalizationValue;
        };
        removeDomainPage: {
            title: import("@clerk/types").LocalizationValue;
            messageLine1: import("@clerk/types").LocalizationValue;
            messageLine2: import("@clerk/types").LocalizationValue;
            successMessage: import("@clerk/types").LocalizationValue;
        };
        membersPage: {
            detailsTitle__emptyRow: import("@clerk/types").LocalizationValue;
            action__invite: import("@clerk/types").LocalizationValue;
            action__search: import("@clerk/types").LocalizationValue;
            start: {
                headerTitle__members: import("@clerk/types").LocalizationValue;
                headerTitle__invitations: import("@clerk/types").LocalizationValue;
                headerTitle__requests: import("@clerk/types").LocalizationValue;
            };
            activeMembersTab: {
                tableHeader__user: import("@clerk/types").LocalizationValue;
                tableHeader__joined: import("@clerk/types").LocalizationValue;
                tableHeader__role: import("@clerk/types").LocalizationValue;
                tableHeader__actions: import("@clerk/types").LocalizationValue;
                menuAction__remove: import("@clerk/types").LocalizationValue;
            };
            invitedMembersTab: {
                tableHeader__invited: import("@clerk/types").LocalizationValue;
                menuAction__revoke: import("@clerk/types").LocalizationValue;
            };
            invitationsTab: {
                table__emptyRow: import("@clerk/types").LocalizationValue;
                autoInvitations: {
                    headerTitle: import("@clerk/types").LocalizationValue;
                    headerSubtitle: import("@clerk/types").LocalizationValue;
                    primaryButton: import("@clerk/types").LocalizationValue;
                };
            };
            requestsTab: {
                tableHeader__requested: import("@clerk/types").LocalizationValue;
                menuAction__approve: import("@clerk/types").LocalizationValue;
                menuAction__reject: import("@clerk/types").LocalizationValue;
                table__emptyRow: import("@clerk/types").LocalizationValue;
                autoSuggestions: {
                    headerTitle: import("@clerk/types").LocalizationValue;
                    headerSubtitle: import("@clerk/types").LocalizationValue;
                    primaryButton: import("@clerk/types").LocalizationValue;
                };
            };
        };
        billingPage: {
            title: import("@clerk/types").LocalizationValue;
            start: {
                headerTitle__payments: import("@clerk/types").LocalizationValue;
                headerTitle__plans: import("@clerk/types").LocalizationValue;
                headerTitle__subscriptions: import("@clerk/types").LocalizationValue;
                headerTitle__statements: import("@clerk/types").LocalizationValue;
            };
            statementsSection: {
                empty: import("@clerk/types").LocalizationValue;
                itemCaption__paidForPlan: import("@clerk/types").LocalizationValue;
                itemCaption__proratedCredit: import("@clerk/types").LocalizationValue;
                itemCaption__subscribedAndPaidForPlan: import("@clerk/types").LocalizationValue;
                notFound: import("@clerk/types").LocalizationValue;
                tableHeader__date: import("@clerk/types").LocalizationValue;
                tableHeader__amount: import("@clerk/types").LocalizationValue;
                title: import("@clerk/types").LocalizationValue;
                totalPaid: import("@clerk/types").LocalizationValue;
            };
            switchPlansSection: {
                title: import("@clerk/types").LocalizationValue;
            };
            subscriptionsListSection: {
                tableHeader__plan: import("@clerk/types").LocalizationValue;
                tableHeader__startDate: import("@clerk/types").LocalizationValue;
                tableHeader__edit: import("@clerk/types").LocalizationValue;
                title: import("@clerk/types").LocalizationValue;
                actionLabel__newSubscription: import("@clerk/types").LocalizationValue;
                actionLabel__switchPlan: import("@clerk/types").LocalizationValue;
            };
            paymentHistorySection: {
                empty: import("@clerk/types").LocalizationValue;
                notFound: import("@clerk/types").LocalizationValue;
                tableHeader__date: import("@clerk/types").LocalizationValue;
                tableHeader__amount: import("@clerk/types").LocalizationValue;
                tableHeader__status: import("@clerk/types").LocalizationValue;
            };
            paymentSourcesSection: {
                title: import("@clerk/types").LocalizationValue;
                add: import("@clerk/types").LocalizationValue;
                addSubtitle: import("@clerk/types").LocalizationValue;
                cancelButton: import("@clerk/types").LocalizationValue;
                actionLabel__default: import("@clerk/types").LocalizationValue;
                actionLabel__remove: import("@clerk/types").LocalizationValue;
                formButtonPrimary__add: import("@clerk/types").LocalizationValue;
                formButtonPrimary__pay: import("@clerk/types").LocalizationValue;
                removeResource: {
                    title: import("@clerk/types").LocalizationValue;
                    messageLine1: import("@clerk/types").LocalizationValue;
                    messageLine2: import("@clerk/types").LocalizationValue;
                    successMessage: import("@clerk/types").LocalizationValue;
                };
                payWithTestCardButton: import("@clerk/types").LocalizationValue;
            };
            subscriptionsSection: {
                actionLabel__default: import("@clerk/types").LocalizationValue;
            };
        };
        plansPage: {
            title: import("@clerk/types").LocalizationValue;
            alerts: {
                noPermissionsToManageBilling: import("@clerk/types").LocalizationValue;
            };
        };
        apiKeysPage: {
            title: import("@clerk/types").LocalizationValue;
            detailsTitle__emptyRow: import("@clerk/types").LocalizationValue;
        };
    };
    createOrganization: {
        title: import("@clerk/types").LocalizationValue;
        formButtonSubmit: import("@clerk/types").LocalizationValue;
        invitePage: {
            formButtonReset: import("@clerk/types").LocalizationValue;
        };
    };
    organizationList: {
        createOrganization: import("@clerk/types").LocalizationValue;
        title: import("@clerk/types").LocalizationValue;
        titleWithoutPersonal: import("@clerk/types").LocalizationValue;
        subtitle: import("@clerk/types").LocalizationValue;
        action__invitationAccept: import("@clerk/types").LocalizationValue;
        invitationAcceptedLabel: import("@clerk/types").LocalizationValue;
        action__suggestionsAccept: import("@clerk/types").LocalizationValue;
        suggestionsAcceptedLabel: import("@clerk/types").LocalizationValue;
        action__createOrganization: import("@clerk/types").LocalizationValue;
    };
    unstable__errors: {
        external_account_not_found: import("@clerk/types").LocalizationValue;
        identification_deletion_failed: import("@clerk/types").LocalizationValue;
        phone_number_exists: import("@clerk/types").LocalizationValue;
        form_identifier_not_found: import("@clerk/types").LocalizationValue;
        captcha_unavailable: import("@clerk/types").LocalizationValue;
        captcha_invalid: import("@clerk/types").LocalizationValue;
        passkey_not_supported: import("@clerk/types").LocalizationValue;
        passkey_pa_not_supported: import("@clerk/types").LocalizationValue;
        passkey_retrieval_cancelled: import("@clerk/types").LocalizationValue;
        passkey_registration_cancelled: import("@clerk/types").LocalizationValue;
        passkey_already_exists: import("@clerk/types").LocalizationValue;
        web3_missing_identifier: import("@clerk/types").LocalizationValue;
        form_password_pwned: import("@clerk/types").LocalizationValue;
        form_password_pwned__sign_in: import("@clerk/types").LocalizationValue;
        form_username_invalid_length: import("@clerk/types").LocalizationValue;
        form_username_invalid_character: import("@clerk/types").LocalizationValue;
        form_param_format_invalid: import("@clerk/types").LocalizationValue;
        form_param_format_invalid__email_address: import("@clerk/types").LocalizationValue;
        form_password_length_too_short: import("@clerk/types").LocalizationValue;
        form_param_nil: import("@clerk/types").LocalizationValue;
        form_code_incorrect: import("@clerk/types").LocalizationValue;
        form_password_incorrect: import("@clerk/types").LocalizationValue;
        form_password_validation_failed: import("@clerk/types").LocalizationValue;
        not_allowed_access: import("@clerk/types").LocalizationValue;
        form_identifier_exists: import("@clerk/types").LocalizationValue;
        form_identifier_exists__email_address: import("@clerk/types").LocalizationValue;
        form_identifier_exists__username: import("@clerk/types").LocalizationValue;
        form_identifier_exists__phone_number: import("@clerk/types").LocalizationValue;
        form_password_not_strong_enough: import("@clerk/types").LocalizationValue;
        form_password_size_in_bytes_exceeded: import("@clerk/types").LocalizationValue;
        form_param_value_invalid: import("@clerk/types").LocalizationValue;
        passwordComplexity: {
            sentencePrefix: import("@clerk/types").LocalizationValue;
            minimumLength: import("@clerk/types").LocalizationValue;
            maximumLength: import("@clerk/types").LocalizationValue;
            requireNumbers: import("@clerk/types").LocalizationValue;
            requireLowercase: import("@clerk/types").LocalizationValue;
            requireUppercase: import("@clerk/types").LocalizationValue;
            requireSpecialCharacter: import("@clerk/types").LocalizationValue;
        };
        session_exists: import("@clerk/types").LocalizationValue;
        zxcvbn: {
            notEnough: import("@clerk/types").LocalizationValue;
            couldBeStronger: import("@clerk/types").LocalizationValue;
            goodPassword: import("@clerk/types").LocalizationValue;
            warnings: {
                straightRow: import("@clerk/types").LocalizationValue;
                keyPattern: import("@clerk/types").LocalizationValue;
                simpleRepeat: import("@clerk/types").LocalizationValue;
                extendedRepeat: import("@clerk/types").LocalizationValue;
                sequences: import("@clerk/types").LocalizationValue;
                recentYears: import("@clerk/types").LocalizationValue;
                dates: import("@clerk/types").LocalizationValue;
                topTen: import("@clerk/types").LocalizationValue;
                topHundred: import("@clerk/types").LocalizationValue;
                common: import("@clerk/types").LocalizationValue;
                similarToCommon: import("@clerk/types").LocalizationValue;
                wordByItself: import("@clerk/types").LocalizationValue;
                namesByThemselves: import("@clerk/types").LocalizationValue;
                commonNames: import("@clerk/types").LocalizationValue;
                userInputs: import("@clerk/types").LocalizationValue;
                pwned: import("@clerk/types").LocalizationValue;
            };
            suggestions: {
                l33t: import("@clerk/types").LocalizationValue;
                reverseWords: import("@clerk/types").LocalizationValue;
                allUppercase: import("@clerk/types").LocalizationValue;
                capitalization: import("@clerk/types").LocalizationValue;
                dates: import("@clerk/types").LocalizationValue;
                recentYears: import("@clerk/types").LocalizationValue;
                associatedYears: import("@clerk/types").LocalizationValue;
                sequences: import("@clerk/types").LocalizationValue;
                repeated: import("@clerk/types").LocalizationValue;
                longerKeyboardPattern: import("@clerk/types").LocalizationValue;
                anotherWord: import("@clerk/types").LocalizationValue;
                useWords: import("@clerk/types").LocalizationValue;
                noNeed: import("@clerk/types").LocalizationValue;
                pwned: import("@clerk/types").LocalizationValue;
            };
        };
        form_param_max_length_exceeded: import("@clerk/types").LocalizationValue;
        organization_minimum_permissions_needed: import("@clerk/types").LocalizationValue;
        already_a_member_in_organization: import("@clerk/types").LocalizationValue;
        organization_domain_common: import("@clerk/types").LocalizationValue;
        organization_domain_blocked: import("@clerk/types").LocalizationValue;
        organization_domain_exists_for_enterprise_connection: import("@clerk/types").LocalizationValue;
        organization_membership_quota_exceeded: import("@clerk/types").LocalizationValue;
    } & Partial<Record<"form_param_format_invalid__email_address" | "form_identifier_exists__email_address" | "form_identifier_exists__username" | "form_identifier_exists__phone_number" | "form_identifier_not_found__email_address" | "form_password_incorrect__email_address" | "form_password_pwned__email_address" | "not_allowed_access__email_address" | "external_account_not_found__email_address" | "captcha_invalid__email_address" | "captcha_unavailable__email_address" | "passkey_not_supported__email_address" | "passkey_pa_not_supported__email_address" | "passkey_already_exists__email_address" | "passkey_retrieval_cancelled__email_address" | "passkey_registration_cancelled__email_address" | "zxcvbn__email_address" | "identification_deletion_failed__email_address" | "phone_number_exists__email_address" | "web3_missing_identifier__email_address" | "form_password_pwned__sign_in__email_address" | "form_username_invalid_length__email_address" | "form_username_invalid_character__email_address" | "form_param_format_invalid__email_address__email_address" | "form_password_length_too_short__email_address" | "form_param_nil__email_address" | "form_code_incorrect__email_address" | "form_password_validation_failed__email_address" | "form_identifier_exists__email_address__email_address" | "form_identifier_exists__username__email_address" | "form_identifier_exists__phone_number__email_address" | "form_password_not_strong_enough__email_address" | "form_password_size_in_bytes_exceeded__email_address" | "form_param_value_invalid__email_address" | "passwordComplexity__email_address" | "session_exists__email_address" | "form_param_max_length_exceeded__email_address" | "organization_minimum_permissions_needed__email_address" | "already_a_member_in_organization__email_address" | "organization_domain_common__email_address" | "organization_domain_blocked__email_address" | "organization_domain_exists_for_enterprise_connection__email_address" | "organization_membership_quota_exceeded__email_address" | "form_identifier_not_found__phone_number" | "form_password_incorrect__phone_number" | "form_password_pwned__phone_number" | "not_allowed_access__phone_number" | "external_account_not_found__phone_number" | "captcha_invalid__phone_number" | "captcha_unavailable__phone_number" | "passkey_not_supported__phone_number" | "passkey_pa_not_supported__phone_number" | "passkey_already_exists__phone_number" | "passkey_retrieval_cancelled__phone_number" | "passkey_registration_cancelled__phone_number" | "zxcvbn__phone_number" | "identification_deletion_failed__phone_number" | "phone_number_exists__phone_number" | "web3_missing_identifier__phone_number" | "form_password_pwned__sign_in__phone_number" | "form_username_invalid_length__phone_number" | "form_username_invalid_character__phone_number" | "form_param_format_invalid__phone_number" | "form_param_format_invalid__email_address__phone_number" | "form_password_length_too_short__phone_number" | "form_param_nil__phone_number" | "form_code_incorrect__phone_number" | "form_password_validation_failed__phone_number" | "form_identifier_exists__email_address__phone_number" | "form_identifier_exists__username__phone_number" | "form_identifier_exists__phone_number__phone_number" | "form_password_not_strong_enough__phone_number" | "form_password_size_in_bytes_exceeded__phone_number" | "form_param_value_invalid__phone_number" | "passwordComplexity__phone_number" | "session_exists__phone_number" | "form_param_max_length_exceeded__phone_number" | "organization_minimum_permissions_needed__phone_number" | "already_a_member_in_organization__phone_number" | "organization_domain_common__phone_number" | "organization_domain_blocked__phone_number" | "organization_domain_exists_for_enterprise_connection__phone_number" | "organization_membership_quota_exceeded__phone_number" | "form_identifier_not_found__username" | "form_password_incorrect__username" | "form_password_pwned__username" | "not_allowed_access__username" | "external_account_not_found__username" | "captcha_invalid__username" | "captcha_unavailable__username" | "passkey_not_supported__username" | "passkey_pa_not_supported__username" | "passkey_already_exists__username" | "passkey_retrieval_cancelled__username" | "passkey_registration_cancelled__username" | "zxcvbn__username" | "identification_deletion_failed__username" | "phone_number_exists__username" | "web3_missing_identifier__username" | "form_password_pwned__sign_in__username" | "form_username_invalid_length__username" | "form_username_invalid_character__username" | "form_param_format_invalid__username" | "form_param_format_invalid__email_address__username" | "form_password_length_too_short__username" | "form_param_nil__username" | "form_code_incorrect__username" | "form_password_validation_failed__username" | "form_identifier_exists__email_address__username" | "form_identifier_exists__username__username" | "form_identifier_exists__phone_number__username" | "form_password_not_strong_enough__username" | "form_password_size_in_bytes_exceeded__username" | "form_param_value_invalid__username" | "passwordComplexity__username" | "session_exists__username" | "form_param_max_length_exceeded__username" | "organization_minimum_permissions_needed__username" | "already_a_member_in_organization__username" | "organization_domain_common__username" | "organization_domain_blocked__username" | "organization_domain_exists_for_enterprise_connection__username" | "organization_membership_quota_exceeded__username" | "form_identifier_not_found__first_name" | "form_password_incorrect__first_name" | "form_password_pwned__first_name" | "not_allowed_access__first_name" | "external_account_not_found__first_name" | "captcha_invalid__first_name" | "captcha_unavailable__first_name" | "passkey_not_supported__first_name" | "passkey_pa_not_supported__first_name" | "passkey_already_exists__first_name" | "passkey_retrieval_cancelled__first_name" | "passkey_registration_cancelled__first_name" | "zxcvbn__first_name" | "identification_deletion_failed__first_name" | "phone_number_exists__first_name" | "web3_missing_identifier__first_name" | "form_password_pwned__sign_in__first_name" | "form_username_invalid_length__first_name" | "form_username_invalid_character__first_name" | "form_param_format_invalid__first_name" | "form_param_format_invalid__email_address__first_name" | "form_password_length_too_short__first_name" | "form_param_nil__first_name" | "form_code_incorrect__first_name" | "form_password_validation_failed__first_name" | "form_identifier_exists__first_name" | "form_identifier_exists__email_address__first_name" | "form_identifier_exists__username__first_name" | "form_identifier_exists__phone_number__first_name" | "form_password_not_strong_enough__first_name" | "form_password_size_in_bytes_exceeded__first_name" | "form_param_value_invalid__first_name" | "passwordComplexity__first_name" | "session_exists__first_name" | "form_param_max_length_exceeded__first_name" | "organization_minimum_permissions_needed__first_name" | "already_a_member_in_organization__first_name" | "organization_domain_common__first_name" | "organization_domain_blocked__first_name" | "organization_domain_exists_for_enterprise_connection__first_name" | "organization_membership_quota_exceeded__first_name" | "form_identifier_not_found__last_name" | "form_password_incorrect__last_name" | "form_password_pwned__last_name" | "not_allowed_access__last_name" | "external_account_not_found__last_name" | "captcha_invalid__last_name" | "captcha_unavailable__last_name" | "passkey_not_supported__last_name" | "passkey_pa_not_supported__last_name" | "passkey_already_exists__last_name" | "passkey_retrieval_cancelled__last_name" | "passkey_registration_cancelled__last_name" | "zxcvbn__last_name" | "identification_deletion_failed__last_name" | "phone_number_exists__last_name" | "web3_missing_identifier__last_name" | "form_password_pwned__sign_in__last_name" | "form_username_invalid_length__last_name" | "form_username_invalid_character__last_name" | "form_param_format_invalid__last_name" | "form_param_format_invalid__email_address__last_name" | "form_password_length_too_short__last_name" | "form_param_nil__last_name" | "form_code_incorrect__last_name" | "form_password_validation_failed__last_name" | "form_identifier_exists__last_name" | "form_identifier_exists__email_address__last_name" | "form_identifier_exists__username__last_name" | "form_identifier_exists__phone_number__last_name" | "form_password_not_strong_enough__last_name" | "form_password_size_in_bytes_exceeded__last_name" | "form_param_value_invalid__last_name" | "passwordComplexity__last_name" | "session_exists__last_name" | "form_param_max_length_exceeded__last_name" | "organization_minimum_permissions_needed__last_name" | "already_a_member_in_organization__last_name" | "organization_domain_common__last_name" | "organization_domain_blocked__last_name" | "organization_domain_exists_for_enterprise_connection__last_name" | "organization_membership_quota_exceeded__last_name" | "form_identifier_not_found__password" | "form_password_incorrect__password" | "form_password_pwned__password" | "not_allowed_access__password" | "external_account_not_found__password" | "captcha_invalid__password" | "captcha_unavailable__password" | "passkey_not_supported__password" | "passkey_pa_not_supported__password" | "passkey_already_exists__password" | "passkey_retrieval_cancelled__password" | "passkey_registration_cancelled__password" | "zxcvbn__password" | "identification_deletion_failed__password" | "phone_number_exists__password" | "web3_missing_identifier__password" | "form_password_pwned__sign_in__password" | "form_username_invalid_length__password" | "form_username_invalid_character__password" | "form_param_format_invalid__password" | "form_param_format_invalid__email_address__password" | "form_password_length_too_short__password" | "form_param_nil__password" | "form_code_incorrect__password" | "form_password_validation_failed__password" | "form_identifier_exists__password" | "form_identifier_exists__email_address__password" | "form_identifier_exists__username__password" | "form_identifier_exists__phone_number__password" | "form_password_not_strong_enough__password" | "form_password_size_in_bytes_exceeded__password" | "form_param_value_invalid__password" | "passwordComplexity__password" | "session_exists__password" | "form_param_max_length_exceeded__password" | "organization_minimum_permissions_needed__password" | "already_a_member_in_organization__password" | "organization_domain_common__password" | "organization_domain_blocked__password" | "organization_domain_exists_for_enterprise_connection__password" | "organization_membership_quota_exceeded__password" | "form_identifier_not_found__legal_accepted" | "form_password_incorrect__legal_accepted" | "form_password_pwned__legal_accepted" | "not_allowed_access__legal_accepted" | "external_account_not_found__legal_accepted" | "captcha_invalid__legal_accepted" | "captcha_unavailable__legal_accepted" | "passkey_not_supported__legal_accepted" | "passkey_pa_not_supported__legal_accepted" | "passkey_already_exists__legal_accepted" | "passkey_retrieval_cancelled__legal_accepted" | "passkey_registration_cancelled__legal_accepted" | "zxcvbn__legal_accepted" | "identification_deletion_failed__legal_accepted" | "phone_number_exists__legal_accepted" | "web3_missing_identifier__legal_accepted" | "form_password_pwned__sign_in__legal_accepted" | "form_username_invalid_length__legal_accepted" | "form_username_invalid_character__legal_accepted" | "form_param_format_invalid__legal_accepted" | "form_param_format_invalid__email_address__legal_accepted" | "form_password_length_too_short__legal_accepted" | "form_param_nil__legal_accepted" | "form_code_incorrect__legal_accepted" | "form_password_validation_failed__legal_accepted" | "form_identifier_exists__legal_accepted" | "form_identifier_exists__email_address__legal_accepted" | "form_identifier_exists__username__legal_accepted" | "form_identifier_exists__phone_number__legal_accepted" | "form_password_not_strong_enough__legal_accepted" | "form_password_size_in_bytes_exceeded__legal_accepted" | "form_param_value_invalid__legal_accepted" | "passwordComplexity__legal_accepted" | "session_exists__legal_accepted" | "form_param_max_length_exceeded__legal_accepted" | "organization_minimum_permissions_needed__legal_accepted" | "already_a_member_in_organization__legal_accepted" | "organization_domain_common__legal_accepted" | "organization_domain_blocked__legal_accepted" | "organization_domain_exists_for_enterprise_connection__legal_accepted" | "organization_membership_quota_exceeded__legal_accepted" | "form_identifier_not_found__code" | "form_password_incorrect__code" | "form_password_pwned__code" | "not_allowed_access__code" | "external_account_not_found__code" | "captcha_invalid__code" | "captcha_unavailable__code" | "passkey_not_supported__code" | "passkey_pa_not_supported__code" | "passkey_already_exists__code" | "passkey_retrieval_cancelled__code" | "passkey_registration_cancelled__code" | "zxcvbn__code" | "identification_deletion_failed__code" | "phone_number_exists__code" | "web3_missing_identifier__code" | "form_password_pwned__sign_in__code" | "form_username_invalid_length__code" | "form_username_invalid_character__code" | "form_param_format_invalid__code" | "form_param_format_invalid__email_address__code" | "form_password_length_too_short__code" | "form_param_nil__code" | "form_code_incorrect__code" | "form_password_validation_failed__code" | "form_identifier_exists__code" | "form_identifier_exists__email_address__code" | "form_identifier_exists__username__code" | "form_identifier_exists__phone_number__code" | "form_password_not_strong_enough__code" | "form_password_size_in_bytes_exceeded__code" | "form_param_value_invalid__code" | "passwordComplexity__code" | "session_exists__code" | "form_param_max_length_exceeded__code" | "organization_minimum_permissions_needed__code" | "already_a_member_in_organization__code" | "organization_domain_common__code" | "organization_domain_blocked__code" | "organization_domain_exists_for_enterprise_connection__code" | "organization_membership_quota_exceeded__code" | "form_identifier_not_found__name" | "form_password_incorrect__name" | "form_password_pwned__name" | "not_allowed_access__name" | "external_account_not_found__name" | "captcha_invalid__name" | "captcha_unavailable__name" | "passkey_not_supported__name" | "passkey_pa_not_supported__name" | "passkey_already_exists__name" | "passkey_retrieval_cancelled__name" | "passkey_registration_cancelled__name" | "zxcvbn__name" | "identification_deletion_failed__name" | "phone_number_exists__name" | "web3_missing_identifier__name" | "form_password_pwned__sign_in__name" | "form_username_invalid_length__name" | "form_username_invalid_character__name" | "form_param_format_invalid__name" | "form_param_format_invalid__email_address__name" | "form_password_length_too_short__name" | "form_param_nil__name" | "form_code_incorrect__name" | "form_password_validation_failed__name" | "form_identifier_exists__name" | "form_identifier_exists__email_address__name" | "form_identifier_exists__username__name" | "form_identifier_exists__phone_number__name" | "form_password_not_strong_enough__name" | "form_password_size_in_bytes_exceeded__name" | "form_param_value_invalid__name" | "passwordComplexity__name" | "session_exists__name" | "form_param_max_length_exceeded__name" | "organization_minimum_permissions_needed__name" | "already_a_member_in_organization__name" | "organization_domain_common__name" | "organization_domain_blocked__name" | "organization_domain_exists_for_enterprise_connection__name" | "organization_membership_quota_exceeded__name" | "form_identifier_not_found__identifier" | "form_password_incorrect__identifier" | "form_password_pwned__identifier" | "not_allowed_access__identifier" | "external_account_not_found__identifier" | "captcha_invalid__identifier" | "captcha_unavailable__identifier" | "passkey_not_supported__identifier" | "passkey_pa_not_supported__identifier" | "passkey_already_exists__identifier" | "passkey_retrieval_cancelled__identifier" | "passkey_registration_cancelled__identifier" | "zxcvbn__identifier" | "identification_deletion_failed__identifier" | "phone_number_exists__identifier" | "web3_missing_identifier__identifier" | "form_password_pwned__sign_in__identifier" | "form_username_invalid_length__identifier" | "form_username_invalid_character__identifier" | "form_param_format_invalid__identifier" | "form_param_format_invalid__email_address__identifier" | "form_password_length_too_short__identifier" | "form_param_nil__identifier" | "form_code_incorrect__identifier" | "form_password_validation_failed__identifier" | "form_identifier_exists__identifier" | "form_identifier_exists__email_address__identifier" | "form_identifier_exists__username__identifier" | "form_identifier_exists__phone_number__identifier" | "form_password_not_strong_enough__identifier" | "form_password_size_in_bytes_exceeded__identifier" | "form_param_value_invalid__identifier" | "passwordComplexity__identifier" | "session_exists__identifier" | "form_param_max_length_exceeded__identifier" | "organization_minimum_permissions_needed__identifier" | "already_a_member_in_organization__identifier" | "organization_domain_common__identifier" | "organization_domain_blocked__identifier" | "organization_domain_exists_for_enterprise_connection__identifier" | "organization_membership_quota_exceeded__identifier" | "form_identifier_not_found__slug" | "form_password_incorrect__slug" | "form_password_pwned__slug" | "not_allowed_access__slug" | "external_account_not_found__slug" | "captcha_invalid__slug" | "captcha_unavailable__slug" | "passkey_not_supported__slug" | "passkey_pa_not_supported__slug" | "passkey_already_exists__slug" | "passkey_retrieval_cancelled__slug" | "passkey_registration_cancelled__slug" | "zxcvbn__slug" | "identification_deletion_failed__slug" | "phone_number_exists__slug" | "web3_missing_identifier__slug" | "form_password_pwned__sign_in__slug" | "form_username_invalid_length__slug" | "form_username_invalid_character__slug" | "form_param_format_invalid__slug" | "form_param_format_invalid__email_address__slug" | "form_password_length_too_short__slug" | "form_param_nil__slug" | "form_code_incorrect__slug" | "form_password_validation_failed__slug" | "form_identifier_exists__slug" | "form_identifier_exists__email_address__slug" | "form_identifier_exists__username__slug" | "form_identifier_exists__phone_number__slug" | "form_password_not_strong_enough__slug" | "form_password_size_in_bytes_exceeded__slug" | "form_param_value_invalid__slug" | "passwordComplexity__slug" | "session_exists__slug" | "form_param_max_length_exceeded__slug" | "organization_minimum_permissions_needed__slug" | "already_a_member_in_organization__slug" | "organization_domain_common__slug" | "organization_domain_blocked__slug" | "organization_domain_exists_for_enterprise_connection__slug" | "organization_membership_quota_exceeded__slug" | "form_identifier_not_found__enrollment_mode" | "form_password_incorrect__enrollment_mode" | "form_password_pwned__enrollment_mode" | "not_allowed_access__enrollment_mode" | "external_account_not_found__enrollment_mode" | "captcha_invalid__enrollment_mode" | "captcha_unavailable__enrollment_mode" | "passkey_not_supported__enrollment_mode" | "passkey_pa_not_supported__enrollment_mode" | "passkey_already_exists__enrollment_mode" | "passkey_retrieval_cancelled__enrollment_mode" | "passkey_registration_cancelled__enrollment_mode" | "zxcvbn__enrollment_mode" | "identification_deletion_failed__enrollment_mode" | "phone_number_exists__enrollment_mode" | "web3_missing_identifier__enrollment_mode" | "form_password_pwned__sign_in__enrollment_mode" | "form_username_invalid_length__enrollment_mode" | "form_username_invalid_character__enrollment_mode" | "form_param_format_invalid__enrollment_mode" | "form_param_format_invalid__email_address__enrollment_mode" | "form_password_length_too_short__enrollment_mode" | "form_param_nil__enrollment_mode" | "form_code_incorrect__enrollment_mode" | "form_password_validation_failed__enrollment_mode" | "form_identifier_exists__enrollment_mode" | "form_identifier_exists__email_address__enrollment_mode" | "form_identifier_exists__username__enrollment_mode" | "form_identifier_exists__phone_number__enrollment_mode" | "form_password_not_strong_enough__enrollment_mode" | "form_password_size_in_bytes_exceeded__enrollment_mode" | "form_param_value_invalid__enrollment_mode" | "passwordComplexity__enrollment_mode" | "session_exists__enrollment_mode" | "form_param_max_length_exceeded__enrollment_mode" | "organization_minimum_permissions_needed__enrollment_mode" | "already_a_member_in_organization__enrollment_mode" | "organization_domain_common__enrollment_mode" | "organization_domain_blocked__enrollment_mode" | "organization_domain_exists_for_enterprise_connection__enrollment_mode" | "organization_membership_quota_exceeded__enrollment_mode" | "form_identifier_not_found__current_password" | "form_password_incorrect__current_password" | "form_password_pwned__current_password" | "not_allowed_access__current_password" | "external_account_not_found__current_password" | "captcha_invalid__current_password" | "captcha_unavailable__current_password" | "passkey_not_supported__current_password" | "passkey_pa_not_supported__current_password" | "passkey_already_exists__current_password" | "passkey_retrieval_cancelled__current_password" | "passkey_registration_cancelled__current_password" | "zxcvbn__current_password" | "identification_deletion_failed__current_password" | "phone_number_exists__current_password" | "web3_missing_identifier__current_password" | "form_password_pwned__sign_in__current_password" | "form_username_invalid_length__current_password" | "form_username_invalid_character__current_password" | "form_param_format_invalid__current_password" | "form_param_format_invalid__email_address__current_password" | "form_password_length_too_short__current_password" | "form_param_nil__current_password" | "form_code_incorrect__current_password" | "form_password_validation_failed__current_password" | "form_identifier_exists__current_password" | "form_identifier_exists__email_address__current_password" | "form_identifier_exists__username__current_password" | "form_identifier_exists__phone_number__current_password" | "form_password_not_strong_enough__current_password" | "form_password_size_in_bytes_exceeded__current_password" | "form_param_value_invalid__current_password" | "passwordComplexity__current_password" | "session_exists__current_password" | "form_param_max_length_exceeded__current_password" | "organization_minimum_permissions_needed__current_password" | "already_a_member_in_organization__current_password" | "organization_domain_common__current_password" | "organization_domain_blocked__current_password" | "organization_domain_exists_for_enterprise_connection__current_password" | "organization_membership_quota_exceeded__current_password" | "form_identifier_not_found__new_password" | "form_password_incorrect__new_password" | "form_password_pwned__new_password" | "not_allowed_access__new_password" | "external_account_not_found__new_password" | "captcha_invalid__new_password" | "captcha_unavailable__new_password" | "passkey_not_supported__new_password" | "passkey_pa_not_supported__new_password" | "passkey_already_exists__new_password" | "passkey_retrieval_cancelled__new_password" | "passkey_registration_cancelled__new_password" | "zxcvbn__new_password" | "identification_deletion_failed__new_password" | "phone_number_exists__new_password" | "web3_missing_identifier__new_password" | "form_password_pwned__sign_in__new_password" | "form_username_invalid_length__new_password" | "form_username_invalid_character__new_password" | "form_param_format_invalid__new_password" | "form_param_format_invalid__email_address__new_password" | "form_password_length_too_short__new_password" | "form_param_nil__new_password" | "form_code_incorrect__new_password" | "form_password_validation_failed__new_password" | "form_identifier_exists__new_password" | "form_identifier_exists__email_address__new_password" | "form_identifier_exists__username__new_password" | "form_identifier_exists__phone_number__new_password" | "form_password_not_strong_enough__new_password" | "form_password_size_in_bytes_exceeded__new_password" | "form_param_value_invalid__new_password" | "passwordComplexity__new_password" | "session_exists__new_password" | "form_param_max_length_exceeded__new_password" | "organization_minimum_permissions_needed__new_password" | "already_a_member_in_organization__new_password" | "organization_domain_common__new_password" | "organization_domain_blocked__new_password" | "organization_domain_exists_for_enterprise_connection__new_password" | "organization_membership_quota_exceeded__new_password" | "form_identifier_not_found__sign_out_of_other_sessions" | "form_password_incorrect__sign_out_of_other_sessions" | "form_password_pwned__sign_out_of_other_sessions" | "not_allowed_access__sign_out_of_other_sessions" | "external_account_not_found__sign_out_of_other_sessions" | "captcha_invalid__sign_out_of_other_sessions" | "captcha_unavailable__sign_out_of_other_sessions" | "passkey_not_supported__sign_out_of_other_sessions" | "passkey_pa_not_supported__sign_out_of_other_sessions" | "passkey_already_exists__sign_out_of_other_sessions" | "passkey_retrieval_cancelled__sign_out_of_other_sessions" | "passkey_registration_cancelled__sign_out_of_other_sessions" | "zxcvbn__sign_out_of_other_sessions" | "identification_deletion_failed__sign_out_of_other_sessions" | "phone_number_exists__sign_out_of_other_sessions" | "web3_missing_identifier__sign_out_of_other_sessions" | "form_password_pwned__sign_in__sign_out_of_other_sessions" | "form_username_invalid_length__sign_out_of_other_sessions" | "form_username_invalid_character__sign_out_of_other_sessions" | "form_param_format_invalid__sign_out_of_other_sessions" | "form_param_format_invalid__email_address__sign_out_of_other_sessions" | "form_password_length_too_short__sign_out_of_other_sessions" | "form_param_nil__sign_out_of_other_sessions" | "form_code_incorrect__sign_out_of_other_sessions" | "form_password_validation_failed__sign_out_of_other_sessions" | "form_identifier_exists__sign_out_of_other_sessions" | "form_identifier_exists__email_address__sign_out_of_other_sessions" | "form_identifier_exists__username__sign_out_of_other_sessions" | "form_identifier_exists__phone_number__sign_out_of_other_sessions" | "form_password_not_strong_enough__sign_out_of_other_sessions" | "form_password_size_in_bytes_exceeded__sign_out_of_other_sessions" | "form_param_value_invalid__sign_out_of_other_sessions" | "passwordComplexity__sign_out_of_other_sessions" | "session_exists__sign_out_of_other_sessions" | "form_param_max_length_exceeded__sign_out_of_other_sessions" | "organization_minimum_permissions_needed__sign_out_of_other_sessions" | "already_a_member_in_organization__sign_out_of_other_sessions" | "organization_domain_common__sign_out_of_other_sessions" | "organization_domain_blocked__sign_out_of_other_sessions" | "organization_domain_exists_for_enterprise_connection__sign_out_of_other_sessions" | "organization_membership_quota_exceeded__sign_out_of_other_sessions" | "form_identifier_not_found__passkey_name" | "form_password_incorrect__passkey_name" | "form_password_pwned__passkey_name" | "not_allowed_access__passkey_name" | "external_account_not_found__passkey_name" | "captcha_invalid__passkey_name" | "captcha_unavailable__passkey_name" | "passkey_not_supported__passkey_name" | "passkey_pa_not_supported__passkey_name" | "passkey_already_exists__passkey_name" | "passkey_retrieval_cancelled__passkey_name" | "passkey_registration_cancelled__passkey_name" | "zxcvbn__passkey_name" | "identification_deletion_failed__passkey_name" | "phone_number_exists__passkey_name" | "web3_missing_identifier__passkey_name" | "form_password_pwned__sign_in__passkey_name" | "form_username_invalid_length__passkey_name" | "form_username_invalid_character__passkey_name" | "form_param_format_invalid__passkey_name" | "form_param_format_invalid__email_address__passkey_name" | "form_password_length_too_short__passkey_name" | "form_param_nil__passkey_name" | "form_code_incorrect__passkey_name" | "form_password_validation_failed__passkey_name" | "form_identifier_exists__passkey_name" | "form_identifier_exists__email_address__passkey_name" | "form_identifier_exists__username__passkey_name" | "form_identifier_exists__phone_number__passkey_name" | "form_password_not_strong_enough__passkey_name" | "form_password_size_in_bytes_exceeded__passkey_name" | "form_param_value_invalid__passkey_name" | "passwordComplexity__passkey_name" | "session_exists__passkey_name" | "form_param_max_length_exceeded__passkey_name" | "organization_minimum_permissions_needed__passkey_name" | "already_a_member_in_organization__passkey_name" | "organization_domain_common__passkey_name" | "organization_domain_blocked__passkey_name" | "organization_domain_exists_for_enterprise_connection__passkey_name" | "organization_membership_quota_exceeded__passkey_name" | "form_identifier_not_found__confirm_password" | "form_password_incorrect__confirm_password" | "form_password_pwned__confirm_password" | "not_allowed_access__confirm_password" | "external_account_not_found__confirm_password" | "captcha_invalid__confirm_password" | "captcha_unavailable__confirm_password" | "passkey_not_supported__confirm_password" | "passkey_pa_not_supported__confirm_password" | "passkey_already_exists__confirm_password" | "passkey_retrieval_cancelled__confirm_password" | "passkey_registration_cancelled__confirm_password" | "zxcvbn__confirm_password" | "identification_deletion_failed__confirm_password" | "phone_number_exists__confirm_password" | "web3_missing_identifier__confirm_password" | "form_password_pwned__sign_in__confirm_password" | "form_username_invalid_length__confirm_password" | "form_username_invalid_character__confirm_password" | "form_param_format_invalid__confirm_password" | "form_param_format_invalid__email_address__confirm_password" | "form_password_length_too_short__confirm_password" | "form_param_nil__confirm_password" | "form_code_incorrect__confirm_password" | "form_password_validation_failed__confirm_password" | "form_identifier_exists__confirm_password" | "form_identifier_exists__email_address__confirm_password" | "form_identifier_exists__username__confirm_password" | "form_identifier_exists__phone_number__confirm_password" | "form_password_not_strong_enough__confirm_password" | "form_password_size_in_bytes_exceeded__confirm_password" | "form_param_value_invalid__confirm_password" | "passwordComplexity__confirm_password" | "session_exists__confirm_password" | "form_param_max_length_exceeded__confirm_password" | "organization_minimum_permissions_needed__confirm_password" | "already_a_member_in_organization__confirm_password" | "organization_domain_common__confirm_password" | "organization_domain_blocked__confirm_password" | "organization_domain_exists_for_enterprise_connection__confirm_password" | "organization_membership_quota_exceeded__confirm_password" | "form_identifier_not_found__delete_confirmation" | "form_password_incorrect__delete_confirmation" | "form_password_pwned__delete_confirmation" | "not_allowed_access__delete_confirmation" | "external_account_not_found__delete_confirmation" | "captcha_invalid__delete_confirmation" | "captcha_unavailable__delete_confirmation" | "passkey_not_supported__delete_confirmation" | "passkey_pa_not_supported__delete_confirmation" | "passkey_already_exists__delete_confirmation" | "passkey_retrieval_cancelled__delete_confirmation" | "passkey_registration_cancelled__delete_confirmation" | "zxcvbn__delete_confirmation" | "identification_deletion_failed__delete_confirmation" | "phone_number_exists__delete_confirmation" | "web3_missing_identifier__delete_confirmation" | "form_password_pwned__sign_in__delete_confirmation" | "form_username_invalid_length__delete_confirmation" | "form_username_invalid_character__delete_confirmation" | "form_param_format_invalid__delete_confirmation" | "form_param_format_invalid__email_address__delete_confirmation" | "form_password_length_too_short__delete_confirmation" | "form_param_nil__delete_confirmation" | "form_code_incorrect__delete_confirmation" | "form_password_validation_failed__delete_confirmation" | "form_identifier_exists__delete_confirmation" | "form_identifier_exists__email_address__delete_confirmation" | "form_identifier_exists__username__delete_confirmation" | "form_identifier_exists__phone_number__delete_confirmation" | "form_password_not_strong_enough__delete_confirmation" | "form_password_size_in_bytes_exceeded__delete_confirmation" | "form_param_value_invalid__delete_confirmation" | "passwordComplexity__delete_confirmation" | "session_exists__delete_confirmation" | "form_param_max_length_exceeded__delete_confirmation" | "organization_minimum_permissions_needed__delete_confirmation" | "already_a_member_in_organization__delete_confirmation" | "organization_domain_common__delete_confirmation" | "organization_domain_blocked__delete_confirmation" | "organization_domain_exists_for_enterprise_connection__delete_confirmation" | "organization_membership_quota_exceeded__delete_confirmation" | "form_identifier_not_found__delete_organization_confirmation" | "form_password_incorrect__delete_organization_confirmation" | "form_password_pwned__delete_organization_confirmation" | "not_allowed_access__delete_organization_confirmation" | "external_account_not_found__delete_organization_confirmation" | "captcha_invalid__delete_organization_confirmation" | "captcha_unavailable__delete_organization_confirmation" | "passkey_not_supported__delete_organization_confirmation" | "passkey_pa_not_supported__delete_organization_confirmation" | "passkey_already_exists__delete_organization_confirmation" | "passkey_retrieval_cancelled__delete_organization_confirmation" | "passkey_registration_cancelled__delete_organization_confirmation" | "zxcvbn__delete_organization_confirmation" | "identification_deletion_failed__delete_organization_confirmation" | "phone_number_exists__delete_organization_confirmation" | "web3_missing_identifier__delete_organization_confirmation" | "form_password_pwned__sign_in__delete_organization_confirmation" | "form_username_invalid_length__delete_organization_confirmation" | "form_username_invalid_character__delete_organization_confirmation" | "form_param_format_invalid__delete_organization_confirmation" | "form_param_format_invalid__email_address__delete_organization_confirmation" | "form_password_length_too_short__delete_organization_confirmation" | "form_param_nil__delete_organization_confirmation" | "form_code_incorrect__delete_organization_confirmation" | "form_password_validation_failed__delete_organization_confirmation" | "form_identifier_exists__delete_organization_confirmation" | "form_identifier_exists__email_address__delete_organization_confirmation" | "form_identifier_exists__username__delete_organization_confirmation" | "form_identifier_exists__phone_number__delete_organization_confirmation" | "form_password_not_strong_enough__delete_organization_confirmation" | "form_password_size_in_bytes_exceeded__delete_organization_confirmation" | "form_param_value_invalid__delete_organization_confirmation" | "passwordComplexity__delete_organization_confirmation" | "session_exists__delete_organization_confirmation" | "form_param_max_length_exceeded__delete_organization_confirmation" | "organization_minimum_permissions_needed__delete_organization_confirmation" | "already_a_member_in_organization__delete_organization_confirmation" | "organization_domain_common__delete_organization_confirmation" | "organization_domain_blocked__delete_organization_confirmation" | "organization_domain_exists_for_enterprise_connection__delete_organization_confirmation" | "organization_membership_quota_exceeded__delete_organization_confirmation" | "form_identifier_not_found__affiliation_email_address" | "form_password_incorrect__affiliation_email_address" | "form_password_pwned__affiliation_email_address" | "not_allowed_access__affiliation_email_address" | "external_account_not_found__affiliation_email_address" | "captcha_invalid__affiliation_email_address" | "captcha_unavailable__affiliation_email_address" | "passkey_not_supported__affiliation_email_address" | "passkey_pa_not_supported__affiliation_email_address" | "passkey_already_exists__affiliation_email_address" | "passkey_retrieval_cancelled__affiliation_email_address" | "passkey_registration_cancelled__affiliation_email_address" | "zxcvbn__affiliation_email_address" | "identification_deletion_failed__affiliation_email_address" | "phone_number_exists__affiliation_email_address" | "web3_missing_identifier__affiliation_email_address" | "form_password_pwned__sign_in__affiliation_email_address" | "form_username_invalid_length__affiliation_email_address" | "form_username_invalid_character__affiliation_email_address" | "form_param_format_invalid__affiliation_email_address" | "form_param_format_invalid__email_address__affiliation_email_address" | "form_password_length_too_short__affiliation_email_address" | "form_param_nil__affiliation_email_address" | "form_code_incorrect__affiliation_email_address" | "form_password_validation_failed__affiliation_email_address" | "form_identifier_exists__affiliation_email_address" | "form_identifier_exists__email_address__affiliation_email_address" | "form_identifier_exists__username__affiliation_email_address" | "form_identifier_exists__phone_number__affiliation_email_address" | "form_password_not_strong_enough__affiliation_email_address" | "form_password_size_in_bytes_exceeded__affiliation_email_address" | "form_param_value_invalid__affiliation_email_address" | "passwordComplexity__affiliation_email_address" | "session_exists__affiliation_email_address" | "form_param_max_length_exceeded__affiliation_email_address" | "organization_minimum_permissions_needed__affiliation_email_address" | "already_a_member_in_organization__affiliation_email_address" | "organization_domain_common__affiliation_email_address" | "organization_domain_blocked__affiliation_email_address" | "organization_domain_exists_for_enterprise_connection__affiliation_email_address" | "organization_membership_quota_exceeded__affiliation_email_address" | "form_identifier_not_found__delete_existing_invitations_suggestions" | "form_password_incorrect__delete_existing_invitations_suggestions" | "form_password_pwned__delete_existing_invitations_suggestions" | "not_allowed_access__delete_existing_invitations_suggestions" | "external_account_not_found__delete_existing_invitations_suggestions" | "captcha_invalid__delete_existing_invitations_suggestions" | "captcha_unavailable__delete_existing_invitations_suggestions" | "passkey_not_supported__delete_existing_invitations_suggestions" | "passkey_pa_not_supported__delete_existing_invitations_suggestions" | "passkey_already_exists__delete_existing_invitations_suggestions" | "passkey_retrieval_cancelled__delete_existing_invitations_suggestions" | "passkey_registration_cancelled__delete_existing_invitations_suggestions" | "zxcvbn__delete_existing_invitations_suggestions" | "identification_deletion_failed__delete_existing_invitations_suggestions" | "phone_number_exists__delete_existing_invitations_suggestions" | "web3_missing_identifier__delete_existing_invitations_suggestions" | "form_password_pwned__sign_in__delete_existing_invitations_suggestions" | "form_username_invalid_length__delete_existing_invitations_suggestions" | "form_username_invalid_character__delete_existing_invitations_suggestions" | "form_param_format_invalid__delete_existing_invitations_suggestions" | "form_param_format_invalid__email_address__delete_existing_invitations_suggestions" | "form_password_length_too_short__delete_existing_invitations_suggestions" | "form_param_nil__delete_existing_invitations_suggestions" | "form_code_incorrect__delete_existing_invitations_suggestions" | "form_password_validation_failed__delete_existing_invitations_suggestions" | "form_identifier_exists__delete_existing_invitations_suggestions" | "form_identifier_exists__email_address__delete_existing_invitations_suggestions" | "form_identifier_exists__username__delete_existing_invitations_suggestions" | "form_identifier_exists__phone_number__delete_existing_invitations_suggestions" | "form_password_not_strong_enough__delete_existing_invitations_suggestions" | "form_password_size_in_bytes_exceeded__delete_existing_invitations_suggestions" | "form_param_value_invalid__delete_existing_invitations_suggestions" | "passwordComplexity__delete_existing_invitations_suggestions" | "session_exists__delete_existing_invitations_suggestions" | "form_param_max_length_exceeded__delete_existing_invitations_suggestions" | "organization_minimum_permissions_needed__delete_existing_invitations_suggestions" | "already_a_member_in_organization__delete_existing_invitations_suggestions" | "organization_domain_common__delete_existing_invitations_suggestions" | "organization_domain_blocked__delete_existing_invitations_suggestions" | "organization_domain_exists_for_enterprise_connection__delete_existing_invitations_suggestions" | "organization_membership_quota_exceeded__delete_existing_invitations_suggestions" | "form_identifier_not_found__api_key_description" | "form_password_incorrect__api_key_description" | "form_password_pwned__api_key_description" | "not_allowed_access__api_key_description" | "external_account_not_found__api_key_description" | "captcha_invalid__api_key_description" | "captcha_unavailable__api_key_description" | "passkey_not_supported__api_key_description" | "passkey_pa_not_supported__api_key_description" | "passkey_already_exists__api_key_description" | "passkey_retrieval_cancelled__api_key_description" | "passkey_registration_cancelled__api_key_description" | "zxcvbn__api_key_description" | "identification_deletion_failed__api_key_description" | "phone_number_exists__api_key_description" | "web3_missing_identifier__api_key_description" | "form_password_pwned__sign_in__api_key_description" | "form_username_invalid_length__api_key_description" | "form_username_invalid_character__api_key_description" | "form_param_format_invalid__api_key_description" | "form_param_format_invalid__email_address__api_key_description" | "form_password_length_too_short__api_key_description" | "form_param_nil__api_key_description" | "form_code_incorrect__api_key_description" | "form_password_validation_failed__api_key_description" | "form_identifier_exists__api_key_description" | "form_identifier_exists__email_address__api_key_description" | "form_identifier_exists__username__api_key_description" | "form_identifier_exists__phone_number__api_key_description" | "form_password_not_strong_enough__api_key_description" | "form_password_size_in_bytes_exceeded__api_key_description" | "form_param_value_invalid__api_key_description" | "passwordComplexity__api_key_description" | "session_exists__api_key_description" | "form_param_max_length_exceeded__api_key_description" | "organization_minimum_permissions_needed__api_key_description" | "already_a_member_in_organization__api_key_description" | "organization_domain_common__api_key_description" | "organization_domain_blocked__api_key_description" | "organization_domain_exists_for_enterprise_connection__api_key_description" | "organization_membership_quota_exceeded__api_key_description" | "form_identifier_not_found__api_key_expiration_date" | "form_password_incorrect__api_key_expiration_date" | "form_password_pwned__api_key_expiration_date" | "not_allowed_access__api_key_expiration_date" | "external_account_not_found__api_key_expiration_date" | "captcha_invalid__api_key_expiration_date" | "captcha_unavailable__api_key_expiration_date" | "passkey_not_supported__api_key_expiration_date" | "passkey_pa_not_supported__api_key_expiration_date" | "passkey_already_exists__api_key_expiration_date" | "passkey_retrieval_cancelled__api_key_expiration_date" | "passkey_registration_cancelled__api_key_expiration_date" | "zxcvbn__api_key_expiration_date" | "identification_deletion_failed__api_key_expiration_date" | "phone_number_exists__api_key_expiration_date" | "web3_missing_identifier__api_key_expiration_date" | "form_password_pwned__sign_in__api_key_expiration_date" | "form_username_invalid_length__api_key_expiration_date" | "form_username_invalid_character__api_key_expiration_date" | "form_param_format_invalid__api_key_expiration_date" | "form_param_format_invalid__email_address__api_key_expiration_date" | "form_password_length_too_short__api_key_expiration_date" | "form_param_nil__api_key_expiration_date" | "form_code_incorrect__api_key_expiration_date" | "form_password_validation_failed__api_key_expiration_date" | "form_identifier_exists__api_key_expiration_date" | "form_identifier_exists__email_address__api_key_expiration_date" | "form_identifier_exists__username__api_key_expiration_date" | "form_identifier_exists__phone_number__api_key_expiration_date" | "form_password_not_strong_enough__api_key_expiration_date" | "form_password_size_in_bytes_exceeded__api_key_expiration_date" | "form_param_value_invalid__api_key_expiration_date" | "passwordComplexity__api_key_expiration_date" | "session_exists__api_key_expiration_date" | "form_param_max_length_exceeded__api_key_expiration_date" | "organization_minimum_permissions_needed__api_key_expiration_date" | "already_a_member_in_organization__api_key_expiration_date" | "organization_domain_common__api_key_expiration_date" | "organization_domain_blocked__api_key_expiration_date" | "organization_domain_exists_for_enterprise_connection__api_key_expiration_date" | "organization_membership_quota_exceeded__api_key_expiration_date" | "form_identifier_not_found__api_key_revoke_confirmation" | "form_password_incorrect__api_key_revoke_confirmation" | "form_password_pwned__api_key_revoke_confirmation" | "not_allowed_access__api_key_revoke_confirmation" | "external_account_not_found__api_key_revoke_confirmation" | "captcha_invalid__api_key_revoke_confirmation" | "captcha_unavailable__api_key_revoke_confirmation" | "passkey_not_supported__api_key_revoke_confirmation" | "passkey_pa_not_supported__api_key_revoke_confirmation" | "passkey_already_exists__api_key_revoke_confirmation" | "passkey_retrieval_cancelled__api_key_revoke_confirmation" | "passkey_registration_cancelled__api_key_revoke_confirmation" | "zxcvbn__api_key_revoke_confirmation" | "identification_deletion_failed__api_key_revoke_confirmation" | "phone_number_exists__api_key_revoke_confirmation" | "web3_missing_identifier__api_key_revoke_confirmation" | "form_password_pwned__sign_in__api_key_revoke_confirmation" | "form_username_invalid_length__api_key_revoke_confirmation" | "form_username_invalid_character__api_key_revoke_confirmation" | "form_param_format_invalid__api_key_revoke_confirmation" | "form_param_format_invalid__email_address__api_key_revoke_confirmation" | "form_password_length_too_short__api_key_revoke_confirmation" | "form_param_nil__api_key_revoke_confirmation" | "form_code_incorrect__api_key_revoke_confirmation" | "form_password_validation_failed__api_key_revoke_confirmation" | "form_identifier_exists__api_key_revoke_confirmation" | "form_identifier_exists__email_address__api_key_revoke_confirmation" | "form_identifier_exists__username__api_key_revoke_confirmation" | "form_identifier_exists__phone_number__api_key_revoke_confirmation" | "form_password_not_strong_enough__api_key_revoke_confirmation" | "form_password_size_in_bytes_exceeded__api_key_revoke_confirmation" | "form_param_value_invalid__api_key_revoke_confirmation" | "passwordComplexity__api_key_revoke_confirmation" | "session_exists__api_key_revoke_confirmation" | "form_param_max_length_exceeded__api_key_revoke_confirmation" | "organization_minimum_permissions_needed__api_key_revoke_confirmation" | "already_a_member_in_organization__api_key_revoke_confirmation" | "organization_domain_common__api_key_revoke_confirmation" | "organization_domain_blocked__api_key_revoke_confirmation" | "organization_domain_exists_for_enterprise_connection__api_key_revoke_confirmation" | "organization_membership_quota_exceeded__api_key_revoke_confirmation", string>>;
    dates: {
        previous6Days: import("@clerk/types").LocalizationValue;
        lastDay: import("@clerk/types").LocalizationValue;
        sameDay: import("@clerk/types").LocalizationValue;
        nextDay: import("@clerk/types").LocalizationValue;
        next6Days: import("@clerk/types").LocalizationValue;
        numeric: import("@clerk/types").LocalizationValue;
    };
    waitlist: {
        start: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            formButton: import("@clerk/types").LocalizationValue;
            actionText: import("@clerk/types").LocalizationValue;
            actionLink: import("@clerk/types").LocalizationValue;
        };
        success: {
            title: import("@clerk/types").LocalizationValue;
            subtitle: import("@clerk/types").LocalizationValue;
            message: import("@clerk/types").LocalizationValue;
        };
    };
    apiKeys: {
        formTitle: import("@clerk/types").LocalizationValue;
        formHint: import("@clerk/types").LocalizationValue;
        formButtonPrimary__add: import("@clerk/types").LocalizationValue;
        menuAction__revoke: import("@clerk/types").LocalizationValue;
        action__search: import("@clerk/types").LocalizationValue;
        action__add: import("@clerk/types").LocalizationValue;
        detailsTitle__emptyRow: import("@clerk/types").LocalizationValue;
        revokeConfirmation: {
            formTitle: import("@clerk/types").LocalizationValue;
            formHint: import("@clerk/types").LocalizationValue;
            formButtonPrimary__revoke: import("@clerk/types").LocalizationValue;
        };
        dates: {
            lastUsed__seconds: import("@clerk/types").LocalizationValue;
            lastUsed__minutes: import("@clerk/types").LocalizationValue;
            lastUsed__hours: import("@clerk/types").LocalizationValue;
            lastUsed__days: import("@clerk/types").LocalizationValue;
            lastUsed__months: import("@clerk/types").LocalizationValue;
            lastUsed__years: import("@clerk/types").LocalizationValue;
        };
    };
}>;
