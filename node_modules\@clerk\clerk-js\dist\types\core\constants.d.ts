export declare const PRESERVED_QUERYSTRING_PARAMS: string[];
export declare const CLERK_MODAL_STATE = "__clerk_modal_state";
export declare const CLERK_SYNCED = "__clerk_synced";
export declare const CLERK_SUFFIXED_COOKIES = "suffixed_cookies";
export declare const CLERK_SATELLITE_URL = "__clerk_satellite_url";
export declare const ERROR_CODES: {
    readonly FORM_IDENTIFIER_NOT_FOUND: "form_identifier_not_found";
    readonly FORM_PASSWORD_INCORRECT: "form_password_incorrect";
    readonly FORM_PASSWORD_PWNED: "form_password_pwned";
    readonly INVALID_STRATEGY_FOR_USER: "strategy_for_user_invalid";
    readonly NOT_ALLOWED_TO_SIGN_UP: "not_allowed_to_sign_up";
    readonly OAUTH_ACCESS_DENIED: "oauth_access_denied";
    readonly OAUTH_EMAIL_DOMAIN_RESERVED_BY_SAML: "oauth_email_domain_reserved_by_saml";
    readonly NOT_ALLOWED_ACCESS: "not_allowed_access";
    readonly SAML_USER_ATTRIBUTE_MISSING: "saml_user_attribute_missing";
    readonly USER_LOCKED: "user_locked";
    readonly EXTERNAL_ACCOUNT_NOT_FOUND: "external_account_not_found";
    readonly SIGN_UP_MODE_RESTRICTED: "sign_up_mode_restricted";
    readonly SIGN_UP_MODE_RESTRICTED_WAITLIST: "sign_up_restricted_waitlist";
    readonly ENTERPRISE_SSO_USER_ATTRIBUTE_MISSING: "enterprise_sso_user_attribute_missing";
    readonly ENTERPRISE_SSO_EMAIL_ADDRESS_DOMAIN_MISMATCH: "enterprise_sso_email_address_domain_mismatch";
    readonly ENTERPRISE_SSO_HOSTED_DOMAIN_MISMATCH: "enterprise_sso_hosted_domain_mismatch";
    readonly SAML_EMAIL_ADDRESS_DOMAIN_MISMATCH: "saml_email_address_domain_mismatch";
    readonly INVITATION_ACCOUNT_NOT_EXISTS: "invitation_account_not_exists";
    readonly ORGANIZATION_MEMBERSHIP_QUOTA_EXCEEDED_FOR_SSO: "organization_membership_quota_exceeded_for_sso";
    readonly CAPTCHA_INVALID: "captcha_invalid";
    readonly FRAUD_DEVICE_BLOCKED: "device_blocked";
    readonly FRAUD_ACTION_BLOCKED: "action_blocked";
    readonly SIGNUP_RATE_LIMIT_EXCEEDED: "signup_rate_limit_exceeded";
};
export declare const SIGN_IN_INITIAL_VALUE_KEYS: string[];
export declare const SIGN_UP_INITIAL_VALUE_KEYS: string[];
export declare const DEBOUNCE_MS = 350;
export declare const SIGN_UP_MODES: {
    PUBLIC: "public";
    RESTRICTED: "restricted";
    WAITLIST: "waitlist";
};
export declare const SUPPORTED_FAPI_VERSION = "2025-04-10";
