// src/sv-SE.ts
var svSE = {
  locale: "sv-SE",
  backButton: "Tillbaka",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Standard",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Annans imitat\xF6renhet",
  badge__primary: "Prim\xE4r",
  badge__renewsAt: void 0,
  badge__requiresAction: "Kr\xE4ver \xE5tg\xE4rd",
  badge__startsAt: void 0,
  badge__thisDevice: "Den h\xE4r enheten",
  badge__unverified: "Overifierad",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Anv\xE4ndarens enhet",
  badge__you: "Du",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Skapa organisation",
    invitePage: {
      formButtonReset: "Hoppa \xF6ver"
    },
    title: "Skapa organisation"
  },
  dates: {
    lastDay: "Ig\xE5r klockan {{ date | timeString('sv-SE') }}",
    next6Days: "{{ date | weekday('sv-SE','long') }} klockan {{ date | timeString('sv-SE') }}",
    nextDay: "Imorgon klockan {{ date | timeString('sv-SE') }}",
    numeric: "{{ date | numeric('sv-SE') }}",
    previous6Days: "Senaste {{ date | weekday('sv-SE','long') }} klockan {{ date | timeString('sv-SE') }}",
    sameDay: "Idag klockan {{ date | timeString('sv-SE') }}"
  },
  dividerText: "eller",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Anv\xE4nd en annan metod",
  footerPageLink__help: "Hj\xE4lp",
  footerPageLink__privacy: "Integritet",
  footerPageLink__terms: "Villkor",
  formButtonPrimary: "Forts\xE4tt",
  formButtonPrimary__verify: "Verifiera",
  formFieldAction__forgotPassword: "Gl\xF6mt l\xF6senord?",
  formFieldError__matchingPasswords: "L\xF6senorden matchar.",
  formFieldError__notMatchingPasswords: "L\xF6senorden matchar inte.",
  formFieldError__verificationLinkExpired: "Verifieringsl\xE4nken har l\xF6pt ut. V\xE4nligen beg\xE4r en ny l\xE4nk.",
  formFieldHintText__optional: "Valfritt",
  formFieldHintText__slug: "En slug \xE4r ett l\xE4sbart ID som m\xE5ste vara unikt. Det anv\xE4nds ofta i URL:er.",
  formFieldInputPlaceholder__backupCode: "Ange din reservkod",
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Radera konto",
  formFieldInputPlaceholder__emailAddress: "Ange din e-postadress",
  formFieldInputPlaceholder__emailAddress_username: "Ange din e-postadress eller ditt anv\xE4ndarnamn",
  formFieldInputPlaceholder__emailAddresses: "Ange eller klistra in en eller flera e-postadresser, separerade med mellanslag eller kommatecken",
  formFieldInputPlaceholder__firstName: "Ange ditt f\xF6rnamn",
  formFieldInputPlaceholder__lastName: "Ange ditt efternamn",
  formFieldInputPlaceholder__organizationDomain: "Ange organisationsdom\xE4n",
  formFieldInputPlaceholder__organizationDomainEmailAddress: "Ange e-postadress f\xF6r verifiering",
  formFieldInputPlaceholder__organizationName: "Ange organisationsnamn",
  formFieldInputPlaceholder__organizationSlug: "min-organisation",
  formFieldInputPlaceholder__password: "Ange ditt l\xF6senord",
  formFieldInputPlaceholder__phoneNumber: "Ange ditt telefonnummer",
  formFieldInputPlaceholder__username: "Ange ditt anv\xE4ndarnamn",
  formFieldLabel__automaticInvitations: "Aktivera automatiska inbjudningar f\xF6r denna dom\xE4n",
  formFieldLabel__backupCode: "Reserv-kod",
  formFieldLabel__confirmDeletion: "Radera konto",
  formFieldLabel__confirmPassword: "Bekr\xE4fta l\xF6senord",
  formFieldLabel__currentPassword: "Nuvarande l\xF6senord",
  formFieldLabel__emailAddress: "E-postadress",
  formFieldLabel__emailAddress_username: "E-postadress eller anv\xE4ndarnamn",
  formFieldLabel__emailAddresses: "E-postadresser",
  formFieldLabel__firstName: "F\xF6rnamn",
  formFieldLabel__lastName: "Efternamn",
  formFieldLabel__newPassword: "Nytt l\xF6senord",
  formFieldLabel__organizationDomain: "Dom\xE4n",
  formFieldLabel__organizationDomainDeletePending: "Ta bort v\xE4ntande inbjudningar och f\xF6rslag",
  formFieldLabel__organizationDomainEmailAddress: "Verifierings-e-postadress",
  formFieldLabel__organizationDomainEmailAddressDescription: "Ange en e-postadress under denna dom\xE4n f\xF6r att f\xE5 en kod och verifiera denna dom\xE4n.",
  formFieldLabel__organizationName: "Organisationsnamn",
  formFieldLabel__organizationSlug: "Slug",
  formFieldLabel__passkeyName: "Namn p\xE5 passkey",
  formFieldLabel__password: "L\xF6senord",
  formFieldLabel__phoneNumber: "Telefonnummer",
  formFieldLabel__role: "Roll",
  formFieldLabel__signOutOfOtherSessions: "Logga ut fr\xE5n alla andra enheter",
  formFieldLabel__username: "Anv\xE4ndarnamn",
  impersonationFab: {
    action__signOut: "Logga ut",
    title: "Inloggad som {{identifier}}"
  },
  maintenanceMode: "Vi genomf\xF6r f\xF6r n\xE4rvarande underh\xE5ll, men oroa dig inte, det b\xF6r inte ta mer \xE4n n\xE5gra minuter.",
  membershipRole__admin: "Admin",
  membershipRole__basicMember: "Medlem",
  membershipRole__guestMember: "G\xE4st",
  organizationList: {
    action__createOrganization: "Skapa organisation",
    action__invitationAccept: "G\xE5 med",
    action__suggestionsAccept: "Be om att g\xE5 med",
    createOrganization: "Skapa Organisation",
    invitationAcceptedLabel: "G\xE5tt med",
    subtitle: "f\xF6r att forts\xE4tta till {{applicationName}}",
    suggestionsAcceptedLabel: "V\xE4ntar godk\xE4nnande",
    title: "V\xE4lj ett konto",
    titleWithoutPersonal: "V\xE4lj en organisation"
  },
  organizationProfile: {
    badge__automaticInvitation: "Automatiska inbjudningar",
    badge__automaticSuggestion: "Automatiska f\xF6rslag",
    badge__manualInvitation: "Ingen automatisk registrering",
    badge__unverified: "Overifierad",
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "L\xE4gg till dom\xE4nen f\xF6r att verifiera. Anv\xE4ndare med e-postadresser i denna dom\xE4n kan g\xE5 med i organisationen automatiskt eller beg\xE4ra att f\xE5 g\xE5 med.",
      title: "L\xE4gg till dom\xE4n"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Inbjudningarna kunde inte skickas. \xC5tg\xE4rda f\xF6ljande och f\xF6rs\xF6k igen:",
      formButtonPrimary__continue: "Skicka inbjudningar",
      selectDropdown__role: "V\xE4lj roll",
      subtitle: "Bjud in nya medlemmar till denna organisation",
      successMessage: "Inbjudningar skickade",
      title: "Bjud in medlemmar"
    },
    membersPage: {
      action__invite: "Bjud in",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "Ta bort medlem",
        tableHeader__actions: "\xC5tg\xE4rder",
        tableHeader__joined: "Gick med",
        tableHeader__role: "Roll",
        tableHeader__user: "Anv\xE4ndare"
      },
      detailsTitle__emptyRow: "Inga medlemmar att visa",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Bjud in anv\xE4ndare genom att koppla en e-postdom\xE4n till din organisation. Alla som registrerar sig med en matchande e-postdom\xE4n kommer att kunna g\xE5 med i organisationen n\xE4r som helst.",
          headerTitle: "Automatiska inbjudningar",
          primaryButton: "Hantera verifierade dom\xE4ner"
        },
        table__emptyRow: "Inga inbjudningar att visa"
      },
      invitedMembersTab: {
        menuAction__revoke: "\xC5terkalla inbjudan",
        tableHeader__invited: "Inbjudna"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Anv\xE4ndare som registrerar sig med en matchande e-postdom\xE4n kommer att kunna se ett f\xF6rslag om att beg\xE4ra att g\xE5 med i din organisation.",
          headerTitle: "Automatiska f\xF6rslag",
          primaryButton: "Hantera verifierade dom\xE4ner"
        },
        menuAction__approve: "Godk\xE4nn",
        menuAction__reject: "Avvisa",
        tableHeader__requested: "Beg\xE4rd \xE5tkomst",
        table__emptyRow: "Inga f\xF6rfr\xE5gningar att visa"
      },
      start: {
        headerTitle__invitations: "Inbjudningar",
        headerTitle__members: "Medlemmar",
        headerTitle__requests: "F\xF6rfr\xE5gningar"
      }
    },
    navbar: {
      billing: void 0,
      description: "Hantera din organisation.",
      general: "Allm\xE4nna inst\xE4llningar",
      members: "Medlemar",
      title: "Organisation"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Skriv "{{organizationName}}" nedan f\xF6r att forts\xE4tta.',
          messageLine1: "\xC4r du s\xE4ker p\xE5 att du vill radera denna organisation?",
          messageLine2: "Denna \xE5tg\xE4rd \xE4r permanent och kan inte \xE5ngras.",
          successMessage: "Du har raderat organisationen.",
          title: "Radera organisation"
        },
        leaveOrganization: {
          actionDescription: 'Skriv "{{organizationName}}" nedan f\xF6r att forts\xE4tta.',
          messageLine1: "\xC4r du s\xE4ker p\xE5 att du vill l\xE4mna denna organisation? Du kommer att f\xF6rlora \xE5tkomst till organisationen och dess applikationer.",
          messageLine2: "Denna \xE5tg\xE4rd \xE4r permanent och o\xE5terkallelig.",
          successMessage: "Du har l\xE4mnat organisationen.",
          title: "L\xE4mna organisation"
        },
        title: "Farligt"
      },
      domainSection: {
        menuAction__manage: "Hantera",
        menuAction__remove: "Radera",
        menuAction__verify: "Verifiera",
        primaryButton: "L\xE4gg till dom\xE4n",
        subtitle: "Till\xE5t anv\xE4ndare att g\xE5 med i organisationen automatiskt eller beg\xE4ra att g\xE5 med baserat p\xE5 en verifierad e-postdom\xE4n.",
        title: "Verifierade dom\xE4ner"
      },
      successMessage: "Organisationen har uppdaterats.",
      title: "Organisationsprofil"
    },
    removeDomainPage: {
      messageLine1: "E-postdom\xE4nen {{domain}} kommer att tas bort.",
      messageLine2: "Anv\xE4ndare kommer inte att kunna g\xE5 med i organisationen automatiskt efter detta.",
      successMessage: "{{domain}} har tagits bort.",
      title: "Ta bort dom\xE4n"
    },
    start: {
      headerTitle__general: "Allm\xE4nna inst\xE4llningar",
      headerTitle__members: "Medlemmar",
      profileSection: {
        primaryButton: "Uppdatera profil",
        title: "Organisationsprofil",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Att ta bort denna dom\xE4n kommer att p\xE5verka inbjudna anv\xE4ndare.",
        removeDomainActionLabel__remove: "Ta bort dom\xE4n",
        removeDomainSubtitle: "Ta bort denna dom\xE4n fr\xE5n dina verifierade dom\xE4ner",
        removeDomainTitle: "Ta bort dom\xE4n"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Anv\xE4ndare bjuds automatiskt in att g\xE5 med i organisationen n\xE4r de registrerar sig och kan g\xE5 med n\xE4r som helst.",
        automaticInvitationOption__label: "Automatiska inbjudningar",
        automaticSuggestionOption__description: "Anv\xE4ndare f\xE5r ett f\xF6rslag om att beg\xE4ra att f\xE5 g\xE5 med, men m\xE5ste godk\xE4nnas av en administrat\xF6r innan de kan g\xE5 med i organisationen.",
        automaticSuggestionOption__label: "Automatiska f\xF6rslag",
        calloutInfoLabel: "Att \xE4ndra registreringsl\xE4get kommer endast att p\xE5verka nya anv\xE4ndare.",
        calloutInvitationCountLabel: "V\xE4ntande inbjudningar skickade till anv\xE4ndare: {{count}}",
        calloutSuggestionCountLabel: "V\xE4ntande f\xF6rslag skickade till anv\xE4ndare: {{count}}",
        manualInvitationOption__description: "Anv\xE4ndare kan endast bjudas in manuellt till organisationen.",
        manualInvitationOption__label: "Ingen automatisk registrering",
        subtitle: "V\xE4lj hur anv\xE4ndare fr\xE5n denna dom\xE4n kan g\xE5 med i organisationen."
      },
      start: {
        headerTitle__danger: "Fara",
        headerTitle__enrollment: "Registreringsalternativ"
      },
      subtitle: "Dom\xE4nen {{domain}} \xE4r nu verifierad. Forts\xE4tt genom att v\xE4lja registreringsl\xE4ge.",
      title: "Uppdatera {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Ange verifieringskoden som skickats till din e-postadress",
      formTitle: "Verifieringskod",
      resendButton: "Fick du inte koden? Skicka igen",
      subtitle: "Dom\xE4nen {{domainName}} beh\xF6ver verifieras via e-post.",
      subtitleVerificationCodeScreen: "En verifieringskod skickades till {{emailAddress}}. Ange koden f\xF6r att forts\xE4tta.",
      title: "Verifiera dom\xE4n"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Skapa organisation",
    action__invitationAccept: "Join",
    action__manageOrganization: "Hantera organisation",
    action__suggestionsAccept: "Request to join",
    notSelected: "Ingen organisation vald",
    personalWorkspace: "Personligt Arbetsomr\xE5de",
    suggestionsAcceptedLabel: "Pending approval"
  },
  paginationButton__next: "N\xE4sta",
  paginationButton__previous: "F\xF6reg\xE5ende",
  paginationRowText__displaying: "Visar",
  paginationRowText__of: "av",
  reverification: {
    alternativeMethods: {
      actionLink: "F\xE5 hj\xE4lp",
      actionText: "Har du inget av dessa?",
      blockButton__backupCode: "Anv\xE4nd en reservkod",
      blockButton__emailCode: "Skicka kod via e-post till {{identifier}}",
      blockButton__passkey: void 0,
      blockButton__password: "Forts\xE4tt med ditt l\xF6senord",
      blockButton__phoneCode: "Skicka SMS-kod till {{identifier}}",
      blockButton__totp: "Anv\xE4nd din autentiseringsapp",
      getHelp: {
        blockButton__emailSupport: "E-posta support",
        content: "Om du har problem med att verifiera ditt konto, maila oss s\xE5 hj\xE4lper vi dig att \xE5terst\xE4lla \xE5tkomsten s\xE5 snart som m\xF6jligt.",
        title: "F\xE5 hj\xE4lp"
      },
      subtitle: "Har du problem? Du kan anv\xE4nda n\xE5gon av dessa metoder f\xF6r verifiering.",
      title: "Anv\xE4nd en annan metod"
    },
    backupCodeMfa: {
      subtitle: "Din reservkod \xE4r den du fick n\xE4r du st\xE4llde in tv\xE5stegsverifiering.",
      title: "Ange en reservkod"
    },
    emailCode: {
      formTitle: "Verifieringskod",
      resendButton: "Fick du ingen kod? Skicka igen",
      subtitle: "f\xF6r att forts\xE4tta till {{applicationName}}",
      title: "Kontrollera din e-post"
    },
    noAvailableMethods: {
      message: "Kan inte forts\xE4tta med verifieringen. Det finns ingen tillg\xE4nglig autentiseringsfaktor.",
      subtitle: "Ett fel intr\xE4ffade",
      title: "Kan inte verifiera ditt konto"
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: "Anv\xE4nd en annan metod",
      subtitle: "Ange l\xF6senordet som \xE4r kopplat till ditt konto",
      title: "Ange ditt l\xF6senord"
    },
    phoneCode: {
      formTitle: "Verifieringskod",
      resendButton: "Fick du ingen kod? Skicka igen",
      subtitle: "f\xF6r att forts\xE4tta till {{applicationName}}",
      title: "Kontrollera din telefon"
    },
    phoneCodeMfa: {
      formTitle: "Verifieringskod",
      resendButton: "Fick du ingen kod? Skicka igen",
      subtitle: "F\xF6r att forts\xE4tta, v\xE4nligen ange verifieringskoden som skickats till din telefon",
      title: "Kontrollera din telefon"
    },
    totpMfa: {
      formTitle: "Verifieringskod",
      subtitle: "F\xF6r att forts\xE4tta, v\xE4nligen ange verifieringskoden som genererats av din autentiseringsapp",
      title: "Tv\xE5stegsverifiering"
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "L\xE4gg till konto",
      action__signOutAll: "Logga ut fr\xE5n alla konton",
      subtitle: "V\xE4lj det konto du vill forts\xE4tta med",
      title: "V\xE4lj ett konto"
    },
    alternativeMethods: {
      actionLink: "F\xE5 hj\xE4lp",
      actionText: "Saknar du n\xE5gon av dessa?",
      blockButton__backupCode: "Anv\xE4nd en reservkod",
      blockButton__emailCode: "Skicka kod till {{identifier}}",
      blockButton__emailLink: "Skicka l\xE4nk till {{identifier}}",
      blockButton__passkey: "Anv\xE4nd din passkey",
      blockButton__password: "Logga in med ditt l\xF6senord",
      blockButton__phoneCode: "Skicka kod till {{identifier}}",
      blockButton__totp: "Anv\xE4nd din autentiseringsapp",
      getHelp: {
        blockButton__emailSupport: "E-post support",
        content: "Om du har problem med att logga in p\xE5 ditt konto, kontakta oss via e-post s\xE5 hj\xE4lper vi dig att \xE5terst\xE4lla \xE5tkomsten s\xE5 snabbt som m\xF6jligt.",
        title: "F\xE5 hj\xE4lp"
      },
      subtitle: "Har du problem? Du kan anv\xE4nda n\xE5gon av dessa metoder f\xF6r att logga in.",
      title: "Anv\xE4nd en annan metod"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "f\xF6r att forts\xE4tta till {{applicationName}}",
      title: "Ange en reservkod"
    },
    emailCode: {
      formTitle: "Verifieringskod",
      resendButton: "Skicka koden igen",
      subtitle: "f\xF6r att forts\xE4tta till {{applicationName}}",
      title: "Kontrollera din e-post"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "F\xF6r att forts\xE4tta, \xF6ppna verifieringsl\xE4nken p\xE5 enhet och webbl\xE4sare fr\xE5n vilken du startade inloggningen",
        title: "Verifieringsl\xE4nken \xE4r ogiltig f\xF6r denna enhet"
      },
      expired: {
        subtitle: "\xC5terg\xE5 till ursprungliga fliken f\xF6r att forts\xE4tta.",
        title: "Denna verifieringsl\xE4nk har upph\xF6rt att g\xE4lla"
      },
      failed: {
        subtitle: "\xC5terg\xE5 till ursprungliga fliken f\xF6r att forts\xE4tta.",
        title: "Denna verifieringsl\xE4nk \xE4r ogiltig"
      },
      formSubtitle: "Anv\xE4nd verifieringsl\xE4nken som skickades till din e-postadress",
      formTitle: "Verifieringsl\xE4nk",
      loading: {
        subtitle: "Du kommer att omdirigeras snart",
        title: "Loggar in..."
      },
      resendButton: "Skicka l\xE4nken igen",
      subtitle: "f\xF6r att forts\xE4tta till {{applicationName}}",
      title: "Kontrollera din e-post",
      unusedTab: {
        title: "Du kan st\xE4nga den h\xE4r fliken"
      },
      verified: {
        subtitle: "Du kommer att omdirigeras snart",
        title: "Inloggningen lyckades"
      },
      verifiedSwitchTab: {
        subtitle: "\xC5terg\xE5 till ursprungliga fliken f\xF6r att forts\xE4tta",
        subtitleNewTab: "\xC5terg\xE5 till den nyligen \xF6ppnade fliken f\xF6r att forts\xE4tta",
        titleNewTab: "Loggade in p\xE5 annan flik"
      }
    },
    forgotPassword: {
      formTitle: "\xC5terst\xE4ll l\xF6senordskod",
      resendButton: "Fick du inte en kod? Skicka igen",
      subtitle: "f\xF6r att \xE5terst\xE4lla ditt l\xF6senord",
      subtitle_email: "F\xF6rst, ange koden som skickats till din e-postadress",
      subtitle_phone: "F\xF6rst, ange koden som skickats till din telefon",
      title: "\xC5terst\xE4ll l\xF6senord"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "\xC5terst\xE4ll ditt l\xF6senord",
      label__alternativeMethods: "Eller, logga in med en annan metod",
      title: "Gl\xF6mt l\xF6senord?"
    },
    noAvailableMethods: {
      message: "Kan inte forts\xE4tta med inloggning. Det finns ingen tillg\xE4nglig autentiseringsfaktor.",
      subtitle: "Ett fel intr\xE4ffade",
      title: "Kan inte logga in"
    },
    passkey: {
      subtitle: "Att anv\xE4nda din passkey bekr\xE4ftar att det \xE4r du. Din enhet kan be om ditt fingeravtryck, ansikte eller sk\xE4rml\xE5s.",
      title: "Anv\xE4nd din passkey"
    },
    password: {
      actionLink: "Anv\xE4nd en annan metod",
      subtitle: "f\xF6r att forts\xE4tta till {{applicationName}}",
      title: "Ange ditt l\xF6senord"
    },
    passwordPwned: {
      title: "L\xF6senord \xE4r f\xF6r os\xE4kert"
    },
    phoneCode: {
      formTitle: "Verifieringskod",
      resendButton: "Skicka koden igen",
      subtitle: "f\xF6r att forts\xE4tta till {{applicationName}}",
      title: "Kolla din telefon"
    },
    phoneCodeMfa: {
      formTitle: "Verifieringskod",
      resendButton: "Skicka koden igen",
      subtitle: "F\xF6r att forts\xE4tta, v\xE4nligen ange verifieringskoden som skickats till din telefon",
      title: "Kolla din telefon"
    },
    resetPassword: {
      formButtonPrimary: "\xC5terst\xE4ll l\xF6senord",
      requiredMessage: "Av s\xE4kerhetssk\xE4l \xE4r det n\xF6dv\xE4ndigt att \xE5terst\xE4lla ditt l\xF6senord.",
      successMessage: "Ditt l\xF6senord har \xE4ndrats framg\xE5ngsrikt. Loggar in dig, var god v\xE4nta en stund.",
      title: "Ange nytt l\xF6senord"
    },
    resetPasswordMfa: {
      detailsLabel: "Vi beh\xF6ver verifiera din identitet innan vi \xE5terst\xE4ller ditt l\xF6senord."
    },
    start: {
      actionLink: "Skapa konto",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "Use email",
      actionLink__use_email_username: "Use email or username",
      actionLink__use_passkey: "Anv\xE4nd passkey ist\xE4llet",
      actionLink__use_phone: "Anv\xE4nd telefon",
      actionLink__use_username: "Anv\xE4nd anv\xE4ndarnamn",
      actionText: "Har du inget konto?",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "f\xF6r att forts\xE4tta till {{applicationName}}",
      subtitleCombined: void 0,
      title: "Logga in",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "Verifieringskod",
      subtitle: "F\xF6r att forts\xE4tta, v\xE4nligen ange verifieringskoden som genereras av din autentiseringsapp",
      title: "Tv\xE5stegsverifiering"
    }
  },
  signInEnterPasswordTitle: "Ange ditt l\xF6senord",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Logga in",
      actionText: "Har du redan ett konto?",
      subtitle: "f\xF6r att forts\xE4tta till {{applicationName}}",
      title: "Fyll i n\xF6dv\xE4ndiga f\xE4lt"
    },
    emailCode: {
      formSubtitle: "Ange verifieringskoden som skickades till din e-postadress",
      formTitle: "Verifieringskod",
      resendButton: "Skicka koden igen",
      subtitle: "f\xF6r att forts\xE4tta till {{applicationName}}",
      title: "Verifiera din e-post"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "F\xF6r att forts\xE4tta, \xF6ppna verifieringsl\xE4nken p\xE5 enhet och webbl\xE4sare fr\xE5n vilken du startade inloggningen",
        title: "Verifieringsl\xE4nken \xE4r ogiltig f\xF6r denna enhet"
      },
      formSubtitle: "Anv\xE4nd verifieringsl\xE4nken som skickades till din e-postadress",
      formTitle: "Verifieringsl\xE4nk",
      loading: {
        title: "Registrerar..."
      },
      resendButton: "Skicka l\xE4nken igen",
      subtitle: "f\xF6r att forts\xE4tta till {{applicationName}}",
      title: "Verifiera din e-post",
      verified: {
        title: "Registreringen lyckades"
      },
      verifiedSwitchTab: {
        subtitle: "\xC5terg\xE5 till den nyligen \xF6ppnade fliken f\xF6r att forts\xE4tta",
        subtitleNewTab: "\xC5terg\xE5 till f\xF6reg\xE5ende flik f\xF6r att forts\xE4tta",
        title: "E-posten har verifierats"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: 'Jag godk\xE4nner {{ privacyPolicyLink || link("Integritetspolicyn") }}',
        label__onlyTermsOfService: 'Jag godk\xE4nner de {{ termsOfServiceLink || link("Allm\xE4nna villkoren") }}',
        label__termsOfServiceAndPrivacyPolicy: 'Jag godk\xE4nner de {{ termsOfServiceLink || link("Allm\xE4nna villkoren") }} och {{ privacyPolicyLink || link("Integritetspolicyn") }}'
      },
      continue: {
        subtitle: "V\xE4nligen l\xE4s och godk\xE4nn villkoren f\xF6r att forts\xE4tta",
        title: "Juridiskt samtycke"
      }
    },
    phoneCode: {
      formSubtitle: "Ange verifieringskoden som skickades till ditt telefonnummer",
      formTitle: "Verifieringskod",
      resendButton: "Skicka koden igen",
      subtitle: "f\xF6r att forts\xE4tta till {{applicationName}}",
      title: "Verifiera din telefon"
    },
    restrictedAccess: {
      actionLink: "Tillbaka till inloggning",
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: "\xC5tkomst till denna app \xE4r begr\xE4nsad och en inbjudan kr\xE4vs f\xF6r att registrera sig.",
      subtitleWaitlist: void 0,
      title: "Begr\xE4nsad \xE5tkomst"
    },
    start: {
      actionLink: "Logga in",
      actionLink__use_email: "Anv\xE4nd e-post ist\xE4llet",
      actionLink__use_phone: "Anv\xE4nd telefon ist\xE4llet",
      actionText: "Har du redan ett konto?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "f\xF6r att forts\xE4tta till {{applicationName}}",
      subtitleCombined: "f\xF6r att forts\xE4tta till {{applicationName}}",
      title: "Skapa ditt konto",
      titleCombined: "Skapa ditt konto"
    }
  },
  socialButtonsBlockButton: "Forts\xE4tt med {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: "{{provider|titleize}}",
  unstable__errors: {
    already_a_member_in_organization: "{{email}} \xE4r redan medlem i organisationen.",
    captcha_invalid: "Registrering misslyckades p\xE5 grund av s\xE4kerhetskontroller. V\xE4nligen uppdatera sidan och f\xF6rs\xF6k igen eller kontakta supporten f\xF6r mer hj\xE4lp.",
    captcha_unavailable: "Registrering misslyckades p\xE5 grund av misslyckad bot-validering. V\xE4nligen uppdatera sidan och f\xF6rs\xF6k igen eller kontakta supporten f\xF6r mer hj\xE4lp.",
    form_code_incorrect: "Koden \xE4r felaktig",
    form_identifier_exists__email_address: "Denna e-postadress \xE4r taget. V\xE4nligen prova ett annat.",
    form_identifier_exists__phone_number: "Detta telefonnummer \xE4r taget. V\xE4nligen prova ett annat.",
    form_identifier_exists__username: "Detta anv\xE4ndarnamn \xE4r taget. V\xE4nligen prova ett annat.",
    form_identifier_not_found: "Vi kunde inte hitta ett konto med dessa uppgifter.",
    form_param_format_invalid: "Formatet \xE4r ogiltigt.",
    form_param_format_invalid__email_address: "E-postadressen m\xE5ste vara en giltig e-postadress.",
    form_param_format_invalid__phone_number: "Telefonnumret m\xE5ste vara i ett giltigt internationellt format.",
    form_param_max_length_exceeded__first_name: "F\xF6rnamnet f\xE5r inte \xF6verskrida 256 tecken.",
    form_param_max_length_exceeded__last_name: "Efternamnet f\xE5r inte \xF6verskrida 256 tecken.",
    form_param_max_length_exceeded__name: "Namnet f\xE5r inte \xF6verskrida 256 tecken.",
    form_param_nil: "Parametern f\xE5r inte vara tom.",
    form_param_value_invalid: void 0,
    form_password_incorrect: "L\xF6senordet \xE4r felaktigt.",
    form_password_length_too_short: "L\xF6senordet \xE4r f\xF6r kort.",
    form_password_not_strong_enough: "Ditt l\xF6senord \xE4r inte tillr\xE4ckligt starkt.",
    form_password_pwned: "L\xF6senordet har l\xE4ckt i tidigare dataintr\xE5ng.",
    form_password_pwned__sign_in: "L\xF6senordet har l\xE4ckt, v\xE4nligen logga in f\xF6r att \xE4ndra det.",
    form_password_size_in_bytes_exceeded: "Ditt l\xF6senord har \xF6verskridit det maximala antalet till\xE5tna bytes, v\xE4nligen f\xF6rkorta det eller ta bort n\xE5gra specialtecken.",
    form_password_validation_failed: "Felaktigt l\xF6senord",
    form_username_invalid_character: "Anv\xE4ndarnamnet inneh\xE5ller ogiltiga tecken.",
    form_username_invalid_length: "Anv\xE4ndarnamnets l\xE4ngd \xE4r ogiltig.",
    identification_deletion_failed: "Du kan inte ta bort din sista identifiering.",
    not_allowed_access: "Adressen eller telefonnumret du anv\xE4nder f\xF6r registrering \xE4r inte till\xE5tet. Detta kan bero p\xE5 att du anv\xE4nder '+', '=', '#' eller '.' i din e-postadress, anv\xE4nder en dom\xE4n som \xE4r kopplad till en tidsbegr\xE4nsad e-posttj\xE4nst eller har ett explicit blockerat.",
    organization_domain_blocked: "Dom\xE4nen \xE4r blockerad.",
    organization_domain_common: "Dom\xE4nen \xE4r vanlig.",
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: "Medlemskapet \xE4r fullt.",
    organization_minimum_permissions_needed: "Du m\xE5ste ha tillr\xE4ckligt med beh\xF6righeter.",
    passkey_already_exists: "Passnyckeln finns redan.",
    passkey_not_supported: "Passnyckel st\xF6ds inte.",
    passkey_pa_not_supported: "Passnyckel PA st\xF6ds inte.",
    passkey_registration_cancelled: "Registrering av passnyckel avbruten.",
    passkey_retrieval_cancelled: "H\xE4mtning av passnyckel avbruten.",
    passwordComplexity: {
      maximumLength: "Maximal l\xE4ngd",
      minimumLength: "Minimal l\xE4ngd",
      requireLowercase: "Kr\xE4ver sm\xE5 bokst\xE4ver",
      requireNumbers: "Kr\xE4ver siffror",
      requireSpecialCharacter: "Kr\xE4ver specialtecken",
      requireUppercase: "Kr\xE4ver stora bokst\xE4ver",
      sentencePrefix: "L\xF6senordet m\xE5ste inneh\xE5lla"
    },
    phone_number_exists: "Detta telefonnummer \xE4r taget. V\xE4nligen prova ett annat.",
    session_exists: "Du \xE4r redan inloggad.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "Ditt l\xF6senord fungerar, men kunde vara starkare. F\xF6rs\xF6k l\xE4gga till fler tecken.",
      goodPassword: "Ditt l\xF6senord uppfyller alla n\xF6dv\xE4ndiga krav.",
      notEnough: "Ditt l\xF6senord \xE4r inte tillr\xE4ckligt starkt.",
      suggestions: {
        allUppercase: "Anv\xE4nd stora bokst\xE4ver, men inte f\xF6r alla tecken.",
        anotherWord: "L\xE4gg till fler ord som \xE4r mindre vanliga.",
        associatedYears: "Undvik \xE5r som \xE4r associerade med dig.",
        capitalization: "Anv\xE4nd stor bokstav f\xF6r mer \xE4n det f\xF6rsta tecknet.",
        dates: "Undvik datum och \xE5r som \xE4r associerade med dig.",
        l33t: "Undvik f\xF6ruts\xE4gbara bokstavsers\xE4ttningar som '@' f\xF6r 'a'.",
        longerKeyboardPattern: "Anv\xE4nd l\xE4ngre tangentbordsm\xF6nster och \xE4ndra skrivr\xE4ttning flera g\xE5nger.",
        noNeed: "Du kan skapa starka l\xF6senord utan att anv\xE4nda symboler, siffror eller stora bokst\xE4ver.",
        pwned: "Om du anv\xE4nder detta l\xF6senord n\xE5gon annanstans b\xF6r du \xE4ndra det.",
        recentYears: "Undvik de senaste \xE5ren.",
        repeated: "Undvik upprepade ord och tecken.",
        reverseWords: "Undvik omv\xE4nd stavning av vanliga ord.",
        sequences: "Undvik vanliga teckenf\xF6ljder.",
        useWords: "Anv\xE4nd flera ord, men undvik vanliga fraser."
      },
      warnings: {
        common: "Detta \xE4r ett vanligt anv\xE4nt l\xF6senord.",
        commonNames: "Vanliga namn och efternamn \xE4r l\xE4tta att gissa.",
        dates: "Datum \xE4r l\xE4tta att gissa.",
        extendedRepeat: 'Upprepade teckenm\xF6nster som "abcabcabc" \xE4r l\xE4tta att gissa.',
        keyPattern: "Korta tangentbordsm\xF6nster \xE4r l\xE4tta att gissa.",
        namesByThemselves: "Enskilda namn eller efternamn \xE4r l\xE4tta att gissa.",
        pwned: "Ditt l\xF6senord har exponerats genom ett dataintr\xE5ng p\xE5 internet.",
        recentYears: "Senaste \xE5ren \xE4r l\xE4tta att gissa.",
        sequences: 'Vanliga teckenf\xF6ljder som "abc" \xE4r l\xE4tta att gissa.',
        similarToCommon: "Detta liknar ett vanligt anv\xE4nt l\xF6senord.",
        simpleRepeat: 'Upprepade tecken som "aaa" \xE4r l\xE4tta att gissa.',
        straightRow: "Raka rader av tangenter p\xE5 ditt tangentbord \xE4r l\xE4tta att gissa.",
        topHundred: "Detta \xE4r ett ofta anv\xE4nt l\xF6senord.",
        topTen: "Detta \xE4r ett mycket anv\xE4nt l\xF6senord.",
        userInputs: "Det b\xF6r inte finnas n\xE5gra personliga eller sidrelaterade data.",
        wordByItself: "Enskilda ord \xE4r l\xE4tta att gissa."
      }
    }
  },
  userButton: {
    action__addAccount: "L\xE4gg till konto",
    action__manageAccount: "Hantera konto",
    action__signOut: "Logga ut",
    action__signOutAll: "Logga ut fr\xE5n alla konton"
  },
  userProfile: {
    backupCodePage: {
      actionLabel__copied: "Kopierat!",
      actionLabel__copy: "Kopiera alla",
      actionLabel__download: "Ladda ner .txt",
      actionLabel__print: "Skriv ut",
      infoText1: "Backupkoder kommer att aktiveras f\xF6r detta konto.",
      infoText2: "H\xE5ll backupkoderna hemliga och f\xF6rvara dem s\xE4kert. Du kan generera nya backupkoder om du misst\xE4nker att de har komprometterats.",
      subtitle__codelist: "F\xF6rvara dem s\xE4kert och h\xE5ll dem hemliga.",
      successMessage: "Backupkoder \xE4r nu aktiverade. Du kan anv\xE4nda en av dessa f\xF6r att logga in p\xE5 ditt konto om du f\xF6rlorar \xE5tkomsten till din autentiseringsenhet. Varje kod kan endast anv\xE4ndas en g\xE5ng.",
      successSubtitle: "Du kan anv\xE4nda en av dessa f\xF6r att logga in p\xE5 ditt konto om du f\xF6rlorar \xE5tkomsten till din autentiseringsenhet.",
      title: "L\xE4gg till backupkodverifiering",
      title__codelist: "Backupkoder"
    },
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "V\xE4lj en leverant\xF6r f\xF6r att ansluta ditt konto.",
      formHint__noAccounts: "Det finns inga tillg\xE4ngliga externa kontoleverant\xF6rer.",
      removeResource: {
        messageLine1: "{{identifier}} kommer att tas bort fr\xE5n detta konto.",
        messageLine2: "Du kommer inte l\xE4ngre att kunna anv\xE4nda detta anslutna konto och alla beroende funktioner kommer att sluta fungera.",
        successMessage: "{{connectedAccount}} har tagits bort fr\xE5n ditt konto.",
        title: "Ta bort anslutet konto"
      },
      socialButtonsBlockButton: "Anslut {{provider|titleize}} konto",
      successMessage: "Leverant\xF6ren har lagts till i ditt konto.",
      title: "L\xE4gg till anslutet konto"
    },
    deletePage: {
      actionDescription: 'Skriv "Radera konto" nedan f\xF6r att forts\xE4tta.',
      confirm: "Radera konto",
      messageLine1: "\xC4r du s\xE4ker p\xE5 att du vill radera ditt konto?",
      messageLine2: "Denna \xE5tg\xE4rd \xE4r permanent och kan inte \xE5ngras.",
      title: "Radera konto"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "Ett e-postmeddelande med en verifieringskod kommer att skickas till denna e-postadress.",
        formSubtitle: "Ange verifieringskoden som skickats till {{identifier}}",
        formTitle: "Verifieringskod",
        resendButton: "Skicka kod igen",
        successMessage: "E-postadressen {{identifier}} har lagts till i ditt konto."
      },
      emailLink: {
        formHint: "Ett e-postmeddelande med en verifieringsl\xE4nk kommer att skickas till denna e-postadress.",
        formSubtitle: "Klicka p\xE5 verifieringsl\xE4nken i e-postmeddelandet som skickats till {{identifier}}",
        formTitle: "Verifieringsl\xE4nk",
        resendButton: "Skicka l\xE4nken igen",
        successMessage: "E-postadressen {{identifier}} har lagts till i ditt konto."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} kommer att tas bort fr\xE5n detta konto.",
        messageLine2: "Du kommer inte l\xE4ngre att kunna logga in med denna e-postadress.",
        successMessage: "{{emailAddress}} har tagits bort fr\xE5n ditt konto.",
        title: "Ta bort e-postadress"
      },
      title: "L\xE4gg till e-postadress",
      verifyTitle: "Verifiera e-postadress"
    },
    formButtonPrimary__add: "L\xE4gg till",
    formButtonPrimary__continue: "Forts\xE4tt",
    formButtonPrimary__finish: "Slutf\xF6r",
    formButtonPrimary__remove: "Ta bort",
    formButtonPrimary__save: "Spara",
    formButtonReset: "Avbryt",
    mfaPage: {
      formHint: "V\xE4lj en metod att l\xE4gga till.",
      title: "L\xE4gg till tv\xE5stegsverifiering"
    },
    mfaPhoneCodePage: {
      backButton: "Anv\xE4nd befintligt telefonnummer",
      primaryButton__addPhoneNumber: "L\xE4gg till ett telefonnummer",
      removeResource: {
        messageLine1: "{{identifier}} kommer inte l\xE4ngre att ta emot verifieringskoder vid inloggning.",
        messageLine2: "Ditt konto kan vara mindre s\xE4kert. \xC4r du s\xE4ker p\xE5 att du vill forts\xE4tta?",
        successMessage: "SMS-kod tv\xE5stegsverifiering har tagits bort f\xF6r {{mfaPhoneCode}}",
        title: "Ta bort tv\xE5stegsverifiering"
      },
      subtitle__availablePhoneNumbers: "V\xE4lj ett telefonnummer att registrera f\xF6r SMS-kod tv\xE5stegsverifiering.",
      subtitle__unavailablePhoneNumbers: "Det finns inga tillg\xE4ngliga telefonnummer att registrera f\xF6r SMS-kod tv\xE5stegsverifiering.",
      successMessage1: "N\xE4r du loggar in kommer du att beh\xF6va ange en verifieringskod som skickats till detta telefonnummer som ett ytterligare steg.",
      successMessage2: "Spara dessa s\xE4kerhetskoder och f\xF6rvara dem p\xE5 ett s\xE4kert st\xE4lle. Om du f\xF6rlorar tillg\xE5ng till din autentiseringsenhet kan du anv\xE4nda s\xE4kerhetskoder f\xF6r att logga in.",
      successTitle: "SMS-kodsverifiering aktiverad",
      title: "L\xE4gg till SMS-kodverifiering"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Skanna QR-kod ist\xE4llet",
        buttonUnableToScan__nonPrimary: "Kan inte skanna QR-kod?",
        infoText__ableToScan: "Konfigurera en ny inloggningsmetod i din autentiseringsapp och skanna f\xF6ljande QR-kod f\xF6r att l\xE4nka den till ditt konto.",
        infoText__unableToScan: "Konfigurera en ny inloggningsmetod i din autentiseringsapp och ange nyckeln nedan.",
        inputLabel__unableToScan1: "Se till att tidsbaserade eller eng\xE5ngsl\xF6senord \xE4r aktiverade och slutf\xF6r sedan l\xE4nkningen till ditt konto.",
        inputLabel__unableToScan2: "Alternativt, om din autentiseringsapp st\xF6djer TOTP URI kan du ocks\xE5 kopiera hela URI."
      },
      removeResource: {
        messageLine1: "Verifieringskoder fr\xE5n denna autentiseringsapp kommer inte l\xE4ngre att kr\xE4vas vid inloggning.",
        messageLine2: "Ditt konto kan vara mindre s\xE4kert. \xC4r du s\xE4ker p\xE5 att du vill forts\xE4tta?",
        successMessage: "Tv\xE5stegsverifiering via autentiseringsapp har tagits bort.",
        title: "Ta bort tv\xE5stegsverifiering"
      },
      successMessage: "Tv\xE5stegsverifiering \xE4r nu aktiverat. Vid inloggning beh\xF6ver du ange en verifieringskod fr\xE5n denna autentiseringsapp som ett extra steg.",
      title: "L\xE4gg till autentiseringsapp",
      verifySubtitle: "Ange verifieringskoden genererad av din autentiseringsapp",
      verifyTitle: "Verifieringskod"
    },
    mobileButton__menu: "Meny",
    navbar: {
      account: "Profil",
      billing: void 0,
      description: "Hantera din kontoinformation.",
      security: "S\xE4kerhet",
      title: "Konto"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "{{name}} kommer att tas bort fr\xE5n detta konto.",
        title: "Ta bort passkey"
      },
      subtitle__rename: "Du kan \xE4ndra passkey-namnet f\xF6r att g\xF6ra det l\xE4ttare att hitta.",
      title__rename: "Byt namn p\xE5 Passkey"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Det rekommenderas att logga ut fr\xE5n alla andra enheter som kan ha anv\xE4nt ditt gamla l\xF6senord.",
      readonly: "Ditt l\xF6senord kan f\xF6r n\xE4rvarande inte redigeras eftersom du endast kan logga in via f\xF6retagsanslutningen.",
      successMessage__set: "Ditt l\xF6senord har angetts.",
      successMessage__signOutOfOtherSessions: "Alla andra enheter har loggats ut.",
      successMessage__update: "Ditt l\xF6senord har uppdaterats.",
      title__set: "Ange l\xF6senord",
      title__update: "Byt l\xF6senord"
    },
    phoneNumberPage: {
      infoText: "Ett textmeddelande med en verifieringsl\xE4nk kommer att skickas till detta telefonnummer.",
      removeResource: {
        messageLine1: "{{identifier}} kommer att tas bort fr\xE5n detta konto.",
        messageLine2: "Du kommer inte l\xE4ngre att kunna logga in med detta telefonnummer.",
        successMessage: "{{phoneNumber}} har tagits bort fr\xE5n ditt konto.",
        title: "Ta bort telefonnummer"
      },
      successMessage: "{{identifier}} har lagts till i ditt konto.",
      title: "L\xE4gg till telefonnummer",
      verifySubtitle: "Ange verifieringskoden som skickats till {{identifier}}",
      verifyTitle: "Verifiera telefonnummer"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "Ladda upp en JPG, PNG, GIF, eller WEBP bild som \xE4r mindre \xE4n 10 MB",
      imageFormDestructiveActionSubtitle: "Ta bort bild",
      imageFormSubtitle: "Ladda upp bild",
      imageFormTitle: "Profilbild",
      readonly: "Din profilinformation har tillhandah\xE5llits av f\xF6retagsanslutningen och kan inte redigeras.",
      successMessage: "Din profil har uppdaterats.",
      title: "Uppdatera profil"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Logga ut fr\xE5n enhet",
        title: "Aktiva enheter"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "F\xF6rs\xF6k igen",
        actionLabel__reauthorize: "Autentisera nu",
        destructiveActionTitle: "Ta bort",
        primaryButton: "Anslut konto",
        subtitle__disconnected: "Detta konto har kopplats bort.",
        subtitle__reauthorize: "De n\xF6dv\xE4ndiga beh\xF6righeterna har uppdaterats, och du kan uppleva begr\xE4nsad funktionalitet. V\xE4nligen ge ny auktorisering till denna applikation f\xF6r att undvika problem",
        title: "Anslutna konton"
      },
      dangerSection: {
        deleteAccountButton: "Radera konto",
        title: "Radera konto"
      },
      emailAddressesSection: {
        destructiveAction: "Ta bort e-postadress",
        detailsAction__nonPrimary: "S\xE4tt som prim\xE4r",
        detailsAction__primary: "Fullborda verifiering",
        detailsAction__unverified: "Fullborda verifiering",
        primaryButton: "L\xE4gg till en e-postadress",
        title: "E-postadresser"
      },
      enterpriseAccountsSection: {
        title: "Enterprise accounts"
      },
      headerTitle__account: "Konto",
      headerTitle__security: "S\xE4kerhet",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "\xC5tergenerera koder",
          headerTitle: "S\xE4kerhetskopieringskoder",
          subtitle__regenerate: "F\xE5 en ny upps\xE4ttning s\xE4kra s\xE4kerhetskopieringskoder. Tidigare koder kommer att raderas och kan inte anv\xE4ndas.",
          title__regenerate: "\xC5tergenerera s\xE4kerhetskopieringskoder"
        },
        phoneCode: {
          actionLabel__setDefault: "Ange som standard",
          destructiveActionLabel: "Ta bort telefonnummer"
        },
        primaryButton: "L\xE4gg till tv\xE5stegsverifiering",
        title: "Tv\xE5stegsverifiering",
        totp: {
          destructiveActionTitle: "Ta bort",
          headerTitle: "Autentiseringsapp"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Ta bort",
        menuAction__rename: "Byt namn",
        primaryButton: void 0,
        title: "Passkeys"
      },
      passwordSection: {
        primaryButton__setPassword: "St\xE4ll in l\xF6senord",
        primaryButton__updatePassword: "Byt l\xF6senord",
        title: "L\xF6senord"
      },
      phoneNumbersSection: {
        destructiveAction: "Ta bort telefonnummer",
        detailsAction__nonPrimary: "S\xE4tt som prim\xE4r",
        detailsAction__primary: "Fullborda verifiering",
        detailsAction__unverified: "Fullborda verifiering",
        primaryButton: "L\xE4gg till ett telefonnummer",
        title: "Telefonnummer"
      },
      profileSection: {
        primaryButton: "Uppdatera profil",
        title: "Profil"
      },
      usernameSection: {
        primaryButton__setUsername: "S\xE4tt anv\xE4ndarnamn",
        primaryButton__updateUsername: "\xC4ndra anv\xE4ndarnamn",
        title: "Anv\xE4ndarnamn"
      },
      web3WalletsSection: {
        destructiveAction: "Ta bort pl\xE5nbok",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Web3 pl\xE5nb\xF6cker",
        title: "Web3 pl\xE5nb\xF6cker"
      }
    },
    usernamePage: {
      successMessage: "Ditt anv\xE4ndarnamn har uppdaterats.",
      title__set: "Uppdatera anv\xE4ndarnamn",
      title__update: "Uppdatera anv\xE4ndarnamn"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} kommer att tas bort fr\xE5n detta konto.",
        messageLine2: "Du kommer inte l\xE4ngre att kunna logga in med denna web3-pl\xE5nbok.",
        successMessage: "{{web3Wallet}} har tagits bort fr\xE5n ditt konto.",
        title: "Ta bort web3-pl\xE5nbok"
      },
      subtitle__availableWallets: "V\xE4lj en web3-pl\xE5nbok att ansluta till ditt konto.",
      subtitle__unavailableWallets: "Det finns inga tillg\xE4ngliga web3-pl\xE5nb\xF6cker.",
      successMessage: "Pl\xE5nboken har lagts till i ditt konto.",
      title: "L\xE4gg till web3-pl\xE5nbok",
      web3WalletButtonsBlockButton: "{{provider|titleize}}"
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  svSE
};
//# sourceMappingURL=sv-SE.mjs.map