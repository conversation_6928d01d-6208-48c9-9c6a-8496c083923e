"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["662"],{7389:function(e,t,r){r.d(t,{v:()=>n});var i=r(9109),o=r(4562),a=r(9541);let n=e=>{let{onBackLinkClick:t}=e;return(0,i.tZ)(o._,{cardTitle:(0,a.localizationKeys)("signIn.alternativeMethods.getHelp.title"),cardSubtitle:(0,a.localizationKeys)("signIn.alternativeMethods.getHelp.content"),onBackLinkClick:t})}},5518:function(e,t,r){r.d(t,{Vh:()=>f,Vs:()=>k,bx:()=>h,mQ:()=>Z,s2:()=>v,t3:()=>s,xT:()=>u}),r(5027);var i=r(2208),o=r(7772),a=r(7623),n=r(577);let l=e=>t=>"safeIdentifier"in t&&t.safeIdentifier===e;function c(e){if((0,i.iW)()){let t=e.find(({strategy:e})=>"passkey"===e);if(t)return t}return null}function s(e,t,r){return e&&0!==e.length?r===o.kJ.Password?function(e,t){let r=c(e);if(r)return r;let i=e.sort(n.sZ)[0];return"password"===i.strategy?i:e.find(l(t))||i||null}(e,t):function(e,t){let r=c(e);if(r)return r;let i=e.sort(n.b8),o=i.find(l(t));if(o)return o;let a=i[0];return"email_link"===a.strategy?a:e.find(l(t))||a||null}(e,t):null}let d=["passkey","email_code","password","phone_code","email_link"];function u(e){return!!e&&d.includes(e.strategy)}function h(e){if(!e||0===e.length)return null;let t=e.find(e=>"totp"===e.strategy);if(t)return t;let r=e.find(e=>"phone_code"===e.strategy);return r||e[0]}let p=["reset_password_phone_code","reset_password_email_code"],f=e=>!!e&&p.includes(e),y=e=>/^\S+@\S+\.\S+$/.test(e);function v(e){return"tel"===e.type?"phoneNumber":y(e.value)?"emailAddress":"username"}let k=(e,t,r)=>{if(!t)return null;let i=e.find(e=>"strategy"===e.id)?.value;if(i&&"phone_code"!==i)return null;let o=e.find(e=>e.id===r)?.value;if(!o||!o?.startsWith("+"))return null;let n=(0,a.jR)(o,t);return"sms"===n?null:n},Z=(e,t,r)=>{if(!e||!t||"phoneNumber"!==t||!r||!r?.startsWith("+"))return null;let i=(0,a.jR)(r,e);return"sms"===i?null:i}},1419:function(e,t,r){r.r(t),r.d(t,{UserVerification:()=>ei,UserVerificationModal:()=>eo});var i=r(9109),o=r(9144),a=r(1576),n=r(9541),l=r(4676),c=r(2672),s=r(4562),d=r(1455),u=r(1085),h=r(5518),p=r(8246),f=r(5482),y=r(4455),v=r(2654),k=r(4174),Z=r(7623),m=r(2208);let C=(e,t)=>!!e&&!!t&&("email_code"===e.strategy&&"email_code"===t.strategy?e.emailAddressId===t.emailAddressId:"phone_code"===e.strategy&&"phone_code"===t.strategy?e.phoneNumberId===t.phoneNumberId:e.strategy===t.strategy),b=(e,t)=>!!e&&!!t&&("phone_code"===e.strategy&&"phone_code"===t.strategy?e.phoneNumberId===t.phoneNumberId:e.strategy===t.strategy);function g({filterOutFactor:e,supportedFirstFactors:t}){let r=t?t.filter(e=>!(0,h.Vh)(e.strategy)):[],i=r&&r.length>0,a=(0,o.useMemo)(()=>t?t.filter(e=>!e.strategy.startsWith("oauth_")).filter(e=>(0,h.xT)(e)).filter(t=>!C(t,e)).filter(e=>"passkey"!==e.strategy||(0,m.iW)()).sort(Z.U6):[],[t,e]);return{hasAnyStrategy:i,hasFirstParty:a&&a.length>0,firstPartyFactors:a}}var w=r(3799),M=r(2464);let _=()=>{let{level:e}=(0,a.useUserVerification)();return(0,o.useMemo)(()=>({level:e||"second_factor"}),[e])},B=()=>{let{session:e}=(0,w.kP)(),t=_();return{...(0,M.ib)(e?e.startVerification:void 0,t,{throttleTime:300})}};function S(e){let t=t=>{let{isLoading:r,data:o}=B();return r||!o?(0,i.tZ)(d.W,{}):(0,i.tZ)(e,{...t})},r=e.displayName||e.name||"Component";return e.displayName=r,t.displayName=r,t}let A=e=>{let{onBackLinkClick:t}=e;return(0,i.tZ)(s._,{cardTitle:(0,n.localizationKeys)("reverification.alternativeMethods.getHelp.title"),cardSubtitle:(0,n.localizationKeys)("reverification.alternativeMethods.getHelp.content"),onBackLinkClick:t})},z=(e,t)=>{let[r,a]=o.useState(!1),n=o.useCallback(()=>a(e=>!e),[a]);return r?(0,i.tZ)(A,{onBackLinkClick:n}):(0,i.tZ)(e,{...t,onHavingTroubleClick:n})},K=e=>z(F,{...e}),F=e=>{let{onBackLinkClick:t,onHavingTroubleClick:r,onFactorSelected:o}=e,a=(0,c.useCardState)(),{data:l}=B(),{firstPartyFactors:s,hasAnyStrategy:d}=g({filterOutFactor:e?.currentFactor,supportedFirstFactors:l?.supportedFirstFactors});return(0,i.tZ)(n.Flow.Part,{part:"alternativeMethods",children:(0,i.BX)(y.Z.Root,{children:[(0,i.BX)(y.Z.Content,{children:[(0,i.BX)(v.h.Root,{children:[(0,i.tZ)(v.h.Title,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.title")}),(0,i.tZ)(v.h.Subtitle,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.subtitle")})]}),(0,i.tZ)(y.Z.Alert,{children:a.error}),(0,i.tZ)(n.Flex,{direction:"col",elementDescriptor:n.descriptors.main,gap:6,children:(0,i.BX)(n.Col,{gap:4,children:[d&&(0,i.tZ)(n.Flex,{elementDescriptor:n.descriptors.alternativeMethods,direction:"col",gap:2,children:s.map((e,t)=>{var r;return(0,i.tZ)(p.$,{leftIcon:(r=e,({email_code:k.GT,phone_code:k.iU,password:k.kh,passkey:k.IG})[r.strategy]),textLocalizationKey:function(e){switch(e.strategy){case"email_code":return(0,n.localizationKeys)("reverification.alternativeMethods.blockButton__emailCode",{identifier:(0,Z.HT)(e.safeIdentifier)||""});case"phone_code":return(0,n.localizationKeys)("reverification.alternativeMethods.blockButton__phoneCode",{identifier:(0,Z.HT)(e.safeIdentifier)||""});case"password":return(0,n.localizationKeys)("reverification.alternativeMethods.blockButton__password");case"passkey":return(0,n.localizationKeys)("reverification.alternativeMethods.blockButton__passkey");default:throw Error(`Invalid sign in strategy: "${e.strategy}"`)}}(e),elementDescriptor:n.descriptors.alternativeMethodsBlockButton,textElementDescriptor:n.descriptors.alternativeMethodsBlockButtonText,arrowElementDescriptor:n.descriptors.alternativeMethodsBlockButtonArrow,textVariant:"buttonLarge",isDisabled:a.isLoading,onClick:()=>{a.setError(void 0),o(e)}},t)})}),t&&(0,i.tZ)(f.h,{boxElementDescriptor:n.descriptors.backRow,linkElementDescriptor:n.descriptors.backLink,onClick:t})]})})]}),(0,i.tZ)(y.Z.Footer,{children:(0,i.BX)(y.Z.Action,{elementId:"havingTrouble",children:[(0,i.tZ)(y.Z.ActionText,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.actionText")}),(0,i.tZ)(y.Z.ActionLink,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.actionLink"),onClick:r})]})})]})})};var L=r(431),T=r(7389),I=r(4152),P=r(3234);let R=()=>{let{afterVerification:e}=(0,a.useUserVerification)(),t=(0,P.H)(),{setActive:r}=(0,w.cL)(),{setCache:i}=B(),{navigate:n}=(0,l.useRouter)();return{handleVerificationResponse:(0,o.useCallback)(async o=>{switch(i({data:o,isLoading:!1,isValidating:!1,error:null,cachedAt:Date.now()}),o.status){case"complete":return await r({session:o.session.id}),e?.();case"needs_second_factor":return n("./factor-two");default:return console.error((0,I.Ws)(o.status,t))}},[n,r,t])}};function X(e){let{onShowAlternativeMethodsClick:t}=e,{session:r}=(0,w.kP)(),{handleVerificationResponse:a}=R(),l=(0,c.useCardState)(),[s,d]=o.useState(!1),u=o.useCallback(()=>d(e=>!e),[d]),h=(0,Z.Yp)("password","",{type:"password",label:(0,n.localizationKeys)("formFieldLabel__password"),placeholder:(0,n.localizationKeys)("formFieldInputPlaceholder__password")}),p=async e=>(e.preventDefault(),r?.attemptFirstFactorVerification({strategy:"password",password:h.value}).then(a).catch(e=>Z.S3(e,[h],l.setError)));return s?(0,i.tZ)(T.v,{onBackLinkClick:u}):(0,i.tZ)(n.Flow.Part,{part:"password",children:(0,i.BX)(y.Z.Root,{children:[(0,i.BX)(y.Z.Content,{children:[(0,i.BX)(v.h.Root,{showLogo:!0,children:[(0,i.tZ)(v.h.Title,{localizationKey:(0,n.localizationKeys)("reverification.password.title")}),(0,i.tZ)(v.h.Subtitle,{localizationKey:(0,n.localizationKeys)("reverification.password.subtitle")})]}),(0,i.tZ)(y.Z.Alert,{children:l.error}),(0,i.BX)(n.Col,{elementDescriptor:n.descriptors.main,gap:4,children:[(0,i.BX)(L.l.Root,{onSubmit:p,gap:8,children:[(0,i.tZ)(L.l.ControlRow,{elementId:h.id,children:(0,i.tZ)(L.l.PasswordInput,{...h.props,autoFocus:!0})}),(0,i.tZ)(L.l.SubmitButton,{hasArrow:!0})]}),(0,i.tZ)(y.Z.Action,{elementId:t?"alternativeMethods":"havingTrouble",children:(0,i.tZ)(y.Z.ActionLink,{localizationKey:(0,n.localizationKeys)(t?"reverification.password.actionLink":"reverification.alternativeMethods.actionLink"),onClick:t||u})})]})]}),(0,i.tZ)(y.Z.Footer,{})]})})}function E(e,t){return"primary"in e&&e.primary&&!("primary"in t&&t.primary)?-1:"primary"in t&&t.primary&&!("primary"in e&&e.primary)?1:0}var x=r(3465);let V=e=>{let{session:t}=(0,w.kP)(),r=(0,c.useCardState)(),{handleVerificationResponse:a}=R();o.useEffect(()=>{e.factorAlreadyPrepared||n()},[]);let n=()=>{t.prepareFirstFactorVerification(e.factor).then(()=>e.onFactorPrepare()).catch(e=>(0,Z.S3)(e,[],r.setError))};return(0,i.tZ)(x.U,{cardTitle:e.cardTitle,cardSubtitle:e.cardSubtitle,inputLabel:e.inputLabel,resendButton:e.resendButton,onCodeEntryFinishedAction:(r,i,o)=>{t.attemptFirstFactorVerification({strategy:e.factor.strategy,code:r}).then(async e=>(await i(),a(e))).catch(o)},onResendCodeClicked:n,safeIdentifier:e.factor.safeIdentifier,profileImageUrl:t?.user?.imageUrl,onShowAlternativeMethodsClicked:e.onShowAlternativeMethodsClicked,showAlternativeMethods:e.showAlternativeMethods,onBackLinkClicked:e.onBackLinkClicked})},D=e=>(0,i.tZ)(n.Flow.Part,{part:"emailCode",children:(0,i.tZ)(V,{...e,cardTitle:(0,n.localizationKeys)("reverification.emailCode.title"),cardSubtitle:(0,n.localizationKeys)("reverification.emailCode.subtitle"),inputLabel:(0,n.localizationKeys)("reverification.emailCode.formTitle"),resendButton:(0,n.localizationKeys)("reverification.emailCode.resendButton")})}),N=e=>{let{onShowAlternativeMethodsClicked:t}=e,{session:r}=(0,w.kP)(),{__internal_isWebAuthnSupported:o}=(0,w.cL)(),{handleVerificationResponse:a}=R(),l=(0,c.useCardState)(),s=()=>{r?.verifyWithPasskey().then(e=>a(e)).catch(e=>Z.S3(e,[],l.setError))};return(0,i.BX)(y.Z.Root,{children:[(0,i.BX)(y.Z.Content,{children:[(0,i.BX)(v.h.Root,{showLogo:!0,children:[(0,i.tZ)(v.h.Title,{localizationKey:(0,n.localizationKeys)("reverification.passkey.title")}),(0,i.tZ)(v.h.Subtitle,{localizationKey:(0,n.localizationKeys)("reverification.passkey.subtitle")})]}),(0,i.tZ)(y.Z.Alert,{children:l.error}),(0,i.tZ)(n.Col,{elementDescriptor:n.descriptors.main,gap:8,children:(0,i.tZ)(L.l.Root,{children:(0,i.BX)(n.Col,{gap:3,children:[(0,i.tZ)(n.Button,{type:"button",onClick:e=>{e.preventDefault(),s()},localizationKey:(0,n.localizationKeys)("reverification.passkey.blockButton__passkey"),hasArrow:!0}),(0,i.tZ)(y.Z.Action,{elementId:"alternativeMethods",children:t&&(0,i.tZ)(y.Z.ActionLink,{localizationKey:(0,n.localizationKeys)("footerActionLink__useAnotherMethod"),onClick:t})})]})})})]}),(0,i.tZ)(y.Z.Footer,{})]})},U=e=>(0,i.tZ)(n.Flow.Part,{part:"phoneCode",children:(0,i.tZ)(V,{...e,cardTitle:(0,n.localizationKeys)("reverification.phoneCode.title"),cardSubtitle:(0,n.localizationKeys)("reverification.phoneCode.subtitle"),inputLabel:(0,n.localizationKeys)("reverification.phoneCode.formTitle"),resendButton:(0,n.localizationKeys)("reverification.phoneCode.resendButton"),showAlternativeMethods:e.showAlternativeMethods})}),W=e=>{if(!e)return"";let t=e.strategy;return"emailAddressId"in e&&(t+=e.emailAddressId),"phoneNumberId"in e&&(t+=e.phoneNumberId),t},H=["password","email_code","phone_code","passkey"],$=S((0,c.withCardStateProvider)(function(){let{data:e}=B(),t=(0,c.useCardState)(),{navigate:r}=(0,l.useRouter)(),n=o.useRef(""),p=(0,o.useMemo)(()=>e.supportedFirstFactors?.filter(e=>H.includes(e.strategy))?.sort(E)||null,[e.supportedFirstFactors]),{preferredSignInStrategy:f}=(0,a.useEnvironment)().displayConfig,[{currentFactor:y},v]=o.useState(()=>({currentFactor:(0,h.t3)(p,null,f),prevCurrentFactor:void 0})),{hasAnyStrategy:k,hasFirstParty:Z}=g({filterOutFactor:y,supportedFirstFactors:p}),[m,C]=o.useState(()=>!y||!(0,h.xT)(y)),b=k?()=>{t.setError(void 0),C(e=>!e)}:void 0,w=()=>{n.current=W(y)},M=e=>{v(t=>({currentFactor:e,prevCurrentFactor:t.currentFactor}))};if((0,o.useEffect)(()=>{"needs_second_factor"===e.status&&r("factor-two")},[]),!y)return(0,i.tZ)(s._,{cardTitle:(0,u.u1)("reverification.noAvailableMethods.title"),cardSubtitle:(0,u.u1)("reverification.noAvailableMethods.subtitle"),message:(0,u.u1)("reverification.noAvailableMethods.message"),shouldNavigateBack:!1});if(m){let e=(0,h.xT)(y);return(0,i.tZ)(K,{onBackLinkClick:e?()=>{t.setError(void 0),b?.()}:void 0,onFactorSelected:e=>{M(e),b?.()},currentFactor:y})}switch(y?.strategy){case"password":return(0,i.tZ)(X,{onShowAlternativeMethodsClick:b});case"email_code":return(0,i.tZ)(D,{factorAlreadyPrepared:n.current===W(y),onFactorPrepare:w,onShowAlternativeMethodsClicked:b,factor:y,showAlternativeMethods:Z});case"phone_code":return(0,i.tZ)(U,{factorAlreadyPrepared:n.current===W(y),onFactorPrepare:w,onShowAlternativeMethodsClicked:b,factor:y,showAlternativeMethods:Z});case"passkey":return(0,i.tZ)(N,{onShowAlternativeMethodsClicked:b});default:return(0,i.tZ)(d.W,{})}})),j=e=>{let t=(0,c.useCardState)(),{session:r}=(0,w.kP)(),{handleVerificationResponse:a}=R();o.useEffect(()=>{!e.factorAlreadyPrepared&&n?.()},[]);let n=e.prepare?()=>e.prepare?.().then(()=>e.onFactorPrepare()).catch(e=>Z.S3(e,[],t.setError)):void 0;return(0,i.tZ)(x.U,{cardTitle:e.cardTitle,cardSubtitle:e.cardSubtitle,resendButton:e.resendButton,inputLabel:e.inputLabel,onCodeEntryFinishedAction:(t,i,o)=>{r.attemptSecondFactorVerification({strategy:e.factor.strategy,code:t}).then(async e=>(await i(),a(e))).catch(o)},onResendCodeClicked:n,safeIdentifier:"safeIdentifier"in e.factor?e.factor.safeIdentifier:void 0,profileImageUrl:r?.user?.imageUrl,onShowAlternativeMethodsClicked:e.onShowAlternativeMethodsClicked,showAlternativeMethods:e.showAlternativeMethods})};function G(e){return(0,i.tZ)(n.Flow.Part,{part:"totp2Fa",children:(0,i.tZ)(j,{...e,cardTitle:(0,n.localizationKeys)("reverification.totpMfa.title"),cardSubtitle:(0,n.localizationKeys)("reverification.totpMfa.subtitle"),inputLabel:(0,n.localizationKeys)("reverification.totpMfa.formTitle"),showAlternativeMethods:e.showAlternativeMethods})})}let O=e=>{let[t,r]=o.useState(!1),a=o.useCallback(()=>r(e=>!e),[r]);return t?(0,i.tZ)(A,{onBackLinkClick:a}):(0,i.tZ)(Q,{supportedSecondFactors:e.supportedSecondFactors,onBackLinkClick:e.onBackLinkClick,onFactorSelected:e.onFactorSelected,onHavingTroubleClick:a})},Q=e=>{let{supportedSecondFactors:t,onHavingTroubleClick:r,onFactorSelected:o,onBackLinkClick:a}=e,l=(0,c.useCardState)();return(0,i.tZ)(n.Flow.Part,{part:"alternativeMethods",children:(0,i.BX)(y.Z.Root,{children:[(0,i.BX)(y.Z.Content,{children:[(0,i.BX)(v.h.Root,{showLogo:!0,children:[(0,i.tZ)(v.h.Title,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.title")}),(0,i.tZ)(v.h.Subtitle,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.subtitle")})]}),(0,i.tZ)(y.Z.Alert,{children:l.error}),(0,i.BX)(n.Col,{elementDescriptor:n.descriptors.main,gap:3,children:[(0,i.tZ)(n.Col,{gap:2,children:t?.sort(Z.Q0).map((e,t)=>i.tZ(p.$,{textLocalizationKey:function(e){switch(e.strategy){case"phone_code":return(0,n.localizationKeys)("reverification.alternativeMethods.blockButton__phoneCode",{identifier:(0,Z.HT)(e.safeIdentifier)||""});case"totp":return(0,n.localizationKeys)("reverification.alternativeMethods.blockButton__totp");case"backup_code":return(0,n.localizationKeys)("reverification.alternativeMethods.blockButton__backupCode");default:throw Error(`Invalid verification strategy: "${e.strategy}"`)}}(e),elementDescriptor:n.descriptors.alternativeMethodsBlockButton,textElementDescriptor:n.descriptors.alternativeMethodsBlockButtonText,arrowElementDescriptor:n.descriptors.alternativeMethodsBlockButtonArrow,isDisabled:l.isLoading,onClick:()=>o(e)},t))}),(0,i.tZ)(y.Z.Action,{elementId:"alternativeMethods",children:a&&(0,i.tZ)(y.Z.ActionLink,{localizationKey:(0,n.localizationKeys)("backButton"),onClick:e.onBackLinkClick})})]})]}),(0,i.tZ)(y.Z.Footer,{children:(0,i.BX)(y.Z.Action,{elementId:"havingTrouble",children:[(0,i.tZ)(y.Z.ActionText,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.actionText")}),(0,i.tZ)(y.Z.ActionLink,{localizationKey:(0,n.localizationKeys)("reverification.alternativeMethods.actionLink"),onClick:r})]})})]})})},Y=e=>{let{onShowAlternativeMethodsClicked:t}=e,{session:r}=(0,w.kP)(),{handleVerificationResponse:o}=R(),a=(0,c.useCardState)(),l=(0,Z.Yp)("code","",{type:"text",label:(0,n.localizationKeys)("formFieldLabel__backupCode"),isRequired:!0});return(0,i.BX)(y.Z.Root,{children:[(0,i.BX)(y.Z.Content,{children:[(0,i.BX)(v.h.Root,{showLogo:!0,children:[(0,i.tZ)(v.h.Title,{localizationKey:(0,n.localizationKeys)("reverification.backupCodeMfa.title")}),(0,i.tZ)(v.h.Subtitle,{localizationKey:(0,n.localizationKeys)("reverification.backupCodeMfa.subtitle")})]}),(0,i.tZ)(y.Z.Alert,{children:a.error}),(0,i.tZ)(n.Col,{elementDescriptor:n.descriptors.main,gap:8,children:(0,i.BX)(L.l.Root,{onSubmit:e=>(e.preventDefault(),r.attemptSecondFactorVerification({strategy:"backup_code",code:l.value}).then(o).catch(e=>(0,Z.S3)(e,[l],a.setError))),children:[(0,i.tZ)(L.l.ControlRow,{elementId:l.id,children:(0,i.tZ)(L.l.PlainInput,{...l.props,autoFocus:!0,onActionClicked:t})}),(0,i.BX)(n.Col,{gap:3,children:[(0,i.tZ)(L.l.SubmitButton,{hasArrow:!0}),(0,i.tZ)(y.Z.Action,{elementId:"alternativeMethods",children:t&&(0,i.tZ)(y.Z.ActionLink,{localizationKey:(0,n.localizationKeys)("footerActionLink__useAnotherMethod"),onClick:t})})]})]})})]}),(0,i.tZ)(y.Z.Footer,{})]})},q=e=>{let{session:t}=(0,w.kP)();return(0,i.tZ)(n.Flow.Part,{part:"phoneCode2Fa",children:(0,i.tZ)(j,{...e,cardTitle:(0,n.localizationKeys)("reverification.phoneCodeMfa.title"),cardSubtitle:(0,n.localizationKeys)("reverification.phoneCodeMfa.subtitle"),inputLabel:(0,n.localizationKeys)("reverification.phoneCodeMfa.formTitle"),resendButton:(0,n.localizationKeys)("reverification.phoneCodeMfa.resendButton"),prepare:()=>{let{phoneNumberId:r,strategy:i}=e.factor;return t.prepareSecondFactorVerification({phoneNumberId:r,strategy:i})}})})},J=e=>{if(!e)return"";let t=e.strategy;return"phoneNumberId"in e&&(t+=e.phoneNumberId),t},ee=["phone_code","totp","backup_code"],et=S((0,c.withCardStateProvider)(function(){let{navigate:e}=(0,l.useRouter)(),{data:t}=B(),r=(0,o.useMemo)(()=>t.supportedSecondFactors?.filter(e=>ee.includes(e.strategy))?.sort(E)||null,[t.supportedSecondFactors]),a=o.useRef(""),[n,c]=o.useState(()=>(0,h.bx)(r)),[s,u]=o.useState(!n),p=()=>u(e=>!e),f=(0,o.useMemo)(()=>r?.filter(e=>!b(e,n)),[r,n]),y=()=>{a.current=J(n)},v=(0,o.useMemo)(()=>f&&f.length>0||!1,[f]);if((0,o.useEffect)(()=>{"needs_first_factor"===t.status&&e("../")},[]),!n)return(0,i.tZ)(d.W,{});if(s&&v)return(0,i.tZ)(O,{supportedSecondFactors:f||null,onBackLinkClick:p,onFactorSelected:e=>{c(e),p()}});switch(n?.strategy){case"phone_code":return(0,i.tZ)(q,{factorAlreadyPrepared:a.current===J(n),onFactorPrepare:y,factor:n,onShowAlternativeMethodsClicked:p,showAlternativeMethods:v});case"totp":return(0,i.tZ)(G,{factorAlreadyPrepared:a.current===J(n),onFactorPrepare:y,factor:n,onShowAlternativeMethodsClicked:p,showAlternativeMethods:v});case"backup_code":return(0,i.tZ)(Y,{onShowAlternativeMethodsClicked:p});default:return(0,i.tZ)(d.W,{})}}));function er(){let{invalidate:e}=B();return(0,o.useEffect)(()=>()=>{e()},[]),(0,i.tZ)(n.Flow.Root,{flow:"userVerification",children:(0,i.BX)(l.Switch,{children:[(0,i.tZ)(l.Route,{path:"factor-two",children:(0,i.tZ)(et,{})}),(0,i.tZ)(l.Route,{index:!0,children:(0,i.tZ)($,{})})]})})}er.displayName="UserVerification";let ei=(0,a.withCoreSessionSwitchGuard)(er),eo=e=>(0,i.tZ)(l.Route,{path:"user-verification",children:(0,i.tZ)(a.UserVerificationContext.Provider,{value:{componentName:"UserVerification",...e,routing:"virtual"},children:(0,i.tZ)("div",{children:(0,i.tZ)(ei,{...e,routing:"virtual"})})})})}}]);