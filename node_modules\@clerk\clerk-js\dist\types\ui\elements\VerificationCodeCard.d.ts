import type { PropsWithChildren } from 'react';
import React from 'react';
import type { LocalizationKey } from '../localization';
export type VerificationCodeCardProps = {
    cardTitle: LocalizationKey;
    cardSubtitle: LocalizationKey;
    inputLabel?: LocalizationKey;
    safeIdentifier?: string | undefined | null;
    resendButton?: LocalizationKey;
    alternativeMethodsLabel?: LocalizationKey;
    profileImageUrl?: string;
    onCodeEntryFinishedAction: (code: string, resolve: () => Promise<void>, reject: (err: unknown) => Promise<void>) => void;
    onResendCodeClicked?: React.MouseEventHandler;
    showAlternativeMethods?: boolean;
    onShowAlternativeMethodsClicked?: React.MouseEventHandler;
    onIdentityPreviewEditClicked?: React.MouseEventHandler;
    onBackLinkClicked?: React.MouseEventHandler;
};
export declare const VerificationCodeCard: (props: PropsWithChildren<VerificationCodeCardProps>) => import("@emotion/react/jsx-runtime").JSX.Element;
