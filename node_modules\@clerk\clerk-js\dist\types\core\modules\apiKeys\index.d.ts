import type { APIKeyResource, APIKeysNamespace, CreateAPIKeyParams, GetAPIKeysParams, RevokeAPIKeyParams } from '@clerk/types';
export declare class APIKeys implements APIKeysNamespace {
    /**
     * Returns the base options for the FAPI proxy requests.
     */
    private getBaseFapiProxyOptions;
    getAll(params?: GetAPIKeysParams): Promise<APIKeyResource[]>;
    getSecret(id: string): Promise<string>;
    create(params: CreateAPIKeyParams): Promise<APIKeyResource>;
    revoke(params: RevokeAPIKeyParams): Promise<APIKeyResource>;
}
