import type { Clerk, ClerkOptions, EnvironmentResource } from '@clerk/types';
export type ComponentGuard = (clerk: Clerk, environment?: EnvironmentResource | null, options?: ClerkOptions) => boolean;
export declare const sessionExistsAndSingleSessionModeEnabled: ComponentGuard;
export declare const noUserExists: ComponentGuard;
export declare const noOrganizationExists: ComponentGuard;
export declare const disabledOrganizationsFeature: ComponentGuard;
export declare const disabledBillingFeature: ComponentGuard;
export declare const hasPaidOrgPlans: ComponentGuard;
export declare const hasPaidUserPlans: ComponentGuard;
export declare const disabledAPIKeysFeature: ComponentGuard;
