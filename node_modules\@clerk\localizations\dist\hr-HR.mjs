// src/hr-HR.ts
var hrHR = {
  locale: "hr-HR",
  backButton: "Natrag",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Zada<PERSON>",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Drugi ure\u0111aj za opona\u0161anje",
  badge__primary: "Primarno",
  badge__renewsAt: void 0,
  badge__requiresAction: "Zahtijeva a<PERSON>ciju",
  badge__startsAt: void 0,
  badge__thisDevice: "Ovaj ure\u0111aj",
  badge__unverified: "Nepotvr\u0111eno",
  badge__upcomingPlan: void 0,
  badge__userDevice: "<PERSON><PERSON><PERSON>\u010Dki ure\u0111aj",
  badge__you: "Vi",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "kreiraj organizaciju",
    invitePage: {
      formButtonReset: "Presko\u010Di"
    },
    title: "Kreiraj organizaciju"
  },
  dates: {
    lastDay: "Ju\u010Der u {{ date | timeString('hr-HR') }}",
    next6Days: "{{ date | weekday('hr-HR','long') }} u {{ date | timeString('hr-HR') }}",
    nextDay: "Sutra u {{ date | timeString('hr-HR') }}",
    numeric: "{{ date | numeric('hr-HR') }}",
    previous6Days: "Pro\u0161li {{ date | weekday('hr-HR','long') }} u {{ date | timeString('hr-HR') }}",
    sameDay: "Danas u {{ date | timeString('hr-HR') }}"
  },
  dividerText: "ili",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Koristite drugu metodu",
  footerPageLink__help: "Pomo\u0107",
  footerPageLink__privacy: "Privatnost",
  footerPageLink__terms: "Uvjeti",
  formButtonPrimary: "Nastavi",
  formButtonPrimary__verify: "Potvrdi",
  formFieldAction__forgotPassword: "Zaboravili ste lozinku?",
  formFieldError__matchingPasswords: "Lozinke se podudaraju.",
  formFieldError__notMatchingPasswords: "Lozinke se ne podudaraju.",
  formFieldError__verificationLinkExpired: "Verifikacijska poveznica je istekla. Molimo zatra\u017Eite novu poveznicu.",
  formFieldHintText__optional: "Neobavezno",
  formFieldHintText__slug: "Slug je \u010Ditljiv ID koji mora biti jedinstven. \u010Cesto se koristi u URL-ovima.",
  formFieldInputPlaceholder__backupCode: void 0,
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Izbri\u0161i ra\u010Dun",
  formFieldInputPlaceholder__emailAddress: void 0,
  formFieldInputPlaceholder__emailAddress_username: void 0,
  formFieldInputPlaceholder__emailAddresses: "<EMAIL>, <EMAIL>",
  formFieldInputPlaceholder__firstName: void 0,
  formFieldInputPlaceholder__lastName: void 0,
  formFieldInputPlaceholder__organizationDomain: void 0,
  formFieldInputPlaceholder__organizationDomainEmailAddress: void 0,
  formFieldInputPlaceholder__organizationName: void 0,
  formFieldInputPlaceholder__organizationSlug: "moja-organizacija",
  formFieldInputPlaceholder__password: void 0,
  formFieldInputPlaceholder__phoneNumber: void 0,
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__automaticInvitations: "Omogu\u0107i automatske pozivnice za ovu domenu",
  formFieldLabel__backupCode: "Rezervni kod",
  formFieldLabel__confirmDeletion: "Potvrda",
  formFieldLabel__confirmPassword: "Potvrdi lozinku",
  formFieldLabel__currentPassword: "Trenutna lozinka",
  formFieldLabel__emailAddress: "E-mail adresa",
  formFieldLabel__emailAddress_username: "E-mail adresa ili korisni\u010Dko ime",
  formFieldLabel__emailAddresses: "E-mail adrese",
  formFieldLabel__firstName: "Ime",
  formFieldLabel__lastName: "Prezime",
  formFieldLabel__newPassword: "Nova lozinka",
  formFieldLabel__organizationDomain: "Domena",
  formFieldLabel__organizationDomainDeletePending: "Izbri\u0161i prijedloge i pozivnice na \u010Dekanju",
  formFieldLabel__organizationDomainEmailAddress: "E-mail adresa za verifikaciju",
  formFieldLabel__organizationDomainEmailAddressDescription: "Unesite e-mail adresu pod ovom domenom kako biste primili kod i potvrdili ovu domenu.",
  formFieldLabel__organizationName: "Naziv",
  formFieldLabel__organizationSlug: "Slug",
  formFieldLabel__passkeyName: "Naziv pristupnog klju\u010Da",
  formFieldLabel__password: "Lozinka",
  formFieldLabel__phoneNumber: "Telefonski broj",
  formFieldLabel__role: "Uloga",
  formFieldLabel__signOutOfOtherSessions: "Odjavi se sa svih ostalih ure\u0111aja",
  formFieldLabel__username: "Korisni\u010Dko ime",
  impersonationFab: {
    action__signOut: "Odjava",
    title: "Prijavljeni ste kao {{identifier}}"
  },
  maintenanceMode: "Trenutno provodimo odr\u017Eavanje, ali ne brinite, ne bi trebalo trajati du\u017Ee od nekoliko minuta.",
  membershipRole__admin: "Administrator",
  membershipRole__basicMember: "\u010Clan",
  membershipRole__guestMember: "Gost",
  organizationList: {
    action__createOrganization: "Kreiraj organizaciju",
    action__invitationAccept: "Pridru\u017Ei se",
    action__suggestionsAccept: "Zatra\u017Ei pridru\u017Eivanje",
    createOrganization: "Kreiraj organizaciju",
    invitationAcceptedLabel: "Pridru\u017Een",
    subtitle: "za nastavak na {{applicationName}}",
    suggestionsAcceptedLabel: "\u010Ceka odobrenje",
    title: "Odaberite ra\u010Dun",
    titleWithoutPersonal: "Odaberite organizaciju"
  },
  organizationProfile: {
    badge__automaticInvitation: "Automatske pozivnice",
    badge__automaticSuggestion: "Automatski prijedlozi",
    badge__manualInvitation: "Bez automatskog u\u010Dlanjenja",
    badge__unverified: "Nepotvr\u0111eno",
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Dodajte domenu za provjeru. Korisnici s e-mail adresama na ovoj domeni mogu se automatski pridru\u017Eiti organizaciji ili zatra\u017Eiti pridru\u017Eivanje.",
      title: "Dodaj domenu"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Pozivnice se nisu mogle poslati. Ve\u0107 postoje pozivnice na \u010Dekanju za sljede\u0107e e-mail adrese: {{email_addresses}}.",
      formButtonPrimary__continue: "Po\u0161alji pozivnice",
      selectDropdown__role: "Odaberi ulogu",
      subtitle: "Unesite ili zalijepite jednu ili vi\u0161e e-mail adresa, odvojenih razmakom ili zarezom.",
      successMessage: "Pozivnice uspje\u0161no poslane",
      title: "Pozovi nove \u010Dlanove"
    },
    membersPage: {
      action__invite: "Pozovi",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "Ukloni \u010Dlana",
        tableHeader__actions: void 0,
        tableHeader__joined: "Pridru\u017Een",
        tableHeader__role: "Uloga",
        tableHeader__user: "Korisnik"
      },
      detailsTitle__emptyRow: "Nema \u010Dlanova za prikaz",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Pozovite korisnike povezivanjem e-mail domene s va\u0161om organizacijom. Svatko tko se prijavi s odgovaraju\u0107om e-mail domenom mo\u0107i \u0107e se pridru\u017Eiti organizaciji u bilo kojem trenutku.",
          headerTitle: "Automatske pozivnice",
          primaryButton: "Upravljaj potvr\u0111enim domenama"
        },
        table__emptyRow: "Nema pozivnica za prikaz"
      },
      invitedMembersTab: {
        menuAction__revoke: "Opozovi pozivnicu",
        tableHeader__invited: "Pozvan"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Korisnici koji se prijave s odgovaraju\u0107om e-mail domenom mo\u0107i \u0107e vidjeti prijedlog za zahtjev za pridru\u017Eivanje va\u0161oj organizaciji.",
          headerTitle: "Automatski prijedlozi",
          primaryButton: "Upravljaj potvr\u0111enim domenama"
        },
        menuAction__approve: "Odobri",
        menuAction__reject: "Odbij",
        tableHeader__requested: "Zatra\u017Een pristup",
        table__emptyRow: "Nema zahtjeva za prikaz"
      },
      start: {
        headerTitle__invitations: "Pozivnice",
        headerTitle__members: "\u010Clanovi",
        headerTitle__requests: "Zahtjevi"
      }
    },
    navbar: {
      billing: void 0,
      description: "Upravljajte svojom organizacijom.",
      general: "Op\u0107enito",
      members: "\u010Clanovi",
      title: "Organizacija"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Upi\u0161ite "{{organizationName}}" ispod za nastavak.',
          messageLine1: "Jeste li sigurni da \u017Eelite izbrisati ovu organizaciju?",
          messageLine2: "Ova radnja je trajna i nepovratna.",
          successMessage: "Izbrisali ste organizaciju.",
          title: "Izbri\u0161i organizaciju"
        },
        leaveOrganization: {
          actionDescription: 'Upi\u0161ite "{{organizationName}}" ispod za nastavak.',
          messageLine1: "Jeste li sigurni da \u017Eelite napustiti ovu organizaciju? Izgubit \u0107ete pristup ovoj organizaciji i njezinim aplikacijama.",
          messageLine2: "Ova radnja je trajna i nepovratna.",
          successMessage: "Napustili ste organizaciju.",
          title: "Napusti organizaciju"
        },
        title: "Opasnost"
      },
      domainSection: {
        menuAction__manage: "Upravljaj",
        menuAction__remove: "Izbri\u0161i",
        menuAction__verify: "Potvrdi",
        primaryButton: "Dodaj domenu",
        subtitle: "Dopustite korisnicima da se automatski pridru\u017Ee organizaciji ili zatra\u017Ee pridru\u017Eivanje na temelju potvr\u0111ene e-mail domene.",
        title: "Potvr\u0111ene domene"
      },
      successMessage: "Organizacija je a\u017Eurirana.",
      title: "A\u017Euriraj profil"
    },
    removeDomainPage: {
      messageLine1: "E-mail domena {{domain}} bit \u0107e uklonjena.",
      messageLine2: "Korisnici se nakon ovoga ne\u0107e mo\u0107i automatski pridru\u017Eiti organizaciji.",
      successMessage: "{{domain}} je uklonjena.",
      title: "Ukloni domenu"
    },
    start: {
      headerTitle__general: "Op\u0107enito",
      headerTitle__members: "\u010Clanovi",
      profileSection: {
        primaryButton: "A\u017Euriraj profil",
        title: "Profil organizacije",
        uploadAction__title: "Logotip"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Uklanjanje ove domene utjecat \u0107e na pozvane korisnike.",
        removeDomainActionLabel__remove: "Ukloni domenu",
        removeDomainSubtitle: "Ukloni ovu domenu iz va\u0161ih potvr\u0111enih domena",
        removeDomainTitle: "Ukloni domenu"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Korisnici su automatski pozvani da se pridru\u017Ee organizaciji kada se prijave i mogu se pridru\u017Eiti u bilo kojem trenutku.",
        automaticInvitationOption__label: "Automatske pozivnice",
        automaticSuggestionOption__description: "Korisnici primaju prijedlog za zahtjev za pridru\u017Eivanje, ali moraju biti odobreni od strane administratora prije nego \u0161to se mogu pridru\u017Eiti organizaciji.",
        automaticSuggestionOption__label: "Automatski prijedlozi",
        calloutInfoLabel: "Promjena na\u010Dina upisa utjecat \u0107e samo na nove korisnike.",
        calloutInvitationCountLabel: "Pozivnice na \u010Dekanju poslane korisnicima: {{count}}",
        calloutSuggestionCountLabel: "Prijedlozi na \u010Dekanju poslani korisnicima: {{count}}",
        manualInvitationOption__description: "Korisnici mogu biti pozvani u organizaciju samo ru\u010Dno.",
        manualInvitationOption__label: "Bez automatskog upisa",
        subtitle: "Odaberite kako se korisnici s ove domene mogu pridru\u017Eiti organizaciji."
      },
      start: {
        headerTitle__danger: "Opasnost",
        headerTitle__enrollment: "Opcije upisa"
      },
      subtitle: "Domena {{domain}} je sada potvr\u0111ena. Nastavite odabirom na\u010Dina upisa.",
      title: "A\u017Euriraj {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Unesite verifikacijski kod poslan na va\u0161u e-mail adresu",
      formTitle: "Verifikacijski kod",
      resendButton: "Niste primili kod? Po\u0161alji ponovno",
      subtitle: "Domena {{domainName}} mora biti potvr\u0111ena putem e-maila.",
      subtitleVerificationCodeScreen: "Verifikacijski kod poslan je na {{emailAddress}}. Unesite kod za nastavak.",
      title: "Potvrdi domenu"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Kreiraj organizaciju",
    action__invitationAccept: "Pridru\u017Ei se",
    action__manageOrganization: "Upravljaj",
    action__suggestionsAccept: "Zatra\u017Ei pridru\u017Eivanje",
    notSelected: "Nije odabrana organizacija",
    personalWorkspace: "Osobni ra\u010Dun",
    suggestionsAcceptedLabel: "\u010Ceka odobrenje"
  },
  paginationButton__next: "Sljede\u0107e",
  paginationButton__previous: "Prethodno",
  paginationRowText__displaying: "Prikazuje se",
  paginationRowText__of: "od",
  reverification: {
    alternativeMethods: {
      actionLink: "Zatra\u017Eite pomo\u0107",
      actionText: "Nemate ni\u0161ta od ovoga?",
      blockButton__backupCode: "Koristite rezervni kod",
      blockButton__emailCode: "Po\u0161alji kod e-po\u0161tom na {{identifier}}",
      blockButton__passkey: void 0,
      blockButton__password: "Nastavite s va\u0161om lozinkom",
      blockButton__phoneCode: "Po\u0161alji SMS kod na {{identifier}}",
      blockButton__totp: "Koristite va\u0161u aplikaciju za autentifikaciju",
      getHelp: {
        blockButton__emailSupport: "Po\u0161alji e-po\u0161tu podr\u0161ci",
        content: "Ako imate problema s verifikacijom va\u0161eg ra\u010Duna, po\u0161aljite nam e-po\u0161tu i radit \u0107emo s vama na vra\u0107anju pristupa \u0161to je prije mogu\u0107e.",
        title: "Zatra\u017Eite pomo\u0107"
      },
      subtitle: "Imate problema? Mo\u017Eete koristiti bilo koju od ovih metoda za verifikaciju.",
      title: "Koristite drugu metodu"
    },
    backupCodeMfa: {
      subtitle: "Va\u0161 rezervni kod je onaj koji ste dobili prilikom postavljanja dvostupanjske autentifikacije.",
      title: "Unesite rezervni kod"
    },
    emailCode: {
      formTitle: "Verifikacijski kod",
      resendButton: "Niste primili kod? Po\u0161alji ponovno",
      subtitle: "za nastavak na {{applicationName}}",
      title: "Provjerite svoju e-po\u0161tu"
    },
    noAvailableMethods: {
      message: "Ne mo\u017Ee se nastaviti s verifikacijom. Nema dostupnog faktora autentifikacije.",
      subtitle: "Do\u0161lo je do pogre\u0161ke",
      title: "Ne mo\u017Ee se verificirati va\u0161 ra\u010Dun"
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: "Koristite drugu metodu",
      subtitle: "Unesite lozinku povezanu s va\u0161im ra\u010Dunom",
      title: "Unesite va\u0161u lozinku"
    },
    phoneCode: {
      formTitle: "Verifikacijski kod",
      resendButton: "Niste primili kod? Po\u0161alji ponovno",
      subtitle: "za nastavak na {{applicationName}}",
      title: "Provjerite svoj telefon"
    },
    phoneCodeMfa: {
      formTitle: "Verifikacijski kod",
      resendButton: "Niste primili kod? Po\u0161alji ponovno",
      subtitle: "Za nastavak, molimo unesite verifikacijski kod poslan na va\u0161 telefon",
      title: "Provjerite svoj telefon"
    },
    totpMfa: {
      formTitle: "Verifikacijski kod",
      subtitle: "Za nastavak, molimo unesite verifikacijski kod generiran va\u0161om aplikacijom za autentifikaciju",
      title: "Dvostupanjska verifikacija"
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Dodaj ra\u010Dun",
      action__signOutAll: "Odjavi se sa svih ra\u010Duna",
      subtitle: "Odaberite ra\u010Dun s kojim \u017Eelite nastaviti.",
      title: "Odaberite ra\u010Dun"
    },
    alternativeMethods: {
      actionLink: "Zatra\u017Eite pomo\u0107",
      actionText: "Nemate ni\u0161ta od ovoga?",
      blockButton__backupCode: "Koristite rezervni kod",
      blockButton__emailCode: "Po\u0161alji kod e-po\u0161tom na {{identifier}}",
      blockButton__emailLink: "Po\u0161alji poveznicu e-po\u0161tom na {{identifier}}",
      blockButton__passkey: "Prijavite se svojim pristupnim klju\u010Dem",
      blockButton__password: "Prijavite se svojom lozinkom",
      blockButton__phoneCode: "Po\u0161alji SMS kod na {{identifier}}",
      blockButton__totp: "Koristite svoju aplikaciju za autentifikaciju",
      getHelp: {
        blockButton__emailSupport: "Po\u0161aljite e-po\u0161tu podr\u0161ci",
        content: "Ako imate problema s prijavom na svoj ra\u010Dun, po\u0161aljite nam e-po\u0161tu i radit \u0107emo s vama na vra\u0107anju pristupa \u0161to je prije mogu\u0107e.",
        title: "Zatra\u017Eite pomo\u0107"
      },
      subtitle: "Imate problema? Mo\u017Eete koristiti bilo koju od ovih metoda za prijavu.",
      title: "Koristite drugu metodu"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "Va\u0161 rezervni kod je onaj koji ste dobili prilikom postavljanja dvostupanjske autentifikacije.",
      title: "Unesite rezervni kod"
    },
    emailCode: {
      formTitle: "Verifikacijski kod",
      resendButton: "Niste primili kod? Po\u0161alji ponovno",
      subtitle: "za nastavak na {{applicationName}}",
      title: "Provjerite svoju e-po\u0161tu"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Za nastavak, otvorite verifikacijsku poveznicu na ure\u0111aju i pregledniku s kojeg ste zapo\u010Deli prijavu",
        title: "Verifikacijska poveznica nije valjana za ovaj ure\u0111aj"
      },
      expired: {
        subtitle: "Vratite se na izvornu karticu za nastavak.",
        title: "Ova verifikacijska poveznica je istekla"
      },
      failed: {
        subtitle: "Vratite se na izvornu karticu za nastavak.",
        title: "Ova verifikacijska poveznica nije valjana"
      },
      formSubtitle: "Koristite verifikacijsku poveznicu poslanu na va\u0161u e-po\u0161tu",
      formTitle: "Verifikacijska poveznica",
      loading: {
        subtitle: "Uskoro \u0107ete biti preusmjereni",
        title: "Prijava..."
      },
      resendButton: "Niste primili poveznicu? Po\u0161alji ponovno",
      subtitle: "za nastavak na {{applicationName}}",
      title: "Provjerite svoju e-po\u0161tu",
      unusedTab: {
        title: "Mo\u017Eete zatvoriti ovu karticu"
      },
      verified: {
        subtitle: "Uskoro \u0107ete biti preusmjereni",
        title: "Uspje\u0161no prijavljeni"
      },
      verifiedSwitchTab: {
        subtitle: "Vratite se na izvornu karticu za nastavak",
        subtitleNewTab: "Vratite se na novootvorenu karticu za nastavak",
        titleNewTab: "Prijavljeni na drugoj kartici"
      }
    },
    forgotPassword: {
      formTitle: "Kod za resetiranje lozinke",
      resendButton: "Niste primili kod? Po\u0161alji ponovno",
      subtitle: "za resetiranje va\u0161e lozinke",
      subtitle_email: "Prvo unesite kod poslan na va\u0161u e-mail adresu",
      subtitle_phone: "Prvo unesite kod poslan na va\u0161 telefon",
      title: "Resetiraj lozinku"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Resetirajte svoju lozinku",
      label__alternativeMethods: "Ili se prijavite na drugi na\u010Din",
      title: "Zaboravili ste lozinku?"
    },
    noAvailableMethods: {
      message: "Nije mogu\u0107e nastaviti s prijavom. Nema dostupnog faktora autentifikacije.",
      subtitle: "Do\u0161lo je do pogre\u0161ke",
      title: "Nije mogu\u0107e prijaviti se"
    },
    passkey: {
      subtitle: "Kori\u0161tenje va\u0161eg pristupnog klju\u010Da potvr\u0111uje da ste to vi. Va\u0161 ure\u0111aj mo\u017Ee tra\u017Eiti otisak prsta, prepoznavanje lica ili zaklju\u010Davanje zaslona.",
      title: "Koristite svoj pristupni klju\u010D"
    },
    password: {
      actionLink: "Koristite drugu metodu",
      subtitle: "Unesite lozinku povezanu s va\u0161im ra\u010Dunom",
      title: "Unesite svoju lozinku"
    },
    passwordPwned: {
      title: "Lozinka je kompromitirana"
    },
    phoneCode: {
      formTitle: "Verifikacijski kod",
      resendButton: "Niste primili kod? Po\u0161alji ponovno",
      subtitle: "za nastavak na {{applicationName}}",
      title: "Provjerite svoj telefon"
    },
    phoneCodeMfa: {
      formTitle: "Verifikacijski kod",
      resendButton: "Niste primili kod? Po\u0161alji ponovno",
      subtitle: "Za nastavak, unesite verifikacijski kod poslan na va\u0161 telefon",
      title: "Provjerite svoj telefon"
    },
    resetPassword: {
      formButtonPrimary: "Resetiraj lozinku",
      requiredMessage: "Iz sigurnosnih razloga, potrebno je resetirati va\u0161u lozinku.",
      successMessage: "Va\u0161a lozinka je uspje\u0161no promijenjena. Prijavljujemo vas, molimo pri\u010Dekajte trenutak.",
      title: "Postavite novu lozinku"
    },
    resetPasswordMfa: {
      detailsLabel: "Moramo potvrditi va\u0161 identitet prije resetiranja lozinke."
    },
    start: {
      actionLink: "Registrirajte se",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "Koristite e-po\u0161tu",
      actionLink__use_email_username: "Koristite e-po\u0161tu ili korisni\u010Dko ime",
      actionLink__use_passkey: "Koristite pristupni klju\u010D umjesto toga",
      actionLink__use_phone: "Koristite telefon",
      actionLink__use_username: "Koristite korisni\u010Dko ime",
      actionText: "Nemate ra\u010Dun?",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "Dobrodo\u0161li natrag! Molimo prijavite se za nastavak",
      subtitleCombined: void 0,
      title: "Prijavite se na {{applicationName}}",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "Verifikacijski kod",
      subtitle: "Za nastavak, unesite verifikacijski kod generiran va\u0161om aplikacijom za autentifikaciju",
      title: "Dvostupanjska verifikacija"
    }
  },
  signInEnterPasswordTitle: "Unesite svoju lozinku",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Prijavi se",
      actionText: "Ve\u0107 imate ra\u010Dun?",
      subtitle: "Molimo ispunite preostale detalje za nastavak.",
      title: "Ispunite preostala polja"
    },
    emailCode: {
      formSubtitle: "Unesite verifikacijski kod poslan na va\u0161u e-mail adresu",
      formTitle: "Verifikacijski kod",
      resendButton: "Niste primili kod? Po\u0161alji ponovno",
      subtitle: "Unesite verifikacijski kod poslan na va\u0161u e-po\u0161tu",
      title: "Potvrdite svoju e-po\u0161tu"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Za nastavak, otvorite verifikacijsku poveznicu na ure\u0111aju i pregledniku s kojeg ste zapo\u010Deli registraciju",
        title: "Verifikacijska poveznica nije valjana za ovaj ure\u0111aj"
      },
      formSubtitle: "Koristite verifikacijsku poveznicu poslanu na va\u0161u e-mail adresu",
      formTitle: "Verifikacijska poveznica",
      loading: {
        title: "Registracija..."
      },
      resendButton: "Niste primili poveznicu? Po\u0161alji ponovno",
      subtitle: "za nastavak na {{applicationName}}",
      title: "Potvrdite svoju e-po\u0161tu",
      verified: {
        title: "Uspje\u0161no registrirani"
      },
      verifiedSwitchTab: {
        subtitle: "Vratite se na novootvorenu karticu za nastavak",
        subtitleNewTab: "Vratite se na prethodnu karticu za nastavak",
        title: "Uspje\u0161no potvr\u0111ena e-po\u0161ta"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: 'Sla\u017Eem se s {{ privacyPolicyLink || link("Politikom privatnosti") }}',
        label__onlyTermsOfService: 'Sla\u017Eem se s {{ termsOfServiceLink || link("Uvjetima kori\u0161tenja") }}',
        label__termsOfServiceAndPrivacyPolicy: 'Sla\u017Eem se s {{ termsOfServiceLink || link("Uvjetima kori\u0161tenja") }} i {{ privacyPolicyLink || link("Politikom privatnosti") }}'
      },
      continue: {
        subtitle: "Molimo pro\u010Ditajte i prihvatite uvjete kako biste nastavili",
        title: "Pravni pristanak"
      }
    },
    phoneCode: {
      formSubtitle: "Unesite verifikacijski kod poslan na va\u0161 broj telefona",
      formTitle: "Verifikacijski kod",
      resendButton: "Niste primili kod? Po\u0161alji ponovno",
      subtitle: "Unesite verifikacijski kod poslan na va\u0161 telefon",
      title: "Potvrdite svoj telefon"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "Prijavi se",
      actionLink__use_email: "Koristite e-po\u0161tu",
      actionLink__use_phone: "Koristite telefon",
      actionText: "Ve\u0107 imate ra\u010Dun?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "Dobrodo\u0161li! Molimo ispunite detalje za po\u010Detak.",
      subtitleCombined: "Dobrodo\u0161li! Molimo ispunite detalje za po\u010Detak.",
      title: "Kreirajte svoj ra\u010Dun",
      titleCombined: "Kreirajte svoj ra\u010Dun"
    }
  },
  socialButtonsBlockButton: "Nastavite s {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: "{{provider|titleize}}",
  unstable__errors: {
    already_a_member_in_organization: "{{email}} je ve\u0107 \u010Dlan organizacije.",
    captcha_invalid: "Registracija neuspje\u0161na zbog neuspjelih sigurnosnih provjera. Molimo osvje\u017Eite stranicu i poku\u0161ajte ponovno ili se obratite podr\u0161ci za dodatnu pomo\u0107.",
    captcha_unavailable: "Registracija neuspje\u0161na zbog neuspjele provjere bota. Molimo osvje\u017Eite stranicu i poku\u0161ajte ponovno ili se obratite podr\u0161ci za dodatnu pomo\u0107.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: "Ova e-mail adresa je zauzeta. Molimo poku\u0161ajte s drugom.",
    form_identifier_exists__phone_number: "Ovaj telefonski broj je zauzet. Molimo poku\u0161ajte s drugim.",
    form_identifier_exists__username: "Ovo korisni\u010Dko ime je zauzeto. Molimo poku\u0161ajte s drugim.",
    form_identifier_not_found: "Nismo prona\u0161li ra\u010Dun s tim podacima.",
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: "E-mail adresa mora biti valjana e-mail adresa.",
    form_param_format_invalid__phone_number: "Telefonski broj mora biti u valjanom me\u0111unarodnom formatu",
    form_param_max_length_exceeded__first_name: "Ime ne smije biti du\u017Ee od 256 znakova.",
    form_param_max_length_exceeded__last_name: "Prezime ne smije biti du\u017Ee od 256 znakova.",
    form_param_max_length_exceeded__name: "Ime ne smije biti du\u017Ee od 256 znakova.",
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: void 0,
    form_password_length_too_short: void 0,
    form_password_not_strong_enough: "Va\u0161a lozinka nije dovoljno jaka.",
    form_password_pwned: "Ova lozinka je prona\u0111ena kao dio curenja podataka i ne mo\u017Ee se koristiti, molimo poku\u0161ajte s drugom lozinkom.",
    form_password_pwned__sign_in: "Ova lozinka je prona\u0111ena kao dio curenja podataka i ne mo\u017Ee se koristiti, molimo resetirajte svoju lozinku.",
    form_password_size_in_bytes_exceeded: "Va\u0161a lozinka je prema\u0161ila maksimalni dopu\u0161teni broj bajtova, molimo skratite je ili uklonite neke posebne znakove.",
    form_password_validation_failed: "Neto\u010Dna lozinka",
    form_username_invalid_character: void 0,
    form_username_invalid_length: void 0,
    identification_deletion_failed: "Ne mo\u017Eete izbrisati svoju posljednju identifikaciju.",
    not_allowed_access: "E-mail adresa ili broj telefona nije dozvoljen za registraciju. Ovo mo\u017Ee biti zbog kori\u0161tenja '+', '=', '#' ili '.' u va\u0161oj e-mail adresi, kori\u0161tenja domene povezane s vremenskom e-mail uslugom ili eksplicitnog blokiranja. Ako smatrate da je ovo pogre\u0161ka, obratite se podr\u0161ci.",
    organization_domain_blocked: "Ovo je blokirana domena pru\u017Eatelja e-po\u0161te. Molimo koristite drugu.",
    organization_domain_common: "Ovo je uobi\u010Dajena domena pru\u017Eatelja e-po\u0161te. Molimo koristite drugu.",
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: "Dostigli ste ograni\u010Denje \u010Dlanstava u organizacijama, uklju\u010Duju\u0107i otvorene pozivnice.",
    organization_minimum_permissions_needed: "Mora postojati barem jedan \u010Dlan organizacije s minimalnim potrebnim dozvolama.",
    passkey_already_exists: "Pristupni klju\u010D je ve\u0107 registriran na ovom ure\u0111aju.",
    passkey_not_supported: "Pristupni klju\u010Devi nisu podr\u017Eani na ovom ure\u0111aju.",
    passkey_pa_not_supported: "Registracija zahtijeva platformski autentifikator, ali ure\u0111aj ga ne podr\u017Eava.",
    passkey_registration_cancelled: "Registracija pristupnog klju\u010Da je otkazana ili je isteklo vrijeme.",
    passkey_retrieval_cancelled: "Provjera pristupnog klju\u010Da je otkazana ili je isteklo vrijeme.",
    passwordComplexity: {
      maximumLength: "manje od {{length}} znakova",
      minimumLength: "{{length}} ili vi\u0161e znakova",
      requireLowercase: "malo slovo",
      requireNumbers: "broj",
      requireSpecialCharacter: "poseban znak",
      requireUppercase: "veliko slovo",
      sentencePrefix: "Va\u0161a lozinka mora sadr\u017Eavati"
    },
    phone_number_exists: "Ovaj telefonski broj je zauzet. Molimo poku\u0161ajte s drugim.",
    session_exists: "Ve\u0107 ste prijavljeni.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "Va\u0161a lozinka funkcionira, ali mogla bi biti ja\u010Da. Poku\u0161ajte dodati vi\u0161e znakova.",
      goodPassword: "Va\u0161a lozinka zadovoljava sve potrebne zahtjeve.",
      notEnough: "Va\u0161a lozinka nije dovoljno jaka.",
      suggestions: {
        allUppercase: "Napi\u0161ite velika samo neka, a ne sva slova.",
        anotherWord: "Dodajte vi\u0161e rije\u010Di koje su manje uobi\u010Dajene.",
        associatedYears: "Izbjegavajte godine koje su povezane s vama.",
        capitalization: "Napi\u0161ite velikim slovima vi\u0161e od prvog slova.",
        dates: "Izbjegavajte datume i godine koje su povezane s vama.",
        l33t: "Izbjegavajte predvidljive zamjene slova poput '@' za 'a'.",
        longerKeyboardPattern: "Koristite du\u017Ee obrasce tipkovnice i vi\u0161e puta promijenite smjer tipkanja.",
        noNeed: "Mo\u017Eete stvoriti jake lozinke bez kori\u0161tenja simbola, brojeva ili velikih slova.",
        pwned: "Ako ovu lozinku koristite negdje drugdje, trebali biste je promijeniti.",
        recentYears: "Izbjegavajte nedavne godine.",
        repeated: "Izbjegavajte ponavljanje rije\u010Di i znakova.",
        reverseWords: "Izbjegavajte obrnuta spelovanja uobi\u010Dajenih rije\u010Di.",
        sequences: "Izbjegavajte uobi\u010Dajene nizove znakova.",
        useWords: "Koristite vi\u0161e rije\u010Di, ali izbjegavajte uobi\u010Dajene fraze."
      },
      warnings: {
        common: "Ovo je \u010Desto kori\u0161tena lozinka.",
        commonNames: "Uobi\u010Dajena imena i prezimena lako je pogoditi.",
        dates: "Datume je lako pogoditi.",
        extendedRepeat: 'Ponavljaju\u0107e uzorke znakova poput "abcabcabc" lako je pogoditi.',
        keyPattern: "Kratke obrasce tipkovnice lako je pogoditi.",
        namesByThemselves: "Pojedina\u010Dna imena ili prezimena lako je pogoditi.",
        pwned: "Va\u0161a lozinka je otkrivena u curenju podataka na internetu.",
        recentYears: "Nedavne godine lako je pogoditi.",
        sequences: 'Uobi\u010Dajene nizove znakova poput "abc" lako je pogoditi.',
        similarToCommon: "Ovo je sli\u010Dno \u010Desto kori\u0161tenoj lozinci.",
        simpleRepeat: 'Ponavljaju\u0107e znakove poput "aaa" lako je pogoditi.',
        straightRow: "Ravne redove tipki na va\u0161oj tipkovnici lako je pogoditi.",
        topHundred: "Ovo je \u010Desto kori\u0161tena lozinka.",
        topTen: "Ovo je vrlo \u010Desto kori\u0161tena lozinka.",
        userInputs: "Ne bi trebalo biti osobnih podataka ili podataka vezanih uz stranicu.",
        wordByItself: "Pojedina\u010Dne rije\u010Di lako je pogoditi."
      }
    }
  },
  userButton: {
    action__addAccount: "Dodaj ra\u010Dun",
    action__manageAccount: "Upravljaj ra\u010Dunom",
    action__signOut: "Odjavi se",
    action__signOutAll: "Odjavi se sa svih ra\u010Duna"
  },
  userProfile: {
    backupCodePage: {
      actionLabel__copied: "Kopirano!",
      actionLabel__copy: "Kopiraj sve",
      actionLabel__download: "Preuzmi .txt",
      actionLabel__print: "Ispi\u0161i",
      infoText1: "Rezervni kodovi bit \u0107e omogu\u0107eni za ovaj ra\u010Dun.",
      infoText2: "\u010Cuvajte rezervne kodove u tajnosti i pohranite ih na sigurno. Mo\u017Eete regenerirati rezervne kodove ako sumnjate da su kompromitirani.",
      subtitle__codelist: "Pohranite ih na sigurno i \u010Duvajte u tajnosti.",
      successMessage: "Rezervni kodovi su sada omogu\u0107eni. Mo\u017Eete koristiti jedan od njih za prijavu na svoj ra\u010Dun ako izgubite pristup svom ure\u0111aju za autentifikaciju. Svaki kod se mo\u017Ee koristiti samo jednom.",
      successSubtitle: "Mo\u017Eete koristiti jedan od ovih za prijavu na svoj ra\u010Dun ako izgubite pristup svom ure\u0111aju za autentifikaciju.",
      title: "Dodaj verifikaciju rezervnim kodom",
      title__codelist: "Rezervni kodovi"
    },
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "Odaberite pru\u017Eatelja usluge za povezivanje va\u0161eg ra\u010Duna.",
      formHint__noAccounts: "Nema dostupnih vanjskih pru\u017Eatelja ra\u010Duna.",
      removeResource: {
        messageLine1: "{{identifier}} \u0107e biti uklonjen s ovog ra\u010Duna.",
        messageLine2: "Vi\u0161e ne\u0107ete mo\u0107i koristiti ovaj povezani ra\u010Dun i sve ovisne zna\u010Dajke vi\u0161e ne\u0107e raditi.",
        successMessage: "{{connectedAccount}} je uklonjen s va\u0161eg ra\u010Duna.",
        title: "Ukloni povezani ra\u010Dun"
      },
      socialButtonsBlockButton: "{{provider|titleize}}",
      successMessage: "Pru\u017Eatelj usluge je dodan va\u0161em ra\u010Dunu",
      title: "Dodaj povezani ra\u010Dun"
    },
    deletePage: {
      actionDescription: 'Upi\u0161ite "Izbri\u0161i ra\u010Dun" ispod za nastavak.',
      confirm: "Izbri\u0161i ra\u010Dun",
      messageLine1: "Jeste li sigurni da \u017Eelite izbrisati svoj ra\u010Dun?",
      messageLine2: "Ova radnja je trajna i nepovratna.",
      title: "Izbri\u0161i ra\u010Dun"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "Poruka s verifikacijskim kodom bit \u0107e poslana na ovu adresu e-po\u0161te.",
        formSubtitle: "Unesite verifikacijski kod poslan na {{identifier}}",
        formTitle: "Verifikacijski kod",
        resendButton: "Niste primili kod? Po\u0161alji ponovno",
        successMessage: "E-po\u0161ta {{identifier}} je dodana va\u0161em ra\u010Dunu."
      },
      emailLink: {
        formHint: "Poruka s verifikacijskom poveznicom bit \u0107e poslana na ovu adresu e-po\u0161te.",
        formSubtitle: "Kliknite na verifikacijsku poveznicu u e-po\u0161ti poslanoj na {{identifier}}",
        formTitle: "Verifikacijska poveznica",
        resendButton: "Niste primili poveznicu? Po\u0161alji ponovno",
        successMessage: "E-po\u0161ta {{identifier}} je dodana va\u0161em ra\u010Dunu."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} \u0107e biti uklonjen s ovog ra\u010Duna.",
        messageLine2: "Vi\u0161e se ne\u0107ete mo\u0107i prijaviti koriste\u0107i ovu adresu e-po\u0161te.",
        successMessage: "{{emailAddress}} je uklonjena s va\u0161eg ra\u010Duna.",
        title: "Ukloni adresu e-po\u0161te"
      },
      title: "Dodaj adresu e-po\u0161te",
      verifyTitle: "Potvrdi adresu e-po\u0161te"
    },
    formButtonPrimary__add: "Dodaj",
    formButtonPrimary__continue: "Nastavi",
    formButtonPrimary__finish: "Zavr\u0161i",
    formButtonPrimary__remove: "Ukloni",
    formButtonPrimary__save: "Spremi",
    formButtonReset: "Odustani",
    mfaPage: {
      formHint: "Odaberite metodu za dodavanje.",
      title: "Dodaj dvostupanjsku provjeru"
    },
    mfaPhoneCodePage: {
      backButton: "Koristi postoje\u0107i broj",
      primaryButton__addPhoneNumber: "Dodaj telefonski broj",
      removeResource: {
        messageLine1: "{{identifier}} vi\u0161e ne\u0107e primati verifikacijske kodove prilikom prijave.",
        messageLine2: "Va\u0161 ra\u010Dun mo\u017Eda ne\u0107e biti toliko siguran. Jeste li sigurni da \u017Eelite nastaviti?",
        successMessage: "SMS kod dvostupanjske provjere je uklonjen za {{mfaPhoneCode}}",
        title: "Ukloni dvostupanjsku provjeru"
      },
      subtitle__availablePhoneNumbers: "Odaberite postoje\u0107i telefonski broj za registraciju SMS koda dvostupanjske provjere ili dodajte novi.",
      subtitle__unavailablePhoneNumbers: "Nema dostupnih telefonskih brojeva za registraciju SMS koda dvostupanjske provjere, molimo dodajte novi.",
      successMessage1: "Prilikom prijave, morat \u0107ete unijeti verifikacijski kod poslan na ovaj telefonski broj kao dodatni korak.",
      successMessage2: "Spremite ove rezervne kodove i pohranite ih na sigurno mjesto. Ako izgubite pristup svom ure\u0111aju za autentifikaciju, mo\u017Eete koristiti rezervne kodove za prijavu.",
      successTitle: "SMS kod verifikacije omogu\u0107en",
      title: "Dodaj SMS kod verifikacije"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Umjesto toga, skeniraj QR kod",
        buttonUnableToScan__nonPrimary: "Ne mo\u017Eete skenirati QR kod?",
        infoText__ableToScan: "Postavite novu metodu prijave u va\u0161oj aplikaciji za autentifikaciju i skenirajte sljede\u0107i QR kod da biste je povezali s va\u0161im ra\u010Dunom.",
        infoText__unableToScan: "Postavite novu metodu prijave u va\u0161em autentifikatoru i unesite Klju\u010D naveden ispod.",
        inputLabel__unableToScan1: "Provjerite jesu li omogu\u0107ene vremenski temeljene ili jednokratne lozinke, zatim zavr\u0161ite povezivanje va\u0161eg ra\u010Duna.",
        inputLabel__unableToScan2: "Alternativno, ako va\u0161 autentifikator podr\u017Eava TOTP URI-je, mo\u017Eete tako\u0111er kopirati cijeli URI."
      },
      removeResource: {
        messageLine1: "Verifikacijski kodovi iz ovog autentifikatora vi\u0161e ne\u0107e biti potrebni prilikom prijave.",
        messageLine2: "Va\u0161 ra\u010Dun mo\u017Eda ne\u0107e biti toliko siguran. Jeste li sigurni da \u017Eelite nastaviti?",
        successMessage: "Dvostupanjska provjera putem aplikacije za autentifikaciju je uklonjena.",
        title: "Ukloni dvostupanjsku provjeru"
      },
      successMessage: "Dvostupanjska provjera je sada omogu\u0107ena. Prilikom prijave, morat \u0107ete unijeti verifikacijski kod iz ovog autentifikatora kao dodatni korak.",
      title: "Dodaj aplikaciju za autentifikaciju",
      verifySubtitle: "Unesite verifikacijski kod generiran va\u0161im autentifikatorom",
      verifyTitle: "Verifikacijski kod"
    },
    mobileButton__menu: "Izbornik",
    navbar: {
      account: "Profil",
      billing: void 0,
      description: "Upravljajte informacijama va\u0161eg ra\u010Duna.",
      security: "Sigurnost",
      title: "Ra\u010Dun"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "{{name}} \u0107e biti uklonjen s ovog ra\u010Duna.",
        title: "Ukloni pristupni klju\u010D"
      },
      subtitle__rename: "Mo\u017Eete promijeniti naziv pristupnog klju\u010Da kako biste ga lak\u0161e prona\u0161li.",
      title__rename: "Preimenuj pristupni klju\u010D"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Preporu\u010Duje se odjava sa svih drugih ure\u0111aja koji su mo\u017Eda koristili va\u0161u staru lozinku.",
      readonly: "Va\u0161a lozinka trenutno se ne mo\u017Ee ure\u0111ivati jer se mo\u017Eete prijaviti samo putem poslovne veze.",
      successMessage__set: "Va\u0161a lozinka je postavljena.",
      successMessage__signOutOfOtherSessions: "Svi ostali ure\u0111aji su odjavljeni.",
      successMessage__update: "Va\u0161a lozinka je a\u017Eurirana.",
      title__set: "Postavi lozinku",
      title__update: "A\u017Euriraj lozinku"
    },
    phoneNumberPage: {
      infoText: "Tekstualna poruka s verifikacijskim kodom bit \u0107e poslana na ovaj telefonski broj. Mogu se primijeniti naknade za poruke i podatke.",
      removeResource: {
        messageLine1: "{{identifier}} \u0107e biti uklonjen s ovog ra\u010Duna.",
        messageLine2: "Vi\u0161e se ne\u0107ete mo\u0107i prijaviti koriste\u0107i ovaj telefonski broj.",
        successMessage: "{{phoneNumber}} je uklonjen s va\u0161eg ra\u010Duna.",
        title: "Ukloni telefonski broj"
      },
      successMessage: "{{identifier}} je dodan va\u0161em ra\u010Dunu.",
      title: "Dodaj telefonski broj",
      verifySubtitle: "Unesite verifikacijski kod poslan na {{identifier}}",
      verifyTitle: "Potvrdi telefonski broj"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "Preporu\u010Dena veli\u010Dina 1:1, do 10MB.",
      imageFormDestructiveActionSubtitle: "Ukloni",
      imageFormSubtitle: "U\u010Ditaj",
      imageFormTitle: "Profilna slika",
      readonly: "Va\u0161e profilne informacije su pru\u017Eene putem poslovne veze i ne mogu se ure\u0111ivati.",
      successMessage: "Va\u0161 profil je a\u017Euriran.",
      title: "A\u017Euriraj profil"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Odjavi se s ure\u0111aja",
        title: "Aktivni ure\u0111aji"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Ponovno pove\u017Ei",
        actionLabel__reauthorize: "Autoriziraj sada",
        destructiveActionTitle: "Ukloni",
        primaryButton: "Pove\u017Ei ra\u010Dun",
        subtitle__disconnected: "Ovaj ra\u010Dun je isklju\u010Den.",
        subtitle__reauthorize: "Potrebna ovla\u0161tenja su a\u017Eurirana i mo\u017Eda do\u017Eivljavate ograni\u010Denu funkcionalnost. Molimo vas da ponovno autorizirate ovu aplikaciju kako biste izbjegli probleme",
        title: "Povezani ra\u010Duni"
      },
      dangerSection: {
        deleteAccountButton: "Izbri\u0161i ra\u010Dun",
        title: "Izbri\u0161i ra\u010Dun"
      },
      emailAddressesSection: {
        destructiveAction: "Ukloni e-po\u0161tu",
        detailsAction__nonPrimary: "Postavi kao primarnu",
        detailsAction__primary: "Dovr\u0161i verifikaciju",
        detailsAction__unverified: "Potvrdi",
        primaryButton: "Dodaj adresu e-po\u0161te",
        title: "Adrese e-po\u0161te"
      },
      enterpriseAccountsSection: {
        title: "Poslovni ra\u010Duni"
      },
      headerTitle__account: "Detalji profila",
      headerTitle__security: "Sigurnost",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Regeneriraj",
          headerTitle: "Rezervni kodovi",
          subtitle__regenerate: "Dobijte novi set sigurnosnih rezervnih kodova. Prethodni rezervni kodovi bit \u0107e izbrisani i ne mogu se koristiti.",
          title__regenerate: "Regeneriraj rezervne kodove"
        },
        phoneCode: {
          actionLabel__setDefault: "Postavi kao zadano",
          destructiveActionLabel: "Ukloni"
        },
        primaryButton: "Dodaj dvostupanjsku provjeru",
        title: "Dvostupanjska provjera",
        totp: {
          destructiveActionTitle: "Ukloni",
          headerTitle: "Aplikacija za autentifikaciju"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Ukloni",
        menuAction__rename: "Preimenuj",
        primaryButton: void 0,
        title: "Pristupni klju\u010Devi"
      },
      passwordSection: {
        primaryButton__setPassword: "Postavi lozinku",
        primaryButton__updatePassword: "A\u017Euriraj lozinku",
        title: "Lozinka"
      },
      phoneNumbersSection: {
        destructiveAction: "Ukloni telefonski broj",
        detailsAction__nonPrimary: "Postavi kao primarni",
        detailsAction__primary: "Dovr\u0161i verifikaciju",
        detailsAction__unverified: "Potvrdi telefonski broj",
        primaryButton: "Dodaj telefonski broj",
        title: "Telefonski brojevi"
      },
      profileSection: {
        primaryButton: "A\u017Euriraj profil",
        title: "Profil"
      },
      usernameSection: {
        primaryButton__setUsername: "Postavi korisni\u010Dko ime",
        primaryButton__updateUsername: "A\u017Euriraj korisni\u010Dko ime",
        title: "Korisni\u010Dko ime"
      },
      web3WalletsSection: {
        destructiveAction: "Ukloni nov\u010Danik",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Pove\u017Ei nov\u010Danik",
        title: "Web3 nov\u010Danici"
      }
    },
    usernamePage: {
      successMessage: "Va\u0161e korisni\u010Dko ime je a\u017Eurirano.",
      title__set: "Postavi korisni\u010Dko ime",
      title__update: "A\u017Euriraj korisni\u010Dko ime"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} \u0107e biti uklonjen s ovog ra\u010Duna.",
        messageLine2: "Vi\u0161e se ne\u0107ete mo\u0107i prijaviti koriste\u0107i ovaj web3 nov\u010Danik.",
        successMessage: "{{web3Wallet}} je uklonjen s va\u0161eg ra\u010Duna.",
        title: "Ukloni web3 nov\u010Danik"
      },
      subtitle__availableWallets: "Odaberite web3 nov\u010Danik za povezivanje s va\u0161im ra\u010Dunom.",
      subtitle__unavailableWallets: "Nema dostupnih web3 nov\u010Danika.",
      successMessage: "Nov\u010Danik je dodan va\u0161em ra\u010Dunu.",
      title: "Dodaj web3 nov\u010Danik",
      web3WalletButtonsBlockButton: "{{provider|titleize}}"
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  hrHR
};
//# sourceMappingURL=hr-HR.mjs.map