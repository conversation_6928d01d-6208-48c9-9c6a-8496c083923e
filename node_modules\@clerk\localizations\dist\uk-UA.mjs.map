{"version": 3, "sources": ["../src/uk-UA.ts"], "sourcesContent": ["/*\n * =====================================================================================\n * DISCLAIMER:\n * =====================================================================================\n * This localization file is a community contribution and is not officially maintained\n * by Clerk. It has been provided by the community and may not be fully aligned\n * with the current or future states of the main application. Clerk does not guarantee\n * the accuracy, completeness, or timeliness of the translations in this file.\n * Use of this file is at your own risk and discretion.\n * =====================================================================================\n */\n\nimport type { LocalizationResource } from '@clerk/types';\n\nexport const ukUA: LocalizationResource = {\n  locale: 'uk-UA',\n  backButton: 'Назад',\n  badge__activePlan: undefined,\n  badge__canceledEndsAt: undefined,\n  badge__currentPlan: undefined,\n  badge__default: 'За замовчуванням',\n  badge__endsAt: undefined,\n  badge__expired: undefined,\n  badge__otherImpersonatorDevice: 'Інший пристрій-двійник',\n  badge__primary: 'Основний',\n  badge__renewsAt: undefined,\n  badge__requiresAction: 'Потребує дії',\n  badge__startsAt: undefined,\n  badge__thisDevice: 'Цей пристрій',\n  badge__unverified: 'Неперевірений',\n  badge__upcomingPlan: undefined,\n  badge__userDevice: 'Пристрій користувача',\n  badge__you: 'Ви',\n  commerce: {\n    addPaymentMethod: undefined,\n    alwaysFree: undefined,\n    annually: undefined,\n    availableFeatures: undefined,\n    billedAnnually: undefined,\n    billedMonthlyOnly: undefined,\n    cancelSubscription: undefined,\n    cancelSubscriptionAccessUntil: undefined,\n    cancelSubscriptionNoCharge: undefined,\n    cancelSubscriptionTitle: undefined,\n    cannotSubscribeMonthly: undefined,\n    checkout: {\n      description__paymentSuccessful: undefined,\n      description__subscriptionSuccessful: undefined,\n      downgradeNotice: undefined,\n      emailForm: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      lineItems: {\n        title__paymentMethod: undefined,\n        title__statementId: undefined,\n        title__subscriptionBegins: undefined,\n        title__totalPaid: undefined,\n      },\n      perMonth: undefined,\n      title: undefined,\n      title__paymentSuccessful: undefined,\n      title__subscriptionSuccessful: undefined,\n    },\n    creditRemainder: undefined,\n    defaultFreePlanActive: undefined,\n    free: undefined,\n    getStarted: undefined,\n    keepSubscription: undefined,\n    manage: undefined,\n    manageSubscription: undefined,\n    month: undefined,\n    monthly: undefined,\n    pastDue: undefined,\n    pay: undefined,\n    paymentMethods: undefined,\n    paymentSource: {\n      applePayDescription: {\n        annual: undefined,\n        monthly: undefined,\n      },\n      dev: {\n        anyNumbers: undefined,\n        cardNumber: undefined,\n        cvcZip: undefined,\n        developmentMode: undefined,\n        expirationDate: undefined,\n        testCardInfo: undefined,\n      },\n    },\n    popular: undefined,\n    pricingTable: {\n      billingCycle: undefined,\n      included: undefined,\n    },\n    reSubscribe: undefined,\n    seeAllFeatures: undefined,\n    subscribe: undefined,\n    subtotal: undefined,\n    switchPlan: undefined,\n    switchToAnnual: undefined,\n    switchToMonthly: undefined,\n    totalDueToday: undefined,\n    viewFeatures: undefined,\n    year: undefined,\n  },\n  createOrganization: {\n    formButtonSubmit: 'Створити організацію',\n    invitePage: {\n      formButtonReset: 'Пропустити',\n    },\n    title: 'Створити організацію',\n  },\n  dates: {\n    lastDay: \"Вчора в {{ date | timeString('uk-UA') }}\",\n    next6Days: \"{{ date | weekday('uk-UA','long') }} в {{ date | timeString('uk-UA') }}\",\n    nextDay: \"Завтра в {{ date | timeString('uk-UA') }}\",\n    numeric: \"{{ date | numeric('uk-UA') }}\",\n    previous6Days: \"Останній {{ date | weekday('uk-UA','long') }} в {{ date | timeString('uk-UA') }}\",\n    sameDay: \"Сьогодні в {{ date | timeString('uk-UA') }}\",\n  },\n  dividerText: 'або',\n  footerActionLink__alternativePhoneCodeProvider: undefined,\n  footerActionLink__useAnotherMethod: 'Використовувати інший метод',\n  footerPageLink__help: 'Допомога',\n  footerPageLink__privacy: 'Приватність',\n  footerPageLink__terms: 'Умови',\n  formButtonPrimary: 'Продовжити',\n  formButtonPrimary__verify: 'Verify',\n  formFieldAction__forgotPassword: 'Забули пароль?',\n  formFieldError__matchingPasswords: 'Паролі збігаються.',\n  formFieldError__notMatchingPasswords: 'Паролі не збігаються.',\n  formFieldError__verificationLinkExpired: 'The verification link expired. Please request a new link.',\n  formFieldHintText__optional: \"Необов'язково\",\n  formFieldHintText__slug: 'A slug is a human-readable ID that must be unique. It’s often used in URLs.',\n  formFieldInputPlaceholder__backupCode: undefined,\n  formFieldInputPlaceholder__confirmDeletionUserAccount: 'Delete account',\n  formFieldInputPlaceholder__emailAddress: undefined,\n  formFieldInputPlaceholder__emailAddress_username: undefined,\n  formFieldInputPlaceholder__emailAddresses:\n    'Введіть або вставте одну або більше адрес електронної пошти, розділених пробілами або комами',\n  formFieldInputPlaceholder__firstName: undefined,\n  formFieldInputPlaceholder__lastName: undefined,\n  formFieldInputPlaceholder__organizationDomain: undefined,\n  formFieldInputPlaceholder__organizationDomainEmailAddress: undefined,\n  formFieldInputPlaceholder__organizationName: undefined,\n  formFieldInputPlaceholder__organizationSlug: undefined,\n  formFieldInputPlaceholder__password: undefined,\n  formFieldInputPlaceholder__phoneNumber: undefined,\n  formFieldInputPlaceholder__username: undefined,\n  formFieldLabel__automaticInvitations: 'Enable automatic invitations for this domain',\n  formFieldLabel__backupCode: 'Код відновлення',\n  formFieldLabel__confirmDeletion: 'Підтвердження',\n  formFieldLabel__confirmPassword: 'Підтвердження пароля',\n  formFieldLabel__currentPassword: 'Поточний пароль',\n  formFieldLabel__emailAddress: 'Пошта',\n  formFieldLabel__emailAddress_username: \"Пошта або ім'я користувача\",\n  formFieldLabel__emailAddresses: 'Поштові адреси',\n  formFieldLabel__firstName: \"Ім'я\",\n  formFieldLabel__lastName: 'Прізвище',\n  formFieldLabel__newPassword: 'Новий пароль',\n  formFieldLabel__organizationDomain: 'Domain',\n  formFieldLabel__organizationDomainDeletePending: 'Delete pending invitations and suggestions',\n  formFieldLabel__organizationDomainEmailAddress: 'Verification email address',\n  formFieldLabel__organizationDomainEmailAddressDescription:\n    'Enter an email address under this domain to receive a code and verify this domain.',\n  formFieldLabel__organizationName: 'Назва організації',\n  formFieldLabel__organizationSlug: 'URL адреса',\n  formFieldLabel__passkeyName: undefined,\n  formFieldLabel__password: 'Пароль',\n  formFieldLabel__phoneNumber: 'Номер телефону',\n  formFieldLabel__role: 'Роль',\n  formFieldLabel__signOutOfOtherSessions: 'Вийти з усіх інших пристроїв',\n  formFieldLabel__username: \"Ім'я користувача\",\n  impersonationFab: {\n    action__signOut: 'Вийти',\n    title: 'Ви увійшли як {{identifier}}',\n  },\n  maintenanceMode: undefined,\n  membershipRole__admin: 'Адміністратор',\n  membershipRole__basicMember: 'Член',\n  membershipRole__guestMember: 'Гість',\n  organizationList: {\n    action__createOrganization: 'Create organization',\n    action__invitationAccept: 'Join',\n    action__suggestionsAccept: 'Request to join',\n    createOrganization: 'Create Organization',\n    invitationAcceptedLabel: 'Joined',\n    subtitle: 'to continue to {{applicationName}}',\n    suggestionsAcceptedLabel: 'Pending approval',\n    title: 'Choose an account',\n    titleWithoutPersonal: 'Choose an organization',\n  },\n  organizationProfile: {\n    badge__automaticInvitation: 'Automatic invitations',\n    badge__automaticSuggestion: 'Automatic suggestions',\n    badge__manualInvitation: 'No automatic enrollment',\n    badge__unverified: 'Unverified',\n    billingPage: {\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    createDomainPage: {\n      subtitle:\n        'Add the domain to verify. Users with email addresses at this domain can join the organization automatically or request to join.',\n      title: 'Add domain',\n    },\n    invitePage: {\n      detailsTitle__inviteFailed: 'Запрошення не вдалося надіслати. Виправте наступне і повторіть спробу:',\n      formButtonPrimary__continue: 'Надіслати запрошення',\n      selectDropdown__role: 'Select role',\n      subtitle: 'Запросіть нових учасників до цієї організації',\n      successMessage: 'Запрошення успішно надіслано',\n      title: 'Запросити учасників',\n    },\n    membersPage: {\n      action__invite: 'Запросити',\n      action__search: undefined,\n      activeMembersTab: {\n        menuAction__remove: 'Видалити учасника',\n        tableHeader__actions: undefined,\n        tableHeader__joined: 'Приєднався',\n        tableHeader__role: 'Роль',\n        tableHeader__user: 'Користувач',\n      },\n      detailsTitle__emptyRow: 'Немає учасників для відображення',\n      invitationsTab: {\n        autoInvitations: {\n          headerSubtitle:\n            'Invite users by connecting an email domain with your organization. Anyone who signs up with a matching email domain will be able to join the organization anytime.',\n          headerTitle: 'Automatic invitations',\n          primaryButton: 'Manage verified domains',\n        },\n        table__emptyRow: 'No invitations to display',\n      },\n      invitedMembersTab: {\n        menuAction__revoke: 'Відкликати запрошення',\n        tableHeader__invited: 'Запрошені',\n      },\n      requestsTab: {\n        autoSuggestions: {\n          headerSubtitle:\n            'Users who sign up with a matching email domain, will be able to see a suggestion to request to join your organization.',\n          headerTitle: 'Automatic suggestions',\n          primaryButton: 'Manage verified domains',\n        },\n        menuAction__approve: 'Approve',\n        menuAction__reject: 'Reject',\n        tableHeader__requested: 'Requested access',\n        table__emptyRow: 'No requests to display',\n      },\n      start: {\n        headerTitle__invitations: 'Invitations',\n        headerTitle__members: 'Members',\n        headerTitle__requests: 'Requests',\n      },\n    },\n    navbar: {\n      billing: undefined,\n      description: 'Manage your organization.',\n      general: 'General',\n      members: 'Members',\n      title: 'Organization',\n    },\n    plansPage: {\n      alerts: {\n        noPermissionsToManageBilling: undefined,\n      },\n      title: undefined,\n    },\n    profilePage: {\n      dangerSection: {\n        deleteOrganization: {\n          actionDescription: 'Type \"{{organizationName}}\" below to continue.',\n          messageLine1: 'Are you sure you want to delete this organization?',\n          messageLine2: 'This action is permanent and irreversible.',\n          successMessage: 'You have deleted the organization.',\n          title: 'Delete organization',\n        },\n        leaveOrganization: {\n          actionDescription: 'Type \"{{organizationName}}\" below to continue.',\n          messageLine1:\n            'Ви впевнені, що хочете покинути цю організацію? Ви втратите доступ до цієї організації та її додатків.',\n          messageLine2: 'Ця дія є постійною і незворотною.',\n          successMessage: 'Ви покинули організацію.',\n          title: 'Покинути організацію',\n        },\n        title: 'Небезпека',\n      },\n      domainSection: {\n        menuAction__manage: 'Manage',\n        menuAction__remove: 'Delete',\n        menuAction__verify: 'Verify',\n        primaryButton: 'Add domain',\n        subtitle:\n          'Allow users to join the organization automatically or request to join based on a verified email domain.',\n        title: 'Verified domains',\n      },\n      successMessage: 'Організацію було оновлено.',\n      title: 'Профіль організації',\n    },\n    removeDomainPage: {\n      messageLine1: 'The email domain {{domain}} will be removed.',\n      messageLine2: 'Users won’t be able to join the organization automatically after this.',\n      successMessage: '{{domain}} has been removed.',\n      title: 'Remove domain',\n    },\n    start: {\n      headerTitle__general: 'General',\n      headerTitle__members: 'Учасники',\n      profileSection: {\n        primaryButton: undefined,\n        title: 'Organization Profile',\n        uploadAction__title: 'Logo',\n      },\n    },\n    verifiedDomainPage: {\n      dangerTab: {\n        calloutInfoLabel: 'Removing this domain will affect invited users.',\n        removeDomainActionLabel__remove: 'Remove domain',\n        removeDomainSubtitle: 'Remove this domain from your verified domains',\n        removeDomainTitle: 'Remove domain',\n      },\n      enrollmentTab: {\n        automaticInvitationOption__description:\n          'Users are automatically invited to join the organization when they sign-up and can join anytime.',\n        automaticInvitationOption__label: 'Automatic invitations',\n        automaticSuggestionOption__description:\n          'Users receive a suggestion to request to join, but must be approved by an admin before they are able to join the organization.',\n        automaticSuggestionOption__label: 'Automatic suggestions',\n        calloutInfoLabel: 'Changing the enrollment mode will only affect new users.',\n        calloutInvitationCountLabel: 'Pending invitations sent to users: {{count}}',\n        calloutSuggestionCountLabel: 'Pending suggestions sent to users: {{count}}',\n        manualInvitationOption__description: 'Users can only be invited manually to the organization.',\n        manualInvitationOption__label: 'No automatic enrollment',\n        subtitle: 'Choose how users from this domain can join the organization.',\n      },\n      start: {\n        headerTitle__danger: 'Danger',\n        headerTitle__enrollment: 'Enrollment options',\n      },\n      subtitle: 'The domain {{domain}} is now verified. Continue by selecting enrollment mode.',\n      title: 'Update {{domain}}',\n    },\n    verifyDomainPage: {\n      formSubtitle: 'Enter the verification code sent to your email address',\n      formTitle: 'Verification code',\n      resendButton: \"Didn't receive a code? Resend\",\n      subtitle: 'The domain {{domainName}} needs to be verified via email.',\n      subtitleVerificationCodeScreen: 'A verification code was sent to {{emailAddress}}. Enter the code to continue.',\n      title: 'Verify domain',\n    },\n  },\n  organizationSwitcher: {\n    action__createOrganization: 'Створити організацію',\n    action__invitationAccept: 'Join',\n    action__manageOrganization: 'Управління організацією',\n    action__suggestionsAccept: 'Request to join',\n    notSelected: 'Організація не обрана',\n    personalWorkspace: 'Особистий робочий простір',\n    suggestionsAcceptedLabel: 'Pending approval',\n  },\n  paginationButton__next: 'Вперед',\n  paginationButton__previous: 'Назад',\n  paginationRowText__displaying: 'Відображення',\n  paginationRowText__of: 'з',\n  reverification: {\n    alternativeMethods: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__backupCode: undefined,\n      blockButton__emailCode: undefined,\n      blockButton__passkey: undefined,\n      blockButton__password: undefined,\n      blockButton__phoneCode: undefined,\n      blockButton__totp: undefined,\n      getHelp: {\n        blockButton__emailSupport: undefined,\n        content: undefined,\n        title: undefined,\n      },\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    emailCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    noAvailableMethods: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    passkey: {\n      blockButton__passkey: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    phoneCodeMfa: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    totpMfa: {\n      formTitle: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n  signIn: {\n    accountSwitcher: {\n      action__addAccount: 'Add account',\n      action__signOutAll: 'Sign out of all accounts',\n      subtitle: 'Select the account with which you wish to continue.',\n      title: 'Choose an account',\n    },\n    alternativeMethods: {\n      actionLink: 'Допомога',\n      actionText: 'Don’t have any of these?',\n      blockButton__backupCode: 'Використовуйте код відновлення',\n      blockButton__emailCode: 'Надіслати код на {{identifier}}',\n      blockButton__emailLink: 'Надіслати посилання на {{identifier}}',\n      blockButton__passkey: undefined,\n      blockButton__password: 'Увійти з паролем',\n      blockButton__phoneCode: 'Надіслати код на {{identifier}}',\n      blockButton__totp: 'Використовуйте аутентифікатор',\n      getHelp: {\n        blockButton__emailSupport: 'Написати в підтримку',\n        content:\n          'Якщо у вас виникли труднощі з входом у Ваш акаунт, напишіть нам, і ми попрацюємо з Вами, щоб відновити доступ якнайшвидше.',\n        title: 'Допомога',\n      },\n      subtitle: 'Facing issues? You can use any of these methods to sign in.',\n      title: 'Використовувати інший метод',\n    },\n    alternativePhoneCodeProvider: {\n      formTitle: undefined,\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    backupCodeMfa: {\n      subtitle: 'щоб продовжити роботу в \"{{applicationName}}\"',\n      title: 'Введіть код відновлення',\n    },\n    emailCode: {\n      formTitle: 'Код підтвердження',\n      resendButton: 'Не отримали код? Повторно відправити',\n      subtitle: 'продовжити до {{applicationName}}',\n      title: 'Перевірте свою електронну пошту',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      expired: {\n        subtitle: 'Поверніться на початкову вкладку, щоб продовжити.',\n        title: 'Термін дії цього посилання для підтвердження закінчився',\n      },\n      failed: {\n        subtitle: 'Поверніться на початкову вкладку, щоб продовжити',\n        title: 'Це посилання для підтвердження є недійсним',\n      },\n      formSubtitle: 'Використовуйте посилання для підтвердження, надіслане на Вашу електронну пошту',\n      formTitle: 'Посилання для підтвердження',\n      loading: {\n        subtitle: 'Вас буде перенаправлено найближчим часом',\n        title: 'Вхід в систему...',\n      },\n      resendButton: 'Перевідправити посилання',\n      subtitle: 'щоб продовжити роботу в \"{{applicationName}}\"',\n      title: 'Перевірте Вашу пошту',\n      unusedTab: {\n        title: 'Вкладку можна закрити',\n      },\n      verified: {\n        subtitle: 'Ви скоро будете перенаправлені',\n        title: 'Успішний вхід',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Поверніться на попередню вкладку, щоб продовжити',\n        subtitleNewTab: 'Поверніться до щойно відкритої вкладки, щоб продовжити',\n        titleNewTab: 'Ви ввійшли на іншій вкладці',\n      },\n    },\n    forgotPassword: {\n      formTitle: 'Код відновлення пароля',\n      resendButton: 'Надіслати код ще раз',\n      subtitle: 'to reset your password',\n      subtitle_email: 'First, enter the code sent to your email ID',\n      subtitle_phone: 'First, enter the code sent to your phone',\n      title: 'Reset password',\n    },\n    forgotPasswordAlternativeMethods: {\n      blockButton__resetPassword: 'Відновити пароль',\n      label__alternativeMethods: 'Або, увійти іншим способом',\n      title: 'Забули пароль?',\n    },\n    noAvailableMethods: {\n      message: 'Не вдається виконати вхід. Немає доступного фактору автентифікації.',\n      subtitle: 'Виникла помилка',\n      title: 'Не вдалося увійти',\n    },\n    passkey: {\n      subtitle: undefined,\n      title: undefined,\n    },\n    password: {\n      actionLink: 'Використати інший метод',\n      subtitle: 'щоб продовжити роботу в \"{{applicationName}}\"',\n      title: 'Введіть пароль',\n    },\n    passwordPwned: {\n      title: undefined,\n    },\n    phoneCode: {\n      formTitle: 'Код підтвердження',\n      resendButton: 'Не отримали код? повторно відправити',\n      subtitle: 'продовжити в {{applicationName}}',\n      title: 'Перевірте свій телефон',\n    },\n    phoneCodeMfa: {\n      formTitle: 'Код підтвердження',\n      resendButton: 'Не отримали код? повторно відправити',\n      subtitle: undefined,\n      title: 'Перевірте свій телефон',\n    },\n    resetPassword: {\n      formButtonPrimary: 'Скинути пароль',\n      requiredMessage: 'For security reasons, it is required to reset your password.',\n      successMessage: 'Ваш пароль успішно змінено. Виконується вхід, зачекайте.',\n      title: 'Скинути пароль',\n    },\n    resetPasswordMfa: {\n      detailsLabel: 'Необхідно верифікувати вашу особу перед відновленням пароля',\n    },\n    start: {\n      actionLink: 'Зареєструватися',\n      actionLink__join_waitlist: undefined,\n      actionLink__use_email: 'Використовувати пошту',\n      actionLink__use_email_username: \"Використовувати пошту або ім'я користувача\",\n      actionLink__use_passkey: undefined,\n      actionLink__use_phone: 'Використовувати номер телефону',\n      actionLink__use_username: \"Використовувати ім'я користувача\",\n      actionText: 'Немає акаунта?',\n      actionText__join_waitlist: undefined,\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'щоб продовжити роботу в \"{{applicationName}}\"',\n      subtitleCombined: undefined,\n      title: 'Увійти',\n      titleCombined: undefined,\n    },\n    totpMfa: {\n      formTitle: 'Верифікаційний код',\n      subtitle: undefined,\n      title: 'Двоетапна перевірка',\n    },\n  },\n  signInEnterPasswordTitle: 'Введіть Ваш пароль',\n  signUp: {\n    alternativePhoneCodeProvider: {\n      resendButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    continue: {\n      actionLink: 'Увійти',\n      actionText: 'Уже є акаунт?',\n      subtitle: 'щоб продовжити роботу в \"{{applicationName}}\"',\n      title: 'Заповніть усі поля',\n    },\n    emailCode: {\n      formSubtitle: 'Введіть код підтвердження, надісланий на вашу електронну адресу',\n      formTitle: 'Код підтвердження',\n      resendButton: 'Не отримали код? Повторно відправити',\n      subtitle: 'продовжити до {{applicationName}}',\n      title: 'Підтвердіть свою електронну пошту',\n    },\n    emailLink: {\n      clientMismatch: {\n        subtitle: undefined,\n        title: undefined,\n      },\n      formSubtitle: 'Використовуйте посилання для підтвердження, надіслане на вашу електронну адресу',\n      formTitle: 'Посилання для підтвердження',\n      loading: {\n        title: 'Реєстрація...',\n      },\n      resendButton: 'Не отримали посилання? Повторно відправити',\n      subtitle: 'продовжити до {{applicationName}}',\n      title: 'Підтвердіть свою електронну пошту',\n      verified: {\n        title: 'Успішно зареєстровано',\n      },\n      verifiedSwitchTab: {\n        subtitle: 'Поверніться на нову вкладку, щоб продовжити',\n        subtitleNewTab: 'Повернутися до попередньої вкладки для продовження',\n        title: 'Успішно перевірено email',\n      },\n    },\n    legalConsent: {\n      checkbox: {\n        label__onlyPrivacyPolicy: undefined,\n        label__onlyTermsOfService: undefined,\n        label__termsOfServiceAndPrivacyPolicy: undefined,\n      },\n      continue: {\n        subtitle: undefined,\n        title: undefined,\n      },\n    },\n    phoneCode: {\n      formSubtitle: 'Введіть код підтвердження, надісланий на ваш номер телефону',\n      formTitle: 'Код підтвердження',\n      resendButton: 'Не отримали код? Повторно відправити',\n      subtitle: 'продовжити з {{applicationName}}',\n      title: 'Підтвердіть свій телефон',\n    },\n    restrictedAccess: {\n      actionLink: undefined,\n      actionText: undefined,\n      blockButton__emailSupport: undefined,\n      blockButton__joinWaitlist: undefined,\n      subtitle: undefined,\n      subtitleWaitlist: undefined,\n      title: undefined,\n    },\n    start: {\n      actionLink: 'Увійти',\n      actionLink__use_email: undefined,\n      actionLink__use_phone: undefined,\n      actionText: 'Уже є акаунт?',\n      alternativePhoneCodeProvider: {\n        actionLink: undefined,\n        label: undefined,\n        subtitle: undefined,\n        title: undefined,\n      },\n      subtitle: 'щоб продовжити роботу в \"{{applicationName}}\"',\n      subtitleCombined: 'щоб продовжити роботу в \"{{applicationName}}\"',\n      title: 'Створіть Ваш акаунт',\n      titleCombined: 'Створіть Ваш акаунт',\n    },\n  },\n  socialButtonsBlockButton: 'Продовжити за допомогою {{provider|titleize}}',\n  socialButtonsBlockButtonManyInView: undefined,\n  unstable__errors: {\n    already_a_member_in_organization: undefined,\n    captcha_invalid:\n      'Sign up unsuccessful due to failed security validations. Please refresh the page to try again or reach out to support for more assistance.',\n    captcha_unavailable:\n      'Sign up unsuccessful due to failed bot validation. Please refresh the page to try again or reach out to support for more assistance.',\n    form_code_incorrect: undefined,\n    form_identifier_exists__email_address: undefined,\n    form_identifier_exists__phone_number: undefined,\n    form_identifier_exists__username: undefined,\n    form_identifier_not_found: 'Не вдалося знайти акаунт з цими даними.',\n    form_param_format_invalid: undefined,\n    form_param_format_invalid__email_address: 'Email address must be a valid email address.',\n    form_param_format_invalid__phone_number: 'Phone number must be in a valid international format',\n    form_param_max_length_exceeded__first_name: 'First name should not exceed 256 characters.',\n    form_param_max_length_exceeded__last_name: 'Last name should not exceed 256 characters.',\n    form_param_max_length_exceeded__name: 'Name should not exceed 256 characters.',\n    form_param_nil: undefined,\n    form_param_value_invalid: undefined,\n    form_password_incorrect: undefined,\n    form_password_length_too_short: undefined,\n    form_password_not_strong_enough: 'Ваш пароль недостатньо надійний.',\n    form_password_pwned: 'Цей пароль було зламано і його не можна використовувати, спробуйте інший пароль.',\n    form_password_pwned__sign_in: undefined,\n    form_password_size_in_bytes_exceeded:\n      'Ваш пароль перевищує максимально допустиму кількість байтів, скоротіть його або видаліть деякі спеціальні символи.',\n    form_password_validation_failed: 'Невірний пароль',\n    form_username_invalid_character: undefined,\n    form_username_invalid_length: undefined,\n    identification_deletion_failed: 'You cannot delete your last identification.',\n    not_allowed_access:\n      \"Адреса електронної пошти або номер телефону не дозволено для реєстрації. Це може бути пов'язано з використанням '+', '=', '#' або '.' в адресі електронної пошти, використанням домену, пов'язаного з тимчасовою електронною поштою, або явного виключення.\",\n    organization_domain_blocked: undefined,\n    organization_domain_common: undefined,\n    organization_domain_exists_for_enterprise_connection: undefined,\n    organization_membership_quota_exceeded: undefined,\n    organization_minimum_permissions_needed: undefined,\n    passkey_already_exists: undefined,\n    passkey_not_supported: undefined,\n    passkey_pa_not_supported: undefined,\n    passkey_registration_cancelled: undefined,\n    passkey_retrieval_cancelled: undefined,\n    passwordComplexity: {\n      maximumLength: 'менше {{length}} символів',\n      minimumLength: '{{length}} або більше символів',\n      requireLowercase: 'букву в нижньому регістрі',\n      requireNumbers: 'цифру',\n      requireSpecialCharacter: 'спеціальний символ',\n      requireUppercase: 'букву у верхньому регістрі',\n      sentencePrefix: 'Ваш пароль повинен містити',\n    },\n    phone_number_exists: 'Цей номер телефону вже використовується. Спробуйте інший.',\n    session_exists: 'Ви вже увійшли в систему.',\n    web3_missing_identifier: undefined,\n    zxcvbn: {\n      couldBeStronger: 'Ваш пароль підходить, але міг би бути надійнішим. Спробуйте додати більше символів.',\n      goodPassword: 'Хороша робота. Це відмінний пароль.',\n      notEnough: 'Ваш пароль недостатньо надійний.',\n      suggestions: {\n        allUppercase: 'Робіть великими деякі, але не всі букви.',\n        anotherWord: 'Додайте більше слів, які менш поширені.',\n        associatedYears: \"Уникайте років, які пов'язані з вами.\",\n        capitalization: 'Робіть великими не тільки першу букву',\n        dates: \"Уникайте дат і років, які пов'язані з вами.\",\n        l33t: 'Уникайте передбачуваних замін букв, таких як \"@\" замість \"a\".',\n        longerKeyboardPattern: 'Використовуйте довші поєднання клавіш і кілька разів змінюйте напрямок введення.',\n        noNeed: 'Ви можете створювати надійні паролі без використання символів, цифр або великих літер.',\n        pwned: 'Якщо ви використовуєте цей пароль в іншому місці, вам слід змінити його.',\n        recentYears: 'Уникайте останніх років.',\n        repeated: 'Уникайте повторюваних слів і символів.',\n        reverseWords: 'Уникайте зворотного написання часто використовуваних слів.',\n        sequences: 'Уникайте частих послідовностей символів.',\n        useWords: 'Використовуйте кілька слів, але уникайте поширених фраз.',\n      },\n      warnings: {\n        common: 'Це поширений пароль.',\n        commonNames: 'Поширені імена та прізвища легко вгадати.',\n        dates: 'Дати легко вгадати.',\n        extendedRepeat: 'Шаблони символів, що повторюються, такі як \"abcabcabcabc\", легко вгадати.',\n        keyPattern: 'Короткі поєднання клавіш легко вгадати.',\n        namesByThemselves: 'Одні імена або прізвища легко вгадати.',\n        pwned: 'Ваш пароль було розкрито внаслідок витоку даних в Інтернеті.',\n        recentYears: 'Останні роки легко вгадати.',\n        sequences: 'Часті послідовності символів, такі як \"abc\", легко вгадати.',\n        similarToCommon: 'Цей пароль схожий на часто використовуваний пароль.',\n        simpleRepeat: 'Символи, що повторюються, такі як \"aaa\", легко вгадати.',\n        straightRow: 'Прямі ряди клавіш на клавіатурі легко вгадати.',\n        topHundred: 'Це часто використовуваний пароль.',\n        topTen: 'Це дуже часто використовуваний пароль.',\n        userInputs: \"Не повинно бути ніяких особистих даних або даних, пов'язаних зі сторінкою.\",\n        wordByItself: 'Окремі слова легко вгадати.',\n      },\n    },\n  },\n  userButton: {\n    action__addAccount: 'Додати акаунт',\n    action__manageAccount: 'Управління акаунтом',\n    action__signOut: 'Вийти',\n    action__signOutAll: 'Вийти з усіх акаунтів',\n  },\n  userProfile: {\n    backupCodePage: {\n      actionLabel__copied: 'Скопійовано!',\n      actionLabel__copy: 'Копіювати все',\n      actionLabel__download: 'Завантажити .txt',\n      actionLabel__print: 'Друк',\n      infoText1: 'Резервні коди будуть включені для цього облікового запису.',\n      infoText2:\n        'Зберігайте резервні коди в таємниці та зберігайте їх у безпеці. Ви можете створити нові резервні коди, якщо підозрюєте, що вони були скомпрометовані.',\n      subtitle__codelist: 'Зберігайте їх у безпеці та не повідомляйте нікому.',\n      successMessage:\n        'Резервні коди ввімкнено. Ви можете використовувати один із цих кодів для входу до свого облікового запису, якщо ви втратите доступ до свого аутентифікаційного пристрою. Кожен код може бути використаний тільки один раз.',\n      successSubtitle:\n        'Ви можете використовувати один із цих кодів для входу у свій обліковий запис, якщо ви втратите доступ до свого аутентифікаційного пристрою.',\n      title: 'Додати резервний код підтвердження',\n      title__codelist: 'Резервні коди',\n    },\n    billingPage: {\n      paymentSourcesSection: {\n        actionLabel__default: undefined,\n        actionLabel__remove: undefined,\n        add: undefined,\n        addSubtitle: undefined,\n        cancelButton: undefined,\n        formButtonPrimary__add: undefined,\n        formButtonPrimary__pay: undefined,\n        payWithTestCardButton: undefined,\n        removeResource: {\n          messageLine1: undefined,\n          messageLine2: undefined,\n          successMessage: undefined,\n          title: undefined,\n        },\n        title: undefined,\n      },\n      start: {\n        headerTitle__plans: undefined,\n        headerTitle__statements: undefined,\n        headerTitle__subscriptions: undefined,\n      },\n      subscriptionsListSection: {\n        actionLabel__newSubscription: undefined,\n        actionLabel__switchPlan: undefined,\n        title: undefined,\n      },\n      subscriptionsSection: {\n        actionLabel__default: undefined,\n      },\n      switchPlansSection: {\n        title: undefined,\n      },\n      title: undefined,\n    },\n    connectedAccountPage: {\n      formHint: 'Виберіть провайдера для підключення вашого акаунта.',\n      formHint__noAccounts: 'Немає доступних провайдерів зовнішніх акаунтів.',\n      removeResource: {\n        messageLine1: '{{identifier}} буде видалено з вашого облікового запису.',\n        messageLine2:\n          'Ви більше не зможете використовувати цей підключений акаунт, і будь-які залежні функції більше не працюватимуть.',\n        successMessage: '{{connectedAccount}} було видалено з вашого облікового запису.',\n        title: 'Видалити підключений акаунт',\n      },\n      socialButtonsBlockButton: 'Підключити акаунт {{provider|titleize}}',\n      successMessage: 'Провайдера було додано до вашого акаунта',\n      title: 'Додати підключений акаунт',\n    },\n    deletePage: {\n      actionDescription: 'Введіть \"Видалити акаунт\" нижче, щоб продовжити.',\n      confirm: 'Видалити акаунт',\n      messageLine1: 'Ви впевнені, що хочете видалити свій акаунт?',\n      messageLine2: 'Ця дія є остаточною та незворотною.',\n      title: 'Видалити акаунт',\n    },\n    emailAddressPage: {\n      emailCode: {\n        formHint: 'На цю адресу електронної пошти буде надіслано лист із верифікаційним кодом.',\n        formSubtitle: 'Введіть верифікаційний код, відправлений на {{identifier}}',\n        formTitle: 'Верифікаційний код',\n        resendButton: 'Надіслати код повторно',\n        successMessage: 'Адресу електронної пошти {{identifier}} було додано до вашого облікового запису.',\n      },\n      emailLink: {\n        formHint: 'На цю адресу електронної пошти буде надіслано верифікаційне посилання.',\n        formSubtitle: 'Натисніть на верифікаційне посилання в листі, відправленому на {{identifier}}',\n        formTitle: 'Верифікаційне посилання',\n        resendButton: 'Надіслати посилання повторно',\n        successMessage: 'Адресу електронної пошти {{identifier}} було додано до вашого облікового запису.',\n      },\n      enterpriseSSOLink: {\n        formButton: undefined,\n        formSubtitle: undefined,\n      },\n      formHint: undefined,\n      removeResource: {\n        messageLine1: '{{identifier}} буде видалено з цього акаунта.',\n        messageLine2: 'Ви більше не зможете увійти з використанням цієї адреси електронної пошти.',\n        successMessage: '{{emailAddress}} було видалено з вашого облікового запису.',\n        title: 'Видалити адресу електронної пошти',\n      },\n      title: 'Додати адресу електронної пошти',\n      verifyTitle: 'Verify email address',\n    },\n    formButtonPrimary__add: 'Add',\n    formButtonPrimary__continue: 'Продовжити',\n    formButtonPrimary__finish: 'Завершити',\n    formButtonPrimary__remove: 'Remove',\n    formButtonPrimary__save: 'Save',\n    formButtonReset: 'Скасувати',\n    mfaPage: {\n      formHint: 'Виберіть метод для додавання.',\n      title: 'Додати двофакторну аутентифікацію',\n    },\n    mfaPhoneCodePage: {\n      backButton: 'Use existing number',\n      primaryButton__addPhoneNumber: 'Додати номер телефону',\n      removeResource: {\n        messageLine1: '{{identifier}} більше не буде отримувати коди підтвердження при вході в систему.',\n        messageLine2: 'Ваш обліковий запис буде менш захищеним. Ви впевнені, що хочете продовжити?',\n        successMessage: 'Двоетапна перевірка з кодом з SMS була видалена для {{mfaPhoneCode}}',\n        title: 'Видалити двоетапну перевірку',\n      },\n      subtitle__availablePhoneNumbers: 'Виберіть номер телефону для реєстрації у двоетапній перевірці з кодом з SMS.',\n      subtitle__unavailablePhoneNumbers:\n        'Немає доступних номерів телефону для реєстрації в двоетапній перевірці з кодом з SMS.',\n      successMessage1:\n        'When signing in, you will need to enter a verification code sent to this phone number as an additional step.',\n      successMessage2:\n        'Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.',\n      successTitle: 'SMS code verification enabled',\n      title: 'Додати перевірку кодом з SMS',\n    },\n    mfaTOTPPage: {\n      authenticatorApp: {\n        buttonAbleToScan__nonPrimary: 'Замість цього відскануйте QR-код',\n        buttonUnableToScan__nonPrimary: 'Не вдається відсканувати QR-код?',\n        infoText__ableToScan:\n          \"Налаштуйте новий метод входу у вашому застосунку аутентифікації та відскануйте наступний QR-код, щоб пов'язати його з вашим обліковим записом.\",\n        infoText__unableToScan:\n          'Налаштуйте новий метод входу у вашому застосунку автентифікації та введіть нижче наданий ключ.',\n        inputLabel__unableToScan1:\n          \"Переконайтеся, що ввімкнено одноразові паролі на основі часу, потім завершіть зв'язування свого облікового запису.\",\n        inputLabel__unableToScan2:\n          'Крім того, якщо ваш додаток аутентифікації підтримує URI TOTP, ви також можете скопіювати повний URI.',\n      },\n      removeResource: {\n        messageLine1:\n          'Верифікаційний код із цього додатка автентифікації більше не буде потрібен під час входу в систему.',\n        messageLine2: 'Ваш акаунт буде менш захищеним. Ви впевнені, що хочете продовжити?',\n        successMessage: 'Двоетапну автентифікацію через застосунок автентифікації було видалено.',\n        title: 'Видалення двоетапної аутентифікації',\n      },\n      successMessage:\n        'Двоетапна перевірка ввімкнена. Під час входу в систему вам потрібно буде ввести верифікаційний код із цього додатка як додатковий крок.',\n      title: 'Додати додаток аутентифікації',\n      verifySubtitle: 'Введіть верифікаційний код, створений вашим додатком аутентифікації',\n      verifyTitle: 'Верифікаційний код',\n    },\n    mobileButton__menu: 'Меню',\n    navbar: {\n      account: 'Profile',\n      billing: undefined,\n      description: 'Manage your account info.',\n      security: 'Security',\n      title: 'Account',\n    },\n    passkeyScreen: {\n      removeResource: {\n        messageLine1: undefined,\n        title: undefined,\n      },\n      subtitle__rename: undefined,\n      title__rename: undefined,\n    },\n    passwordPage: {\n      checkboxInfoText__signOutOfOtherSessions:\n        'It is recommended to sign out of all other devices which may have used your old password.',\n      readonly: 'Your password can currently not be edited because you can sign in only via the enterprise connection.',\n      successMessage__set: 'Ваш пароль встановлено.',\n      successMessage__signOutOfOtherSessions: 'Усі інші пристрої були виведені із системи.',\n      successMessage__update: 'Ваш пароль було оновлено.',\n      title__set: 'Встановити пароль',\n      title__update: 'Змінити пароль',\n    },\n    phoneNumberPage: {\n      infoText: 'На цей номер телефону буде надіслано текстове повідомлення з верифікаційним посиланням.',\n      removeResource: {\n        messageLine1: '{{identifier}} буде видалено з цього облікового запису.',\n        messageLine2: 'Ви більше не зможете увійти, використовуючи цей номер телефону.',\n        successMessage: '{{phoneNumber}} було видалено з вашого облікового запису.',\n        title: 'Видалити номер телефону',\n      },\n      successMessage: '{{identifier}} було додано до вашого облікового запису.',\n      title: 'Додати номер телефону',\n      verifySubtitle: 'Enter the verification code sent to {{identifier}}',\n      verifyTitle: 'Verify phone number',\n    },\n    plansPage: {\n      title: undefined,\n    },\n    profilePage: {\n      fileDropAreaHint: 'Завантажте зображення у форматах JPG, PNG, GIF або WEBP розміром менше 10 МБ',\n      imageFormDestructiveActionSubtitle: 'Видалити зображення',\n      imageFormSubtitle: 'Завантажити зображення',\n      imageFormTitle: 'Зображення профілю',\n      readonly: 'Your profile information has been provided by the enterprise connection and cannot be edited.',\n      successMessage: 'Ваш профіль було оновлено.',\n      title: 'Оновити профіль',\n    },\n    start: {\n      activeDevicesSection: {\n        destructiveAction: 'Вийти з пристрою',\n        title: 'Активні пристрої',\n      },\n      connectedAccountsSection: {\n        actionLabel__connectionFailed: 'Спробувати знову',\n        actionLabel__reauthorize: 'Авторизувати зараз',\n        destructiveActionTitle: 'Видалити',\n        primaryButton: 'Підключити акаунт',\n        subtitle__disconnected: undefined,\n        subtitle__reauthorize:\n          'The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues',\n        title: 'Підключені акаунти',\n      },\n      dangerSection: {\n        deleteAccountButton: 'Видалити акаунт',\n        title: 'Небезпека',\n      },\n      emailAddressesSection: {\n        destructiveAction: 'Видалити адресу електронної пошти',\n        detailsAction__nonPrimary: 'Встановити як основну',\n        detailsAction__primary: 'Завершити перевірку',\n        detailsAction__unverified: 'Завершити перевірку',\n        primaryButton: 'Додати адресу електронної пошти',\n        title: 'Адреси електронної пошти',\n      },\n      enterpriseAccountsSection: {\n        title: 'Enterprise accounts',\n      },\n      headerTitle__account: 'Обліковий запис',\n      headerTitle__security: 'Безпека',\n      mfaSection: {\n        backupCodes: {\n          actionLabel__regenerate: 'Згенерувати коди',\n          headerTitle: 'Резервні коди',\n          subtitle__regenerate:\n            'Отримайте новий набір безпечних резервних кодів. Попередні резервні коди будуть видалені і не можуть бути використані.',\n          title__regenerate: 'Згенерувати нові резервні коди',\n        },\n        phoneCode: {\n          actionLabel__setDefault: 'Встановити за замовчуванням',\n          destructiveActionLabel: 'Видалити номер телефону',\n        },\n        primaryButton: 'Додати двофакторну аутентифікацію',\n        title: 'Двофакторна аутентифікація',\n        totp: {\n          destructiveActionTitle: 'Видалити',\n          headerTitle: 'Додаток аутентифікації',\n        },\n      },\n      passkeysSection: {\n        menuAction__destructive: undefined,\n        menuAction__rename: undefined,\n        primaryButton: undefined,\n        title: undefined,\n      },\n      passwordSection: {\n        primaryButton__setPassword: 'Встановити пароль',\n        primaryButton__updatePassword: 'Змінити пароль',\n        title: 'Пароль',\n      },\n      phoneNumbersSection: {\n        destructiveAction: 'Видалити номер телефону',\n        detailsAction__nonPrimary: 'Встановити як основний',\n        detailsAction__primary: 'Завершити верифікацію',\n        detailsAction__unverified: 'Завершити верифікацію',\n        primaryButton: 'Додати номер телефону',\n        title: 'Номери телефонів',\n      },\n      profileSection: {\n        primaryButton: undefined,\n        title: 'Профіль',\n      },\n      usernameSection: {\n        primaryButton__setUsername: \"Встановити ім'я користувача\",\n        primaryButton__updateUsername: \"Змінити ім'я користувача\",\n        title: \"Ім'я користувача\",\n      },\n      web3WalletsSection: {\n        destructiveAction: 'Видалити гаманець',\n        detailsAction__nonPrimary: undefined,\n        primaryButton: 'Web3 гаманці',\n        title: 'Web3 гаманці',\n      },\n    },\n    usernamePage: {\n      successMessage: \"Ім'я користувача було оновлено.\",\n      title__set: \"Оновити ім'я користувача\",\n      title__update: \"Оновити ім'я користувача\",\n    },\n    web3WalletPage: {\n      removeResource: {\n        messageLine1: '{{identifier}} буде видалено з цього облікового запису.',\n        messageLine2: 'Ви більше не зможете Увійти з використанням цього web3 гаманця.',\n        successMessage: '{{web3Wallet}} було видалено з вашого облікового запису.',\n        title: 'Видалити web3 гаманець',\n      },\n      subtitle__availableWallets: 'Виберіть web3 гаманець для підключення до вашого облікового запису.',\n      subtitle__unavailableWallets: 'Немає доступних web3 гаманців.',\n      successMessage: 'Гаманець було додано до вашого облікового запису.',\n      title: 'Додати web3 гаманець',\n      web3WalletButtonsBlockButton: undefined,\n    },\n  },\n  waitlist: {\n    start: {\n      actionLink: undefined,\n      actionText: undefined,\n      formButton: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n    success: {\n      message: undefined,\n      subtitle: undefined,\n      title: undefined,\n    },\n  },\n} as const;\n"], "mappings": ";AAcO,IAAM,OAA6B;AAAA,EACxC,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,gCAAgC;AAAA,EAChC,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,YAAY;AAAA,EACZ,UAAU;AAAA,IACR,kBAAkB;AAAA,IAClB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,+BAA+B;AAAA,IAC/B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,wBAAwB;AAAA,IACxB,UAAU;AAAA,MACR,gCAAgC;AAAA,MAChC,qCAAqC;AAAA,MACrC,iBAAiB;AAAA,MACjB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,WAAW;AAAA,QACT,sBAAsB;AAAA,QACtB,oBAAoB;AAAA,QACpB,2BAA2B;AAAA,QAC3B,kBAAkB;AAAA,MACpB;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,MACP,0BAA0B;AAAA,MAC1B,+BAA+B;AAAA,IACjC;AAAA,IACA,iBAAiB;AAAA,IACjB,uBAAuB;AAAA,IACvB,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,KAAK;AAAA,IACL,gBAAgB;AAAA,IAChB,eAAe;AAAA,MACb,qBAAqB;AAAA,QACnB,QAAQ;AAAA,QACR,SAAS;AAAA,MACX;AAAA,MACA,KAAK;AAAA,QACH,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT,cAAc;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ;AAAA,IACA,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,oBAAoB;AAAA,IAClB,kBAAkB;AAAA,IAClB,YAAY;AAAA,MACV,iBAAiB;AAAA,IACnB;AAAA,IACA,OAAO;AAAA,EACT;AAAA,EACA,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,IACT,eAAe;AAAA,IACf,SAAS;AAAA,EACX;AAAA,EACA,aAAa;AAAA,EACb,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,2CACE;AAAA,EACF,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,+CAA+C;AAAA,EAC/C,2DAA2D;AAAA,EAC3D,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,2DACE;AAAA,EACF,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,sBAAsB;AAAA,EACtB,wCAAwC;AAAA,EACxC,0BAA0B;AAAA,EAC1B,kBAAkB;AAAA,IAChB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT;AAAA,EACA,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,kBAAkB;AAAA,IAChB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,2BAA2B;AAAA,IAC3B,oBAAoB;AAAA,IACpB,yBAAyB;AAAA,IACzB,UAAU;AAAA,IACV,0BAA0B;AAAA,IAC1B,OAAO;AAAA,IACP,sBAAsB;AAAA,EACxB;AAAA,EACA,qBAAqB;AAAA,IACnB,4BAA4B;AAAA,IAC5B,4BAA4B;AAAA,IAC5B,yBAAyB;AAAA,IACzB,mBAAmB;AAAA,IACnB,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,UACE;AAAA,MACF,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,4BAA4B;AAAA,MAC5B,6BAA6B;AAAA,MAC7B,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,kBAAkB;AAAA,QAChB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,mBAAmB;AAAA,QACnB,mBAAmB;AAAA,MACrB;AAAA,MACA,wBAAwB;AAAA,MACxB,gBAAgB;AAAA,QACd,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,MACA,mBAAmB;AAAA,QACjB,oBAAoB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,aAAa;AAAA,QACX,iBAAiB;AAAA,UACf,gBACE;AAAA,UACF,aAAa;AAAA,UACb,eAAe;AAAA,QACjB;AAAA,QACA,qBAAqB;AAAA,QACrB,oBAAoB;AAAA,QACpB,wBAAwB;AAAA,QACxB,iBAAiB;AAAA,MACnB;AAAA,MACA,OAAO;AAAA,QACL,0BAA0B;AAAA,QAC1B,sBAAsB;AAAA,QACtB,uBAAuB;AAAA,MACzB;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,aAAa;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,QAAQ;AAAA,QACN,8BAA8B;AAAA,MAChC;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,eAAe;AAAA,QACb,oBAAoB;AAAA,UAClB,mBAAmB;AAAA,UACnB,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,mBAAmB;AAAA,UACnB,cACE;AAAA,UACF,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,UACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,MACtB,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,QACP,qBAAqB;AAAA,MACvB;AAAA,IACF;AAAA,IACA,oBAAoB;AAAA,MAClB,WAAW;AAAA,QACT,kBAAkB;AAAA,QAClB,iCAAiC;AAAA,QACjC,sBAAsB;AAAA,QACtB,mBAAmB;AAAA,MACrB;AAAA,MACA,eAAe;AAAA,QACb,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,wCACE;AAAA,QACF,kCAAkC;AAAA,QAClC,kBAAkB;AAAA,QAClB,6BAA6B;AAAA,QAC7B,6BAA6B;AAAA,QAC7B,qCAAqC;AAAA,QACrC,+BAA+B;AAAA,QAC/B,UAAU;AAAA,MACZ;AAAA,MACA,OAAO;AAAA,QACL,qBAAqB;AAAA,QACrB,yBAAyB;AAAA,MAC3B;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gCAAgC;AAAA,MAChC,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,sBAAsB;AAAA,IACpB,4BAA4B;AAAA,IAC5B,0BAA0B;AAAA,IAC1B,4BAA4B;AAAA,IAC5B,2BAA2B;AAAA,IAC3B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,0BAA0B;AAAA,EAC5B;AAAA,EACA,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,gBAAgB;AAAA,IACd,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SAAS;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,sBAAsB;AAAA,MACtB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN,iBAAiB;AAAA,MACf,oBAAoB;AAAA,MACpB,oBAAoB;AAAA,MACpB,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,yBAAyB;AAAA,MACzB,wBAAwB;AAAA,MACxB,wBAAwB;AAAA,MACxB,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,SAAS;AAAA,QACP,2BAA2B;AAAA,QAC3B,SACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,8BAA8B;AAAA,MAC5B,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,WAAW;AAAA,QACT,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,aAAa;AAAA,MACf;AAAA,IACF;AAAA,IACA,gBAAgB;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kCAAkC;AAAA,MAChC,4BAA4B;AAAA,MAC5B,2BAA2B;AAAA,MAC3B,OAAO;AAAA,IACT;AAAA,IACA,oBAAoB;AAAA,MAClB,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,cAAc;AAAA,MACZ,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,mBAAmB;AAAA,MACnB,iBAAiB;AAAA,MACjB,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,uBAAuB;AAAA,MACvB,gCAAgC;AAAA,MAChC,yBAAyB;AAAA,MACzB,uBAAuB;AAAA,MACvB,0BAA0B;AAAA,MAC1B,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,MACP,WAAW;AAAA,MACX,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,IACN,8BAA8B;AAAA,MAC5B,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,WAAW;AAAA,MACT,gBAAgB;AAAA,QACd,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,WAAW;AAAA,MACX,SAAS;AAAA,QACP,OAAO;AAAA,MACT;AAAA,MACA,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,mBAAmB;AAAA,QACjB,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,UAAU;AAAA,QACR,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,QAC3B,uCAAuC;AAAA,MACzC;AAAA,MACA,UAAU;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,WAAW;AAAA,MACT,cAAc;AAAA,MACd,WAAW;AAAA,MACX,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,2BAA2B;AAAA,MAC3B,2BAA2B;AAAA,MAC3B,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,YAAY;AAAA,MACZ,8BAA8B;AAAA,QAC5B,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,UAAU;AAAA,QACV,OAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,kBAAkB;AAAA,MAClB,OAAO;AAAA,MACP,eAAe;AAAA,IACjB;AAAA,EACF;AAAA,EACA,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,kBAAkB;AAAA,IAChB,kCAAkC;AAAA,IAClC,iBACE;AAAA,IACF,qBACE;AAAA,IACF,qBAAqB;AAAA,IACrB,uCAAuC;AAAA,IACvC,sCAAsC;AAAA,IACtC,kCAAkC;AAAA,IAClC,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,0CAA0C;AAAA,IAC1C,yCAAyC;AAAA,IACzC,4CAA4C;AAAA,IAC5C,2CAA2C;AAAA,IAC3C,sCAAsC;AAAA,IACtC,gBAAgB;AAAA,IAChB,0BAA0B;AAAA,IAC1B,yBAAyB;AAAA,IACzB,gCAAgC;AAAA,IAChC,iCAAiC;AAAA,IACjC,qBAAqB;AAAA,IACrB,8BAA8B;AAAA,IAC9B,sCACE;AAAA,IACF,iCAAiC;AAAA,IACjC,iCAAiC;AAAA,IACjC,8BAA8B;AAAA,IAC9B,gCAAgC;AAAA,IAChC,oBACE;AAAA,IACF,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,sDAAsD;AAAA,IACtD,wCAAwC;AAAA,IACxC,yCAAyC;AAAA,IACzC,wBAAwB;AAAA,IACxB,uBAAuB;AAAA,IACvB,0BAA0B;AAAA,IAC1B,gCAAgC;AAAA,IAChC,6BAA6B;AAAA,IAC7B,oBAAoB;AAAA,MAClB,eAAe;AAAA,MACf,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,yBAAyB;AAAA,MACzB,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,qBAAqB;AAAA,IACrB,gBAAgB;AAAA,IAChB,yBAAyB;AAAA,IACzB,QAAQ;AAAA,MACN,iBAAiB;AAAA,MACjB,cAAc;AAAA,MACd,WAAW;AAAA,MACX,aAAa;AAAA,QACX,cAAc;AAAA,QACd,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,gBAAgB;AAAA,QAChB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,uBAAuB;AAAA,QACvB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,aAAa;AAAA,QACb,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,MACZ;AAAA,MACA,UAAU;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,OAAO;AAAA,QACP,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,OAAO;AAAA,QACP,aAAa;AAAA,QACb,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AAAA,IACV,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,EACtB;AAAA,EACA,aAAa;AAAA,IACX,gBAAgB;AAAA,MACd,qBAAqB;AAAA,MACrB,mBAAmB;AAAA,MACnB,uBAAuB;AAAA,MACvB,oBAAoB;AAAA,MACpB,WAAW;AAAA,MACX,WACE;AAAA,MACF,oBAAoB;AAAA,MACpB,gBACE;AAAA,MACF,iBACE;AAAA,MACF,OAAO;AAAA,MACP,iBAAiB;AAAA,IACnB;AAAA,IACA,aAAa;AAAA,MACX,uBAAuB;AAAA,QACrB,sBAAsB;AAAA,QACtB,qBAAqB;AAAA,QACrB,KAAK;AAAA,QACL,aAAa;AAAA,QACb,cAAc;AAAA,QACd,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,uBAAuB;AAAA,QACvB,gBAAgB;AAAA,UACd,cAAc;AAAA,UACd,cAAc;AAAA,UACd,gBAAgB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,QACL,oBAAoB;AAAA,QACpB,yBAAyB;AAAA,QACzB,4BAA4B;AAAA,MAC9B;AAAA,MACA,0BAA0B;AAAA,QACxB,8BAA8B;AAAA,QAC9B,yBAAyB;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,QACpB,sBAAsB;AAAA,MACxB;AAAA,MACA,oBAAoB;AAAA,QAClB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,IACT;AAAA,IACA,sBAAsB;AAAA,MACpB,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cACE;AAAA,QACF,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,MAC1B,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,mBAAmB;AAAA,MACnB,SAAS;AAAA,MACT,cAAc;AAAA,MACd,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,QACT,UAAU;AAAA,QACV,cAAc;AAAA,QACd,WAAW;AAAA,QACX,cAAc;AAAA,QACd,gBAAgB;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QACjB,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,MACA,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,IACf;AAAA,IACA,wBAAwB;AAAA,IACxB,6BAA6B;AAAA,IAC7B,2BAA2B;AAAA,IAC3B,2BAA2B;AAAA,IAC3B,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACP,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,kBAAkB;AAAA,MAChB,YAAY;AAAA,MACZ,+BAA+B;AAAA,MAC/B,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,iCAAiC;AAAA,MACjC,mCACE;AAAA,MACF,iBACE;AAAA,MACF,iBACE;AAAA,MACF,cAAc;AAAA,MACd,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,gCAAgC;AAAA,QAChC,sBACE;AAAA,QACF,wBACE;AAAA,QACF,2BACE;AAAA,QACF,2BACE;AAAA,MACJ;AAAA,MACA,gBAAgB;AAAA,QACd,cACE;AAAA,QACF,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBACE;AAAA,MACF,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,oBAAoB;AAAA,IACpB,QAAQ;AAAA,MACN,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,eAAe;AAAA,MACb,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,OAAO;AAAA,MACT;AAAA,MACA,kBAAkB;AAAA,MAClB,eAAe;AAAA,IACjB;AAAA,IACA,cAAc;AAAA,MACZ,0CACE;AAAA,MACF,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,wCAAwC;AAAA,MACxC,wBAAwB;AAAA,MACxB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,iBAAiB;AAAA,MACf,UAAU;AAAA,MACV,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,gBAAgB;AAAA,MAChB,aAAa;AAAA,IACf;AAAA,IACA,WAAW;AAAA,MACT,OAAO;AAAA,IACT;AAAA,IACA,aAAa;AAAA,MACX,kBAAkB;AAAA,MAClB,oCAAoC;AAAA,MACpC,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,gBAAgB;AAAA,MAChB,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,sBAAsB;AAAA,QACpB,mBAAmB;AAAA,QACnB,OAAO;AAAA,MACT;AAAA,MACA,0BAA0B;AAAA,QACxB,+BAA+B;AAAA,QAC/B,0BAA0B;AAAA,QAC1B,wBAAwB;AAAA,QACxB,eAAe;AAAA,QACf,wBAAwB;AAAA,QACxB,uBACE;AAAA,QACF,OAAO;AAAA,MACT;AAAA,MACA,eAAe;AAAA,QACb,qBAAqB;AAAA,QACrB,OAAO;AAAA,MACT;AAAA,MACA,uBAAuB;AAAA,QACrB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,2BAA2B;AAAA,QACzB,OAAO;AAAA,MACT;AAAA,MACA,sBAAsB;AAAA,MACtB,uBAAuB;AAAA,MACvB,YAAY;AAAA,QACV,aAAa;AAAA,UACX,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,sBACE;AAAA,UACF,mBAAmB;AAAA,QACrB;AAAA,QACA,WAAW;AAAA,UACT,yBAAyB;AAAA,UACzB,wBAAwB;AAAA,QAC1B;AAAA,QACA,eAAe;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,UACJ,wBAAwB;AAAA,UACxB,aAAa;AAAA,QACf;AAAA,MACF;AAAA,MACA,iBAAiB;AAAA,QACf,yBAAyB;AAAA,QACzB,oBAAoB;AAAA,QACpB,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,qBAAqB;AAAA,QACnB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,QACxB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,gBAAgB;AAAA,QACd,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,MACA,iBAAiB;AAAA,QACf,4BAA4B;AAAA,QAC5B,+BAA+B;AAAA,QAC/B,OAAO;AAAA,MACT;AAAA,MACA,oBAAoB;AAAA,QAClB,mBAAmB;AAAA,QACnB,2BAA2B;AAAA,QAC3B,eAAe;AAAA,QACf,OAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,cAAc;AAAA,MACZ,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,eAAe;AAAA,IACjB;AAAA,IACA,gBAAgB;AAAA,MACd,gBAAgB;AAAA,QACd,cAAc;AAAA,QACd,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB,OAAO;AAAA,MACT;AAAA,MACA,4BAA4B;AAAA,MAC5B,8BAA8B;AAAA,MAC9B,gBAAgB;AAAA,MAChB,OAAO;AAAA,MACP,8BAA8B;AAAA,IAChC;AAAA,EACF;AAAA,EACA,UAAU;AAAA,IACR,OAAO;AAAA,MACL,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,SAAS;AAAA,MACP,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,IACT;AAAA,EACF;AACF;", "names": []}