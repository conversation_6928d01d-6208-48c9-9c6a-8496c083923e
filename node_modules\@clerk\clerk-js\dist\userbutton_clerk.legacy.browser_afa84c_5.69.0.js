"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["270"],{49327:function(e,t,o){o.d(t,{fB:()=>p,gW:()=>m,uj:()=>B}),o(28419);var n=o(79109),r=o(42305),i=o(48774),s=o(97295),c=o(24676),l=o(31673),u=o(11576),d=o(39541),a=o(96519);let p=e=>{let{navigate:t}=(0,c.useRouter)(),{handleManageAccountClicked:o,handleSignOutSessionClicked:i,handleUserProfileActionClicked:s,session:a}=e,{menutItems:p}=(0,u.useUserButtonContext)(),B=e=>({borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100,padding:"".concat(e.space.$4," ").concat(e.space.$5)}),m=async n=>{var r;return(null==n?void 0:n.path)?(await t(n.path),null==e?void 0:e.completedCallback()):n.id===l.Zb.MANAGE_ACCOUNT?await o():(null==n?void 0:n.open)?s(n.open):(null===(r=n.onClick)||void 0===r||r.call(n),null==e?void 0:e.completedCallback())};return(0,n.tZ)(r.eX,{role:"menu",elementDescriptor:d.descriptors.userButtonPopoverActions,elementId:d.descriptors.userButtonPopoverActions.setId("singleSession"),sx:e=>({borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100}),children:null==p?void 0:p.map(e=>{let t;return t=Object.values(l.Zb).includes(e.id)?{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId(e.id)}:{elementDescriptor:d.descriptors.userButtonPopoverCustomItemButton,elementId:d.descriptors.userButtonPopoverCustomItemButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverCustomItemButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverCustomItemButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionItemButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionItemButtonIcon.setId(e.id)},(0,n.tZ)(r.aU,{...t,icon:e.icon,label:e.name,onClick:e.id===l.Zb.SIGN_OUT?i(a):()=>m(e),sx:B,iconSx:e=>({width:e.sizes.$4,height:e.sizes.$4})},e.id)})})},B=e=>{let{navigate:t}=(0,c.useRouter)(),{handleManageAccountClicked:o,handleSignOutSessionClicked:p,handleSessionClicked:B,handleAddAccountClicked:m,handleUserProfileActionClicked:v,session:I,otherSessions:A}=e,{menutItems:h}=(0,u.useUserButtonContext)(),C=async n=>{var r;return(null==n?void 0:n.path)?(await t(n.path),null==e?void 0:e.completedCallback()):n.id===l.Zb.MANAGE_ACCOUNT?await o():(null==n?void 0:n.open)?v(n.open):(null===(r=n.onClick)||void 0===r||r.call(n),null==e?void 0:e.completedCallback())},P=h.every(e=>Object.values(l.Zb).includes(e.id));return(0,n.BX)(n.HY,{children:[P?(0,n.tZ)(r.r5,{role:"menu",elementDescriptor:d.descriptors.userButtonPopoverActions,elementId:d.descriptors.userButtonPopoverActions.setId("multiSession"),children:(0,n.BX)(d.Flex,{justify:"between",sx:e=>({marginLeft:e.space.$12,padding:"0 ".concat(e.space.$5," ").concat(e.space.$4),gap:e.space.$2}),children:[(0,n.tZ)(r.U8,{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId("manageAccount"),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId("manageAccount"),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId("manageAccount"),icon:a.tc,label:(0,d.localizationKeys)("userButton.action__manageAccount"),onClick:o}),(0,n.tZ)(r.U8,{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId("signOut"),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId("signOut"),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId("signOut"),icon:a.lv,label:(0,d.localizationKeys)("userButton.action__signOut"),onClick:p(I)})]})}):(0,n.tZ)(r.r5,{role:"menu",elementDescriptor:d.descriptors.userButtonPopoverActions,elementId:d.descriptors.userButtonPopoverActions.setId("multiSession"),sx:e=>({gap:e.space.$1,paddingBottom:e.space.$2}),children:null==h?void 0:h.map(e=>{let t;return t=Object.values(l.Zb).includes(e.id)?{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId(e.id)}:{elementDescriptor:d.descriptors.userButtonPopoverCustomItemButton,elementId:d.descriptors.userButtonPopoverCustomItemButton.setId(e.id),iconBoxElementDescriptor:d.descriptors.userButtonPopoverCustomItemButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverCustomItemButtonIconBox.setId(e.id),iconElementDescriptor:d.descriptors.userButtonPopoverActionItemButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionItemButtonIcon.setId(e.id)},(0,n.tZ)(r.aU,{...t,icon:e.icon,label:e.name,onClick:e.id===l.Zb.SIGN_OUT?p(I):()=>C(e),sx:e=>({border:0,padding:"".concat(e.space.$2," ").concat(e.space.$5),gap:e.space.$3x5}),iconSx:e=>({width:e.sizes.$4,height:e.sizes.$4}),iconBoxSx:e=>({minHeight:e.sizes.$4,minWidth:e.sizes.$4,alignItems:"center"})},e.id)})}),(0,n.BX)(r.eX,{role:"menu",sx:e=>({borderTopStyle:e.borderStyles.$solid,borderTopWidth:e.borderWidths.$normal,borderTopColor:e.colors.$neutralAlpha100}),children:[A.map(e=>(0,n.tZ)(i.K,{icon:a.nR,onClick:B(e),role:"menuitem",children:(0,n.tZ)(s.E,{user:e.user})},e.id)),(0,n.tZ)(r.aU,{elementDescriptor:d.descriptors.userButtonPopoverActionButton,elementId:d.descriptors.userButtonPopoverActionButton.setId("addAccount"),iconBoxElementDescriptor:d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:d.descriptors.userButtonPopoverActionButtonIconBox.setId("addAccount"),iconElementDescriptor:d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:d.descriptors.userButtonPopoverActionButtonIcon.setId("addAccount"),icon:a.mm,label:(0,d.localizationKeys)("userButton.action__addAccount"),onClick:m,iconSx:e=>({width:e.sizes.$9,height:e.sizes.$6}),iconBoxSx:e=>({minHeight:e.sizes.$9,minWidth:e.sizes.$6,alignItems:"center"}),spinnerSize:"md"})]})]})},m=e=>{let{handleSignOutAllClicked:t,elementDescriptor:o,elementId:i,iconBoxElementDescriptor:s,iconBoxElementId:c,iconElementDescriptor:l,iconElementId:u,label:p,sx:B,actionSx:m}=e;return(0,n.tZ)(r.eX,{role:"menu",sx:[e=>({padding:e.space.$2}),B],children:(0,n.tZ)(r.aU,{elementDescriptor:o||d.descriptors.userButtonPopoverActionButton,elementId:i||d.descriptors.userButtonPopoverActionButton.setId("signOutAll"),iconBoxElementDescriptor:s||d.descriptors.userButtonPopoverActionButtonIconBox,iconBoxElementId:c||d.descriptors.userButtonPopoverActionButtonIconBox.setId("signOutAll"),iconElementDescriptor:l||d.descriptors.userButtonPopoverActionButtonIcon,iconElementId:u||d.descriptors.userButtonPopoverActionButtonIcon.setId("signOutAll"),icon:a.lv,label:p||(0,d.localizationKeys)("userButton.action__signOutAll"),onClick:t,variant:"ghost",colorScheme:"neutral",sx:[e=>({backgroundColor:e.colors.$transparent,padding:"".concat(e.space.$2," ").concat(e.space.$3),borderBottomWidth:0,borderRadius:e.radii.$lg}),m],spinnerSize:"md"})})}},37322:function(e,t,o){o.r(t),o.d(t,{UserButton:()=>b});var n=o(79109),r=o(69144),i=o(2672),s=o(84045),c=o(11576),l=o(39541),u=o(12464),d=o(83799),a=o(1151),p=o(53929),B=o(97295),m=o(49327),v=o(35241);let I=r.forwardRef((e,t)=>{let{close:o,...r}=e,i=()=>null==o?void 0:o(!1),{session:s}=(0,d.kP)(),u=(0,c.useUserButtonContext)(),{__experimental_asStandalone:I}=u,{authConfig:A}=(0,c.useEnvironment)(),{user:h}=(0,d.aF)(),{handleAddAccountClicked:C,handleManageAccountClicked:P,handleSessionClicked:g,handleSignOutAllClicked:x,handleSignOutSessionClicked:b,handleUserProfileActionClicked:k,otherSessions:f}=(0,v.Z)({...u,actionCompleteCallback:i,user:h});return(0,n.tZ)(p.r,{elementDescriptor:l.descriptors.userButtonPopoverRootBox,children:(0,n.BX)(a.f.Root,{elementDescriptor:l.descriptors.userButtonPopoverCard,ref:t,role:"dialog","aria-label":"User button popover",shouldEntryAnimate:!I,...r,children:[(0,n.BX)(a.f.Content,{elementDescriptor:l.descriptors.userButtonPopoverMain,children:[(0,n.tZ)(B.E,{elementId:"userButton",user:h,sx:e=>({width:"100%",padding:"".concat(e.space.$4," ").concat(e.space.$5)})}),A.singleSessionMode?(0,n.tZ)(m.fB,{handleManageAccountClicked:P,handleSignOutSessionClicked:b,handleUserProfileActionClicked:k,session:s,completedCallback:i}):(0,n.tZ)(m.uj,{session:s,otherSessions:f,handleManageAccountClicked:P,handleSignOutSessionClicked:b,handleSessionClicked:g,handleAddAccountClicked:C,handleUserProfileActionClicked:k,completedCallback:i})]}),(0,n.tZ)(a.f.Footer,{elementDescriptor:l.descriptors.userButtonPopoverFooter,children:!A.singleSessionMode&&f.length>0&&(0,n.tZ)(m.gW,{handleSignOutAllClicked:x})})]})})});var A=o(5472),h=o(77711),C=o(95878);let P=e=>{let{showName:t}=e,{user:o}=(0,d.aF)();return o&&t?(0,n.tZ)(l.Text,{variant:"subtitle",as:"span",elementDescriptor:l.descriptors.userButtonOuterIdentifier,sx:[e=>({paddingLeft:e.space.$2})],children:(0,C.Pp)(o)||(0,C.xC)(o)}):null},g=(0,h.C)((0,r.forwardRef)((e,t)=>{let{sx:o,...r}=e,{user:i}=(0,d.aF)(),{showName:s}=(0,c.useUserButtonContext)();return(0,n.tZ)(l.Button,{elementDescriptor:l.descriptors.userButtonTrigger,variant:"roundWrapper",sx:[e=>({borderRadius:s?e.radii.$md:e.radii.$circle,color:e.colors.$colorText}),o],ref:t,"aria-label":"".concat(e.isOpen?"Close":"Open"," user button"),"aria-expanded":e.isOpen,"aria-haspopup":"dialog",...r,children:(0,n.BX)(l.Flex,{elementDescriptor:l.descriptors.userButtonBox,isOpen:e.isOpen,align:"center",as:"span",gap:2,children:[(0,n.tZ)(P,{showName:s}),(0,n.tZ)(A.Y,{boxElementDescriptor:l.descriptors.userButtonAvatarBox,imageElementDescriptor:l.descriptors.userButtonAvatarImage,...i,size:e=>e.sizes.$7})]})})})),x=(0,i.withFloatingTree)(e=>{let{children:t}=e,{defaultOpen:o}=(0,c.useUserButtonContext)(),{floating:i,reference:l,styles:d,toggle:a,isOpen:p,nodeId:B,context:m}=(0,u.Sv)({defaultOpen:o,placement:"bottom-end",offset:8}),v=(0,r.useId)();return(0,n.BX)(n.HY,{children:[(0,n.tZ)(g,{ref:l,onClick:a,isOpen:p,"aria-controls":p?v:void 0,"aria-expanded":p}),(0,n.tZ)(s.J,{nodeId:B,context:m,isOpen:p,children:(0,r.cloneElement)(t,{id:v,close:a,ref:i,style:d})})]})}),b=(0,c.withCoreUserGuard)((0,i.withCardStateProvider)(()=>{let{__experimental_asStandalone:e}=(0,c.useUserButtonContext)();return(0,n.tZ)(l.Flow.Root,{flow:"userButton",sx:{display:"inline-flex"},children:e?(0,n.tZ)(I,{close:"function"==typeof e?e:void 0}):(0,n.tZ)(x,{children:(0,n.tZ)(I,{})})})}))},35241:function(e,t,o){o.d(t,{Z:()=>u}),o(38062),o(50725);var n=o(83799),r=o(2672),i=o(55809),s=o(54264),c=o(24676),l=o(77623);let u=e=>{let{setActive:t,signOut:o,openUserProfile:u}=(0,n.cL)(),d=(0,r.useCardState)(),{signedInSessions:a,otherSessions:p}=(0,s.j)({user:e.user}),{navigate:B}=(0,c.useRouter)();return{handleSignOutSessionClicked:t=>()=>0===p.length?o(e.navigateAfterSignOut):o(e.navigateAfterMultiSessionSingleSignOut,{sessionId:t.id}).finally(()=>d.setIdle()),handleManageAccountClicked:()=>{var t;return"navigation"===e.userProfileMode?B(e.userProfileUrl||"").finally(()=>{(async()=>{var t;await (0,l._v)(300),null===(t=e.actionCompleteCallback)||void 0===t||t.call(e)})()}):(u(e.userProfileProps),null===(t=e.actionCompleteCallback)||void 0===t?void 0:t.call(e))},handleUserProfileActionClicked:t=>{var o;return"navigation"===e.userProfileMode?B(e.userProfileUrl||"").finally(()=>{(async()=>{var t;await (0,l._v)(300),null===(t=e.actionCompleteCallback)||void 0===t||t.call(e)})()}):(u({...e.userProfileProps,...t&&{__experimental_startPath:t}}),null===(o=e.actionCompleteCallback)||void 0===o?void 0:o.call(e))},handleSignOutAllClicked:()=>o(e.navigateAfterSignOut),handleSessionClicked:o=>async()=>(d.setLoading(),t({session:o,redirectUrl:e.afterSwitchSessionUrl}).finally(()=>{var t;d.setIdle(),null===(t=e.actionCompleteCallback)||void 0===t||t.call(e)})),handleAddAccountClicked:()=>((0,i.T7)(e.signInUrl||window.location.href),(0,l._v)(2e3)),otherSessions:p,signedInSessions:a}}}}]);