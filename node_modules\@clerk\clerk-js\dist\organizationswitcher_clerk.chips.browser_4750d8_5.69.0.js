"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["780"],{6061:function(e,t,i){i.r(t),i.d(t,{OrganizationSwitcher:()=>T});var o=i(9109),n=i(9144),r=i(2672),a=i(4045),c=i(1576),s=i(9541),l=i(2464),d=i(3799),g=i(2305),p=i(5973),h=i(2915),u=i(1151),z=i(6917),m=i(8969),S=i(3929),w=i(4174),x=i(4676),v=i(8774),$=i(1201),f=i(7623),Z=i(7484);let B=()=>{let{userInvitations:e,userSuggestions:t}=(0,d.eW)(Z.AO),{ref:i}=(0,l.YD)({threshold:0,onChange:i=>{i&&(e.hasNextPage?e.fetchNext?.():t.fetchNext?.())}});return{userInvitations:e,userSuggestions:t,ref:i}},b=e=>{let t=(0,r.useCardState)(),{userSuggestions:i}=(0,d.eW)({userSuggestions:Z.AO.userSuggestions});return"accepted"===e.status?(0,o.tZ)(s.Text,{colorScheme:"secondary",localizationKey:(0,s.localizationKeys)("organizationSwitcher.suggestionsAcceptedLabel")}):(0,o.tZ)(s.Button,{elementDescriptor:s.descriptors.organizationSwitcherInvitationAcceptButton,textVariant:"buttonSmall",variant:"outline",colorScheme:"neutral",size:"sm",isLoading:t.isLoading,onClick:()=>t.runAsync(e.accept).then(e=>i?.setData?.(t=>Z.Sz(e,t))).catch(e=>(0,f.S3)(e,[],t.setError)),localizationKey:(0,s.localizationKeys)("organizationSwitcher.action__suggestionsAccept")})},I=e=>{let t=(0,r.useCardState)();return(0,o.tZ)(s.Button,{elementDescriptor:s.descriptors.organizationSwitcherInvitationAcceptButton,textVariant:"buttonSmall",variant:"outline",colorScheme:"neutral",size:"xs",isLoading:t.isLoading,onClick:e.onAccept,localizationKey:(0,s.localizationKeys)("organizationSwitcher.action__invitationAccept")})},A=e=>{let{children:t,publicOrganizationData:i}=e;return(0,o.BX)(s.Flex,{align:"center",gap:2,sx:e=>({justifyContent:"space-between",padding:`${e.space.$4} ${e.space.$5}`}),elementDescriptor:s.descriptors.organizationSwitcherPopoverInvitationActionsBox,children:[(0,o.tZ)(p.Z,{elementId:"organizationSwitcherListedOrganization",organization:i,sx:e=>({color:e.colors.$colorTextSecondary,":hover":{color:e.colors.$colorTextSecondary}})}),t]})},C=(0,r.withCardStateProvider)(e=>{let{invitation:t,onOrganizationClick:i}=e,{accept:n,publicOrganizationData:a,status:l}=t,g=(0,r.useCardState)(),{getOrganization:h}=(0,d.cL)(),{organization:u}=(0,d.o8)(),{userInvitations:z}=(0,d.eW)({userInvitations:Z.AO.userInvitations,userMemberships:Z.AO.userMemberships}),{acceptedInvitations:m,setAcceptedInvitations:S}=(0,c.useAcceptedInvitations)(),x=m.find(e=>e.invitation.id===t.id)?.organization;return"accepted"===l?x?.id&&u?.id===x.id?null:(0,o.tZ)(v.K,{elementDescriptor:s.descriptors.organizationSwitcherPreviewButton,icon:w.nR,onClick:x?()=>i(x):void 0,role:"menuitem",children:(0,o.tZ)(p.Z,{elementId:"organizationSwitcherListedOrganization",organization:a,sx:e=>({color:e.colors.$colorTextSecondary,":hover":{color:e.colors.$colorTextSecondary}})})}):(0,o.tZ)(A,{publicOrganizationData:a,children:(0,o.tZ)(I,{onAccept:()=>g.runAsync(async()=>[await n(),await h(a.id)]).then(([e,t])=>{z?.setData?.(t=>Z.Sz(e,t,"negative")),S(i=>[...i,{organization:t,invitation:e}])}).catch(e=>(0,f.S3)(e,[],g.setError))})})}),O=e=>{let{showBorder:t,...i}=e;return(0,o.tZ)(g.eX,{role:"menu",...i})},y=(0,r.withCardStateProvider)(e=>(0,o.tZ)(A,{publicOrganizationData:e.publicOrganizationData,children:(0,o.tZ)(b,{...e})})),P=e=>{let{onOrganizationClick:t}=e,{ref:i,userSuggestions:n,userInvitations:r}=B(),a=r.isLoading||n.isLoading,c=r.hasNextPage||n.hasNextPage,l=r.data?.filter(e=>!!e)||[],d=n.data?.filter(e=>!!e)||[],g=l.length>0||d.length>0;return g||a?(0,o.tZ)(O,{showBorder:g||a,elementDescriptor:s.descriptors.organizationSwitcherPopoverInvitationActions,children:(0,o.BX)(s.Box,{sx:e=>({maxHeight:`calc(4 * ${e.sizes.$17} + 4px)`,overflowY:"auto",...$.common.unstyledScrollbar(e)}),children:[l?.map(e=>o.tZ(C,{invitation:e,onOrganizationClick:t},e.id)),!r.hasNextPage&&d?.map(e=>o.tZ(y,{...e},e.id)),(c||a)&&(0,o.tZ)(m.q7,{ref:i})]})}):null},D=()=>{let{userMemberships:e}=(0,d.eW)({userMemberships:Z.AO.userMemberships}),{ref:t}=(0,l.YD)({threshold:0,onChange:t=>{t&&e.hasNextPage&&e.fetchNext?.()}});return{userMemberships:e,ref:t}},k=e=>{let{onPersonalWorkspaceClick:t,onOrganizationClick:i}=e,{hidePersonal:n}=(0,c.useOrganizationSwitcherContext)(),{organization:r}=(0,d.o8)(),{ref:a,userMemberships:l}=D(),{user:g}=(0,d.aF)(),u=((l.count||0)>0&&l.data||[]).map(e=>e.organization).filter(e=>e.id!==r?.id);if(!g)return null;let{username:z,primaryEmailAddress:S,primaryPhoneNumber:x,...f}=g,{isLoading:Z,hasNextPage:B}=l;return(0,o.BX)(s.Box,{sx:e=>({maxHeight:`calc((4 * ${e.sizes.$17}) + 4px)`,overflowY:"auto","> button,div":{border:`0 solid ${e.colors.$neutralAlpha100}`},">:not([hidden])~:not([hidden])":{borderTopWidth:"1px",borderBottomWidth:"0"},...$.common.unstyledScrollbar(e)}),role:"group","aria-label":n?"List of all organization memberships":"List of all accounts",children:[r&&!n&&(0,o.tZ)(v.K,{elementDescriptor:s.descriptors.organizationSwitcherPreviewButton,elementId:s.descriptors.organizationSwitcherPreviewButton.setId("personal"),icon:w.nR,onClick:t,role:"menuitem",children:(0,o.tZ)(h.g,{user:f,mainIdentifierVariant:"buttonLarge",title:(0,s.localizationKeys)("organizationSwitcher.personalWorkspace")})}),u.map(e=>(0,o.tZ)(v.K,{elementDescriptor:s.descriptors.organizationSwitcherPreviewButton,elementId:s.descriptors.organizationSwitcherPreviewButton.setId("organization"),icon:w.nR,onClick:()=>i(e),role:"menuitem",sx:e=>({border:`0 solid ${e.colors.$neutralAlpha100}`}),children:(0,o.tZ)(p.Z,{elementId:"organizationSwitcherListedOrganization",organization:e})},e.id)),(B||Z)&&(0,o.tZ)(m.q7,{ref:a})]})},_=({onCreateOrganizationClick:e})=>{let{user:t}=(0,d.aF)();return t?.createOrganizationEnabled?(0,o.tZ)(g.aU,{elementDescriptor:s.descriptors.organizationSwitcherPopoverActionButton,elementId:s.descriptors.organizationSwitcherPopoverActionButton.setId("createOrganization"),iconBoxElementDescriptor:s.descriptors.organizationSwitcherPopoverActionButtonIconBox,iconBoxElementId:s.descriptors.organizationSwitcherPopoverActionButtonIconBox.setId("createOrganization"),iconElementDescriptor:s.descriptors.organizationSwitcherPopoverActionButtonIcon,iconElementId:s.descriptors.organizationSwitcherPopoverActionButtonIcon.setId("createOrganization"),icon:w.mm,label:(0,s.localizationKeys)("organizationSwitcher.action__createOrganization"),onClick:e,sx:e=>({padding:`${e.space.$5} ${e.space.$5}`}),iconSx:e=>({width:e.sizes.$9,height:e.sizes.$6}),iconBoxSx:e=>({width:e.sizes.$9,height:e.sizes.$6}),spinnerSize:"sm"}):null},L=e=>{let{onCreateOrganizationClick:t,onPersonalWorkspaceClick:i,onOrganizationClick:n}=e;return(0,o.BX)(o.HY,{children:[(0,o.tZ)(P,{onOrganizationClick:n}),(0,o.tZ)(k,{onPersonalWorkspaceClick:i,onOrganizationClick:n}),(0,o.tZ)(_,{onCreateOrganizationClick:t})]})},R=n.forwardRef((e,t)=>{let{close:i,...n}=e,a=()=>i?.(!1),l=(0,r.useCardState)(),{__experimental_asStandalone:m}=(0,c.useOrganizationSwitcherContext)(),{openOrganizationProfile:v,openCreateOrganization:$}=(0,d.cL)(),{organization:f}=(0,d.o8)(),{isLoaded:Z,setActive:B}=(0,d.eW)(),b=(0,x.useRouter)(),{hidePersonal:I,__unstable_manageBillingUrl:A,__unstable_manageBillingLabel:C,__unstable_manageBillingMembersLimit:O,createOrganizationMode:y,organizationProfileMode:P,afterLeaveOrganizationUrl:D,afterCreateOrganizationUrl:k,navigateCreateOrganization:_,navigateOrganizationProfile:R,afterSelectOrganizationUrl:K,afterSelectPersonalUrl:W,organizationProfileProps:X,skipInvitationScreen:T,hideSlug:F}=(0,c.useOrganizationSwitcherContext)(),{user:N}=(0,d.aF)();if(!N)return null;let{username:M,primaryEmailAddress:U,primaryPhoneNumber:Y,...j}=N;if(!Z)return null;let H=()=>(a(),"navigation"===P)?R():v({...X,afterLeaveOrganizationUrl:D,__unstable_manageBillingUrl:A,__unstable_manageBillingLabel:C,__unstable_manageBillingMembersLimit:O}),V=(0,o.tZ)(g.U8,{elementDescriptor:s.descriptors.organizationSwitcherPopoverActionButton,elementId:s.descriptors.organizationSwitcherPopoverActionButton.setId("manageOrganization"),iconBoxElementDescriptor:s.descriptors.organizationSwitcherPopoverActionButtonIconBox,iconBoxElementId:s.descriptors.organizationSwitcherPopoverActionButtonIconBox.setId("manageOrganization"),iconElementDescriptor:s.descriptors.organizationSwitcherPopoverActionButtonIcon,iconElementId:s.descriptors.organizationSwitcherPopoverActionButtonIcon.setId("manageOrganization"),icon:w.tc,label:(0,s.localizationKeys)("organizationSwitcher.action__manageOrganization"),onClick:()=>H(),trailing:(0,o.tZ)(E,{})}),q=(0,o.tZ)(g.U8,{icon:w.Nj,label:(0,z.OR)(C)||"Upgrade",onClick:()=>b.navigate((0,z.OR)(A))});return(0,o.tZ)(S.r,{elementDescriptor:s.descriptors.organizationSwitcherPopoverRootBox,children:(0,o.BX)(u.f.Root,{elementDescriptor:s.descriptors.organizationSwitcherPopoverCard,ref:t,role:"dialog","aria-label":`${f?.name} is active`,shouldEntryAnimate:!m,...n,children:[(0,o.tZ)(u.f.Content,{elementDescriptor:s.descriptors.organizationSwitcherPopoverMain,children:(0,o.BX)(g.eX,{elementDescriptor:s.descriptors.organizationSwitcherPopoverActions,role:"menu",children:[f?A?(0,o.BX)(o.HY,{children:[(0,o.tZ)(p.Z,{elementId:"organizationSwitcherActiveOrganization",organization:f,user:N,fetchRoles:!0,mainIdentifierVariant:"buttonLarge",sx:e=>({padding:`${e.space.$4} ${e.space.$5}`})}),(0,o.tZ)(g.eX,{role:"menu",sx:e=>({borderBottomWidth:e.borderWidths.$normal,borderBottomStyle:e.borderStyles.$solid,borderBottomColor:e.colors.$neutralAlpha100}),children:(0,o.BX)(s.Flex,{justify:"between",sx:e=>({marginLeft:e.space.$12,padding:`0 ${e.space.$5} ${e.space.$4}`,gap:e.space.$2}),children:[V,q]})})]}):(0,o.BX)(s.Flex,{justify:"between",align:"center",sx:e=>({width:"100%",paddingRight:e.space.$5}),children:[(0,o.tZ)(p.Z,{elementId:"organizationSwitcherActiveOrganization",organization:f,user:N,fetchRoles:!0,mainIdentifierVariant:"buttonLarge",sx:e=>({padding:`${e.space.$4} ${e.space.$5}`})}),(0,o.tZ)(g.eX,{role:"menu",children:V})]}):!I&&(0,o.tZ)(h.g,{user:j,sx:e=>({padding:`${e.space.$4} ${e.space.$5}`,width:"100%"}),title:(0,s.localizationKeys)("organizationSwitcher.personalWorkspace")}),(0,o.tZ)(L,{onCreateOrganizationClick:()=>(a(),"navigation"===y)?_():$({afterCreateOrganizationUrl:k,skipInvitationScreen:T,hideSlug:F}),onPersonalWorkspaceClick:()=>l.runAsync(()=>B({organization:null,redirectUrl:W(N)})).then(a),onOrganizationClick:e=>l.runAsync(()=>B({organization:e,redirectUrl:K(e)})).then(a)})]})}),(0,o.tZ)(u.f.Footer,{elementDescriptor:s.descriptors.organizationSwitcherPopoverFooter})]})})}),E=(0,m.Ci)(({sx:e})=>{let{organizationSettings:t}=(0,c.useEnvironment)(),i=t?.domains?.enabled,{membershipRequests:n}=(0,d.o8)({membershipRequests:i||void 0});return n?.count?(0,o.tZ)(m.dN,{notificationCount:n.count,containerSx:e}):null},{permission:"org:sys_memberships:manage"}),K=(0,i(7711).C)((0,n.forwardRef)((e,t)=>{let{sx:i,...n}=e,{user:r}=(0,d.aF)(),{organization:a}=(0,d.o8)(),{hidePersonal:l}=(0,c.useOrganizationSwitcherContext)();if(!r)return null;let{username:g,primaryEmailAddress:u,primaryPhoneNumber:z,...m}=r;return(0,o.BX)(s.Button,{elementDescriptor:s.descriptors.organizationSwitcherTrigger,elementId:s.descriptors.organizationSwitcherTrigger.setId(a?"organization":"personal"),variant:"ghost",colorScheme:"neutral",hoverAsFocus:!0,focusRing:!1,sx:[e=>({padding:`${e.space.$1} ${e.space.$2}`,position:"relative"}),i],ref:t,"aria-label":`${e.isOpen?"Close":"Open"} organization switcher`,"aria-expanded":e.isOpen,"aria-haspopup":"dialog",...n,children:[a&&(0,o.tZ)(p.Z,{elementId:"organizationSwitcherTrigger",gap:3,size:"xs",fetchRoles:!0,organization:a,sx:{maxWidth:"30ch"}}),!a&&(0,o.tZ)(h.g,{size:"xs",gap:3,user:m,showAvatar:!l,sx:e=>({color:e.colors.$colorTextSecondary}),title:l?(0,s.localizationKeys)("organizationSwitcher.notSelected"):(0,s.localizationKeys)("organizationSwitcher.personalWorkspace")}),(0,o.tZ)(W,{}),(0,o.tZ)(s.Icon,{elementDescriptor:s.descriptors.organizationSwitcherTriggerIcon,icon:w._M,sx:e=>({marginLeft:`${e.space.$2}`})})]})})),W=()=>{let{userInvitations:e,userSuggestions:t}=(0,d.eW)(Z.AO),{organizationSettings:i}=(0,c.useEnvironment)(),n=(0,m.N2)({permission:"org:sys_memberships:manage"}),r=i?.domains?.enabled,{membershipRequests:a}=(0,d.o8)({membershipRequests:r&&n||void 0}),s=(e.count||0)+(t.count||0)+(a?.count||0);return s?(0,o.tZ)(m.dN,{containerSx:e=>({position:"absolute",top:`-${e.space.$2}`,right:`-${e.space.$2}`}),notificationCount:s}):null},X=(0,r.withFloatingTree)(({children:e})=>{let{defaultOpen:t}=(0,c.useOrganizationSwitcherContext)(),{floating:i,reference:r,styles:s,toggle:d,isOpen:g,nodeId:p,context:h}=(0,l.Sv)({defaultOpen:t,placement:"bottom-start",offset:8}),u=(0,n.useId)();return(0,o.BX)(o.HY,{children:[(0,o.tZ)(K,{ref:r,onClick:d,isOpen:g,"aria-controls":g?u:void 0,"aria-expanded":g}),(0,o.tZ)(a.J,{nodeId:p,context:h,isOpen:g,children:(0,n.cloneElement)(e,{id:u,close:d,ref:i,style:s})})]})}),T=(0,c.withCoreUserGuard)((0,r.withCardStateProvider)(()=>{let{__experimental_asStandalone:e}=(0,c.useOrganizationSwitcherContext)();return(0,o.tZ)(s.Flow.Root,{flow:"organizationSwitcher",sx:{display:"inline-flex"},children:(0,o.tZ)(c.AcceptedInvitationsProvider,{children:e?(0,o.tZ)(R,{close:"function"==typeof e?e:void 0}):(0,o.tZ)(X,{children:(0,o.tZ)(R,{})})})})}))},7484:function(e,t,i){i.d(t,{AO:()=>o,Sz:()=>n});let o={userMemberships:{infinite:!0},userInvitations:{infinite:!0},userSuggestions:{infinite:!0,status:["pending","accepted"]}},n=(e,t,i)=>{if(void 0===t)return[{data:[e],total_count:1}];let o=t?.[t.length-1]?.total_count||1;return t.map(t=>{if(void 0===t)return t;let n=t.data.map(t=>t.id===e.id?{...e}:t);return{...t,data:n,total_count:"negative"===i?o-1:o}})}}}]);