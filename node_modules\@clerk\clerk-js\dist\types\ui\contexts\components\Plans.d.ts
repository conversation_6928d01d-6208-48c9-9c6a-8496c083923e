import type { Appearance, CommercePlanResource, CommerceSubscriptionPlanPeriod, CommerceSubscriptionResource } from '@clerk/types';
import type { LocalizationKey } from '../../localization';
export declare const usePaymentSourcesCacheKey: () => {
    key: string;
    resourceId: string | undefined;
};
export declare const usePaymentSources: () => import("swr").SWRResponse<import("@clerk/types").ClerkPaginatedResponse<import("@clerk/types").CommercePaymentSourceResource> | undefined, any, {
    dedupingInterval: number;
    keepPreviousData: boolean;
}>;
export declare const usePaymentAttemptsCacheKey: () => {
    key: string;
    userId: string | undefined;
    args: {
        orgId: string | undefined;
    };
};
export declare const usePaymentAttempts: () => import("swr").SWRResponse<import("@clerk/types").ClerkPaginatedResponse<import("@clerk/types").CommercePaymentResource> | undefined, any, {
    dedupingInterval: number;
    keepPreviousData: boolean;
}>;
export declare const useStatementsCacheKey: () => {
    key: string;
    userId: string | undefined;
    args: {
        orgId: string | undefined;
    };
};
export declare const useStatements: () => import("swr").SWRResponse<import("@clerk/types").ClerkPaginatedResponse<import("@clerk/types").CommerceStatementResource> | undefined, any, {
    dedupingInterval: number;
    keepPreviousData: boolean;
}>;
export declare const useSubscriptions: () => {
    error: any;
    mutate: import("swr").KeyedMutator<import("@clerk/types").ClerkPaginatedResponse<CommerceSubscriptionResource> | undefined>;
    isValidating: boolean;
    isLoading: boolean;
    data: CommerceSubscriptionResource[];
};
export declare const usePlans: () => import("swr").SWRResponse<CommercePlanResource[], any, {
    dedupingInterval: number;
    keepPreviousData: boolean;
}>;
type HandleSelectPlanProps = {
    plan: CommercePlanResource;
    planPeriod: CommerceSubscriptionPlanPeriod;
    onSubscriptionChange?: () => void;
    mode?: 'modal' | 'mounted';
    event?: React.MouseEvent<HTMLElement>;
    appearance?: Appearance;
    newSubscriptionRedirectUrl?: string;
};
export declare const usePlansContext: () => {
    activeOrUpcomingSubscription: (plan: CommercePlanResource) => CommerceSubscriptionResource | undefined;
    activeAndUpcomingSubscriptions: (plan: CommercePlanResource) => CommerceSubscriptionResource[];
    activeOrUpcomingSubscriptionBasedOnPlanPeriod: (plan: CommercePlanResource, planPeriod?: CommerceSubscriptionPlanPeriod) => CommerceSubscriptionResource | undefined;
    isDefaultPlanImplicitlyActiveOrUpcoming: boolean;
    handleSelectPlan: ({ plan, planPeriod, onSubscriptionChange, mode, event, appearance, newSubscriptionRedirectUrl, }: HandleSelectPlanProps) => void;
    buttonPropsForPlan: ({ plan, subscription: sub, isCompact, selectedPlanPeriod, }: {
        plan?: CommercePlanResource;
        subscription?: CommerceSubscriptionResource;
        isCompact?: boolean;
        selectedPlanPeriod?: CommerceSubscriptionPlanPeriod;
    }) => {
        localizationKey: LocalizationKey;
        variant: "bordered" | "solid";
        colorScheme: "secondary" | "primary";
        isDisabled: boolean;
        disabled: boolean;
    };
    canManageSubscription: ({ plan, subscription: sub }: {
        plan?: CommercePlanResource;
        subscription?: CommerceSubscriptionResource;
    }) => boolean;
    captionForSubscription: (subscription: CommerceSubscriptionResource) => LocalizationKey;
    upcomingSubscriptionsExist: boolean;
    defaultFreePlan: CommercePlanResource | undefined;
    revalidateAll: () => void;
};
export {};
