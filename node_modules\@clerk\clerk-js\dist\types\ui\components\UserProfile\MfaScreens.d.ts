import type { VerificationStrategy } from '@clerk/types';
export declare const RemoveMfaTOTPScreen: () => import("@emotion/react/jsx-runtime").JSX.Element;
type RemoveMfaPhoneCodeProps = {
    phoneId: string;
};
export declare const RemoveMfaPhoneCodeScreen: (props: RemoveMfaPhoneCodeProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export declare const MfaBackupCodeCreateScreen: () => import("@emotion/react/jsx-runtime").JSX.Element;
type MfaScreenProps = {
    selectedStrategy: VerificationStrategy;
};
export declare const MfaScreen: (props: MfaScreenProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
