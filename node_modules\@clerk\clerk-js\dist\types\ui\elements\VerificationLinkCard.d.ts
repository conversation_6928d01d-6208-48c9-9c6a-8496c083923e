import React from 'react';
import type { LocalizationKey } from '../customizables';
type VerificationLinkCardProps = {
    safeIdentifier: string | undefined | null;
    cardTitle: LocalizationKey;
    cardSubtitle: LocalizationKey;
    formTitle: LocalizationKey;
    formSubtitle: LocalizationKey;
    resendButton: LocalizationKey;
    profileImageUrl?: string;
    onResendCodeClicked: React.MouseEventHandler;
    onShowAlternativeMethodsClicked?: React.MouseEventHandler;
};
export declare const VerificationLinkCard: (props: VerificationLinkCardProps) => import("@emotion/react/jsx-runtime").JSX.Element;
type VerificationLinkProps = {
    formSubtitle?: LocalizationKey;
    resendButton: LocalizationKey;
    onResendCodeClicked: React.MouseEventHandler;
    children?: React.ReactNode;
};
export declare const VerificationLink: (props: VerificationLinkProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
