"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["82"],{91800:function(e,t,i){i.d(t,{b:()=>d});var a=i(79109),l=i(19655),n=i(8969),o=i(11576),s=i(39541),r=i(96519),c=i(24676);function d(e){let{title:t,arrowButtonText:i,arrowButtonEmptyText:d}=e,{handleSelectPlan:u,captionForSubscription:h,canManageSubscription:p}=(0,o.usePlansContext)(),g=(0,o.useSubscriberTypeLocalizationRoot)(),b=(0,o.useSubscriberTypeContext)(),{data:y}=(0,o.useSubscriptions)(),z=(0,n.N2)(e=>e({permission:"org:sys_billing:manage"})||"user"===b),{navigate:m}=(0,c.useRouter)(),x=(e,t)=>{u({mode:"modal",plan:e.plan,planPeriod:e.planPeriod,event:t})},Z=y.sort((e,t)=>"active"===e.status&&"active"!==t.status?-1:("active"===t.status&&e.status,1));return(0,a.BX)(l.zd.Root,{id:"subscriptionsList",title:t,centered:!1,sx:e=>({borderTop:"none",paddingTop:e.space.$1}),children:[y.length>0&&(0,a.BX)(s.Table,{tableHeadVisuallyHidden:!0,children:[(0,a.tZ)(s.Thead,{children:(0,a.BX)(s.Tr,{children:[(0,a.tZ)(s.Th,{localizationKey:(0,s.localizationKeys)("".concat(g,".billingPage.subscriptionsListSection.tableHeader__plan"))}),(0,a.tZ)(s.Th,{localizationKey:(0,s.localizationKeys)("".concat(g,".billingPage.subscriptionsListSection.tableHeader__startDate"))}),(0,a.tZ)(s.Th,{localizationKey:(0,s.localizationKeys)("".concat(g,".billingPage.subscriptionsListSection.tableHeader__edit"))})]})}),(0,a.tZ)(s.Tbody,{children:Z.map(e=>(0,a.BX)(s.Tr,{children:[(0,a.tZ)(s.Td,{children:(0,a.BX)(s.Col,{gap:1,children:[(0,a.BX)(s.Flex,{align:"center",gap:1,children:[(0,a.tZ)(s.Icon,{icon:r.ou,sx:e=>({width:e.sizes.$4,height:e.sizes.$4,opacity:e.opacity.$inactive})}),(0,a.tZ)(s.Text,{variant:"subtitle",sx:e=>({marginRight:e.sizes.$1}),children:e.plan.name}),Z.length>1||e.canceledAt?(0,a.tZ)(s.Badge,{colorScheme:"active"===e.status?"secondary":"primary",localizationKey:"active"===e.status?(0,s.localizationKeys)("badge__activePlan"):(0,s.localizationKeys)("badge__upcomingPlan")}):null]}),(!e.plan.isDefault||"upcoming"===e.status)&&(0,a.tZ)(s.Text,{variant:"caption",colorScheme:"secondary",localizationKey:h(e)})]})}),(0,a.tZ)(s.Td,{sx:e=>({textAlign:"right"}),children:(0,a.BX)(s.Text,{variant:"subtitle",children:[e.plan.currencySymbol,"annual"===e.planPeriod?e.plan.annualAmountFormatted:e.plan.amountFormatted,(e.plan.amount>0||e.plan.annualAmount>0)&&(0,a.tZ)(s.Span,{sx:e=>({color:e.colors.$colorTextSecondary,textTransform:"lowercase",":before":{content:'"/"',marginInline:e.space.$1}}),localizationKey:"annual"===e.planPeriod?(0,s.localizationKeys)("commerce.year"):(0,s.localizationKeys)("commerce.month")})]})}),(0,a.tZ)(s.Td,{sx:e=>({textAlign:"right"}),children:p({subscription:e})&&e.id&&!e.plan.isDefault&&(0,a.tZ)(s.Button,{"aria-label":"Manage subscription",onClick:t=>x(e,t),variant:"bordered",colorScheme:"secondary",isDisabled:!z,sx:e=>({width:e.sizes.$6,height:e.sizes.$6}),children:(0,a.tZ)(s.Icon,{icon:r.tc,sx:e=>({width:e.sizes.$4,height:e.sizes.$4,opacity:e.opacity.$inactive})})})})]},e.id))})]}),(0,a.tZ)(l.zd.ArrowButton,{id:"subscriptionsList",textLocalizationKey:y.length>0?i:d,sx:[e=>({justifyContent:"start",height:e.sizes.$8})],leftIcon:y.length>0?r.Ic:r.v3,leftIconSx:e=>({width:e.sizes.$4,height:e.sizes.$4}),onClick:()=>void m("plans")})]})}},41470:function(e,t,i){i.r(t),i.d(t,{BillingPage:()=>z});var a=i(79109),l=i(44455),n=i(2672),o=i(92654),s=i(90708),r=i(11576),c=i(39541),d=i(93746),u=i(12264),h=i(86054),p=i(15515),g=i(91800);let b={0:"subscriptions",1:"statements",2:"payments"},y=(0,n.withCardStateProvider)(()=>{let e=(0,n.useCardState)(),{selectedTab:t,handleTabChange:i}=(0,d.x)(b);return(0,a.tZ)(c.Col,{elementDescriptor:c.descriptors.page,sx:e=>({gap:e.space.$8,color:e.colors.$colorText}),children:(0,a.BX)(c.Col,{elementDescriptor:c.descriptors.profilePage,elementId:c.descriptors.profilePage.setId("billing"),gap:4,children:[(0,a.tZ)(o.h.Root,{children:(0,a.tZ)(o.h.Title,{localizationKey:(0,c.localizationKeys)("userProfile.billingPage.title"),textVariant:"h2"})}),(0,a.tZ)(l.Z.Alert,{children:e.error}),(0,a.BX)(s.mQ,{value:t,onChange:i,children:[(0,a.BX)(s.dr,{sx:e=>({gap:e.space.$6}),children:[(0,a.tZ)(s.OK,{localizationKey:(0,c.localizationKeys)("userProfile.billingPage.start.headerTitle__subscriptions")}),(0,a.tZ)(s.OK,{localizationKey:(0,c.localizationKeys)("userProfile.billingPage.start.headerTitle__statements")}),(0,a.tZ)(s.OK,{localizationKey:(0,c.localizationKeys)("userProfile.billingPage.start.headerTitle__payments")})]}),(0,a.BX)(s.nP,{children:[(0,a.BX)(s.x4,{sx:e=>({width:"100%",flexDirection:"column"}),children:[(0,a.tZ)(g.b,{title:(0,c.localizationKeys)("userProfile.billingPage.subscriptionsListSection.title"),arrowButtonText:(0,c.localizationKeys)("userProfile.billingPage.subscriptionsListSection.actionLabel__switchPlan"),arrowButtonEmptyText:(0,c.localizationKeys)("userProfile.billingPage.subscriptionsListSection.actionLabel__newSubscription")}),(0,a.tZ)(h.Hw,{})]}),(0,a.tZ)(s.x4,{sx:{width:"100%"},children:(0,a.tZ)(p.f,{})}),(0,a.tZ)(s.x4,{sx:{width:"100%"},children:(0,a.tZ)(u.B,{})})]})]})]})})}),z=()=>(0,a.tZ)(r.SubscriberTypeContext.Provider,{value:"user",children:(0,a.tZ)(y,{})})}}]);