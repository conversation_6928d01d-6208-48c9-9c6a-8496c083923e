import type { APIKeysSettingsJSON, APIKeysSettingsJSONSnapshot, APIKeysSettingsResource } from '@clerk/types';
import { BaseResource } from './internal';
/**
 * @internal
 */
export declare class APIKeySettings extends BaseResource implements APIKeysSettingsResource {
    enabled: boolean;
    constructor(data?: APIKeysSettingsJSON | APIKeysSettingsJSONSnapshot | null);
    protected fromJSON(data: APIKeysSettingsJSON | APIKeysSettingsJSONSnapshot | null): this;
    __internal_toSnapshot(): APIKeysSettingsJSONSnapshot;
}
