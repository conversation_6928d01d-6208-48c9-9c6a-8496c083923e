import React from 'react';
export declare const ProfileCardRoot: React.ForwardRefExoticComponent<Omit<Omit<Omit<import("../../primitives").FlexProps, "ref"> & React.RefAttributes<HTMLDivElement> & {
    elementDescriptor?: import("../../customizables/elementDescriptors").ElementDescriptor | Array<import("../../customizables/elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("../../customizables/elementDescriptors").ElementId;
    css?: never;
    sx?: import("../../styledSystem").ThemableCssProp;
}, "ref"> & React.RefAttributes<HTMLDivElement>, "ref"> & React.RefAttributes<HTMLDivElement>>;
