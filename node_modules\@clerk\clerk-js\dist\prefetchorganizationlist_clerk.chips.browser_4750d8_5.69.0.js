"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["211"],{7484:function(t,e,n){n.d(e,{AO:()=>i,Sz:()=>r});let i={userMemberships:{infinite:!0},userInvitations:{infinite:!0},userSuggestions:{infinite:!0,status:["pending","accepted"]}},r=(t,e,n)=>{if(void 0===e)return[{data:[t],total_count:1}];let i=e?.[e.length-1]?.total_count||1;return e.map(e=>{if(void 0===e)return e;let r=e.data.map(e=>e.id===t.id?{...t}:e);return{...e,data:r,total_count:"negative"===n?i-1:i}})}},6703:function(t,e,n){n.r(e),n.d(e,{OrganizationSwitcherPrefetch:()=>a});var i=n(3799),r=n(7484);function a(){return(0,i.eW)(r.AO),null}}}]);