import type { NavbarRoute } from '@/ui/elements/Navbar';
import type { ParsedQueryString } from '../../router';
import type { UserProfileCtx } from '../../types';
import type { CustomPageContent } from '../../utils';
type PagesType = {
    routes: NavbarRoute[];
    contents: CustomPageContent[];
    pageToRootNavbarRouteMap: Record<string, NavbarRoute>;
};
export type UserProfileContextType = UserProfileCtx & {
    queryParams: ParsedQueryString;
    authQueryString: string | null;
    pages: PagesType;
};
export declare const UserProfileContext: import("react").Context<UserProfileCtx | null>;
export declare const useUserProfileContext: () => UserProfileContextType;
export {};
