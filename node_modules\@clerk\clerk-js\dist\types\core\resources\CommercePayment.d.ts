import type { CommerceMoney, CommercePaymentChargeType, CommercePaymentJSON, CommercePaymentResource, CommercePaymentStatus } from '@clerk/types';
import { BaseResource, CommercePaymentSource, CommerceSubscription } from './internal';
export declare class CommercePayment extends BaseResource implements CommercePaymentResource {
    id: string;
    amount: CommerceMoney;
    failedAt?: Date;
    paidAt?: Date;
    updatedAt: Date;
    paymentSource: CommercePaymentSource;
    subscription: CommerceSubscription;
    subscriptionItem: CommerceSubscription;
    chargeType: CommercePaymentChargeType;
    status: CommercePaymentStatus;
    constructor(data: CommercePaymentJSON);
    protected fromJSON(data: CommercePaymentJSON | null): this;
}
