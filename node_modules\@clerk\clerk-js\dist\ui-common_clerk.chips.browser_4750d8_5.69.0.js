"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["573"],{4995:function(e,t,r){r.d(t,{O:()=>i});var n=r(9109),o=r(9541),a=r(7623);let i=({mount:e,unmount:t})=>(0,n.tZ)(o.Col,{elementDescriptor:o.descriptors.page,gap:8,children:(0,n.tZ)(o.Col,{elementDescriptor:o.descriptors.profilePage,gap:8,children:(0,n.tZ)(a.qT,{mount:e,unmount:t})})})},2189:function(e,t,r){r.d(t,{P:()=>o});var n=r(3799);let o=({children:e})=>"development"!==(0,n.cL)().instanceType?null:e},5112:function(e,t,r){r.d(t,{$:()=>d,J:()=>c});var n=r(9109),o=r(2672),a=r(1085),i=r(686);let l={verified:{title:(0,a.u1)("signIn.emailLink.verified.title"),subtitle:(0,a.u1)("signIn.emailLink.verified.subtitle")},verified_switch_tab:{title:(0,a.u1)("signIn.emailLink.verified.title"),subtitle:(0,a.u1)("signIn.emailLink.verifiedSwitchTab.subtitle")},loading:{title:(0,a.u1)("signIn.emailLink.loading.title"),subtitle:(0,a.u1)("signIn.emailLink.loading.subtitle")},failed:{title:(0,a.u1)("signIn.emailLink.failed.title"),subtitle:(0,a.u1)("signIn.emailLink.failed.subtitle")},expired:{title:(0,a.u1)("signIn.emailLink.expired.title"),subtitle:(0,a.u1)("signIn.emailLink.expired.subtitle")},client_mismatch:{title:(0,a.u1)("signIn.emailLink.clientMismatch.title"),subtitle:(0,a.u1)("signIn.emailLink.clientMismatch.subtitle")}},s={...l,verified:{...l.verified,title:(0,a.u1)("signUp.emailLink.verified.title")},verified_switch_tab:{...l.verified_switch_tab,title:(0,a.u1)("signUp.emailLink.verified.title")},loading:{...l.loading,title:(0,a.u1)("signUp.emailLink.loading.title")},client_mismatch:{...l.client_mismatch,subtitle:(0,a.u1)("signUp.emailLink.clientMismatch.subtitle")}},c=(0,o.withCardStateProvider)(e=>(0,n.tZ)(i.$,{...e,texts:l})),d=(0,o.withCardStateProvider)(e=>(0,n.tZ)(i.$,{...e,texts:s}))},6273:function(e,t,r){r.d(t,{E:()=>p});var n=r(9109);r(9144);var o=r(9541),a=r(4455),i=r(2672),l=r(2654),s=r(4174),c=r(1201);let d={verified:s.qy,verified_switch_tab:s.oX,expired:s.SV,failed:s.SV,client_mismatch:s.SV},u=(e,t)=>({verified:e.colors.$success500,verified_switch_tab:e.colors.$primary500,expired:e.colors.$warning500,failed:e.colors.$danger500,client_mismatch:e.colors.$warning500})[t],p=e=>{let t=(0,i.useCardState)();return(0,n.tZ)(o.Flow.Part,{part:"emailLinkStatus",children:(0,n.BX)(a.Z.Root,{children:[(0,n.BX)(a.Z.Content,{children:[(0,n.BX)(l.h.Root,{children:[(0,n.tZ)(l.h.Title,{localizationKey:e.title}),(0,n.tZ)(l.h.Subtitle,{localizationKey:e.subtitle})]}),(0,n.tZ)(a.Z.Alert,{children:t.error}),(0,n.tZ)(o.Col,{elementDescriptor:o.descriptors.main,children:(0,n.tZ)(m,{status:e.status})})]}),(0,n.tZ)(a.Z.Footer,{})]})})},m=e=>(0,n.tZ)(o.Flex,{elementDescriptor:o.descriptors.verificationLinkStatusBox,center:!0,direction:"col",gap:8,children:"loading"===e.status?(0,n.tZ)(o.Spinner,{size:"xl",colorScheme:"primary",sx:e=>({margin:`${e.space.$12} 0`}),elementDescriptor:o.descriptors.spinner}):(0,n.BX)(n.HY,{children:[(0,n.tZ)(h,{status:e.status}),(0,n.tZ)(o.Text,{elementDescriptor:o.descriptors.verificationLinkStatusText,colorScheme:"secondary",localizationKey:(0,o.localizationKeys)("signIn.emailLink.unusedTab.title")})]})}),h=e=>{let{status:t}=e;return(0,n.tZ)(o.Flex,{elementDescriptor:o.descriptors.verificationLinkStatusIconBox,center:!0,sx:e=>({width:e.sizes.$24,height:e.sizes.$24,borderRadius:e.radii.$circle,backgroundColor:e.colors.$neutralAlpha100,color:u(e,t),animation:`${c.animations.dropdownSlideInScaleAndFade} 500ms ease`}),children:(0,n.tZ)(o.Icon,{elementDescriptor:o.descriptors.verificationLinkStatusIcon,icon:d[t],sx:e=>({height:e.sizes.$6,width:e.sizes.$5})})})}},686:function(e,t,r){r.d(t,{$:()=>p});var n=r(9109),o=r(3531),a=r(3799),i=r(9144),l=r(6917),s=r(1576),c=r(4676),d=r(7623),u=r(6273);let p=e=>{let{redirectUrl:t,redirectUrlComplete:r,verifyEmailPath:p,verifyPhonePath:m,continuePath:h}=e,{handleEmailLinkVerification:g}=(0,a.cL)(),{navigate:f}=(0,c.useRouter)(),b=(0,s.useCoreSignUp)(),[v,y]=i.useState("loading"),w=async()=>{try{await (0,d._v)(750),await g({redirectUrlComplete:r,redirectUrl:t},f),y("verified_switch_tab"),await (0,d._v)(750),await (0,l.vx)({signUp:b,verifyEmailPath:p,verifyPhonePath:m,continuePath:h,navigate:f})}catch(e){if((0,o.G1)(e)&&(e.code===o.u$.Expired||e.code===o.u$.ClientMismatch)){y(e.code);return}y(o.u$.Failed)}};return i.useEffect(()=>{w()},[]),(0,n.tZ)(u.E,{title:e.texts[v]?.title||"",subtitle:e.texts[v]?.subtitle||"",status:v})}},3559:function(e,t,r){r.d(t,{Ci:()=>c,Cv:()=>s,N2:()=>l});var n=r(9109),o=r(3799),a=r(9144),i=r(4676);let l=e=>{let{session:t}=(0,o.kP)();return!!t?.id&&("function"==typeof e?e(t.checkAuthorization):t.checkAuthorization(e))},s=e=>{let{children:t,fallback:r,redirectTo:o,...s}=e,c=l("function"==typeof s.condition?s.condition:s),{navigate:d}=(0,i.useRouter)();return((0,a.useEffect)(()=>{!c&&o&&d(o)},[c,o]),!c&&r)?(0,n.tZ)(n.HY,{children:r}):c?(0,n.tZ)(n.HY,{children:t}):null};function c(e,t){let r=e.displayName||e.name||"Component",o=r=>(0,n.tZ)(s,{...t,children:(0,n.tZ)(e,{...r})});return o.displayName=`withProtect(${r})`,o}},7772:function(e,t,r){r.d(t,{kJ:()=>l,vO:()=>i,vX:()=>a});var n=r(5059);let o=Object.freeze({email_address_username:{label:(0,n.u)("formFieldLabel__emailAddress_username"),placeholder:(0,n.u)("formFieldInputPlaceholder__emailAddress_username"),type:"text",action:(0,n.u)("signIn.start.actionLink__use_email_username")},email_address:{label:(0,n.u)("formFieldLabel__emailAddress"),placeholder:(0,n.u)("formFieldInputPlaceholder__emailAddress"),type:"email",action:(0,n.u)("signIn.start.actionLink__use_email")},phone_number:{label:(0,n.u)("formFieldLabel__phoneNumber"),placeholder:(0,n.u)("formFieldInputPlaceholder__phoneNumber"),type:"tel",action:(0,n.u)("signIn.start.actionLink__use_phone")},username:{label:(0,n.u)("formFieldLabel__username"),placeholder:(0,n.u)("formFieldInputPlaceholder__username"),type:"text",action:(0,n.u)("signIn.start.actionLink__use_username")},default:{label:"",placeholder:"",type:"text",action:""}}),a=e=>{let t=[...e.filter(e=>"passkey"!==e)];return["email_address","username"].every(e=>t.includes(e))&&(t=t.filter(e=>!["email_address","username"].includes(e))).unshift("email_address_username"),t},i=(e,t)=>{let r=e.indexOf(t);return -1===r?{currentIdentifier:{...o.default},nextIdentifier:void 0}:{currentIdentifier:{...o[t]},nextIdentifier:e.length>1?{...o[e[(r+1)%e.length]]}:void 0}},l=Object.freeze({Password:"password",OTP:"otp"})},8969:function(e,t,r){r.d(t,{q7:()=>d,sN:()=>V,e_:()=>w,dN:()=>h,L:()=>D,vX:()=>i.vX,a2:()=>H,LE:()=>T,wT:()=>$.wT,Hy:()=>j,Qz:()=>g,vO:()=>i.vO,Ej:()=>l.E,en:()=>N,s_:()=>S,N2:()=>c.N2,Ci:()=>c.Ci,aX:()=>a,Cv:()=>c.Cv,o4:()=>f});var n=r(9541);r(8246),r(4174);var o=r(9109);let a=e=>{let{icon:t,text:r,textSx:a,actionLabel:i,onClick:l}=e;return(0,o.tZ)(n.Flex,{sx:e=>({background:e.colors.$neutralAlpha50,padding:`${e.space.$2x5} ${e.space.$4}`,justifyContent:"space-between",alignItems:"flex-start",borderRadius:e.radii.$md}),children:(0,o.BX)(n.Flex,{gap:2,children:[(0,o.tZ)(n.Icon,{colorScheme:"neutral",icon:t,sx:e=>({marginTop:e.space.$1})}),(0,o.BX)(n.Col,{gap:4,children:[(0,o.tZ)(n.Text,{colorScheme:"secondary",sx:a,localizationKey:r,children:e.children}),i&&(0,o.tZ)(n.Link,{colorScheme:"primary",variant:"subtitle",localizationKey:i,onClick:e=>{l?.(e)}})]})]})})};var i=r(7772),l=r(6273);r(686);var s=r(9144),c=r(3559);let d=(0,s.forwardRef)((e,t)=>(0,o.tZ)(n.Box,{ref:t,sx:e=>({width:"100%",height:e.space.$12,position:"relative"}),children:(0,o.tZ)(n.Box,{sx:{margin:"auto",position:"absolute",left:"50%",top:"50%",transform:"translateY(-50%) translateX(-50%)"},children:(0,o.tZ)(n.Spinner,{size:"sm",colorScheme:"primary",elementDescriptor:n.descriptors.spinner})})}));var u=r(8545),p=r(2464),m=r(1201);let h=e=>{let{notificationCount:t,containerSx:r,shouldAnimate:a=!0,...i}=e,l=(0,p.Tb)(),{t:s}=(0,n.useLocalizations)(),c=s((0,n.localizationKeys)("locale")),d=(0,u.$p)(t,c);return(0,o.tZ)(n.Flex,{justify:"center",align:"center",as:"span",sx:[e=>({marginLeft:e.space.$1x5}),r],children:(0,o.tZ)(n.NotificationBadge,{sx:e=>({animation:a&&!l?`${m.animations.notificationAnimation} ${e.transitionDuration.$textField} ${e.transitionTiming.$slowBezier} 0s 1 normal forwards`:"none"}),...i,children:d})})},g=()=>{let e=[];return{print:()=>e.forEach(e=>e()),printableProps:{onPrint:t=>e.push(t)}}},f=e=>{let{children:t,onPrint:r}=e,n=s.useRef(null);return r(()=>{y(n)}),(0,o.tZ)("div",{ref:n,style:{position:"fixed",left:"-9999px",top:0,display:"none"},children:t})},b=(e,t="[data-emotion=cl-internal]")=>{if(!e.contentDocument)return;let r=[...document.head.querySelectorAll(t)].map(e=>e.innerHTML).join("\n"),n=e.contentDocument.createElement("style");n.innerHTML=r,e.contentDocument.head.prepend(n)},v=e=>{e.contentDocument&&(e.contentDocument.body.style.fontFamily="Arial",e.contentDocument.body.style.cssText=`* {
-webkit-print-color-adjust: exact !important;
color-adjust: exact !important;
print-color-adjust: exact !important;
}`)},y=e=>{let t=e.current;if(!t)return;let r=document.createElement("iframe");r.style.position="fixed",r.style.right="-2000px",r.style.bottom="-2000px",r.onload=()=>{b(r),v(r),r.contentDocument&&r.contentWindow&&(r.contentDocument.body.innerHTML=t.innerHTML,r.contentWindow.print())},window.document.body.appendChild(r)},w=e=>{let{value:t,id:r,...a}=e;return(0,o.tZ)(n.Box,{as:"span",elementDescriptor:[n.descriptors.providerIcon,n.descriptors.socialButtonsProviderInitialIcon],elementId:n.descriptors.socialButtonsProviderInitialIcon.setId(r),sx:e=>({...m.common.centeredFlex("inline-flex"),width:e.space.$4,height:e.space.$4,borderRadius:e.radii.$sm,color:e.colors.$colorTextOnPrimaryBackground,backgroundColor:e.colors.$primary500}),...a,children:(0,o.tZ)(n.Text,{as:"span",variant:"buttonSmall",sx:{...m.common.centeredFlex("inline-flex"),width:"100%"},children:t[0].toUpperCase()})})};var x=r(4718);let S=e=>{let{size:t=200,url:r,...a}=e;return(0,o.tZ)(n.Flex,{elementDescriptor:n.descriptors.qrCodeRow,...a,children:(0,o.tZ)(n.Flex,{elementDescriptor:n.descriptors.qrCodeContainer,sx:e=>({backgroundColor:"white",padding:e.space.$2x5}),children:(0,o.tZ)(x.t,{value:r||"",size:t})})})};var $=r(6749),C=r(3799),_=r(2672),k=r(431),P=r(9460),I=r(8487),A=r(7623);let T=(0,_.withCardStateProvider)(e=>{let{title:t,messageLine1:r,messageLine2:a,deleteResource:i,onSuccess:l,onReset:s}=e,c=(0,_.useCardState)(),d=(0,C.WZ)(i),u=async()=>{try{await d().then(l)}catch(e){(0,A.S3)(e,[],c.setError)}};return(0,o.tZ)(I.Y,{headerTitle:t,headerSubtitle:r,children:(0,o.BX)(k.l.Root,{onSubmit:u,children:[a?(0,o.tZ)(n.Text,{colorScheme:"secondary",localizationKey:a}):null,(0,o.tZ)(P.A,{submitLabel:(0,n.localizationKeys)("userProfile.formButtonPrimary__remove"),colorScheme:"danger",onReset:s})]})})});var R=r(7321),B=r(4455),Z=r(1455),z=r(4676);let D=(0,_.withCardStateProvider)(e=>(0,o.tZ)(n.Flow.Part,{part:"ssoCallback",children:(0,o.tZ)(L,{...e})})),L=e=>{let{handleRedirectCallback:t,__internal_setActiveInProgress:r}=(0,C.cL)(),{navigate:a}=(0,z.useRouter)(),i=(0,_.useCardState)();return s.useEffect(()=>{let n;if(!0!==r){let r=new URLSearchParams(window.location.search).get("intent");t({...e,reloadResource:"signIn"===r||"signUp"===r?r:void 0},a).catch(e=>{(0,A.S3)(e,[],i.setError),n=setTimeout(()=>void a("../"),4e3)})}return()=>clearTimeout(n)},[A.S3,t]),(0,o.tZ)(n.Flow.Part,{part:"ssoCallback",children:(0,o.BX)(B.Z.Root,{children:[(0,o.BX)(B.Z.Content,{children:[(0,o.tZ)(B.Z.Alert,{children:i.error}),(0,o.tZ)(Z.I,{}),(0,o.tZ)(R.S,{})]}),(0,o.tZ)(B.Z.Footer,{})]})})};var E=r(1402),F=r(5274),O=r(6917),M=r(1576);function U(e,t,r,n){let a=e.displayName||e.name||"Component";e.displayName=a;let i=a=>{let{navigate:i}=(0,z.useRouter)(),l=(0,C.cL)(),c=(0,M.useEnvironment)(),d=(0,M.useOptions)(),u=t(l,c,d);return(s.useEffect(()=>{u&&(n&&(0,E.kZ)(l.publishableKey)&&console.info(n),i(r({clerk:l,environment:c,options:d})))},[]),u)?null:(0,o.tZ)(e,{...a})};return i.displayName=`withRedirect(${a})`,i}let j=e=>{let t=e.displayName||e.name||"Component";e.displayName=t;let r=t=>{let r=(0,M.useSignInContext)();return U(e,O.So,({clerk:e})=>r.sessionTaskUrl||r.afterSignInUrl||e.buildAfterSignInUrl(),r.sessionTaskUrl?F.q.cannotRenderSignInComponentWhenTaskExists:F.q.cannotRenderSignInComponentWhenSessionExists)(t)};return r.displayName=`withRedirectToAfterSignIn(${t})`,r},V=e=>{let t=e.displayName||e.name||"Component";e.displayName=t;let r=t=>{let r=(0,M.useSignUpContext)();return U(e,O.So,({clerk:e})=>r.sessionTaskUrl||r.afterSignUpUrl||e.buildAfterSignUpUrl(),r.sessionTaskUrl?F.q.cannotRenderSignUpComponentWhenTaskExists:F.q.cannotRenderSignUpComponentWhenSessionExists)(t)};return r.displayName=`withRedirectToAfterSignUp(${t})`,r};var W=r(6781);let H=(e={})=>{let{defaultStep:t=0,onNextStep:r}=e,[n,o]=s.useState(t),a=s.useCallback(()=>{r?.(),o(e=>e+1)},[]);return{nextStep:a,prevStep:s.useCallback(()=>o(e=>e-1),[]),goToStep:s.useCallback(e=>o(e),[]),props:{step:n}}},N=e=>{let{step:t,children:r}=e;return(0,o.tZ)(W.f,{children:s.Children.toArray(r)[t]})}},6749:function(e,t,r){r.d(t,{F_:()=>d,Uu:()=>l,X8:()=>a,gC:()=>s,wT:()=>c,zg:()=>i});var n=r(6663),o=r(6971);let a="/sso-callback",i="/verify";function l({ctx:e,baseUrl:t="",intent:r="sign-in"}){let{routing:n,authQueryString:o,path:a}=e;return d({routing:n,baseUrl:t,authQueryString:o,path:a,endpoint:"isCombinedFlow"in e&&e.isCombinedFlow&&"sign-up"===r?`/create${i}`:i})}function s({routing:e,path:t,baseUrl:r,task:o}){return o?d({routing:e,baseUrl:r,path:t,endpoint:`/tasks/${n.m[o.key]}`,authQueryString:null}):null}function c(e,t=""){let{routing:r,authQueryString:n,path:o}=e;return e.ssoCallbackUrl&&e.isCombinedFlow&&"virtual"!==r?e.ssoCallbackUrl:d({routing:r,baseUrl:t,authQueryString:n,path:o,endpoint:a})}let d=({routing:e,authQueryString:t,baseUrl:r,path:n,endpoint:o})=>e&&"hash"!==e?"path"===e?p(n||"",t,o):m(r||"",t,o):u(t,o),u=(e,t)=>{let r=t+(e?`?${e}`:"");return(0,o.KV)({hash:r},{stringify:!0})},p=(e,t,r)=>(0,o.KV)({pathname:e+r,...t?{search:"?"+t}:{}},{stringify:!0}),m=(e,t,r)=>{let n=r+(t?`?${t}`:"");return(0,o.KV)({base:e,hash:n},{stringify:!0})}},1673:function(e,t,r){r.d(t,{Zb:()=>a,aw:()=>l,bm:()=>o,g8:()=>i,xM:()=>n});let n={ACCOUNT:"account",SECURITY:"security",BILLING:"billing",API_KEYS:"apiKeys"},o={GENERAL:"general",MEMBERS:"members",BILLING:"billing",API_KEYS:"apiKeys"},a={MANAGE_ACCOUNT:"manageAccount",SIGN_OUT:"signOut"},i="clerk-profileCardScrollBox",l="clerk-organizationProfileScrollBox"},5747:function(e,t,r){r.d(t,{O:()=>s,u:()=>l});var n=r(9109),o=r(9144),a=r(8555);let i=o.createContext(null);function l({children:e,value:t}){return(0,n.tZ)(i.Provider,{value:t,children:e})}function s(){let e=o.useContext(i);return(0,a.LB)(e,"EnvironmentProvider"),e}},6952:function(e,t,r){r.d(t,{D2:()=>l,fM:()=>i,jI:()=>a});var n=r(9109),o=r(9144);let a=o.createContext({});function i({children:e,value:t}){return(0,n.tZ)(a.Provider,{value:t,children:e})}function l(){let e=o.useContext(a);if(void 0===e)throw Error("useOptions must be used within an OptionsContext");return e}},6988:function(e,t,r){r.d(t,{G:()=>a,H:()=>o});var n=r(9144);let o=(0,n.createContext)(null),a=()=>{let e=(0,n.useContext)(o);if(null===e)throw Error("Clerk: useSessionTasksContext called outside of the mounted SessionTasks component.");return e}},5298:function(e,t,r){r.d(t,{H2:()=>_,EY:()=>T,Tq:()=>eh,WL:()=>ea,Pd:()=>el,Ki:()=>ec,HA:()=>f,cj:()=>E,mN:()=>Y,js:()=>B,p4:()=>ei,fR:()=>m,p6:()=>er,gc:()=>V,DC:()=>F,MB:()=>eu,w6:()=>q,yy:()=>eo,xR:()=>ed,O$:()=>A,Z$:()=>L,I9:()=>h,sC:()=>w,wP:()=>R,yQ:()=>eg,Tr:()=>N,Fk:()=>W,xn:()=>es,KI:()=>ep,Yf:()=>k,Rh:()=>z,pl:()=>U,_t:()=>et,FA:()=>P,dh:()=>H,rC:()=>y,xr:()=>x,HQ:()=>M,WH:()=>en,Us:()=>Z,Bf:()=>O,OQ:()=>em,Pb:()=>b,fm:()=>C,lO:()=>g});var n=r(3799),o=r(4944),a=r(9144),i=r(753),l=r(6917),s=r(1607),c=r(6749),d=r(1576),u=r(4676),p=r(8555);let m=(0,a.createContext)(null),h=()=>{let e=(0,a.useContext)(m),{navigate:t}=(0,u.useRouter)(),{displayConfig:r,userSettings:h}=(0,d.useEnvironment)(),{queryParams:g,queryString:f}=(0,u.useRouter)(),b=h.signUp.mode,v=(0,d.useOptions)(),y=(0,n.cL)();if(null===e||"SignIn"!==e.componentName)throw Error("Clerk: useSignInContext called outside of the mounted SignIn component.");let w="restricted"!==b&&!!(!v.signUpUrl&&v.signInUrl&&!(0,o.sD)(v.signInUrl))&&!1!==e.withSignUp||e.withSignUp||!1,{componentName:x,mode:S,...$}=e,C=(0,a.useMemo)(()=>(0,p.x6)(f,i.im),[]),_=new s.O(v,{...$,signInFallbackRedirectUrl:$.signInFallbackRedirectUrl||$.fallbackRedirectUrl,signInForceRedirectUrl:$.signInForceRedirectUrl||$.forceRedirectUrl},g,S);delete $.fallbackRedirectUrl,delete $.forceRedirectUrl;let k=y.buildUrlWithAuth(_.getAfterSignInUrl()),P=y.buildUrlWithAuth(_.getAfterSignUpUrl()),I="path"===$.routing&&$.path||v.signInUrl||r.signInUrl,A=w?"path"===$.routing&&$.path||v.signUpUrl||r.signUpUrl:$.signUpUrl||v.signUpUrl||r.signUpUrl,T=$.waitlistUrl||v.waitlistUrl||r.waitlistUrl,R=_.getPreservedSearchParams();I=(0,l.KV)({base:I,hashSearchParams:[g,R]},{stringify:!0}),A=(0,l.KV)({base:A,hashSearchParams:[g,R]},{stringify:!0}),T=(0,l.KV)({base:T,hashSearchParams:[g,R]},{stringify:!0});let B=_.toSearchParams().toString(),Z=(0,c.F_)({routing:$.routing,baseUrl:A,authQueryString:B,path:$.path,endpoint:w?"/create"+c.zg:c.zg}),z=(0,c.F_)({routing:$.routing,baseUrl:A,authQueryString:B,path:$.path,endpoint:w?"/create"+c.X8:c.X8});w&&(A=(0,l.KV)({base:I,hashPath:"/create",hashSearchParams:[g,R]},{stringify:!0}));let D=(0,l.KV)({base:A,hashPath:"/continue"},{stringify:!0}),L=(0,c.gC)({task:y.session?.currentTask,path:$.path,routing:$.routing,baseUrl:I});return{...$,transferable:$.transferable??!0,oauthFlow:$.oauthFlow||"auto",componentName:x,signUpUrl:A,signInUrl:I,waitlistUrl:T,afterSignInUrl:k,afterSignUpUrl:P,emailLinkRedirectUrl:Z,ssoCallbackUrl:z,sessionTaskUrl:L,navigateAfterSignIn:()=>t(k),signUpContinueUrl:D,queryParams:g,initialValues:{...$.initialValues,...C},authQueryString:B,isCombinedFlow:w}},g=(0,a.createContext)(null),f=()=>{let e=(0,a.useContext)(g),{navigate:t}=(0,u.useRouter)(),{displayConfig:r,userSettings:m}=(0,d.useEnvironment)(),{queryParams:h,queryString:f}=(0,u.useRouter)(),b=m.signUp.mode,v=(0,d.useOptions)(),y=(0,n.cL)(),w="restricted"!==b&&!!(!v.signUpUrl&&v.signInUrl&&!(0,o.sD)(v.signInUrl)&&"public"===b),x=(0,a.useMemo)(()=>(0,p.x6)(f,i.bf),[]);if(!e||"SignUp"!==e.componentName)throw Error("Clerk: useSignUpContext called outside of the mounted SignUp component.");let{componentName:S,mode:$,...C}=e,_=new s.O(v,{...C,signUpFallbackRedirectUrl:C.signUpFallbackRedirectUrl||C.fallbackRedirectUrl,signUpForceRedirectUrl:C.signUpForceRedirectUrl||C.forceRedirectUrl},h,$);delete C.fallbackRedirectUrl,delete C.forceRedirectUrl;let k=y.buildUrlWithAuth(_.getAfterSignUpUrl()),P=y.buildUrlWithAuth(_.getAfterSignInUrl()),I="path"===C.routing&&C.path||v.signUpUrl||r.signUpUrl,A=C.signInUrl||v.signInUrl||r.signInUrl,T=C.waitlistUrl||v.waitlistUrl||r.waitlistUrl,R=_.getPreservedSearchParams();A=(0,l.KV)({base:A,hashSearchParams:[h,R]},{stringify:!0}),I=(0,l.KV)({base:I,hashSearchParams:[h,R]},{stringify:!0}),T=(0,l.KV)({base:T,hashSearchParams:[h,R]},{stringify:!0});let B=_.toSearchParams().toString(),Z=C.emailLinkRedirectUrl??(0,c.F_)({routing:C.routing,baseUrl:I,authQueryString:B,path:C.path,endpoint:w?"/create"+c.zg:c.zg}),z=C.ssoCallbackUrl??(0,c.F_)({routing:C.routing,baseUrl:I,authQueryString:B,path:C.path,endpoint:w?"/create"+c.X8:c.X8}),D=(0,l.KV)({base:A,hashPath:"/factor-two"},{stringify:!0}),L=(0,c.gC)({task:y.session?.currentTask,path:C.path,routing:C.routing,baseUrl:I});return{...C,oauthFlow:C.oauthFlow||"auto",componentName:S,signInUrl:A,signUpUrl:I,waitlistUrl:T,secondFactorUrl:D,afterSignUpUrl:k,afterSignInUrl:P,emailLinkRedirectUrl:Z,ssoCallbackUrl:z,sessionTaskUrl:L,navigateAfterSignUp:()=>t(k),queryParams:h,initialValues:{...C.initialValues,...x},authQueryString:B,isCombinedFlow:w}},b=()=>{let{navigate:e}=(0,u.useRouter)(),t=(0,n.cL)();return{navigateAfterSignOut:()=>e(t.buildAfterSignOutUrl()),navigateAfterMultiSessionSingleSignOutUrl:()=>e(t.buildAfterMultiSessionSingleSignOutUrl()),afterSignOutUrl:t.buildAfterSignOutUrl(),afterMultiSessionSingleSignOutUrl:t.buildAfterMultiSessionSingleSignOutUrl()}},v="user",y=(0,a.createContext)(v),w=()=>(0,a.useContext)(y)||v,x=()=>"user"===w()?"userProfile":"organizationProfile";var S=r(7623),$=r(5747);let C=(0,a.createContext)(null),_=()=>{let e=(0,a.useContext)(C),{queryParams:t}=(0,u.useRouter)(),r=(0,n.cL)(),o=(0,$.O)();if(!e||"UserProfile"!==e.componentName)throw Error("Clerk: useUserProfileContext called outside of the mounted UserProfile component.");let{componentName:i,customPages:l,...s}=e,c=(0,a.useMemo)(()=>(0,S.BG)(l||[],r,o),[l]);return{...s,pages:c,componentName:i,queryParams:t,authQueryString:""}},k=(0,a.createContext)(null),P=()=>{let e=(0,a.useContext)(k);if(!e||"UserVerification"!==e.componentName)throw Error("Clerk: useUserVerificationContext called outside of the mounted UserVerification component.");let{componentName:t,...r}=e;return{...r,componentName:t}};var I=r(1188);let A=(0,a.createContext)(null),T=()=>{let e=(0,a.useContext)(A),t=(0,n.cL)(),{navigate:r}=(0,u.useRouter)(),{displayConfig:o}=(0,d.useEnvironment)(),i=(0,d.useOptions)();if(!e||"UserButton"!==e.componentName)throw Error("Clerk: useUserButtonContext called outside of the mounted UserButton component.");let{componentName:l,customMenuItems:s,...c}=e,p=c.signInUrl||i.signInUrl||o.signInUrl,m=c.userProfileUrl||o.userProfileUrl;c.afterSignOutUrl&&(0,I.x6)(c,"afterSignOutUrl","Move 'afterSignOutUrl' to '<ClerkProvider/>");let h=c.afterSignOutUrl||t.buildAfterSignOutUrl();c.afterSignOutUrl&&(0,I.x6)(c,"afterMultiSessionSingleSignOutUrl","Move 'afterMultiSessionSingleSignOutUrl' to '<ClerkProvider/>");let g=c.afterMultiSessionSingleSignOutUrl||t.buildAfterMultiSessionSingleSignOutUrl(),f=c.afterSwitchSessionUrl||o.afterSwitchSessionUrl,b=c.userProfileUrl&&!c.userProfileMode?"navigation":c.userProfileMode,v=(0,a.useMemo)(()=>(0,S.c8)(s||[],t),[]);return{...c,componentName:l,navigateAfterMultiSessionSingleSignOut:()=>t.redirectWithAuth(g),navigateAfterSignOut:()=>r(h),signInUrl:p,userProfileUrl:m,afterMultiSessionSingleSignOutUrl:g,afterSignOutUrl:h,afterSwitchSessionUrl:f,userProfileMode:b||"modal",menutItems:v}},R=(0,a.createContext)(null),B=()=>{let e=(0,a.useContext)(R),{navigate:t}=(0,u.useRouter)(),{displayConfig:r,organizationSettings:n}=(0,d.useEnvironment)();if(!e||"OrganizationSwitcher"!==e.componentName)throw Error("Clerk: useOrganizationSwitcherContext called outside OrganizationSwitcher.");let{componentName:o,...i}=e,l=i.afterCreateOrganizationUrl||r.afterCreateOrganizationUrl,s=i.afterLeaveOrganizationUrl||r.afterLeaveOrganizationUrl,c=({organization:e,user:r})=>{let n=m({organization:e,user:r});return n?t(n):Promise.resolve()},m=({organization:e,user:t})=>"function"==typeof i.afterSelectPersonalUrl&&t?i.afterSelectPersonalUrl(t):"function"==typeof i.afterSelectOrganizationUrl&&e?i.afterSelectOrganizationUrl(e):i.afterSelectPersonalUrl&&t?(0,p.Q8)({urlWithParam:i.afterSelectPersonalUrl,entity:t}):i.afterSelectOrganizationUrl&&e?(0,p.Q8)({urlWithParam:i.afterSelectOrganizationUrl,entity:e}):void 0,h=i.organizationProfileUrl&&!i.organizationProfileMode?"navigation":i.organizationProfileMode,g=i.createOrganizationUrl&&!i.createOrganizationMode?"navigation":i.createOrganizationMode;return{...i,hidePersonal:n.forceOrganizationSelection||i.hidePersonal||!1,organizationProfileMode:h||"modal",createOrganizationMode:g||"modal",skipInvitationScreen:i.skipInvitationScreen||!1,hideSlug:i.hideSlug||!1,afterCreateOrganizationUrl:l,afterLeaveOrganizationUrl:s,navigateOrganizationProfile:()=>t(i.organizationProfileUrl||r.organizationProfileUrl),navigateCreateOrganization:()=>t(i.createOrganizationUrl||r.createOrganizationUrl),navigateAfterSelectOrganization:e=>c({organization:e}),navigateAfterSelectPersonal:e=>c({user:e}),afterSelectOrganizationUrl:e=>m({organization:e}),afterSelectPersonalUrl:e=>m({user:e}),componentName:o}},Z=(0,a.createContext)(null),z=()=>{let e=(0,a.useContext)(Z),{navigate:t}=(0,u.useRouter)(),{displayConfig:r,organizationSettings:n}=(0,d.useEnvironment)();if(!e||"OrganizationList"!==e.componentName)throw Error("Clerk: useOrganizationListContext called outside OrganizationList.");let{componentName:o,...i}=e,l=i.afterCreateOrganizationUrl||r.afterCreateOrganizationUrl,s=({organization:e,user:r})=>"function"==typeof i.afterSelectPersonalUrl&&r?t(i.afterSelectPersonalUrl(r)):"function"==typeof i.afterSelectOrganizationUrl&&e?t(i.afterSelectOrganizationUrl(e)):i.afterSelectPersonalUrl&&r?t((0,p.Q8)({urlWithParam:i.afterSelectPersonalUrl,entity:r})):i.afterSelectOrganizationUrl&&e?t((0,p.Q8)({urlWithParam:i.afterSelectOrganizationUrl,entity:e})):Promise.resolve();return{...i,afterCreateOrganizationUrl:l,skipInvitationScreen:i.skipInvitationScreen||!1,hideSlug:i.hideSlug||!1,hidePersonal:n.forceOrganizationSelection||i.hidePersonal||!1,navigateAfterCreateOrganization:e=>"function"==typeof i.afterCreateOrganizationUrl?t(i.afterCreateOrganizationUrl(e)):i.afterCreateOrganizationUrl?t((0,p.Q8)({urlWithParam:i.afterCreateOrganizationUrl,entity:e})):t(r.afterCreateOrganizationUrl),navigateAfterSelectOrganization:e=>s({organization:e}),navigateAfterSelectPersonal:e=>s({user:e}),componentName:o}};var D=r(1673);let L=(0,a.createContext)(null),E=()=>{let e=(0,a.useContext)(L),{navigate:t}=(0,u.useRouter)(),r=(0,d.useEnvironment)(),o=(0,n.cL)();if(!e||"OrganizationProfile"!==e.componentName)throw Error("Clerk: useOrganizationProfileContext called outside OrganizationProfile.");let{componentName:i,customPages:l,...s}=e,c=(0,a.useMemo)(()=>(0,S.sn)(l||[],o,r),[l]),p=c.routes[0].id===D.bm.MEMBERS,m=c.routes[0].id===D.bm.GENERAL,h=c.routes[0].id===D.bm.BILLING,g=c.routes[0].id===D.bm.API_KEYS;return{...s,pages:c,navigateAfterLeaveOrganization:()=>t(s.afterLeaveOrganizationUrl||r.displayConfig.afterLeaveOrganizationUrl),componentName:i,navigateToGeneralPageRoot:()=>t(m?"../":p?"./organization-general":"../organization-general"),isMembersPageRoot:p,isGeneralPageRoot:m,isBillingPageRoot:h,isApiKeysPageRoot:g}},F=(0,a.createContext)(null),O=()=>{let e=(0,a.useContext)(F),{navigate:t}=(0,u.useRouter)(),{displayConfig:r}=(0,d.useEnvironment)();if(!e||"CreateOrganization"!==e.componentName)throw Error("Clerk: useCreateOrganizationContext called outside CreateOrganization.");let{componentName:n,...o}=e;return{...o,skipInvitationScreen:o.skipInvitationScreen||!1,hideSlug:o.hideSlug||!1,navigateAfterCreateOrganization:e=>"function"==typeof o.afterCreateOrganizationUrl?t(o.afterCreateOrganizationUrl(e)):o.afterCreateOrganizationUrl?t((0,p.Q8)({urlWithParam:o.afterCreateOrganizationUrl,entity:e})):t(r.afterCreateOrganizationUrl),componentName:n}},M=(0,a.createContext)(null),U=()=>{let e=(0,a.useContext)(M),t=(0,d.useOptions)(),{displayConfig:r}=(0,d.useEnvironment)(),{queryParams:n}=(0,u.useRouter)();if(!e||"GoogleOneTap"!==e.componentName)throw Error("Clerk: useGoogleOneTapContext called outside GoogleOneTap.");let{componentName:o,...i}=e,c=(0,a.useCallback)(e=>{let o=new s.O(t,{...i,signInFallbackRedirectUrl:e,signUpFallbackRedirectUrl:e},n),a=t.signUpUrl||r.signUpUrl,c=t.signInUrl||r.signInUrl,d=o.getPreservedSearchParams();c=(0,l.KV)({base:c,hashSearchParams:[n,d]},{stringify:!0}),a=(0,l.KV)({base:a,hashSearchParams:[n,d]},{stringify:!0});let u=o.getAfterSignInUrl(),p=o.getAfterSignUpUrl(),m=(0,l.KV)({base:a,hashPath:"/continue",hashSearch:new URLSearchParams({sign_up_force_redirect_url:p}).toString()},{stringify:!0}),h=(0,l.KV)({base:c,hashPath:"/factor-one",hashSearch:new URLSearchParams({sign_in_force_redirect_url:u}).toString()},{stringify:!0}),g=(0,l.KV)({base:c,hashPath:"/factor-two",hashSearch:new URLSearchParams({sign_in_force_redirect_url:u}).toString()},{stringify:!0});return{signInUrl:c,signUpUrl:a,firstFactorUrl:h,secondFactorUrl:g,continueSignUpUrl:m,signInForceRedirectUrl:u,signUpForceRedirectUrl:p}},[i,r.signInUrl,r.signUpUrl,t,n]);return{...i,componentName:o,generateCallbackUrls:c}},j=["email_address"],V=(0,a.createContext)(null),W=()=>{let e=(0,a.useContext)(V),{displayConfig:t}=(0,d.useEnvironment)(),r=(0,d.useOptions)(),{queryString:n}=(0,u.useRouter)(),o=(0,a.useMemo)(()=>(0,p.x6)(n,j),[]);if(!e||"Waitlist"!==e.componentName)throw Error("Clerk: useWaitlistContext called outside Waitlist.");let{componentName:i,...s}=e,c=s.signInUrl||r.signInUrl||t.signInUrl;return c=(0,l.KV)({base:c},{stringify:!0}),{...s,componentName:i,signInUrl:c,initialValues:{...o}}},H=(0,a.createContext)(null),N=()=>{let e=(0,a.useContext)(H);if(!e||"PricingTable"!==e.componentName)throw Error("Clerk: usePricingTableContext called outside PricingTable.");let{componentName:t,...r}=e;return{...r,componentName:t}};var K=r(6952);function X(e,t){if(!e)throw Error(t)}let Y=(0,a.createContext)(null),q=()=>{let e=(0,a.useContext)(Y),t=(0,n.cL)(),r=(0,K.D2)();if(!e||"Checkout"!==e.componentName)throw Error("Clerk: useCheckoutContext called outside Checkout.");let o=(0,a.useMemo)(()=>{if(e.portalRoot)return;let n=e.newSubscriptionRedirectUrl||t.buildNewSubscriptionRedirectUrl?.();return(0,l.QD)(r?.allowedRedirectOrigins,window.location.origin)(n)?n:void 0},[e.portalRoot,e.newSubscriptionRedirectUrl,t,r?.allowedRedirectOrigins]),{componentName:i,...s}=e;return{...s,componentName:i,newSubscriptionRedirectUrl:o,subscriber:()=>"org"===s.subscriberType&&t.organization?(X(t.organization,'Clerk: subscriberType is "org" but no active organization was found'),t.organization):(X(t.user,"Clerk: no active user found"),t.user)}};var G=r(9626),Q=r(3139),J=r(1085);let ee={dedupingInterval:6e4,keepPreviousData:!0},et=()=>{let{organization:e}=(0,n.o8)(),{user:t}=(0,n.aF)();return{key:"commerce-payment-sources",resourceId:"org"===w()?e?.id:t?.id}},er=()=>{let{organization:e}=(0,n.o8)(),{user:t}=(0,n.aF)(),r=w(),o=et();return(0,G.default)(o,()=>("org"===r?e:t)?.getPaymentSources({}),ee)},en=()=>{let{organization:e}=(0,n.o8)(),{user:t}=(0,n.aF)(),r=w();return{key:"commerce-payment-history",userId:t?.id,args:{orgId:"org"===r?e?.id:void 0}}},eo=()=>{let{billing:e}=(0,n.cL)(),t=en();return(0,G.default)(t,({args:t,userId:r})=>r?e.getPaymentAttempts(t):void 0,ee)},ea=()=>{let{organization:e}=(0,n.o8)(),{user:t}=(0,n.aF)(),r=w();return{key:"commerce-statements",userId:t?.id,args:{orgId:"org"===r?e?.id:void 0}}},ei=()=>{let{billing:e}=(0,n.cL)(),t=ea();return(0,G.default)(t,({args:t,userId:r})=>r?e.getStatements(t):void 0,ee)},el=()=>{let{billing:e}=(0,n.cL)(),{organization:t}=(0,n.o8)(),{user:r,isSignedIn:o}=(0,n.aF)(),i=w(),{data:l}=es(),{data:s,...c}=(0,G.default)({key:"commerce-subscriptions",userId:r?.id,args:{orgId:"org"===i?t?.id:void 0}},({args:t,userId:r})=>r?e.getSubscriptions(t):void 0,ee);return{data:(0,a.useMemo)(()=>{if(!s)return[];let e=l?.find(e=>!1===e.hasBaseFee&&0===e.amount);if(o&&e&&(0===s.data.length||!s.data.some(e=>!e.canceledAt))){let t=s.data.find(e=>e.canceledAt);return[...s.data,new Q.gU({object:"commerce_subscription",id:"__implicit_default_plan_subscription__",payment_source_id:"",plan:e.__internal_toSnapshot(),plan_period:"month",canceled_at:null,status:0===s.data.length?"active":"upcoming",period_start:t?.periodEnd||0,period_end:0})]}return s.data},[s,l,o]),...c}},es=()=>{let{billing:e}=(0,n.cL)(),t=w();return(0,G.default)({key:"commerce-plans",args:{subscriberType:t}},({args:t})=>e.getPlans(t),ee)},ec=()=>{let e=(0,n.cL)(),{session:t}=(0,n.kP)(),r=w(),o=(0,a.useMemo)(()=>!!(!e.session||e?.session?.checkAuthorization({permission:"org:sys_billing:manage"})||"user"===r),[e,r]),{data:i,mutate:l}=el(),{data:s,mutate:c}=(0,G.default)({key:"commerce-plans",args:{subscriberType:r}}),{mutate:d}=(0,G.default)(ea()),{mutate:u}=er(),p=(0,a.useCallback)(()=>{l(),c(),d(),u()},[l,c,d,u]),m=(0,a.useMemo)(()=>0===i.length||!i.some(e=>!e.canceledAt),[i]),h=(0,a.useCallback)(e=>i.find(t=>t.plan.id===e.id),[i]),g=(0,a.useCallback)(e=>i.filter(t=>t.plan.id===e.id),[i]),f=(0,a.useCallback)((e,t="month")=>{let r=g(e);if(r.length>1){let e=r.find(e=>e.planPeriod===t);return e||r[0]}if(1===r.length)return r[0]},[g]),b=(0,a.useCallback)(({plan:e,subscription:t})=>{let r=t??(e?h(e):void 0);return!r||!r.canceledAt},[h]),v=(0,a.useMemo)(()=>i.some(e=>"upcoming"===e.status)||m,[i,m]),y=(0,a.useCallback)(({plan:e,subscription:t,isCompact:r=!1,selectedPlanPeriod:n="annual"})=>{let a=t??(e?f(e,n):void 0),l=n;"annual"===l&&t?.plan.annualMonthlyAmount===0&&(l="month");let s=(e?.annualMonthlyAmount??0)>0;return{localizationKey:(()=>{if(a){if(l!==a.planPeriod&&a.canceledAt){if("month"===l)return(0,J.u1)("commerce.switchToMonthly");if(s)return(0,J.u1)("commerce.switchToAnnual")}if(a.canceledAt)return(0,J.u1)("commerce.reSubscribe");if(l!==a.planPeriod){if("month"===l)return(0,J.u1)("commerce.switchToMonthly");if(s)return(0,J.u1)("commerce.switchToAnnual")}return(0,J.u1)("commerce.manageSubscription")}return i.filter(e=>!e.plan.isDefault).length>0?(0,J.u1)("commerce.switchPlan"):(0,J.u1)("commerce.subscribe")})(),variant:r?"bordered":"solid",colorScheme:r?"secondary":"primary",isDisabled:!o,disabled:!o}},[f,o,i]),x=(0,a.useCallback)(e=>"upcoming"===e.status?(0,J.u1)("badge__startsAt",{date:e.periodStart}):e.canceledAt?(0,J.u1)("badge__canceledEndsAt",{date:e.periodEnd}):(0,J.u1)("badge__renewsAt",{date:e.periodEnd}),[]),$=(0,a.useCallback)(({plan:n,planPeriod:o,onSubscriptionChange:a,mode:i="mounted",event:l,appearance:s,newSubscriptionRedirectUrl:c})=>{let d=f(n,o),u=(0,S.N2)(i,l);d&&d.planPeriod===o&&!d.canceledAt?e.__internal_openPlanDetails({plan:n,initialPlanPeriod:o,subscriberType:r,onSubscriptionCancel:()=>{p(),a?.()},appearance:s,portalRoot:u}):e.__internal_openCheckout({planId:n.id,planPeriod:"annual"===o&&0===n.annualMonthlyAmount?"month":o,subscriberType:r,onSubscriptionComplete:()=>{p(),a?.()},onClose:()=>{t?.id&&e.setActive({session:t.id})},appearance:s,portalRoot:u,newSubscriptionRedirectUrl:c})},[e,p,h,r,t?.id]);return{activeOrUpcomingSubscription:h,activeAndUpcomingSubscriptions:g,activeOrUpcomingSubscriptionBasedOnPlanPeriod:f,isDefaultPlanImplicitlyActiveOrUpcoming:m,handleSelectPlan:$,buttonPropsForPlan:y,canManageSubscription:b,captionForSubscription:x,upcomingSubscriptionsExist:v,defaultFreePlan:(0,a.useMemo)(()=>s?.find(e=>e.isDefault),[s]),revalidateAll:p}},ed=()=>{let{data:e}=ei();return{getStatementById:(0,a.useCallback)(t=>e?.data.find(e=>e.id===t),[e?.data])}},eu=()=>{let{data:e}=eo();return{getPaymentAttemptById:(0,a.useCallback)(t=>e?.data.find(e=>e.id===t),[e?.data])}},ep=(0,a.createContext)(null),em=()=>{let e=(0,a.useContext)(ep);if(!e||"APIKeys"!==e.componentName)throw Error("Clerk: useApiKeysContext called outside ApiKeys.");let{componentName:t,...r}=e;return{...r,componentName:t}},eh=(0,a.createContext)(null),eg=()=>{let e=(0,a.useContext)(eh);if(null===e)throw Error("Clerk: useOAuthConsentContext called outside of the mounted OAuthConsent component.");return e}},1576:function(e,t,r){r.r(t),r.d(t,{CreateOrganizationContext:()=>l.DC,withCoreSessionSwitchGuard:()=>u,useStatementsContext:()=>l.xR,useSubscriberTypeContext:()=>l.sC,UserButtonContext:()=>l.O$,CheckoutContext:()=>l.mN,useUserButtonContext:()=>l.EY,useOptions:()=>d.D2,OptionsContext:()=>d.jI,SignInContext:()=>l.fR,UserVerificationContext:()=>l.Yf,useOrganizationListContext:()=>l.Rh,WaitlistContext:()=>l.gc,SubscriberTypeContext:()=>l.rC,useOrganizationProfileContext:()=>l.cj,useEnvironment:()=>c.O,useWaitlistContext:()=>l.Fk,withCoreUserGuard:()=>i,useCreateOrganizationContext:()=>l.Bf,useAcceptedInvitations:()=>v,PricingTableContext:()=>l.dh,useSignOutContext:()=>l.Pb,OrganizationProfileContext:()=>l.Z$,useStatements:()=>l.p4,useCheckoutContext:()=>l.w6,UserProfileContext:()=>l.fm,usePaymentSourcesCacheKey:()=>l._t,useApiKeysContext:()=>l.OQ,usePlansContext:()=>l.Ki,ComponentContextProvider:()=>s,useUserProfileContext:()=>l.H2,OrganizationSwitcherContext:()=>l.wP,usePaymentAttempts:()=>l.yy,useSignInContext:()=>l.I9,usePaymentAttemptsContext:()=>l.MB,useSubscriptions:()=>l.Pd,useSignUpContext:()=>l.HA,useCoreSignIn:()=>p,useOAuthConsentContext:()=>l.yQ,useStatementsCacheKey:()=>l.WL,usePaymentSources:()=>l.p6,AcceptedInvitationsProvider:()=>b,GoogleOneTapContext:()=>l.HQ,usePlans:()=>l.xn,ApiKeysContext:()=>l.KI,OrganizationListContext:()=>l.Us,useCoreSignUp:()=>m,EnvironmentProvider:()=>c.u,SignUpContext:()=>l.lO,OAuthConsentContext:()=>l.Tq,usePricingTableContext:()=>l.Tr,CoreClerkContextWrapper:()=>g,OptionsProvider:()=>d.fM,useOrganizationSwitcherContext:()=>l.js,useSubscriberTypeLocalizationRoot:()=>l.xr,usePaymentAttemptsCacheKey:()=>l.WH,useGoogleOneTapContext:()=>l.pl,useUserVerification:()=>l.FA});var n=r(9109),o=r(3799),a=r(9144);function i(e){let t=t=>(0,o.SE)()?(0,n.tZ)(e,{...t}):null,r=e.displayName||e.name||"Component";return e.displayName=r,t.displayName=r,t}var l=r(5298);function s({componentName:e,props:t,children:r}){switch(e){case"SignIn":return(0,n.tZ)(l.fR.Provider,{value:{componentName:e,...t},children:r});case"SignUp":return(0,n.tZ)(l.lO.Provider,{value:{componentName:e,...t},children:r});case"UserProfile":return(0,n.tZ)(l.fm.Provider,{value:{componentName:e,...t},children:r});case"UserVerification":return(0,n.tZ)(l.Yf.Provider,{value:{componentName:e,...t},children:r});case"UserButton":return(0,n.tZ)(l.O$.Provider,{value:{componentName:e,...t},children:r});case"OrganizationSwitcher":return(0,n.tZ)(l.wP.Provider,{value:{componentName:e,...t},children:r});case"OrganizationList":return(0,n.tZ)(l.Us.Provider,{value:{componentName:e,...t},children:r});case"OrganizationProfile":return(0,n.tZ)(l.Z$.Provider,{value:{componentName:e,...t},children:r});case"CreateOrganization":return(0,n.tZ)(l.DC.Provider,{value:{componentName:e,...t},children:r});case"GoogleOneTap":return(0,n.tZ)(l.HQ.Provider,{value:{componentName:e,...t},children:r});case"Waitlist":return(0,n.tZ)(l.gc.Provider,{value:{componentName:e,...t},children:r});case"PricingTable":return(0,n.tZ)(l.rC.Provider,{value:t.forOrganizations?"org":"user",children:(0,n.tZ)(l.dh.Provider,{value:{componentName:e,...t},children:r})});case"APIKeys":return(0,n.tZ)(l.KI.Provider,{value:{componentName:e,...t},children:r});case"OAuthConsent":return(0,n.tZ)(l.Tq.Provider,{value:{componentName:e,...t},children:r});default:throw Error(`Unknown component context: ${e}`)}}var c=r(5747),d=r(6952);function u(e){let t=t=>void 0===(0,o.Tt)()?null:(0,n.tZ)(e,{...t}),r=e.displayName||e.name||"Component";return e.displayName=r,t.displayName=r,t}function p(){let e=(0,o.sX)();return(0,o.LB)(e,o.RY),e.signIn}function m(){let e=(0,o.sX)();return(0,o.LB)(e,o.RY),e.signUp}var h=r(8555);function g(e){let t=e.clerk;(0,h.MN)(t);let[r,i]=a.useState({client:t.client,session:t.session,user:t.user,organization:t.organization});a.useEffect(()=>t.addListener(e=>i({...e})),[]);let{client:l,session:s,user:c,organization:d}=r,u=a.useMemo(()=>({value:t}),[]),p=a.useMemo(()=>({value:l}),[l]),m=a.useMemo(()=>({value:s}),[s]),g=a.useMemo(()=>({value:c}),[c]),f=a.useMemo(()=>({value:{organization:d}}),[d]);return(0,n.tZ)(o.b5.Provider,{value:u,children:(0,n.tZ)(o.RY.Provider,{value:p,children:(0,n.tZ)(o.B3.Provider,{value:m,children:(0,n.tZ)(o.f0,{...f.value,swrConfig:e.swrConfig,children:(0,n.tZ)(o.St.Provider,{value:g,children:e.children})})})})})}let f=(0,a.createContext)({acceptedInvitations:[],setAcceptedInvitations:()=>{}});function b({children:e}){let[t,r]=(0,a.useState)([]);return(0,n.tZ)(f.Provider,{value:{acceptedInvitations:t,setAcceptedInvitations:r},children:e})}function v(){return(0,a.useContext)(f)}},8555:function(e,t,r){r.d(t,{LB:()=>i,MN:()=>a,Q8:()=>s,x6:()=>l});var n=r(5027),o=r(4152);function a(e){e||(0,o.G6)()}function i(e,t){e||(0,o.k2)(t)}function l(e,t){let r={};return new URLSearchParams(e).forEach((e,o)=>{t.includes(o)&&"string"==typeof e&&(r[(0,n.TD)(o)]=e)}),r}let s=(0,r(6917).OY)({regex:/:(\w+)/})},9541:function(e,t,r){r.r(t),r.d(t,{localizationKeys:()=>n.u1,Dt:()=>eQ,AppearanceProvider:()=>W,Flex:()=>ew,Table:()=>eW,descriptors:()=>d,Text:()=>eP,Form:()=>eO,Grid:()=>eS,Heading:()=>e_,Input:()=>eR,SimpleButton:()=>eC,Th:()=>eX,Dd:()=>eG,FormLabel:()=>ez,Box:()=>ey,Flow:()=>ev,FormErrorText:()=>eD,Link:()=>ek,FormInfoText:()=>eF,FormWarningText:()=>eE,Tr:()=>eK,CheckboxInput:()=>eB,NotificationBadge:()=>eV,Dl:()=>eq,AlertIcon:()=>eT,Alert:()=>eA,Icon:()=>eM,Image:()=>eI,Span:()=>eJ,FormSuccessText:()=>eL,Spinner:()=>eU,useLocalizations:()=>n.zJ,generateFlowPartClassname:()=>ea,Button:()=>e$,Hr:()=>e0,Badge:()=>ej,Col:()=>ex,Tbody:()=>eN,Td:()=>eY,Thead:()=>eH,RadioInput:()=>eZ,useAppearance:()=>V});var n=r(1085),o=r(215),a=r(7623);let i=(0,a.Nr)()(["button","input","checkbox","radio","table","rootBox","cardBox","card","footerItem","popoverBox","disclosureRoot","disclosureTrigger","disclosureContentRoot","disclosureContentInner","disclosureContent","lineItemsRoot","lineItemsDivider","lineItemsGroup","lineItemsTitle","lineItemsTitleDescription","lineItemsDescription","lineItemsDescriptionInner","lineItemsDescriptionSuffix","lineItemsDescriptionPrefix","lineItemsDescriptionText","lineItemsCopyButton","lineItemsDowngradeNotice","actionCard","logoBox","logoImage","header","headerTitle","headerSubtitle","headerBackLink","main","footer","footerAction","footerActionText","footerActionLink","footerPages","footerPagesLink","backRow","backLink","socialButtonsRoot","socialButtons","socialButtonsIconButton","socialButtonsBlockButton","socialButtonsBlockButtonText","socialButtonsProviderIcon","socialButtonsProviderInitialIcon","enterpriseButtonsProviderIcon","providerIcon","providerInitialIcon","alternativeMethods","alternativeMethodsBlockButton","alternativeMethodsBlockButtonText","alternativeMethodsBlockButtonArrow","checkoutFormLineItemsRoot","checkoutFormElementsRoot","checkoutSuccessRoot","checkoutSuccessRings","checkoutSuccessBadge","checkoutSuccessTitle","checkoutSuccessDescription","otpCodeField","otpCodeFieldInputs","otpCodeFieldInput","otpCodeFieldErrorText","formResendCodeLink","dividerRow","dividerColumn","dividerText","dividerLine","drawerBackdrop","drawerRoot","drawerContent","drawerHeader","drawerTitle","drawerBody","drawerFooter","drawerFooterTitle","drawerFooterDescription","drawerClose","drawerConfirmationBackdrop","drawerConfirmationRoot","drawerConfirmationTitle","drawerConfirmationDescription","drawerConfirmationActions","formHeader","formHeaderTitle","formHeaderSubtitle","verificationLinkStatusBox","verificationLinkStatusIconBox","verificationLinkStatusIcon","verificationLinkStatusText","form","formContainer","formFieldRow","formField","formFieldLabelRow","formFieldLabel","formFieldRadioGroup","formFieldRadioGroupItem","formFieldRadioInput","formFieldRadioLabel","formFieldRadioLabelTitle","formFieldRadioLabelDescription","formFieldCheckboxInput","formFieldCheckboxLabel","formFieldAction","formFieldInput","formFieldErrorText","formFieldWarningText","formFieldSuccessText","formFieldInfoText","formFieldHintText","formButtonPrimary","formButtonReset","formFieldInputGroup","formFieldInputShowPasswordButton","formFieldInputShowPasswordIcon","formFieldInputCopyToClipboardButton","formFieldInputCopyToClipboardIcon","phoneInputBox","formInputGroup","avatarBox","avatarImage","avatarImageActions","avatarImageActionsUpload","avatarImageActionsRemove","userButtonBox","userButtonOuterIdentifier","userButtonTrigger","userButtonAvatarBox","userButtonAvatarImage","userButtonPopoverRootBox","userButtonPopoverCard","userButtonPopoverMain","userButtonPopoverActions","userButtonPopoverActionButton","userButtonPopoverActionButtonIconBox","userButtonPopoverActionButtonIcon","userButtonPopoverCustomItemButton","userButtonPopoverCustomItemButtonIconBox","userButtonPopoverActionItemButtonIcon","userButtonPopoverFooter","userButtonPopoverFooterPagesLink","organizationSwitcherTrigger","organizationSwitcherTriggerIcon","organizationSwitcherPopoverRootBox","organizationSwitcherPopoverCard","organizationSwitcherPopoverMain","organizationSwitcherPopoverActions","organizationSwitcherPopoverInvitationActions","organizationSwitcherPopoverInvitationActionsBox","organizationSwitcherPopoverActionButton","organizationSwitcherPreviewButton","organizationSwitcherInvitationAcceptButton","organizationSwitcherPopoverActionButtonIconBox","organizationSwitcherPopoverActionButtonIcon","organizationSwitcherPopoverFooter","organizationProfileMembersSearchInputIcon","organizationProfileMembersSearchInput","organizationListPreviewItems","organizationListPreviewItem","organizationListPreviewButton","organizationListPreviewItemActionButton","organizationListCreateOrganizationActionButton","userPreview","userPreviewAvatarContainer","userPreviewAvatarBox","userPreviewAvatarImage","userPreviewAvatarIcon","userPreviewTextContainer","userPreviewMainIdentifier","userPreviewMainIdentifierText","userPreviewSecondaryIdentifier","organizationPreview","organizationPreviewAvatarContainer","organizationPreviewAvatarBox","organizationPreviewAvatarImage","organizationPreviewTextContainer","organizationPreviewMainIdentifier","organizationPreviewSecondaryIdentifier","organizationAvatarUploaderContainer","membersPageInviteButton","identityPreview","identityPreviewText","identityPreviewEditButton","identityPreviewEditButtonIcon","passkeyIcon","accountSwitcherActionButton","accountSwitcherActionButtonIconBox","accountSwitcherActionButtonIcon","pricingTable","pricingTableCard","pricingTableCardHeader","pricingTableCardTitleContainer","pricingTableCardTitle","pricingTableCardDescription","pricingTableCardBody","pricingTableCardStatusRow","pricingTableCardStatus","pricingTableCardFeatures","pricingTableCardFeaturesList","pricingTableCardFeaturesListItem","pricingTableCardFeaturesListItemContent","pricingTableCardFeaturesListItemTitle","pricingTableCardPeriodToggle","pricingTableCardFeeContainer","pricingTableCardFee","pricingTableCardFeePeriod","pricingTableCardFeePeriodNotice","pricingTableCardFooter","pricingTableCardFooterButton","pricingTableCardFooterNotice","pricingTableMatrix","pricingTableMatrixTable","pricingTableMatrixRowGroup","pricingTableMatrixRowGroupHeader","pricingTableMatrixRowGroupBody","pricingTableMatrixRow","pricingTableMatrixRowHeader","pricingTableMatrixRowBody","pricingTableMatrixColumnHeader","pricingTableMatrixCell","pricingTableMatrixCellFooter","pricingTableMatrixAvatar","pricingTableMatrixBadge","pricingTableMatrixPlanName","pricingTableMatrixFee","pricingTableMatrixFeePeriod","pricingTableMatrixFeePeriodNotice","pricingTableMatrixFeePeriodNoticeInner","pricingTableMatrixFeePeriodNoticeLabel","pricingTableMatrixFooter","planDetailHeader","planDetailAvatar","planDetailBadgeAvatarTitleDescriptionContainer","planDetailBadgeContainer","planDetailBadge","planDetailTitle","planDetailTitleDescriptionContainer","planDetailDescription","planDetailAction","planDetailFeeContainer","planDetailFee","planDetailFeePeriod","planDetailFeePeriodNotice","planDetailFeePeriodNoticeInner","planDetailFeePeriodNoticeLabel","planDetailCaption","planDetailFeatures","planDetailFeaturesList","planDetailFeaturesListItem","planDetailFeaturesListItemContent","planDetailFeaturesListItemTitle","planDetailFeaturesListItemDescription","planDetailPeriodToggle","segmentedControlRoot","segmentedControlButton","switchRoot","switchIndicator","switchThumb","switchLabel","alert","alertIcon","alertText","alertTextContainer","tagInputContainer","tagPillIcon","tagPillContainer","tabPanel","tabButton","tabListContainer","tableHead","paginationButton","paginationButtonIcon","paginationRowText","selectButton","selectSearchInput","selectButtonIcon","selectOptionsContainer","selectOption","paymentSourceRow","paymentSourceRowIcon","paymentSourceRowText","paymentSourceRowType","paymentSourceRowValue","paymentSourceRowBadge","statementRoot","statementHeader","statementHeaderTitleContainer","statementHeaderTitle","statementHeaderBadge","statementBody","statementSection","statementSectionHeader","statementSectionHeaderTitle","statementSectionContent","statementSectionContentItem","statementSectionContentDetailsList","statementSectionContentDetailsListItem","statementSectionContentDetailsListItemLabelContainer","statementSectionContentDetailsListItemLabel","statementSectionContentDetailsListItemValue","statementSectionContentDetailsHeader","statementSectionContentDetailsHeaderItem","statementSectionContentDetailsHeaderItemIcon","statementSectionContentDetailsHeaderTitle","statementSectionContentDetailsHeaderDescription","statementSectionContentDetailsHeaderSecondaryTitle","statementSectionContentDetailsHeaderSecondaryDescription","statementFooter","statementFooterLabel","statementFooterValueContainer","statementFooterCurrency","statementFooterValue","statementCopyButton","menuButton","menuButtonEllipsis","menuList","menuItem","paymentAttemptRoot","paymentAttemptHeader","paymentAttemptHeaderTitleContainer","paymentAttemptHeaderTitle","paymentAttemptHeaderBadge","paymentAttemptBody","paymentAttemptFooter","paymentAttemptFooterLabel","paymentAttemptFooterValueContainer","paymentAttemptFooterCurrency","paymentAttemptFooterValue","paymentAttemptCopyButton","modalBackdrop","modalContent","modalCloseButton","profileSection","profileSectionItemList","profileSectionItem","profileSectionHeader","profileSectionTitle","profileSectionTitleText","profileSectionSubtitle","profileSectionSubtitleText","profileSectionContent","profileSectionPrimaryButton","profilePage","formattedPhoneNumber","formattedPhoneNumberFlag","formattedPhoneNumberText","formattedDate","scrollBox","navbar","navbarButtons","navbarButton","navbarButtonIcon","navbarButtonText","navbarMobileMenuRow","navbarMobileMenuButton","navbarMobileMenuButtonIcon","pageScrollBox","page","activeDevice","activeDeviceListItem","activeDeviceIcon","impersonationFab","impersonationFabIcon","impersonationFabIconContainer","impersonationFabTitle","impersonationFabActionLink","invitationsSentIconBox","invitationsSentIcon","qrCodeRow","qrCodeContainer","tooltip","tooltipContent","tooltipText","badge","notificationBadge","buttonArrowIcon","spinner"]).map(e=>e.replace(/-./g,e=>e[1].toUpperCase())),l=e=>"cl-"+e,s=e=>e.replace(/([-][a-z])/,e=>e[1].toUpperCase()),c=e=>{let t=s(e);return{objectKey:t,targettableClassname:l(e),getTargettableIdClassname:t=>l(e)+"__"+t.id,getObjectKeyWithState:e=>t+"__"+e,getObjectKeyWithId:e=>t+"__"+e.id,getObjectKeyWithIdAndState:(e,r)=>t+"__"+e.id+"__"+r,setId:e=>e?{id:e,__type:"id"}:void 0}},d=((e=i)=>{let t=e.map(e=>[s(e),c(e)]);return(0,a.sq)(t)})();var u=r(9109),p=r(9144),m=r(3799),h=r(5100),g=r(8345);let f="0px 0px 0px 1px",b=e=>`${f} ${e}, 0px 1px 1px 0px rgba(255, 255, 255, 0.07) inset, 0px 2px 3px 0px rgba(34, 42, 53, 0.20), 0px 1px 1px 0px rgba(0, 0, 0, 0.24)`,v=e=>`${f} ${e}, 0px 2px 3px -1px rgba(0, 0, 0, 0.08), 0px 1px 0px 0px rgba(0, 0, 0, 0.02)`,y=e=>({borderWidth:0,boxShadow:`${e.shadows.$cardContentShadow}, ${f} ${e.colors.$neutralAlpha50}`}),w=(e,t)=>{let r=[`0px 0px 0px 1px ${t.idle1}`,e.shadows.$input.replace("{{color}}",t.idle2)].toString(),n=[`0px 0px 0px 1px ${t.hover1}`,e.shadows.$input.replace("{{color}}",t.hover2)].toString();return{boxShadow:r,"&:hover":{boxShadow:n},"&:focus-within":{boxShadow:[n,e.shadows.$focusRing.replace("{{color}}",t.focus)].toString()}}},x=(e,t)=>{let r=[`0px 0px 0px 1px ${t.idle1}`,e.shadows.$input.replace("{{color}}",t.idle2)].toString(),n=[`0px 0px 0px 1px ${t.hover1}`,e.shadows.$input.replace("{{color}}",t.hover2)].toString();return{boxShadow:r,"&:hover":{boxShadow:n},"&:focus-visible":{boxShadow:[n,e.shadows.$focusRing.replace("{{color}}",t.focus)].toString()}}},S=e=>({borderWidth:0,...w(e,{idle1:e.colors.$neutralAlpha150,idle2:e.colors.$neutralAlpha100,hover1:e.colors.$neutralAlpha300,hover2:e.colors.$neutralAlpha150,focus:e.colors.$neutralAlpha150}),'&[data-feedback="error"]':w(e,{idle1:e.colors.$dangerAlpha400,idle2:e.colors.$dangerAlpha200,hover1:e.colors.$dangerAlpha500,hover2:e.colors.$dangerAlpha200,focus:e.colors.$dangerAlpha200}),'&[data-feedback="warning"]':w(e,{idle1:e.colors.$warningAlpha400,idle2:e.colors.$warningAlpha200,hover1:e.colors.$warningAlpha500,hover2:e.colors.$warningAlpha200,focus:e.colors.$warningAlpha200}),'&[data-feedback="success"]':w(e,{idle1:e.colors.$successAlpha400,idle2:e.colors.$successAlpha200,hover1:e.colors.$successAlpha500,hover2:e.colors.$successAlpha200,focus:e.colors.$successAlpha200})}),$={elements:({theme:e})=>({button:{'&[data-variant="solid"]':{borderWidth:"0px","&:after":{position:"absolute",content:'""',borderRadius:"inherit",zIndex:-1,inset:0,opacity:1,transitionProperty:e.transitionProperty.$common,transitionDuration:e.transitionDuration.$controls,background:`linear-gradient(180deg, ${e.colors.$whiteAlpha150} 0%, ${e.colors.$transparent} 100%)`},"&:hover::after":{opacity:0},"&:active::after":{opacity:1},'&[data-color="primary"]':{boxShadow:b(e.colors.$primary500),"&:focus":{boxShadow:[b(e.colors.$primary500),e.shadows.$focusRing.replace("{{color}}",e.colors.$neutralAlpha200)].toString()}},'&[data-color="danger"]':{boxShadow:b(e.colors.$danger500),"&:focus":{boxShadow:[b(e.colors.$danger500),e.shadows.$focusRing.replace("{{color}}",e.colors.$dangerAlpha200)].toString()}}},'&[data-variant="outline"]':{borderWidth:0,boxShadow:v(e.colors.$neutralAlpha100),"&:focus":{boxShadow:[v(e.colors.$neutralAlpha100),e.shadows.$focusRing.replace("{{color}}",e.colors.$neutralAlpha200)].toString()}},'&[data-variant="bordered"]':{borderWidth:0,boxShadow:v(e.colors.$neutralAlpha100),"&:focus":{boxShadow:[v(e.colors.$neutralAlpha100),e.shadows.$focusRing.replace("{{color}}",e.colors.$neutralAlpha200)].toString()}}},badge:{borderWidth:0,margin:"1px",'&[data-color="primary"]':{boxShadow:`${f} ${e.colors.$neutralAlpha150}, ${e.shadows.$badge}`},'&[data-color="danger"]':{boxShadow:`${f} ${e.colors.$dangerAlpha300}, ${e.shadows.$badge}`},'&[data-color="success"]':{boxShadow:`${f} ${e.colors.$successAlpha300}, ${e.shadows.$badge}`},'&[data-color="warning"]':{boxShadow:`${f} ${e.colors.$warningAlpha300}, ${e.shadows.$badge}`}},input:{'&[data-variant="default"]':{...S(e)}},checkbox:{...x(e,{idle1:e.colors.$neutralAlpha150,idle2:e.colors.$neutralAlpha100,hover1:e.colors.$neutralAlpha300,hover2:e.colors.$neutralAlpha150,focus:e.colors.$neutralAlpha150}),padding:e.space.$1,width:e.sizes.$3x5,height:e.sizes.$3x5,appearance:"none",borderRadius:e.radii.$sm,border:"none","&:checked":{backgroundImage:`url("data:image/svg+xml,%3Csvg width='16' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M4.25 8L6.5 9.75L9.75 4.25' stroke='${e.colors.$whiteAlpha900}' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3C/path%3E%3C/svg%3E")`,borderColor:e.colors.$transparent,backgroundColor:e.colors.$primary900,backgroundSize:"100% 100%",backgroundPosition:"center",backgroundRepeat:"no-repeat"}},tagInputContainer:{...S(e)},tagPillContainer:{borderWidth:0,boxShadow:`${f} ${e.colors.$neutralAlpha150}, ${e.shadows.$badge}`},phoneInputBox:{...S(e)},formInputGroup:{...S(e)},selectSearchInput__countryCode:{boxShadow:"none","&:focus":{boxShadow:"none"}},cardBox:{borderWidth:0,boxShadow:`${e.shadows.$cardBoxShadow}, ${f} ${e.colors.$neutralAlpha100}`},drawerContent:{borderWidth:0,boxShadow:`${e.shadows.$cardBoxShadow}, ${f} ${e.colors.$neutralAlpha100}`},popoverBox:{borderWidth:0,boxShadow:`${e.shadows.$cardBoxShadow}, ${f} ${e.colors.$neutralAlpha100}`},card:{...y(e)},pricingTableCard:{'&[data-variant="default"]':{borderWidth:0,boxShadow:`${e.shadows.$cardBoxShadow}, ${f} ${e.colors.$neutralAlpha100}`},'&[data-variant="compact"]':{borderWidth:0,boxShadow:`0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 1px 2px 0px rgba(25, 28, 33, 0.12), 0px 0px 0px 1px ${e.colors.$neutralAlpha100}`}},scrollBox:{...y(e)},userButtonPopoverMain:{...y(e)},organizationSwitcherPopoverMain:{...y(e)},menuList:{borderWidth:0,boxShadow:`${e.shadows.$menuShadow}, ${f} ${e.colors.$neutralAlpha100}`},actionCard:{borderWidth:0,boxShadow:`${e.shadows.$actionCardShadow}, ${f} ${e.colors.$neutralAlpha100}`},table:{borderWidth:0,boxShadow:`0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 1px 2px 0px rgba(25, 28, 33, 0.12), 0px 0px 0px 1px ${e.colors.$neutralAlpha100}`}})};var C=r(2635);let _=e=>{let t=e.variables||{},r=(0,a.QD)(t.colorPrimary,"primary"),n=(0,a.Ii)(r?.primary500,"primaryAlpha"),o=(0,a.QD)(t.colorDanger,"danger"),i=(0,a.Ii)(o?.danger500,"dangerAlpha"),l=(0,a.QD)(t.colorSuccess,"success"),s=(0,a.Ii)(l?.success500,"successAlpha"),c=(0,a.QD)(t.colorWarning,"warning"),d=(0,a.Ii)(c?.warning500,"warningAlpha");return(0,a.Yb)({...r,...n,...o,...i,...l,...s,...c,...d,...(0,a.Ii)(t.colorNeutral,"neutralAlpha"),primaryHover:a.O9.adjustForLightness(r?.primary500),colorTextOnPrimaryBackground:k(t.colorTextOnPrimaryBackground),colorText:k(t.colorText),colorTextSecondary:k(t.colorTextSecondary)||a.O9.makeTransparent(t.colorText,.35),colorInputText:k(t.colorInputText),colorBackground:k(t.colorBackground),colorInputBackground:k(t.colorInputBackground),colorShimmer:k(t.colorShimmer)})},k=e=>e?a.O9.toHslaString(e):void 0,P=e=>{let{borderRadius:t}=e.variables||{};if(void 0===t)return;let r="none"===t?"0":t,{numericValue:n,unit:o="rem"}=B(r);return{sm:(.66*n).toString()+o,md:r,lg:(1.33*n).toString()+o,xl:(2*n).toString()+o}},I=e=>{let{spacingUnit:t}=e.variables||{};if(void 0===t)return;let{numericValue:r,unit:n}=B(t);return(0,a.sq)(C.Mx.map(e=>{let t=Number.parseFloat(e.replace("x","."));return[e,`${t/.5*.125*r}${n}`]}))},A=e=>{let{fontSize:t}=e.variables||{};if(void 0===t)return;let{numericValue:r,unit:n="rem"}=B(t);return{xs:(.8*r).toString()+n,sm:(.9*r).toString()+n,md:t,lg:(1.3*r).toString()+n,xl:(1.85*r).toString()+n}},T=e=>{let{fontWeight:t}=e.variables||{};return(0,a.Yb)({...t})},R=e=>{let{fontFamily:t,fontFamilyButtons:r}=e.variables||{};return(0,a.Yb)({main:t,buttons:r})},B=e=>{let t=Number.parseFloat(e),r=e.replace(t.toString(),"")||void 0;return{numericValue:t,unit:r}},Z={logoPlacement:"inside",socialButtonsPlacement:"top",socialButtonsVariant:"auto",logoImageUrl:"",logoLinkUrl:"",showOptionalFields:!0,helpPageUrl:"",privacyPageUrl:"",termsPageUrl:"",shimmer:!0,animations:!0,unsafe_disableDevelopmentModeWarnings:!1},z={theme:"auto",size:"normal",language:""},D=e=>{let{globalAppearance:t,appearance:r,appearanceKey:n}=e,o=[];[t,t?.[n],r].forEach(e=>L(e,o));let a=M(o),i=F(o),l=O(o);return o.find(e=>!!e.simpleStyles)||o.unshift($),{parsedElements:E(o.map(e=>{if(!e.elements||"function"!=typeof e.elements)return e;let t={...e};return t.elements=e.elements({theme:a}),t})),parsedInternalTheme:a,parsedLayout:i,parsedCaptcha:l}},L=(e,t)=>{e&&((Array.isArray(e.baseTheme)?e.baseTheme:[e.baseTheme]).forEach(e=>L(e,t)),t.push(e))},E=e=>e.map(e=>({...e?.elements})),F=e=>({...Z,...e.reduce((e,t)=>({...e,...t.layout}),{})}),O=e=>({...z,...e.reduce((e,t)=>({...e,...t.captcha}),{})}),M=e=>{let t={};return(0,h.EB)({...g.$4},t),e.forEach(e=>{(0,h.EB)(U(e),t)}),t},U=e=>{if(!e)return{};let t={..._(e)},r={...P(e)},n={...I(e)},o={...A(e)},a={...T(e)},i={...R(e)};return(0,g.cS)({colors:t,radii:r,space:n,fontSizes:o,fontWeights:a,fonts:i})},[j,V]=(0,m.uH)("AppearanceContext"),W=e=>{let t=(0,m.I7)(()=>({value:D(e)}),[e.appearance,e.globalAppearance]);return(0,u.tZ)(j.Provider,{value:t,children:e.children})},H=Object.freeze({loading:" cl-loading",error:" cl-error",open:" cl-open",active:" cl-active"}),N=Object.freeze({loading:2,error:2,open:2,active:2}),K=(e,t,r,n)=>{let o=et(n),a="",i=[];return a=ee(a=G(a=q(a=Y(a=X(a,t),t,r),o),n),e,t,r,o),J(i,e,t,r,o),{className:a,css:i}},X=(e,t)=>{for(let r=t.length-1;r>=0;r--)e+=t[r].targettableClassname+" ";return e.trimEnd()},Y=(e,t,r)=>{if(!r)return e;for(let n=t.length-1;n>=0;n--)e=e+" "+t[n].getTargettableIdClassname(r);return e},q=(e,t)=>t?e+H[t]:e,G=(e,t)=>t&&t.isRequired?e+" cl-required":e,Q=(e,t)=>e+(t?" "+t:"")+" \uD83D\uDD12️",J=(e,t,r,n,o)=>{for(let a=0;a<r.length;a++)for(let i=0;i<t.length;i++)el(e,t[i],r[a],n,o)},ee=(e,t,r,n,o)=>{for(let a=0;a<r.length;a++)for(let i=0;i<t.length;i++)e=ei(e,t[i],r[a],n,o);return e},et=e=>e?e.isLoading?"loading":e.hasError?"error":e.isOpen?"open":e.isActive?"active":void 0:void 0,er=(e,t)=>"string"==typeof t?e+" "+t:e,en=(e,t,r=0)=>{r?t&&"object"==typeof t&&e.push({["&".repeat(r)]:t}):t&&"object"==typeof t&&e.push(t)},eo=e=>"cl-"+e.flow+"-root",ea=e=>e.part?"cl-"+e.flow+"-"+e.part:"",ei=(e,t,r,n,o)=>(t&&(e=er(e,t[r.objectKey]),n&&(e=er(e,t[r.getObjectKeyWithId(n)])),o&&(e=er(e,t[r.getObjectKeyWithState(o)])),n&&o&&(e=er(e,t[r.getObjectKeyWithIdAndState(n,o)]))),e),el=(e,t,r,n,o)=>{t&&(en(e,t[r.objectKey]),n&&en(e,t[r.getObjectKeyWithId(n)]),o&&en(e,t[r.getObjectKeyWithState(o)],N[o]),n&&o&&en(e,t[r.getObjectKeyWithIdAndState(n,o)],N[o]))},es=(e,t)=>{let{defaultStyles:r,defaultDescriptor:n}=t||{},o=p.forwardRef((t,o)=>{let{elementDescriptor:a,elementId:i,sx:l,className:s,...c}=t,{parsedElements:d}=V(),p=[n,...Array.isArray(a)?a:[a]].filter(e=>e);if(!p.length)return(0,u.tZ)(e,{css:l,...c,className:s,ref:o});let m=K(d,p,i,t),h=Q(m.className,s);return m.css.unshift(r,l),(0,u.tZ)(e,{elementId:i,css:m.css,className:h,...c,ref:o})}),a=e.displayName||e.name||"Component";return o.displayName=`Customizable${a}`.replace("_",""),o};var ec=r(6917);let ed=["https://img.clerk.com/","https://img.clerkstage.dev/","https://img.lclclerk.com/"],eu=e=>!!ed.some(t=>e?.includes(t)),ep=({src:e,width:t,xDescriptors:r})=>e?r.map(r=>`${em({src:e,width:t*r})} ${r}x`).toString():"",em=({src:e,width:t})=>{if(!(0,ec.jv)(e)||(0,ec.pU)(e))return e;let r=new URL(e);return t&&r.searchParams.append("width",t?.toString()),r.href},eh=e=>p.forwardRef((t,r)=>{let{elementId:n,elementDescriptor:o,localizationKey:a,...i}=t;return(0,u.tZ)(e,{...i,ref:r})});var eg=r(2672);let ef=es(p.memo(e=>{let[t,r]=p.useState(!0),n=p.useRef(null);return p.useEffect(()=>{let o=n.current;o&&(t&&r(!1),o.className=e.className)},[e.className]),(0,u.BX)(u.HY,{children:[e.children,t&&(0,u.tZ)("span",{ref:e=>n.current=e?e.parentElement:n.current,"aria-hidden":!0,style:{display:"none"}})]})}),{defaultStyles:e=>({boxSizing:"border-box",width:"fit-content",fontFamily:e.fonts.$main,fontStyle:e.fontStyles.$normal})});var eb=r(1201);let ev={Root:e=>(0,u.tZ)(eg.FlowMetadataProvider,{flow:e.flow,children:(0,u.tZ)(eb.InternalThemeProvider,{children:(0,u.tZ)(ef,{elementDescriptor:d.rootBox,className:eo(e),...e})})}),Part:e=>{let{flow:t}=(0,eg.useFlowMetadata)();return(0,u.tZ)(eg.FlowMetadataProvider,{flow:t,part:e.part,children:(0,u.tZ)(eb.InternalThemeProvider,{children:e.children})})}},ey=es(eh(o.xu)),ew=es(eh(o.kC)),ex=es(eh(o.JX)),eS=es(eh(o.rj)),e$=es((0,n.W5)(eh(o.zx)),{defaultDescriptor:d.button}),eC=es((0,n.W5)(eh(o.rF)),{defaultDescriptor:d.button}),e_=es((0,n.W5)(eh(o.X6))),ek=es((0,n.W5)(eh(o.rU))),eP=es((0,n.W5)(eh(o.xv))),eI=es(eh((e=>{let t=p.forwardRef((t,r)=>{let{src:n,size:o=80,xDescriptors:a=[1,2],...i}=t,l=eu(n);return(0,u.tZ)(e,{srcSet:l?ep({src:n,width:o,xDescriptors:a}):void 0,src:l?em({src:n,width:2*o}):n,...i,ref:r})}),r=e.displayName||e.name||"Component";return t.displayName=`Responsive${r}`.replace("_",""),t})(o.Ee))),eA=es(eh(o.bZ)),eT=es(eh(o.zM)),eR=es(eh(o.II),{defaultDescriptor:d.input}),eB=es(eh(o.I0),{defaultDescriptor:d.checkbox}),eZ=es(eh(o.NA),{defaultDescriptor:d.radio}),ez=es((0,n.W5)(eh(o.lX))),eD=es((0,n.W5)(eh(o.jo))),eL=es((0,n.W5)(eh(o.Xc))),eE=es((0,n.W5)(eh(o.ZD))),eF=es((0,n.W5)(eh(o.Zh))),eO=es(eh(o.l0)),eM=es(eh(o.JO)),eU=es(eh(o.$j)),ej=es((0,n.W5)(eh(o.Ct)),{defaultDescriptor:d.badge}),eV=es((0,n.W5)(eh(o.Cc)),{defaultDescriptor:d.notificationBadge}),eW=es(eh(o.iA),{defaultDescriptor:d.table}),eH=es(eh(o.hr)),eN=es(eh(o.p3)),eK=es(eh(o.Tr)),eX=es((0,n.W5)(eh(o.Th))),eY=es((0,n.W5)(eh(o.Td))),eq=es((0,n.W5)(eh(o.Dl))),eG=es((0,n.W5)(eh(o.Dd))),eQ=es((0,n.W5)(eh(o.Dt))),eJ=es((0,n.W5)(eh(o.Dr))),e0=es(eh(o.Hr))},4709:function(e,t,r){r.d(t,{FR:()=>d,XC:()=>s});var n=r(9109),o=r(3799),a=r(9144),i=r(6781);let[l,s,c]=(0,o.uH)("ActionContext"),d=e=>{let{animate:t=!0,children:r,value:o,onChange:s}=e,[c,d]=(0,a.useState)(null),u=(0,a.useCallback)(()=>{s?s(null):d(null)},[s]),p=(0,a.useCallback)(e=>{s?s(e):d(e)},[s]),m=(0,n.tZ)(l.Provider,{value:{value:{active:void 0!==o?o:c,open:p,close:u}},children:r});return t?(0,n.tZ)(i.f,{children:m}):m}},9805:function(e,t,r){r.d(t,{a:()=>c});var n=r(9109),o=r(9541);let a=e=>({neutral:{backgroundColor:e.colors.$colorBackground},destructive:{backgroundColor:e.colors.$neutralAlpha50}});var i=r(4709),l=r(9144);let s=l.forwardRef((e,t)=>(0,n.tZ)("div",{ref:t,...e})),c={Root:i.FR,Card:e=>{let{children:t,sx:r,variant:i="neutral",...l}=e;return(0,n.tZ)(o.Col,{elementDescriptor:o.descriptors.actionCard,sx:[e=>({boxShadow:e.shadows.$actionCardShadow,gap:e.space.$4,borderRadius:e.radii.$lg,padding:`${e.space.$4} ${e.space.$5}`,borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha100,...a(e)[i]}),r],...l,children:t})},Trigger:e=>{let{children:t,value:r,hideOnActive:n=!0}=e,{active:o,open:a}=(0,i.XC)(),s=l.Children.only(t);if(!(0,l.isValidElement)(s))throw Error("Children of ActionTrigger must be a valid element");return n&&o===r?null:(0,l.cloneElement)(s,{onClick:async()=>{await s.props.onClick?.(),a(r)}})},Open:({children:e,value:t})=>{let{active:r}=(0,i.XC)(),o=(0,l.useRef)(null);return((0,l.useEffect)(()=>{let e=o.current,n=new MutationObserver(()=>{n.disconnect(),e&&setTimeout(()=>{e.scrollIntoView({behavior:"smooth",block:"center"})},300)});return r===t&&e&&n.observe(e,{childList:!0,subtree:!0,attributes:!0}),()=>n.disconnect()},[r,t]),r!==t)?null:(0,n.tZ)(s,{ref:o,children:e})},Closed:e=>{let{children:t,value:r}=e,n=Array.isArray(r)?r:[r],{active:o}=(0,i.XC)();return o&&n.includes(o)?null:t}}},2305:function(e,t,r){r.d(t,{U8:()=>c,aU:()=>d,eX:()=>l,r5:()=>s});var n=r(9109);r(9144);var o=r(9541),a=r(2672),i=r(2464);let l=e=>{let{sx:t,...r}=e;return(0,n.tZ)(o.Col,{sx:[e=>({"> button,div":{border:`0 solid ${e.colors.$neutralAlpha100}`},">:not([hidden],:empty)~:not([hidden],:empty)":{borderTopWidth:"1px",borderBottomWidth:"0"}}),t],...r})},s=e=>(0,n.tZ)(o.Col,{...e}),c=e=>{let{sx:t,iconBoxSx:r,iconSx:o,...a}=e;return(0,n.tZ)(d,{size:"xs",variant:"outline",textVariant:"buttonSmall",sx:[e=>({borderRadius:e.radii.$lg,gap:e.space.$0x5,justifyContent:"center",flex:"1 1 0",padding:`${e.space.$1} ${e.space.$1x5}`}),t],iconSx:[e=>({width:e.sizes.$4,height:e.sizes.$4}),o],iconBoxSx:[{flex:"unset"},r],...a})},d=e=>{let t=(0,a.useCardState)(),r=(0,i._m)(),{t:l}=(0,o.useLocalizations)(),{icon:s,label:c,onClick:d,iconElementDescriptor:u,sx:p,iconElementId:m,iconSx:h,iconBoxElementDescriptor:g,iconBoxElementId:f,iconBoxSx:b,trailing:v,spinnerSize:y,...w}=e,x=async e=>{t.setLoading(),r.setLoading();try{await d?.(e)}finally{t.setIdle(),r.setIdle()}};return(0,n.BX)(o.Button,{size:"md",variant:"ghost",colorScheme:"neutral",textVariant:"buttonLarge",hoverAsFocus:!0,focusRing:!1,sx:[e=>({flex:"1",borderRadius:0,gap:e.space.$4,justifyContent:"flex-start"}),p],isDisabled:t.isLoading,onClick:x,role:"menuitem",...w,children:[(0,n.tZ)(o.Flex,{elementDescriptor:g,elementId:f,justify:"center",as:"span",sx:[e=>({flex:`0 0 ${e.sizes.$9}`,gap:e.space.$2,alignItems:"center"}),b],children:r.isLoading?(0,n.tZ)(o.Spinner,{size:y||"xs",elementDescriptor:o.descriptors.spinner,sx:e=>({marginRight:e.space.$1})}):(0,n.tZ)(o.Icon,{elementDescriptor:u,elementId:m,icon:s,sx:[e=>({width:e.sizes.$4,height:"auto",maxWidth:"100%"}),h]})}),c?l(c):null,v]})}},9117:function(e,t,r){r.d(t,{b:()=>a});var n=r(9109),o=r(9541);let a=e=>{let{children:t,title:r,subtitle:a,variant:i="warning",...l}=e;return t||r||a?(0,n.BX)(o.Alert,{elementDescriptor:o.descriptors.alert,elementId:o.descriptors.alert.setId(i),colorScheme:i,align:"start",...l,sx:[l.sx],children:[(0,n.tZ)(o.AlertIcon,{elementId:o.descriptors.alert.setId(i),elementDescriptor:o.descriptors.alertIcon,variant:i,colorScheme:i,sx:{flexShrink:"0"}}),(0,n.BX)(o.Col,{elementDescriptor:o.descriptors.alertTextContainer,elementId:o.descriptors.alertTextContainer.setId(i),gap:1,children:[(0,n.tZ)(o.Text,{elementDescriptor:o.descriptors.alertText,elementId:o.descriptors.alert.setId(i),colorScheme:"danger"===i?"danger":"warning"===i?"warning":"secondary",variant:"body",localizationKey:r,sx:{textAlign:"left"},children:t}),a&&(0,n.tZ)(o.Text,{elementDescriptor:o.descriptors.alertText,elementId:o.descriptors.alert.setId(i),colorScheme:"danger"===i?"danger":"secondary",variant:"body",localizationKey:a})]})]}):null}},6781:function(e,t,r){r.d(t,{f:()=>l});var n=r(9109),o=r(7627),a=r(9144),i=r(9541);let l=e=>{let{children:t,asChild:r}=e,{animations:l}=(0,i.useAppearance)().parsedLayout,[s]=(0,o.u)();return r?(0,a.cloneElement)(t,{ref:l?s:null}):(0,n.tZ)("div",{ref:l?s:null,children:t})}},3174:function(e,t,r){r.d(t,{u:()=>c});var n=r(9109),o=r(9144),a=r(1576),i=r(9541),l=r(6990);let s=(e,t)=>{let r=16*Number.parseFloat(t.replace("rem",""));if(!e.current)return r;let n=e.current.naturalWidth/e.current.naturalHeight,o=`${r}px`;return n<=1?o=`${2*r}px`:n>1&&n<=2&&(o=`${2*r/n}px`),o},c=e=>{let t=o.useRef(null),[r,c]=o.useState(!1),{logoImageUrl:d,applicationName:u,homeUrl:p}=(0,a.useEnvironment)().displayConfig,{parsedLayout:m}=(0,i.useAppearance)(),h=m.logoImageUrl||d,g=m.logoLinkUrl||p;if(!h)return null;let f=(0,n.tZ)(i.Image,{ref:t,elementDescriptor:i.descriptors.logoImage,alt:u,src:h,size:200,onLoad:()=>c(!0),sx:{display:r?"inline-block":"none",height:"100%",width:"100%",objectFit:"contain"}});return(0,n.tZ)(i.Flex,{elementDescriptor:i.descriptors.logoBox,...e,sx:[e=>({height:s(t,e.sizes.$6),justifyContent:"center"}),e.sx],children:g?(0,n.tZ)(l.r,{sx:{justifyContent:"center"},to:g,children:f}):f})}},8246:function(e,t,r){r.d(t,{$:()=>l});var n=r(9109),o=r(9144),a=r(9541),i=r(4174);let l=o.forwardRef((e,t)=>{let{rightIcon:r=i.LZ,rightIconSx:l,leftIcon:s,leftIconSx:c,leftIconElementId:d,leftIconElementDescriptor:u,isLoading:p,children:m,textElementDescriptor:h,textElementId:g,spinnerElementDescriptor:f,spinnerElementId:b,arrowElementDescriptor:v,arrowElementId:y,textLocalizationKey:w,childrenSx:x,badge:S,textVariant:$="buttonSmall",...C}=e,_=(0,o.isValidElement)(s);return(0,n.BX)(a.SimpleButton,{variant:"outline",block:!0,isLoading:p,...C,ref:t,sx:t=>[{gap:t.space.$1,position:"relative",justifyContent:"center",borderColor:t.colors.$neutralAlpha100,alignItems:"center",padding:`${t.space.$1x5} ${t.space.$3} ${t.space.$1x5} ${t.space.$2x5}`,"--arrow-opacity":"0","--arrow-transform":`translateX(-${t.space.$2});`,"&:hover,&:focus ":{"--arrow-opacity":"0.5","--arrow-transform":"translateX(0px);"}},e.sx],children:[(p||s)&&(0,n.tZ)(a.Flex,{as:"span",sx:e=>({flex:`0 0 ${e.space.$5}`}),children:p?(0,n.tZ)(a.Spinner,{elementDescriptor:f,elementId:b,size:"md"}):!_&&s?(0,n.tZ)(a.Icon,{elementDescriptor:u,elementId:d,icon:s,sx:[e=>({width:e.sizes.$5}),c]}):s}),(0,n.BX)(a.Flex,{gap:2,as:"span",sx:[{overflow:"hidden"},x],children:[(0,n.tZ)(a.Text,{elementDescriptor:h,elementId:g,as:"span",truncate:!0,variant:$,localizationKey:w,children:m}),S]}),(0,n.tZ)(a.Icon,{elementDescriptor:v,elementId:y,icon:r,sx:[e=>({transition:"all 100ms ease",minWidth:e.sizes.$4,minHeight:e.sizes.$4,width:"1em",height:"1em",opacity:"var(--arrow-opacity)",transform:"var(--arrow-transform)"}),l]})]})})},9629:function(e,t,r){r.d(t,{q:()=>l});var n=r(9109),o=r(9144),a=r(9541),i=r(1201);let l=e=>{let{size:t=()=>26,title:r,initials:i,imageUrl:l,rounded:c=!0,imageFetchSize:d=80,sx:u,boxElementDescriptor:p,imageElementDescriptor:m}=e,[h,g]=o.useState(!1),f=i&&(!l||h)?(0,n.tZ)(s,{initials:i}):(0,n.tZ)(a.Image,{elementDescriptor:[m,a.descriptors.avatarImage],title:r,alt:`${r}'s logo`,src:l||"",sx:{objectFit:"cover",width:"100%",height:"100%"},onError:()=>g(!0),size:d});return(0,n.BX)(a.Flex,{as:"span",elementDescriptor:[p,a.descriptors.avatarBox],sx:[e=>({flexShrink:0,borderRadius:c?e.radii.$circle:e.radii.$avatar,overflow:"hidden",width:t(e),height:t(e),backgroundColor:e.colors.$avatarBackground,backgroundClip:"padding-box",position:"relative"}),u],children:[f,(0,n.tZ)(a.Box,{as:"span",sx:e=>({overflow:"hidden",background:e.colors.$colorShimmer,position:"absolute",width:"25%",height:"100%",transition:`all ${e.transitionDuration.$slow} ${e.transitionTiming.$easeOut}`,transform:"var(--cl-shimmer-hover-transform, skewX(-45deg) translateX(-300%))",":after":{display:"block",boxSizing:"border-box",content:"''",position:"absolute",width:"400%",height:"100%",transform:"var(--cl-shimmer-hover-after-transform, skewX(45deg) translateX(75%))",transition:`all ${e.transitionDuration.$slow} ${e.transitionTiming.$easeOut}`,borderWidth:e.borderWidths.$heavy,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$colorShimmer,borderRadius:c?e.radii.$circle:e.radii.$avatar}})})]})},s=e=>{let t=e.initials;return(0,n.tZ)(a.Text,{as:"span",sx:e=>({...i.common.centeredFlex("inline-flex"),width:"100%",color:e.colors.$colorText}),children:t})}},6654:function(e,t,r){r.d(t,{C:()=>m});var n=r(9109),o=r(9144),a=r(9541),i=r(7623),l=r(2672);let s=e=>new Promise((t,r)=>{let n=new FileReader;n.readAsDataURL(e),n.onload=()=>t(n.result),n.onerror=e=>r(e)}),c=Object.freeze(["image/png","image/jpeg","image/gif","image/webp"]),d=e=>c.includes(e.type),u=e=>e.size<=1e7,p=e=>d(e)&&u(e),m=e=>{let[t,r]=o.useState(!1),[d,u]=o.useState(),m=(0,l.useCardState)(),h=o.useRef(null),g=()=>h.current?.click(),{onAvatarChange:f,onAvatarRemove:b,title:v,avatarPreview:y,avatarPreviewPlaceholder:w,...x}=e,S=()=>{r(!t)},$=e=>null===e?u(""):(s(e).then(u),m.setLoading(),f(e).then(()=>{S(),m.setIdle()}).catch(e=>(0,i.S3)(e,[],m.setError))),C=async()=>(m.setLoading(),await $(null),b?.()),_=async e=>{e&&p(e)&&await $(e)},k=d?o.cloneElement(y,{imageUrl:d}):w?o.cloneElement(w,{onClick:g}):y;return(0,n.BX)(a.Col,{gap:4,children:[(0,n.tZ)("input",{type:"file",accept:c.join(","),style:{display:"none"},ref:h,onChange:e=>_(e.currentTarget.files?.[0])}),(0,n.BX)(a.Flex,{gap:4,align:"center",...x,children:[k,(0,n.BX)(a.Col,{gap:1,children:[(0,n.BX)(a.Flex,{elementDescriptor:a.descriptors.avatarImageActions,gap:2,children:[(0,n.tZ)(a.SimpleButton,{elementDescriptor:a.descriptors.avatarImageActionsUpload,localizationKey:(0,a.localizationKeys)("userProfile.profilePage.imageFormSubtitle"),isDisabled:m.isLoading,variant:"outline",size:"xs",onClick:g}),!!b&&!t&&(0,n.tZ)(a.Button,{elementDescriptor:a.descriptors.avatarImageActionsRemove,localizationKey:(0,a.localizationKeys)("userProfile.profilePage.imageFormDestructiveActionSubtitle"),isDisabled:m.isLoading,sx:e=>({color:e.colors.$danger500}),variant:"ghost",colorScheme:"danger",onClick:C})]}),(0,n.tZ)(a.Text,{colorScheme:"secondary",sx:e=>({fontSize:e.fontSizes.$sm}),localizationKey:(0,a.localizationKeys)("userProfile.profilePage.fileDropAreaHint")})]})]})]})}},5482:function(e,t,r){r.d(t,{h:()=>i});var n=r(9109),o=r(9541),a=r(6990);let i=e=>{let{boxElementDescriptor:t,linkElementDescriptor:r,...i}=e;return(0,n.tZ)(o.Flex,{elementDescriptor:t,sx:e=>({margin:`${e.space.$none} auto`}),children:(0,n.tZ)(a.r,{...i,children:(0,n.tZ)(o.Text,{localizationKey:(0,o.localizationKeys)("backButton"),elementDescriptor:r,variant:"body"})})})}},7321:function(e,t,r){r.d(t,{S:()=>l});var n=r(9109),o=r(9144),a=r(3935),i=r(9541);let l=()=>{let e=(0,o.useRef)(null),t=(0,o.useRef)("0"),r=(0,o.useRef)("unset"),l=(0,o.useRef)("unset"),{parsedCaptcha:s}=(0,i.useAppearance)(),{locale:c}=(0,i.useLocalizations)(),d=s?.theme,u=s?.size,p=s?.language||c?.toLowerCase();return(0,o.useEffect)(()=>{if(!e.current)return;let n=new MutationObserver(n=>{n.forEach(n=>{let o=n.target;"attributes"===n.type&&"style"===n.attributeName&&e.current&&(t.current=o.style.maxHeight||"0",r.current=o.style.minHeight||"unset",l.current=o.style.marginBottom||"unset")})});return n.observe(e.current,{attributes:!0,attributeFilter:["style"]}),()=>n.disconnect()},[]),(0,n.tZ)(i.Box,{ref:e,id:a.M,style:{display:"block",alignSelf:"center",maxHeight:t.current,minHeight:r.current,marginBottom:l.current},"data-cl-theme":d,"data-cl-size":u,"data-cl-language":p})}},4455:function(e,t,r){r.d(t,{Z:()=>_});var n=r(9109),o=r(9144),a=r(9541),i=r(6990),l=r(1201),s=r(9117);let c=o.memo(e=>(0,n.tZ)(s.b,{variant:"danger",sx:e=>({animation:`${l.animations.textInBig} ${e.transitionDuration.$slow}`}),...e}));var d=r(1576),u=r(143),p=r(4174),m=r(7434);let h=o.memo(o.forwardRef((e,t)=>{let{sx:r,outerSx:o,withFooterPages:i=!1,withDevOverlay:l=!1,devModeNoticeSx:s,...c}=e,{displayConfig:p}=(0,d.useEnvironment)(),{showDevModeNotice:h}=(0,u.n)();return p.branded||i||h?(0,n.BX)(a.Box,{sx:[{width:"100%",position:"relative",isolation:"isolate"},o],children:[l&&(0,n.tZ)(m.W,{gradient:0}),(0,n.BX)(a.Col,{sx:e=>({gap:p.branded||i?e.space.$2:0,marginLeft:"auto",marginRight:"auto",width:"100%",justifyContent:"center",alignItems:"center",zIndex:1,position:"relative"}),children:[(p.branded||i)&&(0,n.BX)(a.Flex,{sx:[{":has(div:only-child)":{justifyContent:"center"},justifyContent:"space-between",width:"100%"},r],...c,ref:t,children:[p.branded&&(0,n.tZ)(a.Flex,{gap:1,align:"center",justify:"center",sx:e=>({color:e.colors.$colorTextSecondary}),children:(0,n.BX)(n.HY,{children:[(0,n.tZ)(a.Text,{variant:"buttonSmall",children:"Secured by"}),(0,n.tZ)(g,{})]})}),i&&(0,n.tZ)(_.FooterLinks,{})]}),(0,n.tZ)(m.Y,{sx:s})]})]}):null})),g=()=>(0,n.tZ)(a.Link,{href:"https://go.clerk.com/components",colorScheme:"inherit",isExternal:!0,"aria-label":"Clerk logo",children:(0,n.tZ)(a.Icon,{icon:p.cN,sx:e=>({width:e.sizes.$12,height:e.sizes.$3x5})})});var f=r(2672),b=r(3412),v=r(6459);let y=o.forwardRef((e,t)=>{let{children:r,sx:o,...i}=e,l=(0,f.useFlowMetadata)(),{toggle:s}=(0,v.useUnsafeModalContext)(),{maintenanceMode:u}=(0,d.useEnvironment)(),m=(0,f.useCardState)(),{t:h}=(0,a.useLocalizations)();return(0,n.BX)(a.Flex,{direction:"col",className:(0,a.generateFlowPartClassname)(l),elementDescriptor:a.descriptors.card,sx:[e=>({backgroundColor:e.colors.$colorBackground,transitionProperty:e.transitionProperty.$common,transitionDuration:"200ms",textAlign:"center",zIndex:e.zIndices.$card,borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha50,boxShadow:e.shadows.$cardContentShadow,borderRadius:e.radii.$lg,position:"relative",padding:`${e.space.$8} ${e.space.$10}`,justifyContent:"center",alignContent:"center"}),o],gap:8,ref:t,...i,children:[s&&(0,n.tZ)(b.h,{elementDescriptor:a.descriptors.modalCloseButton,variant:"ghost","aria-label":"Close modal",onClick:s,icon:(0,n.tZ)(a.Icon,{icon:p.x8,size:"xs"}),sx:e=>({color:e.colors.$colorTextSecondary,zIndex:e.zIndices.$modal,position:"absolute",top:e.space.$2,right:e.space.$2,padding:e.space.$3})}),u&&!m.error&&(0,n.tZ)(c,{variant:"warning",children:h((0,a.localizationKeys)("maintenanceMode"))}),r]})});var w=r(7623);let x=o.forwardRef((e,t)=>{let{children:r,isProfileFooter:o=!1,sx:i,...s}=e,{displayConfig:c}=(0,d.useEnvironment)(),{branded:p}=c,{showDevModeNotice:m}=(0,u.n)(),{helpPageUrl:h,privacyPageUrl:g,termsPageUrl:f}=(0,a.useAppearance)().parsedLayout,b=o?p:!!(p||h||g||f);return r||b||m?(0,n.BX)(a.Flex,{direction:"col",align:"center",justify:"center",elementDescriptor:a.descriptors.footer,sx:[e=>({marginTop:`-${e.space.$2}`,paddingTop:e.space.$2,background:l.common.mergedColorsBackground(w.O9.setAlpha(e.colors.$colorBackground,1),e.colors.$neutralAlpha50),"&:empty":{padding:0,marginTop:0}}),!o&&(e=>({">:first-of-type":{padding:`${e.space.$4} ${e.space.$8} ${e.space.$4} ${e.space.$8}`},">:not(:first-of-type)":{padding:`${e.space.$4} ${e.space.$8}`,borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100}})),i],...s,ref:t,children:[r,(0,n.tZ)(_.ClerkAndPagesTag,{withFooterPages:b&&!o,devModeNoticeSx:e=>({padding:e.space.$none}),outerSx:o?e=>({padding:`${e.space.$4} ${e.space.$8}`}):void 0,withDevOverlay:!0})]}):null}),S=e=>(0,n.tZ)(a.Link,{elementDescriptor:a.descriptors.footerPagesLink,...e,colorScheme:"neutral",variant:"buttonSmall"}),$=o.memo(()=>{let{helpPageUrl:e,privacyPageUrl:t,termsPageUrl:r}=(0,a.useAppearance)().parsedLayout;return e||t||r?(0,n.BX)(a.Flex,{elementDescriptor:a.descriptors.footerPages,justify:"between",sx:e=>({gap:e.space.$3,[l.mqu.sm]:{gap:e.space.$2}}),children:[e&&(0,n.tZ)(S,{localizationKey:(0,a.localizationKeys)("footerPageLink__help"),elementId:a.descriptors.footerPagesLink.setId("help"),isExternal:!0,href:e}),t&&(0,n.tZ)(S,{localizationKey:(0,a.localizationKeys)("footerPageLink__privacy"),elementId:a.descriptors.footerPagesLink.setId("privacy"),isExternal:!0,href:t}),r&&(0,n.tZ)(S,{localizationKey:(0,a.localizationKeys)("footerPageLink__terms"),elementId:a.descriptors.footerPagesLink.setId("terms"),isExternal:!0,href:r})]}):null});var C=r(3174);let _={Root:o.forwardRef((e,t)=>{let{sx:r,children:o,...i}=e,s=(0,a.useAppearance)(),c=(0,f.useFlowMetadata)();return(0,n.BX)(n.HY,{children:["outside"===s.parsedLayout.logoPlacement&&(0,n.tZ)(C.u,{sx:e=>({position:"relative",[l.mqu.sm]:{margin:`0 0 ${e.space.$7} 0`}})}),(0,n.tZ)(a.Col,{elementDescriptor:[a.descriptors.cardBox,e.elementDescriptor],className:(0,a.generateFlowPartClassname)(c),ref:t,sx:[e=>({isolation:"isolate",maxWidth:`calc(100vw - ${e.sizes.$10})`,width:e.sizes.$100,borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha100,boxShadow:e.shadows.$cardBoxShadow,borderRadius:e.radii.$xl,color:e.colors.$colorText,position:"relative",overflow:"hidden"}),r],...i,children:o})]})}),Content:y,Footer:x,Alert:c,Action:e=>{let{elementId:t,sx:r,...o}=e;return(0,n.tZ)(a.Flex,{elementDescriptor:a.descriptors.footerAction,elementId:a.descriptors.footerAction.setId(t),...o,gap:1,sx:[e=>({margin:`${e.space.$none} auto`}),r]})},ActionLink:e=>(0,n.tZ)(i.r,{elementDescriptor:a.descriptors.footerActionLink,...e,colorScheme:"primary",variant:"buttonLarge"}),ActionText:e=>(0,n.tZ)(a.Text,{elementDescriptor:a.descriptors.footerActionText,...e,as:"span",variant:"body",colorScheme:"secondary"}),FooterLinks:$,ClerkAndPagesTag:h}},8864:function(e,t,r){r.d(t,{D:()=>l});var n=r(9109),o=r(9541),a=r(2464),i=r(4174);let l=e=>{let{id:t,value:r,...l}=e,{onCopy:s,hasCopied:c}=(0,a.VP)(r);return(0,n.BX)(o.Flex,{direction:"col",justify:"center",sx:{position:"relative"},children:[(0,n.tZ)(o.Input,{...l,value:r,isDisabled:!0,sx:e=>({paddingRight:e.space.$8})}),(0,n.tZ)(o.Button,{elementDescriptor:o.descriptors.formFieldInputCopyToClipboardButton,variant:"ghost",tabIndex:-1,onClick:s,sx:{position:"absolute",right:0},children:(0,n.tZ)(o.Icon,{elementDescriptor:o.descriptors.formFieldInputCopyToClipboardIcon,icon:c?i.qy:i.TU,size:"sm"})})]})}},2667:function(e,t,r){r.d(t,{GZ:()=>b,KR:()=>v,e3:()=>p,q0:()=>f});var n=r(9109),o=r(3799),a=r(9144),i=r(9541),l=r(2672),s=r(2464),c=r(1201),d=r(7623),u=r(5679);let p=e=>{let t=(0,l.useCardState)(),{id:r="code",onCodeEntryFinished:n,onResendCodeClicked:o,onResolve:i}=e,c=(0,d.Yp)(r,""),u=m(c),p=(0,s._m)(),h=async e=>{c.setSuccess("success"),await (0,d._v)(750),await i?.(e)},g=async e=>{(0,d.S3)(e,[c],t.setError),p.setIdle(),await (0,d._v)(750),u.reset()};u.onCodeEntryFinished(e=>{p.setLoading(),c.clearFeedback(),n(e,h,g)});let f=(0,a.useCallback)(e=>{u.reset(),o?.(e)},[u,o]);return{isLoading:p.isLoading,otpControl:u,onResendCode:o?f:void 0,onFakeContinue:()=>{c.clearFeedback(),n("",h,g)}}},m=(e,t)=>{let r=a.useRef(),n=a.useRef(),o=e.value,{feedback:i,feedbackType:l,onChange:s,clearFeedback:c}=e,{length:d=6}=t||{},[u,p]=a.useState(()=>o?o.split("").slice(0,d):Array.from({length:d},()=>""));return a.useEffect(()=>{if(u.filter(e=>e).length===d){let e=u.map(e=>e||" ").join("");n.current?.(e)}else{let e=u.join("");s?.({target:{value:e}})}},[u.toString()]),{otpInputProps:{length:d,values:u,setValues:p,feedback:i,feedbackType:l,clearFeedback:c,ref:r},onCodeEntryFinished:e=>{n.current=e},reset:()=>r.current?.reset()}},[h,g]=(0,o.uH)("OTPInputContext"),f=({children:e,...t})=>(0,n.tZ)(h.Provider,{value:{value:t},children:e}),b=()=>{let{resendButton:e,onResendCode:t,isLoading:r,otpControl:o}=g();return t?(0,n.tZ)(u.u,{elementDescriptor:i.descriptors.formResendCodeLink,onClick:t,startDisabled:!0,isDisabled:"success"===o.otpInputProps.feedbackType||r,showCounter:"success"!==o.otpInputProps.feedbackType,localizationKey:e}):null},v=a.forwardRef((e,t)=>{let[r,o]=a.useState(!1),l=a.useRef([]),s=a.useRef(!1),{otpControl:c,isLoading:d,isDisabled:u,centerAlign:p=!0}=g(),{feedback:m,values:h,setValues:f,feedbackType:b,length:v}=c.otpInputProps;a.useImperativeHandle(t,()=>({reset:()=>{f(h.map(()=>"")),o(!1),setTimeout(()=>$(0),0)}})),a.useLayoutEffect(()=>{setTimeout(()=>$(0),0)},[]),a.useEffect(()=>{m&&o(!0)},[m]);let x=({eventValue:e,inputPosition:t})=>{let r=(e||"").split("");if(0!==r.length&&r.every(e=>w(e))){if(r.length===v){f([...r]),$(v-1);return}f(h.map((e,n)=>n<t?e:r[n-t]||e)),$(t+r.length)}},S=(e,t)=>{let r=[...h];r[e]=t,f(r)},$=e=>{let t=Math.min(Math.max(0,e),l.current.length-1),r=l.current[t];r&&(r.focus(),h[t]&&r.select())},C=e=>t=>{if(t.preventDefault(),!s.current){$(0),s.current=!0;return}$(e)},_=e=>t=>{t.preventDefault(),x({eventValue:t.target.value||"",inputPosition:e})},k=e=>t=>{t.preventDefault(),w(t.target.value)&&$(e+1)},P=e=>t=>{t.preventDefault(),x({eventValue:t.clipboardData.getData("text/plain")||"",inputPosition:e})},I=e=>t=>{switch(t.key){case"Backspace":t.preventDefault(),S(e,""),$(e-1);return;case"ArrowLeft":t.preventDefault(),$(e-1);return;case"ArrowRight":t.preventDefault(),$(e+1);return;case" ":t.preventDefault();return}},A=p?{justifyContent:"center",alignItems:"center"}:{};return(0,n.tZ)(i.Flex,{isLoading:d,hasError:"error"===b,elementDescriptor:i.descriptors.otpCodeFieldInputs,gap:2,sx:e=>({direction:"ltr",padding:e.space.$1,marginLeft:`-${e.space.$1}`,...A}),children:h.map((e,t)=>(0,n.tZ)(y,{elementDescriptor:i.descriptors.otpCodeFieldInput,value:e,onClick:C(t),onChange:_(t),onKeyDown:I(t),onInput:k(t),onPaste:P(t),id:`digit-${t}-field`,ref:e=>l.current[t]=e,autoFocus:0===t||void 0,autoComplete:"one-time-code","aria-label":`${0===t?"Enter verification code. ":""}Digit ${t+1}`,isDisabled:u||d||r||"success"===b,hasError:"error"===b,isSuccessfullyFilled:"success"===b,type:"text",inputMode:"numeric",name:`codeInput-${t}`},t))})}),y=a.forwardRef((e,t)=>{let{isSuccessfullyFilled:r,...o}=e;return(0,n.tZ)(i.Input,{ref:t,type:"text",sx:t=>({textAlign:"center",...c.common.textVariants(t).h2,padding:`${t.space.$0x5} 0`,boxSizing:"border-box",height:t.space.$10,width:t.space.$10,borderRadius:t.radii.$md,...r?{borderColor:t.colors.$success500}:c.common.borderColor(t,e),backgroundColor:"unset","&:focus":{},[c.mqu.sm]:{height:t.space.$8,width:t.space.$8}}),...o})}),w=e=>void 0!=e&&Number.isInteger(+e)},6600:function(e,t,r){r.d(t,{Uk:()=>s,wQ:()=>i});var n=r(9109);r(9144);var o=r(9541),a=r(3638);let i=e=>{let{headers:t,page:r,onPageChange:i,rows:s,isLoading:c,itemCount:d,itemsPerPage:u,pageCount:p,emptyStateLocalizationKey:m}=e,h=d>0?Math.max(0,(r-1)*u)+1:0,g=Math.min(r*u,d);return(0,n.BX)(o.Col,{gap:4,sx:{width:"100%"},children:[(0,n.tZ)(o.Flex,{sx:e=>({overflowX:"auto",padding:e.space.$1}),children:(0,n.BX)(o.Table,{sx:{tableLayout:"fixed"},children:[(0,n.tZ)(o.Thead,{children:(0,n.tZ)(o.Tr,{children:t.map((e,t)=>(0,n.tZ)(o.Th,{elementDescriptor:o.descriptors.tableHead,localizationKey:e,sx:{width:0===t?"auto":"25%"}},t))})}),(0,n.tZ)(o.Tbody,{children:c?(0,n.tZ)(o.Tr,{children:(0,n.tZ)(o.Td,{colSpan:4,children:(0,n.tZ)(o.Spinner,{colorScheme:"primary",sx:{margin:"auto",display:"block"},elementDescriptor:o.descriptors.spinner})})}):s.length?s:(0,n.tZ)(l,{localizationKey:m},"empty")})]})}),p>1&&(0,n.tZ)(a.t,{count:p,page:r,onChange:i,siblingCount:1,rowInfo:{allRowsCount:d,startingRow:h,endingRow:g}})]})},l=e=>(0,n.tZ)(o.Tr,{children:(0,n.tZ)(o.Td,{colSpan:4,children:(0,n.tZ)(o.Text,{localizationKey:e.localizationKey,sx:{margin:"auto",display:"block",width:"fit-content"}})})}),s=e=>(0,n.tZ)(o.Tr,{...e,sx:e=>({":hover":{backgroundColor:e.colors.$neutralAlpha50}})})},7434:function(e,t,r){r.d(t,{W:()=>i,Y:()=>l});var n=r(9109),o=r(9541),a=r(143);let i=e=>{let{gradient:t=60}=e,{showDevModeNotice:r}=(0,a.n)();return r?(0,n.tZ)(o.Box,{sx:e=>({userSelect:"none",pointerEvents:"none",inset:0,position:"absolute",background:`repeating-linear-gradient(-45deg,${e.colors.$warningAlpha100},${e.colors.$warningAlpha100} 6px,${e.colors.$warningAlpha150} 6px,${e.colors.$warningAlpha150} 12px)`,maskImage:`linear-gradient(transparent ${t}%, black)`})}):null},l=e=>{let{sx:t}=e,{showDevModeNotice:r}=(0,a.n)();return r?(0,n.tZ)(o.Text,{sx:[e=>({color:e.colors.$warning500,fontWeight:e.fontWeights.$semibold,padding:e.space.$1x5}),t],children:"Development mode"}):null}},6735:function(e,t,r){r.d(t,{i:()=>a,p:()=>i});var n=r(9109),o=r(9541);let a=e=>{let{dividerText:t,...r}=e;return(0,n.BX)(o.Flex,{center:!0,elementDescriptor:o.descriptors.dividerRow,...r,children:[(0,n.tZ)(l,{}),(0,n.tZ)(o.Text,{localizationKey:t||(0,o.localizationKeys)("dividerText"),elementDescriptor:o.descriptors.dividerText,variant:"body",colorScheme:"secondary",sx:e=>({margin:`0 ${e.space.$4}`})}),(0,n.tZ)(l,{})]})},i=e=>{let{sx:t,...r}=e;return(0,n.tZ)(o.Flex,{center:!0,direction:"col",elementDescriptor:o.descriptors.dividerColumn,sx:[e=>({height:e.space.$6}),t],...r,children:(0,n.tZ)(l,{vertical:!0})})},l=e=>{let t=e?.vertical?{width:"1px"}:{height:"1px"};return(0,n.tZ)(o.Flex,{elementDescriptor:o.descriptors.dividerLine,sx:e=>({flex:"1",backgroundColor:e.colors.$neutralAlpha100,...t})})}},1102:function(e,t,r){r.r(t),r.d(t,{Drawer:()=>C,FloatingOverlay:()=>f,useDrawerContext:()=>g});var n=r(9109),o=r(3799),a=r(7126),i=r(9144),l=r(3003),s=r(9541),c=r(2464),d=r(4174),u=r(1201),p=r(7623),m=r(3412);let h=i.createContext(null),g=()=>{let e=i.useContext(h);if(!e)throw Error("Drawer components must be wrapped in <Drawer.Root>");return e},f=i.forwardRef(function(e,t){let{strategy:r}=g(),{disableScrollLock:a,enableScrollLock:i}=(0,c.Pr)();return(0,o.Gw)(()=>{if("fixed"===r)return i(),()=>{a()}},[r,a,i]),(0,n.tZ)(s.Box,{ref:t,...e,elementDescriptor:s.descriptors.drawerBackdrop,sx:[e=>({inset:0,backgroundColor:p.O9.setAlpha(e.colors.$colorBackground,.28)}),e.sx]})}),b=i.forwardRef((e,t)=>{let{strategy:r,context:o}=g(),{isMounted:i,styles:s}=(0,a.Y_)(o,{initial:{opacity:0},open:{opacity:1},close:{opacity:0},common:{position:r,inset:0,transitionProperty:"opacity",transitionTimingFunction:l.$$.bezier},duration:l.bf.drawer});return i?(0,n.tZ)(f,{ref:t,style:{...s}}):null});b.displayName="Drawer.Overlay";let v=i.forwardRef(({children:e},t)=>{let r=(0,c.Tb)(),{animations:o}=(0,s.useAppearance)().parsedLayout,{strategy:i,refs:d,context:u,getFloatingProps:p,direction:m}=g(),h=(0,a.qq)([t,d.setFloating]),{isMounted:f,styles:b}=(0,a.Y_)(u,{initial:{transform:"translate3d(var(--transform-offset), 0, 0)"},open:{transform:"translate3d(0, 0, 0)"},close:{transform:"translate3d(var(--transform-offset), 0, 0)"},common:{transitionProperty:"transform",transitionTimingFunction:l.$$.bezier},duration:r||!0!==o?0:l.bf.drawer});return f?(0,n.tZ)(a.wD,{context:u,modal:!0,outsideElementsInert:!0,initialFocus:d.floating,children:(0,n.tZ)(s.Box,{ref:h,...p(),sx:e=>({position:i,insetBlock:0,insetInline:0,pointerEvents:"none",isolation:"isolate",zIndex:"absolute"===i?e.zIndices.$modal:void 0}),elementDescriptor:s.descriptors.drawerRoot,children:(0,n.tZ)(s.Flex,{elementDescriptor:s.descriptors.drawerContent,style:b,direction:"col",sx:e=>({"--transform-offset":"fixed"===i?`calc((100% + ${e.space.$3} + ${e.space.$8x75}) * ${"rtl"===m?-1:1})`:`calc((100% + ${e.space.$8x75}) * ${"rtl"===m?-1:1})`,willChange:"transform",position:i,insetBlock:"fixed"===i?e.space.$3:0,insetInlineEnd:"fixed"===i?e.space.$3:0,outline:0,width:e.sizes.$100,maxWidth:"fixed"===i?`calc(100% - ${e.space.$6})`:"100%",backgroundColor:e.colors.$colorBackground,borderStartStartRadius:e.radii.$lg,borderEndStartRadius:e.radii.$lg,borderEndEndRadius:"fixed"===i?e.radii.$lg:0,borderStartEndRadius:"fixed"===i?e.radii.$lg:0,borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha100,boxShadow:e.shadows.$cardBoxShadow,overflow:"hidden",pointerEvents:"auto"}),children:e})})}):null});v.displayName="Drawer.Content";let y=i.forwardRef(({title:e,children:t,sx:r},o)=>(0,n.tZ)(s.Box,{ref:o,elementDescriptor:s.descriptors.drawerHeader,as:"header",sx:[t=>({display:"flex",background:u.common.mergedColorsBackground(p.O9.setAlpha(t.colors.$colorBackground,1),t.colors.$neutralAlpha50),borderBlockEndWidth:t.borderWidths.$normal,borderBlockEndStyle:t.borderStyles.$solid,borderBlockEndColor:t.colors.$neutralAlpha100,borderStartStartRadius:t.radii.$lg,borderStartEndRadius:t.radii.$lg,paddingBlock:e?t.space.$3:void 0,paddingInline:e?t.space.$4:void 0}),r],children:e?(0,n.BX)(n.HY,{children:[(0,n.tZ)(s.Heading,{localizationKey:e,as:"h2",elementDescriptor:s.descriptors.drawerTitle,textVariant:"h2",sx:{alignSelf:"center"}}),(0,n.tZ)(S,{})]}):t})),w=i.forwardRef(({children:e,...t},r)=>(0,n.tZ)(s.Box,{ref:r,elementDescriptor:s.descriptors.drawerBody,sx:{display:"flex",flexDirection:"column",flex:1,overflowY:"auto",overflowX:"hidden"},...t,children:e})),x=i.forwardRef(({children:e,sx:t,...r},o)=>(0,n.tZ)(s.Box,{ref:o,elementDescriptor:s.descriptors.drawerFooter,sx:[e=>({display:"flex",flexDirection:"column",background:u.common.mergedColorsBackground(p.O9.setAlpha(e.colors.$colorBackground,1),e.colors.$neutralAlpha50),borderBlockStartWidth:e.borderWidths.$normal,borderBlockStartStyle:e.borderStyles.$solid,borderBlockStartColor:e.colors.$neutralAlpha100,borderEndStartRadius:e.radii.$xl,borderEndEndRadius:e.radii.$xl,paddingBlock:e.space.$3,paddingInline:e.space.$4}),t],...r,children:e})),S=i.forwardRef((e,t)=>{let{setIsOpen:r}=g();return(0,n.tZ)(m.h,{ref:t,elementDescriptor:s.descriptors.drawerClose,variant:"ghost","aria-label":"Close drawer",onClick:()=>r(!1),icon:(0,n.tZ)(s.Icon,{icon:d.x8,size:"sm"}),sx:e=>({color:e.colors.$colorTextSecondary,padding:e.space.$3,marginInlineStart:"auto"})})});S.displayName="Drawer.Close";let $=i.forwardRef(({open:e,onOpenChange:t,children:r,actionsSlot:o,roleProps:i},d)=>{let m=(0,c.Tb)(),{animations:h}=(0,s.useAppearance)().parsedLayout,{refs:g,context:f}=(0,a.YF)({open:e,onOpenChange:t,transform:!1,strategy:"absolute"}),{getFloatingProps:b}=(0,a.NI)([(0,a.eS)(f),(0,a.bQ)(f),(0,a.qs)(f,{role:"alertdialog",...i})]),v=(0,a.qq)([d,g.setFloating]),{styles:y}=(0,a.Y_)(f,{initial:{opacity:0},open:{opacity:1},close:{opacity:0},common:{transitionProperty:"opacity",transitionTimingFunction:l.$$.bezier},duration:l.bf.drawer}),{isMounted:w,styles:x}=(0,a.Y_)(f,{initial:{transform:"translate3D(0, 100%, 0)"},open:{transform:"translate3D(0, 0, 0)"},close:{transform:"translate3D(0, 100%, 0)"},common:{transitionProperty:"transform",transitionTimingFunction:l.$$.bezier},duration:m||!0!==h?0:l.bf.drawer});return w?(0,n.BX)(n.HY,{children:[(0,n.tZ)(s.Span,{elementDescriptor:s.descriptors.drawerConfirmationBackdrop,style:y,sx:e=>({position:"absolute",inset:0,backgroundImage:`linear-gradient(to bottom, ${p.O9.setAlpha(e.colors.$colorBackground,.28)}, ${e.colors.$colorBackground})`})}),(0,n.tZ)(a.wD,{context:f,modal:!0,outsideElementsInert:!0,initialFocus:g.floating,children:(0,n.BX)(s.Box,{ref:v,elementDescriptor:s.descriptors.drawerConfirmationRoot,style:x,...b(),sx:e=>({display:"flex",flexDirection:"column",rowGap:e.space.$6,outline:"none",willChange:"transform",position:"absolute",bottom:0,left:0,right:0,background:u.common.mergedColorsBackground(p.O9.setAlpha(e.colors.$colorBackground,1),e.colors.$neutralAlpha50),padding:e.space.$4,borderStartStartRadius:e.radii.$md,borderStartEndRadius:e.radii.$md,boxShadow:`0 0 0 1px ${e.colors.$neutralAlpha100}`}),children:[r,(0,n.tZ)(s.Flex,{elementDescriptor:s.descriptors.drawerConfirmationActions,gap:3,justify:"end",children:o})]})})]}):null});$.displayName="Drawer.Confirmation";let C={Root:function({children:e,open:t,onOpenChange:r,strategy:o="fixed",floatingProps:i,portalProps:l,dismissProps:s}){let d=(0,c.gm)(),{refs:u,context:p}=(0,a.YF)({open:t,onOpenChange:r,transform:!1,strategy:o,placement:"ltr"===d?"right":"left",...i}),{getFloatingProps:m}=(0,a.NI)([(0,a.eS)(p),(0,a.bQ)(p,s),(0,a.qs)(p)]);return(0,n.tZ)(h.Provider,{value:{isOpen:t,setIsOpen:r,strategy:o,portalProps:l||{},refs:u,context:p,getFloatingProps:m,direction:d},children:(0,n.tZ)(a.ll,{...l,children:e})})},Overlay:b,Content:v,Header:y,Body:w,Footer:x,Confirmation:$,Close:S}},4562:function(e,t,r){r.d(t,{_:()=>d});var n=r(9109);r(9144);var o=r(9541),a=r(3234),i=r(4676),l=r(4455),s=r(2672),c=r(2654);let d=e=>{let{shouldNavigateBack:t=!0}=e,r=(0,s.useCardState)(),{navigate:d}=(0,i.useRouter)(),u=(0,a.H)();return(0,n.tZ)(o.Flow.Part,{part:"havingTrouble",children:(0,n.BX)(l.Z.Root,{children:[(0,n.BX)(l.Z.Content,{children:[(0,n.BX)(c.h.Root,{showLogo:!0,children:[(0,n.tZ)(c.h.Title,{localizationKey:e.cardTitle||"Error"}),e.cardSubtitle&&(0,n.tZ)(c.h.Subtitle,{localizationKey:e.cardSubtitle})]}),(0,n.tZ)(l.Z.Alert,{children:r.error}),(0,n.BX)(o.Flex,{direction:"col",elementDescriptor:o.descriptors.main,gap:4,children:[e.message&&(0,n.tZ)(o.Text,{colorScheme:"secondary",localizationKey:e.message}),(0,n.tZ)(o.Button,{localizationKey:(0,o.localizationKeys)("signIn.alternativeMethods.getHelp.blockButton__emailSupport"),onClick:()=>{window.location.href=`mailto:${u}`},hasArrow:!0}),t?(0,n.tZ)(l.Z.Action,{elementId:"alternativeMethods",children:(0,n.tZ)(l.Z.ActionLink,{localizationKey:(0,o.localizationKeys)("backButton"),onClick:r=>{if(e.onBackLinkClick)return e.onBackLinkClick(r);t&&d("../")}})}):null]})]}),(0,n.tZ)(l.Z.Footer,{})]})})}},9923:function(e,t,r){r.d(t,{g:()=>E});var n=r(9109),o=r(9144),a=r(9541),i=r(734),l=r(2667),s=r(2672),c=r(5027),d=r(2464),u=r(1201);let p=({feedback:e})=>{let[t,r]=(0,o.useState)(0);return{height:t,calculateHeight:(0,o.useCallback)(e=>{e&&r(e.scrollHeight+2*e.offsetTop)},[e])}},m=e=>{let{id:t,elementDescriptors:r,sx:i,feedback:l,feedbackType:s="info",center:m=!1,errorMessageId:h}=e,g=(0,o.useRef)({a:void 0,b:void 0}),{getFormTextAnimation:f}=function(){let e=(0,d.Tb)(),{animations:t}=(0,a.useAppearance)().parsedLayout;return{getFormTextAnimation:(0,o.useCallback)((r,n)=>{if(e||!t)return{animation:"none"};let o=n?.inDelay?u.animations.inDelayAnimation:u.animations.inAnimation;return e=>({animation:`${r?o:u.animations.outAnimation} ${e.transitionDuration.$textField} ${e.transitionTiming.$common}`,transitionProperty:"height",transitionDuration:e.transitionDuration.$slow,transitionTimingFunction:e.transitionTiming.$common})},[e])}}(),b={error:a.descriptors.formFieldErrorText,warning:a.descriptors.formFieldWarningText,info:a.descriptors.formFieldInfoText,success:a.descriptors.formFieldSuccessText},v=(0,o.useMemo)(()=>{let e;let t=g.current;return e=t.a?.shouldEnter?{a:{...t.a,shouldEnter:!1},b:{feedback:l,feedbackType:s,shouldEnter:!0}}:{a:{feedback:l,feedbackType:s,shouldEnter:!0},b:{...t.b,shouldEnter:!1}},g.current=e,e},[l,s]),{calculateHeight:y,height:w}=p({feedback:v.a?.feedback||""}),{calculateHeight:x,height:S}=p({feedback:v.b?.feedback||""}),$=(0,o.useRef)(Math.max(w,S)),C=(0,o.useMemo)(()=>{let e=Math.max(w,S,$.current);return $.current=e,e},[w,S]),_=e=>{if(!e)return{};let n=r?.[e]||b[e];return{elementDescriptor:n,elementId:t?n?.setId?.(t):void 0,id:"error"===e?h:void 0}},k={error:a.FormErrorText,info:a.FormInfoText,success:a.FormSuccessText,warning:a.FormWarningText},P=k[v.a?.feedbackType||"info"],I=k[v.b?.feedbackType||"info"];return(0,n.BX)(a.Flex,{style:{height:l?C:0,position:"relative"},center:m,sx:[f(!!l),i],children:[(0,n.tZ)(P,{..._(v.a?.feedbackType),ref:y,sx:[()=>({visibility:v.a?.shouldEnter?"visible":"hidden"}),f(!!v.a?.shouldEnter,{inDelay:!0})],localizationKey:(0,c.MI)(v.a?.feedback)}),(0,n.tZ)(I,{..._(v.b?.feedbackType),ref:x,sx:[()=>({visibility:v.b?.shouldEnter?"visible":"hidden"}),f(!!v.b?.shouldEnter,{inDelay:!0})],localizationKey:(0,c.MI)(v.b?.feedback)})]})},h=(0,o.forwardRef)((e,t)=>{let{sx:r,groupPrefix:o,groupSuffix:i,...l}=e,s=o?{borderTopLeftRadius:"0",borderBottomLeftRadius:"0"}:{borderTopRightRadius:"0",borderBottomRightRadius:"0"},c=e=>({paddingInline:e.space.$2,borderTopRightRadius:"0",borderBottomRightRadius:"0",width:"fit-content",display:"flex",alignItems:"center"});return(0,n.BX)(a.Flex,{elementDescriptor:a.descriptors.formInputGroup,direction:"row",hasError:l.hasError,sx:e=>({position:"relative",zIndex:1,...u.common.borderVariants(e).normal,":focus-within":{...u.common.borderVariants(e,{focusRignt:!0}).normal["&:focus"]}}),children:[o&&(0,n.tZ)(a.Text,{colorScheme:"secondary",sx:c,children:o}),(0,n.tZ)(a.Input,{maxLength:25,sx:[{...s},r],variant:"unstyled",ref:t,...l}),i&&(0,n.tZ)(a.Text,{colorScheme:"secondary",sx:c,children:i})]})});var g=r(753),f=r(1576),b=r(9067),v=r(4174),y=r(7623),w=r(3412);let x=(0,o.forwardRef)((e,t)=>{let[r,i]=o.useState(!0),{id:l,onChange:s,validatePassword:c=!1,setInfo:d,setSuccess:u,setWarning:p,setError:m,setHasPassedComplexity:h,...x}=e,S=(0,o.useRef)(null),[$,C]=(0,o.useState)(null),{userSettings:{passwordSettings:_}}=(0,f.useEnvironment)(),{t:k}=(0,a.useLocalizations)(),{validatePassword:P}=(0,b.i)({..._,validatePassword:c},{onValidationSuccess:()=>u(k((0,a.localizationKeys)("unstable__errors.zxcvbn.goodPassword"))),onValidationError:e=>m(e),onValidationWarning:e=>p(e),onValidationInfo:e=>{S.current&&(S.current===document.activeElement?d(e):m(e))},onValidationComplexity:e=>h(e)});return(0,n.BX)(a.Flex,{elementDescriptor:a.descriptors.formFieldInputGroup,direction:"col",justify:"center",sx:{position:"relative"},children:[(0,n.tZ)(a.Input,{...x,onChange:e=>($&&clearTimeout($),C(setTimeout(()=>{P(e.target.value)},g.hz)),s?.(e)),onBlur:e=>{x.onBlur?.(e),P(e.target.value)},onFocus:e=>{x.onFocus?.(e),P(e.target.value)},ref:(0,y.lq)(t,S),type:r?"password":"text",sx:e=>({paddingRight:e.space.$10})}),(0,n.tZ)(w.h,{elementDescriptor:a.descriptors.formFieldInputShowPasswordButton,iconElementDescriptor:a.descriptors.formFieldInputShowPasswordIcon,"aria-label":`${r?"Show":"Hide"} password`,variant:"ghost",size:"xs",tabIndex:-1,onClick:()=>i(e=>!e),sx:e=>({position:"absolute",right:0,marginRight:e.space.$1,color:e.colors.$neutralAlpha400}),icon:r?v.bA:v.N6})]})});var S=r(3799),$=r(3146),C=r(8191);let _=(e,t)=>{if(!e)return"";let r=C.h5.get(t);return(0,y.un)(e,r?.pattern,r?.code)},k=e=>{let[t,r]=o.useState(()=>{let{number:t}=(0,y.y3)(e.initPhoneWithCode||"");return t}),[n,a]=o.useState((0,y.y3)(e.initPhoneWithCode||"").number?(0,y.y3)(e.initPhoneWithCode||"").iso:e.locationBasedCountryIso||"us");o.useEffect(()=>{r((0,y.yy)(t))},[n,t]);let i=o.useMemo(()=>{if(!t)return"";let e=C.h5.get(n)?.code||"1";return"+"+(0,y.yy)(`${e}${t}`)},[n,t]),l=o.useMemo(()=>_(t,n),[n,t]),s=o.useCallback(e=>{let{iso:t,number:n}=(0,y.y3)(e);r(n),a(t)},[n,t]);return{setNumber:r,setIso:a,setNumberAndIso:s,iso:n,number:t,numberWithCode:i,formattedNumber:l}},P=[...C.h5.values()].map(e=>({searchTerm:`${e.iso} ${e.name} ${e.code}`,value:`${e.iso} ${e.name} ${e.code}`,country:e})),I=(0,o.forwardRef)((e,t)=>{let{onChange:r,value:i,locationBasedCountryIso:l,feedbackType:s,sx:c,...d}=e,p=(0,o.useRef)(null),m=(0,o.useRef)(null),{setNumber:h,setIso:g,setNumberAndIso:f,numberWithCode:b,iso:w,formattedNumber:x}=k({initPhoneWithCode:i,locationBasedCountryIso:l}),S=(0,o.useMemo)(()=>P.find(e=>e.country.iso===w)||P[0],[w]);return(0,o.useEffect)(()=>{r?.({target:{value:b}})},[b]),(0,n.BX)(a.Flex,{elementDescriptor:a.descriptors.phoneInputBox,direction:"row",hasError:d.hasError,"data-feedback":s,ref:m,sx:e=>({...u.common.borderVariants(e,{hasError:d.hasError}).normal,position:"relative",borderRadius:e.radii.$md,zIndex:1,"&:focus-within":{...u.common.borderVariants(e,{hasError:d.hasError}).normal["&:focus"]}}),children:[(0,n.BX)($.Ph,{elementId:"countryCode",value:S.value,options:P,portal:!0,referenceElement:m,renderOption:(e,t,r)=>(0,n.tZ)(A,{sx:e=>({"&:hover":{backgroundColor:e.colors.$neutralAlpha100},'&[data-focused="true"]':{backgroundColor:e.colors.$neutralAlpha150}}),isSelected:r,country:e.country}),onChange:e=>{g(e.country.iso),p.current?.focus()},noResultsMessage:"No countries found",searchPlaceholder:"Search country or code",comparator:(e,t)=>t.searchTerm.toLowerCase().includes(e.toLowerCase()),children:[(0,n.tZ)($.UN,{variant:"ghost",sx:e=>({borderWidth:"0",borderRadius:e.radii.$md,borderBottomRightRadius:"0",borderTopRightRadius:"0",paddingRight:e.space.$2,":focus":{zIndex:2,boxShadow:"none"},":active":{zIndex:2}}),hoverAsFocus:!0,isDisabled:d.isDisabled,icon:v.jC,iconSx:e=>({color:d.isDisabled?e.colors.$neutralAlpha300:e.colors.$neutralAlpha500}),children:(0,n.tZ)(a.Text,{colorScheme:"body",as:"span",sx:{textTransform:"uppercase"},children:w})}),(0,n.tZ)($.pp,{sx:{padding:"0 0"},containerSx:e=>({gap:0,padding:`${e.space.$0x5} 0`})})]}),(0,n.BX)(a.Flex,{sx:{position:"relative",width:"100%"},children:[(0,n.BX)(a.Text,{sx:t=>({display:"flex",alignItems:"center",pointerEvents:"none",paddingLeft:t.space.$0x5,opacity:e.isDisabled?t.opacity.$disabled:1}),children:["+",S.country.code]}),(0,n.tZ)(a.Input,{value:x,variant:"unstyled",onPaste:e=>{e.preventDefault();let t=e.clipboardData.getData("text");t.includes("+")?f(t):h(t)},onChange:e=>{let t=e.target.value;t.includes("+")?f(t):h(t)},maxLength:25,type:"tel",sx:[e=>({boxShadow:"none",height:"100%",transitionProperty:e.transitionProperty.$common,transitionTimingFunction:e.transitionTiming.$common,transitionDuration:e.transitionDuration.$focusRing,"&[type=tel]":{borderRadius:e.radii.$md,borderWidth:0,borderTopLeftRadius:0,borderBottomLeftRadius:0,paddingLeft:e.space.$1,"&:focus":{borderColor:"unset",boxShadow:"unset"}}}),c],ref:(0,y.lq)(p,t),...d})]})]})}),A=(0,o.memo)(e=>{let{country:t,isSelected:r,sx:o,...i}=e;return(0,n.BX)(a.Flex,{center:!0,sx:[e=>({width:"100%",gap:e.space.$2,padding:`${e.space.$1x5} ${e.space.$4}`,color:e.colors.$colorText}),o],...i,children:[(0,n.tZ)(a.Icon,{icon:v.Jr,size:"sm",sx:{visibility:r?"visible":"hidden"}}),(0,n.tZ)(a.Text,{as:"div",sx:{width:"100%",textAlign:"left"},children:t.name}),(0,n.BX)(a.Text,{colorScheme:"secondary",children:["+",t.code]})]})}),T=(0,o.forwardRef)((e,t)=>{let{__internal_country:r}=(0,S.cL)();return(0,n.tZ)(I,{...e,locationBasedCountryIso:r,ref:t})}),R=(0,o.forwardRef)((e,t)=>{let r=(0,i.YV)(),{value:o,placeholder:l,...s}=(0,i.X2)(r);return(0,n.tZ)(a.RadioInput,{ref:t,...s,elementDescriptor:a.descriptors.formFieldRadioInput,id:e.id,focusRing:!1,sx:e=>({width:"fit-content",marginTop:e.space.$0x5}),value:e.value,checked:e.value===o})}),B=e=>(0,n.BX)(a.FormLabel,{elementDescriptor:a.descriptors.formFieldRadioLabel,htmlFor:e.id,sx:e=>({padding:`${e.space.$none} ${e.space.$2}`,display:"flex",flexDirection:"column"}),children:[(0,n.tZ)(a.Text,{elementDescriptor:a.descriptors.formFieldRadioLabelTitle,variant:"subtitle",localizationKey:e.label}),e.description&&(0,n.tZ)(a.Text,{elementDescriptor:a.descriptors.formFieldRadioLabelDescription,colorScheme:"secondary",localizationKey:e.description})]}),Z=(0,o.forwardRef)((e,t)=>{let r=(0,o.useId)();return(0,n.BX)(a.Flex,{elementDescriptor:a.descriptors.formFieldRadioGroupItem,align:"start",children:[(0,n.tZ)(R,{id:r,ref:t,value:e.value}),(0,n.tZ)(B,{id:r,label:e.label,description:e.description})]})}),z=(0,o.forwardRef)((e,t)=>{let{t:r}=(0,a.useLocalizations)(),o=(0,i.YV)(),{placeholder:l,...s}=(0,i.X2)(o);return(0,n.tZ)(T,{ref:t,elementDescriptor:a.descriptors.formFieldInput,elementId:a.descriptors.formFieldInput.setId(o.fieldId),...s,feedbackType:o.feedbackType,placeholder:r(l)})}),D=(0,o.forwardRef)((e,t)=>{let{t:r}=(0,a.useLocalizations)(),o=(0,i.YV)(),{placeholder:l,...s}=(0,i.X2)(o,["validatePassword","setError","setWarning","setSuccess","setInfo","setHasPassedComplexity"]);return(0,n.tZ)(x,{ref:t,elementDescriptor:a.descriptors.formFieldInput,elementId:a.descriptors.formFieldInput.setId(o.fieldId),...s,placeholder:r(l)})}),L=(0,o.forwardRef)(({elementDescriptor:e,elementId:t},r)=>{let o=(0,i.YV)(),{placeholder:l,...s}=(0,i.X2)(o);return(0,n.tZ)(a.CheckboxInput,{ref:r,...s,elementDescriptor:e||a.descriptors.formFieldInput,elementId:t||a.descriptors.formFieldInput.setId(o.fieldId),focusRing:!1,sx:e=>({width:"fit-content",flexShrink:0,marginTop:e.space.$0x5})})}),E={Root:e=>{let t=(0,s.useCardState)(),{children:r,isDisabled:o,...a}=e,l=o||t.isLoading,c={...a,isDisabled:l};return(0,n.tZ)(i.aS,{...c,children:r})},Label:e=>{let{isRequired:t,id:r,label:o,isDisabled:l,hasError:s}=(0,i.YV)();return e.localizationKey||o||e.children?(0,n.tZ)(a.FormLabel,{localizationKey:e.localizationKey||o,elementDescriptor:a.descriptors.formFieldLabel,elementId:a.descriptors.formFieldLabel.setId(r),hasError:!!s,isDisabled:l,isRequired:t,sx:{display:"flex",alignItems:"center"},children:e.children}):null},LabelRow:e=>{let{fieldId:t}=(0,i.YV)();return(0,n.tZ)(a.Flex,{justify:"between",align:"center",elementDescriptor:a.descriptors.formFieldLabelRow,elementId:a.descriptors.formFieldLabelRow.setId(t),children:e.children})},Input:(0,o.forwardRef)((e,t)=>{let{t:r}=(0,a.useLocalizations)(),o=(0,i.YV)(),{placeholder:l,...s}=(0,i.X2)(o);return(0,n.tZ)(a.Input,{ref:t,elementDescriptor:a.descriptors.formFieldInput,elementId:a.descriptors.formFieldInput.setId(o.fieldId),...s,placeholder:r(l)})}),PasswordInput:D,PhoneInput:z,InputGroup:(0,o.forwardRef)((e,t)=>{let{t:r}=(0,a.useLocalizations)(),o=(0,i.YV)(),{placeholder:l,...s}=(0,i.X2)(o);return(0,n.tZ)(h,{ref:t,elementDescriptor:a.descriptors.formFieldInput,elementId:a.descriptors.formFieldInput.setId(o.fieldId),...s,groupPrefix:e.groupPrefix,groupSuffix:e.groupSuffix,placeholder:r(l)})}),RadioItem:Z,CheckboxIndicator:L,CheckboxLabel:e=>{let{label:t,id:r}=(0,i.YV)();return t?(0,n.tZ)(B,{label:t,id:r,description:e.description}):null},Action:e=>{let{fieldId:t,isDisabled:r}=(0,i.YV)();return e.localizationKey||e.children?(0,n.tZ)(a.Link,{localizationKey:e.localizationKey,elementDescriptor:a.descriptors.formFieldAction,elementId:a.descriptors.formFieldLabel.setId(t),isDisabled:r,colorScheme:"primary",variant:"buttonSmall",onClick:t=>{t.preventDefault(),e.onClick?.(t)},children:e.children}):null},AsOptional:()=>{let{fieldId:e,isDisabled:t}=(0,i.YV)();return(0,n.tZ)(a.Text,{localizationKey:(0,a.localizationKeys)("formFieldHintText__optional"),elementDescriptor:a.descriptors.formFieldHintText,elementId:a.descriptors.formFieldHintText.setId(e),as:"span",variant:"caption",colorScheme:"secondary",isDisabled:t})},LabelIcon:e=>{let{t}=(0,a.useLocalizations)();return e.icon?(0,n.tZ)(a.Flex,{as:"span",title:t((0,a.localizationKeys)("formFieldHintText__slug")),sx:{marginRight:"auto"},children:(0,n.tZ)(a.Icon,{icon:e.icon,sx:e=>({marginLeft:e.space.$0x5,color:e.colors.$neutralAlpha400,width:e.sizes.$4,height:e.sizes.$4})})}):null},Feedback:e=>{let{fieldId:t,debouncedFeedback:r,errorMessageId:o}=(0,i.YV)();return(0,n.tZ)(m,{center:e.center,errorMessageId:o,...r,elementDescriptors:e.elementDescriptors,id:t})},OTPRoot:l.q0,OTPCodeControl:l.KR,OTPResendButton:l.GZ}},431:function(e,t,r){r.d(t,{l:()=>m});var n=r(9109),o=r(3799),a=r(9144),i=r(9541),l=r(2464),s=r(2672),c=r(9923);let[d,u]=(0,o.uH)("FormState"),p=e=>{let{isOptional:t,icon:r,actionLabel:o,children:a,onActionClicked:l,...s}=e;return(0,n.tZ)(c.g.Root,{...s,children:(0,n.BX)(i.Col,{elementDescriptor:i.descriptors.formField,elementId:i.descriptors.formField.setId(s.id),sx:{position:"relative",flex:"1 1 auto"},children:[(0,n.BX)(i.Flex,{direction:"col",sx:e=>({gap:e.space.$2}),children:[(0,n.BX)(c.g.LabelRow,{children:[(0,n.tZ)(c.g.Label,{}),(0,n.tZ)(c.g.LabelIcon,{icon:r}),!o&&t&&(0,n.tZ)(c.g.AsOptional,{}),o&&(0,n.tZ)(c.g.Action,{localizationKey:o,onClick:l}),(0,n.tZ)(c.g.Action,{})]}),a]}),(0,n.tZ)(c.g.Feedback,{})]})})},m={Root:e=>{let t=(0,s.useCardState)(),r=(0,l._m)(),[o,c]=(0,a.useState)(!1),u=async n=>{if(n.preventDefault(),n.stopPropagation(),e.onSubmit)try{t.setLoading(),r.setLoading(),c(!0),await e.onSubmit(n)}finally{t.setIdle(),r.setIdle()}},p=a.useMemo(()=>({value:{isLoading:r.isLoading,isDisabled:t.isLoading||r.isLoading,submittedWithEnter:o}}),[t.isLoading,r.isLoading,o]);return(0,n.tZ)(d.Provider,{value:p,children:(0,n.BX)(i.Form,{elementDescriptor:i.descriptors.form,gap:6,...e,onSubmit:u,children:[(0,n.tZ)("button",{type:"submit","aria-hidden":!0,style:{visibility:"hidden",position:"absolute"}}),e.children]})})},ControlRow:e=>{let{elementId:t,...r}=e;return(0,n.tZ)(i.Flex,{elementDescriptor:i.descriptors.formFieldRow,elementId:i.descriptors.formFieldRow.setId(t),justify:"between",gap:4,...r})},PlainInput:e=>(0,n.tZ)(p,{...e,children:(0,n.tZ)(c.g.Input,{})}),PasswordInput:(0,a.forwardRef)((e,t)=>(0,n.tZ)(p,{...e,children:(0,n.tZ)(c.g.PasswordInput,{ref:t})})),PhoneInput:e=>(0,n.tZ)(p,{...e,children:(0,n.tZ)(c.g.PhoneInput,{})}),OTPInput:e=>{let{ref:t,...r}=e.otpControl.otpInputProps,{centerAlign:o=!0}=e;return(0,n.tZ)(c.g.Root,{...r,children:(0,n.tZ)(c.g.OTPRoot,{...e,children:(0,n.BX)(i.Col,{elementDescriptor:i.descriptors.form,gap:2,align:o?"center":"start",children:[(0,n.BX)(i.Flex,{elementDescriptor:i.descriptors.otpCodeField,isLoading:e.isLoading,hasError:"error"===e.otpControl.otpInputProps.feedbackType,direction:"col",children:[(0,n.tZ)(c.g.OTPCodeControl,{ref:t}),(0,n.tZ)(c.g.Feedback,{center:!0,elementDescriptors:{error:i.descriptors.otpCodeFieldErrorText}})]}),(0,n.tZ)(c.g.OTPResendButton,{})]})})})},InputGroup:e=>{let{groupSuffix:t,groupPrefix:r,...o}=e;return(0,n.tZ)(p,{...o,children:(0,n.tZ)(c.g.InputGroup,{groupSuffix:t,groupPrefix:r})})},RadioGroup:e=>{let{radioOptions:t,...r}=e;return(0,n.BX)(c.g.Root,{...r,children:[(0,n.tZ)(i.Col,{elementDescriptor:i.descriptors.formFieldRadioGroup,gap:3,children:t?.map(({value:e,description:t,label:r})=>n.tZ(i.Flex,{sx:e=>({borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha100,borderRadius:e.radii.$md,padding:e.space.$2}),children:n.tZ(c.g.RadioItem,{value:e,label:r,description:t})},e))}),(0,n.tZ)(c.g.Feedback,{})]})},Checkbox:e=>(0,n.tZ)(c.g.Root,{...e,children:(0,n.BX)(i.Flex,{align:"start",children:[(0,n.tZ)(c.g.CheckboxIndicator,{}),(0,n.tZ)(c.g.CheckboxLabel,{description:e.description})]})}),SubmitButton:e=>{let{isLoading:t,isDisabled:r}=u();return(0,n.tZ)(i.Button,{elementDescriptor:i.descriptors.formButtonPrimary,block:!0,textVariant:"buttonLarge",isLoading:t,isDisabled:r,type:"submit",...e,localizationKey:e.localizationKey||(0,i.localizationKeys)("formButtonPrimary")})},ResetButton:e=>{let{isLoading:t,isDisabled:r}=u();return(0,n.tZ)(i.Button,{elementDescriptor:i.descriptors.formButtonReset,block:!0,variant:"ghost",type:"reset",isDisabled:t||r,...e})}}},9460:function(e,t,r){r.d(t,{A:()=>s,K:()=>c});var n=r(9109),o=r(9541),a=r(2464),i=r(1085),l=r(431);let s=e=>{let{isDisabled:t,onReset:r,submitLabel:o,resetLabel:s,hideReset:d,...u}=e,{navigateToFlowStart:p}=(0,a.zk)();return(0,n.BX)(c,{children:[(0,n.tZ)(l.l.SubmitButton,{block:!1,isDisabled:t,localizationKey:o||(0,i.u1)("userProfile.formButtonPrimary__save"),...u}),!d&&(0,n.tZ)(l.l.ResetButton,{localizationKey:s||(0,i.u1)("userProfile.formButtonReset"),block:!1,onClick:r||p})]})},c=e=>(0,n.tZ)(o.Flex,{direction:"rowReverse",gap:2,...e,children:e.children})},8487:function(e,t,r){r.d(t,{Y:()=>s});var n=r(9109),o=r(9541),a=r(4455),i=r(2672),l=r(2654);let s=e=>{let{headerTitle:t,headerTitleTextVariant:r="h3",headerSubtitle:s,headerSubtitleTextVariant:c="body",children:d,sx:u,...p}=e,m=(0,i.useCardState)();return(0,n.BX)(o.Col,{elementDescriptor:o.descriptors.formContainer,gap:4,...p,sx:[u],children:[(t||s)&&(0,n.BX)(l.h.Root,{children:[t&&(0,n.tZ)(l.h.Title,{textVariant:r,..."string"==typeof t?{children:t}:{localizationKey:t}}),s&&(0,n.tZ)(l.h.Subtitle,{variant:c,..."string"==typeof s?{children:s}:{localizationKey:s}})]}),(0,n.tZ)(a.Z.Alert,{children:m.error}),(0,n.tZ)(o.Col,{gap:8,children:d})]})}},5994:function(e,t,r){r.d(t,{q:()=>i});var n=r(9109),o=r(9541),a=r(7623);let i=e=>{let t=(0,a.L_)(e.value);return(0,n.tZ)(o.Text,{as:"span",elementDescriptor:o.descriptors.formattedPhoneNumberText,children:t})}},8104:function(e,t,r){r.d(t,{m:()=>a});var n=r(9109),o=r(9541);let a=()=>(0,n.tZ)(o.Flex,{center:!0,sx:{height:"100%"},children:(0,n.tZ)(o.Spinner,{colorScheme:"primary",size:"lg",elementDescriptor:o.descriptors.spinner})})},932:function(e,t,r){r.d(t,{a:()=>s});var n=r(9109),o=r(9144),a=r(9541),i=r(8345);let l={xs:{svgSize:24,textSize:i.$4.fontSizes.$xs},sm:{svgSize:32,textSize:i.$4.fontSizes.$sm},md:{svgSize:52,textSize:i.$4.fontSizes.$md},lg:{svgSize:64,textSize:i.$4.fontSizes.$lg},xl:{svgSize:96,textSize:i.$4.fontSizes.$xl}},s=o.memo(e=>{let{value:t,limit:r,size:o="sm"}=e,{textSize:i,svgSize:s}=l[o],c=s/2,d=e.strokeWidth||s/10,u=c-d/2,p=2*Math.PI*u,m=t/r*p;return(0,n.BX)(a.Col,{center:!0,sx:e=>({"--cl-gauge-inner-stroke-color":e.colors.$neutralAlpha900,"--cl-gauge-outter-stroke-color":e.colors.$neutralAlpha300,"> svg":{transform:"rotate(-90deg)"}}),children:[(0,n.BX)("svg",{width:s,height:s,viewBox:`0 0 ${s} ${s}`,children:[(0,n.tZ)("circle",{r:u,cx:c,cy:c,strokeWidth:d,fill:"transparent",stroke:"var(--cl-gauge-outter-stroke-color)",strokeLinecap:"round"}),t>=0?(0,n.tZ)("circle",{r:u,cx:c,cy:c,strokeWidth:d,strokeDasharray:p+" "+p,strokeDashoffset:p-m,fill:"transparent",stroke:"var(--cl-gauge-inner-stroke-color)",strokeLinecap:"round"}):null]}),(0,n.tZ)(a.Text,{as:"div",sx:e=>({position:"absolute",display:"flex",fontSize:i,color:e.colors.$neutralAlpha900}),children:t})]})})},2654:function(e,t,r){r.d(t,{h:()=>u});var n=r(9109),o=r(9144),a=r(9541),i=r(4174),l=r(3174),s=r(6735);let c=o.memo(o.forwardRef((e,t)=>{let{sx:r,children:o,contentSx:i,gap:c=6,showLogo:d=!1,showDivider:u=!1,...p}=e,m="inside"===(0,a.useAppearance)().parsedLayout.logoPlacement&&d,h=u&&m;return(0,n.BX)(a.Col,{ref:t,elementDescriptor:a.descriptors.header,gap:c,sx:r,...p,children:[m&&(0,n.tZ)(l.u,{}),h&&(0,n.tZ)(s.p,{}),(0,n.tZ)(a.Col,{gap:1,sx:i,...p,children:o})]})})),d=o.memo(e=>{let{sx:t,textVariant:r="h2",...o}=e;return(0,n.tZ)(a.Heading,{elementDescriptor:a.descriptors.headerTitle,textVariant:r,sx:t,...o})}),u={Root:c,Title:d,Subtitle:o.memo(e=>{let{sx:t,...r}=e;return(0,n.tZ)(a.Text,{elementDescriptor:a.descriptors.headerSubtitle,variant:"body",colorScheme:"secondary",sx:[{wordWrap:"break-word"},t],...r})}),BackLink:o.memo(e=>{let{sx:t,children:r,...o}=e;return(0,n.BX)(a.Link,{elementDescriptor:a.descriptors.headerBackLink,sx:e=>[{display:"inline-flex",alignItems:"center",gap:e.space.$2,width:"fit-content","&:hover":{textDecoration:"none"}},t],...o,children:[(0,n.tZ)(a.Icon,{icon:i.Y4,sx:e=>({color:e.colors.$colorText})}),r]})})}},3412:function(e,t,r){r.d(t,{h:()=>i});var n=r(9109),o=r(9144),a=r(9541);let i=e=>{let{children:t,icon:r,localizationKey:i,iconElementDescriptor:l,...s}=e,{t:c}=(0,a.useLocalizations)(),d=c(i),u=(0,o.isValidElement)(r)?(0,o.cloneElement)(r,{"aria-hidden":!0,focusable:!1}):(0,n.tZ)(a.Icon,{elementDescriptor:l,icon:r,size:"md"});return(0,n.tZ)(a.Button,{...s,children:(0,n.BX)(n.HY,{children:[u,d||t]})})}},9566:function(e,t,r){r.d(t,{f:()=>a});var n=r(9109),o=r(9541);let a=e=>{let{icon:t,boxElementDescriptor:r,iconElementDescriptor:a,sx:i,...l}=e;return(0,n.tZ)(o.Flex,{center:!0,elementDescriptor:r,sx:[e=>({backgroundColor:e.colors.$neutralAlpha50,width:e.sizes.$16,height:e.sizes.$16,borderRadius:e.radii.$circle}),i],...l,children:(0,n.tZ)(o.Icon,{elementDescriptor:a,icon:t,size:"lg",sx:e=>({color:e.colors.$neutralAlpha600})})})}},9577:function(e,t,r){r.d(t,{m:()=>c});var n=r(9109),o=r(6096),a=r(9144),i=r(9541),l=r(4174),s=r(7623);let c=e=>{let{avatarUrl:t=(0,o.WY)("avatar_placeholder","jpeg"),identifier:r,onClick:c,...d}=e,h=a.useRef({avatarUrl:t,identifier:(0,s.HT)(r)}),g=c&&(0,n.tZ)(i.Button,{elementDescriptor:i.descriptors.identityPreviewEditButton,variant:"link",textVariant:"buttonSmall",onClick:c,"aria-label":"Edit",children:(0,n.tZ)(i.Icon,{elementDescriptor:i.descriptors.identityPreviewEditButtonIcon,icon:l.ET})});if(!h.current.identifier)return(0,n.tZ)(m,{...d,children:g});if((0,s.Sj)(h.current.identifier)||!h.current.identifier.startsWith("+"))return(0,n.BX)(m,{...d,children:[(0,n.tZ)(u,{...h.current}),g]});let f=(0,s.y3)(h.current.identifier||""),b=(0,s.uz)(f.iso);return(0,n.BX)(m,{...d,children:[(0,n.tZ)(p,{identifier:h.current.identifier,flag:b}),g]})},d=e=>(0,n.tZ)(i.Text,{elementDescriptor:i.descriptors.identityPreviewText,colorScheme:"secondary",truncate:!0,...e}),u=e=>(0,n.tZ)(d,{children:e.identifier}),p=e=>(0,n.BX)(n.HY,{children:[(0,n.tZ)(i.Text,{sx:e=>({fontSize:e.fontSizes.$sm}),children:e.flag}),(0,n.tZ)(d,{children:e.identifier})]}),m=e=>(0,n.tZ)(i.Flex,{elementDescriptor:i.descriptors.identityPreview,align:"center",gap:2,sx:{justifyContent:"center"},...e})},3300:function(e,t,r){r.d(t,{e:()=>i});var n=r(9109),o=r(9541),a=r(4174);function i(e){return(0,n.BX)(o.Flex,{sx:e=>({gap:e.space.$2,padding:`${e.space.$3} ${e.space.$4}`,backgroundColor:e.colors.$neutralAlpha50,borderRadius:e.radii.$md}),children:[(0,n.tZ)(o.Icon,{icon:a.I$,sx:e=>({opacity:e.opacity.$disabled})}),(0,n.tZ)(o.Text,{localizationKey:e.message,sx:e=>({color:e.colors.$colorTextSecondary})})]})}},1319:function(e,t,r){r.d(t,{W:()=>i});var n=r(9109),o=r(9144),a=r(9541);let i=o.forwardRef((e,t)=>{let{leftIcon:r,sx:o,...i}=e;return(0,n.BX)(a.Flex,{center:!0,sx:{width:"100%",position:"relative"},children:[r?(0,n.tZ)(a.Box,{sx:e=>[{position:"absolute",left:e.space.$3x5,width:e.sizes.$3x5,height:e.sizes.$3x5,pointerEvents:"none",display:"grid",placeContent:"center","& svg":{position:"absolute",width:"100%",height:"100%"}}],children:r}):null,(0,n.tZ)(a.Input,{...i,sx:[e=>({width:"100%",paddingLeft:e.space.$10}),o],ref:t})]})})},8314:function(e,t,r){r.d(t,{H:()=>p});var n=r(9109),o=r(1576),a=r(9541),i=r(734),l=r(9923),s=r(9144);let c=/\[([^\]]+)\]\(([^)]+)\)/g,d=(0,s.memo)(({text:e,...t})=>{let r=(0,s.useMemo)(()=>t,[t]);return(0,s.useMemo)(()=>{let t=[],o=0;return e.replace(c,(i,l,s,c)=>(c>o&&t.push(e.slice(o,c)),t.push((0,n.tZ)(a.Link,{href:s,...r,children:l},c)),o=c+i.length,i)),o<e.length&&t.push(e.slice(o)),t},[e,r])}),u=e=>{let t;let{termsUrl:r,privacyPolicyUrl:o}=e,{t:l}=(0,a.useLocalizations)(),s=(0,i.YV)(),{placeholder:c,...u}=(0,i.X2)(s);return r&&o?t=(0,a.localizationKeys)("signUp.legalConsent.checkbox.label__termsOfServiceAndPrivacyPolicy",{termsOfServiceLink:e.termsUrl,privacyPolicyLink:e.privacyPolicyUrl}):r?t=(0,a.localizationKeys)("signUp.legalConsent.checkbox.label__onlyTermsOfService",{termsOfServiceLink:e.termsUrl}):o&&(t=(0,a.localizationKeys)("signUp.legalConsent.checkbox.label__onlyPrivacyPolicy",{privacyPolicyLink:e.privacyPolicyUrl})),(0,n.tZ)(a.FormLabel,{elementDescriptor:a.descriptors.formFieldCheckboxLabel,htmlFor:u.id,isDisabled:u.isDisabled,sx:e=>({paddingLeft:e.space.$1x5,textAlign:"left"}),children:(0,n.tZ)(a.Text,{variant:"body",as:"span",children:(0,n.tZ)(d,{text:l(t),isExternal:!0,sx:e=>({textDecoration:"underline",textUnderlineOffset:e.space.$1})})})})},p=e=>{let{displayConfig:t}=(0,o.useEnvironment)(),{parsedLayout:r}=(0,a.useAppearance)(),i=r.termsPageUrl||t.termsUrl,s=r.privacyPageUrl||t.privacyPolicyUrl;return(0,n.tZ)(l.g.Root,{...e,children:(0,n.BX)(a.Flex,{justify:"center",children:[(0,n.tZ)(l.g.CheckboxIndicator,{elementDescriptor:a.descriptors.formFieldCheckboxInput,elementId:a.descriptors.formFieldInput.setId("legalAccepted")}),(0,n.tZ)(u,{termsUrl:i,privacyPolicyUrl:s})]})})}},8e3:function(e,t,r){r.d(t,{a:()=>m});var n=r(9109),o=r(9144),a=r(9541),i=r(2464),l=r(4174),s=r(1201),c=r(5913);let d=o.createContext(void 0);function u({text:e}){let{onCopy:t}=(0,i.VP)(e);return(0,n.tZ)(a.Span,{elementDescriptor:a.descriptors.lineItemsDescriptionText,sx:e=>({...s.common.textVariants(e).body,display:"flex",minWidth:"0"}),onCopy:async e=>{e.preventDefault(),await t()},children:(0,c.L)(e,15)})}function p({text:e,copyLabel:t="Copy"}){let{onCopy:r,hasCopied:o}=(0,i.VP)(e);return(0,n.tZ)(a.Button,{variant:"unstyled",onClick:r,sx:e=>({color:"inherit",width:e.sizes.$4,height:e.sizes.$4,padding:0,borderRadius:e.radii.$sm,"&:focus-visible":{outline:"2px solid",outlineColor:e.colors.$neutralAlpha200}}),focusRing:!1,"aria-label":o?"Copied":t,children:(0,n.tZ)(a.Icon,{size:"sm",icon:o?l.Jr:l.CK,"aria-hidden":!0})})}let m={Root:function({children:e}){return(0,n.tZ)(a.Dl,{elementDescriptor:a.descriptors.lineItemsRoot,sx:e=>({display:"grid",gridRowGap:e.space.$2}),children:e})},Group:function({children:e,borderTop:t=!1,variant:r="primary"}){return(0,n.tZ)(d.Provider,{value:{variant:r},children:(0,n.tZ)(a.Box,{elementDescriptor:a.descriptors.lineItemsGroup,elementId:a.descriptors.lineItemsGroup.setId(r),sx:e=>({display:"grid",gridTemplateColumns:"repeat(2, minmax(0, 1fr))",...t?{borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100,paddingTop:e.space.$2}:{}}),children:e})})},Title:o.forwardRef(({title:e,description:t,icon:r},i)=>{let l=o.useContext(d);if(!l)throw Error("LineItems.Title must be used within LineItems.Group");let{variant:c}=l,u="primary"===c?"subtitle":"caption";return(0,n.BX)(a.Dt,{ref:i,elementDescriptor:a.descriptors.lineItemsTitle,elementId:a.descriptors.lineItemsTitle.setId(c),sx:e=>({display:"grid",color:"primary"===c?e.colors.$colorText:e.colors.$colorTextSecondary,...s.common.textVariants(e)[u]}),children:[(0,n.BX)(a.Span,{sx:e=>({display:"inline-flex",alignItems:"center",gap:e.space.$1}),children:[r?(0,n.tZ)(a.Icon,{size:"md",icon:r,"aria-hidden":!0}):null,(0,n.tZ)(a.Span,{localizationKey:e})]}),t?(0,n.tZ)(a.Span,{localizationKey:t,elementDescriptor:a.descriptors.lineItemsTitleDescription,sx:e=>({fontSize:e.fontSizes.$sm,color:e.colors.$colorTextSecondary})}):null]})}),Description:function({text:e,prefix:t,suffix:r,truncateText:i=!1,copyText:l=!1,copyLabel:c}){let m=o.useContext(d);if(!m)throw Error("LineItems.Description must be used within LineItems.Group");let{variant:h}=m;return(0,n.BX)(a.Dd,{elementDescriptor:a.descriptors.lineItemsDescription,elementId:a.descriptors.lineItemsDescription.setId(h),sx:e=>({display:"grid",justifyContent:"end",color:"tertiary"===h?e.colors.$colorTextSecondary:e.colors.$colorText}),children:[(0,n.BX)(a.Span,{elementDescriptor:a.descriptors.lineItemsDescriptionInner,sx:e=>({display:"inline-flex",justifyContent:"flex-end",alignItems:"center",gap:e.space.$1,minWidth:"0"}),children:[t?(0,n.tZ)(a.Span,{localizationKey:t,elementDescriptor:a.descriptors.lineItemsDescriptionPrefix,sx:e=>({color:e.colors.$colorTextSecondary,...s.common.textVariants(e).caption})}):null,"string"==typeof e&&i?(0,n.tZ)(u,{text:e}):(0,n.tZ)(a.Span,{localizationKey:e,elementDescriptor:a.descriptors.lineItemsDescriptionText,sx:e=>({...s.common.textVariants(e).body,minWidth:"0",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"})}),"string"==typeof e&&l?(0,n.tZ)(p,{text:e,copyLabel:c}):null]}),r?(0,n.tZ)(a.Span,{localizationKey:r,elementDescriptor:a.descriptors.lineItemsDescriptionSuffix,sx:e=>({color:e.colors.$colorTextSecondary,...s.common.textVariants(e).caption,justifySelf:"flex-end"})}):null]})}}},1455:function(e,t,r){r.d(t,{I:()=>s,W:()=>c});var n=r(9109),o=r(9541),a=r(7321),i=r(4455),l=r(2672);let s=({children:e})=>(0,n.BX)(o.Flex,{direction:"col",center:!0,elementDescriptor:o.descriptors.main,gap:8,sx:e=>({marginTop:e.space.$16,marginBottom:e.space.$13}),children:[(0,n.tZ)(o.Spinner,{size:"xl",colorScheme:"primary",elementDescriptor:o.descriptors.spinner}),e]}),c=(0,l.withCardStateProvider)(()=>{let e=(0,l.useCardState)();return(0,n.BX)(i.Z.Root,{children:[(0,n.BX)(i.Z.Content,{children:[(0,n.tZ)(i.Z.Alert,{children:e.error}),(0,n.tZ)(s,{}),(0,n.tZ)(a.S,{})]}),(0,n.tZ)(i.Z.Footer,{})]})})},5158:function(e,t,r){r.d(t,{bF:()=>g,qy:()=>b,sN:()=>v,v2:()=>h});var n=r(9109),o=r(3799),a=r(9144),i=r(9541),l=r(2464),s=r(1201),c=r(6747),d=r(2672),u=r(4045);let[p,m]=(0,o.uH)("MenuState"),h=(0,d.withFloatingTree)(e=>{let{popoverPlacement:t="bottom-end",elementId:r,...o}=e,i=(0,l.Sv)({placement:t,offset:8,shoudFlip:!0}),s=a.useMemo(()=>({value:{popoverCtx:i,elementId:r}}),[{...i},r]);return(0,n.tZ)(p.Provider,{value:s,...o})}),g=e=>{let{children:t,arialLabel:r}=e,{popoverCtx:n,elementId:o}=m(),{reference:l,toggle:s,isOpen:c}=n,d="function"==typeof r?r(c):r;return(0,a.isValidElement)(t)?(0,a.cloneElement)(t,{ref:l,elementDescriptor:t.props.elementDescriptor||i.descriptors.menuButton,elementId:t.props.elementId||i.descriptors.menuButton.setId(o),"aria-label":d,"aria-expanded":c,onClick:e=>{t.props?.onClick?.(e),s()}}):null},f=(e,t,r)=>{let n=r?.countSelf?e.tagName:"",o=e;for(;o&&"BUTTON"!==n.toUpperCase();)o=o["prev"===t?"previousElementSibling":"nextElementSibling"],n=o?.tagName??"";return o},b=e=>{let{sx:t,asPortal:r,...o}=e,{popoverCtx:l,elementId:d}=m(),{floating:p,styles:h,isOpen:g,context:b,nodeId:v}=l,y=(0,a.useRef)(null);return(0,a.useLayoutEffect)(()=>{p(y.current)},[g]),(0,n.tZ)(u.J,{context:b,nodeId:v,isOpen:g,portal:r,children:(0,n.tZ)(i.Col,{elementDescriptor:i.descriptors.menuList,elementId:i.descriptors.menuList.setId(d),ref:y,role:"menu",onKeyDown:e=>{let t=y.current;if(t&&t===document.activeElement&&"ArrowDown"===e.key)return e.preventDefault(),f(t.children[0],"next",{countSelf:!0})?.focus()},sx:[e=>({backgroundColor:c.O.makeSolid(e.colors.$colorBackground),borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha50,outline:"none",borderRadius:e.radii.$md,padding:e.space.$0x5,overflow:"hidden",top:`calc(100% + ${e.space.$2})`,animation:`${s.animations.dropdownSlideInScaleAndFade} ${e.transitionDuration.$slower} ${e.transitionTiming.$slowBezier}`,transformOrigin:"top center",boxShadow:e.shadows.$menuShadow,zIndex:e.zIndices.$dropdown,gap:e.space.$0x5}),t],style:h,...o})})},v=e=>{let{sx:t,onClick:r,destructive:o,closeAfterClick:l=!0,...s}=e,{popoverCtx:c,elementId:d}=m(),{toggle:u}=c,p=(0,a.useRef)(null);return(0,n.tZ)(i.SimpleButton,{ref:p,elementDescriptor:i.descriptors.menuItem,elementId:i.descriptors.menuItem.setId(d),hoverAsFocus:!0,variant:"ghost",colorScheme:o?"danger":"neutral",role:"menuitem",onKeyDown:e=>{let t=p.current;if(!t)return;let r=e.key;if("ArrowUp"!==r&&"ArrowDown"!==r)return;e.preventDefault();let n=f(t,"ArrowUp"===r?"prev":"next");n?.focus()},focusRing:!1,onClick:e=>{r?.(e),l&&u()},sx:[e=>({justifyContent:"start",borderRadius:e.radii.$sm,padding:`${e.space.$1} ${e.space.$3}`}),t],...s})}},6459:function(e,t,r){r.r(t),r.d(t,{Modal:()=>g,ModalContext:()=>p,_:()=>m,useUnsafeModalContext:()=>h});var n=r(9109),o=r(3799),a=r(9144),i=r(9541),l=r(2464),s=r(2505),c=r(1201),d=r(2672),u=r(4045);let[p,m,h]=(0,o.uH)("ModalContext"),g=(0,d.withFloatingTree)(e=>{let{disableScrollLock:t,enableScrollLock:r}=(0,s.P)(),{handleClose:d,handleOpen:m,contentSx:h,containerSx:g,canCloseModal:f,id:b,style:v,portalRoot:y}=e,w=(0,a.useRef)(null),{floating:x,isOpen:S,context:$,nodeId:C,toggle:_}=(0,l.Sv)({defaultOpen:!0,autoUpdate:!1,outsidePress:e=>e.target===w.current,canCloseModal:f});a.useEffect(()=>{S?m?.():d?.()},[S]);let k=a.useMemo(()=>({value:!1===f?{}:{toggle:_}}),[_,f]);return(0,o.Gw)(()=>(r(),()=>{t()}),[]),(0,n.tZ)(u.J,{nodeId:C,context:$,isOpen:S,outsideElementsInert:!0,root:y,children:(0,n.tZ)(p.Provider,{value:k,children:(0,n.tZ)(i.Flex,{id:b,ref:w,elementDescriptor:i.descriptors.modalBackdrop,style:v,sx:[e=>({animation:`${c.animations.fadeIn} 150ms ${e.transitionTiming.$common}`,zIndex:e.zIndices.$modal,backgroundColor:e.colors.$modalBackdrop,alignItems:"flex-start",justifyContent:"center",overflow:"auto",width:"100vw",height:["100vh","-webkit-fill-available"],position:"fixed",left:0,top:0}),g],children:(0,n.tZ)(i.Flex,{elementDescriptor:i.descriptors.modalContent,ref:x,"aria-modal":"true",role:"dialog",sx:[e=>({position:"relative",outline:0,animation:`${c.animations.modalSlideAndFade} 180ms ${e.transitionTiming.$easeOut}`,margin:`${e.space.$16} 0`,[c.mqu.sm]:{margin:`${e.space.$10} 0`}}),h],children:e.children})})})})})},5579:function(e,t,r){r.d(t,{Uh:()=>y,ap:()=>C,jh:()=>v,l2:()=>w});var n=r(9109),o=r(3799),a=r(9144),i=r(9541),l=r(2464),s=r(4174),c=r(4676),d=r(1201),u=r(7623),p=r(4455),m=r(2672),h=r(7434),g=r(4045);let[f,b,v]=(0,o.uH)("NavbarContext"),y=({children:e,contentRef:t})=>{let[r,o]=a.useState(!1),i=a.useCallback(()=>o(!0),[]),l=a.useCallback(()=>o(!1),[]),s=a.useMemo(()=>({value:{isOpen:r,open:i,close:l,contentRef:t}}),[r]);return(0,n.tZ)(f.Provider,{value:s,children:e})},w=e=>{let{contentRef:t,title:r,description:o,routes:s,header:d}=e,{close:u}=b(),{navigate:p}=(0,c.useRouter)(),{navigateToFlowStart:m}=(0,l.zk)(),h=(0,c.useRouter)(),g=e=>e?.external?()=>p(e.path):()=>f(e),f=async e=>{t.current&&(u(),"/"===e.path?await m():await p(e.path))},v=(0,a.useCallback)(e=>{if(e.external)return!1;let t=h.currentPath===h.fullPath&&"/"===e.path,r=h.matches(e.path);return t||r},[h.currentPath]),y=(0,n.tZ)(i.Col,{elementDescriptor:i.descriptors.navbarButtons,sx:e=>({gap:e.space.$0x5}),children:s.map(e=>(0,n.tZ)($,{elementDescriptor:i.descriptors.navbarButton,elementId:i.descriptors.navbarButton.setId(e.id),iconElementDescriptor:i.descriptors.navbarButtonIcon,iconElementId:i.descriptors.navbarButtonIcon.setId(e.id),onClick:g(e),icon:e.icon,isActive:v(e),sx:e=>({padding:`${e.space.$1x5} ${e.space.$3}`,minHeight:e.space.$8}),children:(0,n.tZ)(i.Span,{elementDescriptor:i.descriptors.navbarButtonText,elementId:i.descriptors.navbarButtonText.setId(e.id),localizationKey:e.name})},e.id))});return(0,n.BX)(n.HY,{children:[(0,n.BX)(x,{title:r,description:o,children:[d,y]}),(0,n.BX)(S,{children:[d,y]})]})},x=e=>{let{title:t,description:r}=e;return(0,n.BX)(i.Col,{elementDescriptor:i.descriptors.navbar,sx:e=>({[d.mqu.md]:{display:"none"},flex:`0 0 ${e.space.$57}`,width:e.sizes.$57,position:"relative",maxWidth:e.space.$57,background:d.common.mergedColorsBackground(u.O9.setAlpha(e.colors.$colorBackground,1),e.colors.$neutralAlpha50),padding:`${e.space.$6} ${e.space.$5} ${e.space.$4} ${e.space.$3}`,marginRight:`-${e.space.$2}`,color:e.colors.$colorText,justifyContent:"space-between"}),children:[(0,n.tZ)(h.W,{}),(0,n.BX)(i.Col,{sx:e=>({gap:e.space.$6,flex:`0 0 ${e.space.$60}`}),children:[(0,n.BX)(i.Col,{sx:e=>({gap:e.space.$0x5,padding:`${e.space.$none} ${e.space.$3}`}),children:[(0,n.tZ)(i.Heading,{as:"h1",localizationKey:t}),(0,n.tZ)(i.Text,{colorScheme:"secondary",localizationKey:r})]}),e.children]}),(0,n.tZ)(p.Z.ClerkAndPagesTag,{sx:{width:"fit-content"}})]})},S=(0,m.withFloatingTree)(e=>{let t=b(),{floating:r,isOpen:o,open:s,close:c,nodeId:p,context:m}=(0,l.Sv)({defaultOpen:!1,autoUpdate:!1});return a.useEffect(()=>{t.isOpen?s():c()},[t.isOpen]),a.useEffect(()=>{o||t.close()},[o]),(0,n.tZ)(g.J,{nodeId:p,context:m,isOpen:o,portal:!1,children:(0,n.tZ)(i.Col,{sx:e=>({position:"absolute",top:0,bottom:0,width:"100%",zIndex:e.zIndices.$navbar,overflow:"hidden",color:e.colors.$colorText}),children:(0,n.tZ)(i.Col,{ref:r,elementDescriptor:i.descriptors.navbar,tabIndex:0,sx:e=>({outline:0,position:"absolute",top:0,bottom:0,width:e.space.$60,backgroundColor:u.O9.makeSolid(e.colors.$colorBackground),borderTopRightRadius:e.radii.$lg,borderBottomRightRadius:e.radii.$lg,padding:`${e.space.$10} ${e.space.$6}`,animation:`${d.animations.navbarSlideIn} ${e.transitionDuration.$slower} ${e.transitionTiming.$slowBezier}`,borderRightWidth:e.borderWidths.$normal,borderRightStyle:e.borderStyles.$solid,borderRightColor:e.colors.$neutralAlpha150,boxShadow:e.shadows.$cardContentShadow}),children:e.children})})})}),$=e=>{let{icon:t,children:r,isActive:o,iconElementDescriptor:a,iconElementId:l,sx:s,...c}=e;return(0,n.BX)(i.Button,{variant:"unstyled",colorScheme:o?"primary":"neutral",textVariant:"buttonLarge",size:"md",isActive:o,focusRing:!1,...c,sx:[e=>({gap:e.space.$3,justifyContent:"flex-start",backgroundColor:o?e.colors.$neutralAlpha100:void 0,color:o?e.colors.$primary500:e.colors.$neutralAlpha600,"&:hover":{backgroundColor:o?void 0:e.colors.$neutralAlpha25},"&:focus":{backgroundColor:o?void 0:e.colors.$neutralAlpha50},opacity:o?1:.6}),s],children:[(0,n.tZ)(i.Icon,{elementDescriptor:a,elementId:l,icon:t,sx:{opacity:o?1:.7,transform:"translateZ(0)"}}),r]})},C=({navbarTitleLocalizationKey:e,...t})=>{let{open:r}=v(),{t:o}=(0,i.useLocalizations)();return r?(0,n.tZ)(i.Flex,{elementDescriptor:i.descriptors.navbarMobileMenuRow,sx:e=>({display:"none",background:d.common.mergedColorsBackground(u.O9.setAlpha(e.colors.$colorBackground,1),e.colors.$neutralAlpha50),padding:`${e.space.$2} ${e.space.$3} ${e.space.$4} ${e.space.$3}`,marginBottom:`-${e.space.$2}`,[d.mqu.md]:{display:"flex"}}),children:(0,n.BX)(i.Button,{elementDescriptor:i.descriptors.navbarMobileMenuButton,...t,onClick:r,size:"xs",textVariant:"h2",variant:"ghost",sx:e=>({color:e.colors.$colorText,gap:e.space.$2x5,width:"fit-content",alignItems:"center",justifyContent:"center"}),children:[(0,n.tZ)(i.Icon,{elementDescriptor:i.descriptors.navbarMobileMenuButtonIcon,icon:s.v2,size:"md"}),o(e)]})}):null}},5623:function(e,t,r){r.d(t,{l:()=>a});var n=r(9109),o=r(9629);let a=e=>{let{name:t="",imageUrl:r,...a}=e;return(0,n.tZ)(o.q,{title:t,initials:(t||" ")[0],imageUrl:r,rounded:!1,...a})}},5973:function(e,t,r){r.d(t,{Z:()=>l});var n=r(9109);r(9144);var o=r(9541),a=r(5872),i=r(5623);let l=e=>{let{organization:t,size:r="md",icon:l,rounded:s=!1,fetchRoles:c=!1,badge:d,sx:u,user:p,avatarSx:m,mainIdentifierSx:h,mainIdentifierVariant:g,elementId:f,...b}=e,{localizeCustomRole:v}=(0,a.q)(),{options:y}=(0,a.e)(c),w=p?.organizationMemberships.find(e=>e.organization.id===t.id),x=y?.find(e=>e.value===w?.role)?.label,S=v(w?.role)||x;return(0,n.BX)(o.Flex,{elementDescriptor:o.descriptors.organizationPreview,elementId:o.descriptors.organizationPreview.setId(f),gap:3,align:"center",as:"span",sx:[{minWidth:"0"},u],...b,children:[(0,n.BX)(o.Flex,{elementDescriptor:o.descriptors.organizationPreviewAvatarContainer,elementId:o.descriptors.organizationPreviewAvatarContainer.setId(f),justify:"center",as:"span",sx:{position:"relative"},children:[(0,n.tZ)(i.l,{boxElementDescriptor:o.descriptors.organizationPreviewAvatarBox,imageElementDescriptor:o.descriptors.organizationPreviewAvatarImage,...t,size:e=>({xs:e.sizes.$5,sm:e.sizes.$8,md:e.sizes.$9,lg:e.sizes.$12})[r],sx:m,rounded:s}),l&&(0,n.tZ)(o.Flex,{sx:{position:"absolute",left:0,bottom:0},children:l})]}),(0,n.BX)(o.Flex,{elementDescriptor:o.descriptors.organizationPreviewTextContainer,elementId:o.descriptors.organizationPreviewTextContainer.setId(f),direction:"col",justify:"center",as:"span",sx:{minWidth:"0px",textAlign:"left"},children:[(0,n.BX)(o.Text,{elementDescriptor:o.descriptors.organizationPreviewMainIdentifier,elementId:o.descriptors.organizationPreviewMainIdentifier.setId(f),variant:g||({xs:"subtitle",sm:"caption",md:"subtitle",lg:"h1"})[r],as:"span",truncate:!0,sx:h,title:t.name,children:[t.name," ",d]}),S&&(0,n.tZ)(o.Text,{elementDescriptor:o.descriptors.organizationPreviewSecondaryIdentifier,elementId:o.descriptors.organizationPreviewSecondaryIdentifier.setId(f),as:"span",localizationKey:S,truncate:!0})]})]})}},3638:function(e,t,r){r.d(t,{t:()=>m});var n=r(9109);r(9144);var o=r(9541),a=r(4174),i=r(1201),l=r(7623);let s=e=>{let{sx:t,isActive:r,icon:a,children:i,...l}=e;return(0,n.BX)(o.Button,{size:"xs",variant:"outline",sx:e=>[{color:e.colors.$colorText,opacity:r?1:e.opacity.$inactive,padding:`${e.space.$0x5} ${e.space.$0x5}`},t],elementDescriptor:o.descriptors.paginationButton,...l,children:[a&&(0,n.tZ)(o.Icon,{size:"md",elementDescriptor:o.descriptors.paginationButtonIcon,icon:e.icon}),i]})},c=e=>{let{rowInfo:{startingRow:t,endingRow:r,allRowsCount:a}}=e;return(0,n.BX)(o.Text,{children:[(0,n.tZ)(o.Text,{as:"span",elementDescriptor:o.descriptors.paginationRowText,elementId:o.descriptors.paginationRowText?.setId("displaying"),sx:e=>({opacity:e.opacity.$inactive}),localizationKey:(0,o.localizationKeys)("paginationRowText__displaying"),colorScheme:"secondary"})," ",(0,n.tZ)(o.Text,{as:"span",elementDescriptor:o.descriptors.paginationRowText,elementId:o.descriptors.paginationRowText?.setId("rowsCount"),sx:e=>({fontWeight:e.fontWeights.$medium}),children:t===r&&[0,1].includes(t)?t:`${t} – ${r}`})," ",(0,n.tZ)(o.Text,{as:"span",elementDescriptor:o.descriptors.paginationRowText,elementId:o.descriptors.paginationRowText?.setId("displaying"),sx:e=>({opacity:e.opacity.$inactive}),localizationKey:(0,o.localizationKeys)("paginationRowText__of")})," ",(0,n.tZ)(o.Text,{as:"span",elementDescriptor:o.descriptors.paginationRowText,elementId:o.descriptors.paginationRowText?.setId("allRowsCount"),children:a})]})},d=(e,t,r,n)=>Math.abs(e-t)<=r||t===n||1===t,u=(e,t,r)=>Math.abs(e-t)===r+1,p=()=>(0,n.tZ)(o.Flex,{center:!0,children:(0,n.tZ)(o.Text,{colorScheme:"secondary",children:"..."})}),m=e=>{let{page:t,count:r,rowInfo:m,siblingCount:h=1,onChange:g}=e,{t:f}=(0,o.useLocalizations)();return(0,n.BX)(o.Flex,{justify:m?"between":"center",align:"center",sx:e=>({fontSize:e.fontSizes.$sm,"*":{fontSize:"inherit"},[i.mqu.sm]:{flexDirection:"column",gap:e.space.$2}}),children:[m&&(0,n.tZ)(c,{rowInfo:m}),(0,n.BX)(o.Flex,{gap:2,align:"center",sx:{display:"inline-flex"},children:[(0,n.tZ)(s,{isDisabled:t<=1,icon:a.W,"aria-label":f((0,o.localizationKeys)("paginationButton__previous")),onClick:()=>{g?.(t-1)}}),(0,l.w6)(1,r).map(e=>d(t,e,h,r)?(0,n.tZ)(s,{isActive:e===t,variant:"ghost",onClick:()=>{g?.(e)},children:e},e):u(t,e,h)?(0,n.tZ)(p,{},e):null),(0,n.tZ)(s,{isDisabled:t>=r||t<1,icon:a.TZ,"aria-label":f((0,o.localizationKeys)("paginationButton__next")),onClick:()=>{g?.(t+1)}})]})]})}},2915:function(e,t,r){r.d(t,{g:()=>a});var n=r(9109),o=r(7295);let a=e=>(0,n.tZ)(o.E,{elementId:"personalWorkspace",rounded:!1,...e})},8191:function(e,t,r){r.d(t,{h5:()=>i,nX:()=>l,ug:()=>o});let n=[["United States","us","1","(...) ...-....",100],["United Kingdom","gb","44",".... ......",100],["India","in","91",".....-.....",100],["Canada","ca","1","(...) ...-....",100],["Germany","de","49","... .......",100],["France","fr","33",". .. .. .. ..",100],["Russia","ru","7","... ...-..-..",100],["Afghanistan","af","93"],["Albania","al","355"],["Algeria ","dz","213"],["American Samoa","as","1684"],["Andorra","ad","376"],["Angola","ao","244"],["Anguilla","ai","1264"],["Antigua and Barbuda","ag","1268"],["Argentina","ar","54"],["Armenia","am","374"],["Aruba","aw","297"],["Australia","au","61","... ... ..."],["Austria","at","43"],["Azerbaijan","az","994"],["Bahamas","bs","1242"],["Bahrain","bh","973"],["Bangladesh","bd","880"],["Barbados","bb","1246"],["Belarus","by","375"],["Belgium","be","32","... .. .. .."],["Belize","bz","501"],["Benin","bj","229"],["Bermuda","bm","1441"],["Bhutan","bt","975"],["Bolivia","bo","591"],["Bosnia and Herzegovina","ba","387"],["Botswana","bw","267"],["Brazil","br","55"],["British Indian Ocean Territory","io","246"],["British Virgin Islands","vg","1284"],["Brunei","bn","673"],["Bulgaria","bg","359"],["Burkina Faso","bf","226"],["Burundi","bi","257"],["Cambodia","kh","855"],["Cameroon","cm","237"],["Cape Verde","cv","238"],["Caribbean Netherlands","bq","599"],["Cayman Islands","ky","1345"],["Central African Republic","cf","236"],["Chad","td","235"],["Chile","cl","56"],["China","cn","86","...-....-...."],["Colombia","co","57"],["Comoros","km","269"],["Congo","cd","243"],["Congo","cg","242"],["Cook Islands","ck","682"],["Costa Rica","cr","506","....-...."],["C\xf4te d’Ivoire","ci","225"],["Croatia","hr","385"],["Cuba","cu","53"],["Cura\xe7ao","cw","599"],["Cyprus","cy","357"],["Czech Republic","cz","420"],["Denmark","dk","45",".. .. .. .."],["Djibouti","dj","253"],["Dominica","dm","1767"],["Dominican Republic","do","1"],["Ecuador","ec","593"],["Egypt","eg","20"],["El Salvador","sv","503","....-...."],["Equatorial Guinea","gq","240"],["Eritrea","er","291"],["Estonia","ee","372"],["Ethiopia","et","251"],["Falkland Islands","fk","500"],["Faroe Islands","fo","298"],["Fiji","fj","679"],["Finland","fi","358",".. ... .. .."],["French Guiana","gf","594"],["French Polynesia","pf","689"],["Gabon","ga","241"],["Gambia","gm","220"],["Georgia","ge","995"],["Ghana","gh","233"],["Gibraltar","gi","350"],["Greece","gr","30","... ......."],["Greenland","gl","299"],["Grenada","gd","1473"],["Guadeloupe","gp","590"],["Guam","gu","1671"],["Guatemala","gt","502","....-...."],["Guinea","gn","224"],["Guinea-Bissau","gw","245"],["Guyana","gy","592"],["Haiti","ht","509","....-...."],["Honduras","hn","504"],["Hong Kong","hk","852",".... ...."],["Hungary","hu","36"],["Iceland","is","354","... ...."],["Indonesia","id","62"],["Iran","ir","98"],["Iraq","iq","964"],["Ireland","ie","353",".. ......."],["Israel","il","972"],["Italy","it","39","... ......"],["Jamaica","jm","1876"],["Japan","jp","81","... .. ...."],["Jordan","jo","962"],["Kazakhstan","kz","7","... ...-..-.."],["Kenya","ke","254"],["Kiribati","ki","686"],["Kuwait","kw","965"],["Kyrgyzstan","kg","996"],["Laos","la","856"],["Latvia","lv","371"],["Lebanon","lb","961"],["Lesotho","ls","266"],["Liberia","lr","231"],["Libya","ly","218"],["Liechtenstein","li","423"],["Lithuania","lt","370"],["Luxembourg","lu","352"],["Macau","mo","853"],["Macedonia","mk","389"],["Madagascar","mg","261"],["Malawi","mw","265"],["Malaysia","my","60","..-....-...."],["Maldives","mv","960"],["Mali","ml","223"],["Malta","mt","356"],["Marshall Islands","mh","692"],["Martinique","mq","596"],["Mauritania","mr","222"],["Mauritius","mu","230"],["Mexico","mx","52"],["Micronesia","fm","691"],["Moldova","md","373"],["Monaco","mc","377"],["Mongolia","mn","976"],["Montenegro","me","382"],["Montserrat","ms","1664"],["Morocco","ma","212"],["Mozambique","mz","258"],["Myanmar","mm","95"],["Namibia","na","264"],["Nauru","nr","674"],["Nepal","np","977"],["Netherlands","nl","31",".. ........"],["New Caledonia","nc","687"],["New Zealand","nz","64","...-...-...."],["Nicaragua","ni","505"],["Niger","ne","227"],["Nigeria","ng","234"],["Niue","nu","683"],["Norfolk Island","nf","672"],["North Korea","kp","850"],["Northern Mariana Islands","mp","1670"],["Norway","no","47","... .. ..."],["Oman","om","968"],["Pakistan","pk","92","...-......."],["Palau","pw","680"],["Palestine","ps","970"],["Panama","pa","507"],["Papua New Guinea","pg","675"],["Paraguay","py","595"],["Peru","pe","51"],["Philippines","ph","63","... ...."],["Poland","pl","48","...-...-..."],["Portugal","pt","351"],["Puerto Rico","pr","1"],["Qatar","qa","974"],["R\xe9union","re","262"],["Romania","ro","40"],["Rwanda","rw","250"],["Saint Barth\xe9lemy","bl","590"],["Saint Helena","sh","290"],["Saint Kitts and Nevis","kn","1869"],["Saint Lucia","lc","1758"],["Saint Martin","mf","590"],["Saint Pierre and Miquelon","pm","508"],["Saint Vincent and the Grenadines","vc","1784"],["Samoa","ws","685"],["San Marino","sm","378"],["S\xe3o Tom\xe9 and Pr\xedncipe","st","239"],["Saudi Arabia","sa","966"],["Senegal","sn","221"],["Serbia","rs","381"],["Seychelles","sc","248"],["Sierra Leone","sl","232"],["Singapore","sg","65","....-...."],["Sint Maarten","sx","1721"],["Slovakia","sk","421"],["Slovenia","si","386"],["Solomon Islands","sb","677"],["Somalia","so","252"],["South Africa","za","27"],["South Korea","kr","82"],["South Sudan","ss","211"],["Spain","es","34","... ... ..."],["Sri Lanka","lk","94"],["Sudan","sd","249"],["Suriname","sr","597"],["Swaziland","sz","268"],["Sweden","se","46",".. ... .. .."],["Switzerland","ch","41",".. ... .. .."],["Syria","sy","963"],["Taiwan","tw","886"],["Tajikistan","tj","992"],["Tanzania","tz","255"],["Thailand","th","66"],["Timor-Leste","tl","670"],["Togo","tg","228"],["Tokelau","tk","690"],["Tonga","to","676"],["Trinidad and Tobago","tt","1868"],["Tunisia","tn","216"],["Turkey","tr","90","... ... .. .."],["Turkmenistan","tm","993"],["Turks and Caicos Islands","tc","1649"],["Tuvalu","tv","688"],["U.S. Virgin Islands","vi","1340"],["Uganda","ug","256"],["Ukraine","ua","380"],["United Arab Emirates","ae","971"],["Uruguay","uy","598"],["Uzbekistan","uz","998"],["Vanuatu","vu","678"],["Vatican City","va","39",".. .... ...."],["Venezuela","ve","58"],["Vietnam","vn","84"],["Wallis and Futuna","wf","681"],["Yemen","ye","967"],["Zambia","zm","260"],["Zimbabwe","zw","263"]],o={us:new Set(["907","205","251","256","334","479","501","870","480","520","602","623","928","209","213","310","323","408","415","510","530","559","562","619","626","650","661","707","714","760","805","818","831","858","909","916","925","949","951","303","719","970","203","860","202","302","239","305","321","352","386","407","561","727","772","813","850","863","904","941","954","229","404","478","706","770","912","808","319","515","563","641","712","208","217","309","312","618","630","708","773","815","847","219","260","317","574","765","812","316","620","785","913","270","502","606","859","225","318","337","504","985","413","508","617","781","978","301","410","207","231","248","269","313","517","586","616","734","810","906","989","218","320","507","612","651","763","952","314","417","573","636","660","816","228","601","662","406","252","336","704","828","910","919","701","308","402","603","201","609","732","856","908","973","505","575","702","775","212","315","516","518","585","607","631","716","718","845","914","216","330","419","440","513","614","740","937","405","580","918","503","541","215","412","570","610","717","724","814","401","803","843","864","605","423","615","731","865","901","931","210","214","254","281","325","361","409","432","512","713","806","817","830","903","915","936","940","956","972","979","435","801","276","434","540","703","757","804","802","206","253","360","425","509","262","414","608","715","920","304","307"]),ca:new Set(["204","226","236","249","250","289","306","343","365","387","403","416","418","431","437","438","450","506","514","519","548","579","581","587","604","613","639","647","672","705","709","742","778","780","782","807","819","825","867","873","902","905"])},a=([e,t,r,n="",o=0])=>({name:e,iso:t,code:r,pattern:n,priority:o}),i=n.reduce((e,t)=>e.set(t[1],a(t)),new Map),l=n.reduce((e,t)=>{let r=t[2],n=a(t);return e.has(r)?e.get(r)?.push(n):e.set(r,[n]),e},new Map)},4045:function(e,t,r){r.d(t,{J:()=>a});var n=r(9109),o=r(7126);r(9144);let a=e=>{let{context:t,initialFocus:r,outsideElementsInert:a=!1,order:i=["reference","content"],nodeId:l,isOpen:s,portal:c=!0,root:d,children:u}=e;return c?(0,n.tZ)(o.mN,{id:l,children:(0,n.tZ)(o.ll,{root:d,children:s&&(0,n.tZ)(o.wD,{context:t,initialFocus:r,outsideElementsInert:a,order:i,children:(0,n.tZ)(n.HY,{children:u})})})}):(0,n.tZ)(o.mN,{id:l,children:s&&(0,n.tZ)(o.wD,{context:t,initialFocus:r,order:i,children:(0,n.tZ)(n.HY,{children:u})})})}},1151:function(e,t,r){r.d(t,{f:()=>d});var n=r(9109),o=r(9144),a=r(1576),i=r(9541),l=r(1201),s=r(7623),c=r(4455);let d={Root:o.forwardRef((e,t)=>{let{elementDescriptor:r,shouldEntryAnimate:o=!0,...a}=e;return(0,n.tZ)(i.Flow.Part,{part:"popover",children:(0,n.tZ)(c.Z.Root,{elementDescriptor:[i.descriptors.popoverBox,r],...a,ref:t,sx:[e=>({width:e.sizes.$94,maxWidth:`calc(100vw - ${e.sizes.$8})`,zIndex:e.zIndices.$modal,borderRadius:e.radii.$xl,outline:"none"}),e=>({animation:o?`${l.animations.dropdownSlideInScaleAndFade} ${e.transitionDuration.$fast}`:void 0})],children:e.children})})}),Content:e=>{let{sx:t,...r}=e;return(0,n.tZ)(i.Flex,{direction:"col",sx:[e=>({backgroundColor:e.colors.$colorBackground,overflow:"hidden",borderRadius:e.radii.$lg,zIndex:e.zIndices.$card,borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha50,boxShadow:e.shadows.$cardContentShadow}),t],...r,children:e.children})},Footer:e=>{let{sx:t,children:r,...o}=e,{branded:d}=(0,a.useEnvironment)().displayConfig,{privacyPageUrl:u,termsPageUrl:p,helpPageUrl:m}=(0,i.useAppearance)().parsedLayout;return(0,n.BX)(i.Col,{justify:"between",sx:[e=>({background:l.common.mergedColorsBackground(s.O9.setAlpha(e.colors.$colorBackground,1),e.colors.$neutralAlpha50),marginTop:`-${e.space.$2}`,paddingTop:e.space.$2,"&:empty":{padding:0,marginTop:0},">:not(:first-of-type)":{padding:`${e.space.$4} ${e.space.$8}`,borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100}}),t],...o,children:[r,(0,n.tZ)(c.Z.ClerkAndPagesTag,{outerSx:e=>({padding:`${e.space.$4} ${e.space.$8}`}),withFooterPages:!!(d||u||p||m),devModeNoticeSx:e=>({padding:e.space.$none}),withDevOverlay:!0})]})}}},7348:function(e,t,r){r.d(t,{h:()=>a});var n=r(9144),o=r(8315);let a=e=>{let t=n.useRef(document.createElement("div"));return n.useEffect(()=>(document.body.appendChild(t.current),()=>{document.body.removeChild(t.current)}),[]),(0,o.createPortal)(e.children,t.current)}},8774:function(e,t,r){r.d(t,{K:()=>i});var n=r(9109),o=r(9541),a=r(2672);let i=e=>{let{sx:t,children:r,icon:i,iconProps:l,showIconOnHover:s=!0,...c}=e,d=(0,a.useCardState)(),{sx:u,...p}=l||{};return(0,n.BX)(o.Button,{variant:"ghost",colorScheme:"neutral",focusRing:!1,block:!0,hoverAsFocus:!0,isDisabled:d.isLoading,sx:[e=>({justifyContent:"space-between",padding:`${e.space.$4} ${e.space.$5}`,borderRadius:0,...s?{":hover > svg":{visibility:"initial"}}:{}}),t],...c,children:[r,i&&(0,n.tZ)(o.Icon,{icon:i,sx:[e=>({color:e.colors.$colorTextSecondary,marginLeft:e.space.$2,visibility:s?"hidden":"initial"}),u],...p})]})}},3394:function(e,t,r){r.d(t,{P:()=>p});var n=r(9109),o=r(9144),a=r(9541),i=r(4676),l=r(1201),s=r(4174),c=r(4455),d=r(3412),u=r(6459);let p={Root:o.forwardRef((e,t)=>{let{sx:r,children:o,...i}=e,{toggle:p}=(0,u.useUnsafeModalContext)();return(0,n.BX)(c.Z.Root,{ref:t,sx:[e=>({width:e.sizes.$220,maxWidth:`calc(100vw - ${e.sizes.$8})`,display:"flex",flexDirection:"row",[l.mqu.md]:{display:"flex",flexDirection:"column"},overflow:"hidden",height:e.sizes.$176,position:"relative"}),r],...i,children:[p&&(0,n.tZ)(a.Box,{sx:e=>({[l.mqu.md]:{padding:`${e.space.$1x5} ${e.space.$2}`,top:e.space.$none,right:e.space.$none},zIndex:e.zIndices.$modal,position:"absolute",top:e.space.$2,right:e.space.$2}),children:(0,n.tZ)(d.h,{elementDescriptor:a.descriptors.modalCloseButton,variant:"ghost","aria-label":"Close modal",onClick:p,icon:(0,n.tZ)(a.Icon,{icon:s.x8,size:"sm"}),sx:e=>({color:e.colors.$colorTextSecondary,padding:e.space.$3})})}),o,(0,n.tZ)(c.Z.Footer,{isProfileFooter:!0,sx:{display:"none",[l.mqu.md]:{display:"flex"}}})]})}),Content:e=>{let{contentRef:t,children:r,scrollBoxId:s}=e,c=(0,i.useRouter)(),d=o.useRef(0);return o.useEffect(()=>{let e=e=>{let t=e.target;t.scrollTop&&(d.current=t.scrollTop)};return t?.current?.addEventListener("scroll",e),()=>t?.current?.removeEventListener("scroll",e)},[]),o.useLayoutEffect(()=>{d.current&&t?.current&&(t.current.scrollTop=d.current)},[c.currentPath]),(0,n.tZ)(a.Col,{elementDescriptor:a.descriptors.scrollBox,sx:e=>({backgroundColor:e.colors.$colorBackground,position:"relative",borderRadius:e.radii.$lg,width:"100%",height:"100%",overflow:"hidden",borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha50,boxShadow:e.shadows.$cardContentShadow}),"data-clerk-profile-scroll-box-root":s,children:(0,n.tZ)(a.Col,{elementDescriptor:a.descriptors.pageScrollBox,sx:e=>({flex:"1",scrollbarGutter:"stable",paddingTop:e.space.$7,paddingBottom:e.space.$7,paddingLeft:e.space.$8,paddingRight:e.space.$6,[l.mqu.sm]:{padding:`${e.space.$8} ${e.space.$5}`},...l.common.maxHeightScroller(e)}),ref:t,children:r})})}}},8350:function(e,t,r){r.d(t,{G:()=>l});var n=r(9109),o=r(9144),a=r(9541),i=r(6735);let l=e=>{let t=(0,a.useAppearance)(),r=c(o.Children.toArray(e.children),e=>(0,n.tZ)(i.i,{},`divider${e}`));return(0,n.tZ)(s,{reverse:"bottom"===t.parsedLayout.socialButtonsPlacement,...e,children:r})},s=e=>{let{children:t,reverse:r}=e;return(0,n.tZ)(n.HY,{children:r?o.Children.toArray(t).reverse():t})},c=(e,t)=>e.reduce((r,n,o)=>o===e.length-1?[...r,n]:[...r,n,t(o)],[])},3929:function(e,t,r){r.d(t,{r:()=>a});var n=r(9109),o=r(9541);let a=e=>(0,n.tZ)(o.Col,{...e,sx:e=>({boxSizing:"border-box",width:"fit-content",color:e.colors.$colorText,fontFamily:e.fonts.$main,fontStyle:e.fontStyles.$normal})})},6990:function(e,t,r){r.d(t,{r:()=>i});var n=r(9109),o=r(9541),a=r(4676);let i=e=>{let{to:t,onClick:r,...i}=e,l=(0,a.useRouter)(),s=l.resolve(t||l.indexPath);return(0,n.tZ)(o.Link,{...i,onClick:e=>(e.preventDefault(),r&&!t)?r(e):l.navigate(s.href),href:s.href})}},9655:function(e,t,r){r.d(t,{zd:()=>p});var n=r(9109),o=r(9144),a=r(9541),i=r(4174),l=r(1201),s=r(6781),c=r(8246),d=r(5158);let u=(0,o.forwardRef)((e,t)=>{let{children:r,leftIcon:o=i.v3,id:l,sx:s,localizationKey:d,...u}=e;return(0,n.tZ)(c.$,{elementDescriptor:a.descriptors.profileSectionPrimaryButton,elementId:a.descriptors.profileSectionPrimaryButton.setId(l),variant:"ghost",sx:[e=>({textWrap:"nowrap",justifyContent:"start",height:e.sizes.$8}),s],textLocalizationKey:d,leftIcon:o,leftIconSx:e=>({width:e.sizes.$4,height:e.sizes.$4}),ref:t,...u,children:r})}),p={Root:e=>{let{title:t,centered:r=!0,children:i,id:s,sx:c,...d}=e,u=(0,o.useRef)(null),[p,h]=(0,o.useState)(0);return(0,o.useLayoutEffect)(()=>{let e=u.current;e&&h(e.clientHeight+e.clientTop||0)},[]),(0,n.BX)(a.Flex,{elementDescriptor:a.descriptors.profileSection,elementId:a.descriptors.profileSection.setId(s),sx:[e=>({flexDirection:"row-reverse",borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100,paddingTop:e.space.$4,paddingBottom:e.space.$4,gap:e.space.$6,[l.mqu.lg]:{flexDirection:"column-reverse",gap:e.space.$2}}),c],...d,children:[(0,n.tZ)(a.Col,{elementDescriptor:a.descriptors.profileSectionContent,elementId:a.descriptors.profileSectionContent.setId(s),gap:2,ref:u,sx:{minWidth:0,width:"100%","+ *":{"--clerk-height":`${p}px`}},children:i}),(0,n.tZ)(a.Col,{elementDescriptor:a.descriptors.profileSectionHeader,elementId:a.descriptors.profileSectionHeader.setId(s),sx:e=>({padding:r?void 0:`${e.space.$1x5} 0`,gap:e.space.$1,width:e.space.$66,alignSelf:p?"self-start":r?"center":void 0,marginTop:r?"calc(var(--clerk-height)/2)":void 0,transform:p&&r?"translateY(-50%)":void 0,[l.mqu.lg]:{alignSelf:"self-start",marginTop:"unset",transform:"none",padding:0}}),children:(0,n.tZ)(m,{localizationKey:t,elementDescriptor:a.descriptors.profileSectionTitle,elementId:a.descriptors.profileSectionTitle.setId(s),textElementDescriptor:a.descriptors.profileSectionTitleText,textElementId:a.descriptors.profileSectionTitleText.setId(s)})})]})},ItemList:e=>{let{children:t,id:r,disableAnimation:o=!1,...i}=e,l=(0,n.tZ)(a.Col,{elementDescriptor:a.descriptors.profileSectionItemList,elementId:a.descriptors.profileSectionItemList.setId(r),sx:e=>({gap:e.space.$0x5}),...i,children:t});return o?l:(0,n.tZ)(s.f,{asChild:!0,children:l})},Item:e=>{let{children:t,id:r,sx:o,hoverable:i,...l}=e;return(0,n.tZ)(a.Flex,{elementDescriptor:a.descriptors.profileSectionItem,elementId:a.descriptors.profileSectionItem.setId(r),sx:[e=>({justifyContent:"space-between",width:"100%",alignItems:"center",padding:`${e.space.$1} ${e.space.$1} ${e.space.$1} ${e.space.$2x5}`,gap:e.space.$2,...i&&{borderRadius:e.radii.$lg,":hover":{backgroundColor:e.colors.$neutralAlpha50}}}),o],...l,children:t})},Button:e=>{let{children:t,id:r,sx:o,...i}=e;return(0,n.tZ)(a.Button,{elementDescriptor:a.descriptors.profileSectionPrimaryButton,elementId:a.descriptors.profileSectionPrimaryButton.setId(r),variant:"ghost",sx:[e=>({whiteSpace:"nowrap",justifyContent:"start",gap:e.space.$2,padding:`${e.space.$1x5} ${e.space.$3} ${e.space.$1x5} ${e.space.$2x5}`}),o],...i,children:t})},ArrowButton:u,ActionMenu:e=>{let{children:t,triggerLocalizationKey:r,id:o,triggerSx:l,onClick:s}=e;return(0,n.tZ)(a.Flex,{sx:{position:"relative"},children:(0,n.BX)(d.v2,{elementId:o,children:[(0,n.tZ)(d.bF,{children:(0,n.tZ)(u,{id:o,textLocalizationKey:r,sx:[e=>({justifyContent:"start",height:e.sizes.$8}),l],leftIcon:i.v3,leftIconSx:e=>({width:e.sizes.$4,height:e.sizes.$4}),onClick:s})}),(0,n.tZ)(d.qy,{asPortal:!1,sx:e=>({width:"100%",padding:e.space.$1}),children:t})]})})},ActionMenuItem:e=>{let{children:t,isLoading:r,localizationKey:i,sx:l,leftIcon:s,leftIconSx:c,...u}=e,p=(0,o.isValidElement)(s);return(0,n.BX)(d.sN,{sx:[e=>({padding:`${e.space.$1x5} ${e.space.$2}`}),l],isLoading:r,...u,children:[(r||s)&&(0,n.tZ)(a.Flex,{as:"span",sx:e=>({flex:`0 0 ${e.space.$4}`}),children:r?(0,n.tZ)(a.Spinner,{elementDescriptor:a.descriptors.spinner,size:"sm"}):!p&&s?(0,n.tZ)(a.Icon,{icon:s,sx:[e=>({color:e.colors.$neutralAlpha600,width:e.sizes.$5}),c]}):s}),(0,n.tZ)(a.Text,{localizationKey:i})]})}},m=e=>{let{textElementDescriptor:t,textElementId:r,localizationKey:o,...i}=e;return(0,n.tZ)(a.Flex,{...i,children:(0,n.tZ)(a.Text,{localizationKey:o,variant:"subtitle",elementDescriptor:t,elementId:r})})}},7291:function(e,t,r){r.d(t,{s:()=>d});var n=r(9109),o=r(7126),a=r(9144),i=r(9541);let l=(0,a.createContext)(null),s=a.forwardRef(({children:e,value:t,defaultValue:r,onChange:s,"aria-label":c,"aria-labelledby":d,size:u="md",fullWidth:p=!1,sx:m},h)=>{let[g,f]=(0,a.useState)(r),b=void 0!==t;return(0,n.tZ)(l.Provider,{value:{currentValue:b?t:g,onValueChange:e=>{b||f(e),s?.(e)},size:u,fullWidth:p},children:(0,n.tZ)(o.bU,{orientation:"horizontal",role:"radiogroup",loop:!1,render:(0,n.tZ)(i.Flex,{ref:h,elementDescriptor:i.descriptors.segmentedControlRoot,"aria-label":c,"aria-labelledby":d,sx:[e=>({backgroundColor:e.colors.$neutralAlpha50,borderRadius:e.radii.$md,borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha100,isolation:"isolate"}),m]}),children:e})})});s.displayName="SegmentedControl.Root";let c=a.forwardRef(({text:e,value:t},r)=>{let{currentValue:s,onValueChange:c,size:d,fullWidth:u}=function(){let e=(0,a.useContext)(l);if(!e)throw Error("SegmentedControl.Button must be used within SegmentedControl.Root");return e}(),p=t===s;return(0,n.tZ)(o.A8,{render:o=>(0,n.tZ)(i.SimpleButton,{ref:r,...o,localizationKey:e,elementDescriptor:i.descriptors.segmentedControlButton,variant:"unstyled",role:"radio","aria-checked":p,onClick:()=>c(t),isActive:p,sx:e=>({position:"relative",width:u?"100%":"auto",backgroundColor:p?e.colors.$colorBackground:"transparent",color:p?e.colors.$colorText:e.colors.$colorTextSecondary,fontSize:"lg"===d?e.fontSizes.$md:e.fontSizes.$xs,minHeight:e.sizes.$6,boxShadow:p?e.shadows.$segmentedControl:"none",borderRadius:`calc(${e.radii.$md} - ${e.borderWidths.$normal})`,zIndex:1,":focus-visible":{zIndex:2}})})})});c.displayName="SegmentedControl.Button";let d={Root:s,Button:c}},3146:function(e,t,r){r.d(t,{Ph:()=>b,UN:()=>S,pp:()=>x});var n=r(9109),o=r(3799),a=r(9144),i=r(9541),l=r(2464),s=r(4174),c=r(1201),d=r(7623),u=r(2672),p=r(4045);let[m,h]=(0,o.uH)("SelectState"),g=(e,t,r)=>(0,n.tZ)(i.Flex,{sx:e=>({width:"100%",padding:`${e.space.$2} ${e.space.$4}`,margin:`0 ${e.space.$1}`,borderRadius:e.radii.$md,...r&&{backgroundColor:e.colors.$neutralAlpha100},"&:hover":{backgroundColor:e.colors.$neutralAlpha100}}),children:(0,n.tZ)(i.Text,{truncate:!0,children:e.label||e.value})}),f=e=>e.label||e.value,b=(0,u.withFloatingTree)(e=>{let{value:t,options:r,onChange:o,renderOption:i,noResultsMessage:s,comparator:c,placeholder:d="Select an option",searchPlaceholder:u,elementId:p,children:h,portal:b=!1,referenceElement:v=null,...y}=e,w=(0,l.Sv)({autoUpdate:!0,adjustToReferenceWidth:!!v,referenceElement:v}),$=w.toggle,C=a.useRef(null),_=(0,l.dw)({items:r,comparator:c||(()=>!0)}),k=a.useCallback(e=>{o?.(e),$()},[$,o]),P=(0,n.BX)(n.HY,{children:[(0,n.tZ)(x,{}),(0,n.tZ)(S,{})]});return(0,n.tZ)(m.Provider,{value:{value:{popoverCtx:w,searchInputCtx:_,selectedOption:r.find(e=>e.value===t)||null,noResultsMessage:s,focusedItemRef:C,value:t,renderOption:i||g,buttonRenderOption:i||f,placeholder:d,searchPlaceholder:u,comparator:c,select:k,onTriggerClick:$,elementId:p,portal:b}},...y,children:a.Children.count(h)?h:P})}),v=a.memo(a.forwardRef((e,t)=>{let{option:r,renderOption:o,isSelected:l,index:s,handleSelect:c,isFocused:d,elementId:u}=e;return(0,n.tZ)(i.Flex,{ref:t,sx:{userSelect:"none",cursor:"pointer"},onClick:()=>{c(r)},children:a.cloneElement(o(r,s,l),{elementDescriptor:i.descriptors.selectOption,elementId:i.descriptors.selectOption.setId(u),"data-selected":l,"data-focused":d})})})),y=e=>{let{sx:t,...r}=e;a.useEffect(()=>()=>e.onChange({target:{value:""}}),[]);let{elementId:o}=h();return(0,n.tZ)(i.Flex,{sx:e=>({padding:e.space.$0x5}),children:(0,n.tZ)(i.Input,{elementDescriptor:i.descriptors.selectSearchInput,elementId:i.descriptors.selectSearchInput.setId(o),focusRing:!1,variant:"unstyled",sx:[e=>({borderRadius:e.radii.$md,backgroundColor:e.colors.$neutralAlpha100,padding:e.space.$2}),t],...r})})},w=e=>{let{sx:t,...r}=e;return(0,n.tZ)(i.Text,{as:"div",sx:[e=>({width:"100%",padding:`${e.space.$1} ${e.space.$2} ${e.space.$2} ${e.space.$2}`}),t],...r})},x=e=>{let{containerSx:t,sx:r,...o}=e,{popoverCtx:l,searchInputCtx:s,value:u,renderOption:m,searchPlaceholder:g,comparator:f,focusedItemRef:b,noResultsMessage:x,select:S,onTriggerClick:$,elementId:C,portal:_}=h(),{filteredItems:k,searchInputProps:P}=s,[I,A]=(0,a.useState)(0),{isOpen:T,floating:R,styles:B,nodeId:Z,context:z}=l,D=a.useRef(null);return a.useEffect(()=>{if(!T){A(-1);return}b.current?.scrollIntoView?.({block:"nearest"})},[I,T]),a.useEffect(()=>A(0),[k.length]),a.useEffect(()=>{if(f||D?.current?.focus(),T){A(k.findIndex(e=>e.value===u)),b.current?.scrollIntoView?.({block:"nearest"});return}},[T]),(0,n.tZ)(p.J,{nodeId:Z,context:z,isOpen:T,portal:_||!1,order:["content"],children:(0,n.BX)(i.Flex,{elementDescriptor:i.descriptors.selectOptionsContainer,elementId:i.descriptors.selectOptionsContainer.setId(C),ref:R,onKeyDown:e=>"ArrowUp"===e.key?(e.preventDefault(),T)?A((e=0)=>0===e?k.length-1:e-1):$():"ArrowDown"===e.key?(e.preventDefault(),T)?A((e=0)=>e===k.length-1?0:e+1):$():"Enter"===e.key&&I>=0?(e.preventDefault(),S(k[I])):void 0,direction:"col",justify:"start",sx:[e=>({backgroundColor:d.O9.makeSolid(e.colors.$colorBackground),borderRadius:e.radii.$lg,overflow:"hidden",animation:`${c.animations.dropdownSlideInScaleAndFade} ${e.transitionDuration.$slower} ${e.transitionTiming.$slowBezier}`,transformOrigin:"top center",boxShadow:e.shadows.$menuShadow,zIndex:e.zIndices.$dropdown}),r],style:{...B,left:B.left-1},children:[f&&(0,n.tZ)(y,{placeholder:g,...P}),(0,n.BX)(i.Flex,{ref:D,direction:"col",tabIndex:f?void 0:0,sx:[e=>({gap:e.space.$0x5,outline:"none",overflowY:"auto",maxHeight:"18vh",padding:`${e.space.$0x5} ${e.space.$0x5}`}),t],...o,children:[k.map((e,t)=>{let r=t===I,o=u===e.value;return(0,n.tZ)(v,{index:t,ref:r?b:void 0,option:e,renderOption:m,isSelected:o,isFocused:r,handleSelect:S,elementId:C},e.value)}),x&&0===k.length&&(0,n.tZ)(w,{children:x})]})]})})},S=e=>{let{sx:t,children:r,icon:o,iconSx:a,...l}=e,{popoverCtx:c,onTriggerClick:d,buttonRenderOption:u,selectedOption:p,placeholder:m,elementId:g}=h(),{reference:f}=c,b=r;return r||(b=p?u(p):(0,n.tZ)(i.Text,{as:"span",sx:e=>({opacity:e.opacity.$inactive}),children:m})),(0,n.BX)(i.Button,{elementDescriptor:i.descriptors.selectButton,elementId:i.descriptors.selectButton.setId(g),ref:f,variant:"outline",textVariant:"buttonLarge",onClick:d,sx:[e=>({gap:e.space.$2,paddingLeft:e.space.$3x5,paddingRight:e.space.$3x5,alignItems:"center","> *":{pointerEvents:"none"}}),t],...l,children:[b,(0,n.tZ)(i.Icon,{elementDescriptor:i.descriptors.selectButtonIcon,elementId:i.descriptors.selectButtonIcon.setId(g),size:"md",icon:o||s._M,sx:a})]})}},138:function(e,t,r){r.d(t,{L:()=>h});var n=r(9109),o=r(6543),a=r(9144),i=r(8969),l=r(9541),s=r(2464),c=r(1201),d=r(7623),u=r(2672);let p=e=>e.startsWith("web3_"),m=e=>!!(0,o.H)(e),h=a.memo(e=>{let{oauthCallback:t,web3Callback:r,alternativePhoneCodeCallback:o,enableOAuthProviders:a=!0,enableWeb3Providers:h=!0,enableAlternativePhoneCodeProviders:b=!0,idleAfterDelay:v=!0}=e,{web3Strategies:y,authenticatableOauthStrategies:w,strategyToDisplayData:x,alternativePhoneCodeChannels:S}=(0,s.vO)(),$=(0,u.useCardState)(),{socialButtonsVariant:C}=(0,l.useAppearance)().parsedLayout,_=[...a?w:[],...h?y:[],...b?S:[]];if(!_.length)return null;let k=function(e,t){if(e.length<=6)return[e];let r=Math.ceil(e.length/6),n=Math.ceil(e.length/r),o=Array.from({length:r},()=>[]),a=0;for(let t of e)o[a].push(t),o[a].length===n&&a++;return o}([..._],6),P=k.at(0)?.length??0,I="blockButton"===C||"iconButton"!==C&&_.length<=2,A=async e=>{$.setLoading(e);try{p(e)?await r(e):await t(e)}catch{await (0,d._v)(1e3),$.setIdle()}v&&(await (0,d._v)(5e3),$.setIdle())},T=e=>async()=>{m(e)?o(e):await A(e)},R=I?f:g;return(0,n.tZ)(l.Flex,{direction:"col",gap:2,elementDescriptor:l.descriptors.socialButtonsRoot,children:k.map((e,t)=>(0,n.tZ)(l.Grid,{elementDescriptor:l.descriptors.socialButtons,gap:2,sx:r=>({justifyContent:"center",[c.mqu.sm]:{gridTemplateColumns:"repeat(1, 1fr)"},gridTemplateColumns:_.length<1?"repeat(1, 1fr)":`repeat(${e.length}, ${0===t?"1fr":`calc((100% - (${P} - 1) * ${r.sizes.$2}) / ${P})`})`}),children:e.map(e=>{let t=1===_.length?`Continue with ${x[e].name}`:x[e].name,r=1===_.length?(0,l.localizationKeys)("socialButtonsBlockButton",{provider:x[e].name}):(0,l.localizationKeys)("socialButtonsBlockButtonManyInView",{provider:x[e].name}),o=x[e].iconUrl?(0,n.tZ)(l.Image,{elementDescriptor:[l.descriptors.providerIcon,l.descriptors.socialButtonsProviderIcon],elementId:l.descriptors.socialButtonsProviderIcon.setId(x[e].id),isLoading:$.loadingMetadata===e,isDisabled:$.isLoading,src:x[e].iconUrl,alt:`Sign in with ${x[e].name}`,sx:e=>({width:e.sizes.$4,height:"auto",maxWidth:"100%"})}):(0,n.tZ)(i.e_,{id:x[e].id,value:x[e].name,isLoading:$.loadingMetadata===e,isDisabled:$.isLoading});return(0,n.tZ)(R,{id:x[e].id,onClick:T(e),isLoading:$.loadingMetadata===e,isDisabled:$.isLoading,label:t,textLocalizationKey:r,icon:o},e)})},e.join("-")))})}),g=(0,a.forwardRef)((e,t)=>{let{icon:r,label:o,id:a,textLocalizationKey:i,...s}=e;return(0,n.tZ)(l.Button,{ref:t,elementDescriptor:l.descriptors.socialButtonsIconButton,elementId:l.descriptors.socialButtonsIconButton.setId(a),textVariant:"buttonLarge",variant:"outline",colorScheme:"neutral",sx:e=>({minHeight:e.sizes.$8,width:"100%"}),...s,children:r})}),f=(0,a.forwardRef)((e,t)=>{let{id:r,icon:o,isLoading:i,label:s,textLocalizationKey:c,...d}=e,u=(0,a.isValidElement)(o);return(0,n.tZ)(l.SimpleButton,{elementDescriptor:l.descriptors.socialButtonsBlockButton,elementId:l.descriptors.socialButtonsBlockButton.setId(r),variant:"outline",block:!0,isLoading:i,ref:t,...d,sx:t=>[{gap:t.space.$4,position:"relative",justifyContent:"flex-start"},e.sx],children:(0,n.BX)(l.Flex,{justify:"center",align:"center",as:"span",gap:3,sx:{width:"100%",overflow:"hidden"},children:[(i||o)&&(0,n.tZ)(l.Flex,{as:"span",center:!0,sx:e=>({flex:`0 0 ${e.space.$4}`}),children:i?(0,n.tZ)(l.Spinner,{size:"sm",elementDescriptor:l.descriptors.spinner}):!u&&o?(0,n.tZ)(l.Icon,{icon:o,sx:[e=>({color:e.colors.$neutralAlpha600,width:e.sizes.$4,position:"absolute"})]}):o}),(0,n.tZ)(l.Text,{elementDescriptor:l.descriptors.socialButtonsBlockButtonText,elementId:l.descriptors.socialButtonsBlockButtonText.setId(r),as:"span",truncate:!0,variant:"buttonLarge",localizationKey:c,children:s})]})})})},7263:function(e,t,r){r.d(t,{I:()=>c});var n=r(9109);r(9144);var o=r(9541),a=r(2464),i=r(1085),l=r(9460),s=r(2654);let c=e=>{let{text:t,title:r,finishLabel:c,onFinish:d,contents:u,...p}=e,{navigateToFlowStart:m}=(0,a.zk)();return(0,n.BX)(o.Col,{...p,gap:4,children:[(0,n.tZ)(s.h.Root,{children:(0,n.tZ)(s.h.Title,{localizationKey:r,textVariant:"subtitle"})}),(0,n.tZ)(o.Col,{gap:4,children:Array.isArray(t)?t.map(e=>(0,n.tZ)(o.Text,{localizationKey:e,colorScheme:"secondary",sx:e=>({display:"inline",":not(:last-of-type)":{marginRight:e.sizes.$1}})},e.key)):(0,n.tZ)(o.Text,{localizationKey:t,colorScheme:"secondary"})}),u,(0,n.tZ)(l.K,{children:(0,n.tZ)(o.Button,{autoFocus:!0,localizationKey:c||(0,i.u1)("userProfile.formButtonPrimary__finish"),elementDescriptor:o.descriptors.formButtonPrimary,onClick:d||m})})]})}},4142:function(e,t,r){r.d(t,{r:()=>l});var n=r(9109),o=r(9144),a=r(9541),i=r(1201);let l=(0,o.forwardRef)(({isChecked:e,defaultChecked:t,onChange:r,isDisabled:l=!1,label:s},c)=>{let[d,u]=(0,o.useState)(!!t),p=void 0!==e,m=p?e:d;return(0,n.BX)(a.Flex,{ref:c,elementDescriptor:a.descriptors.switchRoot,direction:"row",align:"center",as:"label",sx:e=>({isolation:"isolate",width:"fit-content","&:has(input:focus-visible) > input + span":{...i.common.focusRingStyles(e)}}),children:[(0,n.tZ)("input",{type:"checkbox",role:"switch",disabled:l,defaultChecked:t,checked:m,onChange:e=>{l||(p||u(e.target.checked),r?.(e.target.checked))},style:{...i.common.visuallyHidden()}}),(0,n.tZ)(a.Flex,{elementDescriptor:a.descriptors.switchIndicator,as:"span","data-checked":m,sx:e=>({width:e.sizes.$6,height:e.sizes.$4,alignItems:"center",position:"relative",backgroundColor:m?e.colors.$primary500:e.colors.$neutralAlpha150,borderRadius:999,transition:"background-color 0.2s",opacity:l?.6:1,cursor:l?"not-allowed":"pointer",outline:"none",boxSizing:"border-box",boxShadow:"0px 0px 0px 1px rgba(0, 0, 0, 0.06) inset"}),children:(0,n.tZ)(a.Flex,{as:"span",elementDescriptor:a.descriptors.switchThumb,sx:e=>({position:"absolute",left:e.sizes.$0x5,width:e.sizes.$3,height:e.sizes.$3,borderRadius:"50%",backgroundColor:e.colors.$colorBackground,boxShadow:e.shadows.$switchControl,transform:`translateX(${m?e.sizes.$2:0})`,transition:"transform 0.2s",zIndex:1})})}),(0,n.tZ)(a.Text,{as:"span",variant:"caption",colorScheme:"secondary",localizationKey:s,sx:e=>({paddingInlineStart:e.sizes.$2,cursor:l?"not-allowed":"pointer",userSelect:"none"})})]})});l.displayName="Switch"},708:function(e,t,r){r.d(t,{OK:()=>m,dr:()=>p,mQ:()=>u,nP:()=>h,x4:()=>g});var n=r(9109),o=r(3799),a=r(9144),i=r(9541),l=r(7623);let[s,c]=(0,o.uH)("TabsContext"),d=e=>{let t=a.useMemo(()=>({value:e.value}),[e.value.selectedIndex,e.value.setFocusedIndex]);return(0,n.tZ)(s.Provider,{value:t,children:e.children})},u=e=>{let{value:t,onChange:r,defaultIndex:o=0,children:i}=e,[l,s]=a.useState(o),[c,u]=a.useState(-1),p=a.useCallback(e=>{r?r(e):s(e)},[r]);return(0,n.tZ)(d,{value:{selectedIndex:void 0!==t?t:l,setSelectedIndex:p,focusedIndex:c,setFocusedIndex:u},children:i})},p=e=>{let{children:t,sx:r,...o}=e,{setSelectedIndex:s,selectedIndex:d,setFocusedIndex:u}=c(),p=0,h=(0,l.WR)(t).map(e=>{let t=a.cloneElement(e,{tabIndex:e.type===m?p:void 0});return p++,t});return(0,n.tZ)(i.Flex,{elementDescriptor:i.descriptors.tabListContainer,onKeyDown:e=>{let t=h.filter(e=>!e.props?.isDisabled&&e.type===m).map(e=>e.props.tabIndex),r=t.length,n=t.indexOf(d);if("ArrowLeft"===e.key){let e=0===n?t[r-1]:t[n-1];return u(e),s(e)}if("ArrowRight"===e.key){let e=r-1===n?t[0]:t[n+1];return u(e),s(e)}},role:"tablist",sx:[e=>({borderBottomStyle:e.borderStyles.$solid,borderBottomWidth:e.borderWidths.$normal,borderColor:e.colors.$neutralAlpha100}),r],...o,children:h})},m=e=>{let{t}=(0,i.useLocalizations)(),{children:r,sx:o,tabIndex:l,isDisabled:s,localizationKey:d,...u}=e;if(void 0===l)throw Error("Tab component must be a direct child of TabList.");let{setSelectedIndex:p,selectedIndex:m,focusedIndex:h,setFocusedIndex:g}=c(),f=a.useRef(null),b=l===m,v=l===h;return a.useEffect(()=>{s&&0===l&&p(l+1)},[]),a.useEffect(()=>{f.current&&v&&f.current.focus()},[v]),(0,n.BX)(i.Button,{elementDescriptor:i.descriptors.tabButton,onClick:()=>{p(l),g(-1)},isDisabled:s,tabIndex:b?0:-1,variant:"ghost","aria-selected":b,id:`cl-tab-${l}`,"aria-controls":`cl-tabpanel-${l}`,role:"tab",ref:f,sx:[e=>({background:e.colors.$transparent,color:b?e.colors.$primary500:e.colors.$neutralAlpha700,gap:e.space.$1x5,fontWeight:e.fontWeights.$medium,borderBottomWidth:b?e.borderWidths.$normal:0,borderBottomStyle:e.borderStyles.$solid,borderBottomColor:e.colors.$primary500,marginBottom:b?"-1px":0,borderRadius:0,padding:`${e.space.$2x5} ${e.space.$0x25}`,width:"fit-content","&:hover, :focus":{backgroundColor:e.colors.$transparent,boxShadow:"none"}}),o],...u,children:[t(d),r]})},h=e=>{let{children:t}=e,r=(0,l.WR)(t).map((e,t)=>a.cloneElement(e,{tabIndex:t}));return(0,n.tZ)(n.HY,{children:r})},g=e=>{let{tabIndex:t,sx:r,children:o,...a}=e;if(void 0===t)throw Error("TabPanel component must be a direct child of TabPanels.");let{selectedIndex:l}=c();return t!==l?null:(0,n.tZ)(i.Flex,{elementDescriptor:i.descriptors.tabPanel,id:`cl-tabpanel-${t}`,role:"tabpanel",tabIndex:0,"aria-labelledby":`cl-tab-${t}`,sx:[{outline:0},r],...a,children:o})}},6443:function(e,t,r){r.d(t,{E:()=>c});var n=r(9109),o=r(9144),a=r(9541),i=r(4174),l=r(1201);let s=e=>e.trim(),c=e=>{let{t}=(0,a.useLocalizations)(),{sx:r,placeholder:i,validate:c=()=>!0,value:u,onChange:p,autoFocus:m,validateUnsubmittedEmail:h=()=>null,...g}=e,f=u.split(",").map(s).filter(Boolean),b=new Set(f),v=o.useRef(!0),y=o.useRef(null),[w,x]=o.useState(""),S=e=>{p({target:{value:e.join(",")}}),k(),h("")},$=e=>{S(f.filter(t=>t!==e))},C=()=>{S(f.slice(0,-1))},_=e=>{let t=(Array.isArray(e)?[...e]:[e]).map(s).filter(Boolean).filter(c).filter(e=>!b.has(e));t.length&&(S([...f,...t]),x(""),k())},k=()=>{y.current?.focus()};return(0,n.BX)(a.Flex,{elementDescriptor:a.descriptors.tagInputContainer,align:"start",gap:2,wrap:"wrap",onClick:k,onFocus:k,sx:[e=>({maxWidth:"100%",padding:`${e.space.$1x5} ${e.space.$2}`,backgroundColor:e.colors.$colorInputBackground,color:e.colors.$colorInputText,minHeight:e.sizes.$20,maxHeight:e.sizes.$60,overflowY:"auto",cursor:"text",justifyItems:"center",...l.common.borderVariants(e).normal}),r],...g,children:[f.map(e=>(0,n.tZ)(d,{onRemoveClick:()=>$(e),children:e},e)),(0,n.tZ)(a.Input,{ref:y,value:w,type:"email","data-testid":"tag-input",placeholder:f.length?void 0:t(i),onKeyDown:e=>{let{key:t}=e;(","===t||" "===t||"Enter"===t)&&w.length?(e.preventDefault(),_(w)):"Backspace"===t&&!w.length&&f.length&&v.current&&(e.preventDefault(),C()),v.current=!1},onKeyUp:()=>{v.current=!0},onChange:e=>{x(e.target.value),h(e.target.value)},onPaste:e=>{e.preventDefault(),_((e.clipboardData.getData("text")||"").split(/,| |\n|\t/).filter(Boolean).map(e=>e.trim()))},onBlur:e=>{e.preventDefault(),_(w)},focusRing:!1,autoFocus:m,variant:"unstyled",sx:e=>({flexGrow:1,borderWidth:0,width:"initial",padding:0,lineHeight:e.space.$5,paddingLeft:e.space.$1,"::placeholder":{color:e.colors.$colorTextSecondary},boxShadow:"none",":hover":{boxShadow:"none"}})})]})},d=e=>{let{onRemoveClick:t,children:r,...o}=e;return(0,n.BX)(a.Flex,{elementDescriptor:a.descriptors.tagPillContainer,onClick:t,gap:1,center:!0,...o,sx:e=>({padding:`${e.space.$0x5} ${e.space.$1x5}`,borderRadius:e.radii.$sm,color:e.colors.$primary500,backgroundColor:e.colors.$neutralAlpha50,boxShadow:e.shadows.$badge,borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha100,display:"inline-flex",marginRight:"1px",cursor:"pointer",":hover":{backgroundColor:e.colors.$neutralAlpha100},":hover svg":{color:e.colors.$danger500},overflow:"hidden"}),children:[(0,n.tZ)(a.Text,{truncate:!0,variant:"buttonSmall",children:r}),(0,n.tZ)(a.Icon,{elementDescriptor:a.descriptors.tagPillIcon,size:"md",icon:i.v3,sx:e=>({color:e.colors.$colorTextSecondary,transform:"translateY(0px) rotate(45deg)"})})]})}},3009:function(e,t,r){r.d(t,{a:()=>l});var n=r(9109),o=r(9541),a=r(4174),i=r(5158);let l=e=>{let{actions:t,elementId:r}=e;return(0,n.BX)(i.v2,{elementId:r,children:[(0,n.tZ)(i.bF,{arialLabel:e=>`${e?"Close":"Open"} menu`,children:(0,n.tZ)(o.Button,{sx:e=>({padding:e.space.$0x5,boxSizing:"content-box",opacity:e.opacity.$inactive,":hover":{opacity:1}}),variant:"ghost",colorScheme:"neutral",elementDescriptor:[o.descriptors.menuButton,o.descriptors.menuButtonEllipsis],children:(0,n.tZ)(o.Icon,{icon:a.g4,sx:e=>({width:"auto",height:e.sizes.$5})})})}),(0,n.tZ)(i.qy,{children:t.map((e,t)=>(0,n.tZ)(i.sN,{destructive:e.isDestructive,onClick:e.onClick,isDisabled:e.isDisabled,localizationKey:e.label},t))})]})}},5679:function(e,t,r){r.d(t,{u:()=>l});var n=r(9109),o=r(3799),a=r(9144),i=r(9541);let l=e=>{let{t}=(0,i.useLocalizations)(),{onClick:r,throttleTimeInSec:l=30,startDisabled:s,children:c,localizationKey:d,showCounter:u=!0,...p}=e,[m,h]=a.useState(0),g=a.useRef(void 0);(0,o.Gw)(()=>{s&&f()},[]),(0,a.useEffect)(()=>()=>clearInterval(g.current),[]);let f=()=>{h(l),g.current=window.setInterval(()=>{h(e=>(1===e&&clearInterval(g.current),e-1))},1e3)};return(0,n.BX)(i.Button,{variant:"link",...p,textVariant:"buttonSmall",isDisabled:m>0||e.isDisabled,onClick:e=>{!m&&(r?.(e),f())},children:[t(d)||c,m&&u?` (${m})`:""]})}},9732:function(e,t,r){r.d(t,{u:()=>p});var n=r(9109),o=r(7126),a=r(6089),i=r(3986),l=r(9144),s=r(9541),c=r(2464);let d=l.createContext(null),u=()=>{let e=l.useContext(d);if(null==e)throw Error("Tooltip components must be wrapped in <Tooltip />");return e},p={Root:function({children:e,...t}){let r=function({initialOpen:e=!1,placement:t="top",open:r,onOpenChange:n}={}){let[d,u]=l.useState(e),p=r??d,m=n??u,h=(0,c.Tb)(),{animations:g}=(0,s.useAppearance)().parsedLayout,f=(0,o.YF)({placement:t,open:p,onOpenChange:m,whileElementsMounted:a.Me,middleware:[(0,i.cv)(6),(0,i.RR)({crossAxis:t.includes("-"),fallbackAxisSideDirection:"start",padding:6}),(0,i.uY)({padding:6})]}),b=f.context,v=(0,o.XI)(b,{move:!1,enabled:null==r}),y=(0,o.KK)(b,{enabled:null==r}),w=(0,o.bQ)(b),x=(0,o.qs)(b,{role:"tooltip"}),{isMounted:S,styles:$}=(0,o.Y_)(b,{duration:200*!!(!h&&!0===g),initial:({side:e})=>({opacity:0,transform:"top"===e?"translateY(4px)":"translateY(-4px)"}),open:{opacity:1,transform:"translate(0)"},close:({side:e})=>({opacity:0,transform:"top"===e?"translateY(4px)":"translateY(-4px)"})}),C=(0,o.NI)([v,y,w,x]);return l.useMemo(()=>({open:p,setOpen:m,isMounted:S,...C,...f,transitionStyles:$}),[p,m,C,f,S,$])}(t);return(0,n.tZ)(d.Provider,{value:r,children:e})},Trigger:l.forwardRef(function({children:e,sx:t,...r},a){let i=u(),c=e.ref,d=(0,o.qq)([i.refs.setReference,a,c]);return l.isValidElement(e)?e.props.isDisabled||e.props.disabled?(0,n.tZ)(s.Span,{ref:d,...i.getReferenceProps({...r}),"data-state":i.open?"open":"closed",sx:[{width:"fit-content",display:"inline-block",cursor:"not-allowed",outline:"none"},t],tabIndex:0,children:e}):l.cloneElement(e,i.getReferenceProps({ref:d,...r,...e.props,"data-state":i.open?"open":"closed"})):null}),Content:l.forwardRef(function({style:e,text:t,sx:r,...a},i){let l=u(),c=(0,o.qq)([l.refs.setFloating,i]);return l.isMounted?(0,n.tZ)(o.ll,{children:(0,n.tZ)(s.Box,{ref:c,elementDescriptor:s.descriptors.tooltip,style:{...l.floatingStyles,...e},...l.getFloatingProps(a),children:(0,n.tZ)(s.Box,{elementDescriptor:s.descriptors.tooltipContent,style:l.transitionStyles,sx:[e=>({paddingBlock:e.space.$1,paddingInline:e.space.$1x5,borderRadius:e.radii.$md,backgroundColor:e.colors.$black,color:e.colors.$white,maxWidth:e.sizes.$60}),r],children:(0,n.tZ)(s.Text,{elementDescriptor:s.descriptors.tooltipText,localizationKey:t,variant:"body",colorScheme:"inherit"})})})}):null})}},5472:function(e,t,r){r.d(t,{Y:()=>i});var n=r(9109),o=r(5878),a=r(9629);let i=e=>{let{name:t,firstName:r,lastName:i,avatarUrl:l,imageUrl:s,...c}=e,d=(0,o.Pp)({name:t,firstName:r,lastName:i}),u=(0,o.Qm)({name:t,firstName:r,lastName:i});return(0,n.tZ)(a.q,{title:d,initials:u,imageUrl:l||s,...c})}},7295:function(e,t,r){r.d(t,{E:()=>l});var n=r(9109);r(9144);var o=r(5878),a=r(9541),i=r(5472);let l=e=>{let{user:t,externalAccount:r,samlAccount:l,size:s="md",showAvatar:c=!0,icon:d,iconSx:u,rounded:p=!0,imageUrl:m,badge:h,elementId:g,sx:f,title:b,subtitle:v,avatarSx:y,mainIdentifierSx:w,mainIdentifierVariant:x,...S}=e,{t:$}=(0,a.useLocalizations)(),C=(0,o.Pp)({...t})||(0,o.Pp)({...r})||(0,o.Pp)({...l}),_=(0,o.xC)({...t})||r?.accountIdentifier?.()||l?.emailAddress,k=$(b),P=m||t?.imageUrl||r?.imageUrl,I=e=>({xs:e.sizes.$5,sm:e.sizes.$8,md:e.sizes.$9,lg:e.sizes.$12})[s],A=k||C||_;return(0,n.BX)(a.Flex,{elementDescriptor:a.descriptors.userPreview,elementId:a.descriptors.userPreview.setId(g),align:"center",as:"span",sx:[e=>({minWidth:"0px",width:"fit-content",gap:e.space.$4}),f],...S,children:[P?c?(0,n.BX)(a.Flex,{elementDescriptor:a.descriptors.userPreviewAvatarContainer,elementId:a.descriptors.userPreviewAvatarContainer.setId(g),justify:"center",as:"span",sx:{position:"relative"},children:[(0,n.tZ)(i.Y,{boxElementDescriptor:a.descriptors.userPreviewAvatarBox,imageElementDescriptor:a.descriptors.userPreviewAvatarImage,...t,...r,...l,name:C,avatarUrl:P,size:I,sx:y,rounded:p}),d&&(0,n.tZ)(a.Flex,{elementDescriptor:a.descriptors.userPreviewAvatarIcon,sx:[{position:"absolute",left:0,bottom:0},u],as:"span",children:d})]}):(0,n.tZ)(a.Flex,{elementDescriptor:a.descriptors.userPreviewAvatarContainer,elementId:a.descriptors.userPreviewAvatarContainer.setId(g),justify:"center",as:"span",sx:e=>({height:I(e)})}):null,(0,n.BX)(a.Flex,{elementDescriptor:a.descriptors.userPreviewTextContainer,elementId:a.descriptors.userPreviewTextContainer.setId(g),direction:"col",justify:"center",as:"span",sx:{minWidth:"0px",textAlign:"left"},children:[(0,n.BX)(a.Text,{elementDescriptor:a.descriptors.userPreviewMainIdentifier,elementId:a.descriptors.userPreviewMainIdentifier.setId(g),variant:x||({xs:"subtitle",sm:"caption",md:"subtitle",lg:"h1"})[s],as:"span",sx:[e=>({display:"flex",gap:e.sizes.$1,alignItems:"center"}),w],children:[A&&(0,n.tZ)(a.Text,{elementDescriptor:a.descriptors.userPreviewMainIdentifierText,elementId:a.descriptors.userPreviewMainIdentifierText.setId(g),as:"span",truncate:!0,sx:{fontWeight:"inherit"},children:A}),h]}),(v||C&&_)&&(0,n.tZ)(a.Text,{elementDescriptor:a.descriptors.userPreviewSecondaryIdentifier,elementId:a.descriptors.userPreviewSecondaryIdentifier.setId(g),truncate:!0,as:"span",localizationKey:v||_})]})]})}},3465:function(e,t,r){r.d(t,{U:()=>u});var n=r(9109);r(9144);var o=r(9541),a=r(4455),i=r(2667),l=r(2672),s=r(431),c=r(2654),d=r(9577);let u=e=>{let{showAlternativeMethods:t=!0,children:r}=e,u=(0,l.useCardState)(),p=(0,i.e3)({onCodeEntryFinished:(t,r,n)=>{e.onCodeEntryFinishedAction(t,r,n)},onResendCodeClicked:e.onResendCodeClicked});return(0,n.BX)(a.Z.Root,{children:[(0,n.BX)(a.Z.Content,{children:[(0,n.BX)(c.h.Root,{children:[(0,n.tZ)(c.h.Title,{localizationKey:e.cardTitle}),(0,n.tZ)(c.h.Subtitle,{localizationKey:e.cardSubtitle}),(0,n.tZ)(d.m,{identifier:e.safeIdentifier,avatarUrl:e.profileImageUrl,onClick:e.onBackLinkClicked?void 0:e.onIdentityPreviewEditClicked})]}),(0,n.tZ)(a.Z.Alert,{children:u.error}),r,(0,n.BX)(o.Col,{elementDescriptor:o.descriptors.main,gap:8,children:[(0,n.tZ)(s.l.OTPInput,{...p,label:e.inputLabel,resendButton:e.resendButton}),(0,n.BX)(o.Col,{gap:3,children:[(0,n.tZ)(o.Button,{elementDescriptor:o.descriptors.formButtonPrimary,block:!0,hasArrow:!0,isLoading:p.isLoading,localizationKey:(0,o.localizationKeys)("formButtonPrimary"),onClick:p.onFakeContinue}),t&&e.onShowAlternativeMethodsClicked&&(0,n.tZ)(a.Z.Action,{elementId:"alternativeMethods",children:(0,n.tZ)(a.Z.ActionLink,{localizationKey:e.alternativeMethodsLabel??(0,o.localizationKeys)("footerActionLink__useAnotherMethod"),onClick:e.onShowAlternativeMethodsClicked})})]})]})]}),(0,n.tZ)(a.Z.Footer,{})]})}},5495:function(e,t,r){r.d(t,{J:()=>u,V:()=>p});var n=r(9109);r(9144);var o=r(9541),a=r(4676),i=r(4455),l=r(2672),s=r(2654),c=r(9577),d=r(5679);let u=e=>{let{navigate:t}=(0,a.useRouter)(),r=(0,l.useCardState)();return(0,n.tZ)(o.Flow.Part,{part:"emailLinkVerify",children:(0,n.BX)(i.Z.Root,{children:[(0,n.BX)(i.Z.Content,{children:[(0,n.BX)(s.h.Root,{showLogo:!0,children:[(0,n.tZ)(s.h.Title,{localizationKey:e.cardTitle}),(0,n.BX)(p,{formSubtitle:e.formSubtitle,resendButton:e.resendButton,onResendCodeClicked:e.onResendCodeClicked,children:[" ",(0,n.tZ)(c.m,{identifier:e.safeIdentifier,avatarUrl:e.profileImageUrl,onClick:()=>t("../")})]})]}),(0,n.tZ)(i.Z.Alert,{children:r.error}),(0,n.tZ)(i.Z.Action,{elementId:"alternativeMethods",children:e.onShowAlternativeMethodsClicked&&(0,n.tZ)(i.Z.ActionLink,{localizationKey:(0,o.localizationKeys)("footerActionLink__useAnotherMethod"),onClick:e.onShowAlternativeMethodsClicked})})]}),(0,n.tZ)(i.Z.Footer,{})]})})},p=e=>{let t=(0,l.useCardState)();return(0,n.BX)(o.Col,{elementDescriptor:o.descriptors.form,gap:1,children:[(0,n.BX)(o.Col,{elementDescriptor:o.descriptors.formHeader,gap:1,children:[!!e.formSubtitle&&(0,n.tZ)(o.Text,{localizationKey:e.formSubtitle,elementDescriptor:o.descriptors.formHeaderSubtitle,colorScheme:"secondary"}),e.children]}),(0,n.tZ)(d.u,{localizationKey:e.resendButton,elementDescriptor:o.descriptors.formResendCodeLink,onClick:e.onResendCodeClicked,startDisabled:!0,isDisabled:t.isLoading,throttleTimeInSec:60,sx:e=>({marginTop:e.space.$4,width:"100%"})})]})}},2672:function(e,t,r){r.r(t),r.d(t,{CardStateProvider:()=>u,FlowMetadataProvider:()=>f,useCardState:()=>p,useFlowMetadata:()=>g,withCardStateProvider:()=>m,withFloatingTree:()=>b});var n=r(9109),o=r(3799),a=r(7126),i=r(9144),l=r(9541),s=r(2464);let[c,d]=(0,o.uH)("CardState"),u=e=>{let{translateError:t}=(0,l.useLocalizations)(),[r,o]=(0,s.FH)({status:"idle",metadata:void 0,error:t(window?.Clerk?.__internal_last_error||void 0)}),a=i.useMemo(()=>({value:{state:r,setState:o}}),[r,o]);return(0,n.tZ)(c.Provider,{value:a,children:e.children})},p=()=>{let{state:e,setState:t}=d(),{translateError:r}=(0,l.useLocalizations)(),n=e=>t(t=>({...t,status:"idle",metadata:e})),o=e=>t(t=>({...t,status:"loading",metadata:e})),a=async(e,t)=>(o(t),("function"==typeof e?e():e).then(e=>e).finally(()=>n(t)));return{setIdle:n,setError:e=>t(t=>({...t,error:r(e)})),setLoading:o,runAsync:a,loadingMetadata:"loading"===e.status?e.metadata:void 0,error:e.error?e.error:void 0,isLoading:"loading"===e.status,isIdle:"idle"===e.status,state:e}},m=e=>t=>(0,n.tZ)(u,{children:(0,n.tZ)(e,{...t})}),[h,g]=(0,o.uH)("FlowMetadata"),f=e=>{let{flow:t,part:r}=e,o=i.useMemo(()=>({value:e}),[t,r]);return(0,n.tZ)(h.Provider,{value:o,children:e.children})},b=e=>t=>null==(0,a.Zm)()?(0,n.tZ)(a.RB,{children:(0,n.tZ)(e,{...t})}):(0,n.tZ)(e,{...t})},7711:function(e,t,r){r.d(t,{C:()=>i});var n=r(9109),o=r(9144),a=r(9541);let i=e=>(0,o.forwardRef)((t,r)=>{let{parsedLayout:o}=(0,a.useAppearance)();return(0,n.tZ)(e,{...t,ref:r,sx:[o.shimmer?{":hover":{"--cl-shimmer-hover-transform":"skew(-45deg) translateX(600%)","--cl-shimmer-hover-after-transform":"skewX(45deg) translateX(-150%)"}}:{},t.sx]})})},8345:function(e,t,r){r.d(t,{$4:()=>x,cS:()=>n});let n=e=>{let t={};for(let r in e)for(let n in t[r]={},e[r])t[r]["$"+n]=e[r][n];return Object.freeze(t)},o=Object.freeze({solid:"solid",dashed:"dashed"}),a=Object.freeze({normal:"1px",heavy:"2px"});var i=r(4929);let l=Object.freeze({whiteAlpha25:"hsla(0, 0%, 100%, 0.02)",whiteAlpha50:"hsla(0, 0%, 100%, 0.03)",whiteAlpha100:"hsla(0, 0%, 100%, 0.07)",whiteAlpha150:"hsla(0, 0%, 100%, 0.11)",whiteAlpha200:"hsla(0, 0%, 100%, 0.15)",whiteAlpha300:"hsla(0, 0%, 100%, 0.28)",whiteAlpha400:"hsla(0, 0%, 100%, 0.41)",whiteAlpha500:"hsla(0, 0%, 100%, 0.53)",whiteAlpha600:"hsla(0, 0%, 100%, 0.62)",whiteAlpha700:"hsla(0, 0%, 100%, 0.73)",whiteAlpha750:"hsla(0, 0%, 100%, 0.78)",whiteAlpha800:"hsla(0, 0%, 100%, 0.81)",whiteAlpha850:"hsla(0, 0%, 100%, 0.84)",whiteAlpha900:"hsla(0, 0%, 100%, 0.87)",whiteAlpha950:"hsla(0, 0%, 100%, 0.92)"}),s=Object.freeze({neutralAlpha25:"hsla(0, 0%, 0%, 0.02)",neutralAlpha50:"hsla(0, 0%, 0%, 0.03)",neutralAlpha100:"hsla(0, 0%, 0%, 0.07)",neutralAlpha150:"hsla(0, 0%, 0%, 0.11)",neutralAlpha200:"hsla(0, 0%, 0%, 0.15)",neutralAlpha300:"hsla(0, 0%, 0%, 0.28)",neutralAlpha400:"hsla(0, 0%, 0%, 0.41)",neutralAlpha500:"hsla(0, 0%, 0%, 0.53)",neutralAlpha600:"hsla(0, 0%, 0%, 0.62)",neutralAlpha700:"hsla(0, 0%, 0%, 0.73)",neutralAlpha750:"hsla(0, 0%, 0%, 0.78)",neutralAlpha800:"hsla(0, 0%, 0%, 0.81)",neutralAlpha850:"hsla(0, 0%, 0%, 0.84)",neutralAlpha900:"hsla(0, 0%, 0%, 0.87)",neutralAlpha950:"hsla(0, 0%, 0%, 0.92)"}),c=Object.freeze({avatarBorder:s.neutralAlpha200,avatarBackground:s.neutralAlpha400,modalBackdrop:s.neutralAlpha700,...s,...l,colorBackground:"white",colorInputBackground:"white",colorText:"#212126",colorTextSecondary:"#747686",colorInputText:"#131316",colorTextOnPrimaryBackground:"white",colorShimmer:"rgba(255, 255, 255, 0.36)",transparent:"transparent",white:"white",black:"black",primary50:"#B9BDBC",primary100:"#9EA1A2",primary200:"#828687",primary300:"#66696D",primary400:"#4B4D52",primary500:"#2F3037",primary600:"#2A2930",primary700:"#25232A",primary800:"#201D23",primary900:"#1B171C",primaryHover:"#3B3C45",...(0,i.I)("#2F3037","primaryAlpha"),danger50:"#FEF2F2",danger100:"#FEE5E5",danger200:"#FECACA",danger300:"#FCA5A5",danger400:"#F87171",danger500:"#EF4444",danger600:"#DC2626",danger700:"#B91C1C",danger800:"#991B1B",danger900:"#7F1D1D",danger950:"#450A0A",...(0,i.I)("#EF4444","dangerAlpha"),warning50:"#FFF6ED",warning100:"#FFEBD5",warning200:"#FED1AA",warning300:"#FDB674",warning400:"#F98C49",warning500:"#F36B16",warning600:"#EA520C",warning700:"#C23A0C",warning800:"#9A2F12",warning900:"#7C2912",warning950:"#431207",...(0,i.I)("#F36B16","warningAlpha"),success50:"#F0FDF2",success100:"#DCFCE2",success200:"#BBF7C6",success300:"#86EF9B",success400:"#4ADE68",success500:"#22C543",success600:"#16A332",success700:"#15802A",success800:"#166527",success900:"#145323",success950:"#052E0F",...(0,i.I)("#22C543","successAlpha")}),d=Object.freeze({sm:"24%",disabled:"50%",inactive:"62%"}),u=Object.freeze({menuShadow:"0px 5px 15px 0px rgba(0, 0, 0, 0.08), 0px 15px 35px -5px rgba(25, 28, 33, 0.20)",fabShadow:"0px 12px 24px rgba(0, 0, 0, 0.32)",buttonShadow:"0px 1px 1px 0px rgba(255, 255, 255, 0.07) inset, 0px 2px 3px 0px rgba(34, 42, 53, 0.20), 0px 1px 1px 0px rgba(0, 0, 0, 0.24)",cardBoxShadow:"0px 5px 15px 0px rgba(0, 0, 0, 0.08), 0px 15px 35px -5px rgba(25, 28, 33, 0.20)",cardContentShadow:"0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 1px 2px 0px rgba(25, 28, 33, 0.06)",actionCardShadow:"0px 1px 4px 0px rgba(0, 0, 0, 0.12), 0px 4px 8px 0px rgba(106, 115, 133, 0.12)",outlineButtonShadow:"0px 2px 3px -1px rgba(0, 0, 0, 0.08), 0px 1px 0px 0px rgba(0, 0, 0, 0.02)",input:"0px 0px 1px 0px {{color}}",focusRing:"0px 0px 0px 4px {{color}}",badge:"0px 2px 0px -1px rgba(0, 0, 0, 0.04)",tableBodyShadow:"0px 0px 2px 0px rgba(0, 0, 0, 0.08), 0px 1px 2px 0px rgba(25, 28, 33, 0.12), 0px 0px 0px 1px rgba(0, 0, 0, 0.06)",segmentedControl:"0px 1px 2px 0px rgba(0, 0, 0, 0.08)",switchControl:"0px 2px 2px -1px rgba(0, 0, 0, 0.06), 0px 0px 0px 1px rgba(0, 0, 0, 0.06), 0px 4px 4px -2px rgba(0, 0, 0, 0.06)"});var p=r(2635),m=r(3003);let h=Object.freeze({normal:400,medium:500,semibold:600,bold:700}),g=Object.freeze({normal:"normal",extraSmall:"1.33333",small:"1.38462",medium:"1.41176",large:"1.45455"}),f=Object.freeze({normal:"normal"}),b=Object.freeze({xs:"0.6875rem",sm:"0.75rem",md:"0.8125rem",lg:"1.0625rem",xl:"1.5rem"}),v=Object.freeze({normal:"normal"}),y=Object.freeze({main:"inherit",buttons:"inherit"}),w=Object.freeze({card:"10",navbar:"100",fab:"9000",modal:"10000",dropdown:"11000"}),x=n(Object.freeze({colors:c,fonts:y,fontStyles:v,fontSizes:b,fontWeights:h,letterSpacings:f,lineHeights:g,radii:p.pD,sizes:p.J7,space:p.Dh,shadows:u,transitionProperty:m.n_,transitionTiming:m.$$,transitionDuration:m.nV,transitionDurationValues:m.bf,opacity:d,borderStyles:o,borderWidths:a,zIndices:w}))},2635:function(e,t,r){r.d(t,{Dh:()=>a,J7:()=>i,Mx:()=>s,pD:()=>l});let n=Object.freeze({none:"0",xxs:"0.5px",px:"1px"}),o=Object.freeze({"0x25":"0.0625rem","0x5":"0.125rem",1:"0.25rem","1x5":"0.375rem",2:"0.5rem","2x5":"0.625rem",3:"0.75rem","3x25":"0.8125rem","3x5":"0.875rem",4:"1rem","4x25":"1.0625rem",5:"1.25rem","5x5":"1.375rem",6:"1.5rem",7:"1.75rem","7x5":"1.875rem",8:"2rem","8x5":"2.125rem","8x75":"2.1875rem",9:"2.25rem",10:"2.5rem",12:"3rem",13:"3.5rem",16:"4rem",17:"4.25rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",57:"14.25rem",60:"15rem",66:"16.5rem",94:"23.5rem",100:"25rem",108:"27rem",120:"30rem",140:"35rem",160:"40rem",176:"44rem",220:"55rem"}),a=Object.freeze({...n,...o}),i=Object.freeze({...a}),l=Object.freeze({none:"0px",circle:"50%",avatar:"0.375rem",sm:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem",halfHeight:"99999px"}),s=Object.keys(o)},3003:function(e,t,r){r.d(t,{$$:()=>l,bf:()=>n,nV:()=>a,n_:()=>i});let n=Object.freeze({slowest:600,slower:280,slow:200,fast:120,focusRing:200,controls:100,textField:450,drawer:500}),o=e=>`${e}ms`,a=Object.freeze(Object.fromEntries(Object.entries(n).map(([e,t])=>[e,o(t)]))),i=Object.freeze({common:"background-color,background,border-color,color,fill,stroke,opacity,box-shadow,transform"}),l=Object.freeze({common:"ease",easeOut:"ease-out",bezier:"cubic-bezier(0.32, 0.72, 0, 1)",slowBezier:"cubic-bezier(0.16, 1, 0.3, 1)"})},2464:function(e,t,r){r.d(t,{VP:()=>l,Sv:()=>C,YD:()=>g,ib:()=>h.ib,Pr:()=>P.P,Fm:()=>A,Nr:()=>s,p5:()=>w.p,gm:()=>d,E2:()=>u.E,vO:()=>p.v,_m:()=>b,_6:()=>n._,Tb:()=>k,eq:()=>m,zk:()=>y,FH:()=>f,dw:()=>I});var n=r(1139),o=r(472),a=r.n(o),i=r(9144);function l(e,t={}){let[r,n]=(0,i.useState)(!1),{timeout:o=1500,...s}="number"==typeof t?{timeout:t}:t,c=(0,i.useCallback)(()=>{n(a()(e,s))},[e,s]);return(0,i.useEffect)(()=>{let e=null;return r&&(e=window.setTimeout(()=>{n(!1)},o)),()=>{e&&window.clearTimeout(e)}},[o,r]),{value:e,onCopy:c,hasCopied:r}}function s(e,t){let[r,n]=(0,i.useState)(e),[o,a]=(0,i.useState)(void 0);return(0,i.useEffect)(()=>(o&&(clearTimeout(o),a(void 0)),a(setTimeout(()=>{n(e),a(void 0)},t||500)),()=>{o&&(clearTimeout(o),a(void 0))}),[JSON.stringify(e),t]),r}function c(e){let t=e.dir;return"rtl"===t?"rtl":"ltr"===t?"ltr":"auto"!==t&&t||"rtl"!==window.getComputedStyle(e).direction?"ltr":"rtl"}function d(e){return"undefined"==typeof window?"ltr":e?c(e):c(document.documentElement)}var u=r(4814),p=r(9693);function m(e){let{startEnterpriseSSOLinkFlow:t,cancelEnterpriseSSOLinkFlow:r}=i.useMemo(()=>e.createEnterpriseSSOLinkFlow(),[e]);return i.useEffect(()=>r,[]),{startEnterpriseSSOLinkFlow:t,cancelEnterpriseSSOLinkFlow:r}}var h=r(7432);let g=e=>{let[t,r]=(0,i.useState)(!1),n=(0,i.useRef)(null),o=Array.isArray(e.threshold)?e.threshold:[e.threshold||0],a=(0,i.useRef)();return a.current=e.onChange,{inView:t,ref:(0,i.useCallback)(t=>{if(!t){n.current&&n.current.disconnect();return}n.current=new IntersectionObserver(e=>{e.forEach(e=>{let t=e.isIntersecting&&o.some(t=>e.intersectionRatio>=t);r(t),a.current&&a.current(t,e)})},{root:e.root,rootMargin:e.rootMargin,threshold:o}),n.current.observe(t)},[])}};function f(e){let[t,r]=i.useState(e),n=i.useRef(!0);return i.useEffect(()=>()=>{n.current=!1},[]),[t,i.useCallback(e=>{n.current&&r(e)},[])]}let b=e=>{let[t,r]=f({status:"idle",metadata:void 0,...e});return{status:t.status,setIdle:e=>r({status:"idle",metadata:e}),setError:e=>r({status:"error",metadata:e}),setLoading:e=>r({status:"loading",metadata:e}),loadingMetadata:"loading"===t.status?t.metadata:void 0,isLoading:"loading"===t.status,isIdle:"idle"===t.status}};var v=r(4676);let y=()=>{let e=(0,v.useRouter)();return{navigateToFlowStart:async()=>{let t=e.indexPath;return t!==e.currentPath?e.navigate(t):e.urlStateParam?.path?e.navigate("/"+e.basePath+e.urlStateParam?.startPath):void 0}}};var w=r(9067);r(3824);var x=r(7126),S=r(6089),$=r(3986);let C=(e={})=>{let{bubbles:t=!1,shoudFlip:r=!0,outsidePress:n,adjustToReferenceWidth:o=!1,referenceElement:a,canCloseModal:l}=e,[s,c]=i.useState(e.defaultOpen||!1),d=(0,x.jV)(),{update:u,refs:p,strategy:m,x:h,y:g,context:f}=(0,x.YF)({open:s,onOpenChange:c,elements:{reference:a?.current},nodeId:d,whileElementsMounted:!1===e.autoUpdate?void 0:S.Me,placement:e.placement||"bottom-start",middleware:[(0,$.cv)(e.offset||6),r&&(0,$.RR)(),(0,$.uY)(),(0,$.dp)({apply({elements:e}){if(o){let t=e.reference;e.floating.style.width=t?`${t?.offsetWidth}px`:""}}})]});(0,x.bQ)(f,{enabled:!1!==l,bubbles:t,outsidePress:n}),(0,i.useEffect)(()=>{e.defaultOpen&&u()},[]);let b=i.useCallback(()=>c(e=>!e),[c]),v=i.useCallback(()=>c(!0),[c]),y=i.useCallback(()=>c(!1),[c]);return{reference:p.setReference,floating:p.setFloating,toggle:b,open:v,nodeId:d,close:y,isOpen:s,styles:{position:m,top:g??0,left:h??0},context:f}},_="(prefers-reduced-motion: no-preference)";function k(){let[e,t]=(0,i.useState)(!0);return(0,i.useEffect)(()=>{var e;let r=window.matchMedia(_);t(!window.matchMedia(_).matches);let n=e=>{t(!e.matches)},o=(e,t)=>"addEventListener"in r?r.removeEventListener(e,t):r.removeListener(t);return e="change","addEventListener"in r?r.addEventListener(e,n):r.addListener(n),()=>o("change",n)},[]),e}var P=r(2505);let I=e=>{let{items:t,comparator:r,searchTermForItem:n}=e,[o,a]=i.useState(""),l=i.useMemo(()=>t.reduce((e,t)=>(e.set(t,n?.(t)),e),new Map),[t]),s=i.useMemo(()=>o?t.filter(e=>r(o,e,l.get(e))):t,[t,o]);return{searchInputProps:{onChange:e=>a(e.target.value||""),value:o},filteredItems:s}},A=(e,t)=>{i.useEffect(()=>{let r=[e].flat().filter(e=>!!e);if(r.length)return r.forEach(e=>window.addEventListener(e,t)),()=>{r.forEach(e=>window.removeEventListener(e,t))}},[e,t])}},1752:function(e,t,r){r.d(t,{l:()=>l});var n=r(2208),o=r(5518),a=r(7623),i=r(9693);function l({filterOutFactor:e,supportedFirstFactors:t}){let{strategies:r}=(0,i.v)(),l=t||[],s=l.filter(t=>t.strategy!==e?.strategy&&!(0,o.Vh)(t.strategy)).length+r.length>0,c=l.filter(t=>!t.strategy.startsWith("oauth_")&&t.strategy!==e?.strategy).filter(e=>(0,o.xT)(e)).filter(e=>"passkey"!==e.strategy||(0,n.iW)()).sort(a.U6);return{hasAnyStrategy:s,hasFirstParty:!!c,firstPartyFactors:c}}},143:function(e,t,r){r.d(t,{n:()=>i});var n=r(9144),o=r(1576),a=r(9541);function i(){let{displayConfig:e,isDevelopmentOrStaging:t}=(0,o.useEnvironment)(),r=t(),{unsafe_disableDevelopmentModeWarnings:i=!1}=(0,a.useAppearance)().parsedLayout,l=r&&i;return{showDevModeNotice:(0,n.useMemo)(()=>!l&&e.showDevModeWarning,[l,e])}}},4814:function(e,t,r){r.d(t,{E:()=>o});var n=r(9144);function o(e){let{startEmailLinkFlow:t,cancelEmailLinkFlow:r}=n.useMemo(()=>e.createEmailLinkFlow(),[e]);return n.useEffect(()=>r,[]),{startEmailLinkFlow:t,cancelEmailLinkFlow:r}}},9693:function(e,t,r){r.d(t,{v:()=>h});var n=r(6543),o=r(6096);r(4310);var a=[{provider:"google",strategy:"oauth_google",name:"Google",docsUrl:"https://clerk.com/docs/authentication/social-connections/google"},{provider:"discord",strategy:"oauth_discord",name:"Discord",docsUrl:"https://clerk.com/docs/authentication/social-connections/discord"},{provider:"facebook",strategy:"oauth_facebook",name:"Facebook",docsUrl:"https://clerk.com/docs/authentication/social-connections/facebook"},{provider:"twitch",strategy:"oauth_twitch",name:"Twitch",docsUrl:"https://clerk.com/docs/authentication/social-connections/twitch"},{provider:"twitter",strategy:"oauth_twitter",name:"Twitter",docsUrl:"https://clerk.com/docs/authentication/social-connections/twitter"},{provider:"microsoft",strategy:"oauth_microsoft",name:"Microsoft",docsUrl:"https://clerk.com/docs/authentication/social-connections/microsoft"},{provider:"tiktok",strategy:"oauth_tiktok",name:"TikTok",docsUrl:"https://clerk.com/docs/authentication/social-connections/tiktok"},{provider:"linkedin",strategy:"oauth_linkedin",name:"LinkedIn",docsUrl:"https://clerk.com/docs/authentication/social-connections/linkedin"},{provider:"linkedin_oidc",strategy:"oauth_linkedin_oidc",name:"LinkedIn",docsUrl:"https://clerk.com/docs/authentication/social-connections/linkedin-oidc"},{provider:"github",strategy:"oauth_github",name:"GitHub",docsUrl:"https://clerk.com/docs/authentication/social-connections/github"},{provider:"gitlab",strategy:"oauth_gitlab",name:"GitLab",docsUrl:"https://clerk.com/docs/authentication/social-connections/gitlab"},{provider:"dropbox",strategy:"oauth_dropbox",name:"Dropbox",docsUrl:"https://clerk.com/docs/authentication/social-connections/dropbox"},{provider:"atlassian",strategy:"oauth_atlassian",name:"Atlassian",docsUrl:"https://clerk.com/docs/authentication/social-connections/atlassian"},{provider:"bitbucket",strategy:"oauth_bitbucket",name:"Bitbucket",docsUrl:"https://clerk.com/docs/authentication/social-connections/bitbucket"},{provider:"hubspot",strategy:"oauth_hubspot",name:"HubSpot",docsUrl:"https://clerk.com/docs/authentication/social-connections/hubspot"},{provider:"notion",strategy:"oauth_notion",name:"Notion",docsUrl:"https://clerk.com/docs/authentication/social-connections/notion"},{provider:"apple",strategy:"oauth_apple",name:"Apple",docsUrl:"https://clerk.com/docs/authentication/social-connections/apple"},{provider:"line",strategy:"oauth_line",name:"LINE",docsUrl:"https://clerk.com/docs/authentication/social-connections/line"},{provider:"instagram",strategy:"oauth_instagram",name:"Instagram",docsUrl:"https://clerk.com/docs/authentication/social-connections/instagram"},{provider:"coinbase",strategy:"oauth_coinbase",name:"Coinbase",docsUrl:"https://clerk.com/docs/authentication/social-connections/coinbase"},{provider:"spotify",strategy:"oauth_spotify",name:"Spotify",docsUrl:"https://clerk.com/docs/authentication/social-connections/spotify"},{provider:"xero",strategy:"oauth_xero",name:"Xero",docsUrl:"https://clerk.com/docs/authentication/social-connections/xero"},{provider:"box",strategy:"oauth_box",name:"Box",docsUrl:"https://clerk.com/docs/authentication/social-connections/box"},{provider:"slack",strategy:"oauth_slack",name:"Slack",docsUrl:"https://clerk.com/docs/authentication/social-connections/slack"},{provider:"linear",strategy:"oauth_linear",name:"Linear",docsUrl:"https://clerk.com/docs/authentication/social-connections/linear"},{provider:"x",strategy:"oauth_x",name:"X / Twitter",docsUrl:"https://clerk.com/docs/authentication/social-connections/x-twitter-v2"},{provider:"enstall",strategy:"oauth_enstall",name:"Enstall",docsUrl:"https://clerk.com/docs/authentication/social-connections/enstall"},{provider:"huggingface",strategy:"oauth_huggingface",name:"Hugging Face",docsUrl:"https://clerk.com/docs/authentication/social-connections/huggingface"}],i=[{provider:"metamask",strategy:"web3_metamask_signature",name:"MetaMask"},{provider:"coinbase_wallet",strategy:"web3_coinbase_wallet_signature",name:"Coinbase Wallet"},{provider:"okx_wallet",strategy:"web3_okx_wallet_signature",name:"OKX Wallet"}],l=r(5747),s=r(7623);let c=a.map(e=>e.strategy),d=i.map(e=>e.strategy),u=n.v.map(e=>e.channel),p=(0,s.sq)([...[...a,...i].map(e=>[e.provider,{strategy:e.strategy,name:e.name,iconUrl:(0,o.WY)(e.provider)}]),...[...n.v].map(e=>[e.channel,{strategy:e.channel,name:e.name,iconUrl:(0,o.WY)(e.channel)}])]),m=(0,s.sq)([...[...a,...i].map(e=>[e.strategy,{id:e.provider,name:e.name,iconUrl:(0,o.WY)(e.provider)}]),...[...n.v].map(e=>[e.channel,{id:e.channel,name:e.name,iconUrl:(0,o.WY)(e.channel)}])]),h=()=>{let{socialProviderStrategies:e,web3FirstFactors:t,authenticatableSocialStrategies:r,social:n,alternativePhoneCodeChannels:o}=(0,l.O)().userSettings,a=e.filter(e=>c.includes(e)),i=e.filter(e=>!c.includes(e)&&e.startsWith("oauth_custom_")),s=r.filter(e=>c.includes(e)),h=r.filter(e=>!c.includes(e)&&e.startsWith("oauth_custom_")),g=Object.keys(n).filter(e=>e.startsWith("oauth_custom_")),f=e.filter(e=>!c.includes(e)||e.startsWith("oauth_custom_"));g.forEach(e=>{p[e.replace("oauth_","")]={strategy:e,name:n[e].name,iconUrl:n[e].logo_url||""}}),f.forEach(e=>{let t=e.replace("oauth_","");m[e]={id:t,iconUrl:n[e].logo_url||"",name:n[e].name}});let b=[...s,...h];b.sort((e,t)=>{let r=e.replace(/^oauth_custom_|^oauth_/,""),n=t.replace(/^oauth_custom_|^oauth_/,"");return r.localeCompare(n)});let v=t.filter(e=>d.includes(e)),y=o.filter(e=>u.includes(e));return{strategies:[...a,...v,...i,...y],web3Strategies:v,alternativePhoneCodeChannels:y,authenticatableOauthStrategies:b,strategyToDisplayData:m,providerToDisplayData:p}}},7432:function(e,t,r){r.d(t,{ib:()=>s});var n=r(9144);let o=new Map,a=new Set,i=e=>"string"==typeof e?e:JSON.stringify(e),l=(e,t=i)=>{let r=t(e),l=(0,n.useCallback)(()=>o.get(r),[r]),s=(0,n.useCallback)(e=>{o.set(r,"function"==typeof e?e(l()):e),a.forEach(e=>e())},[r]),c=(0,n.useCallback)(()=>{s({isLoading:!1,isValidating:!1,data:null,error:null,cachedAt:void 0})},[s]);return{getCache:l,setCache:s,subscribeCache:(0,n.useCallback)(e=>(a.add(e),()=>a.delete(e)),[]),clearCache:c}},s=(e,t,r,o)=>{let a={resourceId:o,params:t},{subscribeCache:s,getCache:c,setCache:d,clearCache:u}=l(a),{subscribeCache:p,getCache:m,setCache:h}=l({...a,rv:!0}),g=r?.staleTime??12e4,f=r?.throttleTime||0,b=(0,n.useRef)(e);if(f<0)throw Error("ClerkJS: A negative value for `throttleTime` is not allowed ");let v=(0,n.useSyncExternalStore)(s,c),y=(0,n.useSyncExternalStore)(p,m),w=(0,n.useCallback)(()=>{d(e=>({...e,cachedAt:0})),h(e=>({isLoading:!1,isValidating:!1,error:null,data:(e?.data||0)+1}))},[d,h]);return(0,n.useEffect)(()=>{let e=!b.current,n=void 0===c()?.cachedAt||Date.now()-(c()?.cachedAt||0)>=g,o=c()?.isValidating??!1;if(e||!n||o)return;let a=performance.now();d({data:c()?.data??null,isLoading:!c()?.data,isValidating:!0,error:null}),b.current(t).then(e=>{if(void 0!==e){let t=Array.isArray(e)?e:"object"==typeof e?{...e}:e;setTimeout(()=>{d({data:t,isLoading:!1,isValidating:!1,error:null,cachedAt:Date.now()}),r?.onSuccess?.(t)},f-(performance.now()-a))}}).catch(e=>{d({data:c()?.data??null,isLoading:!1,isValidating:!1,error:e,cachedAt:Date.now()})})},[i(t),d,c,y]),{...v,setCache:d,invalidate:u,revalidate:w}}},5872:function(e,t,r){r.d(t,{e:()=>s,q:()=>c});var n=r(3799),o=r(1085),a=r(7623),i=r(7432);let l={pageSize:20},s=(e=!0)=>{let{organization:t}=(0,n.o8)(),{data:r,isLoading:o}=(0,i.ib)(e&&t?.id?({pageSize:e,initialPage:r})=>t.getRoles({pageSize:e,initialPage:r}):void 0,{...l,orgId:t?.id});return{isLoading:o,options:r?.data?.map(e=>({value:e.key,label:e.name}))}},c=()=>{let{t:e}=(0,o.zJ)();return{localizeCustomRole:t=>e((0,a.Oi)(t))||e((0,a.JD)(t))}}},4264:function(e,t,r){r.d(t,{j:()=>o});var n=r(3799);let o=e=>{let t=(0,n.cL)().client.signedInSessions;return{signedInSessions:t,otherSessions:t.filter(t=>t.user?.id!==e.user?.id)}}},9067:function(e,t,r){r.d(t,{i:()=>s,p:()=>c});var n=r(5100),o=r(9144),a=r(33),i=r(1085),l=r(3824);let s=(e,t)=>{let{t:r,locale:s}=(0,i.zJ)(),{onValidationError:c=n.ZT,onValidationSuccess:d=n.ZT,onValidationWarning:u=n.ZT,onValidationInfo:p=n.ZT,onValidationComplexity:m}=t||{},h=(0,o.useCallback)(t=>{if(Object.values(t?.complexity||{}).length>0){let n=(0,l.e)({config:e,t:r,failedValidations:t.complexity,locale:s});return t.complexity?.min_length?p(n):c(n)}return t?.strength?.state==="fail"?c(t.strength.keys.map(e=>r((0,i.u1)(e))).join(" ")):t?.strength?.state==="pass"?u(t.strength.keys.map(e=>r((0,i.u1)(e))).join(" ")):d()},[t,r,s]);return{validatePassword:(0,o.useMemo)(()=>(0,a.z)(e,{onValidation:h,onValidationComplexity:m}),[h])}},c=({passwordField:e,confirmPasswordField:t})=>{let{t:r}=(0,i.zJ)(),n=(0,o.useCallback)(t=>e.value===t,[e.value]),a=(0,o.useMemo)(()=>n(t.value),[n,t.value]);return{setConfirmPasswordFeedback:(0,o.useCallback)(e=>{n(e)?t.setSuccess(r((0,i.u1)("formFieldError__matchingPasswords"))):t.setError(r((0,i.u1)("formFieldError__notMatchingPasswords")))},[t.setError,t.setSuccess,r,n]),isPasswordMatch:a}}},3824:function(e,t,r){r.d(t,{e:()=>i}),r(9144);var n=r(1085),o=r(7623);let a={max_length:["unstable__errors.passwordComplexity.maximumLength","length"],min_length:["unstable__errors.passwordComplexity.minimumLength","length"],require_numbers:"unstable__errors.passwordComplexity.requireNumbers",require_lowercase:"unstable__errors.passwordComplexity.requireLowercase",require_uppercase:"unstable__errors.passwordComplexity.requireUppercase",require_special_char:"unstable__errors.passwordComplexity.requireSpecialCharacter"},i=({config:e,failedValidations:t,locale:r,t:i})=>{if(!t||0===Object.keys(t).length)return"";let l=t?.min_length||!1,s=Object.entries(t).filter(e=>!l||"min_length"===e[0]).filter(([,e])=>!!e).map(([t])=>{let r=a[t];if(Array.isArray(r)){let[o,a]=r;return i((0,n.u1)(o,{[a]:e[t]}))}return i((0,n.u1)(r))}),c=(0,o.z$)(s,r);return(0,o.$M)(`${i((0,n.u1)("unstable__errors.passwordComplexity.sentencePrefix"))} ${c}`)}},4511:function(e,t,r){r.d(t,{z:()=>a});var n=r(1576),o=r(2464);function a(){let{organizationSettings:e}=(0,n.useEnvironment)(),t=e.forceOrganizationSelection;(0,o.ib)(t?()=>Promise.all([r.e("344"),r.e("200"),r.e("573"),r.e("192"),r.e("158"),r.e("877")]).then(r.bind(r,1618)):void 0,"preloadComponent",{staleTime:1/0})}},2505:function(e,t,r){r.d(t,{P:()=>a});let n=0,o=()=>{};function a(){return{enableScrollLock:()=>{1==++n&&(o=function(){let e=/iP(hone|ad|od)|iOS/.test(function(){let e=navigator.userAgentData;return e?.platform?e.platform:navigator.platform}()),t=document.body.style,r=Math.round(document.documentElement.getBoundingClientRect().left)+document.documentElement.scrollLeft?"paddingLeft":"paddingRight",n=window.innerWidth-document.documentElement.clientWidth,o=t.left?parseFloat(t.left):window.scrollX,a=t.top?parseFloat(t.top):window.scrollY;if(t.overflow="hidden",n&&(t[r]=`${n}px`),e){let e=window.visualViewport?.offsetLeft||0,r=window.visualViewport?.offsetTop||0;Object.assign(t,{position:"fixed",top:`${-(a-Math.floor(r))}px`,left:`${-(o-Math.floor(e))}px`,right:"0"})}return()=>{Object.assign(t,{overflow:"",[r]:""}),e&&(Object.assign(t,{position:"",top:"",left:"",right:""}),window.scrollTo(o,a))}}())},disableScrollLock:()=>{0==--n&&o()}}}},4875:function(e,t,r){r.d(t,{E:()=>l});var n=r(3799),o=r(9144),a=r(1576),i=r(4676);let l=(e=2e3)=>{let{queryString:t}=(0,i.useRouter)(),{setActive:r}=(0,n.cL)(),{afterSignInUrl:l}=(0,a.useSignInContext)();(0,o.useEffect)(()=>{let n;let o=new URLSearchParams(t).get("createdSessionId");return o&&(n=setTimeout(()=>{r({session:o,redirectUrl:l})},e)),()=>{n&&clearTimeout(n)}},[r,l,t])}},3234:function(e,t,r){r.d(t,{H:()=>l});var n=r(3799),o=r(9144),a=r(6917),i=r(1576);function l(){let e=(0,n.cL)(),{supportEmail:t}=(0,i.useOptions)(),{displayConfig:r}=(0,i.useEnvironment)(),{supportEmail:l}=r;return o.useMemo(()=>t||l||(0,a.aR)({localPart:"support",frontendApi:e.frontendApi}),[e.frontendApi,t,l])}},3746:function(e,t,r){r.d(t,{x:()=>a});var n=r(9144),o=r(4676);let a=(e,t=0)=>{let r=(0,o.useRouter)(),[a,i]=n.useState((()=>{let n=r.queryParams.tab,o=Object.entries(e).find(([e,t])=>t===n)?.[0];return o?parseInt(o,10):t})());return{selectedTab:a,handleTabChange:t=>{i(t);let n=r.currentPath;r.navigate(n,{searchParams:new URLSearchParams({tab:e[t]||""})})}}}},4174:function(e,t,r){r.d(t,{Nn:()=>tR,I$:()=>tZ,mm:()=>eB,iU:()=>eG,bA:()=>t_,nR:()=>rn,oX:()=>ra,lv:()=>t8,xP:()=>tD,n5:()=>rm,Q:()=>rg,Ic:()=>eO,EK:()=>tn,Y4:()=>ez,_M:()=>e5,cp:()=>tY,v2:()=>tH,Kh:()=>t5,UW:()=>tb,eu:()=>tu,SV:()=>t$,g4:()=>rl,W:()=>eK,GT:()=>ty,TU:()=>e7,ds:()=>t6,uU:()=>e8,CK:()=>tl,Fh:()=>tm,hc:()=>eU,jC:()=>e6,v3:()=>t1,IG:()=>tA,uR:()=>t7,x8:()=>tt,ET:()=>tG,Jr:()=>eJ,LG:()=>tO,bR:()=>tx,gO:()=>eH,p8:()=>eD,Yt:()=>tV,Qi:()=>tg,TZ:()=>eY,N6:()=>tP,ij:()=>tK,z6:()=>rt,qy:()=>rc,LZ:()=>eE,tc:()=>ta,Nj:()=>eV,gq:()=>ru,ou:()=>tJ,aB:()=>tc,fU:()=>e1,kh:()=>tE,cN:()=>tU});var n,o,a,i,l,s,c,d,u,p,m,h,g,f,b,v,y,w,x,S,$,C,_,k,P,I,A,T,R,B,Z,z,D,L,E,F,O,M,U,j,V,W,H,N,K,X,Y,q,G,Q,J,ee,et,er,en,eo,ea,ei,el,es,ec,ed,eu,ep,em,eh,eg,ef,eb,ev,ey,ew,ex,eS,e$,eC,e_,ek,eP,eI,eA,eT=r(9144);function eR(){return(eR=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let eB=e=>eT.createElement("svg",eR({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 36 24"},e),n||(n=eT.createElement("circle",{cx:18,cy:12,r:11.5,fillOpacity:.1,strokeOpacity:.4,stroke:"currentColor",strokeDasharray:"2 2"})),o||(o=eT.createElement("path",{d:"M18.75 7.75a.75.75 0 1 0-1.5 0v3.5h-3.5a.75.75 0 1 0 0 1.5h3.5v3.5a.75.75 0 1 0 1.5 0v-3.5h3.5a.75.75 0 1 0 0-1.5h-3.5v-3.5Z"})));function eZ(){return(eZ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let ez=e=>eT.createElement("svg",eZ({xmlns:"http://www.w3.org/2000/svg",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},e),a||(a=eT.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20 12H4m6 6-6-6 6-6"}))),eD=e=>eT.createElement("svg",e,i||(i=eT.createElement("path",{fill:"currentColor",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"m7.25 5-3.5-2.25v4.5L7.25 5Z"})));function eL(){return(eL=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let eE=e=>eT.createElement("svg",eL({xmlns:"http://www.w3.org/2000/svg",fill:"none",stroke:"currentColor",viewBox:"0 0 20 20"},e),l||(l=eT.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3.3 10h13.4m-5-5 5 5-5 5"})));function eF(){return(eF=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let eO=e=>eT.createElement("svg",eF({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},e),s||(s=eT.createElement("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7.5 7.5 3m0 0L12 7.5M7.5 3v13.5m13.5 0L16.5 21m0 0L12 16.5m4.5 4.5V7.5"})));function eM(){return(eM=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let eU=e=>eT.createElement("svg",eM({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),c||(c=eT.createElement("path",{d:"M7 9.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"})),d||(d=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4 2c-1.105 0-2 .895-2 2v8c0 1.105.895 2 2 2h8c1.105 0 2-.895 2-2V4c0-1.105-.895-2-2-2H4Zm3 9a3.002 3.002 0 0 0 2.906-2.25H12a.75.75 0 0 0 0-1.5H9.906A3.002 3.002 0 0 0 4 8c0 .941.438 1.785 1.117 2.336A2.985 2.985 0 0 0 7 11Z"})));function ej(){return(ej=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let eV=e=>eT.createElement("svg",ej({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 12"},e),u||(u=eT.createElement("path",{fill:"currentColor",d:"M2 0a2 2 0 0 0-2 2v1h16V2a2 2 0 0 0-2-2H2Z"})),p||(p=eT.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M16 5H0v5c0 1.1.9 2 2 2h12a2 2 0 0 0 2-2V5ZM2 9c0-.6.4-1 1-1h1a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1Zm5-1a1 1 0 1 0 0 2h1a1 1 0 1 0 0-2H7Z",clipRule:"evenodd"})));function eW(){return(eW=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let eH=e=>eT.createElement("svg",eW({fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 48 48"},e),m||(m=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.996 16.178A13.439 13.439 0 0 0 10.5 24c0 7.456 6.044 13.5 13.5 13.5 2.916 0 5.615-.924 7.822-2.496L12.996 16.178Zm3.182-3.182 18.826 18.826A13.439 13.439 0 0 0 37.5 24c0-7.456-6.044-13.5-13.5-13.5-2.916 0-5.615.924-7.822 2.496Zm20.55 23.731A17.944 17.944 0 0 0 42 24c0-9.941-8.059-18-18-18S6 14.059 6 24s8.059 18 18 18c4.97 0 9.468-2.014 12.725-5.27l.003-.002v-.001Z",fill:"currentColor"})));function eN(){return(eN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let eK=e=>eT.createElement("svg",eN({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),h||(h=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.55 4.24a.75.75 0 0 1-.04 1.06L6.604 7.998l2.655 2.45a.744.744 0 0 1 .021.022l.25.25a.75.75 0 1 1-1.06 1.06l-.24-.24-3.239-2.989a.75.75 0 0 1-.001-1.1l3.5-3.25a.75.75 0 0 1 1.06.039Z"})));function eX(){return(eX=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let eY=e=>eT.createElement("svg",eX({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),g||(g=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.45 11.76a.75.75 0 0 1 .04-1.06l2.906-2.698-2.655-2.45a.756.756 0 0 1-.021-.022l-.25-.25a.75.75 0 0 1 1.06-1.06l.24.24 3.239 2.989a.75.75 0 0 1 .001 1.1l-3.5 3.25a.75.75 0 0 1-1.06-.039Z"})));function eq(){return(eq=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let eG=e=>eT.createElement("svg",eq({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16"},e),f||(f=eT.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M14.4 4v6.4a1.6 1.6 0 0 1-1.6 1.6h-4l-4 3.2V12H3.2a1.6 1.6 0 0 1-1.6-1.6V4a1.6 1.6 0 0 1 1.6-1.6h9.6A1.6 1.6 0 0 1 14.4 4ZM5.6 6.4H4V8h1.6V6.4Zm1.6 0h1.6V8H7.2V6.4Zm4.8 0h-1.6V8H12V6.4Z",clipRule:"evenodd"})));function eQ(){return(eQ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let eJ=e=>eT.createElement("svg",eQ({fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 10 10"},e),b||(b=eT.createElement("path",{d:"m1 6 3 3 5-8",stroke:"currentColor",strokeWidth:1.25,strokeLinecap:"round",strokeLinejoin:"round"})));function e0(){return(e0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let e1=e=>eT.createElement("svg",e0({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 20 20"},e),v||(v=eT.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.7-9.3a1 1 0 0 0-1.4-1.4L9 10.58l-1.3-1.3a1 1 0 0 0-1.4 1.42l2 2a1 1 0 0 0 1.4 0l4-4Z",clipRule:"evenodd"})));function e2(){return(e2=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let e5=e=>eT.createElement("svg",e2({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),y||(y=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.24 6.2a.75.75 0 0 1 1.06.04l2.698 2.906 2.45-2.655a.744.744 0 0 1 .022-.021l.25-.25a.75.75 0 1 1 1.06 1.06l-.24.24-2.989 3.239a.75.75 0 0 1-1.1.001L4.2 7.26A.75.75 0 0 1 4.24 6.2Z"})));function e4(){return(e4=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let e6=e=>eT.createElement("svg",e4({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),w||(w=eT.createElement("path",{d:"M5.53 9.22a.75.75 0 0 0-1.06 1.06l3 3a.75.75 0 0 0 1.06 0l3-3a.75.75 0 1 0-1.06-1.06L8 11.69 5.53 9.22ZM5.53 6.78a.75.75 0 0 1-1.06-1.06l3-3a.75.75 0 0 1 1.06 0l3 3a.75.75 0 0 1-1.06 1.06L8 4.31 5.53 6.78Z"})));function e3(){return(e3=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let e7=e=>eT.createElement("svg",e3({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 12 16"},e),x||(x=eT.createElement("path",{fill:"currentColor",d:"M4 1c0-.6.4-1 1-1h2a1 1 0 0 1 0 2H5a1 1 0 0 1-1-1Z"})),S||(S=eT.createElement("path",{fill:"currentColor",d:"M2 1a2 2 0 0 0-2 2v11c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V3a2 2 0 0 0-2-2 3 3 0 0 1-3 3H5a3 3 0 0 1-3-3Z"})));function e9(){return(e9=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let e8=e=>eT.createElement("svg",e9({fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 12 12"},e),$||($=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.129.33A.75.75 0 0 1 3.75 0h4.5a.75.75 0 0 1 .707.5h.293A2.75 2.75 0 0 1 12 3.25v6A2.75 2.75 0 0 1 9.25 12h-6.5A2.75 2.75 0 0 1 0 9.25v-6A2.75 2.75 0 0 1 2.75.5h.293a.75.75 0 0 1 .086-.17ZM3.442 2H2.75c-.69 0-1.25.56-1.25 1.25v6c0 .69.56 1.25 1.25 1.25h6.5c.69 0 1.25-.56 1.25-1.25v-6C10.5 2.56 9.94 2 9.25 2h-.692l-.147.368a2.596 2.596 0 0 1-4.822 0L3.442 2Zm3.7-.5H4.858l.124.31a1.096 1.096 0 0 0 2.036 0l.124-.31Z",fill:"currentColor"})));function te(){return(te=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tt=e=>eT.createElement("svg",te({fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 10 10"},e),C||(C=eT.createElement("path",{d:"M1.791.722a.756.756 0 0 0-1.07 1.07L3.932 5 .72 8.209a.756.756 0 1 0 1.07 1.07L5 6.068l3.209 3.21a.756.756 0 0 0 1.07-1.07L6.068 5l3.21-3.209a.756.756 0 1 0-1.07-1.07L5 3.932 1.791.72Z",fill:"currentColor"})));function tr(){return(tr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tn=e=>eT.createElement("svg",tr({fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 12 8"},e),_||(_=eT.createElement("path",{d:"M7.22 6.47a.75.75 0 0 0 1.06 1.06l3-3a.75.75 0 0 0 0-1.06l-3-3a.75.75 0 0 0-1.06 1.06L9.69 4 7.22 6.47ZM4.78 6.47a.75.75 0 0 1-1.06 1.06l-3-3a.75.75 0 0 1 0-1.06l3-3a.75.75 0 0 1 1.06 1.06L2.31 4l2.47 2.47Z",fill:"currentColor"})));function to(){return(to=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let ta=e=>eT.createElement("svg",to({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),k||(k=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.559 2.536A.667.667 0 0 1 7.212 2h1.574a.667.667 0 0 1 .653.536l.22 1.101c.466.178.9.429 1.287.744l1.065-.36a.667.667 0 0 1 .79.298l.787 1.362a.666.666 0 0 1-.136.834l-.845.742c.079.492.079.994 0 1.486l.845.742a.666.666 0 0 1 .137.833l-.787 1.363a.667.667 0 0 1-.791.298l-1.065-.36c-.386.315-.82.566-1.286.744l-.22 1.101a.666.666 0 0 1-.654.536H7.212a.666.666 0 0 1-.653-.536l-.22-1.101a4.664 4.664 0 0 1-1.287-.744l-1.065.36a.666.666 0 0 1-.79-.298L2.41 10.32a.667.667 0 0 1 .136-.834l.845-.743a4.7 4.7 0 0 1 0-1.485l-.845-.742a.667.667 0 0 1-.137-.833l.787-1.363a.667.667 0 0 1 .791-.298l1.065.36c.387-.315.821-.566 1.287-.744l.22-1.101ZM7.999 10a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"})));function ti(){return(ti=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tl=e=>eT.createElement("svg",ti({viewBox:"0 0 12 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),P||(P=eT.createElement("path",{d:"M7.94 10.23v2.078a.687.687 0 0 1-.682.692h-5.91a.676.676 0 0 1-.482-.203.698.698 0 0 1-.2-.49V4.463c0-.383.306-.693.682-.693h1.137c.304 0 .609.026.909.077m4.545 6.385h2.046c.376 0 .682-.31.682-.693v-3c0-2.744-1.966-5.022-4.546-5.462A5.41 5.41 0 0 0 5.212 1H4.076a.687.687 0 0 0-.682.692v2.154m4.545 6.385H4.076a.677.677 0 0 1-.482-.203.698.698 0 0 1-.2-.49V3.846m7.273 4.077V6.77c0-.55-.216-1.079-.6-1.468a2.03 2.03 0 0 0-1.446-.609h-.909a.677.677 0 0 1-.482-.202.698.698 0 0 1-.2-.49v-.923a2.105 2.105 0 0 0-.599-1.469A2.043 2.043 0 0 0 4.985 1h-.682",stroke:"currentColor",strokeWidth:1.2,strokeLinecap:"round",strokeLinejoin:"round"})));function ts(){return(ts=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tc=e=>eT.createElement("svg",ts({xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 16 16"},e),I||(I=eT.createElement("path",{d:"M1.5 5a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2h-13Z"})),A||(A=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.5 6.5h13V11a2 2 0 0 1-2 2h-9a2 2 0 0 1-2-2V6.5ZM3.7 9a.2.2 0 0 0-.2.2v1.6c0 .*********.2h2.6a.2.2 0 0 0 .2-.2V9.2a.2.2 0 0 0-.2-.2H3.7Z"})));function td(){return(td=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tu=e=>eT.createElement("svg",td({fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},e),T||(T=eT.createElement("path",{d:"M2.91 1.452c0-.58.077-.83.23-1.079C3.33.166 3.6 0 4.212 0h23.617c.536 0 .765.124.957.332.191.207.268.498.268 1.12v17.013c0 .58-.077.83-.192.996a1.06 1.06 0 0 1-.37.338.968.968 0 0 1-.472.118H3.905c-.306 0-.612-.125-.765-.415-.153-.166-.23-.415-.23-1.037V1.452Z",fill:"#000"})),R||(R=eT.createElement("path",{d:"M3.445 19.334h25.072c.115 0 .23-.083.306-.166.077-.083.077-.207.077-.58V1.45c0-.498-.038-.83-.23-.995-.191-.208-.383-.29-.842-.29H4.211c-.498 0-.766.124-.957.331-.153.166-.192.415-.192.954v17.137c0 .374 0 .498.077.581.***************.306.166Z",fill:"#575757"})),B||(B=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15.999.912a.109.109 0 0 0 .*************** 0 0 0 .045-.046.132.132 0 0 0 0-.128.119.119 0 0 0-.045-.046.108.108 0 0 0-.*************** 0 0 0-.118-.006.12.12 0 0 0-.*************** 0 0 0 0 .128.12.12 0 0 0 .*************** 0 0 0 .118-.006Z",fill:"#000",stroke:"#000",strokeWidth:.3})),Z||(Z=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0 19.5v-.207h32v.207s-.727.25-1.531.332c-.536.042-1.416.166-3.407.166H5.091c-1.723 0-3.177-.124-3.828-.207C.613 19.708 0 19.5 0 19.5Z",fill:"#444"})),z||(z=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.941 1.328h24.115v16.349H3.941V1.328Z",fill:"#000"})));function tp(){return(tp=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tm=e=>eT.createElement("svg",tp({xmlns:"http://www.w3.org/2000/svg",viewBox:"670.6 72.3 84 76"},e),D||(D=eT.createElement("path",{fill:"var(--cl-chassis-screen, #6D6D6D)",fillRule:"evenodd",d:"M712.5 107.2v-.6h.1l.2.1v7.2a.1.1 0 0 1-.2.2l-.1-.3v-6.6Z",clipRule:"evenodd"})),L||(L=eT.createElement("path",{fill:"var(--cl-chassis-back, #6D6D6D)",fillRule:"evenodd",d:"M697.4 100v-.7h-.2a.1.1 0 0 0-.1.2v4.4s0 .2.2.2V100Z",clipRule:"evenodd"})),E||(E=eT.createElement("path",{fill:"var(--cl-chassis-screen, #6D6D6D)",fillRule:"evenodd",d:"M697.4 94v-.7h-.2a.1.1 0 0 0-.1.2v4.4s0 .2.2.2V94Z",clipRule:"evenodd"})),F||(F=eT.createElement("path",{fill:"var(--cl-chassis-back, #363636)",d:"M722.7 78.6c3.6 0 5.5 2.1 5.5 5.7v52.4c0 3.4-2.3 5.3-5.8 5.3H703c-3.8 0-5.8-2.4-5.7-5.4V84.3c0-3.6 2-5.7 5.6-5.7h19.8Z"})),O||(O=eT.createElement("path",{fill:"var(--cl-chassis-screen, #363636)",stroke:"var(--cl-chassis-bottom, black)",strokeWidth:.5,d:"M722.3 79.2c3.7 0 5.4 1.8 5.4 5.4v52c0 3.2-2.2 5-5.5 5h-19c-3.2 0-5.4-2-5.4-5v-52c0-3.6 1.8-5.4 5.5-5.4h19Z"})),M||(M=eT.createElement("path",{fill:"var(--cl-screen, black)",fillRule:"evenodd",d:"M704.9 80.3c.2 0 .3.1.3.4v.2c0 .9.8 1.7 1.6 1.7h11.8c1 0 1.7-.8 1.7-1.7v-.2c0-.3.1-.4.3-.4h3c1.6 0 3 1.7 3 3.3V137c0 1.7-1.5 3.3-3.4 3.3h-21c-2.1 0-3.3-1.3-3.3-3.2V83.6c0-1.6 1.3-3.3 2.9-3.3h3Z",clipRule:"evenodd"})),U||(U=eT.createElement("path",{fillRule:"evenodd",d:"M715.3 81.2a.3.3 0 0 0-.2-.4.3.3 0 1 0-.******* 0 0 0 .4-.2Zm-5.1-.2c0 .2 0 .3.2.3h2.9a.3.3 0 0 0 .2-.3.3.3 0 0 0-.2-.2h-2.9a.3.3 0 0 0-.2.2Z",clipRule:"evenodd"})));function th(){return(th=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tg=e=>eT.createElement("svg",th({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16"},e),j||(j=eT.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16ZM5 7H3v2h2V7Zm8 0h-2v2h2V7ZM7 7h2v2H7V7Z",clipRule:"evenodd"})));function tf(){return(tf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tb=e=>eT.createElement("svg",tf({fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 14 14"},e),V||(V=eT.createElement("path",{d:"M1 10v1.5A1.5 1.5 0 0 0 2.5 13h9a1.5 1.5 0 0 0 1.5-1.5V10m-3-3-3 3m0 0L4 7m3 3V1",stroke:"currentColor",strokeWidth:1.2,strokeLinecap:"round",strokeLinejoin:"round"})));function tv(){return(tv=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let ty=e=>eT.createElement("svg",tv({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),W||(W=eT.createElement("path",{d:"M2.75 2.75A1.75 1.75 0 0 0 1 4.5v1.016l6.51 3.693a1.094 1.094 0 0 0 .98 0L15 5.517V4.5a1.75 1.75 0 0 0-1.75-1.75H2.75Z",fill:"#42434D"})),H||(H=eT.createElement("path",{d:"m15 6.984-5.924 3.4a2.406 2.406 0 0 1-2.152 0L1 6.983V11.5a1.75 1.75 0 0 0 1.75 1.75h10.5A1.75 1.75 0 0 0 15 11.5V6.984Z",fill:"#42434D"})));function tw(){return(tw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tx=e=>eT.createElement("svg",tw({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 14 14"},e),N||(N=eT.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M13.4 7A6.4 6.4 0 1 1 .6 7a6.4 6.4 0 0 1 12.8 0Zm-5.6 3.2a.8.8 0 1 1-1.6 0 .8.8 0 0 1 1.6 0ZM7 3a.8.8 0 0 0-.8.8V7a.8.8 0 0 0 1.6 0V3.8A.8.8 0 0 0 7 3Z",clipRule:"evenodd"})));function tS(){return(tS=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let t$=e=>eT.createElement("svg",tS({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),K||(K=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8.866 3.012a.988.988 0 0 0-1.732 0L2.13 12.004c-.372.67.106 1.496.866 1.496h10.01c.76 0 1.238-.827.866-1.496L8.866 3.012Zm-1.66 5.74a.798.798 0 0 0 1.593 0l.14-2.254a.94.94 0 1 0-1.875 0l.141 2.253Zm.046 2.498a.75.75 0 1 0 1.5 0 .75.75 0 0 0-1.5 0Z"})));function tC(){return(tC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let t_=e=>eT.createElement("svg",tC({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),X||(X=eT.createElement("path",{d:"M8 9.607c.421 0 .825-.17 1.123-.47a1.617 1.617 0 0 0 0-2.273 1.578 1.578 0 0 0-2.246 0 1.617 1.617 0 0 0 0 2.272c.298.302.702.471 1.123.471Z"})),Y||(Y=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.07 8.38a1.073 1.073 0 0 1 0-.763 6.42 6.42 0 0 1 2.334-2.99A6.302 6.302 0 0 1 8 3.5c2.704 0 5.014 1.71 5.93 ************.093.518 0 .763a6.418 6.418 0 0 1-2.334 2.99A6.301 6.301 0 0 1 8 12.5c-2.704 0-5.013-1.71-5.93-4.12ZM10.54 8c0 .682-.267 1.336-.743 1.818A2.526 2.526 0 0 1 8 10.571c-.674 0-1.32-.27-1.796-.753A2.587 2.587 0 0 1 5.459 8c0-.682.268-1.336.745-1.818A2.525 2.525 0 0 1 8 5.429c.674 0 1.32.27 1.797.753.476.482.744 1.136.744 1.818Z"})));function tk(){return(tk=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tP=e=>eT.createElement("svg",tk({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),q||(q=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M3.732 3.126a.48.48 0 0 0-.662.011.465.465 0 0 0-.012.651l9.211 9.063a.477.477 0 0 0 .527.115.479.479 0 0 0 .263-.26.46.46 0 0 0-.117-.518l-1.108-1.09A6.278 6.278 0 0 0 13.93 8.36a1.016 1.016 0 0 0 0-.74 6.27 6.27 0 0 0-2.333-2.909A6.428 6.428 0 0 0 8 3.613a6.407 6.407 0 0 0-3.014.747L3.732 3.126Zm2.84 2.794.694.682a1.61 1.61 0 0 1 1.858.28A1.54 1.54 0 0 1 9.41 8.71l.693.683a2.47 2.47 0 0 0-.304-3.174 2.573 2.573 0 0 0-3.226-.3Z"})),G||(G=eT.createElement("path",{d:"m8.476 10.445 1.602 1.576a6.437 6.437 0 0 1-2.077.342c-2.705 0-5.014-1.662-5.931-4.006a1.016 1.016 0 0 1 0-.741c.31-.79.78-1.51 1.382-2.115l2.052 2.02c-.078.4-.054.813.067 ************.34.744.632 1.033a2.58 2.58 0 0 0 2.272.688Z"})));function tI(){return(tI=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tA=e=>eT.createElement("svg",tI({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 48 48"},e),Q||(Q=eT.createElement("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3.6,d:"M11.127 39.944c.442-.987.847-1.989 1.218-3.01M30.3 43.801c.723-1.89 1.197-4.02 1.725-5.962M16.165 5.997a20.528 20.528 0 0 1 9.432-1.77 20.308 20.308 0 0 1 9.176 2.687c2.764 1.602 5.042 3.83 6.622 6.472C42.974 16.03 43.802 19 43.8 22.022c0 3.94-.335 7.807-.98 11.58l-2.02 10.2M4.2 30.985a35.346 35.346 0 0 0 1.15-8.963c0-3.875 1.336-7.463 3.605-10.386m15.62 10.383c.008 7.484-1.62 14.893-4.775 21.782m-5.056-17.4c.144-1.442.218-2.902.218-4.38 0-2.362 1.013-4.628 2.815-6.299 1.803-1.67 4.248-2.609 6.797-2.609 2.55 0 4.995.939 6.797 2.61 1.803 1.67 2.816 3.936 2.816 6.298 0 1.084-.028 2.161-.087 3.232l-.687 4.186"})));function tT(){return(tT=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tR=e=>eT.createElement("svg",tT({fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),J||(J=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M12.204 3.01A2 2 0 0 1 14 5v6a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 1.796-1.99L4 3h8l.204.01ZM4 7a.5.5 0 0 0-.5.5V8h1.396c.672 0 1.28.396 1.552 1.009.598 1.344 2.506 1.344 3.104 0A1.699 1.699 0 0 1 11.104 8H12.5v-.5A.5.5 0 0 0 12 7H4Zm0-2.5a.5.5 0 0 0-.5.5v.564c.096-.024.195-.043.296-.053L4 5.5h8l.204.01c.************.296.054V5a.5.5 0 0 0-.5-.5H4Z",fill:"currentColor"})));function tB(){return(tB=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tZ=e=>eT.createElement("svg",tB({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16"},e),ee||(ee=eT.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M14.4 8A6.4 6.4 0 1 1 1.6 8a6.4 6.4 0 0 1 12.8 0ZM8.8 4.8a.8.8 0 1 1-1.6 0 .8.8 0 0 1 1.6 0ZM7.2 7.2a.8.8 0 1 0 0 1.6v2.4a.8.8 0 0 0 .8.8h.8a.8.8 0 0 0 0-1.6V8a.8.8 0 0 0-.8-.8h-.8Z",clipRule:"evenodd"})));function tz(){return(tz=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tD=e=>eT.createElement("svg",tz({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16"},e),et||(et=eT.createElement("path",{fill:"currentColor",d:"M10.068 3.668a1.6 1.6 0 0 1 2.263 2.263l-2.4 2.4a1.6 1.6 0 0 1-2.263 0 .8.8 0 1 0-1.131 1.131 3.2 3.2 0 0 0 4.525 0l2.4-2.4a3.2 3.2 0 1 0-4.525-4.525l-1.2 1.2a.8.8 0 1 0 1.131 1.131l1.2-1.2Z"})),er||(er=eT.createElement("path",{fill:"currentColor",d:"M6.068 7.668a1.6 1.6 0 0 1 2.263 0 .8.8 0 1 0 1.131-1.131 3.2 3.2 0 0 0-4.525 0l-2.4 2.4a3.2 3.2 0 0 0 4.525 4.525l1.2-1.2a.8.8 0 0 0-1.131-1.131l-1.2 1.2a1.6 1.6 0 0 1-2.263-2.263l2.4-2.4Z"})));function tL(){return(tL=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tE=e=>eT.createElement("svg",tL({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16"},e),en||(en=eT.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M4 7.2V5.6a4 4 0 1 1 8 0v1.6a1.6 1.6 0 0 1 1.6 1.6v4a1.6 1.6 0 0 1-1.6 1.6H4a1.6 1.6 0 0 1-1.6-1.6v-4A1.6 1.6 0 0 1 4 7.2Zm6.4-1.6v1.6H5.6V5.6a2.4 2.4 0 0 1 4.8 0Z",clipRule:"evenodd"})));function tF(){return(tF=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tO=e=>eT.createElement("svg",tF({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16"},e),eo||(eo=eT.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M8 3.5c-.2 0-.398.013-.59.038a.75.75 0 0 1-.196-1.487 6.05 6.05 0 0 1 1.572 0 .75.75 0 0 1-.195 1.487A4.55 4.55 0 0 0 8 3.5Zm-2.385-.238a.75.75 0 0 1-.186 1.044c-.287.2-.55.434-.783.694a.75.75 0 1 1-1.118-1 6.03 6.03 0 0 1 1.043-.924.75.75 0 0 1 1.044.186Zm4.77 0a.75.75 0 0 1 1.044-.186c.383.267.733.577 1.043.924a.75.75 0 1 1-1.118 1 4.528 4.528 0 0 0-.783-.694.75.75 0 0 1-.186-1.044ZM8 6.5a1 1 0 0 0-1 1V8h2v-.5a1 1 0 0 0-1-1ZM10.5 8v-.5a2.5 2.5 0 0 0-5 0V8h-.75a.75.75 0 0 0-.75.75v3.5c0 .966.783 1.75 1.75 1.75h4.5A1.75 1.75 0 0 0 12 12.25v-3.5a.75.75 0 0 0-.75-.75h-.75Zm0 1.5h-5v2.75c0 .*************.25h4.5a.25.25 0 0 0 .25-.25V9.5ZM3.181 5.781a.75.75 0 0 1 .505.933 4.477 4.477 0 0 0-.166.857.75.75 0 1 1-1.493-.142c.037-.393.112-.775.222-1.143a.75.75 0 0 1 .932-.505Zm9.638 0a.75.75 0 0 1 .932.505c.11.368.185.75.222 1.143a.75.75 0 0 1-1.493.142 4.478 4.478 0 0 0-.166-.857.75.75 0 0 1 .505-.933Z",clipRule:"evenodd"})));function tM(){return(tM=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tU=e=>eT.createElement("svg",tM({fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 50 14"},e),ea||(ea=eT.createElement("ellipse",{cx:7.889,cy:7,rx:2.187,ry:2.188,fill:"currentColor"})),ei||(ei=eT.createElement("path",{d:"M11.83 12.18a.415.415 0 0 1-.05.64A6.967 6.967 0 0 1 7.888 14a6.967 6.967 0 0 1-3.891-1.18.415.415 0 0 1-.051-.64l1.598-1.6a.473.473 0 0 1 .55-.074 3.92 3.92 0 0 0 1.794.431 3.92 3.92 0 0 0 1.792-.43.473.473 0 0 1 .551.074l1.599 1.598Z",fill:"currentColor"})),el||(el=eT.createElement("path",{opacity:.5,d:"M11.78 1.18a.415.415 0 0 1 .05.64l-1.598 1.6a.473.473 0 0 1-.55.073 3.937 3.937 0 0 0-5.3 5.3.473.473 0 0 1-.074.55L2.71 10.942a.415.415 0 0 1-.641-.051 7 7 0 0 1 9.71-9.71Z",fill:"currentColor"})),es||(es=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M23.748 1.422c0-.06.05-.11.11-.11h1.64c.06 0 .11.05.11.11v11.156a.11.11 0 0 1-.11.11h-1.64a.11.11 0 0 1-.11-.11V1.422Zm-2.315 8.9a.112.112 0 0 0-.15.004 2.88 2.88 0 0 1-.884.569c-.36.148-.747.222-1.137.219-.33.01-.658-.047-.965-.166a2.422 2.422 0 0 1-.817-.527c-.424-.432-.668-1.05-.668-1.785 0-1.473.98-2.48 2.45-2.48.394-.005.785.074 1.144.234.325.144.617.35.86.607.*************.155.01l1.108-.959a.107.107 0 0 0 .01-.152c-.832-.93-2.138-1.412-3.379-1.412-2.499 0-4.27 1.686-4.27 4.166 0 1.227.44 2.26 1.182 2.99.743.728 1.801 1.157 3.022 1.157 1.53 0 2.763-.587 3.485-1.34a.107.107 0 0 0-.009-.155l-1.137-.98Zm13.212-1.14a.108.108 0 0 1-.107.096H28.79a.106.106 0 0 0-.104.132c.286 1.06 1.138 1.701 2.302 1.701a2.59 2.59 0 0 0 1.136-.236 2.55 2.55 0 0 0 .862-.645.08.08 0 0 1 .112-.01l1.155 1.006c.**************.013.15-.698.823-1.828 1.42-3.38 1.42-2.386 0-4.185-1.651-4.185-4.162 0-1.232.424-2.264 1.13-2.994.373-.375.82-.67 1.314-.87a3.968 3.968 0 0 1 1.557-.285c2.419 0 3.983 1.701 3.983 4.05a6.737 6.737 0 0 1-.04.647Zm-5.924-1.524a.104.104 0 0 0 .103.133h3.821c.07 0 .123-.066.103-.134-.26-.901-.921-1.503-1.947-1.503a2.13 2.13 0 0 0-.88.16 2.1 2.1 0 0 0-.733.507 2.242 2.242 0 0 0-.467.837Zm11.651-3.172c.061-.001.11.048.11.109v1.837a.11.11 0 0 1-.117.109 7.17 7.17 0 0 0-.455-.024c-1.43 0-2.27 1.007-2.27 2.329v3.732a.11.11 0 0 1-.11.11h-1.64a.11.11 0 0 1-.11-.11v-7.87c0-.06.049-.109.11-.109h1.64c.06 0 .11.05.11.11v1.104a.011.011 0 0 0 .02.007c.64-.857 1.587-1.333 2.587-1.333l.125-.001Zm4.444 4.81a.035.035 0 0 1 .056.006l2.075 3.334a.11.11 0 0 0 .093.052h1.865a.11.11 0 0 0 .093-.168L46.152 7.93a.11.11 0 0 1 .012-.131l2.742-3.026a.11.11 0 0 0-.081-.183h-1.946a.11.11 0 0 0-.08.036l-3.173 3.458a.11.11 0 0 1-.19-.074V1.422a.11.11 0 0 0-.11-.11h-1.64a.11.11 0 0 0-.11.11v11.156c0 .06.05.11.11.11h1.64a.11.11 0 0 0 .11-.11v-1.755a.11.11 0 0 1 .03-.075l1.35-1.452Z",fill:"currentColor"})));function tj(){return(tj=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tV=e=>eT.createElement("svg",tj({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 14 14"},e),ec||(ec=eT.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M5.4 2.2a3.2 3.2 0 1 0 0 6.4 3.2 3.2 0 0 0 0-6.4ZM.6 5.4a4.8 4.8 0 1 1 8.7 2.8l3.9 3.8a.8.8 0 0 1-1.2 1.2L8.2 9.3A4.8 4.8 0 0 1 .6 5.4Z",clipRule:"evenodd"})));function tW(){return(tW=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tH=e=>eT.createElement("svg",tW({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),ed||(ed=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2 3.75A.75.75 0 0 1 2.75 3h10.5a.75.75 0 1 1 0 1.5H2.75A.75.75 0 0 1 2 3.75ZM2 8a.75.75 0 0 1 .75-.75h10.5a.75.75 0 1 1 0 1.5H2.75A.75.75 0 0 1 2 8Zm0 4.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 1 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z"})));function tN(){return(tN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tK=e=>eT.createElement("svg",tN({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16"},e),eu||(eu=eT.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M5.6 1.6A1.6 1.6 0 0 0 4 3.2v9.6a1.6 1.6 0 0 0 1.6 1.6h4.8a1.6 1.6 0 0 0 1.6-1.6V3.2a1.6 1.6 0 0 0-1.6-1.6H5.6ZM8 12.8a.8.8 0 1 0 0-******* 0 0 0 0 1.6Z",clipRule:"evenodd"})));function tX(){return(tX=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tY=e=>eT.createElement("svg",tX({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),ep||(ep=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.875 13.688V2.312h-.219a.656.656 0 1 1 0-1.312h10.688a.656.656 0 0 1 0 1.313h-.219v11.374h.219a.656.656 0 1 1 0 1.313h-2.942a.656.656 0 0 1-.656-.656v-2.188a.656.656 0 0 0-.656-.656H6.902a.656.656 0 0 0-.656.656v2.188A.656.656 0 0 1 5.59 15H2.656a.656.656 0 1 1 0-1.313h.219Zm2.496-9.626a.438.438 0 0 1 .438-.437h.875a.437.437 0 0 1 .437.438v.875a.437.437 0 0 1-.437.437h-.875a.437.437 0 0 1-.438-.438v-.875Zm.438 3.063a.437.437 0 0 0-.438.438v.875a.438.438 0 0 0 .438.437h.875a.437.437 0 0 0 .437-.438v-.874a.437.437 0 0 0-.437-.438h-.875ZM8.87 4.062a.438.438 0 0 1 .438-.437h.875a.438.438 0 0 1 .437.438v.875a.438.438 0 0 1-.437.437h-.875a.437.437 0 0 1-.438-.438v-.875Zm.438 3.063a.437.437 0 0 0-.438.438v.875a.438.438 0 0 0 .438.437h.875a.438.438 0 0 0 .437-.438v-.874a.438.438 0 0 0-.437-.438h-.875Z"})));function tq(){return(tq=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tG=e=>eT.createElement("svg",tq({fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 17 16"},e),em||(em=eT.createElement("path",{d:"M13.598 5.108c-.418.419-.628.628-.847.669-.22.04-.364-.104-.652-.392l-.984-.984c-.288-.288-.432-.432-.392-.652.04-.22.25-.428.669-.847l.226-.227c.419-.418.628-.628.848-.668.22-.04.364.103.652.392l.983.983c.289.288.433.432.392.652-.04.22-.25.429-.668.848l-.227.226ZM4.66 13.688l-1.206.223c-.5.092-.75.138-.876.011-.127-.126-.08-.376.011-.876l.223-1.206c.04-.217.06-.325.118-.43.058-.105.15-.196.33-.377l5.554-5.554c.419-.418.628-.628.848-.668.22-.04.364.103.652.392l.983.983c.289.288.433.432.392.652-.04.22-.25.429-.668.848l-5.554 5.553c-.181.182-.272.273-.377.331-.105.058-.213.078-.43.118ZM9.25 12.5a.75.75 0 0 0 0 1.5h4.5a.75.75 0 0 0 0-1.5h-4.5Z",fill:"currentColor"})));function tQ(){return(tQ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let tJ=e=>eT.createElement("svg",tQ({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16"},e),eh||(eh=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2 3.75C2 2.784 2.784 2 3.75 2h5.5c.698 0 1.3.409 1.582 1h1.418c.966 0 1.75.784 1.75 1.75v6.5A1.75 1.75 0 0 1 12.25 13h-1.418c-.281.591-.884 1-1.582 1h-5.5A1.75 1.75 0 0 1 2 12.25v-8.5Zm9 7.75h1.25a.25.25 0 0 0 .25-.25v-6.5a.25.25 0 0 0-.25-.25H11v7Zm-7.25-8a.25.25 0 0 0-.25.25v8.5c0 .*************.25h5.5a.25.25 0 0 0 .25-.25v-8.5a.25.25 0 0 0-.25-.25h-5.5ZM5 7.25a.75.75 0 0 1 .75-.75h1.5a.75.75 0 0 1 0 1.5h-1.5A.75.75 0 0 1 5 7.25Zm0 3a.75.75 0 0 1 .75-.75h1.5a.75.75 0 0 1 0 1.5h-1.5a.75.75 0 0 1-.75-.75Z",fill:"currentColor"})));function t0(){return(t0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let t1=e=>eT.createElement("svg",t0({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),eg||(eg=eT.createElement("path",{d:"M8.75 3.75a.75.75 0 0 0-1.5 0v3.5h-3.5a.75.75 0 1 0 0 1.5h3.5v3.5a.75.75 0 0 0 1.5 0v-3.5h3.5a.75.75 0 0 0 0-1.5h-3.5v-3.5Z"})));function t2(){return(t2=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let t5=e=>eT.createElement("svg",t2({fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 13 14"},e),ef||(ef=eT.createElement("path",{d:"M3.107 8.126c-.147.018-.294.038-.44.059m.44-.06c2.142-.27 4.31-.27 6.453 0m-6.453 0-.233 2.567M9.56 8.126c.147.018.293.038.44.059m-.44-.06.232 2.567.14 1.553a.697.697 0 0 1-.407.696.684.684 0 0 1-.277.059H3.419a.69.69 0 0 1-.684-.755l.14-1.553m0 0h-.667a1.37 1.37 0 0 1-.972-.405 1.39 1.39 0 0 1-.403-.98V5.435c0-.665.47-1.24 1.123-1.338.389-.059.778-.11 1.169-.152m6.666 6.748h.667a1.367 1.367 0 0 0 .972-.405 1.386 1.386 0 0 0 .403-.98V5.435c0-.665-.469-1.24-1.122-1.338-.389-.059-.779-.11-1.17-.152m0 0a29.456 29.456 0 0 0-6.416 0m6.417 0V1.692A.69.69 0 0 0 8.854 1H3.813a.69.69 0 0 0-.688.692v2.252M10 6.077h.005v.005H10v-.005Zm-1.833 0h.005v.005h-.005v-.005Z",stroke:"currentColor",strokeWidth:1.2,strokeLinecap:"round",strokeLinejoin:"round"})));function t4(){return(t4=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let t6=e=>eT.createElement("svg",t4({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16"},e),eb||(eb=eT.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M6 7.6v-.8a2 2 0 0 1 4 0v.8a.8.8 0 0 1 .8.8v2a.8.8 0 0 1-.8.8H6a.8.8 0 0 1-.8-.8v-2a.8.8 0 0 1 .8-.8Zm3.2-.8v.8H6.8v-.8a1.2 1.2 0 0 1 2.4 0Z",clipRule:"evenodd"})),ev||(ev=eT.createElement("path",{fill:"currentColor",fillRule:"evenodd",d:"M13.07 2.9a7.2 7.2 0 1 0 .02 10.19s.42-.47 0-.89-.88 0-.88 0a5.95 5.95 0 1 1-.02-8.42l-1.04 1.04c-.25.24-.16.44.18.44h2.5c.63 0 .63 0 .63-.63v-2.5c0-.34-.2-.43-.44-.18l-.95.94Z",clipRule:"evenodd"})));function t3(){return(t3=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let t7=e=>eT.createElement("svg",t3({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 16 16"},e),ey||(ey=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8 3.5a4.502 4.502 0 0 0-4.418 3.642.75.75 0 1 1-1.473-.284A6.002 6.002 0 0 1 8 2c1.72 0 3.204.956 4.2 ***********.205.178.3.266V2.75a.75.75 0 0 1 1.5 0v3.259a.75.75 0 0 1-.75.75H9.991a.75.75 0 1 1 0-1.5h1.577a9.62 9.62 0 0 0-.344-.31C10.32 4.173 9.18 3.5 8 3.5Zm5.392 4.764a.75.75 0 0 1 .594.878A6.002 6.002 0 0 1 8.096 14c-1.72 0-3.205-.956-4.201-1.81-.104-.09-.204-.178-.3-.266v1.326a.75.75 0 0 1-1.5 0V9.991a.75.75 0 0 1 .75-.75h3.259a.75.75 0 0 1 0 1.5H4.527c.***************.344.31.904.776 2.044 1.449 3.224 1.449a4.502 4.502 0 0 0 4.419-3.642.75.75 0 0 1 .878-.594Z",fill:"currentColor"})));function t9(){return(t9=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let t8=e=>eT.createElement("svg",t9({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),ew||(ew=eT.createElement("path",{fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd",d:"M2.6 2.604A2.045 2.045 0 0 1 4.052 2h3.417c.544 0 1.066.217 1.45.604.385.387.601.911.601 1.458v.69c0 .413-.334.75-.746.75a.748.748 0 0 1-.745-.75v-.69a.564.564 0 0 0-.56-.562H4.051a.558.558 0 0 0-.56.563v7.875a.564.564 0 0 0 .56.562h3.417a.558.558 0 0 0 .56-.563v-.671c0-.415.333-.75.745-.75s.746.335.746.75v.671c0 .548-.216 1.072-.6 1.459a2.045 2.045 0 0 1-1.45.604H4.05a2.045 2.045 0 0 1-1.45-.604A2.068 2.068 0 0 1 2 11.937V4.064c0-.548.216-1.072.6-1.459Zm8.386 3.116a.743.743 0 0 1 1.055 0l1.74 1.75a.753.753 0 0 1 0 1.06l-1.74 1.75a.743.743 0 0 1-1.055 0 .753.753 0 0 1 0-1.06l.467-.47H5.858A.748.748 0 0 1 5.112 8c0-.414.334-.75.746-.75h5.595l-.467-.47a.753.753 0 0 1 0-1.06Z"})));function re(){return(re=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let rt=e=>eT.createElement("svg",re({fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 17 16"},e),ex||(ex=eT.createElement("path",{d:"M12.25 11.66h0M8.5 13.25h0M3.25 8h0M4.75 4.33h0M4.75 11.66h0M8.5 2.75v0M13.75 8h0M12.25 4.33h0M13.75 8c0-2.9-2.35-5.25-5.25-5.25",stroke:"#747686",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"})));function rr(){return(rr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let rn=e=>eT.createElement("svg",rr({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),eS||(eS=eT.createElement("path",{d:"M9.53 4.47a.75.75 0 0 0-1.06 1.06l1.72 1.72H4a.75.75 0 0 0 0 1.5h6.19l-1.72 1.72a.75.75 0 1 0 1.06 1.06l3-3a.75.75 0 0 0 0-1.06l-3-3Z"})));function ro(){return(ro=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let ra=e=>eT.createElement("svg",ro({xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 18 20"},e),e$||(e$=eT.createElement("path",{d:"M6.6 4a1.2 1.2 0 0 0 0 2.4h6.7l-1.55 1.55a1.2 1.2 0 0 0 1.7 1.7l3.6-3.6a1.2 1.2 0 0 0 0-1.7l-3.6-3.6a1.2 1.2 0 1 0-1.7 1.7L13.3 4H6.6Zm4.8 12a1.2 1.2 0 1 0 0-2.4H4.7l1.55-1.55a1.2 1.2 0 1 0-1.7-1.7l-3.6 3.6a1.2 1.2 0 0 0 0 1.7l3.6 3.6a1.2 1.2 0 1 0 1.7-1.7L4.7 16h6.7Z"})));function ri(){return(ri=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let rl=e=>eT.createElement("svg",ri({fill:"none",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},e),eC||(eC=eT.createElement("g",{stroke:"currentColor",strokeWidth:2,strokeLinecap:"round"},eT.createElement("path",{d:"M10.01 10H10M4.01 10H4M16.01 10H16"}))));function rs(){return(rs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let rc=e=>eT.createElement("svg",rs({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),e_||(e_=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13.748 2.91a.48.48 0 0 1 .124.083c.**************.126.31v4.054a7.58 7.58 0 0 1-1.227 4.147c-.99 1.52-3.778 3.038-4.563 3.445a.45.45 0 0 1-.416 0c-.785-.407-3.576-1.925-4.565-3.445A7.61 7.61 0 0 1 2 7.357V3.303a.43.43 0 0 1 .14-.31.484.484 0 0 1 .334-.13h.027c2.162 0 4.132-.655 5.148-1.714A.485.485 0 0 1 8.004 1c.137 0 .266.054.355.149 1.016 1.056 2.99 1.714 5.148 1.714h.027c.076 0 .149.016.214.046Zm-2.695 3.097a.75.75 0 0 0-1.106-1.014c-.9.983-1.363 1.624-2.013 2.787-.218.39-.442.876-.626 1.305l-1.242-1.43a.75.75 0 0 0-1.132.982l2.042 2.354a.75.75 0 0 0 1.269-.227v-.003l.005-.011.018-.046a22.354 22.354 0 0 1 .305-.762c.199-.474.447-1.03.67-1.43.594-1.062.988-1.608 1.81-2.505Z"})));function rd(){return(rd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let ru=e=>eT.createElement("svg",rd({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},e),ek||(ek=eT.createElement("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 15v1.2c0 1.68 0 2.52-.327 3.162a3 3 0 0 1-1.311 1.311C18.72 21 17.88 21 16.2 21H7.8c-1.68 0-2.52 0-3.162-.327a3 3 0 0 1-1.311-1.311C3 18.72 3 17.88 3 16.2V15m14-7-5-5m0 0L7 8m5-5v12"})));function rp(){return(rp=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let rm=e=>eT.createElement("svg",rp({xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 16 16"},e),eP||(eP=eT.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15 8A7 7 0 1 1 1 8a7 7 0 0 1 14 0Zm-2.585 2.894c.154.25.107.568-.083.792A5.675 5.675 0 0 1 8 13.688a5.675 5.675 0 0 1-4.332-2.002c-.19-.224-.237-.543-.083-.792.087-.14.189-.271.306-.392.46-.469 1.087-.986 1.703-1.102.514-.097.899.056 1.298.214.331.132.673.267 1.108.267.435 0 .777-.135 1.108-.267.4-.158.784-.31 1.298-.214.616.116 1.243.633 1.703 ************.22.252.306.392ZM8 8.919c1.329 0 2.406-1.559 2.406-2.888a2.406 2.406 0 1 0-4.812 0C5.594 7.361 6.67 8.92 8 8.92Z"})));function rh(){return(rh=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}let rg=e=>eT.createElement("svg",rh({fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),eI||(eI=eT.createElement("path",{d:"M9.167 5.035c0 1.503-1.22 3.266-2.723 3.266S3.722 6.538 3.722 5.035a2.722 2.722 0 0 1 5.445 0ZM1 12.736c0-.797.12-1.804.796-2.643.562-.698 1.23-1.116 1.926-1.247 1.066-.2 1.638.544 2.722.544 1.085 0 1.657-.745 2.723-.544.697.13 1.364.55 1.926 1.247.676.84.796 1.846.796 2.643a.95.95 0 0 1-.95.951H1.95A.95.95 0 0 1 1 12.736Z"})),eA||(eA=eT.createElement("path",{d:"M8.982 7.712c-.088.111-.182.22-.282.324a2.698 2.698 0 0 1 1.21.232c1.336-.247 2.368-1.85 2.368-3.233a2.722 2.722 0 0 0-3.5-2.61 3.491 3.491 0 0 1 1.166 2.61c0 .945-.375 1.927-.962 2.677ZM12.382 13.688c.18-.274.285-.6.285-.952 0-.942-.152-2.21-1.019-3.187-.21-.239-.377-.371-.598-.555.37-.138.75-.238 1.228-.148.697.13 1.364.55 1.926 1.247.676.84.796 1.846.796 2.643a.95.95 0 0 1-.95.951h-1.668Z"})))},8742:function(e,t,r){r.r(t),r.d(t,{MountedCheckoutDrawer:()=>l});var n=r(9109),o=r(3799),a=r(7508),i=r(6556);function l({appearance:e,checkoutDrawer:t,onOpenChange:r}){let{user:l}=(0,o.aF)();return t.props?(0,n.tZ)(i.Ni,{globalAppearance:e,appearanceKey:"checkout",componentAppearance:t.props.appearance||{},flowName:"checkout",open:t.open,onOpenChange:r,componentName:"Checkout",portalId:t.props?.portalId,portalRoot:t.props?.portalRoot,children:t.props&&(0,n.tZ)(a.hZ,{planId:t.props.planId,planPeriod:t.props.planPeriod,subscriberType:t.props.subscriberType,onSubscriptionComplete:t.props.onSubscriptionComplete,portalRoot:t.props.portalRoot,appearance:t.props.appearance,newSubscriptionRedirectUrl:t.props.newSubscriptionRedirectUrl})},l?.id):null}},8902:function(e,t,r){r.r(t),r.d(t,{MountedPlanDetailDrawer:()=>l});var n=r(9109),o=r(3799),a=r(7508),i=r(6556);function l({appearance:e,planDetailsDrawer:t,onOpenChange:r}){let{user:l}=(0,o.aF)();return t.props?(0,n.tZ)(i.Ni,{globalAppearance:e,appearanceKey:"planDetails",componentAppearance:t.props.appearance||{},flowName:"planDetails",open:t.open,onOpenChange:r,componentName:"PlanDetails",portalId:t.props.portalId,portalRoot:t.props.portalRoot,children:(0,n.tZ)(a.KO,{...t.props,subscriberType:t.props.subscriberType||"user",onSubscriptionCancel:t.props.onSubscriptionCancel||(()=>{}),appearance:t.props.appearance})},l?.id):null}},5681:function(e,t,r){r.r(t),r.d(t,{createRoot:()=>n.createRoot}),r(1576);var n=r(9068)},1085:function(e,t,r){let n,o;r.d(t,{u1:()=>w.u,W5:()=>_,zJ:()=>k});var a=r(9109),i=r(3531),l=r(9144),s=r(1576),c=r(7623),d=r(3799),u=r(8387);let p={titleize:r(5027).MI,timeString:(e,t)=>{try{return new Intl.DateTimeFormat(t||"en-US",{timeStyle:"short"}).format((0,u.P9)(e))}catch(e){return console.warn(e),""}},weekday:(e,t,r)=>{try{return new Intl.DateTimeFormat(t||"en-US",{weekday:r||"long"}).format((0,u.P9)(e))}catch(e){return console.warn(e),""}},numeric:(e,t)=>{try{return new Intl.DateTimeFormat(t||"en-US").format((0,u.P9)(e))}catch(e){return console.warn(e),""}},link:(e,t)=>`[${t}](${e})`,shortDate:(e,t)=>{let r=(0,u.P9)(e);try{return new Intl.DateTimeFormat(t||"en-US",{month:"short",day:"numeric",...r.getFullYear()!==new Date().getFullYear()?{year:"numeric"}:{}}).format((0,u.P9)(e))}catch(e){return console.warn(e),""}},longDate:(e,t)=>{try{return new Intl.DateTimeFormat(t||"en-US",{month:"long",day:"numeric",year:"numeric"}).format((0,u.P9)(e))}catch(e){return console.warn(e),""}}},m=(e,t)=>{if(!e)return"";let{normalisedString:r,expressions:n}=g(e,t);return f(r,n,t)},h=()=>{let{applicationName:e}=(0,s.useEnvironment)().displayConfig,{client:t,user:r}=(0,d.cL)(),{signIn:n}=t||{};return{applicationName:e,"signIn.identifier":n?.identifier||"","user.username":r?.username||"","user.firstName":r?.firstName||"","user.lastName":r?.lastName||"","user.primaryEmailAddress":r?.primaryEmailAddress?.emailAddress||"","user.primaryPhoneNumber":r?.primaryPhoneNumber?.phoneNumber||""}},g=(e,t)=>{let r=(e.match(/{{.+?}}/g)||[]).map(e=>e.replace(/[{}]/g,"")).map(e=>e.split("|").map(e=>e.trim())).filter(e=>e[0]in t).map(([e,...t])=>({token:e,modifiers:t.map(e=>v(e)).filter(e=>b(e.modifierName))})),n=e;return r.forEach(({token:e})=>{n=n.replace(/{{.+?}}/,`_++${e}++_`)}),{expressions:r,normalisedString:n}},f=(e,t,r)=>(t.forEach(({token:t,modifiers:n})=>{let o=n.reduce((e,t)=>{try{return p[t.modifierName](e,...t.params)}catch(e){return console.warn(e),""}},r[t]);e=e.replace(`_++${t}++_`,o)}),e),b=e=>Object.prototype.hasOwnProperty.call(p,e),v=e=>{let t=e.split(/[(,)]/g).map(e=>e.trim()).filter(e=>!!e);if(1===t.length){let[e]=t;return{modifierName:e,params:[]}}{let[e,...r]=t;return{modifierName:e,params:r.map(e=>e.replace(/['"]+/g,""))}}},y={locale:"en-US",apiKeys:{action__add:"Add new key",action__search:"Search keys",dates:{lastUsed__days:"{{days}}d ago",lastUsed__hours:"{{hours}}h ago",lastUsed__minutes:"{{minutes}}m ago",lastUsed__months:"{{months}}mo ago",lastUsed__seconds:"{{seconds}}s ago",lastUsed__years:"{{years}}y ago"},detailsTitle__emptyRow:"No API keys found",formButtonPrimary__add:"Create key",formHint:"Provide a name to generate a new key. You’ll be able to revoke it anytime.",formTitle:"Add new API key",menuAction__revoke:"Revoke key",revokeConfirmation:{formButtonPrimary__revoke:"Revoke key",formHint:"Are you sure you want to delete this Secret key?",formTitle:'Revoke "{{apiKeyName}}" secret key?'}},backButton:"Back",badge__activePlan:"Active",badge__canceledEndsAt:"Canceled • Ends {{ date | shortDate('en-US') }}",badge__currentPlan:"Current plan",badge__default:"Default",badge__endsAt:"Ends {{ date | shortDate('en-US') }}",badge__expired:"Expired",badge__otherImpersonatorDevice:"Other impersonator device",badge__primary:"Primary",badge__renewsAt:"Renews {{ date | shortDate('en-US') }}",badge__requiresAction:"Requires action",badge__startsAt:"Starts {{ date | shortDate('en-US') }}",badge__thisDevice:"This device",badge__unverified:"Unverified",badge__upcomingPlan:"Upcoming",badge__userDevice:"User device",badge__you:"You",commerce:{addPaymentMethod:"Add payment method",alwaysFree:"Always free",annually:"Annually",availableFeatures:"Available features",billedAnnually:"Billed annually",billedMonthlyOnly:"Only billed monthly",cancelSubscription:"Cancel subscription",cancelSubscriptionAccessUntil:"You can keep using '{{plan}}' features until {{ date | longDate('en-US') }}, after which you will no longer have access.",cancelSubscriptionNoCharge:"You will not be charged for this subscription.",cancelSubscriptionTitle:"Cancel {{plan}} Subscription?",cannotSubscribeMonthly:"You cannot subscribe to this plan by paying monthly. To subscribe to this plan, you need to choose to pay annually.",checkout:{description__paymentSuccessful:"Your payment was successful.",description__subscriptionSuccessful:"Your new subscription is all set.",downgradeNotice:"You will keep your current subscription and its features until the end of the billing cycle, then you will be switched to this subscription.",pastDueNotice:"Your previous subscription was past due, with no payment.",emailForm:{subtitle:"Before you can complete your purchase you must add an email address where receipts will be sent.",title:"Add an email address"},lineItems:{title__paymentMethod:"Payment method",title__statementId:"Statement ID",title__subscriptionBegins:"Subscription begins",title__totalPaid:"Total paid"},perMonth:"per month",title:"Checkout",title__paymentSuccessful:"Payment was successful!",title__subscriptionSuccessful:"Success!"},credit:"Credit",creditRemainder:"Credit for the remainder of your current subscription.",defaultFreePlanActive:"You're currently on the Free plan",free:"Free",getStarted:"Get started",keepSubscription:"Keep subscription",manage:"Manage",manageSubscription:"Manage subscription",month:"Month",monthly:"Monthly",pastDue:"Past due",pay:"Pay {{amount}}",paymentMethods:"Payment Methods",paymentSource:{applePayDescription:{annual:"Annual payment",monthly:"Monthly payment"},dev:{anyNumbers:"Any numbers",cardNumber:"Card number",cvcZip:"CVC, ZIP",developmentMode:"Development mode",expirationDate:"Expiration date",testCardInfo:"Test card information"}},popular:"Popular",pricingTable:{billingCycle:"Billing cycle",included:"Included"},reSubscribe:"Resubscribe",seeAllFeatures:"See all features",subscribe:"Subscribe",subtotal:"Subtotal",switchPlan:"Switch to this plan",switchToAnnual:"Switch to annual",switchToMonthly:"Switch to monthly",totalDue:"Total due",totalDueToday:"Total Due Today",viewFeatures:"View features",year:"Year"},createOrganization:{formButtonSubmit:"Create organization",invitePage:{formButtonReset:"Skip"},title:"Create organization"},dates:{lastDay:"Yesterday at {{ date | timeString('en-US') }}",next6Days:"{{ date | weekday('en-US','long') }} at {{ date | timeString('en-US') }}",nextDay:"Tomorrow at {{ date | timeString('en-US') }}",numeric:"{{ date | numeric('en-US') }}",previous6Days:"Last {{ date | weekday('en-US','long') }} at {{ date | timeString('en-US') }}",sameDay:"Today at {{ date | timeString('en-US') }}"},dividerText:"or",footerActionLink__alternativePhoneCodeProvider:"Send code via SMS instead",footerActionLink__useAnotherMethod:"Use another method",footerPageLink__help:"Help",footerPageLink__privacy:"Privacy",footerPageLink__terms:"Terms",formButtonPrimary:"Continue",formButtonPrimary__verify:"Verify",formFieldAction__forgotPassword:"Forgot password?",formFieldError__matchingPasswords:"Passwords match.",formFieldError__notMatchingPasswords:"Passwords don't match.",formFieldError__verificationLinkExpired:"The verification link expired. Please request a new link.",formFieldHintText__optional:"Optional",formFieldHintText__slug:"A slug is a human-readable ID that must be unique. It’s often used in URLs.",formFieldInputPlaceholder__apiKeyDescription:"Enter your secret key description",formFieldInputPlaceholder__apiKeyExpirationDate:"Enter expiration date",formFieldInputPlaceholder__apiKeyName:"Enter your secret key name",formFieldInputPlaceholder__backupCode:"Enter backup code",formFieldInputPlaceholder__confirmDeletionUserAccount:"Delete account",formFieldInputPlaceholder__emailAddress:"Enter your email address",formFieldInputPlaceholder__emailAddress_username:"Enter email or username",formFieldInputPlaceholder__emailAddresses:"<EMAIL>, <EMAIL>",formFieldInputPlaceholder__firstName:"First name",formFieldInputPlaceholder__lastName:"Last name",formFieldInputPlaceholder__organizationDomain:"example.com",formFieldInputPlaceholder__organizationDomainEmailAddress:"<EMAIL>",formFieldInputPlaceholder__organizationName:"Organization name",formFieldInputPlaceholder__organizationSlug:"my-org",formFieldInputPlaceholder__password:"Enter your password",formFieldInputPlaceholder__phoneNumber:"Enter your phone number",formFieldInputPlaceholder__username:void 0,formFieldLabel__apiKeyDescription:"Description",formFieldLabel__apiKeyExpiration:"Expiration",formFieldLabel__apiKeyExpirationDate:"Select date",formFieldLabel__apiKeyName:"Name",formFieldLabel__automaticInvitations:"Enable automatic invitations for this domain",formFieldLabel__backupCode:"Backup code",formFieldLabel__confirmDeletion:"Confirmation",formFieldLabel__confirmPassword:"Confirm password",formFieldLabel__currentPassword:"Current password",formFieldLabel__emailAddress:"Email address",formFieldLabel__emailAddress_username:"Email address or username",formFieldLabel__emailAddresses:"Email addresses",formFieldLabel__firstName:"First name",formFieldLabel__lastName:"Last name",formFieldLabel__newPassword:"New password",formFieldLabel__organizationDomain:"Domain",formFieldLabel__organizationDomainDeletePending:"Delete pending invitations and suggestions",formFieldLabel__organizationDomainEmailAddress:"Verification email address",formFieldLabel__organizationDomainEmailAddressDescription:"Enter an email address under this domain to receive a code and verify this domain.",formFieldLabel__organizationName:"Name",formFieldLabel__organizationSlug:"Slug",formFieldLabel__passkeyName:"Name of passkey",formFieldLabel__password:"Password",formFieldLabel__phoneNumber:"Phone number",formFieldLabel__role:"Role",formFieldLabel__signOutOfOtherSessions:"Sign out of all other devices",formFieldLabel__username:"Username",impersonationFab:{action__signOut:"Sign out",title:"Signed in as {{identifier}}"},maintenanceMode:"We are currently undergoing maintenance, but don't worry, it shouldn't take more than a few minutes.",membershipRole__admin:"Admin",membershipRole__basicMember:"Member",membershipRole__guestMember:"Guest",organizationList:{action__createOrganization:"Create organization",action__invitationAccept:"Join",action__suggestionsAccept:"Request to join",createOrganization:"Create Organization",invitationAcceptedLabel:"Joined",subtitle:"to continue to {{applicationName}}",suggestionsAcceptedLabel:"Pending approval",title:"Choose an account",titleWithoutPersonal:"Choose an organization"},organizationProfile:{apiKeysPage:{title:"API Keys"},badge__automaticInvitation:"Automatic invitations",badge__automaticSuggestion:"Automatic suggestions",badge__manualInvitation:"No automatic enrollment",badge__unverified:"Unverified",billingPage:{paymentHistorySection:{empty:"No payment history",tableHeader__date:"Date",tableHeader__amount:"Amount",tableHeader__status:"Status",notFound:"Payment attempt not found"},paymentSourcesSection:{actionLabel__default:"Make default",actionLabel__remove:"Remove",add:"Add new payment method",addSubtitle:"Add a new payment method to your account.",cancelButton:"Cancel",formButtonPrimary__add:"Add Payment Method",formButtonPrimary__pay:"Pay {{amount}}",payWithTestCardButton:"Pay with test card",removeResource:{messageLine1:"{{identifier}} will be removed from this account.",messageLine2:"You will no longer be able to use this payment source and any recurring subscriptions dependent on it will no longer work.",successMessage:"{{paymentSource}} has been removed from your account.",title:"Remove payment method"},title:"Payment methods"},start:{headerTitle__payments:"Payments",headerTitle__plans:"Plans",headerTitle__statements:"Statements",headerTitle__subscriptions:"Subscription"},statementsSection:{empty:"No statements to display",itemCaption__paidForPlan:"Paid for {{plan}} {{period}} plan",itemCaption__proratedCredit:"Prorated credit for partial usage of previous subscription",itemCaption__subscribedAndPaidForPlan:"Subscribed and paid for {{plan}} {{period}} plan",notFound:"Statement not found",tableHeader__date:"Date",tableHeader__amount:"Amount",title:"Statements",totalPaid:"Total paid"},subscriptionsListSection:{actionLabel__newSubscription:"Subscribe to a plan",actionLabel__switchPlan:"Switch plans",tableHeader__plan:"Plan",tableHeader__startDate:"Start date",tableHeader__edit:"Edit",title:"Subscription"},subscriptionsSection:{actionLabel__default:"Manage"},switchPlansSection:{title:"Switch plans"},title:"Billing"},createDomainPage:{subtitle:"Add the domain to verify. Users with email addresses at this domain can join the organization automatically or request to join.",title:"Add domain"},invitePage:{detailsTitle__inviteFailed:"The invitations could not be sent. There are already pending invitations for the following email addresses: {{email_addresses}}.",formButtonPrimary__continue:"Send invitations",selectDropdown__role:"Select role",subtitle:"Enter or paste one or more email addresses, separated by spaces or commas.",successMessage:"Invitations successfully sent",title:"Invite new members"},membersPage:{action__invite:"Invite",action__search:"Search",activeMembersTab:{menuAction__remove:"Remove member",tableHeader__actions:"Actions",tableHeader__joined:"Joined",tableHeader__role:"Role",tableHeader__user:"User"},detailsTitle__emptyRow:"No members to display",invitationsTab:{autoInvitations:{headerSubtitle:"Invite users by connecting an email domain with your organization. Anyone who signs up with a matching email domain will be able to join the organization anytime.",headerTitle:"Automatic invitations",primaryButton:"Manage verified domains"},table__emptyRow:"No invitations to display"},invitedMembersTab:{menuAction__revoke:"Revoke invitation",tableHeader__invited:"Invited"},requestsTab:{autoSuggestions:{headerSubtitle:"Users who sign up with a matching email domain, will be able to see a suggestion to request to join your organization.",headerTitle:"Automatic suggestions",primaryButton:"Manage verified domains"},menuAction__approve:"Approve",menuAction__reject:"Reject",tableHeader__requested:"Requested access",table__emptyRow:"No requests to display"},start:{headerTitle__invitations:"Invitations",headerTitle__members:"Members",headerTitle__requests:"Requests"}},navbar:{apiKeys:"API Keys",billing:"Billing",description:"Manage your organization.",general:"General",members:"Members",title:"Organization"},plansPage:{alerts:{noPermissionsToManageBilling:"You do not have permissions to manage billing for this organization."},title:"Plans"},profilePage:{dangerSection:{deleteOrganization:{actionDescription:'Type "{{organizationName}}" below to continue.',messageLine1:"Are you sure you want to delete this organization?",messageLine2:"This action is permanent and irreversible.",successMessage:"You have deleted the organization.",title:"Delete organization"},leaveOrganization:{actionDescription:'Type "{{organizationName}}" below to continue.',messageLine1:"Are you sure you want to leave this organization? You will lose access to this organization and its applications.",messageLine2:"This action is permanent and irreversible.",successMessage:"You have left the organization.",title:"Leave organization"},title:"Danger"},domainSection:{menuAction__manage:"Manage",menuAction__remove:"Delete",menuAction__verify:"Verify",primaryButton:"Add domain",subtitle:"Allow users to join the organization automatically or request to join based on a verified email domain.",title:"Verified domains"},successMessage:"The organization has been updated.",title:"Update profile"},removeDomainPage:{messageLine1:"The email domain {{domain}} will be removed.",messageLine2:"Users won’t be able to join the organization automatically after this.",successMessage:"{{domain}} has been removed.",title:"Remove domain"},start:{headerTitle__general:"General",headerTitle__members:"Members",profileSection:{primaryButton:"Update profile",title:"Organization Profile",uploadAction__title:"Logo"}},verifiedDomainPage:{dangerTab:{calloutInfoLabel:"Removing this domain will affect invited users.",removeDomainActionLabel__remove:"Remove domain",removeDomainSubtitle:"Remove this domain from your verified domains",removeDomainTitle:"Remove domain"},enrollmentTab:{automaticInvitationOption__description:"Users are automatically invited to join the organization when they sign-up and can join anytime.",automaticInvitationOption__label:"Automatic invitations",automaticSuggestionOption__description:"Users receive a suggestion to request to join, but must be approved by an admin before they are able to join the organization.",automaticSuggestionOption__label:"Automatic suggestions",calloutInfoLabel:"Changing the enrollment mode will only affect new users.",calloutInvitationCountLabel:"Pending invitations sent to users: {{count}}",calloutSuggestionCountLabel:"Pending suggestions sent to users: {{count}}",manualInvitationOption__description:"Users can only be invited manually to the organization.",manualInvitationOption__label:"No automatic enrollment",subtitle:"Choose how users from this domain can join the organization."},start:{headerTitle__danger:"Danger",headerTitle__enrollment:"Enrollment options"},subtitle:"The domain {{domain}} is now verified. Continue by selecting enrollment mode.",title:"Update {{domain}}"},verifyDomainPage:{formSubtitle:"Enter the verification code sent to your email address",formTitle:"Verification code",resendButton:"Didn't receive a code? Resend",subtitle:"The domain {{domainName}} needs to be verified via email.",subtitleVerificationCodeScreen:"A verification code was sent to {{emailAddress}}. Enter the code to continue.",title:"Verify domain"}},organizationSwitcher:{action__createOrganization:"Create organization",action__invitationAccept:"Join",action__manageOrganization:"Manage",action__suggestionsAccept:"Request to join",notSelected:"No organization selected",personalWorkspace:"Personal account",suggestionsAcceptedLabel:"Pending approval"},paginationButton__next:"Next",paginationButton__previous:"Previous",paginationRowText__displaying:"Displaying",paginationRowText__of:"of",reverification:{alternativeMethods:{actionLink:"Get help",actionText:"Don’t have any of these?",blockButton__backupCode:"Use a backup code",blockButton__emailCode:"Email code to {{identifier}}",blockButton__passkey:"Use your passkey",blockButton__password:"Continue with your password",blockButton__phoneCode:"Send SMS code to {{identifier}}",blockButton__totp:"Use your authenticator app",getHelp:{blockButton__emailSupport:"Email support",content:"If you have trouble verifying your account, email us and we will work with you to restore access as soon as possible.",title:"Get help"},subtitle:"Facing issues? You can use any of these methods for verification.",title:"Use another method"},backupCodeMfa:{subtitle:"Enter the backup code you received when setting up two-step authentication",title:"Enter a backup code"},emailCode:{formTitle:"Verification code",resendButton:"Didn't receive a code? Resend",subtitle:"Enter the code sent to your email to continue",title:"Verification required"},noAvailableMethods:{message:"Cannot proceed with verification. No suitable authentication factor is configured",subtitle:"An error occurred",title:"Cannot verify your account"},passkey:{blockButton__passkey:"Use your passkey",subtitle:"Using your passkey confirms your identity. Your device may ask for your fingerprint, face, or screen lock.",title:"Use your passkey"},password:{actionLink:"Use another method",subtitle:"Enter your current password to continue",title:"Verification required"},phoneCode:{formTitle:"Verification code",resendButton:"Didn't receive a code? Resend",subtitle:"Enter the code sent to your phone to continue",title:"Verification required"},phoneCodeMfa:{formTitle:"Verification code",resendButton:"Didn't receive a code? Resend",subtitle:"Enter the code sent to your phone to continue",title:"Verification required"},totpMfa:{formTitle:"Verification code",subtitle:"Enter the code generated by your authenticator app to continue",title:"Verification required"}},signIn:{accountSwitcher:{action__addAccount:"Add account",action__signOutAll:"Sign out of all accounts",subtitle:"Select the account with which you wish to continue.",title:"Choose an account"},alternativeMethods:{actionLink:"Get help",actionText:"Don’t have any of these?",blockButton__backupCode:"Use a backup code",blockButton__emailCode:"Email code to {{identifier}}",blockButton__emailLink:"Email link to {{identifier}}",blockButton__passkey:"Sign in with your passkey",blockButton__password:"Sign in with your password",blockButton__phoneCode:"Send SMS code to {{identifier}}",blockButton__totp:"Use your authenticator app",getHelp:{blockButton__emailSupport:"Email support",content:"If you have trouble signing into your account, email us and we will work with you to restore access as soon as possible.",title:"Get help"},subtitle:"Facing issues? You can use any of these methods to sign in.",title:"Use another method"},alternativePhoneCodeProvider:{formTitle:"Verification code",resendButton:"Didn't receive a code? Resend",subtitle:"to continue to {{applicationName}}",title:"Check your {{provider}}"},backupCodeMfa:{subtitle:"Your backup code is the one you got when setting up two-step authentication.",title:"Enter a backup code"},emailCode:{formTitle:"Verification code",resendButton:"Didn't receive a code? Resend",subtitle:"to continue to {{applicationName}}",title:"Check your email"},emailLink:{clientMismatch:{subtitle:"To continue, open the verification link on the device and browser from which you initiated the sign-in",title:"Verification link is invalid for this device"},expired:{subtitle:"Return to the original tab to continue.",title:"This verification link has expired"},failed:{subtitle:"Return to the original tab to continue.",title:"This verification link is invalid"},formSubtitle:"Use the verification link sent to your email",formTitle:"Verification link",loading:{subtitle:"You will be redirected soon",title:"Signing in..."},resendButton:"Didn't receive a link? Resend",subtitle:"to continue to {{applicationName}}",title:"Check your email",unusedTab:{title:"You may close this tab"},verified:{subtitle:"You will be redirected soon",title:"Successfully signed in"},verifiedSwitchTab:{subtitle:"Return to original tab to continue",subtitleNewTab:"Return to the newly opened tab to continue",titleNewTab:"Signed in on other tab"}},forgotPassword:{formTitle:"Reset password code",resendButton:"Didn't receive a code? Resend",subtitle:"to reset your password",subtitle_email:"First, enter the code sent to your email address",subtitle_phone:"First, enter the code sent to your phone",title:"Reset password"},forgotPasswordAlternativeMethods:{blockButton__resetPassword:"Reset your password",label__alternativeMethods:"Or, sign in with another method",title:"Forgot Password?"},noAvailableMethods:{message:"Cannot proceed with sign in. There's no available authentication factor.",subtitle:"An error occurred",title:"Cannot sign in"},passkey:{subtitle:"Using your passkey confirms it's you. Your device may ask for your fingerprint, face or screen lock.",title:"Use your passkey"},password:{actionLink:"Use another method",subtitle:"Enter the password associated with your account",title:"Enter your password"},passwordPwned:{title:"Password compromised"},phoneCode:{formTitle:"Verification code",resendButton:"Didn't receive a code? Resend",subtitle:"to continue to {{applicationName}}",title:"Check your phone"},phoneCodeMfa:{formTitle:"Verification code",resendButton:"Didn't receive a code? Resend",subtitle:"To continue, please enter the verification code sent to your phone",title:"Check your phone"},resetPassword:{formButtonPrimary:"Reset Password",requiredMessage:"For security reasons, it is required to reset your password.",successMessage:"Your password was successfully changed. Signing you in, please wait a moment.",title:"Set new password"},resetPasswordMfa:{detailsLabel:"We need to verify your identity before resetting your password."},start:{actionLink:"Sign up",actionLink__join_waitlist:"Join waitlist",actionLink__use_email:"Use email",actionLink__use_email_username:"Use email or username",actionLink__use_passkey:"Use passkey instead",actionLink__use_phone:"Use phone",actionLink__use_username:"Use username",actionText:"Don’t have an account?",actionText__join_waitlist:"Want early access?",alternativePhoneCodeProvider:{actionLink:"Use another method",label:"{{provider}} phone number",subtitle:"Enter your phone number to get a verification code on {{provider}}.",title:"Sign in to {{applicationName}} with {{provider}}"},subtitle:"Welcome back! Please sign in to continue",subtitleCombined:void 0,title:"Sign in to {{applicationName}}",titleCombined:"Continue to {{applicationName}}"},totpMfa:{formTitle:"Verification code",subtitle:"To continue, please enter the verification code generated by your authenticator app",title:"Two-step verification"}},signInEnterPasswordTitle:"Enter your password",signUp:{alternativePhoneCodeProvider:{resendButton:"Didn't receive a code? Resend",subtitle:"Enter the verification code sent to your {{provider}}",title:"Verify your {{provider}}"},continue:{actionLink:"Sign in",actionText:"Already have an account?",subtitle:"Please fill in the remaining details to continue.",title:"Fill in missing fields"},emailCode:{formSubtitle:"Enter the verification code sent to your email address",formTitle:"Verification code",resendButton:"Didn't receive a code? Resend",subtitle:"Enter the verification code sent to your email",title:"Verify your email"},emailLink:{clientMismatch:{subtitle:"To continue, open the verification link on the device and browser from which you initiated the sign-up",title:"Verification link is invalid for this device"},formSubtitle:"Use the verification link sent to your email address",formTitle:"Verification link",loading:{title:"Signing up..."},resendButton:"Didn't receive a link? Resend",subtitle:"to continue to {{applicationName}}",title:"Verify your email",verified:{title:"Successfully signed up"},verifiedSwitchTab:{subtitle:"Return to the newly opened tab to continue",subtitleNewTab:"Return to previous tab to continue",title:"Successfully verified email"}},legalConsent:{checkbox:{label__onlyPrivacyPolicy:'I agree to the {{ privacyPolicyLink || link("Privacy Policy") }}',label__onlyTermsOfService:'I agree to the {{ termsOfServiceLink || link("Terms of Service") }}',label__termsOfServiceAndPrivacyPolicy:'I agree to the {{ termsOfServiceLink || link("Terms of Service") }} and {{ privacyPolicyLink || link("Privacy Policy") }}'},continue:{subtitle:"Please read and accept the terms to continue",title:"Legal consent"}},phoneCode:{formSubtitle:"Enter the verification code sent to your phone number",formTitle:"Verification code",resendButton:"Didn't receive a code? Resend",subtitle:"Enter the verification code sent to your phone",title:"Verify your phone"},restrictedAccess:{actionLink:"Sign in",actionText:"Already have an account?",blockButton__emailSupport:"Email support",blockButton__joinWaitlist:"Join waitlist",subtitle:"Sign ups are currently disabled. If you believe you should have access, please contact support.",subtitleWaitlist:"Sign ups are currently disabled. To be the first to know when we launch, join the waitlist.",title:"Access restricted"},start:{actionLink:"Sign in",actionLink__use_email:"Use email instead",actionLink__use_phone:"Use phone instead",actionText:"Already have an account?",alternativePhoneCodeProvider:{actionLink:"Use another method",label:"{{provider}} phone number",subtitle:"Enter your phone number to get a verification code on {{provider}}.",title:"Sign up to {{applicationName}} with {{provider}}"},subtitle:"Welcome! Please fill in the details to get started.",subtitleCombined:"Welcome! Please fill in the details to get started.",title:"Create your account",titleCombined:"Create your account"}},socialButtonsBlockButton:"Continue with {{provider|titleize}}",socialButtonsBlockButtonManyInView:"{{provider|titleize}}",unstable__errors:{already_a_member_in_organization:"{{email}} is already a member of the organization.",captcha_invalid:void 0,captcha_unavailable:"Sign up unsuccessful due to failed bot validation. Please refresh the page to try again or reach out to support for more assistance.",form_code_incorrect:void 0,form_identifier_exists__email_address:void 0,form_identifier_exists__phone_number:void 0,form_identifier_exists__username:void 0,form_identifier_not_found:void 0,form_param_format_invalid:void 0,form_param_format_invalid__email_address:void 0,form_param_format_invalid__phone_number:void 0,form_param_max_length_exceeded__first_name:void 0,form_param_max_length_exceeded__last_name:void 0,form_param_max_length_exceeded__name:void 0,form_param_nil:void 0,form_param_value_invalid:void 0,form_password_incorrect:void 0,form_password_length_too_short:"Your password is too short. It must be at least 8 characters long.",form_password_not_strong_enough:"Your password is not strong enough.",form_password_pwned:"This password has been found as part of a breach and can not be used, please try another password instead.",form_password_pwned__sign_in:"This password has been found as part of a breach and can not be used, please reset your password.",form_password_size_in_bytes_exceeded:void 0,form_password_validation_failed:void 0,form_username_invalid_character:void 0,form_username_invalid_length:"Your username must be between {{min_length}} and {{max_length}} characters long.",identification_deletion_failed:void 0,not_allowed_access:void 0,organization_domain_blocked:void 0,organization_domain_common:void 0,organization_domain_exists_for_enterprise_connection:void 0,organization_membership_quota_exceeded:void 0,organization_minimum_permissions_needed:void 0,passkey_already_exists:"A passkey is already registered with this device.",passkey_not_supported:"Passkeys are not supported on this device.",passkey_pa_not_supported:"Registration requires a platform authenticator but the device does not support it.",passkey_registration_cancelled:"Passkey registration was cancelled or timed out.",passkey_retrieval_cancelled:"Passkey verification was cancelled or timed out.",passwordComplexity:{maximumLength:"less than {{length}} characters",minimumLength:"{{length}} or more characters",requireLowercase:"a lowercase letter",requireNumbers:"a number",requireSpecialCharacter:"a special character",requireUppercase:"an uppercase letter",sentencePrefix:"Your password must contain"},phone_number_exists:void 0,session_exists:void 0,web3_missing_identifier:"A Web3 Wallet extension cannot be found. Please install one to continue.",zxcvbn:{couldBeStronger:"Your password works, but could be stronger. Try adding more characters.",goodPassword:"Your password meets all the necessary requirements.",notEnough:"Your password is not strong enough.",suggestions:{allUppercase:"Capitalize some, but not all letters.",anotherWord:"Add more words that are less common.",associatedYears:"Avoid years that are associated with you.",capitalization:"Capitalize more than the first letter.",dates:"Avoid dates and years that are associated with you.",l33t:"Avoid predictable letter substitutions like '@' for 'a'.",longerKeyboardPattern:"Use longer keyboard patterns and change typing direction multiple times.",noNeed:"You can create strong passwords without using symbols, numbers, or uppercase letters.",pwned:"If you use this password elsewhere, you should change it.",recentYears:"Avoid recent years.",repeated:"Avoid repeated words and characters.",reverseWords:"Avoid reversed spellings of common words.",sequences:"Avoid common character sequences.",useWords:"Use multiple words, but avoid common phrases."},warnings:{common:"This is a commonly used password.",commonNames:"Common names and surnames are easy to guess.",dates:"Dates are easy to guess.",extendedRepeat:'Repeated character patterns like "abcabcabc" are easy to guess.',keyPattern:"Short keyboard patterns are easy to guess.",namesByThemselves:"Single names or surnames are easy to guess.",pwned:"Your password was exposed by a data breach on the Internet.",recentYears:"Recent years are easy to guess.",sequences:'Common character sequences like "abc" are easy to guess.',similarToCommon:"This is similar to a commonly used password.",simpleRepeat:'Repeated characters like "aaa" are easy to guess.',straightRow:"Straight rows of keys on your keyboard are easy to guess.",topHundred:"This is a frequently used password.",topTen:"This is a heavily used password.",userInputs:"There should not be any personal or page related data.",wordByItself:"Single words are easy to guess."}}},userButton:{action__addAccount:"Add account",action__manageAccount:"Manage account",action__signOut:"Sign out",action__signOutAll:"Sign out of all accounts"},userProfile:{apiKeysPage:{title:"API Keys"},backupCodePage:{actionLabel__copied:"Copied!",actionLabel__copy:"Copy all",actionLabel__download:"Download .txt",actionLabel__print:"Print",infoText1:"Backup codes will be enabled for this account.",infoText2:"Keep the backup codes secret and store them securely. You may regenerate backup codes if you suspect they have been compromised.",subtitle__codelist:"Store them securely and keep them secret.",successMessage:"Backup codes are now enabled. You can use one of these to sign in to your account, if you lose access to your authentication device. Each code can only be used once.",successSubtitle:"You can use one of these to sign in to your account, if you lose access to your authentication device.",title:"Add backup code verification",title__codelist:"Backup codes"},billingPage:{paymentHistorySection:{empty:"No payment history",tableHeader__date:"Date",tableHeader__amount:"Amount",tableHeader__status:"Status",notFound:"Payment attempt not found"},paymentSourcesSection:{actionLabel__default:"Make default",actionLabel__remove:"Remove",add:"Add new payment method",addSubtitle:"Add a new payment method to your account.",cancelButton:"Cancel",formButtonPrimary__add:"Add Payment Method",formButtonPrimary__pay:"Pay {{amount}}",payWithTestCardButton:"Pay with test card",removeResource:{messageLine1:"{{identifier}} will be removed from this account.",messageLine2:"You will no longer be able to use this payment source and any recurring subscriptions dependent on it will no longer work.",successMessage:"{{paymentSource}} has been removed from your account.",title:"Remove payment method"},title:"Payment methods"},start:{headerTitle__payments:"Payments",headerTitle__plans:"Plans",headerTitle__statements:"Statements",headerTitle__subscriptions:"Subscription"},statementsSection:{empty:"No statements to display",itemCaption__paidForPlan:"Paid for {{plan}} {{period}} plan",itemCaption__proratedCredit:"Prorated credit for partial usage of previous subscription",itemCaption__subscribedAndPaidForPlan:"Subscribed and paid for {{plan}} {{period}} plan",notFound:"Statement not found",tableHeader__date:"Date",tableHeader__amount:"Amount",title:"Statements",totalPaid:"Total paid"},subscriptionsListSection:{actionLabel__newSubscription:"Subscribe to a plan",actionLabel__switchPlan:"Switch plans",tableHeader__plan:"Plan",tableHeader__startDate:"Start date",tableHeader__edit:"Edit",title:"Subscription"},subscriptionsSection:{actionLabel__default:"Manage"},switchPlansSection:{title:"Switch plans"},title:"Billing"},connectedAccountPage:{formHint:"Select a provider to connect your account.",formHint__noAccounts:"There are no available external account providers.",removeResource:{messageLine1:"{{identifier}} will be removed from this account.",messageLine2:"You will no longer be able to use this connected account and any dependent features will no longer work.",successMessage:"{{connectedAccount}} has been removed from your account.",title:"Remove connected account"},socialButtonsBlockButton:"{{provider|titleize}}",successMessage:"The provider has been added to your account",title:"Add connected account"},deletePage:{actionDescription:'Type "Delete account" below to continue.',confirm:"Delete account",messageLine1:"Are you sure you want to delete your account?",messageLine2:"This action is permanent and irreversible.",title:"Delete account"},emailAddressPage:{emailCode:{formHint:"An email containing a verification code will be sent to this email address.",formSubtitle:"Enter the verification code sent to {{identifier}}",formTitle:"Verification code",resendButton:"Didn't receive a code? Resend",successMessage:"The email {{identifier}} has been added to your account."},emailLink:{formHint:"An email containing a verification link will be sent to this email address.",formSubtitle:"Click on the verification link in the email sent to {{identifier}}",formTitle:"Verification link",resendButton:"Didn't receive a link? Resend",successMessage:"The email {{identifier}} has been added to your account."},enterpriseSSOLink:{formButton:"Click to sign-in",formSubtitle:"Complete the sign-in with {{identifier}}"},formHint:"You'll need to verify this email address before it can be added to your account.",removeResource:{messageLine1:"{{identifier}} will be removed from this account.",messageLine2:"You will no longer be able to sign in using this email address.",successMessage:"{{emailAddress}} has been removed from your account.",title:"Remove email address"},title:"Add email address",verifyTitle:"Verify email address"},formButtonPrimary__add:"Add",formButtonPrimary__continue:"Continue",formButtonPrimary__finish:"Finish",formButtonPrimary__remove:"Remove",formButtonPrimary__save:"Save",formButtonReset:"Cancel",mfaPage:{formHint:"Select a method to add.",title:"Add two-step verification"},mfaPhoneCodePage:{backButton:"Use existing number",primaryButton__addPhoneNumber:"Add phone number",removeResource:{messageLine1:"{{identifier}} will be no longer receiving verification codes when signing in.",messageLine2:"Your account may not be as secure. Are you sure you want to continue?",successMessage:"SMS code two-step verification has been removed for {{mfaPhoneCode}}",title:"Remove two-step verification"},subtitle__availablePhoneNumbers:"Select an existing phone number to register for SMS code two-step verification or add a new one.",subtitle__unavailablePhoneNumbers:"There are no available phone numbers to register for SMS code two-step verification, please add a new one.",successMessage1:"When signing in, you will need to enter a verification code sent to this phone number as an additional step.",successMessage2:"Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.",successTitle:"SMS code verification enabled",title:"Add SMS code verification"},mfaTOTPPage:{authenticatorApp:{buttonAbleToScan__nonPrimary:"Scan QR code instead",buttonUnableToScan__nonPrimary:"Can’t scan QR code?",infoText__ableToScan:"Set up a new sign-in method in your authenticator app and scan the following QR code to link it to your account.",infoText__unableToScan:"Set up a new sign-in method in your authenticator and enter the Key provided below.",inputLabel__unableToScan1:"Make sure Time-based or One-time passwords is enabled, then finish linking your account.",inputLabel__unableToScan2:"Alternatively, if your authenticator supports TOTP URIs, you can also copy the full URI."},removeResource:{messageLine1:"Verification codes from this authenticator will no longer be required when signing in.",messageLine2:"Your account may not be as secure. Are you sure you want to continue?",successMessage:"Two-step verification via authenticator application has been removed.",title:"Remove two-step verification"},successMessage:"Two-step verification is now enabled. When signing in, you will need to enter a verification code from this authenticator as an additional step.",title:"Add authenticator application",verifySubtitle:"Enter verification code generated by your authenticator",verifyTitle:"Verification code"},mobileButton__menu:"Menu",navbar:{account:"Profile",apiKeys:"API keys",billing:"Billing",description:"Manage your account info.",security:"Security",title:"Account"},passkeyScreen:{removeResource:{messageLine1:"{{name}} will be removed from this account.",title:"Remove passkey"},subtitle__rename:"You can change the passkey name to make it easier to find.",title__rename:"Rename Passkey"},passwordPage:{checkboxInfoText__signOutOfOtherSessions:"It is recommended to sign out of all other devices which may have used your old password.",readonly:"Your password can currently not be edited because you can sign in only via the enterprise connection.",successMessage__set:"Your password has been set.",successMessage__signOutOfOtherSessions:"All other devices have been signed out.",successMessage__update:"Your password has been updated.",title__set:"Set password",title__update:"Update password"},phoneNumberPage:{infoText:"A text message containing a verification code will be sent to this phone number. Message and data rates may apply.",removeResource:{messageLine1:"{{identifier}} will be removed from this account.",messageLine2:"You will no longer be able to sign in using this phone number.",successMessage:"{{phoneNumber}} has been removed from your account.",title:"Remove phone number"},successMessage:"{{identifier}} has been added to your account.",title:"Add phone number",verifySubtitle:"Enter the verification code sent to {{identifier}}",verifyTitle:"Verify phone number"},plansPage:{title:"Plans"},profilePage:{fileDropAreaHint:"Recommended size 1:1, up to 10MB.",imageFormDestructiveActionSubtitle:"Remove",imageFormSubtitle:"Upload",imageFormTitle:"Profile image",readonly:"Your profile information has been provided by the enterprise connection and cannot be edited.",successMessage:"Your profile has been updated.",title:"Update profile"},start:{activeDevicesSection:{destructiveAction:"Sign out of device",title:"Active devices"},connectedAccountsSection:{actionLabel__connectionFailed:"Reconnect",actionLabel__reauthorize:"Authorize now",destructiveActionTitle:"Remove",primaryButton:"Connect account",subtitle__disconnected:"This account has been disconnected.",subtitle__reauthorize:"The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues",title:"Connected accounts"},dangerSection:{deleteAccountButton:"Delete account",title:"Delete account"},emailAddressesSection:{destructiveAction:"Remove email",detailsAction__nonPrimary:"Set as primary",detailsAction__primary:"Complete verification",detailsAction__unverified:"Verify",primaryButton:"Add email address",title:"Email addresses"},enterpriseAccountsSection:{title:"Enterprise accounts"},headerTitle__account:"Profile details",headerTitle__security:"Security",mfaSection:{backupCodes:{actionLabel__regenerate:"Regenerate",headerTitle:"Backup codes",subtitle__regenerate:"Get a fresh set of secure backup codes. Prior backup codes will be deleted and cannot be used.",title__regenerate:"Regenerate backup codes"},phoneCode:{actionLabel__setDefault:"Set as default",destructiveActionLabel:"Remove"},primaryButton:"Add two-step verification",title:"Two-step verification",totp:{destructiveActionTitle:"Remove",headerTitle:"Authenticator application"}},passkeysSection:{menuAction__destructive:"Remove",menuAction__rename:"Rename",primaryButton:"Add a passkey",title:"Passkeys"},passwordSection:{primaryButton__setPassword:"Set password",primaryButton__updatePassword:"Update password",title:"Password"},phoneNumbersSection:{destructiveAction:"Remove phone number",detailsAction__nonPrimary:"Set as primary",detailsAction__primary:"Complete verification",detailsAction__unverified:"Verify phone number",primaryButton:"Add phone number",title:"Phone numbers"},profileSection:{primaryButton:"Update profile",title:"Profile"},usernameSection:{primaryButton__setUsername:"Set username",primaryButton__updateUsername:"Update username",title:"Username"},web3WalletsSection:{destructiveAction:"Remove wallet",detailsAction__nonPrimary:"Set as primary",primaryButton:"Connect wallet",title:"Web3 wallets"}},usernamePage:{successMessage:"Your username has been updated.",title__set:"Set username",title__update:"Update username"},web3WalletPage:{removeResource:{messageLine1:"{{identifier}} will be removed from this account.",messageLine2:"You will no longer be able to sign in using this web3 wallet.",successMessage:"{{web3Wallet}} has been removed from your account.",title:"Remove web3 wallet"},subtitle__availableWallets:"Select a web3 wallet to connect to your account.",subtitle__unavailableWallets:"There are no available web3 wallets.",successMessage:"The wallet has been added to your account.",title:"Add web3 wallet",web3WalletButtonsBlockButton:"{{provider|titleize}}"}},waitlist:{start:{actionLink:"Sign in",actionText:"Already have access?",formButton:"Join the waitlist",subtitle:"Enter your email address and we’ll let you know when your spot is ready",title:"Join the waitlist"},success:{message:"You will be redirected soon...",subtitle:"We’ll be in touch when your spot is ready",title:"Thanks for joining the waitlist!"}}};var w=r(5059),x=r(5100),S=r(4662);let $=(e,t)=>{if(!n||o&&o!==e&&!(0,S.J)(e,o)){o=e;let r={};(0,x.EB)(t,r),(0,x.EB)(e,r),n=r}return n},C=()=>{let{localization:e}=(0,s.useOptions)();return $(e||{},y)},_=e=>{let t=l.forwardRef((t,r)=>{let n=C(),{localizationKey:o,...i}=t,l=h();return o?"string"==typeof o?(0,a.tZ)(e,{...i,ref:r,children:o}):(0,a.tZ)(e,{...i,ref:r,"data-localization-key":P(o),children:I(o,n,l)||i.children}):(0,a.tZ)(e,{...i,ref:r})}),r=e.displayName||e.name||"Component";return t.displayName=`Localizable${r}`.replace("_",""),t},k=()=>{let{localization:e}=(0,s.useOptions)(),t=C(),r=h(),n=e=>e&&"string"!=typeof e?I(e,t,r):e||"";return{t:n,translateError:e=>{if(!e||"string"==typeof e)return n(e);if((0,i.uX)(e))return n((0,w.u)(`unstable__errors.${e.code}`))||e.message;let{code:t,message:r,longMessage:o,meta:a}=e||{},{paramName:l=""}=a||{};return t?n((0,w.u)(`unstable__errors.${t}__${l}`))||n((0,w.u)(`unstable__errors.${t}`))||o||r:""},locale:e?.locale||y?.locale}},P=e=>e.key,I=(e,t,r)=>{let n=e.key,o=(0,c.Ai)(t,n),a=e.params;return m(o||"",{...r,...a})}},5059:function(e,t,r){r.d(t,{u:()=>n});let n=(e,t)=>({key:e,params:t})},4750:function(e,t,r){r.r(t),r.d(t,{Portal:()=>u,VirtualBodyRootPortal:()=>p});var n=r(9109),o=r(9144),a=r(8315),i=r(753),l=r(4152),s=r(2073),c=r(1576),d=r(4676);function u({props:e,component:t,componentName:r,node:u}){let p={...e,...(0,s.L)({routing:e?.routing,path:e?.path})},m=(0,n.tZ)(c.ComponentContextProvider,{componentName:r,props:p,children:(0,n.tZ)(o.Suspense,{fallback:"",children:o.createElement(t,p)})});return p?.routing==="path"?(p?.path||(0,l.yI)(r),a.createPortal((0,n.tZ)(d.PathRouter,{preservedParams:i.Yt,basePath:p.path,children:m}),u)):a.createPortal((0,n.tZ)(d.HashRouter,{preservedParams:i.Yt,children:m}),u)}class p extends o.PureComponent{elRef=document.createElement("div");componentDidMount(){document.body.appendChild(this.elRef)}componentWillUnmount(){document.body.removeChild(this.elRef)}render(){let{props:e,startPath:t,component:r,componentName:i}=this.props;return a.createPortal((0,n.tZ)(d.VirtualRouter,{startPath:t,children:(0,n.tZ)(c.ComponentContextProvider,{componentName:i,props:e??{},children:(0,n.tZ)(o.Suspense,{fallback:"",children:o.createElement(r,e)})})}),this.elRef)}}},734:function(e,t,r){r.d(t,{X2:()=>d,YV:()=>s,aS:()=>c});var n=r(9109),o=r(3799),a=r(9144),i=r(6788);let[l,,s]=(0,o.uH)("FormFieldContext"),c=e=>{let{id:t,isRequired:r=!1,isDisabled:o=!1,setError:s,setSuccess:c,setWarning:d,setHasPassedComplexity:u,setInfo:p,clearFeedback:m,children:h,feedbackType:g,feedback:f,isFocused:b,...v}=e,y=`${t}-field`,{debounced:w}=(0,i.V2)({feedback:f,feedbackType:g,isFocused:b}),x="error"===w.feedbackType,S=x?`error-${t}`:"",$=a.useMemo(()=>({isRequired:r,isDisabled:o,hasError:x,id:y,fieldId:t,errorMessageId:S,setError:s,setSuccess:c,setWarning:d,setInfo:p,clearFeedback:m,setHasPassedComplexity:u,feedbackType:g,feedback:f,isFocused:b}),[r,x,y,t,S,o,s,c,d,p,m,u,g,f,b]);return(0,n.tZ)(l.Provider,{value:{value:{...$,...v,debouncedFeedback:w}},children:e.children})},d=(e,t)=>{let{radioOptions:r,validatePassword:n,hasPassedComplexity:o,isFocused:a,feedback:i,feedbackType:l,setHasPassedComplexity:s,setWarning:c,setSuccess:d,setError:u,setInfo:p,errorMessageId:m,fieldId:h,label:g,clearFeedback:f,infoText:b,debouncedFeedback:v,ignorePasswordManager:y,transformer:w,...x}=e;return t?.forEach(t=>{x[t]=e[t]}),x}},215:function(e,t,r){r.d(t,{Dt:()=>z,Cc:()=>ef,l0:()=>D,zM:()=>w,JX:()=>m,rj:()=>q,Th:()=>eD,iA:()=>eP,hr:()=>eL,JO:()=>er,II:()=>ei,$j:()=>e$,Ct:()=>C,Dd:()=>Z,Xc:()=>V,rF:()=>R,xu:()=>s,NA:()=>es,Zh:()=>W,zx:()=>T,Ee:()=>en,I0:()=>el,Tr:()=>eE,rU:()=>ep,Dl:()=>B,p3:()=>eI,bZ:()=>f,kC:()=>p,gx:()=>ec,X6:()=>J,Hr:()=>eM,ZD:()=>K,lX:()=>N,Dr:()=>eb,Td:()=>eR,jo:()=>U,xv:()=>O});var n=r(9109),o=r(1201),a=r(9144);let i=e=>{let{hasError:t,isDisabled:r,isLoading:n,isOpen:o,isActive:a,...i}=e;return{"data-error":t||void 0,"data-disabled":r||void 0,"data-loading":n||void 0,"data-open":o||void 0,"data-active":a||void 0,...i}},{applyVariants:l}=(0,o.createVariants)(()=>({base:{boxSizing:"border-box"},variants:{}})),s=a.forwardRef((e,t)=>{let{as:r="div",...o}=e;return(0,n.tZ)(r,{...i(o),css:l(e),ref:t})}),c=(e,t)=>navigator?.userAgent?.match(/(iphone|ipad).+(os).*(\s13_).+safari/i)?{'& > *:not([hidden]):not([style*="visibility: hidden"]) + *:not([hidden]):not([style*="visibility: hidden"])':{marginLeft:"row"===t?e:void 0,marginTop:"col"===t?e:void 0}}:{gap:e},{applyVariants:d,filterProps:u}=(0,o.createVariants)((e,t)=>{let r="col"===t.flexDirection||"colReverse"===t.flexDirection||"col"===t.direction||"colReverse"===t.direction?"col":"row";return{base:{display:"flex"},variants:{direction:{row:{flexDirection:"row"},col:{flexDirection:"column"},rowReverse:{flexDirection:"row-reverse"},columnReverse:{flexDirection:"column-reverse"}},align:{start:{alignItems:"flex-start"},center:{alignItems:"center"},end:{alignItems:"flex-end"},stretch:{alignItems:"stretch"},baseline:{alignItems:"baseline"}},justify:{start:{justifyContent:"flex-start"},center:{justifyContent:"center"},end:{justifyContent:"flex-end"},between:{justifyContent:"space-between"}},wrap:{noWrap:{flexWrap:"nowrap"},wrap:{flexWrap:"wrap"},wrapReverse:{flexWrap:"wrap-reverse"}},gap:{1:c(e.space.$1,r),2:c(e.space.$2,r),3:c(e.space.$3,r),4:c(e.space.$4,r),5:c(e.space.$5,r),6:c(e.space.$6,r),7:c(e.space.$7,r),8:c(e.space.$8,r),9:c(e.space.$9,r)},center:{true:{justifyContent:"center",alignItems:"center"}}},defaultVariants:{direction:"row",align:"stretch",justify:"start",wrap:"noWrap"}}}),p=a.forwardRef((e,t)=>(0,n.tZ)(s,{...u(e),css:d(e),ref:t})),m=a.forwardRef((e,t)=>(0,n.tZ)(p,{...e,direction:"col",ref:t})),{applyVariants:h,filterProps:g}=(0,o.createVariants)(e=>({base:{padding:`${e.space.$3} ${e.space.$4}`,backgroundColor:e.colors.$neutralAlpha50,...o.common.borderVariants(e).normal},variants:{colorScheme:{danger:{color:e.colors.$danger500,backgroundColor:e.colors.$dangerAlpha50,...o.common.borderVariants(e,{hasError:!0}).normal},info:{color:e.colors.$neutralAlpha150,background:e.colors.$neutralAlpha50,":hover":{boxShadow:"none",borderColor:"inherit"}},warning:{backgroundColor:e.colors.$warningAlpha100}}},defaultVariants:{colorScheme:"warning"}})),f=e=>(0,n.tZ)(p,{align:"center",justify:"start",...g(e),css:h(e),children:e.children});var b=r(4174);let{applyVariants:v,filterProps:y}=(0,o.createVariants)(e=>({base:{marginRight:e.space.$2x5,width:e.sizes.$4,height:e.sizes.$4},variants:{colorScheme:{danger:{color:e.colors.$danger500},warning:{color:e.colors.$warning500},success:{color:e.colors.$success500},primary:{color:e.colors.$primary500},info:{color:e.colors.$colorTextSecondary}}}})),w=e=>{let{variant:t,...r}=e,o={warning:b.SV,danger:b.SV,primary:b.I$,info:b.I$}[t];return(0,n.tZ)(o,{...y(r),css:v(e)})},x=(0,o.createCssVariables)("accent","bg","borderColor"),{applyVariants:S,filterProps:$}=(0,o.createVariants)(e=>({base:{color:x.accent,flexShrink:0,backgroundColor:x.bg,boxShadow:e.shadows.$badge,borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:x.borderColor,borderRadius:e.radii.$sm,padding:`${e.space.$0x25} ${e.space.$1x5}`,display:"inline-flex",marginRight:"1px"},variants:{textVariant:{...o.common.textVariants(e)},colorScheme:{primary:{[x.accent]:e.colors.$neutralAlpha600,[x.bg]:e.colors.$neutralAlpha50,[x.borderColor]:e.colors.$neutralAlpha150},secondary:{[x.accent]:e.colors.$colorTextOnPrimaryBackground,[x.bg]:e.colors.$primary500,[x.borderColor]:e.colors.$primary500},danger:{[x.accent]:e.colors.$danger500,[x.bg]:e.colors.$dangerAlpha50,[x.borderColor]:e.colors.$dangerAlpha300},success:{[x.accent]:e.colors.$success500,[x.bg]:e.colors.$successAlpha50,[x.borderColor]:e.colors.$successAlpha300},warning:{[x.accent]:e.colors.$warning500,[x.bg]:e.colors.$warningAlpha50,[x.borderColor]:e.colors.$warningAlpha300}}},defaultVariants:{colorScheme:"primary",textVariant:"caption"}})),C=e=>(0,n.tZ)(p,{...$(e),center:!0,as:"span",css:S(e),"data-color":e.colorScheme||"primary"});var _=r(9541);let k=(0,o.createCssVariables)("accent","accentHover","accentContrast","alpha","border"),{applyVariants:P,filterProps:I}=(0,o.createVariants)((e,t)=>({base:{margin:0,padding:0,borderWidth:0,outline:0,userSelect:"none",cursor:"pointer",backgroundColor:"unset",color:"currentColor",borderRadius:e.radii.$md,position:"relative",isolation:"isolate",...o.common.centeredFlex("inline-flex"),...o.common.disabled(e),transitionProperty:e.transitionProperty.$common,transitionDuration:e.transitionDuration.$controls},variants:{textVariant:o.common.textVariants(e),size:{iconLg:{width:e.sizes.$13},xs:{padding:`${e.space.$1} ${e.space.$3}`},sm:{padding:`${e.space.$1x5} ${e.space.$3}`},md:{padding:`${e.space.$2x5} ${e.space.$5}`}},colorScheme:{primary:{[k.accent]:e.colors.$primary500,[k.accentHover]:e.colors.$primaryHover,[k.border]:e.colors.$primary500,[k.accentContrast]:e.colors.$colorTextOnPrimaryBackground,[k.alpha]:e.colors.$neutralAlpha50},secondary:{[k.accent]:e.colors.$colorBackground,[k.accentHover]:`color-mix(in srgb, ${k.accent}, ${e.colors.$neutralAlpha50})`,[k.border]:e.colors.$primary500,[k.accentContrast]:e.colors.$colorText,[k.alpha]:e.colors.$neutralAlpha50},neutral:{[k.accent]:e.colors.$neutralAlpha600,[k.accentHover]:e.colors.$neutralAlpha700,[k.border]:e.colors.$neutralAlpha200,[k.accentContrast]:e.colors.$white,[k.alpha]:e.colors.$neutralAlpha50},danger:{[k.accent]:e.colors.$danger500,[k.accentHover]:e.colors.$danger600,[k.accentContrast]:e.colors.$white,[k.border]:e.colors.$danger500,[k.alpha]:e.colors.$dangerAlpha50}},variant:{solid:{backgroundColor:k.accent,color:k.accentContrast,boxShadow:e.shadows.$buttonShadow,borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:k.accent,"&:hover":{backgroundColor:k.accentHover},"&:focus":t.hoverAsFocus?{backgroundColor:k.accentHover}:void 0},outline:{borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha100,color:e.colors.$neutralAlpha600,"&:hover":{backgroundColor:e.colors.$neutralAlpha50},"&:focus":t.hoverAsFocus?{backgroundColor:e.colors.$neutralAlpha50}:void 0,boxShadow:e.shadows.$outlineButtonShadow},bordered:{borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha100,color:k.accentContrast,backgroundColor:k.accent,"&:hover":{backgroundColor:k.accentHover},"&:focus":t.hoverAsFocus?{backgroundColor:k.accentHover}:void 0},ghost:{color:k.accent,"&:hover":{backgroundColor:k.alpha,color:k.accentHover},"&:focus":t.hoverAsFocus?{backgroundColor:k.alpha,color:k.accentHover}:void 0},link:{minHeight:"fit-content",height:"fit-content",width:"fit-content",textTransform:"none",padding:0,color:e.colors.$primary500,"&:hover":{textDecoration:"underline"},"&:focus":t.hoverAsFocus?{textDecoration:"underline"}:void 0},linkDanger:{minHeight:"fit-content",height:"fit-content",width:"fit-content",textTransform:"none",padding:0,color:e.colors.$danger500,"&:hover":{textDecoration:"underline"},"&:focus":t.hoverAsFocus?{textDecoration:"underline"}:void 0},unstyled:{},roundWrapper:{padding:0,margin:0,height:"unset",width:"unset",minHeight:"unset"}},block:{true:{width:"100%"}},focusRing:{true:{...o.common.focusRing(e)}}},defaultVariants:{textVariant:"buttonLarge",variant:"solid",colorScheme:"primary",size:"sm",focusRing:!0}})),A=({children:e})=>(0,n.BX)(p,{align:"center",as:"span",children:[e,(0,n.tZ)(_.Icon,{elementDescriptor:_.descriptors.buttonArrowIcon,icon:b.p8,sx:e=>({marginLeft:e.space.$2,width:e.sizes.$2x5,height:e.sizes.$2x5,opacity:e.opacity.$inactive})})]}),T=a.forwardRef((e,t)=>{let r={...e,isDisabled:e.isDisabled||e.isLoading},{isLoading:o,isDisabled:a,hoverAsFocus:l,loadingText:s,children:c,hasArrow:d,onClick:u,...m}=I(r);return(0,n.BX)("button",{...i(m),type:void 0,onClick:e=>("submit"!==m.type&&e.preventDefault(),u?.(e)),disabled:a,css:P(r),"data-variant":e.variant||"solid","data-color":e.colorScheme||"primary",ref:t,children:[o&&(0,n.BX)(p,{as:"span",gap:2,center:!0,css:{position:"relative"},children:[(0,n.tZ)(_.Spinner,{"aria-label":s||"Loading",elementDescriptor:_.descriptors.spinner,sx:{position:s?void 0:"absolute"}}),s||(0,n.tZ)("span",{style:{display:"inline-flex",visibility:"hidden"},children:c})]}),!o&&(d?(0,n.tZ)(A,{children:c}):c)]})}),R=a.forwardRef((e,t)=>{let r={...e,isDisabled:e.isDisabled||e.isLoading},{loadingText:o,isDisabled:a,hoverAsFocus:l,children:s,onClick:c,...d}=I(r);return(0,n.tZ)("button",{...i(d),type:void 0,onClick:e=>("submit"!==d.type&&e.preventDefault(),c?.(e)),css:P(r),disabled:a,"data-variant":e.variant||"solid","data-color":e.colorScheme||"primary",ref:t,children:s})}),B=a.forwardRef((e,t)=>(0,n.tZ)(s,{as:"dl",css:{margin:0},...e,ref:t})),Z=a.forwardRef((e,t)=>(0,n.tZ)(s,{as:"dd",css:{margin:0},...e,ref:t})),z=a.forwardRef((e,t)=>(0,n.tZ)(s,{as:"dt",css:{margin:0},...e,ref:t})),D=a.forwardRef((e,t)=>(0,n.tZ)(p,{direction:"col",as:"form",...e,ref:t}));var L=r(734);let{applyVariants:E,filterProps:F}=(0,o.createVariants)(e=>({base:{boxSizing:"border-box",margin:0,fontSize:"inherit",...o.common.disabled(e)},variants:{variant:o.common.textVariants(e),colorScheme:{body:{color:e.colors.$colorText},onPrimaryBg:{color:e.colors.$colorTextOnPrimaryBackground},danger:{color:e.colors.$danger500},success:{color:e.colors.$success500},warning:{color:e.colors.$warning500},secondary:{color:e.colors.$colorTextSecondary},inherit:{color:"inherit"}},truncate:{true:{textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"}}},defaultVariants:{variant:"body",colorScheme:"inherit"}})),O=a.forwardRef((e,t)=>{let{as:r="p",...o}=e;return(0,n.tZ)(r,{...i(F(o)),css:E(e),ref:t})}),{applyVariants:M}=(0,o.createVariants)(e=>({base:{marginTop:e.sizes.$1x5,animation:`${o.animations.textInSmall} ${e.transitionDuration.$fast}`,display:"flex",gap:e.sizes.$1,position:"absolute",top:"0",textAlign:"left"},variants:{}})),U=(0,a.forwardRef)((e,t)=>{let{hasError:r,errorMessageId:o}=(0,L.YV)()||{};if(!r&&!e.children)return null;let{children:a,...i}=e;return(0,n.BX)(O,{ref:t,colorScheme:"danger","aria-live":"polite",id:o,...i,css:M(e),children:[(0,n.tZ)(_.Icon,{colorScheme:"danger",icon:b.bR}),a]})}),{applyVariants:j}=(0,o.createVariants)(e=>({base:{marginTop:e.sizes.$1x5,animation:`${o.animations.textInSmall} ${e.transitionDuration.$fast}`,display:"flex",gap:e.sizes.$1,position:"absolute",top:"0",textAlign:"left"},variants:{}})),V=(0,a.forwardRef)((e,t)=>{let{children:r,...o}=e;return(0,n.BX)(O,{ref:t,colorScheme:"secondary","aria-live":"polite",...o,css:j(e),children:[(0,n.tZ)(_.Icon,{colorScheme:"success",icon:b.fU}),r]})}),W=(0,a.forwardRef)((e,t)=>{let{hasError:r,errorMessageId:o}=(0,L.YV)()||{};return r||e.children?(0,n.tZ)(O,{ref:t,colorScheme:"secondary","aria-live":"polite",id:o,...e,css:j(e)}):null}),{applyVariants:H}=(0,o.createVariants)(e=>({base:{color:e.colors.$colorText,...o.common.textVariants(e).subtitle,...o.common.disabled(e)},variants:{}})),N=e=>{let{id:t}=(0,L.YV)(),{isRequired:r,htmlFor:o,...a}=e;return(0,n.tZ)("label",{...i(a),htmlFor:o??t,css:H(e),children:e.children})},K=(0,a.forwardRef)((e,t)=>{let{children:r,...o}=e;return(0,n.BX)(O,{ref:t,colorScheme:"secondary","aria-live":"polite",...o,css:j(e),children:[(0,n.tZ)(_.Icon,{colorScheme:"warning",icon:b.bR}),r]})}),{applyVariants:X,filterProps:Y}=(0,o.createVariants)(e=>({base:{display:"grid"},variants:{align:{start:{alignItems:"flex-start"},center:{alignItems:"center"},end:{alignItems:"flex-end"},stretch:{alignItems:"stretch"},baseline:{alignItems:"baseline"}},justify:{start:{justifyContent:"flex-start"},center:{justifyContent:"center"},end:{justifyContent:"flex-end"},between:{justifyContent:"space-between"},around:{justifyContent:"space-around"},stretch:{justifyContent:"stretch"}},columns:{1:{gridTemplateColumns:"1fr"},2:{gridTemplateColumns:"repeat(2, 1fr)"},3:{gridTemplateColumns:"repeat(3, 1fr)"},4:{gridTemplateColumns:"repeat(4, 1fr)"},6:{gridTemplateColumns:"repeat(6, 1fr)"}},gap:{1:{gap:e.space.$1},2:{gap:e.space.$2},3:{gap:e.space.$3},4:{gap:e.space.$4},5:{gap:e.space.$5},6:{gap:e.space.$6},7:{gap:e.space.$7},8:{gap:e.space.$8},9:{gap:e.space.$9}}},defaultVariants:{align:"stretch",justify:"stretch",wrap:"noWrap"}})),q=a.forwardRef((e,t)=>(0,n.tZ)(s,{...Y(e),css:X(e),ref:t})),{applyVariants:G,filterProps:Q}=(0,o.createVariants)(e=>({base:{boxSizing:"border-box",color:`${e.colors.$colorText}`,margin:0},variants:{textVariant:{...o.common.textVariants(e)}},defaultVariants:{as:"h1",textVariant:"h1"}})),J=e=>{let{as:t="h1",...r}=e;return(0,n.tZ)(t,{...Q(r),css:G(e)})},{applyVariants:ee,filterProps:et}=(0,o.createVariants)(e=>({base:{flexShrink:0},variants:{size:{xs:{width:e.sizes.$2x5,height:e.sizes.$2x5},sm:{width:e.sizes.$3,height:e.sizes.$3},md:{width:e.sizes.$4,height:e.sizes.$4},lg:{width:e.sizes.$5,height:e.sizes.$5}},colorScheme:{success:{color:e.colors.$success500},danger:{color:e.colors.$danger500},warning:{color:e.colors.$warning500},neutral:{color:e.colors.$neutralAlpha400}}},defaultVariants:{size:"md"}})),er=e=>{let{icon:t,...r}=e;return(0,n.tZ)(t,{...et(r),css:ee(e)})},en=a.forwardRef((e,t)=>(0,n.tZ)("img",{crossOrigin:"anonymous",...i(e),ref:t})),{applyVariants:eo,filterProps:ea}=(0,o.createVariants)((e,t)=>({base:{boxSizing:"border-box",margin:0,padding:`${e.space.$1x5} ${e.space.$3}`,backgroundColor:e.colors.$colorInputBackground,color:e.colors.$colorInputText,outline:"transparent solid 2px",outlineOffset:"2px",maxHeight:e.sizes.$9,width:"checkbox"===t.type?e.sizes.$4:"100%",aspectRatio:"checkbox"===t.type?"1/1":"unset",accentColor:e.colors.$primary500,...o.common.textVariants(e).body,...o.common.disabled(e),[o.mqu.ios]:{fontSize:e.fontSizes.$lg,'&:not([type="checkbox"]):not([type="radio"])':{WebkitAppearance:"none"}},":autofill":{animationName:"onAutoFillStart"},"::placeholder":{color:e.colors.$colorTextSecondary}},variants:{variant:{default:{...o.common.borderVariants(e,t).normal},unstyled:{borderWidth:0,boxShadow:"unset",backgroundColor:"transparent"}}},defaultVariants:{variant:"default"}})),ei=a.forwardRef((e,t)=>{var r;let o=(0,L.YV)()||{},{errorMessageId:i,ignorePasswordManager:l,feedbackType:s,...c}=(0,L.X2)(o,["errorMessageId","ignorePasswordManager","feedbackType"]),d=ea({...e,hasError:e.hasError||c.hasError}),{onChange:u}=(r=d.onChange,{onChange:function(e){e.persist(),"function"==typeof r&&r(e)},ref:a.useRef(null)}),{isDisabled:p,hasError:m,focusRing:h,isRequired:g,type:f,...b}=d,v=p||c.isDisabled,y=g||c.isRequired,w=m||c.hasError;return(0,n.tZ)("input",{...b,..."email"===f?{type:"text",pattern:"^.*@[a-zA-Z0-9\\-]+\\.[a-zA-Z0-9\\-\\.]+$"}:{type:f||"text"},...l?{"data-1p-ignore":!0}:void 0,ref:t,onChange:u,disabled:p,required:y,id:e.id||c.id,"aria-invalid":w,"aria-describedby":i||void 0,"aria-required":y,"aria-disabled":v,"data-feedback":s,"data-variant":e.variant||"default",css:eo(e)})}),el=a.forwardRef((e,t)=>(0,n.tZ)(ei,{...e,type:"checkbox",ref:t})),es=a.forwardRef((e,t)=>(0,n.tZ)(ei,{...e,type:"radio",ref:t})),ec=a.forwardRef((e,t)=>{let{isDisabled:r,hasError:o,focusRing:a,isRequired:i,...l}=e,s=r||e.isDisabled,c=i||e.isRequired,d=o||e.hasError;return(0,n.tZ)("textarea",{...l,ref:t,disabled:s,required:c,"aria-invalid":d,"aria-required":c,css:eo(e)})}),{applyVariants:ed,filterProps:eu}=(0,o.createVariants)(e=>({base:{boxSizing:"border-box",display:"inline-flex",alignItems:"center",margin:0,cursor:"pointer",...o.common.disabled(e),textDecoration:"none","&:hover":{textDecoration:"underline"}},variants:{variant:o.common.textVariants(e),colorScheme:{primary:{color:e.colors.$primary500,"&:hover":{color:e.colors.$primary400},"&:active":{color:e.colors.$primary600}},danger:{color:e.colors.$danger500,"&:hover":{color:e.colors.$danger400},"&:active":{color:e.colors.$danger600}},neutral:{color:e.colors.$colorTextSecondary},inherit:{color:"inherit"}}},defaultVariants:{colorScheme:"primary",variant:"body"}})),ep=e=>{let{isExternal:t,children:r,href:o,onClick:a,...l}=e,s=a?e=>{o||e.preventDefault(),a(e)}:void 0;return(0,n.tZ)("a",{...i(eu(l)),onClick:s,href:o||"",target:o&&t?"_blank":void 0,rel:o&&t?"noopener":void 0,css:ed(e),children:r})},em=(0,o.createCssVariables)("accent","bg","borderColor"),{applyVariants:eh,filterProps:eg}=(0,o.createVariants)(e=>({base:{color:em.accent,background:em.bg,borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderRadius:e.radii.$lg,borderColor:em.borderColor,height:e.space.$4,minWidth:e.space.$5,padding:`${e.space.$0x5} ${e.space.$1}`,display:"inline-flex"},variants:{textVariant:{...o.common.textVariants(e)},colorScheme:{primary:{[em.accent]:e.colors.$colorTextOnPrimaryBackground,[em.bg]:`linear-gradient(180deg, ${e.colors.$whiteAlpha300} 0%, ${e.colors.$transparent} 100%), ${e.colors.$primary500}`,borderWidth:0},outline:{[em.accent]:e.colors.$neutralAlpha600,[em.bg]:"transparent",[em.borderColor]:e.colors.$neutralAlpha150}}},defaultVariants:{colorScheme:"primary",textVariant:"caption"}})),ef=e=>(0,n.tZ)(p,{...eg(e),center:!0,as:"span",css:[eh(e),{lineHeight:0}]}),eb=a.forwardRef((e,t)=>(0,n.tZ)("span",{...e,ref:t})),{size:ev,thickness:ey,speed:ew}=(0,o.createCssVariables)("speed","size","thickness"),{applyVariants:ex,filterProps:eS}=(0,o.createVariants)(e=>({base:{display:"inline-block",borderRadius:"99999px",borderTop:`${ey} solid currentColor`,borderRight:`${ey} solid currentColor`,borderBottomWidth:ey,borderLeftWidth:ey,borderBottomStyle:"solid",borderLeftStyle:"solid",borderBottomColor:e.colors.$transparent,borderLeftColor:e.colors.$transparent,opacity:1,animation:`${o.animations.spinning} ${ew} linear 0s infinite normal none running`,width:[ev],height:[ev],minWidth:[ev],minHeight:[ev]},variants:{colorScheme:{primary:{borderTopColor:e.colors.$primary500,borderRightColor:e.colors.$primary500,opacity:1},neutral:{borderTopColor:e.colors.$neutralAlpha700,borderRightColor:e.colors.$neutralAlpha700,opacity:1}},thickness:{sm:{[ey]:e.sizes.$0x5},md:{[ey]:e.sizes.$1}},size:{xs:{[ev]:e.sizes.$3},sm:{[ev]:e.sizes.$4},md:{[ev]:e.sizes.$5},lg:{[ev]:e.sizes.$6},xl:{[ev]:e.sizes.$8}},speed:{slow:{[ew]:"600ms"},normal:{[ew]:"400ms"}}},defaultVariants:{speed:"normal",thickness:"sm",size:"sm"}})),e$=e=>(0,n.tZ)("span",{...eS(e),css:ex(e),"aria-busy":!0,"aria-live":"polite"});var eC=r(7504);let{applyVariants:e_,filterProps:ek}=(0,o.createVariants)(e=>({base:{borderSpacing:"0",borderCollapse:"separate",borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha100,borderRadius:e.radii.$lg,boxShadow:e.shadows.$tableBodyShadow,width:"100%",">:not([hidden])~:not([hidden])":{borderBottomWidth:"0px",borderTopWidth:"1px",borderStyle:"solid",borderLeftWidth:"0px",borderRightWidth:"0px",borderColor:e.colors.$neutralAlpha100},"td:not(:first-of-type)":{paddingLeft:e.space.$2},"th:not(:first-of-type)":{paddingLeft:e.space.$2},"tr > td":{borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100,paddingBottom:e.space.$2,paddingTop:e.space.$2,paddingLeft:e.space.$4,paddingRight:e.space.$4},"tbody > :not([hidden])~:not([hidden])":{borderBottomWidth:"0px",borderTopWidth:"1px",borderStyle:"solid",borderLeftWidth:"0px",borderRightWidth:"0px",borderColor:e.colors.$neutralAlpha100},"tr:hover td:first-of-type":{borderBottomLeftRadius:e.radii.$lg},"tr:hover td:last-of-type":{borderBottomRightRadius:e.radii.$lg},"tr > th:first-of-type":{paddingLeft:e.space.$5},"thead::after":{content:'""',display:"block"}},variants:{tableHeadVisuallyHidden:{true:{thead:{...eC.y.visuallyHidden()},"tr:first-of-type td":{borderTop:"none"}}}}})),eP=a.forwardRef((e,t)=>(0,n.tZ)(s,{as:"table",...ek(e),css:e_(e),ref:t})),eI=a.forwardRef((e,t)=>(0,n.tZ)(s,{as:"tbody",...e,ref:t})),{applyVariants:eA,filterProps:eT}=(0,o.createVariants)(e=>({base:{fontSize:e.fontSizes.$xs,fontWeight:e.fontWeights.$normal,color:e.colors.$colorText},variants:{}})),eR=a.forwardRef((e,t)=>(0,n.tZ)(s,{as:"td",...eT(e),css:eA(e),ref:t}));var eB=r(7623);let{applyVariants:eZ,filterProps:ez}=(0,o.createVariants)(e=>({base:{textAlign:"left",fontSize:e.fontSizes.$sm,fontWeight:e.fontWeights.$normal,color:eB.O9.setAlpha(e.colors.$colorText,.62),padding:`${e.space.$2} ${e.space.$4}`},variants:{}})),eD=a.forwardRef((e,t)=>(0,n.tZ)(s,{as:"th",...ez(e),css:eZ(e),ref:t})),eL=a.forwardRef((e,t)=>(0,n.tZ)(s,{as:"thead",...e,ref:t})),eE=a.forwardRef((e,t)=>(0,n.tZ)(s,{as:"tr",...e,ref:t})),{applyVariants:eF,filterProps:eO}=(0,o.createVariants)(e=>({base:{border:"none",height:e.space.$px,backgroundColor:e.colors.$neutralAlpha100},variants:{}})),eM=a.forwardRef((e,t)=>(0,n.tZ)(s,{as:"hr",...eO(e),css:eF(e),ref:t}))},5117:function(e,t,r){r.d(t,{X:()=>o,t:()=>a});var n=r(9144);let o=n.createContext(null);o.displayName="RouteContext";let a=()=>{let e=n.useContext(o);if(!e)throw Error("useRouter called while Router is null");return e}},4676:function(e,t,r){r.r(t),r.d(t,{PathRouter:()=>v,RouteContext:()=>n.X,VirtualRouter:()=>w,useRouter:()=>n.t,VIRTUAL_ROUTER_BASE_PATH:()=>y,HashRouter:()=>b,Switch:()=>x,Route:()=>h,hashRouterBase:()=>f});var n=r(5117),o=r(9109),a=r(9144),i=r(6917),l=r(3799),s=r(2464);let c=(e,t,r,n)=>{let o=e;r&&(o=t,n||(o+="/"+r)),o.startsWith("//")&&(o=o.substr(1));let a=t+(r?"/"+r:"");return a.startsWith("//")&&(a=a.substr(1)),[o,a]};function d(e,t){let r=[];return function(e,t,r={}){let{decode:n=e=>e}=r;return function(r){let o=e.exec(r);if(!o)return!1;let{0:a,index:i}=o,l=Object.create(null);for(let e=1;e<o.length;e++){if(void 0===o[e])continue;let r=t[e-1];"*"===r.modifier||"+"===r.modifier?l[r.name]=o[e].split(r.prefix+r.suffix).map(e=>n(e,r)):l[r.name]=n(o[e],r)}return{path:a,index:i,params:l}}}(function e(t,r,n){var o,a,i;return t instanceof RegExp?function(e,t){if(!t)return e;let r=e.source.match(/\((?!\?)/g);if(r)for(let e=0;e<r.length;e++)t.push({name:e,prefix:"",suffix:"",modifier:"",pattern:""});return e}(t,r):Array.isArray(t)?function(t,r,n){let o=t.map(t=>e(t,r,n).source);return RegExp(`(?:${o.join("|")})`,p(n))}(t,r,n):function(e,t,r={}){let{strict:n=!1,start:o=!0,end:a=!0,encode:i=e=>e}=r,l=`[${u(r.endsWith||"")}]|$`,s=`[${u(r.delimiter||"/#?")}]`,c=o?"^":"";for(let r of e)if("string"==typeof r)c+=u(i(r));else{let e=u(i(r.prefix)),n=u(i(r.suffix));if(r.pattern){if(t&&t.push(r),e||n){if("+"===r.modifier||"*"===r.modifier){let t="*"===r.modifier?"?":"";c+=`(?:${e}((?:${r.pattern})(?:${n}${e}(?:${r.pattern}))*)${n})${t}`}else c+=`(?:${e}(${r.pattern})${n})${r.modifier}`}else c+=`(${r.pattern})${r.modifier}`}else c+=`(?:${e}${n})${r.modifier}`}if(a)n||(c+=`${s}?`),c+=r.endsWith?`(?=${l})`:"$";else{let t=e[e.length-1],r="string"==typeof t?s.indexOf(t[t.length-1])>-1:void 0===t;n||(c+=`(?:${s}(?=${l}))?`),r||(c+=`(?=${s}|${l})`)}return new RegExp(c,p(r))}(function(e,t={}){let r=function(e){let t=[],r=0;for(;r<e.length;){let n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){let n="",o=r+1;for(;o<e.length;){let t=e.charCodeAt(o);if(t>=48&&t<=57||t>=65&&t<=90||t>=97&&t<=122||95===t){n+=e[o++];continue}break}if(!n)throw TypeError(`Missing parameter name at ${r}`);t.push({type:"NAME",index:r,value:n}),r=o;continue}if("("===n){let n=1,o="",a=r+1;if("?"===e[a])throw TypeError(`Pattern cannot start with "?" at ${a}`);for(;a<e.length;){if("\\"===e[a]){o+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--n){a++;break}}else if("("===e[a]&&(n++,"?"!==e[a+1]))throw TypeError(`Capturing groups are not allowed at ${a}`);o+=e[a++]}if(n)throw TypeError(`Unbalanced pattern at ${r}`);if(!o)throw TypeError(`Missing pattern at ${r}`);t.push({type:"PATTERN",index:r,value:o}),r=a;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),{prefixes:n="./"}=t,o=`[^${u(t.delimiter||"/#?")}]+?`,a=[],i=0,l=0,s="",c=e=>{if(l<r.length&&r[l].type===e)return r[l++].value},d=e=>{let t=c(e);if(void 0!==t)return t;let{type:n,index:o}=r[l];throw TypeError(`Unexpected ${n} at ${o}, expected ${e}`)},p=()=>{let e,t="";for(;e=c("CHAR")||c("ESCAPED_CHAR");)t+=e;return t};for(;l<r.length;){let e=c("CHAR"),t=c("NAME"),r=c("PATTERN");if(t||r){let l=e||"";-1===n.indexOf(l)&&(s+=l,l=""),s&&(a.push(s),s=""),a.push({name:t||i++,prefix:l,suffix:"",pattern:r||o,modifier:c("MODIFIER")||""});continue}let l=e||c("ESCAPED_CHAR");if(l){s+=l;continue}if(s&&(a.push(s),s=""),c("OPEN")){let e=p(),t=c("NAME")||"",r=c("PATTERN")||"",n=p();d("CLOSE"),a.push({name:t||(r?i++:""),pattern:t&&!r?o:r,prefix:e,suffix:n,modifier:c("MODIFIER")||""});continue}d("END")}return a}(t,n),r,n)}(e,r,t),r,t)}function u(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function p(e){return e&&e.sensitive?"":"i"}let m=({canActivate:e,children:t})=>{let{navigateToFlowStart:r}=(0,s.zk)(),n=(0,l.cL)();return(a.useEffect(()=>{e(n)||r()}),e(n))?(0,o.tZ)(o.HY,{children:t}):null};function h(e){let t=(0,n.t)();if(!e.children)return null;if(!e.index&&!e.path)return(0,o.tZ)(o.HY,{children:e.children});if(!t.matches(e.path,e.index))return null;let[r,a]=c(t.indexPath,t.fullPath,e.path,e.index),l=(e,{searchParams:t}={})=>{let r=new URL(e,window.location.origin+a+"/");return t&&(r.search=t.toString()),r.pathname=(0,i.Os)(r.pathname),r},s=(e,n)=>{let[o,l]=c(r,a,e,n),s=(0,i.Os)(t.currentPath),u=e&&d(l+"/:foo*")(s)||n&&d(o)(s)||n&&d(l)(s)||!1;return!1!==u&&u.params},u=t.getMatchData(e.path,e.index)||{},p={};for(let[e,t]of Object.entries(u))p[e]=t;let h=(e.flowStart?(0,i.hb)(t.fullPath).replace("/"+t.basePath,""):t.flowStartPath)||t.startPath;return(0,o.tZ)(n.X.Provider,{value:{basePath:t.basePath,startPath:t.startPath,flowStartPath:h,indexPath:r,fullPath:a,currentPath:t.currentPath,queryParams:t.queryParams,queryString:t.queryString,baseNavigate:t.baseNavigate,getMatchData:s,matches:(e,t)=>!!s(e,t),resolve:l,navigate:(e,{searchParams:r}={})=>{let n=l(e,{searchParams:r});return t.baseNavigate(n)},refresh:t.refresh,params:p,urlStateParam:t.urlStateParam},children:e.canActivate?(0,o.tZ)(m,{canActivate:e.canActivate,children:e.children}):e.children})}let g=({basePath:e,startPath:t,getPath:r,getQueryString:u,internalNavigate:p,refreshEvents:m,preservedParams:g,urlStateParam:f,children:b})=>{let{navigate:v}=(0,l.cL)(),[y,w]=a.useState({path:r(),queryString:u()}),x=y.path,S=y.queryString,$=(0,i.vl)(y.queryString),C=(e,t)=>{let[r,n]=c("","",e,t),o=(0,i.Os)(x),a=e&&d(n+"/:foo*")(o)||t&&d(r)(o)||t&&d(n)(o)||!1;return!1!==a&&a.params},_=a.useCallback(()=>{let e=r(),t=u();(e!==x||t!==S)&&w({path:e,queryString:t})},[x,S,r,u]);(0,s.Fm)(m,_);let k=async t=>{if(!t)return;let r=t.origin!==window.location.origin;if(!t.pathname.startsWith("/"+e)||r){let e=await v(t.href);return _(),e}if(g){let e=(0,i.vl)(t.search);g.forEach(t=>{!e[t]&&$[t]&&(e[t]=$[t])}),t.search=(0,i.f0)(e)}let n=await p(t,{metadata:{navigationType:"internal"}});return w({path:t.pathname,queryString:t.search}),n};return(0,o.tZ)(n.X.Provider,{value:{basePath:e,startPath:t,flowStartPath:t,fullPath:"",indexPath:"",currentPath:x,queryString:S,queryParams:$,getMatchData:C.bind(void 0),matches:((e,t)=>!!C(e,t)).bind(void 0),baseNavigate:k.bind(void 0),navigate:async()=>{},resolve:(e=>new URL(e,window.location.origin)).bind(void 0),refresh:_.bind(void 0),params:{},urlStateParam:f},children:(0,o.tZ)(h,{path:e,children:b})})},f="CLERK-ROUTER/HASH",b=({preservedParams:e,children:t})=>{let r=async e=>{if(e)return window.location.hash=(0,i.M)(e).substring(1+f.length),Promise.resolve()},n=()=>new URL((0,i.eT)(window.location.hash)?window.location.origin+window.location.hash.substring(1):window.location.origin);return(0,o.tZ)(g,{getPath:()=>"/"===n().pathname?"/"+f:"/"+f+n().pathname,basePath:f,startPath:"",getQueryString:()=>n().search,internalNavigate:r,refreshEvents:["popstate","hashchange"],preservedParams:e,children:t})},v=({basePath:e,preservedParams:t,children:r})=>{let{navigate:n}=(0,l.cL)(),[s,c]=a.useState(!1);if(!n)throw Error("Clerk: Missing navigate option.");let d=(e,t)=>{if(e)return n((0,i.M)(e),t)};return(a.useEffect(()=>{(async()=>{if((0,i.eT)(window.location.hash)){let e=(0,i.z9)(new URL(window.location.href));await d(e.href,{replace:!0}),c(!0)}})()},[c,n,window.location.hash]),(0,i.eT)(window.location.hash)&&!s)?null:(0,o.tZ)(g,{basePath:e.substring(1),startPath:"",getPath:()=>window.location.pathname,getQueryString:()=>window.location.search,internalNavigate:d,refreshEvents:["popstate"],preservedParams:t,children:r})},y="CLERK-ROUTER/VIRTUAL",w=({startPath:e,preservedParams:t,onExternalNavigate:r,children:n})=>{let{__internal_addNavigationListener:i}=(0,l.cL)(),[c,d]=a.useState(new URL("/"+y+e,window.location.origin)),{urlStateParam:u,removeQueryParam:p}=(0,s._6)();return(0,a.useEffect)(()=>{let e=()=>{};return r&&(e=i(r)),()=>{e()}},[]),u.componentName&&p(),(0,o.tZ)(g,{getPath:()=>c.pathname,basePath:y,startPath:e,getQueryString:()=>c.search,internalNavigate:e=>{e&&d(e)},preservedParams:t,urlStateParam:u,children:n})};function x({children:e}){let t=(0,n.t)(),r=null;return a.Children.forEach(e,e=>{if(r||!(e&&a.isValidElement(e)&&"object"==typeof e&&e.type===h))return;let{index:n,path:o}=e.props;(!n&&!o||t.matches(o,n))&&(r=e)}),(0,o.tZ)(o.HY,{children:r})}},6560:function(e,t,r){r.r(t),r.d(t,{StyleCacheProvider:()=>s});var n=r(9109),o=r(1025),a=r(3571),i=r(9144);let l=document.querySelector("style#cl-style-insertion-point"),s=e=>{let t=(0,i.useMemo)(()=>{let t=(0,o.Z)({key:"cl-internal",prepend:!e.cssLayerName&&!l,insertionPoint:l||void 0,nonce:e.nonce});if(e.cssLayerName){let r=t.insert.bind(t);t.insert=(t,n,o,a)=>{if(n&&"string"==typeof n.styles&&!n.styles.startsWith("@layer")){let i={...n};return i.styles=`@layer ${e.cssLayerName} {${n.styles}}`,r(t,i,o,a)}return r(t,n,o,a)}}return t},[e.nonce,e.cssLayerName]);return(0,n.tZ)(a.C,{value:t,children:e.children})}},7504:function(e,t,r){r.d(t,{y:()=>a});let n=e=>({"&::-moz-focus-inner":{border:"0"},WebkitTapHighlightColor:"transparent",boxShadow:e.shadows.$focusRing.replace("{{color}}",e.colors.$neutralAlpha200),transitionProperty:e.transitionProperty.$common,transitionTimingFunction:e.transitionTiming.$common,transitionDuration:e.transitionDuration.$focusRing}),o=e=>({"::-webkit-scrollbar":{background:"transparent",width:"8px",height:"8px"},"::-webkit-scrollbar-thumb":{background:e.colors.$neutralAlpha500},"::-webkit-scrollbar-track":{background:"transparent"}}),a={textVariants:e=>{let t={fontFamily:"inherit",letterSpacing:e.letterSpacings.$normal},r={...t,fontWeight:e.fontWeights.$semibold,fontSize:e.fontSizes.$xl,lineHeight:e.lineHeights.$extraSmall},n={...t,fontWeight:e.fontWeights.$bold,fontSize:e.fontSizes.$lg,lineHeight:e.lineHeights.$medium},o={...t,fontWeight:e.fontWeights.$bold,fontSize:e.fontSizes.$md,lineHeight:e.lineHeights.$small},a={...t,fontWeight:e.fontWeights.$medium,fontSize:e.fontSizes.$md,lineHeight:e.lineHeights.$small},i={...t,fontWeight:e.fontWeights.$normal,fontSize:e.fontSizes.$md,lineHeight:e.lineHeights.$small},l={...t,fontWeight:e.fontWeights.$medium,fontSize:e.fontSizes.$xs,lineHeight:e.lineHeights.$large};return{h1:r,h2:n,h3:o,subtitle:a,body:i,caption:l,buttonLarge:{...t,fontWeight:e.fontWeights.$medium,fontSize:e.fontSizes.$md,lineHeight:e.lineHeights.$small,fontFamily:e.fonts.$buttons},buttonSmall:{...t,fontWeight:e.fontWeights.$medium,fontSize:e.fontSizes.$sm,lineHeight:e.lineHeights.$extraSmall,fontFamily:e.fonts.$buttons}}},borderVariants:(e,t)=>{let r=t?.hasError?e.colors.$dangerAlpha500:e.colors.$neutralAlpha300,n=e.shadows.$input.replace("{{color}}",t?.hasError?e.colors.$dangerAlpha200:e.colors.$neutralAlpha150),o=t?.focusRing===!1?{}:{"&:focus":{borderColor:r,WebkitTapHighlightColor:"transparent",boxShadow:[n,e.shadows.$focusRing.replace("{{color}}",t?.hasError?e.colors.$dangerAlpha200:e.colors.$neutralAlpha150)].toString()}};return{normal:{borderRadius:e.radii.$md,borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:t?.hasError?e.colors.$dangerAlpha500:e.colors.$neutralAlpha150,boxShadow:e.shadows.$input.replace("{{color}}",t?.hasError?e.colors.$neutralAlpha150:e.colors.$neutralAlpha100),transitionProperty:e.transitionProperty.$common,transitionTimingFunction:e.transitionTiming.$common,transitionDuration:e.transitionDuration.$focusRing,"&:hover":{WebkitTapHighlightColor:"transparent",borderColor:r,boxShadow:n},...o}}},focusRingStyles:n,focusRing:e=>({"&:focus":{...n(e)}}),disabled:e=>({"&:disabled,&[data-disabled]":{cursor:"not-allowed",pointerEvents:"none",opacity:e.opacity.$disabled}}),borderColor:(e,t)=>({borderColor:t?.hasError?e.colors.$dangerAlpha500:e.colors.$neutralAlpha150}),centeredFlex:(e="flex")=>({display:e,justifyContent:"center",alignItems:"center"}),maxHeightScroller:e=>({height:"100%",overflowY:"auto",...o(e)}),unstyledScrollbar:o,mergedColorsBackground:(e,t)=>`linear-gradient(${t},${t}), linear-gradient(${e}, ${e})`,visuallyHidden:()=>({clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",overflow:"hidden",position:"absolute",whiteSpace:"nowrap",width:"1px"})}},1201:function(e,t,r){r.r(t),r.d(t,{createVariants:()=>c,InternalThemeProvider:()=>i,createCssVariables:()=>v,mqu:()=>z,animations:()=>B,common:()=>y.y});var n=r(9109),o=r(3571);r(9144);var a=r(9541);let i=e=>{let{parsedInternalTheme:t}=(0,a.useAppearance)();return(0,n.tZ)(o.a,{theme:t,children:e.children})};var l=r(5100),s=r(7623);let c=e=>{let t=(0,s.dC)(),r=Object.keys(e(t,t).variants||{});return{applyVariants:(t={})=>r=>{let{base:n,variants:o={},compoundVariants:a=[],defaultVariants:i={}}=e(r,t),l=g(o,t,i),s={};return u(s,n),p(s,l,o),m(s,l,a),h(s),s},filterProps:e=>d(e,r)}},d=(e,t)=>{let r={...e};for(let e of t)delete r[e];return r},u=(e,t)=>{t&&"object"==typeof t&&Object.assign(e,t)},p=(e,t,r)=>{for(let n in t)(0,l.EB)(r[n][t[n]],e)},m=(e,t,r)=>{for(let n of r)f(n,t)&&(0,l.EB)(n.styles,e)},h=e=>{for(let t in e)t.startsWith("var(")&&(e[t.slice(4,-1)]=e[t],delete e[t])},g=(e,t,r)=>{let n={};for(let o in e)o in t?n[o]=t[o]:o in r&&(n[o]=r[o]);return n},f=({condition:e},t)=>{for(let r in e)if(e[r]!==t[r])return!1;return!0};var b=r(2041);let v=(...e)=>(0,b.s)(e.map(e=>[e,`var(--${e})`]));var y=r(7504),w=r(5582);let x=(0,w.F4)`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }`,S=(0,w.F4)`
  0% {
    opacity: 0;
    transform: scaleY(1) translateY(-6px);
  }
  100% {
    opacity: 1;
    transform: scaleY(1)  translateY(0px);
  }
`,$=(0,w.F4)`
  0% {
    opacity: 0;
    transform: translateY(0.5rem);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
`,C=(0,w.F4)`
  0% { opacity: 0; }
  100% { opacity: 1; }
`,_=(0,w.F4)`
  0% { opacity: 1; }
  100% { opacity: 0; }
`,k=(0,w.F4)`
  0% {
    opacity: 0;
    transform: translateY(-5px);
    max-height: 0;
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
    max-height: 6rem;
  }
`,P=(0,w.F4)`
  0% {
    opacity: 0;
    transform: translateY(-5px);
    max-height: 0;
  }
  50% {
    opacity: 0;
    transform: translateY(-5px);
    max-height: 0;
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
    max-height: 6rem;
  }
`,I=(0,w.F4)`
  0% {
    opacity: 0;
    transform: translateY(5px) scale(.5);
  }

  50% {
    opacity: 1;
    transform: translateY(0px) scale(1.2);
  }

  100% {
    opacity: 1;
    transform: translateY(0px) scale(1);
  }
`,A=(0,w.F4)`
  0% {
    opacity:1;
    transform: translateY(0px);
    max-height: 6rem;
    visibility: visible;
  }
  100% {
    opacity: 0;
    transform: translateY(5px);
    max-height: 0;
    visibility: visible;
  }
`,T=(0,w.F4)`
  0% {opacity: 0;max-height: 0;}
  100% {opacity: 1;max-height: 3rem;}
`,R=(0,w.F4)`
  0% {opacity: 0;max-height: 0;}
  100% {opacity: 1;max-height: 8rem;}
`,B={spinning:x,dropdownSlideInScaleAndFade:S,modalSlideAndFade:$,fadeIn:C,fadeOut:_,textInSmall:T,textInBig:R,blockBigIn:(0,w.F4)`
  0% {opacity: 0;max-height: 0;}
  99% {opacity: 1;max-height: 10rem;}
  100% {opacity: 1;max-height: unset;}
`,expandIn:e=>(0,w.F4)`
  0% {opacity: 0;max-height: 0;}
  99% {opacity: 1;max-height: ${e};}
  100% {opacity: 1;max-height: unset;}
`,navbarSlideIn:(0,w.F4)`
  0% {opacity: 0; transform: translateX(-100%);}
  100% {opacity: 1; transform: translateX(0);}
`,inAnimation:k,inDelayAnimation:P,outAnimation:A,notificationAnimation:I},Z=Object.freeze({xs:"21em",sm:"30em",md:"48em",lg:"62em",xl:"80em","2xl":"96em"}),z={ios:"@supports (-webkit-touch-callout: none)",...(0,b.s)(Object.entries(Z).map(([e,t])=>[e,`@media (max-width: ${t})`]))}},4929:function(e,t,r){r.d(t,{I:()=>c,Q:()=>d});var n=r(6747),o=r(2041);let a=["25","50","100","150","200","300","400"].reverse(),i=["600","700","750","800","850","900","950"],l=[...[...a].reverse(),"500",...i];function s(){return{25:void 0,50:void 0,100:void 0,150:void 0,200:void 0,300:void 0,400:void 0,500:void 0,600:void 0,700:void 0,750:void 0,800:void 0,850:void 0,900:void 0,950:void 0}}let c=(e,t)=>u(e,t,b),d=(e,t)=>p(e,t,f),u=(e,t,r)=>{if(e){if("object"==typeof e&&!l.every(t=>t in e))throw Error("You need to provide all the following shades: "+l.join(", "));return"object"==typeof e?h(Object.keys(e).reduce((t,r)=>(t[r]=n.O.toHslaColor(e[r]),t),s()),t):h(r(n.O.toHslaColor(e)),t)}},p=(e,t,r)=>{if(!e)return;if("object"==typeof e&&!e["500"])throw Error("You need to provide at least the 500 shade");let n=g(e);return h(m(r(n["500"]),n),t)},m=(e,t)=>(0,o.s)(Object.entries(t).map(([t,r])=>[t,r||e[t]])),h=(e,t)=>{let r={};for(let o in e)e[o]&&(r[t+o]=n.O.toHslaString(e[o]));return r},g=e=>{let t="string"==typeof e?{500:e}:e,r=Object.keys(s()).map(e=>[e,t[e]?n.O.toHslaColor(t[e]):void 0]);return(0,o.s)(r)},f=e=>{let t=s();t["500"]=e;let r=(97-e.l)/a.length,o=(e.l-12)/i.length;return a.forEach((o,a)=>t[o]=n.O.changeHslaLightness(e,(a+1)*r)),i.map((r,a)=>t[r]=n.O.changeHslaLightness(e,-((a+1)*o*1))),t},b=e=>{let t=s(),r=n.O.setHslaAlpha(e,0),o=[.02,.03,.07,.11,.15,.28,.41,.53,.62,.73,.78,.81,.84,.87,.92];return Object.keys(t).forEach((e,a)=>t[e]=n.O.setHslaAlpha(r,o[a])),t}},6747:function(e,t,r){r.d(t,{O:()=>k});let n=/^#([a-f0-9]{3,4})$/i,o=/^#([a-f0-9]{6})([a-f0-9]{2})?$/i,a=/^rgba?\(\s*([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/,i=/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/,l=/^hsla?\(\s*([+-]?(?:\d{0,3}\.)?\d+)(?:deg)?\s*,?\s*([+-]?[\d\.]+)%\s*,?\s*([+-]?[\d\.]+)%\s*(?:[,|\/]\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/,s=/^hwb\(\s*([+-]?\d{0,3}(?:\.\d+)?)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/,c={black:[0,0,0,1],blue:[0,0,255,1],red:[255,0,0,1],green:[0,128,0,1],grey:[128,128,128,1],gray:[128,128,128,1],white:[255,255,255,1],yellow:[255,255,0,1],transparent:[0,0,0,0]},d=(e,t,r)=>Math.min(Math.max(t,e),r),u=e=>{let t,r,l;if(!e)return null;let s=[0,0,0,1];if(t=e.match(o)){for(r=0,l=t[2],t=t[1];r<3;r++){let e=2*r;s[r]=parseInt(t.slice(e,e+2),16)}l&&(s[3]=parseInt(l,16)/255)}else if(t=e.match(n)){for(r=0,l=(t=t[1])[3];r<3;r++)s[r]=parseInt(t[r]+t[r],16);l&&(s[3]=parseInt(l+l,16)/255)}else if(t=e.match(a)){for(r=0;r<3;r++)s[r]=parseInt(t[r+1],0);t[4]&&(t[5]?s[3]=.01*parseFloat(t[4]):s[3]=parseFloat(t[4]))}else if(t=e.match(i)){for(r=0;r<3;r++)s[r]=Math.round(2.55*parseFloat(t[r+1]));t[4]&&(t[5]?s[3]=.01*parseFloat(t[4]):s[3]=parseFloat(t[4]))}else if(e in c)return c[e];else return null;for(r=0;r<3;r++)s[r]=d(s[r],0,255);return s[3]=d(s[3],0,1),s},p=e=>{if(!e)return null;let t=e.match(l);return t?h(t):null},m=function(e){if(!e)return null;let t=e.match(s);return t?h(t):null},h=e=>{let t=parseFloat(e[4]),r=(parseFloat(e[1])%360+360)%360,n=d(parseFloat(e[2]),0,100);return[r,n,d(parseFloat(e[3]),0,100),d(isNaN(t)?1:t,0,1)]},g=e=>({h:e[0],s:e[1],l:e[2],a:e[3]??1}),f=e=>{let t,r,n,o;let a=e[0]/360,i=e[1]/100,l=e[2]/100,s=e[3]??1,c=i+l;c>1&&(i/=c,l/=c);let d=Math.floor(6*a),u=1-l;t=6*a-d,(1&d)!=0&&(t=1-t);let p=i+t*(u-i);switch(d){default:case 6:case 0:r=u,n=p,o=i;break;case 1:r=p,n=u,o=i;break;case 2:r=i,n=u,o=p;break;case 3:r=i,n=p,o=u;break;case 4:r=p,n=i,o=u;break;case 5:r=u,n=i,o=p}return[255*r,255*n,255*o,s]},b=e=>{let t;let r=e[0]/255,n=e[1]/255,o=e[2]/255,a=e[3]??1,i=Math.min(r,n,o),l=Math.max(r,n,o),s=l-i;l===i?t=0:r===l?t=(n-o)/s:n===l?t=2+(o-r)/s:o===l&&(t=4+(r-n)/s),(t=Math.min(60*t,360))<0&&(t+=360);let c=(i+l)/2;return{h:Math.floor(t),s:Math.floor(100*(l===i?0:c<=.5?s/(l+i):s/(2-l-i))),l:Math.floor(100*c),a}},v=e=>b(f(e)),y=({h:e,s:t,l:r,a:n})=>`hsla(${e}, ${t}%, ${r}%, ${n??1})`,w=e=>{let t;let r=e.substr(0,3).toLowerCase();if(!(t="hsl"===r?{model:"hsl",value:p(e)}:"hwb"===r?{model:"hwb",value:m(e)}:{model:"rgb",value:u(e)})||!t.value)throw Error(`Clerk: "${e}" cannot be used as a color within 'variables'. You can pass one of:
- any valid hsl or hsla color
- any valid rgb or rgba color
- any valid hex color
- any valid hwb color
- ${Object.keys(c).join(", ")}
`);return t},x=e=>{let{model:t,value:r}=w(e);switch(t){case"hsl":return g(r);case"hwb":return v(r);case"rgb":return b(r)}},S=e=>"string"==typeof e?y(x(e)):y(e),$=(e,t)=>({...e,l:e.l+t}),C=(e,t)=>({...e,a:t}),_=(e,t)=>({...e,a:e.a?e.a-t:void 0}),k={toHslaColor:x,toHslaString:S,adjustForLightness:(e,t=5)=>{if(!e)return;let r=k.toHslaColor(e);return r?(100===r.l?r.l=95:r.l=Math.min(r.l+2*t,100),k.toHslaString(r)):e},changeHslaLightness:$,setHslaAlpha:C,lighten:(e,t=0)=>{if(!e)return;let r=x(e);return S($(r,r.l*t))},makeTransparent:(e,t=0)=>{if(!e||""===e.toString())return;let r=x(e);return S(_(r,(r.a??1)*t))},makeSolid:e=>{if(e)return S({...x(e),a:1})},setAlpha:(e,t)=>e.toString()?S(C(x(e),t)):e}},577:function(e,t,r){r.d(t,{Q0:()=>u,U6:()=>p,b8:()=>d,sZ:()=>c});let n=e=>e.reduce((e,t,r)=>(e[t]=r,e),{}),o=n(["passkey","password","email_link","email_code","phone_code"]),a=n(["email_link","email_code","phone_code","passkey","password"]),i=n(["email_link","email_code","phone_code","passkey","password"]),l=n(["totp","phone_code","backup_code"]),s=e=>(t,r)=>{let n=e[t.strategy],o=e[r.strategy];return void 0===n||void 0===o?0:n-o},c=s(o),d=s(a),u=s(l),p=s(i)},9082:function(e,t,r){r.d(t,{p:()=>n});function n(e,t="long",r="en-US"){let o={month:"short"===t?"short":"long",..."monthyear"!==t&&{day:"numeric"},..."short"!==t&&{year:"numeric"}};return new Intl.DateTimeFormat(r,o).format(e)}},2041:function(e,t,r){r.d(t,{s:()=>n});let n=e=>[...e].reduce((e,[t,r])=>(e[t]=r,e),{})},7623:function(e,t,r){r.d(t,{uz:()=>h,BG:()=>es,Oi:()=>V,sn:()=>ec,Yp:()=>P.Yp,fq:()=>eE,jR:()=>S,y3:()=>v,Nr:()=>o,JD:()=>j,p6:()=>eV.p,zQ:()=>Z,s2:()=>u,QD:()=>eC.Q,N2:()=>K,qT:()=>eo,yy:()=>f,WR:()=>O,Qg:()=>N,Q0:()=>s.Q0,$M:()=>Q,un:()=>g,w6:()=>E,HT:()=>C,Sj:()=>$,S3:()=>B,lq:()=>X,Yb:()=>_,L_:()=>y,U6:()=>s.U6,ni:()=>P.ni,dC:()=>a,sq:()=>n.s,Ii:()=>eC.I,Z_:()=>x,LD:()=>eW.L,tc:()=>eU,z$:()=>J,_v:()=>c,qi:()=>Y,Ai:()=>k,O9:()=>l.O,c8:()=>e_,GM:()=>G,Ht:()=>eO,YV:()=>ej,k_:()=>i.k_});var n=r(2041);let o=()=>e=>e,a=()=>{let e=new Proxy({},{get:(t,r)=>r===Symbol.toPrimitive?()=>"":r in Object.getPrototypeOf("")?e=>Object.getPrototypeOf("")[r].call("",e):r===Symbol.toPrimitive?()=>"":e});return e};var i=r(8545),l=r(6747),s=r(577);let c=e=>new Promise(t=>setTimeout(t,e)),d=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i,u=()=>"undefined"!=typeof window&&void 0!==window.document&&!!(window.matchMedia("only screen and (max-width: 760px)").matches||d.test(navigator.userAgent)||"ontouchstart"in document.documentElement&&navigator.userAgent.match(/Mobi/));var p=r(8191);let m={};function h(e,t="us"){if(m[e=e||t])return m[e];let r=String.fromCodePoint(...[...e.toUpperCase()].map(e=>e.codePointAt(0)+127397));return m[e]=r,r}function g(e,t,r){var n;if(!e||!t)return e;let o=[...f(e)].slice(0,15-((n=(n=r)||"1").includes("+")?n:"+"+n).length);if(o.length<=3)return o.join("");let a="";for(let e=0;o.length>0;e++)e>t.length-1?a+=o.shift():a+="."===t[e]?o.shift():t[e];return a}function f(e){return(e||"").replace(/[^\d]/g,"")}function b(e,t){if(!e||!p.h5.get(e)||!t)return!1;let r=t[0],n=t.substring(1,4);return r===p.h5.get(e)?.code&&t.length-1===((p.h5.get(e)?.pattern||"").match(/\./g)||[]).length&&p.ug[e].has(n)}function v(e){let t=f(e),r=function(e,t="us"){let r=f(e);return!r||r.length<4?t:r.startsWith("1")&&b("us",r)?"us":r.startsWith("1")&&b("ca",r)?"ca":x(r).country.iso}(t),n=p.h5.get(r)?.pattern||"",o=p.h5.get(r)?.code||"",a=t.slice(o.length),i=`+${o} ${g(a,n,o)}`;return{iso:r,pattern:n,code:o,number:a,formattedNumberWithCode:i}}function y(e){let t=v(e);return`+${t.code} ${g(t.number,t.pattern,t.code)}`}let w=(e,t)=>t.priority-e.priority;function x(e){let t=f(e),r=[];for(let e of[4,3,2,1]){let n=t.substring(0,e),o=p.nX.get(n)||[];o.length&&r.push(...o)}let n=p.h5.get("us"),o=r.sort(w)[0]||n;return{number:t.slice(o?.code.length||0),country:o}}function S(e,t){if(!t)return null;let{country:r}=x(e);return t[r.iso.toUpperCase()]||null}let $=e=>e&&e.includes("**"),C=e=>!e||e.includes("@")||$(e)||e.match(/[a-zA-Z]/)?e:y(e),_=e=>(Object.keys(e).forEach(t=>void 0===e[t]&&delete e[t]),e),k=(e,t)=>{let r=(t||"").split("."),n=e;for(let e=0;e<r.length;e++)if(void 0===(n=n[r[e]]))return;return n};var P=r(6788),I=r(5027),A=r(3139);let T=e=>e[0];function R(e){return(e||[]).reduce((e,t)=>(t.meta?.paramName?e.fieldErrors.push(t):e.globalErrors.push(t),e),{fieldErrors:[],globalErrors:[]})}let B=(e,t,r)=>{if(!(0,A.sZ)(e))throw e;return(0,A.ZC)(e)?z(e,t,r):(0,A.kD)(e)?D(e,t,r):(0,A.uX)(e)&&"reverification_cancelled"===e.code?void 0:(0,A.uX)(e)?L(e,t,r):void 0};function Z(e){if(!(0,A.kD)(e))return;let{fieldErrors:t}=R(e.errors);if(t.length)return t[0]}let z=(e,t,r)=>r?.(e.message),D=(e,t,r)=>{var n,o;if(!(0,A.kD)(e))return;let{fieldErrors:a,globalErrors:i}=R(e.errors);if(n=t,!(o=a)||o.length<1||n.forEach(e=>{let t=e?.buildErrorMessage;t||(t=T);let r=o.filter(t=>t.meta?.paramName===e.id||(0,I.TD)(t.meta?.paramName)===e.id),n=t(r);r.length&&n?e.setError(n):e.clearFeedback()}),r){r(void 0);let e=i[0];e&&r(e)}},L=(e,t,r)=>{(0,A.uX)(e)&&r&&(r(void 0),e&&r(e))},E=(e,t)=>Array.from({length:t-e+1},(t,r)=>e+r);var F=r(9144);function O(e){return F.Children.toArray(e).filter(e=>F.isValidElement(e))}var M=r(5059);let U={basic_member:(0,M.u)("membershipRole__basicMember"),guest_member:(0,M.u)("membershipRole__guestMember"),admin:(0,M.u)("membershipRole__admin")},j=e=>{if(e)return U[e]},V=e=>{if(e)return(0,M.u)(`roles.${e}`)};var W=r(8387),H=r(1085);let N=e=>{let t=(0,W.lY)({date:e||new Date,relativeTo:new Date});if(!t)return"";switch(t.relativeDateCase){case"previous6Days":return(0,H.u1)("dates.previous6Days",{date:t.date});case"lastDay":return(0,H.u1)("dates.lastDay",{date:t.date});case"sameDay":return(0,H.u1)("dates.sameDay",{date:t.date});case"nextDay":return(0,H.u1)("dates.nextDay",{date:t.date});case"next6Days":return(0,H.u1)("dates.next6Days",{date:t.date});default:return(0,H.u1)("dates.numeric",{date:t.date})}};function K(e,t){if(t&&"mounted"!==e)return t?.target?.closest("[data-clerk-profile-scroll-box-root]")}let X=(...e)=>t=>{for(let r of e)r&&(r.current=t)},Y=e=>e.trim().toLowerCase().replace(/[^a-z0-9]+/g,"-"),q=e=>({form_password_length_too_long:["unstable__errors.passwordComplexity.maximumLength","length",e.max_length],form_password_length_too_short:["unstable__errors.passwordComplexity.minimumLength","length",e.min_length],form_password_no_uppercase:"unstable__errors.passwordComplexity.requireUppercase",form_password_no_lowercase:"unstable__errors.passwordComplexity.requireLowercase",form_password_no_number:"unstable__errors.passwordComplexity.requireNumbers",form_password_no_special_char:"unstable__errors.passwordComplexity.requireSpecialCharacter"}),G=(e,t)=>{if(!t)return e[0].longMessage;let{t:r,locale:n,passwordSettings:o}=t;if(e?.[0]?.code==="form_password_size_in_bytes_exceeded"||e?.[0]?.code==="form_password_pwned")return`${r((0,M.u)(`unstable__errors.${e?.[0]?.code}`))||e?.[0]?.message}`;if(e?.[0]?.code==="form_password_not_strong_enough"){let t=e[0].meta?.zxcvbn?.suggestions?.map(e=>r(M.u(`unstable__errors.zxcvbn.suggestions.${e.code}`))).join(" ");return`${r((0,M.u)("unstable__errors.zxcvbn.notEnough"))} ${t}`}let a=e.filter(e=>"form_password_length_too_short"===e.code),i=J((a.length?a:e).map(e=>{let t=q(o)[e.code];if(Array.isArray(t)){let[e,n,o]=t;return r((0,M.u)(e,{[n]:o}))}return r((0,M.u)(t))}),n);return Q(`${r((0,M.u)("unstable__errors.passwordComplexity.sentencePrefix"))} ${i}`)},Q=e=>e?e.endsWith(".")?e:`${e}.`:"",J=(e,t)=>{let r;return(0,i.k_)(t)?new Intl.ListFormat(t,{style:"long",type:"conjunction"}).format(e):e.join(", ")};var ee=r(9109),et=r(6917),er=r(1673),en=r(4174);let eo=({mount:e,unmount:t,...r})=>{let n=(0,F.useRef)(null);return(0,F.useEffect)(()=>{let r;return n.current&&(r=n.current,e(n.current)),()=>{t(r)}},[n.current]),(0,ee.tZ)("div",{ref:n,...r})};var ea=r(5100);let ei=e=>(0,ea.vf)()||e.sdkMetadata?.environment==="development",el=e=>{if(!e)throw Error("Clerk: URL is required for custom links");return(0,et.jv)(e)?e:"/"===e.charAt(0)?e:`/${e}`},es=(e,t,r)=>ed({customPages:e,getDefaultRoutes:eS,setFirstPathToRoot:ep,excludedPathsFromDuplicateWarning:[]},t,r),ec=(e,t,r)=>ed({customPages:e,getDefaultRoutes:e$,setFirstPathToRoot:em,excludedPathsFromDuplicateWarning:[]},t,r,!0),ed=({customPages:e,getDefaultRoutes:t,setFirstPathToRoot:r,excludedPathsFromDuplicateWarning:n},o,a,i)=>{let{INITIAL_ROUTES:l,pageToRootNavbarRouteMap:s,validReorderItemLabels:c}=t({commerce:!(0,et.KR)(o,a)&&(i?(0,et.le)(o,a):(0,et.Yh)(o,a)),apiKeys:!(0,et.e)(o,a)});ei(o)&&eh(e,c);let{allRoutes:d,contents:u}=eu({customPages:e.filter(e=>!!ef(e,c)||(ei(o)&&console.error("Clerk: Invalid custom page data: ",e),!1)),defaultRoutes:l});ex(d);let p=r(d);return ei(o)&&eg(p,n),{routes:p,contents:u,pageToRootNavbarRouteMap:s}},eu=({customPages:e,defaultRoutes:t})=>{let r=t.map(e=>e),n=[],o=e.map((e,o)=>{if(ev(e))return{name:e.label,id:`custom-page-${o}`,icon:t=>(0,ee.tZ)(eo,{mount:e.mountIcon,unmount:e.unmountIcon,...t}),path:el(e.url),external:!0};if(eb(e)){let t=ew(e.url);return n.push({url:t,mount:e.mount,unmount:e.unmount}),{name:e.label,id:`custom-page-${o}`,icon:t=>(0,ee.tZ)(eo,{mount:e.mountIcon,unmount:e.unmountIcon,...t}),path:t}}let a=t.find(t=>t.id===e.label);return r=r.filter(({id:t})=>t!==e.label),{...a}});return{allRoutes:[...r,...o],contents:n}},ep=e=>e.map((e,t)=>0===t?{...e,path:"/"}:e),em=e=>e.map((e,t)=>0===t?{...e,path:"/"}:e),eh=(e,t)=>{e.filter(e=>ey(e,t)).reduce((e,t)=>(e.includes(t.label)&&console.error(`Clerk: The "${t.label}" item is used more than once when reordering pages. This may cause unexpected behavior.`),[...e,t.label]),[])},eg=(e,t)=>{let r=e.filter(({external:e,path:r})=>!e&&t.every(e=>e!==r)).map(({path:e})=>e);r.filter((e,t)=>r.indexOf(e)!==t).forEach(e=>{console.error(`Clerk: Duplicate path "${e}" found in custom pages. This may cause unexpected behavior.`)})},ef=(e,t)=>eb(e)||ev(e)||ey(e,t),eb=e=>!!e.url&&!!e.label&&!!e.mount&&!!e.unmount&&!!e.mountIcon&&!!e.unmountIcon,ev=e=>!!e.url&&!!e.label&&!e.mount&&!e.unmount&&!!e.mountIcon&&!!e.unmountIcon,ey=(e,t)=>!e.url&&!e.mount&&!e.unmount&&!e.mountIcon&&!e.unmountIcon&&t.some(t=>t===e.label),ew=e=>{if(!e)throw Error("Clerk: URL is required for custom pages");if((0,et.jv)(e))throw Error("Clerk: Absolute URLs are not supported for custom pages");return"/"===e.charAt(0)&&e.length>1?e.substring(1):e},ex=e=>{if(e[0].external)throw Error("Clerk: The first route cannot be a custom external link component")},eS=({commerce:e,apiKeys:t})=>{let r=[{name:(0,H.u1)("userProfile.navbar.account"),id:er.xM.ACCOUNT,icon:en.n5,path:"account"},{name:(0,H.u1)("userProfile.navbar.security"),id:er.xM.SECURITY,icon:en.qy,path:"security"}];e&&r.push({name:(0,H.u1)("userProfile.navbar.billing"),id:er.xM.BILLING,icon:en.aB,path:"billing"}),t&&r.push({name:(0,H.u1)("userProfile.navbar.apiKeys"),id:er.xM.API_KEYS,icon:en.EK,path:"api-keys"});let n={profile:r.find(e=>e.id===er.xM.ACCOUNT),"email-address":r.find(e=>e.id===er.xM.ACCOUNT),"phone-number":r.find(e=>e.id===er.xM.ACCOUNT),"connected-account":r.find(e=>e.id===er.xM.ACCOUNT),"web3-wallet":r.find(e=>e.id===er.xM.ACCOUNT),username:r.find(e=>e.id===er.xM.ACCOUNT),"multi-factor":r.find(e=>e.id===er.xM.SECURITY),password:r.find(e=>e.id===er.xM.SECURITY)},o=r.map(e=>e.id);return{INITIAL_ROUTES:r,pageToRootNavbarRouteMap:n,validReorderItemLabels:o}},e$=({commerce:e,apiKeys:t})=>{let r=[{name:(0,H.u1)("organizationProfile.navbar.general"),id:er.bm.GENERAL,icon:en.cp,path:"organization-general"},{name:(0,H.u1)("organizationProfile.navbar.members"),id:er.bm.MEMBERS,icon:en.Q,path:"organization-members"}];e&&r.push({name:(0,H.u1)("organizationProfile.navbar.billing"),id:er.xM.BILLING,icon:en.aB,path:"organization-billing"}),t&&r.push({name:(0,H.u1)("organizationProfile.navbar.apiKeys"),id:er.bm.API_KEYS,icon:en.EK,path:"organization-api-keys"});let n={"invite-members":r.find(e=>e.id===er.bm.MEMBERS),domain:r.find(e=>e.id===er.bm.GENERAL),profile:r.find(e=>e.id===er.bm.GENERAL),leave:r.find(e=>e.id===er.bm.GENERAL),delete:r.find(e=>e.id===er.bm.GENERAL)},o=r.map(e=>e.id);return{INITIAL_ROUTES:r,pageToRootNavbarRouteMap:n,validReorderItemLabels:o}};var eC=r(4929);let e_=(e,t)=>ek({customMenuItems:e,getDefaultMenuItems:eI},t),ek=({customMenuItems:e,getDefaultMenuItems:t},r)=>{let{INITIAL_MENU_ITEMS:n,validReorderItemLabels:o}=t(),a=e.filter(e=>!!eA(e,o)||(ei(r)&&console.error("Clerk: Invalid custom menu item:",e),!1));ei(r)&&(ez(e,o),eD(a));let{menuItems:i}=eP({customMenuItems:a,defaultMenuItems:n});return i},eP=({customMenuItems:e,defaultMenuItems:t})=>{let r=t.map(e=>e),n=[...e.map((e,n)=>{if(eB(e))return{name:e.label,id:`custom-menutItem-${n}`,icon:t=>(0,ee.tZ)(eo,{mount:e.mountIcon,unmount:e.unmountIcon,...t}),path:el(e.href)};if(eT(e))return{name:e.label,id:`custom-menutItem-${n}`,icon:t=>(0,ee.tZ)(eo,{mount:e.mountIcon,unmount:e.unmountIcon,...t}),onClick:e?.onClick};if(eR(e))return{name:e.label,id:`custom-menutItem-${n}`,icon:t=>(0,ee.tZ)(eo,{mount:e.mountIcon,unmount:e.unmountIcon,...t}),open:e.open};let o=t.find(t=>t.id===e.label);return r=r.filter(t=>t.id!==e.label),{...o}}),...r];return e.some(e=>e.label===er.Zb.MANAGE_ACCOUNT)||n.sort(e=>e.id===er.Zb.MANAGE_ACCOUNT?-1:0),{menuItems:n}},eI=()=>{let e=[{name:(0,H.u1)("userButton.action__manageAccount"),id:er.Zb.MANAGE_ACCOUNT,icon:en.tc},{name:(0,H.u1)("userButton.action__signOut"),id:er.Zb.SIGN_OUT,icon:en.lv}],t=e.map(e=>e.id);return{INITIAL_MENU_ITEMS:e,validReorderItemLabels:t}},eA=(e,t)=>(eB(e)||eT(e)||eR(e)||eZ(e,t))&&"string"==typeof e.label,eT=e=>!!e.label&&!!e.onClick&&!e.mount&&!e.unmount&&!!e.mountIcon&&!!e.unmountIcon,eR=e=>!!e.label&&!!e.open&&!e.mount&&!e.unmount&&!!e.mountIcon&&!!e.unmountIcon,eB=e=>!!e.href&&!!e.label&&!e.mount&&!e.unmount&&!!e.mountIcon&&!!e.unmountIcon,eZ=(e,t)=>!e.mount&&!e.unmount&&!e.mountIcon&&!e.unmountIcon&&t.some(t=>t===e.label),ez=(e,t)=>{e.filter(e=>eZ(e,t)).reduce((e,t)=>(e.includes(t.label)&&console.error(`Clerk: The "${t.label}" item is used more than once when reordering pages. This may cause unexpected behavior.`),[...e,t.label]),[])},eD=e=>{let t=e.map(e=>e.label),r=t.filter((e,r)=>t.indexOf(e)!==r);r.length>0&&console.warn(`Clerk: Duplicate custom menu item labels found: ${r.join(", ")}`)},eL="form_username_invalid_length",eE=(e,t)=>{let{t:r,usernameSettings:n}=t,o=e[0];return t&&o?.code===eL?r((0,H.u1)(`unstable__errors.${eL}`,{min_length:n.min_length,max_length:n.max_length})):o};var eF=r(3531);let eO=(e,t)=>(0,eF.kD)(e)&&e.errors[0].meta?.paramName==="identifier"&&"form_param_nil"===e.errors[0].code?B(new eF.w$("A Web3 Wallet extension cannot be found. Please install one to continue.",{code:"web3_missing_identifier"}),[],t):B(e,[],t),eM=[".lovable.app",".lovableproject.com",".webcontainer-api.io",".vusercontent.net",".v0.dev"];function eU(){return eM.some(e=>window.location.origin.endsWith(e))}function ej(e){if(!e||"string"!=typeof e)return console.warn("Invalid input: color string must be a non-empty string"),e||"";let t=e.trim();if(""===t)return console.warn("Invalid input: color string cannot be empty"),"";if(t.startsWith("#"))return/^#([0-9A-Fa-f]{3}|[0-9A-Fa-f]{4}|[0-9A-Fa-f]{6}|[0-9A-Fa-f]{8})$/.test(t)?5===t.length?"#"+t.slice(1,4):9===t.length?"#"+t.slice(1,7):t:(console.warn(`Invalid hex color format: ${e}`),t);if(/^rgba?\(/.test(t)){let r=t.match(/^rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/);if(r)return`rgb(${r[1]}, ${r[2]}, ${r[3]})`;let n=t.match(/^rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)$/);return n?`rgb(${n[1]}, ${n[2]}, ${n[3]})`:(console.warn(`Invalid RGB/RGBA format: ${e}`),t)}if(/^hsla?\(/.test(t)){let r=t.match(/^hsl\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*\)$/);if(r)return`hsl(${r[1]}, ${r[2]}%, ${r[3]}%)`;let n=t.match(/^hsla\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*,\s*([\d.]+)\s*\)$/);return n?`hsl(${n[1]}, ${n[2]}%, ${n[3]}%)`:(console.warn(`Invalid HSL/HSLA format: ${e}`),t)}return console.warn(`Unrecognized color format: ${e}`),t}var eV=r(9082),eW=r(5913)},8545:function(e,t,r){function n(e){return"ListFormat"in Intl&&function(e){if(!e)return!1;let t=Array.isArray(e)?e:[e];return Intl.ListFormat.supportedLocalesOf(t).length===t.length}(e)}function o(e,t){return"NumberFormat"in Intl&&function(e){if(!e)return!1;try{return Intl.NumberFormat.supportedLocalesOf(e).length>0}catch{return!1}}(t)?new Intl.NumberFormat(t,{notation:"compact"}).format(e):e.toString()}r.d(t,{$p:()=>o,k_:()=>n})},2156:function(e,t,r){r.d(t,{S:()=>o});var n=r(1085);function o(e){let t=new Date,r=new Date(e);if(isNaN(r.getTime()))return"";let o=Math.floor((t.getTime()-r.getTime())/1e3);if(o<60)return(0,n.u1)("apiKeys.dates.lastUsed__seconds",{seconds:o});let a=Math.floor(o/60);if(a<60)return(0,n.u1)("apiKeys.dates.lastUsed__minutes",{minutes:a});let i=Math.floor(a/60);if(i<24)return(0,n.u1)("apiKeys.dates.lastUsed__hours",{hours:i});let l=Math.floor(i/24);if(l<30)return(0,n.u1)("apiKeys.dates.lastUsed__days",{days:l});let s=Math.floor(l/30);if(s<12)return(0,n.u1)("apiKeys.dates.lastUsed__months",{months:s});let c=Math.floor(s/12);return(0,n.u1)("apiKeys.dates.lastUsed__years",{years:c})}},5913:function(e,t,r){r.d(t,{L:()=>n});function n(e,t=20,r=5){if(!e||e.length<=t)return e;if(t<=r+3)return"..."+e.slice(-r);let o=Array.from(e);return o.length<=t?e:o.slice(0,t-r-3).join("")+"..."+o.slice(-r).join("")}},6788:function(e,t,r){r.d(t,{V2:()=>d,Yp:()=>s,ni:()=>c});var n=r(9144),o=r(2464),a=r(1085);let i=e=>e.trim(),l=(e,t)=>{let r=e;for(let e=0;e<t.length;e++)r=t[e](r);return r},s=(e,t,r)=>{let o=r||{type:"text",label:"",isRequired:!1,placeholder:"",options:[],defaultChecked:!1},s=[];o.transformer&&s.push(o.transformer),"email"===o.type&&s.push(i);let{translateError:c,t:d}=(0,a.zJ)(),[u,p]=(0,n.useState)(l(t,s)),[m,h]=(0,n.useState)(!1),[g,f]=(0,n.useState)(o?.defaultChecked||!1),[b,v]=(0,n.useState)(!1),[y,w]=(0,n.useState)({message:"",type:"info"}),x=e=>{w({message:c(e),type:"error"})},{defaultChecked:S,validatePassword:$,buildErrorMessage:C,..._}=o,k={id:e,name:e,value:u,checked:g,setSuccess:e=>{w({message:e,type:"success"})},setError:x,onChange:e=>o?.type==="checkbox"?f(e.target.checked):p(l(e.target.value||"",s)),onBlur:()=>{h(!1)},onFocus:()=>{h(!0)},setWarning:e=>{w({message:c(e),type:"warning"})},feedback:y.message||d(o.infoText),feedbackType:y.type,setInfo:e=>{w({message:e,type:"info"})},clearFeedback:()=>{w({message:"",type:"info"})},hasPassedComplexity:b,setHasPassedComplexity:v,validatePassword:"password"===o.type?o.validatePassword:void 0,isFocused:m,..._};return{props:k,...k,buildErrorMessage:C,setError:x,setValue:e=>p(e||""),setChecked:e=>f(e)}},c=e=>{let t={};return e.forEach(e=>{t[e.id]="checkbox"!==e.type?e.value:e.checked}),t},d=e=>{let{feedback:t="",delayInMs:r=100,feedbackType:n="info",isFocused:a=!1}=e||{},i=!a&&["info","warning"].includes(n);return{debounced:(0,o.Nr)({feedback:i?"":t,feedbackType:i?"info":n},r)}}},2073:function(e,t,r){r.d(t,{L:()=>o});var n=r(4152);let o=({routing:e,path:t})=>t&&!e?{routing:"path",path:t}:"path"!==e&&t?(0,n.PQ)(e):{routing:e,path:t}},5758:function(e,t,r){r.d(t,{g:()=>a});var n=r(748),o=r(4152);async function a(){if(!window.google)try{await (0,n.v)("https://accounts.google.com/gsi/client",{defer:!0})}catch{(0,o.FI)("Google Identity Services")}return window.google}},6543:function(e,t,r){r.d(t,{H:()=>o,v:()=>n}),r(4310);var n=[{channel:"whatsapp",name:"WhatsApp"}],o=e=>e&&n.find(t=>t.channel===e)||null},6096:function(e,t,r){r.d(t,{WY:()=>n.WY});var n=r(6182);r(4310)},3799:function(e,t,r){r.d(t,{RY:()=>S,b5:()=>v,aF:()=>U,LB:()=>g,B3:()=>C,o8:()=>D,WZ:()=>H,Tt:()=>_,f0:()=>I,uH:()=>f,eW:()=>E,cL:()=>O,kP:()=>M,SE:()=>x,Gw:()=>F,I7:()=>V,St:()=>w,sX:()=>$});var n=r(1375),o=r(7989);r(7736);var a=r(8532),i="reverification-error",l=e=>({clerk_error:{type:"forbidden",reason:i,metadata:{reverification:e}}}),s=e=>e&&"object"==typeof e&&"clerk_error"in e&&e.clerk_error?.type==="forbidden"&&e.clerk_error?.reason===i,c=r(1353),d=r(4310),u=r(9144),p=r(9626),m=r(762),h=r(4662);function g(e,t){if(!e)throw"string"==typeof t?Error(t):Error(`${t.displayName} not found`)}var f=(e,t)=>{let{assertCtxFn:r=g}=t||{},n=u.createContext(void 0);return n.displayName=e,[n,()=>{let t=u.useContext(n);return r(t,`${e} not found`),t.value},()=>{let e=u.useContext(n);return e?e.value:{}}]},b={};(0,d.r2)(b,{useSWR:()=>p.default,useSWRInfinite:()=>m.ZP}),(0,d.yA)(b,p);var[v,y]=f("ClerkInstanceContext"),[w,x]=f("UserContext"),[S,$]=f("ClientContext"),[C,_]=f("SessionContext");u.createContext({});var[k,P]=f("OrganizationContext"),I=({children:e,organization:t,swrConfig:r})=>u.createElement(b.SWRConfig,{value:r},u.createElement(k.Provider,{value:{value:{organization:t}}},e));function A(e){if(!u.useContext(v)){if("function"==typeof e){e();return}throw Error(`${e} can only be used within the <ClerkProvider /> component.

Possible fixes:
1. Ensure that the <ClerkProvider /> is correctly wrapping your application where this component is used.
2. Check for multiple versions of the \`@clerk/shared\` package in your project. Use a tool like \`npm ls @clerk/shared\` to identify multiple versions, and update your dependencies to only rely on one.

Learn more: https://clerk.com/docs/components/clerk-provider`.trim())}}function T(e,t){let r=new Set(Object.keys(t)),n={};for(let t of Object.keys(e))r.has(t)||(n[t]=e[t]);return n}var R=(e,t)=>{let r="boolean"==typeof e&&e,n=(0,u.useRef)(r?t.initialPage:e?.initialPage??t.initialPage),o=(0,u.useRef)(r?t.pageSize:e?.pageSize??t.pageSize),a={};for(let n of Object.keys(t))a[n]=r?t[n]:e?.[n]??t[n];return{...a,initialPage:n.current,pageSize:o.current}},B={dedupingInterval:6e4,focusThrottleInterval:12e4},Z=(e,t,r,n)=>{let[o,a]=(0,u.useState)(e.initialPage??1),i=(0,u.useRef)(e.initialPage??1),l=(0,u.useRef)(e.pageSize??10),s=r.enabled??!0,c=r.infinite??!1,d=r.keepPreviousData??!1,h={...n,...e,initialPage:o,pageSize:l.current},{data:g,isValidating:f,isLoading:b,error:v,mutate:y}=(0,p.default)(!c&&t&&s?h:null,e=>{let r=T(e,n);return t?.(r)},{keepPreviousData:d,...B}),{data:w,isLoading:x,isValidating:S,error:$,size:C,setSize:_,mutate:k}=(0,m.ZP)(t=>c&&s?{...e,...n,initialPage:i.current+t,pageSize:l.current}:null,e=>{let r=T(e,n);return t?.(r)},B),P=(0,u.useMemo)(()=>c?C:o,[c,C,o]),I=(0,u.useCallback)(e=>{if(c){_(e);return}return a(e)},[_]),A=(0,u.useMemo)(()=>c?w?.map(e=>e?.data).flat()??[]:g?.data??[],[c,g,w]),R=(0,u.useMemo)(()=>c?w?.[w?.length-1]?.total_count||0:g?.total_count??0,[c,g,w]),Z=c?x:b,z=c?S:f,D=(c?$:v)??null,L=(0,u.useCallback)(()=>{I(e=>Math.max(0,e+1))},[I]),E=(0,u.useCallback)(()=>{I(e=>Math.max(0,e-1))},[I]),F=(i.current-1)*l.current,O=Math.ceil((R-F)/l.current),M=R-F*l.current>P*l.current,U=(P-1)*l.current>F*l.current,j=c?e=>k(e,{revalidate:!1}):e=>y(e,{revalidate:!1});return{data:A,count:R,error:D,isLoading:Z,isFetching:z,isError:!!D,page:P,pageCount:O,fetchPage:I,fetchNext:L,fetchPrevious:E,hasNextPage:M,hasPreviousPage:U,revalidate:c?()=>k():()=>y(),setData:j}},z={data:void 0,count:void 0,error:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0};function D(e){var t,r;let{domains:o,membershipRequests:a,memberships:i,invitations:l,subscriptions:s}=e||{};A("useOrganization");let{organization:c}=P(),d=_(),u=R(o,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1,enrollmentMode:void 0}),p=R(a,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),m=R(i,{initialPage:1,pageSize:10,role:void 0,keepPreviousData:!1,infinite:!1,query:void 0}),h=R(l,{initialPage:1,pageSize:10,status:["pending"],keepPreviousData:!1,infinite:!1}),g=R(s,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1}),f=y();f.telemetry?.record(n.J6("useOrganization"));let b=void 0===o?void 0:{initialPage:u.initialPage,pageSize:u.pageSize,enrollmentMode:u.enrollmentMode},v=void 0===a?void 0:{initialPage:p.initialPage,pageSize:p.pageSize,status:p.status},w=void 0===i?void 0:{initialPage:m.initialPage,pageSize:m.pageSize,role:m.role,query:m.query},x=void 0===l?void 0:{initialPage:h.initialPage,pageSize:h.pageSize,status:h.status},S=void 0===s?void 0:{initialPage:g.initialPage,pageSize:g.pageSize,orgId:c?.id},$=Z({...b},c?.getDomains,{keepPreviousData:u.keepPreviousData,infinite:u.infinite,enabled:!!b},{type:"domains",organizationId:c?.id}),C=Z({...v},c?.getMembershipRequests,{keepPreviousData:p.keepPreviousData,infinite:p.infinite,enabled:!!v},{type:"membershipRequests",organizationId:c?.id}),k=Z(w||{},c?.getMemberships,{keepPreviousData:m.keepPreviousData,infinite:m.infinite,enabled:!!w},{type:"members",organizationId:c?.id}),I=Z({...x},c?.getInvitations,{keepPreviousData:h.keepPreviousData,infinite:h.infinite,enabled:!!x},{type:"invitations",organizationId:c?.id}),T=Z({...S},c?.getSubscriptions,{keepPreviousData:g.keepPreviousData,infinite:g.infinite,enabled:!!S},{type:"subscriptions",organizationId:c?.id});return void 0===c?{isLoaded:!1,organization:void 0,membership:void 0,domains:z,membershipRequests:z,memberships:z,invitations:z,subscriptions:z}:null===c?{isLoaded:!0,organization:null,membership:null,domains:null,membershipRequests:null,memberships:null,invitations:null,subscriptions:null}:!f.loaded&&c?{isLoaded:!0,organization:c,membership:void 0,domains:z,membershipRequests:z,memberships:z,invitations:z,subscriptions:z}:{isLoaded:f.loaded,organization:c,membership:(t=d.user.organizationMemberships,r=c.id,t.find(e=>e.organization.id===r)),domains:$,membershipRequests:C,memberships:k,invitations:I,subscriptions:T}}var L={data:void 0,count:void 0,error:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0};function E(e){let{userMemberships:t,userInvitations:r,userSuggestions:o}=e||{};A("useOrganizationList");let a=R(t,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1}),i=R(r,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),l=R(o,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),s=y(),c=x();s.telemetry?.record(n.J6("useOrganizationList"));let d=void 0===t?void 0:{initialPage:a.initialPage,pageSize:a.pageSize},u=void 0===r?void 0:{initialPage:i.initialPage,pageSize:i.pageSize,status:i.status},p=void 0===o?void 0:{initialPage:l.initialPage,pageSize:l.pageSize,status:l.status},m=!!(s.loaded&&c),h=Z(d||{},c?.getOrganizationMemberships,{keepPreviousData:a.keepPreviousData,infinite:a.infinite,enabled:!!d},{type:"userMemberships",userId:c?.id}),g=Z({...u},c?.getOrganizationInvitations,{keepPreviousData:i.keepPreviousData,infinite:i.infinite,enabled:!!u},{type:"userInvitations",userId:c?.id}),f=Z({...p},c?.getOrganizationSuggestions,{keepPreviousData:l.keepPreviousData,infinite:l.infinite,enabled:!!p},{type:"userSuggestions",userId:c?.id});return m?{isLoaded:m,setActive:s.setActive,createOrganization:s.createOrganization,userMemberships:h,userInvitations:g,userSuggestions:f}:{isLoaded:!1,createOrganization:void 0,setActive:void 0,userMemberships:L,userInvitations:L,userSuggestions:L}}var F="undefined"!=typeof window?u.useLayoutEffect:u.useEffect,O=()=>(A("useClerk"),y()),M=(e={})=>{A("useSession");let t=_(),r=O();if(void 0===t)return{isLoaded:!1,isSignedIn:void 0,session:void 0};let n=t?.status==="pending"&&(e.treatPendingAsSignedOut??r.__internal_getOption("treatPendingAsSignedOut"));return null===t||n?{isLoaded:!0,isSignedIn:!1,session:null}:{isLoaded:!0,isSignedIn:!0,session:t}};function U(){A("useUser");let e=x();return void 0===e?{isLoaded:!1,isSignedIn:void 0,user:void 0}:null===e?{isLoaded:!0,isSignedIn:!1,user:null}:{isLoaded:!0,isSignedIn:!0,user:e}}var j=e=>{let t=u.useRef(e);return(0,h.J)(e,t.current)||(t.current=e),u.useMemo(()=>t.current,[t.current])},V=(e,t)=>u.useMemo(e,j(t));async function W(e){try{let t=await e;if(t instanceof Response)return t.json();return t}catch(e){if((0,a.kD)(e)&&e.errors.find(({code:e})=>"session_reverification_required"===e))return l();throw e}}var H=(e,t)=>{let{__internal_openReverification:r,telemetry:i}=O(),l=(0,u.useRef)(e),d=(0,u.useRef)(t),p=(0,u.useMemo)(()=>{var e;return(e={openUIComponent:r,telemetry:i,...d.current},function(t){return async(...r)=>{let i=await W(t(...r));if(s(i)){let l=(0,o.W)(),s=(0,c.Pm)(i.clerk_error.metadata?.reverification),d=s?s().level:void 0,u=()=>{l.reject(new a.w$("User cancelled attempted verification",{code:"reverification_cancelled"}))},p=()=>{l.resolve(!0)};void 0===e.onNeedsReverification?e.openUIComponent?.({level:d,afterVerification:p,afterVerificationCancelled:u}):(e.telemetry?.record(n.J6("UserVerificationCustomUI")),e.onNeedsReverification({cancel:u,complete:p,level:d})),await l.promise,i=await W(t(...r))}return i}})(l.current)},[r,l.current,d.current]);return F(()=>{l.current=e,d.current=t}),p}}}]);