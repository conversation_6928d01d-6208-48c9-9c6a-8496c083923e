// src/de-DE.ts
var deDE = {
  locale: "de-DE",
  apiKeys: {
    action__add: "Neuen API-Key hinzuf\xFCgen",
    action__search: "Suche",
    dates: {
      lastUsed__days: void 0,
      lastUsed__hours: void 0,
      lastUsed__minutes: void 0,
      lastUsed__months: void 0,
      lastUsed__seconds: void 0,
      lastUsed__years: void 0
    },
    detailsTitle__emptyRow: "Keine API-Keys gefunden",
    formButtonPrimary__add: "API-Key erstellen",
    formHint: "Geben Sie einen Namen an, um einen API-Key zu erstellen. Sie k\xF6nnen ihn jederzeit widerrufen.",
    formTitle: "Neuen API-Key hinzuf\xFCgen",
    menuAction__revoke: "API-Key widerrufen",
    revokeConfirmation: {
      formButtonPrimary__revoke: "API-Key widerrufen",
      formHint: "Sind Sie sicher, dass Sie diesen API-Key l\xF6schen wollen?",
      formTitle: 'API-Key "{{apiKeyName}}" widerrufen?'
    }
  },
  backButton: "Zur\xFCck",
  badge__activePlan: "Aktiv",
  badge__canceledEndsAt: "Storniert \u2022 Endet am {{ date | shortDate('de-DE') }}",
  badge__currentPlan: "Aktueller Plan",
  badge__default: "Standard",
  badge__endsAt: "Endet am {{ date | shortDate('de-DE') }}",
  badge__expired: "Abgelaufen",
  badge__otherImpersonatorDevice: "Anderes Imitationsger\xE4t",
  badge__primary: "Prim\xE4r",
  badge__renewsAt: "Verl\xE4ngert sich am {{ date | shortDate('de-DE') }}",
  badge__requiresAction: "Handlung erforderlich",
  badge__startsAt: "Startet am {{ date | shortDate('de-DE') }}",
  badge__thisDevice: "Dieses Ger\xE4t",
  badge__unverified: "Unbest\xE4tigt",
  badge__upcomingPlan: "Bevorstehend",
  badge__userDevice: "Benutzerger\xE4t",
  badge__you: "Du",
  commerce: {
    addPaymentMethod: "Zahlungsmethode hinzuf\xFCgen",
    alwaysFree: "Immer kostenlos",
    annually: "J\xE4hrlich",
    availableFeatures: "Verf\xFCgbare Funktionen",
    billedAnnually: "J\xE4hrlich abgerechnet",
    billedMonthlyOnly: "Nur monatlich abgerechnet",
    cancelSubscription: "Abonnement k\xFCndigen",
    cancelSubscriptionAccessUntil: "Sie haben Zugriff auf '{{plan}}' Funktionen bis zum {{ date | longDate('de-DE') }}. Danach haben Sie keinen Zugriff mehr.",
    cancelSubscriptionNoCharge: "F\xFCr dieses Abonnement fallen keine Kosten an.",
    cancelSubscriptionTitle: "{{plan}} Abonnement k\xFCndigen?",
    cannotSubscribeMonthly: "Sie k\xF6nnen diesen Plan nicht monatlich abonnieren, da nur eine j\xE4hrliche Abrechnung verf\xFCgbar ist.",
    checkout: {
      description__paymentSuccessful: "Ihre Bezahlung war erfolgreich.",
      description__subscriptionSuccessful: "Ihr Abonnement wurde erfolgreich aktiviert.",
      downgradeNotice: "Sie behalten Ihr aktuelles Abonnement bis zum Ende des Abrechnungszeitraums. So lange k\xF6nnen Sie weiterhin alle Funktionen nutzen, danach werden Sie auf dieses Abonnement umgestellt.",
      emailForm: {
        subtitle: "Geben Sie eine E-Mail-Adresse an, um Abrechnungsbelege zu erhalten.",
        title: "E-Mail-Adresse hinzuf\xFCgen"
      },
      lineItems: {
        title__paymentMethod: "Bezahlmethode",
        title__statementId: "Statement-ID",
        title__subscriptionBegins: "Abonnement beginnt",
        title__totalPaid: "Insgesamt bezahlt"
      },
      perMonth: "pro Monat",
      title: "Bezahlung",
      title__paymentSuccessful: "Zahlung erfolgreich!",
      title__subscriptionSuccessful: "Geschafft!"
    },
    credit: void 0,
    creditRemainder: "Verbleibendes Guthaben f\xFCr den restlichen Abrechnungszeitraum.",
    defaultFreePlanActive: "Sie nutzen aktuell den kostenlosen Plan.",
    free: "Kostenlos",
    getStarted: "Jetzt starten",
    keepSubscription: "Abonnement behalten",
    manage: "Verwalten",
    manageSubscription: "Mitgliedschaft verwalten",
    month: "Monat",
    monthly: "Monatlich",
    pastDue: void 0,
    pay: "{{amount}} bezahlen",
    paymentMethods: "Zahlungsmethoden",
    paymentSource: {
      applePayDescription: {
        annual: "J\xE4hrlich abgerechnet",
        monthly: "Monatlich abgerechnet"
      },
      dev: {
        anyNumbers: "Alle Zahlen",
        cardNumber: "Kartennummer",
        cvcZip: "CVC, PLZ",
        developmentMode: "Entwicklermodus",
        expirationDate: "Ablaufdatum",
        testCardInfo: "Test-Kreditkarteninformationen"
      }
    },
    popular: "Beliebt",
    pricingTable: {
      billingCycle: "Abrechnungszyklus",
      included: "Enthalten"
    },
    reSubscribe: "Erneut abonnieren",
    seeAllFeatures: "Alle Funktionen anzeigen",
    subscribe: "Abonnieren",
    subtotal: "Zwischensumme",
    switchPlan: "Zu diesem Plan wechseln",
    switchToAnnual: "Wechsel zu j\xE4hrlich",
    switchToMonthly: "Wechsel zu monatlich",
    totalDueToday: "Heute f\xE4llig",
    viewFeatures: "Funktionen anzeigen",
    year: "Jahr"
  },
  createOrganization: {
    formButtonSubmit: "Organisation erstellen",
    invitePage: {
      formButtonReset: "\xDCberspringen"
    },
    title: "Organisation erstellen"
  },
  dates: {
    lastDay: "Gestern um {{ date | timeString('de-DE') }}",
    next6Days: "{{ date | weekday('de-DE','long') }} bei {{ date | timeString('de-DE') }}",
    nextDay: "Morgen um {{ date | timeString('de-DE') }}",
    numeric: "{{ date | numeric('de-DE') }}",
    previous6Days: "Letzte {{ date | weekday('de-DE','long') }} um {{ date | timeString('de-DE') }}",
    sameDay: "Heute um {{ date | timeString('de-DE') }}"
  },
  dividerText: "oder",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Verwenden Sie eine andere Methode",
  footerPageLink__help: "Hilfe",
  footerPageLink__privacy: "Privatsph\xE4re",
  footerPageLink__terms: "Bedingungen",
  formButtonPrimary: "Fortsetzen",
  formButtonPrimary__verify: "Verify",
  formFieldAction__forgotPassword: "Passwort vergessen?",
  formFieldError__matchingPasswords: "Passw\xF6rter stimmen \xFCberein.",
  formFieldError__notMatchingPasswords: "Passw\xF6rter stimmen nicht \xFCberein.",
  formFieldError__verificationLinkExpired: "Der Best\xE4tigungslink ist abgelaufen. Bitte fordern Sie einen neuen Link an.",
  formFieldHintText__optional: "Optional",
  formFieldHintText__slug: "Der Slug ist eine f\xFCr Menschen lesbare ID. Sie muss einzigartig sein und wird oft in URLs verwendet.",
  formFieldInputPlaceholder__apiKeyDescription: "Geben Sie eine Beschreibung an",
  formFieldInputPlaceholder__apiKeyExpirationDate: "Geben Sie ein Ablaufdatum an",
  formFieldInputPlaceholder__apiKeyName: "Geben Sie einen Namen an",
  formFieldInputPlaceholder__backupCode: "Sicherheitscode eingeben",
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Konto l\xF6schen",
  formFieldInputPlaceholder__emailAddress: "E-Mail-Adresse eingeben",
  formFieldInputPlaceholder__emailAddress_username: "E-Mail-Adresse oder Benutzername eingeben",
  formFieldInputPlaceholder__emailAddresses: "<EMAIL>, <EMAIL>",
  formFieldInputPlaceholder__firstName: "Vorname eingeben",
  formFieldInputPlaceholder__lastName: "Nachname eingeben",
  formFieldInputPlaceholder__organizationDomain: "Organisations-Domain eingeben",
  formFieldInputPlaceholder__organizationDomainEmailAddress: "E-Mail-Adresse der Organisations-Domain eingeben",
  formFieldInputPlaceholder__organizationName: "Name der Organisation eingeben",
  formFieldInputPlaceholder__organizationSlug: "my-org",
  formFieldInputPlaceholder__password: "Passwort eingeben",
  formFieldInputPlaceholder__phoneNumber: "Telefonnummer eingeben",
  formFieldInputPlaceholder__username: "Benutzername eingeben",
  formFieldLabel__apiKeyDescription: "Beschreibung",
  formFieldLabel__apiKeyExpiration: "Ablaufdatum",
  formFieldLabel__apiKeyExpirationDate: "Datum ausw\xE4hlen",
  formFieldLabel__apiKeyName: "Name",
  formFieldLabel__automaticInvitations: "Aktivieren Sie automatische Einladungen f\xFCr diese Domain",
  formFieldLabel__backupCode: "Sicherungscode",
  formFieldLabel__confirmDeletion: "Best\xE4tigung",
  formFieldLabel__confirmPassword: "Passwort best\xE4tigen",
  formFieldLabel__currentPassword: "Aktuelles Passwort",
  formFieldLabel__emailAddress: "E-Mail-Adresse",
  formFieldLabel__emailAddress_username: "E-Mail-Adresse oder Benutzername",
  formFieldLabel__emailAddresses: "E-Mail-Adressen",
  formFieldLabel__firstName: "Vorname",
  formFieldLabel__lastName: "Nachname",
  formFieldLabel__newPassword: "Neues Passwort",
  formFieldLabel__organizationDomain: "Domain",
  formFieldLabel__organizationDomainDeletePending: "Ausstehende Einladungen und Vorschl\xE4ge l\xF6schen",
  formFieldLabel__organizationDomainEmailAddress: "E-Mail-Adresse f\xFCr die Verifizierung",
  formFieldLabel__organizationDomainEmailAddressDescription: "Geben Sie eine E-Mail-Adresse dieser Domain ein, um einen Code zu erhalten und diese Domain zu verifizieren.",
  formFieldLabel__organizationName: "Organisationsname",
  formFieldLabel__organizationSlug: "Slug",
  formFieldLabel__passkeyName: "Name des Passkeys",
  formFieldLabel__password: "Passwort",
  formFieldLabel__phoneNumber: "Telefonnummer",
  formFieldLabel__role: "Rolle",
  formFieldLabel__signOutOfOtherSessions: "Alle anderen Ger\xE4te abmelden",
  formFieldLabel__username: "Nutzername",
  impersonationFab: {
    action__signOut: "Ausloggen",
    title: "Angemeldet als {{identifier}}"
  },
  maintenanceMode: "Wir f\xFChren derzeit Wartungsarbeiten durch, aber keine Sorge, es sollte nicht l\xE4nger als ein paar Minuten dauern.",
  membershipRole__admin: "Administrator",
  membershipRole__basicMember: "Mitglied",
  membershipRole__guestMember: "Gast",
  organizationList: {
    action__createOrganization: "Organisation erstellen",
    action__invitationAccept: "Beitreten",
    action__suggestionsAccept: "Beitritt anfragen",
    createOrganization: "Organisation erstellen",
    invitationAcceptedLabel: "Beitreten",
    subtitle: "um fortzufahren zu {{applicationName}}",
    suggestionsAcceptedLabel: "Genehmigung ausstehend",
    title: "Konto ausw\xE4hlen",
    titleWithoutPersonal: "Organisation ausw\xE4hlen"
  },
  organizationProfile: {
    apiKeysPage: {
      title: "API-Keys"
    },
    badge__automaticInvitation: "Automatische Einladungen",
    badge__automaticSuggestion: "Automatische Vorschl\xE4ge",
    badge__manualInvitation: "Keine automatische Aufnahme",
    badge__unverified: "Nicht verifiziert",
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: "Als Standard festlegen",
        actionLabel__remove: "Entfernen",
        add: "Neue Zahlungsmethode hinzuf\xFCgen",
        addSubtitle: "F\xFCgen Sie eine neue Zahlungsmethode hinzu.",
        cancelButton: "Abbrechen",
        formButtonPrimary__add: "Zahlungsmethode hinzuf\xFCgen",
        formButtonPrimary__pay: "{{amount}} bezahlen",
        payWithTestCardButton: "Mit Test-Kreditkarte bezahlen",
        removeResource: {
          messageLine1: "{{identifier}} wird von diesem Konto entfernt.",
          messageLine2: "In Zukunft k\xF6nnen Sie diese Zahlungsmethode nicht mehr verwenden. Alle laufenden Abonnements, die diese Zahlungsmethode verwenden, werden aufh\xF6ren zu funktionieren.",
          successMessage: "{{paymentSource}} wurde von diesem Konto entfernt.",
          title: "Zahlungsmethode entfernen"
        },
        title: "Zahlungsmethoden"
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: "Pl\xE4ne",
        headerTitle__statements: "Abrechnungen",
        headerTitle__subscriptions: "Abonnements"
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: "Plan abonnieren",
        actionLabel__switchPlan: "Plan wechseln",
        title: "Abonnement"
      },
      subscriptionsSection: {
        actionLabel__default: "Verwalten"
      },
      switchPlansSection: {
        title: "Plan wechseln"
      },
      title: "Abrechnung"
    },
    createDomainPage: {
      subtitle: "F\xFCgen Sie die zu \xFCberpr\xFCfende Domain hinzu. Benutzer mit E-Mail-Adressen von dieser Domain k\xF6nnen der Organisation automatisch beitreten oder einen Antrag auf Beitritt stellen.",
      title: "Domain hinzuf\xFCgen"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Die Einladungen konnten nicht versendet werden. Beheben Sie Folgendes und versuchen Sie es erneut:",
      formButtonPrimary__continue: "Einladungen verschicken",
      selectDropdown__role: "Rolle w\xE4hlen",
      subtitle: "Laden Sie neue Mitglieder zu dieser Organisation ein",
      successMessage: "Einladungen erfolgreich versendet",
      title: "Mitglieder einladen"
    },
    membersPage: {
      action__invite: "Einladen",
      action__search: "Suchen",
      activeMembersTab: {
        menuAction__remove: "Mitglied entfernen",
        tableHeader__actions: "Aktionen",
        tableHeader__joined: "Trat bei",
        tableHeader__role: "Rolle",
        tableHeader__user: "Benutzer"
      },
      detailsTitle__emptyRow: "Keine Mitglieder zum Anzeigen",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Laden Sie Benutzer ein, indem Sie eine E-Mail-Domain mit Ihrer Organisation verbinden. Jeder, der sich mit dieser passenden E-Mail-Domain anmeldet, kann der Organisation jederzeit beitreten.",
          headerTitle: "Automatische Einladungen",
          primaryButton: "Verwalten Sie verifizierte Domains"
        },
        table__emptyRow: "Keine Einladungen verf\xFCgbar"
      },
      invitedMembersTab: {
        menuAction__revoke: "Einladung widerrufen",
        tableHeader__invited: "Eingeladen"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Benutzer, die sich mit einer passenden E-Mail-Domain anmelden, k\xF6nnen einen Vorschlag f\xFCr eine Beitrittsanfrage zu Ihrer Organisation sehen.",
          headerTitle: "Automatische Vorschl\xE4ge",
          primaryButton: "Verifizierte Domains verwalten"
        },
        menuAction__approve: "Best\xE4tigen",
        menuAction__reject: "Ablehnen",
        tableHeader__requested: "Angefragte Zug\xE4nge",
        table__emptyRow: "Keine Anfragen verf\xFCgbar"
      },
      start: {
        headerTitle__invitations: "Einladungen",
        headerTitle__members: "Mitglieder",
        headerTitle__requests: "Anfragen"
      }
    },
    navbar: {
      apiKeys: "API-Keys",
      billing: "Abrechnung",
      description: "Verwalten Sie ihre Organisation.",
      general: "Allgemein",
      members: "Mitglieder",
      title: "Organisation"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: "Sie haben keine Berechtigung, die Abrechnungen f\xFCr diese Organisation zu verwalten."
      },
      title: "Pl\xE4ne"
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Geben Sie "{{organizationName}}" unten ein, um fortzufahren.',
          messageLine1: "Sind Sie sicher, dass Sie diese Organisation l\xF6schen wollen?",
          messageLine2: "Diese Aktion ist dauerhaft und irreversibel.",
          successMessage: "Sie haben die Organisation gel\xF6scht.",
          title: "Organisation l\xF6schen"
        },
        leaveOrganization: {
          actionDescription: 'Geben Sie "{{organizationName}}" unten ein, um fortzufahren.',
          messageLine1: "M\xF6chten Sie diese Organisation wirklich verlassen? Sie verlieren den Zugriff auf diese Organisation und Ihre Anwendungen.",
          messageLine2: "Diese Aktion ist dauerhaft und irreversibel.",
          successMessage: "Sie haben die Organisation verlassen.",
          title: "Organisation verlassen"
        },
        title: "Achtung"
      },
      domainSection: {
        menuAction__manage: "Verwalten",
        menuAction__remove: "L\xF6schen",
        menuAction__verify: "Verifizieren",
        primaryButton: "Domain hinzuf\xFCgen",
        subtitle: "Erlauben Sie Benutzern, der Organisation automatisch beizutreten oder den Beitritt auf der Grundlage einer verifizierten E-Mail-Domain anzufragen.",
        title: "Verifizierte Domains"
      },
      successMessage: "Die Organisation wurde aktualisiert.",
      title: "Organisationsprofil"
    },
    removeDomainPage: {
      messageLine1: "Die E-mail-Domain {{domain}} wird entfernt.",
      messageLine2: "Benutzer k\xF6nnen der Organisation danach nicht mehr automatisch beitreten.",
      successMessage: "{{domain}} wurde entfernt.",
      title: "Domain entfernen"
    },
    start: {
      headerTitle__general: "Allgemein",
      headerTitle__members: "Mitglieder",
      profileSection: {
        primaryButton: "Profil bearbeiten",
        title: "Organisationsprofil",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Das Entfernen dieser Domain betrifft die eingeladenen Benutzer.",
        removeDomainActionLabel__remove: "Domain entfernen",
        removeDomainSubtitle: "Sie k\xF6nnen diese Domain von den verifizierten Domains entfernen",
        removeDomainTitle: "Domain entfernen"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Benutzer werden bei der Anmeldung automatisch eingeladen, der Organisation beizutreten, und k\xF6nnen jederzeit beitreten.",
        automaticInvitationOption__label: "Automatische Einladungen",
        automaticSuggestionOption__description: "Benutzer erhalten einen Vorschlag f\xFCr eine Beitrittsanfrage, m\xFCssen aber von einem Administrator genehmigt werden, bevor sie der Organisation beitreten k\xF6nnen.",
        automaticSuggestionOption__label: "Automatische Vorschl\xE4ge",
        calloutInfoLabel: "\xC4nderungen des Anmeldemodus wirkt sich nur auf neue Benutzer aus.",
        calloutInvitationCountLabel: "Ausstehende Einladungen gesendet an Benutzer: {{count}}",
        calloutSuggestionCountLabel: "Ausstehende Vorschl\xE4ge gesendet an Benutzer: {{count}}",
        manualInvitationOption__description: "Benutzer k\xF6nnen nur manuell in die Organisation eingeladen werden.",
        manualInvitationOption__label: "Keine automatische Aufnahme",
        subtitle: "W\xE4hlen Sie, wie Benutzer mit dieser Domain der Organisation beitreten k\xF6nnen."
      },
      start: {
        headerTitle__danger: "Gefahr",
        headerTitle__enrollment: "Optionen f\xFCr die Aufnahme"
      },
      subtitle: "Die Domain {{domain}} ist nun verifiziert. Bitte w\xE4hlen Sie einen Aufnahmemodus aus.",
      title: "{{domain}} aktualisieren"
    },
    verifyDomainPage: {
      formSubtitle: "Geben Sie den an Ihre E-Mail-Adresse gesendeten Verifizierungscode ein",
      formTitle: "Verifizierungscode",
      resendButton: "Sie haben keinen Code erhalten? Erneut senden",
      subtitle: "Die Domain {{domainName}} muss per E-mail verifiziert werden.",
      subtitleVerificationCodeScreen: "Ein Verifizierungscode wurde an {{emailAddress}} gesendet. Geben Sie den Code ein, um fortzufahren.",
      title: "Domain verifizieren"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Organisation erstellen",
    action__invitationAccept: "Beitreten",
    action__manageOrganization: "Organisation verwalten",
    action__suggestionsAccept: "Beitritt anfragen",
    notSelected: "Keine Organisation ausgew\xE4hlt",
    personalWorkspace: "Pers\xF6nlicher Arbeitsbereich",
    suggestionsAcceptedLabel: "Annahme ausstehend"
  },
  paginationButton__next: "N\xE4chste",
  paginationButton__previous: "Vorherige",
  paginationRowText__displaying: "Anzeigen",
  paginationRowText__of: "von",
  reverification: {
    alternativeMethods: {
      actionLink: "Klicken Sie hier, um eine alternative Methode zu verwenden",
      actionText: "Verwenden Sie eine alternative Verifizierungsmethode",
      blockButton__backupCode: "Mit Backup-Code verifizieren",
      blockButton__emailCode: "Mit E-Mail-Code verifizieren",
      blockButton__passkey: "Verwenden Sie Ihren Passkey",
      blockButton__password: "Mit Passwort verifizieren",
      blockButton__phoneCode: "Mit SMS-Code verifizieren",
      blockButton__totp: "Mit TOTP verifizieren",
      getHelp: {
        blockButton__emailSupport: "E-Mail-Support kontaktieren",
        content: "Wenn Sie Hilfe ben\xF6tigen, wenden Sie sich bitte an unseren Support.",
        title: "Hilfe erhalten"
      },
      subtitle: "W\xE4hlen Sie eine Methode, um sich zu verifizieren",
      title: "Verifizierung erforderlich"
    },
    backupCodeMfa: {
      subtitle: "Verwenden Sie den Backup-Code, der Ihnen bei der Registrierung zur Verf\xFCgung gestellt wurde.",
      title: "Backup-Code Verifizierung"
    },
    emailCode: {
      formTitle: "Geben Sie den Code ein, den wir an Ihre E-Mail-Adresse gesendet haben.",
      resendButton: "Code erneut senden",
      subtitle: "\xDCberpr\xFCfen Sie Ihre E-Mail auf den Verifizierungscode.",
      title: "E-Mail-Code Verifizierung"
    },
    noAvailableMethods: {
      message: "Es sind keine Verifizierungsmethoden mehr verf\xFCgbar.",
      subtitle: "Bitte kontaktieren Sie den Support, um Hilfe zu erhalten.",
      title: "Keine verf\xFCgbaren Methoden"
    },
    passkey: {
      blockButton__passkey: "Verwenden Sie Ihren Passkey",
      subtitle: "Die Verwendung Ihres Passkeys best\xE4tigt Ihre Identit\xE4t. Ihr Ger\xE4t kann nach Ihrem Fingerabdruck, Gesicht oder Bildschirmsperre fragen.",
      title: "Verwenden Sie Ihren Passkey"
    },
    password: {
      actionLink: "Passwort zur\xFCcksetzen",
      subtitle: "Geben Sie Ihr Passwort ein, um fortzufahren.",
      title: "Passwort-Verifizierung"
    },
    phoneCode: {
      formTitle: "Geben Sie den Code ein, den wir an Ihre Telefonnummer gesendet haben.",
      resendButton: "Code erneut senden",
      subtitle: "\xDCberpr\xFCfen Sie Ihre SMS-Nachricht auf den Verifizierungscode.",
      title: "SMS-Code Verifizierung"
    },
    phoneCodeMfa: {
      formTitle: "Geben Sie den Code ein, den wir Ihnen per SMS gesendet haben.",
      resendButton: "Code erneut senden",
      subtitle: "\xDCberpr\xFCfen Sie Ihre SMS auf den Verifizierungscode.",
      title: "SMS-Code (MFA) Verifizierung"
    },
    totpMfa: {
      formTitle: "Geben Sie den Code aus Ihrer Authentifikator-App ein.",
      subtitle: "Verwenden Sie die Authentifikator-App, die Sie eingerichtet haben.",
      title: "TOTP-Verifizierung (MFA)"
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Konto hinzuf\xFCgen",
      action__signOutAll: "Von allen Konten abmelden",
      subtitle: "W\xE4hlen Sie das Konto, mit dem Sie fortfahren m\xF6chten.",
      title: "W\xE4hlen Sie ein Konto"
    },
    alternativeMethods: {
      actionLink: "Hilfe",
      actionText: "Haben Sie keine davon?",
      blockButton__backupCode: "Verwenden Sie einen Backup-Code",
      blockButton__emailCode: "Code an {{identifier}} senden",
      blockButton__emailLink: "Link senden an {{identifier}}",
      blockButton__passkey: "Melden Sie sich mit Ihrem Passkey an",
      blockButton__password: "Melden Sie sich mit Ihrem Passwort an",
      blockButton__phoneCode: "Code an {{identifier}} senden",
      blockButton__totp: "Verwenden Sie Ihre Authentifizierungs-App",
      getHelp: {
        blockButton__emailSupport: "Unterst\xFCtzung per E-Mail",
        content: "Wenn Sie Schwierigkeiten haben, sich mit Ihrem Konto anzumelden, senden Sie uns eine E-Mail und wir werden mit Ihnen zusammenarbeiten, um den Zugriff so schnell wie m\xF6glich wiederherzustellen.",
        title: "Hilfe"
      },
      subtitle: "Haben Sie Probleme? Sie k\xF6nnen eine der folgenden Methoden zur Anmeldung verwenden.",
      title: "Verwenden Sie eine andere Methode"
    },
    alternativePhoneCodeProvider: {
      formTitle: "Best\xE4tigungscode",
      resendButton: "Best\xE4tigungscode nicht erhalten? Erneut senden",
      subtitle: "weiter zu {{applicationName}}",
      title: "\xDCberpr\xFCfen Sie {{provider}}"
    },
    backupCodeMfa: {
      subtitle: "weiter zu {{applicationName}}",
      title: "Geben Sie einen Backup-Code ein"
    },
    emailCode: {
      formTitle: "Best\xE4tigungscode",
      resendButton: "Code erneut senden",
      subtitle: "weiter zu {{applicationName}}",
      title: "\xDCberpr\xFCfen Sie Ihren Posteingang"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Die Anfrage stammt von einem nicht kompatiblen Client.",
        title: "Client-Kompatibilit\xE4tsfehler"
      },
      expired: {
        subtitle: "Kehren Sie zum urspr\xFCnglichen Tab zur\xFCck, um fortzufahren.",
        title: "Dieser Best\xE4tigungslink ist abgelaufen"
      },
      failed: {
        subtitle: "Kehren Sie zum urspr\xFCnglichen Tab zur\xFCck, um fortzufahren.",
        title: "Dieser Best\xE4tigungslink ist ung\xFCltig"
      },
      formSubtitle: "Verwenden Sie den an Ihre E-Mail gesendeten Best\xE4tigungslink",
      formTitle: "Best\xE4tigungslink",
      loading: {
        subtitle: "Sie werden in K\xFCrze weitergeleitet",
        title: "Einloggen..."
      },
      resendButton: "Link erneut senden",
      subtitle: "weiter zu {{applicationName}}",
      title: "\xDCberpr\xFCfen Sie Ihren Posteingang",
      unusedTab: {
        title: "Sie k\xF6nnen diesen Tab schlie\xDFen"
      },
      verified: {
        subtitle: "Sie werden in K\xFCrze weitergeleitet",
        title: "Erfolgreich angemeldet"
      },
      verifiedSwitchTab: {
        subtitle: "Kehren Sie zum urspr\xFCnglichem Tab zur\xFCck, um fortzufahren",
        subtitleNewTab: "Kehren Sie zum neu ge\xF6ffneten Tab zur\xFCck, um fortzufahren",
        titleNewTab: "In einem anderen Tab angemeldet"
      }
    },
    forgotPassword: {
      formTitle: "Passwort-Code zur\xFCcksetzen",
      resendButton: "Sie haben keinen Code erhalten? Erneut senden",
      subtitle: "um Passwort zur\xFCckzusetzen",
      subtitle_email: "Geben Sie zun\xE4chst den an Ihre E-Mail gesendeten Code ein",
      subtitle_phone: "Geben Sie zun\xE4chst den auf Ihr Mobiltelefon geschickten Code ein",
      title: "Passwort zur\xFCcksetzen"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Passwort zur\xFCcksetzen",
      label__alternativeMethods: "Oder melden Sie sich mit einer anderen Methode an",
      title: "Passwort vergessen?"
    },
    noAvailableMethods: {
      message: "Die Anmeldung kann nicht fortgesetzt werden. Es ist kein Authentifizierungsfaktor verf\xFCgbar.",
      subtitle: "Ein Fehler ist aufgetreten",
      title: "Anmeldung nicht m\xF6glich"
    },
    passkey: {
      subtitle: "Die Verwendung Ihres Passkeys best\xE4tigt, dass Sie es sind. Ihr Ger\xE4t kann nach Ihrem Fingerabdruck, Ihrem Gesicht oder der Bildschirmsperre fragen.",
      title: "Verwenden Sie Ihren Passkey"
    },
    password: {
      actionLink: "Verwenden Sie eine andere Methode",
      subtitle: "weiter zu {{applicationName}}",
      title: "Geben Sie Ihr Passwort ein"
    },
    passwordPwned: {
      title: "Passwort kompromittiert"
    },
    phoneCode: {
      formTitle: "Best\xE4tigungscode",
      resendButton: "Code erneut senden",
      subtitle: "weiter zu {{applicationName}}",
      title: "Schau auf dein Telefon"
    },
    phoneCodeMfa: {
      formTitle: "Best\xE4tigungscode",
      resendButton: "Code erneut senden",
      subtitle: "Um fortzufahren, geben Sie bitte den Best\xE4tigungscode ein, der an Ihre Telefonnummer gesendet wurde",
      title: "Schau auf dein Telefon"
    },
    resetPassword: {
      formButtonPrimary: "Passwort zur\xFCcksetzen",
      requiredMessage: "Es existiert bereits ein Konto mit einer nicht verifizierten E-Mail Adresse. Bitte setzen Sie Ihr Passwort zur Sicherheit zur\xFCck.",
      successMessage: "Ihr Passwort wurde erfolgreich ge\xE4ndert. Bitte warten Sie einen Moment, um Sie anzumelden.",
      title: "Neues Passwort setzen"
    },
    resetPasswordMfa: {
      detailsLabel: "Bevor wir Ihr Passwort zur\xFCcksetzen k\xF6nnen, m\xFCssen wir Ihre Identit\xE4t \xFCberpr\xFCfen."
    },
    start: {
      actionLink: "Anmelden",
      actionLink__join_waitlist: "Warteliste beitreten",
      actionLink__use_email: "E-mail nutzen",
      actionLink__use_email_username: "E-mail oder Benutzernamen nutzen",
      actionLink__use_passkey: "Passkey nutzen",
      actionLink__use_phone: "Mobiltelefon nutzen",
      actionLink__use_username: "Benutzername nutzen",
      actionText: "Kein Account?",
      actionText__join_waitlist: "Warteliste beitreten",
      alternativePhoneCodeProvider: {
        actionLink: "Andere Methode verwenden",
        label: "{{provider}} Telefonnummer",
        subtitle: "Geben Sie Ihre Telefonnummer ein, um einen Best\xE4tigungscode per {{provider}} zu erhalten.",
        title: "In {{applicationName}} mit {{provider}} einloggen"
      },
      subtitle: "weiter zu {{applicationName}}",
      subtitleCombined: void 0,
      title: "In {{applicationName}} einloggen",
      titleCombined: "Weiter zu {{applicationName}}"
    },
    totpMfa: {
      formTitle: "Best\xE4tigungscode",
      subtitle: "Um fortzufahren, geben Sie bitte den Verifizierungscode ein, der von Ihrer Authenticator-App generiert wurde.",
      title: "Best\xE4tigung in zwei Schritten"
    }
  },
  signInEnterPasswordTitle: "Geben Sie Ihr Passwort ein",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: "Best\xE4tigungscode nicht erhalten? Erneut senden",
      subtitle: "Geben Sie den Best\xE4tigungscode ein, der an {{provider}} gesendet wurde",
      title: "{{provider}} verifizieren"
    },
    continue: {
      actionLink: "Einloggen",
      actionText: "Haben Sie ein Konto?",
      subtitle: "weiter zu {{applicationName}}",
      title: "F\xFCllen Sie fehlende Felder aus"
    },
    emailCode: {
      formSubtitle: "Geben Sie den Best\xE4tigungscode ein, der an Ihre E-Mail-Adresse gesendet wurde",
      formTitle: "Best\xE4tigungscode",
      resendButton: "Code erneut senden",
      subtitle: "weiter zu {{applicationName}}",
      title: "Best\xE4tigen Sie Ihre E-Mail"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Die Anfrage konnte nicht verarbeitet werden, da der Client nicht kompatibel ist.",
        title: "Fehler: Inkompatibler Client"
      },
      formSubtitle: "Verwenden Sie den an Ihre E-Mail-Adresse gesendeten Best\xE4tigungslink",
      formTitle: "Best\xE4tigungslink",
      loading: {
        title: "Anmeldung..."
      },
      resendButton: "Link erneut senden",
      subtitle: "weiter zu {{applicationName}}",
      title: "Best\xE4tigen Sie Ihre E-Mail",
      verified: {
        title: "Erfolgreich angemeldet"
      },
      verifiedSwitchTab: {
        subtitle: "Kehren Sie zum neu ge\xF6ffneten Tab zur\xFCck, um fortzufahren",
        subtitleNewTab: "Kehren Sie zum vorherigen Tab zur\xFCck, um fortzufahren",
        title: "E-Mail erfolgreich verifiziert"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: 'Ich stimme der {{ privacyPolicyLink || link("Datenschutzerkl\xE4rung") }} zu',
        label__onlyTermsOfService: 'Ich stimme den {{ termsOfServiceLink || link("Nutzungsbedingungen") }} zu',
        label__termsOfServiceAndPrivacyPolicy: 'Ich stimme den {{ termsOfServiceLink || link("Nutzungsbedingungen") }} und der {{ privacyPolicyLink || link("Datenschutzerkl\xE4rung") }} zu'
      },
      continue: {
        subtitle: "Bitte lesen und akzeptieren Sie die Bedingungen, um fortzufahren",
        title: "Rechtliche Einwilligung"
      }
    },
    phoneCode: {
      formSubtitle: "Geben Sie den Best\xE4tigungscode ein, der an Ihre Telefonnummer gesendet wurde",
      formTitle: "Best\xE4tigungscode",
      resendButton: "Code erneut senden",
      subtitle: "weiter zu {{applicationName}}",
      title: "Verifizieren Sie Ihre Telefonnummer"
    },
    restrictedAccess: {
      actionLink: "Mehr erfahren",
      actionText: "Zugang verweigert?",
      blockButton__emailSupport: "E-Mail-Support kontaktieren",
      blockButton__joinWaitlist: "Warteliste beitreten",
      subtitle: "Ihr Zugang ist momentan eingeschr\xE4nkt.",
      subtitleWaitlist: "Treten Sie der Warteliste bei, um Benachrichtigungen zu erhalten.",
      title: "Zugang verweigert"
    },
    start: {
      actionLink: "Einloggen",
      actionLink__use_email: "Mit E-Mail einloggen",
      actionLink__use_phone: "Mit Telefonnummer einloggen",
      actionText: "Haben Sie ein Konto?",
      alternativePhoneCodeProvider: {
        actionLink: "Andere Methode verwenden",
        label: "{{provider}} Telefonnummer",
        subtitle: "Geben Sie Ihre Telefonnummer ein, um einen Best\xE4tigungscode per {{provider}} zu erhalten.",
        title: "In {{applicationName}} mit {{provider}} anmelden"
      },
      subtitle: "weiter zu {{applicationName}}",
      subtitleCombined: "weiter zu {{applicationName}}",
      title: "Erstelle deinen Account",
      titleCombined: "Erstelle deinen Account"
    }
  },
  socialButtonsBlockButton: "Weiter mit {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: "{{provider|titleize}}",
  unstable__errors: {
    already_a_member_in_organization: "Sie sind bereits Mitglied in dieser Organisation.",
    captcha_invalid: "Anmeldung aufgrund fehlgeschlagener Sicherheits\xFCberpr\xFCfung nicht erfolgreich. Bitte versuchen Sie es erneut oder kontaktieren Sie uns f\xFCr weitere Unterst\xFCtzung.",
    captcha_unavailable: "Die Anmeldung ist aufgrund einer fehlgeschlagenen Bot-Validierung fehlgeschlagen. Bitte aktualisieren Sie die Seite, um es erneut zu versuchen, oder wenden Sie sich an den Support, um weitere Unterst\xFCtzung zu erhalten.",
    form_code_incorrect: "Der eingegebene Code ist falsch. Bitte \xFCberpr\xFCfen Sie ihn und versuchen Sie es erneut.",
    form_identifier_exists__email_address: "Diese E-Mail-Adresse ist bereits vergeben. Bitte w\xE4hlen Sie eine andere.",
    form_identifier_exists__phone_number: "Diese Telefonnummer ist bereits vergeben. Bitte w\xE4hlen Sie eine andere.",
    form_identifier_exists__username: "Dieser Benutzername ist bereits vergeben. Bitte w\xE4hlen Sie einen anderen.",
    form_identifier_not_found: "Wir konnten kein Konto mit diesen Details finden.",
    form_param_format_invalid: "Das Format des eingegebenen Parameters ist ung\xFCltig.",
    form_param_format_invalid__email_address: "Bitte geben Sie eine g\xFCltige E-Mail-Adresse ein.",
    form_param_format_invalid__phone_number: "Die Telefonnummer muss ein g\xFCltiges internationales Format haben.",
    form_param_max_length_exceeded__first_name: "Der Vorname sollte nicht mehr als 256 Zeichen umfassen.",
    form_param_max_length_exceeded__last_name: "Der Nachname sollte nicht mehr als 256 Zeichen umfassen.",
    form_param_max_length_exceeded__name: "Der Name sollte nicht l\xE4nger als 256 Zeichen sein.",
    form_param_nil: "Ein erforderliches Feld wurde nicht ausgef\xFCllt. Bitte \xFCberpr\xFCfen Sie Ihre Eingaben.",
    form_param_value_invalid: "Der eingegebene Wert ist ung\xFCltig.",
    form_password_incorrect: "Das eingegebene Passwort ist falsch.",
    form_password_length_too_short: "Das Passwort ist zu kurz. Es muss mindestens 8 Zeichen lang sein.",
    form_password_not_strong_enough: "Passwort nicht stark genug.",
    form_password_pwned: "Das gew\xE4hlte Passwort wurde bei einem Datenleck im Internet gefunden. W\xE4hlen Sie aus Sicherheitsgr\xFCnden bitte ein anderes Passwort.",
    form_password_pwned__sign_in: "Dieses Passwort wurde in einem Datenleck gefunden und kann nicht verwendet werden. Bitte setzen Sie Ihr Passwort zur\xFCck.",
    form_password_size_in_bytes_exceeded: "Das Passwort hat die maximale Anzahl an Bytes \xFCberschritten. Bitte k\xFCrzen oder Sonderzeichen entfernen.",
    form_password_validation_failed: "Falsches Passwort.",
    form_username_invalid_character: "Der Benutzername enth\xE4lt ung\xFCltige Zeichen. Bitte verwenden Sie nur alphanumerische Zeichen und Unterstriche.",
    form_username_invalid_length: "Der Benutzername muss zwischen 3 und 30 Zeichen lang sein.",
    identification_deletion_failed: "Sie k\xF6nnen Ihre letzte Kennung nicht l\xF6schen.",
    not_allowed_access: "Die E-Mail-Adresse oder Telefonnummer ist f\xFCr die Anmeldung nicht zul\xE4ssig. Dies kann daran liegen, dass Ihre E-Mail-Adresse die Zeichen '+', '=', '#' oder '.' enth\xE4lt, Sie eine Domain verwenden, die mit einem tempor\xE4ren E-Mail-Dienst verkn\xFCpft ist, oder dass Sie explizit gesperrt sind. Wenn Sie glauben, dass dies ein Fehler ist, wenden Sie sich bitte an den Support.",
    organization_domain_blocked: "Diese E-Mail-Provider-Domain ist gesperrt. Bitte verwenden Sie eine andere.",
    organization_domain_common: "Dies ist eine g\xE4ngige E-Mail-Provider-Domain. Bitte verwenden Sie eine andere.",
    organization_domain_exists_for_enterprise_connection: "Diese Domain wird bereits f\xFCr das SSO Ihrer Organisation verwendet",
    organization_membership_quota_exceeded: "Sie haben Ihr Limit an Organisationsmitgliedschaften einschlie\xDFlich ausstehender Einladungen erreicht.",
    organization_minimum_permissions_needed: "Es muss mindestens ein Organisationsmitglied mit den erforderlichen Mindestberechtigungen geben.",
    passkey_already_exists: "Auf diesem Ger\xE4t ist bereits ein Passkey registriert.",
    passkey_not_supported: "Passkeys werden auf diesem Ger\xE4t nicht unterst\xFCtzt.",
    passkey_pa_not_supported: "Die Registrierung erfordert einen Plattformauthentifikator, der vom Ger\xE4t nicht unterst\xFCtzt wird.",
    passkey_registration_cancelled: "Die Passkey-Registrierung wurde abgebrochen oder das Zeitlimit wurde \xFCberschritten.",
    passkey_retrieval_cancelled: "Die Passkey-Registrierung wurde abgebrochen oder das Zeitlimit wurde \xFCberschritten.",
    passwordComplexity: {
      maximumLength: "weniger als {{length}} Zeichen lang sein",
      minimumLength: "mindestens {{length}} Zeichen lang sein",
      requireLowercase: "einen Kleinbuchstaben enthalten",
      requireNumbers: "eine Zahl enthalten",
      requireSpecialCharacter: "ein Sonderzeichen enthalten",
      requireUppercase: "einen Gro\xDFbuchstaben enthalten",
      sentencePrefix: "Das Passwort muss"
    },
    phone_number_exists: "Diese Telefonnummer ist bereits vergeben. Bitte w\xE4hlen Sie eine Andere.",
    session_exists: "Sie sind bereits angemeldet.",
    web3_missing_identifier: "Eine Web3 Wallet-Erweiterung wurde nicht gefunden. Bitte installieren Sie eine, um fortzufahren.",
    zxcvbn: {
      couldBeStronger: "Ihr Passwort funktioniert, k\xF6nnte aber besser sein. Versuchen Sie, mehr Zeichen hinzuzuf\xFCgen.",
      goodPassword: "Ihr Passwort erf\xFCllt alle notwendigen Anforderungen.",
      notEnough: "Ihr Passwort ist nicht stark genug.",
      suggestions: {
        allUppercase: "Einige, aber nicht alle Buchstaben gro\xDF schreiben.",
        anotherWord: "Weitere W\xF6rter, die weniger h\xE4ufig vorkommen, hinzuf\xFCgen.",
        associatedYears: "Jahre, die mit pers\xF6nlichen Daten in Verbindung gebracht werden k\xF6nnen, vermeiden.",
        capitalization: "Nicht nur den ersten Buchstaben gro\xDF schreiben.",
        dates: "Daten, die mit pers\xF6nlichen Daten in Verbindung gebracht werden k\xF6nnen, vermeiden.",
        l33t: "Vorhersehbare Buchstabenersetzungen wie '@' f\xFCr 'a' vermeiden.",
        longerKeyboardPattern: "L\xE4ngere Tastaturmuster in unterschiedlicher Tipprichtung verwenden.",
        noNeed: "Es ist m\xF6glich, starke Passw\xF6rter zu erstellen, ohne Symbole, Zahlen oder Gro\xDFbuchstaben zu verwenden.",
        pwned: "Wenn Sie dieses Passwort an anderer Stelle verwenden, sollten Sie es \xE4ndern.",
        recentYears: "Die j\xFCngsten Jahreszahlen vermeiden.",
        repeated: "Wort- und Zeichenwiederholungen vermeiden.",
        reverseWords: "Umgekehrte Schreibweise von gebr\xE4uchlichen W\xF6rtern vermeiden.",
        sequences: "H\xE4ufige Zeichenfolgen vermeiden.",
        useWords: "Mehrere W\xF6rter verwenden, aber allgemeine Phrasen vermeiden."
      },
      warnings: {
        common: "Dies ist ein oft verwendetes Passwort.",
        commonNames: "Vornamen und Nachnamen sind leicht zu erraten.",
        dates: "Ein Datum ist leicht zu erraten.",
        extendedRepeat: 'Sich wiederholende Zeichenmuster wie "abcabcabc" sind leicht zu erraten.',
        keyPattern: "Kurze Tastaturmuster sind leicht zu erraten.",
        namesByThemselves: "Einzelne Namen oder Nachnamen sind leicht zu erraten.",
        pwned: "Ihr Passwort wurde durch eine Datenpanne im Internet offengelegt.",
        recentYears: "Die j\xFCngsten Jahreszahlen sind leicht zu erraten.",
        sequences: 'H\xE4ufige Zeichenfolgen wie "abc" sind leicht zu erraten.',
        similarToCommon: "Dies weist \xC4hnlichkeit zu anderen oft verwendeten Passw\xF6rtern auf.",
        simpleRepeat: 'Sich wiederholende Zeichen wie "aaa" sind leicht zu erraten.',
        straightRow: "Gerade Linien von Tasten auf der Tastatur sind leicht zu erraten.",
        topHundred: "Dies ist ein h\xE4ufig verwendetes Passwort.",
        topTen: "Dies ist ein sehr h\xE4ufig verwendetes Passwort.",
        userInputs: "Es sollten keine pers\xF6nlichen oder Seiten relevanten Daten vorkommen.",
        wordByItself: "Einzelne W\xF6rter sind leicht zu erraten."
      }
    }
  },
  userButton: {
    action__addAccount: "Konto hinzuf\xFCgen",
    action__manageAccount: "Konto verwalten",
    action__signOut: "Ausloggen",
    action__signOutAll: "Melden Sie sich von allen Konten ab"
  },
  userProfile: {
    apiKeysPage: {
      title: "API-Keys"
    },
    backupCodePage: {
      actionLabel__copied: "Kopiert!",
      actionLabel__copy: "Kopiere alle",
      actionLabel__download: "Laden Sie .txt herunter",
      actionLabel__print: "Drucken",
      infoText1: "Backup-Codes werden f\xFCr dieses Konto aktiviert.",
      infoText2: "Halten Sie die Backup-Codes geheim und bewahren Sie sie sicher auf. Sie k\xF6nnen Sicherungscodes neu generieren, wenn Sie vermuten, dass sie kompromittiert wurden.",
      subtitle__codelist: "Bewahren Sie die Codes sicher auf und halten Sie sie geheim.",
      successMessage: "Sicherungscodes sind jetzt aktiviert. Sie k\xF6nnen eines davon verwenden, um sich bei Ihrem Konto anzumelden, wenn Sie den Zugriff auf Ihr Authentifizierungsger\xE4t verlieren. Jeder Code kann nur einmal verwendet werden.",
      successSubtitle: "Sie k\xF6nnen diese Codes verwenden, um sich bei Ihrem Konto anzumelden, wenn Sie den Zugriff auf Ihr Authentifizierungsger\xE4t verlieren.",
      title: "Backup-Code-Verifizierung hinzuf\xFCgen",
      title__codelist: "Sicherungscodes"
    },
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: "Als Standard festlegen",
        actionLabel__remove: "Entfernen",
        add: "Neue Zahlungsmethode hinzuf\xFCgen",
        addSubtitle: "F\xFCgen Sie eine neue Zahlungsmethode hinzu.",
        cancelButton: "Abbrechen",
        formButtonPrimary__add: "Zahlungsmethode hinzuf\xFCgen",
        formButtonPrimary__pay: "{{amount}} bezahlen",
        payWithTestCardButton: "Mit Test-Kreditkarte bezahlen",
        removeResource: {
          messageLine1: "{{identifier}} wird von diesem Konto entfernt.",
          messageLine2: "In Zukunft k\xF6nnen Sie diese Zahlungsmethode nicht mehr verwenden. Alle laufenden Abonnements, die diese Zahlungsmethode verwenden, werden aufh\xF6ren zu funktionieren.",
          successMessage: "{{paymentSource}} wurde von diesem Konto entfernt.",
          title: "Zahlungsmethode entfernen"
        },
        title: "Zahlungsmethoden"
      },
      start: {
        headerTitle__payments: void 0,
        headerTitle__plans: "Pl\xE4ne",
        headerTitle__statements: "Abrechnungen",
        headerTitle__subscriptions: "Abonnements"
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: "Plan abonnieren",
        actionLabel__switchPlan: "Plan wechseln",
        title: "Abonnement"
      },
      subscriptionsSection: {
        actionLabel__default: "Verwalten"
      },
      switchPlansSection: {
        title: "Plan wechseln"
      },
      title: "Abrechnung"
    },
    connectedAccountPage: {
      formHint: "W\xE4hlen Sie einen Anbieter aus, um Ihr Konto zu verbinden.",
      formHint__noAccounts: "Es sind keine externen Kontoanbieter verf\xFCgbar.",
      removeResource: {
        messageLine1: "{{identifier}} wird aus diesem Konto entfernt.",
        messageLine2: "Sie k\xF6nnen dieses verbundene Konto nicht mehr verwenden und alle abh\xE4ngigen Funktionen funktionieren nicht mehr.",
        successMessage: "{{connectedAccount}} wurde aus Ihrem Konto entfernt.",
        title: "Verbundenes Konto entfernen"
      },
      socialButtonsBlockButton: "{{provider|titleize}}-Konto verbinden",
      successMessage: "Der Anbieter wurde Ihrem Konto hinzugef\xFCgt",
      title: "Verbundenes Konto hinzuf\xFCgen"
    },
    deletePage: {
      actionDescription: 'Geben Sie "Konto l\xF6schen" ein, um fortzufahren.',
      confirm: "Konto l\xF6schen",
      messageLine1: "Sind Sie sicher, dass Sie ihr Konto l\xF6schen m\xF6chten?",
      messageLine2: "Diese Aktion ist endg\xFCltig und kann nicht r\xFCckg\xE4ngig gemacht werden.",
      title: "Konto l\xF6schen"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "An diese E-Mail-Adresse wird eine E-Mail mit einem Best\xE4tigungscode gesendet.",
        formSubtitle: "Geben Sie den Best\xE4tigungscode ein, der an {{identifier}} gesendet wird",
        formTitle: "Verifizierungs-Schl\xFCssel",
        resendButton: "Code erneut senden",
        successMessage: "Die E-Mail-Adresse {{identifier}} wurde Ihrem Konto hinzugef\xFCgt."
      },
      emailLink: {
        formHint: "An diese E-Mail-Adresse wird eine E-Mail mit einem Best\xE4tigungslink gesendet.",
        formSubtitle: "Klicken Sie auf den Best\xE4tigungslink in der an {{identifier}} gesendeten E-Mail",
        formTitle: "Best\xE4tigungslink",
        resendButton: "Link erneut senden",
        successMessage: "Die E-Mail-Adresse {{identifier}} wurde Ihrem Konto hinzugef\xFCgt."
      },
      enterpriseSSOLink: {
        formButton: "Klicken Sie zum Anmelden",
        formSubtitle: "Schlie\xDFen Sie die Anmeldung mit {{identifier}} ab"
      },
      formHint: "Sie m\xFCssen diese E-Mail-Adresse verifizieren, bevor sie Ihrem Konto hinzugef\xFCgt werden kann.",
      removeResource: {
        messageLine1: "{{identifier}} wird aus diesem Konto entfernt.",
        messageLine2: "Sie k\xF6nnen sich nicht mehr mit dieser E-Mail-Adresse anmelden.",
        successMessage: "{{emailAddress}} wurde aus Ihrem Konto entfernt.",
        title: "E-Mail-Adresse entfernen"
      },
      title: "E-Mail-Adresse hinzuf\xFCgen",
      verifyTitle: "E-Mail Adresse verifizieren"
    },
    formButtonPrimary__add: "Add",
    formButtonPrimary__continue: "Fortsetzen",
    formButtonPrimary__finish: "Fertig",
    formButtonPrimary__remove: "Entfernen",
    formButtonPrimary__save: "Speichern",
    formButtonReset: "Zur\xFCcksetzen",
    mfaPage: {
      formHint: "W\xE4hlen Sie eine Methode aus.",
      title: "Aktivieren Sie Zweifaktor-Authentifizierung"
    },
    mfaPhoneCodePage: {
      backButton: "Vorhandene Nummer verwenden",
      primaryButton__addPhoneNumber: "F\xFCgen Sie eine Telefonnummer hinzu",
      removeResource: {
        messageLine1: "{{identifier}} erh\xE4lt bei der Anmeldung keine Best\xE4tigungscodes mehr.",
        messageLine2: "Ihr Konto ist m\xF6glicherweise nicht so sicher. Bist du dir sicher, dass du weitermachen willst?",
        successMessage: "SMS-Code-Best\xE4tigung in zwei Schritten wurde f\xFCr {{mfaPhoneCode}} entfernt",
        title: "Entfernen Sie die Best\xE4tigung in zwei Schritten"
      },
      subtitle__availablePhoneNumbers: "W\xE4hlen Sie eine Telefonnummer aus, um sich f\xFCr die Best\xE4tigung in zwei Schritten per SMS-Code zu registrieren.",
      subtitle__unavailablePhoneNumbers: "Es sind keine Telefonnummern verf\xFCgbar, um sich f\xFCr die SMS-Code-Best\xE4tigung in zwei Schritten zu registrieren.",
      successMessage1: "When signing in, you will need to enter a verification code sent to this phone number as an additional step.",
      successMessage2: "Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.",
      successTitle: "SMS code verification enabled",
      title: "SMS-Code-Best\xE4tigung hinzuf\xFCgen"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Scannen Sie stattdessen den QR-Code",
        buttonUnableToScan__nonPrimary: "QR-Code kann nicht gescannt werden?",
        infoText__ableToScan: "Richten Sie eine neue Anmeldemethode in Ihrer Authentifizierungs-App ein und scannen Sie den folgenden QR-Code, um ihn mit Ihrem Konto zu verkn\xFCpfen.",
        infoText__unableToScan: "Richten Sie eine neue Anmeldemethode in Ihrem Authentifikator ein und geben Sie den unten angegebenen Schl\xFCssel ein.",
        inputLabel__unableToScan1: "Stellen Sie sicher, dass zeitbasierte oder einmalige Passw\xF6rter aktiviert sind, und schlie\xDFen Sie dann die Verkn\xFCpfung Ihres Kontos ab.",
        inputLabel__unableToScan2: "Wenn Ihr Authentifikator TOTP-URIs unterst\xFCtzt, k\xF6nnen Sie alternativ auch die vollst\xE4ndige URI kopieren."
      },
      removeResource: {
        messageLine1: "Bei der Anmeldung sind keine Best\xE4tigungscodes von diesem Authentifikator mehr erforderlich.",
        messageLine2: "Ihr Konto ist m\xF6glicherweise nicht mehr so sicher. Sind Sie sich sicher, dass Sie fortfahren wollen?",
        successMessage: "Die zweistufige Verifizierung \xFCber die Authentifizierungs-App wurde entfernt.",
        title: "Entfernen Sie die Best\xE4tigung in zwei Schritten"
      },
      successMessage: "Die Best\xE4tigung in zwei Schritten ist jetzt aktiviert. Bei der Anmeldung m\xFCssen Sie als zus\xE4tzlichen Schritt einen Best\xE4tigungscode von diesem Authentifikator eingeben.",
      title: "Authentifizierungs-App hinzuf\xFCgen",
      verifySubtitle: "Geben Sie den von Ihrem Authentifikator generierten Best\xE4tigungscode ein",
      verifyTitle: "Verifizierungs-Schl\xFCssel"
    },
    mobileButton__menu: "Men\xFC",
    navbar: {
      account: "Profil",
      apiKeys: "API-Keys",
      billing: "Abrechnung",
      description: "Verwalten Sie Ihre Kontoinformationen.",
      security: "Sicherheit",
      title: "Benutzerkonto"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "{{name}} wird von diesem Konto entfernt.",
        title: "Passkey entfernen"
      },
      subtitle__rename: "Sie k\xF6nnen den Namen des Passkeys \xE4ndern, um ihn leichter zu finden.",
      title__rename: "Passkey umbenennen"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Es wird empfohlen, sich von allen anderen Ger\xE4ten abzumelden, die m\xF6glicherweise Ihr altes Passwort verwendet haben.",
      readonly: "Ihr Passwort kann derzeit nicht ge\xE4ndert werden, da Sie sich nur \xFCber die Enterprise-Verbindung anmelden k\xF6nnen.",
      successMessage__set: "Ihr Passwort wurde festgelegt.",
      successMessage__signOutOfOtherSessions: "Alle anderen Ger\xE4te wurden abgemeldet.",
      successMessage__update: "Dein Passwort wurde aktualisiert.",
      title__set: "Passwort festlegen",
      title__update: "Passwort \xE4ndern"
    },
    phoneNumberPage: {
      infoText: "An diese Telefonnummer wird eine SMS mit einem Best\xE4tigungslink gesendet.",
      removeResource: {
        messageLine1: "{{identifier}} wird aus diesem Konto entfernt.",
        messageLine2: "Sie k\xF6nnen sich nicht mehr mit dieser Telefonnummer anmelden.",
        successMessage: "{{phoneNumber}} wurde aus Ihrem Konto entfernt.",
        title: "Telefonnummer entfernen"
      },
      successMessage: "{{identifier}} wurde Ihrem Konto hinzugef\xFCgt.",
      title: "Telefonnummer hinzuf\xFCgen",
      verifySubtitle: "Enter the verification code sent to {{identifier}}",
      verifyTitle: "Verify phone number"
    },
    plansPage: {
      title: "Pl\xE4ne"
    },
    profilePage: {
      fileDropAreaHint: "Laden Sie ein JPG-, PNG-, GIF- oder WEBP-Bild hoch, das kleiner als 10 MB ist",
      imageFormDestructiveActionSubtitle: "Bild entfernen",
      imageFormSubtitle: "Bild hochladen",
      imageFormTitle: "Profilbild",
      readonly: "Your profile information has been provided by the enterprise connection and cannot be edited.",
      successMessage: "Ihr Profil wurde aktualisiert.",
      title: "Profil aktualisieren"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Vom Ger\xE4t abmelden",
        title: "Aktive Ger\xE4te"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Versuchen Sie es nochmal",
        actionLabel__reauthorize: "Jetzt autorisieren",
        destructiveActionTitle: "Entfernen",
        primaryButton: "Konto verbinden",
        subtitle__disconnected: "Ihr Konto ist derzeit getrennt. Bitte verbinden Sie es erneut.",
        subtitle__reauthorize: "The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues",
        title: "Verbundene Konten"
      },
      dangerSection: {
        deleteAccountButton: "Konto l\xF6schen",
        title: "Achtung"
      },
      emailAddressesSection: {
        destructiveAction: "E-Mail-Adresse entfernen",
        detailsAction__nonPrimary: "Als prim\xE4r festlegen",
        detailsAction__primary: "Verifizierung abschlie\xDFen",
        detailsAction__unverified: "Verifizierung abschlie\xDFen",
        primaryButton: "F\xFCgen Sie eine E-Mail-Adresse hinzu",
        title: "E-Mail-Adressen"
      },
      enterpriseAccountsSection: {
        title: "Unternehmens-Konten"
      },
      headerTitle__account: "Konto",
      headerTitle__security: "Sicherheit",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Codes neu generieren",
          headerTitle: "Backup-Codes",
          subtitle__regenerate: "Generieren Sie einen neuen Satz sicherer Backup-Codes. Alte Backup-Code werden gel\xF6scht und k\xF6nnen nicht mehr verwendet werden.",
          title__regenerate: "Backup-Codes neu generieren"
        },
        phoneCode: {
          actionLabel__setDefault: "Als Standard einstellen",
          destructiveActionLabel: "Telefonnummer entfernen"
        },
        primaryButton: "Aktivieren Sie die Zweifaktor-Authentifizierung",
        title: "Zweifaktor-Authentifizierung",
        totp: {
          destructiveActionTitle: "Entfernen",
          headerTitle: "Authentifizierungs-App"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Entfernen",
        menuAction__rename: "Umbenennen",
        primaryButton: "Passkey hinzuf\xFCgen",
        title: "Passkeys"
      },
      passwordSection: {
        primaryButton__setPassword: "Passwort festlegen",
        primaryButton__updatePassword: "Passwort \xE4ndern",
        title: "Passwort"
      },
      phoneNumbersSection: {
        destructiveAction: "Telefonnummer entfernen",
        detailsAction__nonPrimary: "Als prim\xE4r festlegen",
        detailsAction__primary: "Verifizierung abschlie\xDFen",
        detailsAction__unverified: "Verifizierung abschlie\xDFen",
        primaryButton: "F\xFCgen Sie eine Telefonnummer hinzu",
        title: "Telefonnummern"
      },
      profileSection: {
        primaryButton: "Profil bearbeiten",
        title: "Profil"
      },
      usernameSection: {
        primaryButton__setUsername: "Benutzernamen festlegen",
        primaryButton__updateUsername: "Benutzernamen \xE4ndern",
        title: "Nutzername"
      },
      web3WalletsSection: {
        destructiveAction: "Wallet entfernen",
        detailsAction__nonPrimary: "Als prim\xE4r festlegen",
        primaryButton: "Web3-Wallets",
        title: "Web3-Wallets"
      }
    },
    usernamePage: {
      successMessage: "Ihr Benutzername wurde aktualisiert.",
      title__set: "Benutzernamen aktualisieren",
      title__update: "Benutzernamen aktualisieren"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} wird aus diesem Konto entfernt.",
        messageLine2: "Sie k\xF6nnen sich nicht mehr mit diesem Web3-Wallet anmelden.",
        successMessage: "{{web3Wallet}} wurde aus Ihrem Konto entfernt.",
        title: "Entfernen Sie das Web3-Wallet"
      },
      subtitle__availableWallets: "W\xE4hlen Sie ein Web3-Wallet aus, um sich mit Ihrem Konto zu verbinden.",
      subtitle__unavailableWallets: "Es sind keine Web3-Wallets verf\xFCgbar.",
      successMessage: "Die Brieftasche wurde Ihrem Konto hinzugef\xFCgt.",
      title: "Web3-Wallet hinzuf\xFCgen",
      web3WalletButtonsBlockButton: "{{provider|titleize}}"
    }
  },
  waitlist: {
    start: {
      actionLink: "Jetzt anmelden",
      actionText: "Kein Zugang? Auf die Warteliste setzen!",
      formButton: "Zur Warteliste hinzuf\xFCgen",
      subtitle: "Es tut uns leid, aber derzeit sind keine Pl\xE4tze verf\xFCgbar.",
      title: "Warteliste beitreten"
    },
    success: {
      message: "Sie wurden erfolgreich auf die Warteliste gesetzt. Wir benachrichtigen Sie, sobald Pl\xE4tze verf\xFCgbar sind.",
      subtitle: "Vielen Dank f\xFCr Ihre Geduld. Sie erhalten eine Benachrichtigung, sobald der Zugang freigegeben wird.",
      title: "Erfolgreich auf die Warteliste gesetzt"
    }
  }
};
export {
  deDE
};
//# sourceMappingURL=de-DE.mjs.map