import type { TOTPJSON, TOTPResource } from '@clerk/types';
import { BaseResource } from './internal';
export declare class TOTP extends BaseResource implements TOTPResource {
    pathRoot: string;
    id: string;
    secret?: string;
    uri?: string;
    verified: boolean;
    backupCodes?: string[];
    updatedAt: Date | null;
    createdAt: Date | null;
    constructor(data: TOTPJSON);
    protected fromJSON(data: TOTPJSON | null): this;
}
