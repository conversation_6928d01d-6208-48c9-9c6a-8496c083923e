import { Button } from '../customizables';
import type { ElementDescriptor, ElementId } from '../customizables/elementDescriptors';
import type { PropsOfComponent, ThemableCssProp } from '../styledSystem';
type UsePaginationProps = {
    defaultPage?: number;
};
export declare const usePagination: (props?: UsePaginationProps) => {
    page: number;
    changePage: import("react").Dispatch<import("react").SetStateAction<number>>;
};
export type PageButtonProps = PropsOfComponent<typeof Button> & {
    isActive?: boolean;
    icon?: React.ComponentType | React.ReactElement;
    iconElementDescriptor?: ElementDescriptor;
    iconElementId?: ElementId;
    iconSx?: ThemableCssProp;
};
type PaginationProps = {
    page: number;
    count: number;
    rowInfo?: {
        startingRow: number;
        endingRow: number;
        allRowsCount: number;
    };
    siblingCount?: number;
    onChange?: (page: number) => void;
};
export declare const Pagination: (props: PaginationProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
