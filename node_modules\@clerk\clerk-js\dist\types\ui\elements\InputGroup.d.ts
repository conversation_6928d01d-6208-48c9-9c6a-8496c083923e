import { type ThemableCssProp } from '../styledSystem';
export declare const InputGroup: import("react").ForwardRefExoticComponent<Omit<Omit<import("../primitives").InputProps, "ref"> & import("react").RefAttributes<HTMLInputElement> & {
    elementDescriptor?: import("../customizables/elementDescriptors").ElementDescriptor | Array<import("../customizables/elementDescriptors").ElementDescriptor | undefined>;
    elementId?: import("../customizables/elementDescriptors").ElementId;
    css?: never;
    sx?: ThemableCssProp;
} & {
    groupPrefix?: string;
    groupSuffix?: string;
}, "ref"> & import("react").RefAttributes<HTMLInputElement>>;
