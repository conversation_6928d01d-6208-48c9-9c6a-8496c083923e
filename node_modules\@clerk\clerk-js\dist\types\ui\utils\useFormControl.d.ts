import type { ClerkAPIError } from '@clerk/types';
import type { HTMLInputTypeAttribute } from 'react';
import type { LocalizationKey } from '../localization';
type SelectOption = {
    value: string;
    label?: string;
};
type Transformer = (value: string) => string;
type Options = {
    isRequired?: boolean;
    placeholder?: string | LocalizationKey;
    options?: SelectOption[];
    transformer?: Transformer;
    defaultChecked?: boolean;
    infoText?: LocalizationKey | string;
} & ({
    label: string | LocalizationKey;
    validatePassword?: never;
    buildErrorMessage?: never;
    type?: Exclude<HTMLInputTypeAttribute, 'password' | 'radio'>;
    radioOptions?: never;
} | {
    label: string | LocalizationKey;
    type: Extract<HTMLInputTypeAttribute, 'password'>;
    validatePassword: boolean;
    buildErrorMessage?: (err: ClerkAPIError[]) => ClerkAPIError | string | undefined;
    radioOptions?: never;
} | {
    label: string | LocalizationKey;
    type: Extract<HTMLInputTypeAttribute, 'text'>;
    validatePassword?: never;
    buildErrorMessage?: (err: ClerkAPIError[]) => ClerkAPIError | string | undefined;
    radioOptions?: never;
} | {
    validatePassword?: never;
    buildErrorMessage?: never;
    type: Extract<HTMLInputTypeAttribute, 'radio'>;
    label?: string | LocalizationKey;
    radioOptions: {
        value: string;
        label: string | LocalizationKey;
        description?: string | LocalizationKey;
    }[];
});
type FieldStateProps<Id> = {
    id: Id;
    name: Id;
    value: string;
    checked?: boolean;
    onChange: React.ChangeEventHandler<HTMLInputElement>;
    onBlur: React.FocusEventHandler<HTMLInputElement>;
    onFocus: React.FocusEventHandler<HTMLInputElement>;
    feedback: string;
    feedbackType: FeedbackType;
    setError: (error: string | ClerkAPIError) => void;
    setWarning: (warning: string) => void;
    setSuccess: (message: string) => void;
    setInfo: (info: string) => void;
    setHasPassedComplexity: (b: boolean) => void;
    clearFeedback: () => void;
    hasPassedComplexity: boolean;
    isFocused: boolean;
    ignorePasswordManager?: boolean;
} & Omit<Options, 'defaultChecked'>;
export type FormControlState<Id = string> = FieldStateProps<Id> & {
    setError: (error: string | ClerkAPIError) => void;
    setSuccess: (message: string) => void;
    setInfo: (info: string) => void;
    setValue: (val: string | undefined) => void;
    setChecked: (isChecked: boolean) => void;
    clearFeedback: () => void;
    props: FieldStateProps<Id>;
};
export type FeedbackType = 'success' | 'error' | 'warning' | 'info';
export declare const useFormControl: <Id extends string>(id: Id, initialState: string, opts?: Options) => FormControlState<Id>;
type FormControlStateLike = Pick<FormControlState, 'id' | 'value' | 'checked' | 'type'>;
export declare const buildRequest: (fieldStates: Array<FormControlStateLike>) => Record<string, any>;
type DebouncedFeedback = {
    debounced: {
        feedback: string;
        feedbackType: FeedbackType;
    };
};
type DebouncingOption = {
    feedback?: string;
    feedbackType?: FeedbackType;
    isFocused?: boolean;
    delayInMs?: number;
};
export declare const useFormControlFeedback: (opts?: DebouncingOption) => DebouncedFeedback;
export {};
