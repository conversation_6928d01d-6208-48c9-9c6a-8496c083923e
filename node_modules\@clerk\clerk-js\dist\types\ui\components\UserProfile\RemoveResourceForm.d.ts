import type { PasskeyResource } from '@clerk/types';
import type { FormProps } from '@/ui/elements/FormContainer';
type RemoveEmailFormProps = FormProps & {
    emailId: string;
};
export declare const RemoveEmailForm: (props: RemoveEmailFormProps) => import("@emotion/react/jsx-runtime").JSX.Element | null;
type RemovePhoneFormProps = FormProps & {
    phoneId: string;
};
export declare const RemovePhoneForm: (props: RemovePhoneFormProps) => import("@emotion/react/jsx-runtime").JSX.Element | null;
type ConnectedAccountFormProps = FormProps & {
    accountId: string;
};
export declare const RemoveConnectedAccountForm: (props: ConnectedAccountFormProps) => import("@emotion/react/jsx-runtime").JSX.Element | null;
type RemoveWeb3WalletFormProps = FormProps & {
    walletId: string;
};
export declare const RemoveWeb3WalletForm: (props: RemoveWeb3WalletFormProps) => import("@emotion/react/jsx-runtime").JSX.Element | null;
type RemoveMfaPhoneCodeFormProps = FormProps & {
    phoneId: string;
};
export declare const RemoveMfaPhoneCodeForm: (props: RemoveMfaPhoneCodeFormProps) => import("@emotion/react/jsx-runtime").JSX.Element | null;
type RemoveMfaTOTPFormProps = FormProps;
export declare const RemoveMfaTOTPForm: (props: RemoveMfaTOTPFormProps) => import("@emotion/react/jsx-runtime").JSX.Element | null;
type RemovePasskeyFormProps = FormProps & {
    passkey: PasskeyResource;
};
export declare const RemovePasskeyForm: (props: RemovePasskeyFormProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
