export declare const SignIn: import("react").LazyExoticComponent<import("react").ComponentType<import("@clerk/types").SignInProps>>;
export declare const SignInModal: import("react").LazyExoticComponent<(props: import("@clerk/types").SignInModalProps) => JSX.Element>;
export declare const GoogleOneTap: import("react").LazyExoticComponent<import("react").ComponentType<import("@clerk/types").GoogleOneTapProps>>;
export declare const UserVerification: import("react").LazyExoticComponent<import("react").ComponentType<import("@clerk/types").__internal_UserVerificationProps>>;
export declare const UserVerificationModal: import("react").LazyExoticComponent<(props: import("@clerk/types").__internal_UserVerificationModalProps) => JSX.Element>;
export declare const SignUp: import("react").LazyExoticComponent<import("react").ComponentType<import("@clerk/types").SignUpProps>>;
export declare const SignUpModal: import("react").LazyExoticComponent<(props: import("@clerk/types").SignUpModalProps) => JSX.Element>;
export declare const UserButton: import("react").LazyExoticComponent<import("react").ComponentType<unknown>>;
export declare const UserProfile: import("react").LazyExoticComponent<(props: import("@clerk/types").UserProfileProps) => import("@emotion/react/jsx-runtime").JSX.Element>;
export declare const UserProfileModal: import("react").LazyExoticComponent<(props: import("@clerk/types").UserProfileModalProps) => JSX.Element>;
export declare const CreateOrganization: import("react").LazyExoticComponent<(props: unknown) => import("@emotion/react/jsx-runtime").JSX.Element>;
export declare const CreateOrganizationModal: import("react").LazyExoticComponent<(props: import("@clerk/types").CreateOrganizationModalProps) => JSX.Element>;
export declare const OrganizationProfile: import("react").LazyExoticComponent<(props: import("@clerk/types").OrganizationProfileProps) => import("@emotion/react/jsx-runtime").JSX.Element>;
export declare const OrganizationProfileModal: import("react").LazyExoticComponent<(props: import("@clerk/types").OrganizationProfileModalProps) => JSX.Element>;
export declare const OrganizationSwitcher: import("react").LazyExoticComponent<import("react").ComponentType<unknown>>;
export declare const OrganizationList: import("react").LazyExoticComponent<() => import("@emotion/react/jsx-runtime").JSX.Element>;
export declare const Waitlist: import("react").LazyExoticComponent<(props: unknown) => import("@emotion/react/jsx-runtime").JSX.Element>;
export declare const WaitlistModal: import("react").LazyExoticComponent<(props: import("@clerk/types").WaitlistModalProps) => JSX.Element>;
export declare const BlankCaptchaModal: import("react").LazyExoticComponent<typeof import("./../components/BlankCaptchaModal").BlankCaptchaModal>;
export declare const ImpersonationFab: import("react").LazyExoticComponent<import("react").ComponentType<unknown>>;
export declare const KeylessPrompt: import("react").LazyExoticComponent<(props: {
    claimUrl: string;
    copyKeysUrl: string;
    onDismiss: (() => Promise<unknown>) | undefined | null;
}) => import("@emotion/react/jsx-runtime").JSX.Element>;
export declare const PricingTable: import("react").LazyExoticComponent<(props: import("@clerk/types").PricingTableProps) => import("@emotion/react/jsx-runtime").JSX.Element>;
export declare const APIKeys: import("react").LazyExoticComponent<import("react").ComponentType<unknown>>;
export declare const Checkout: import("react").LazyExoticComponent<(props: import("@clerk/types").__internal_CheckoutProps) => import("@emotion/react/jsx-runtime").JSX.Element>;
export declare const PlanDetails: import("react").LazyExoticComponent<(props: import("@clerk/types").__internal_PlanDetailsProps) => import("@emotion/react/jsx-runtime").JSX.Element>;
export declare const OAuthConsent: import("react").LazyExoticComponent<(props: unknown) => import("@emotion/react/jsx-runtime").JSX.Element>;
export declare const SessionTasks: import("react").LazyExoticComponent<typeof import("../components/SessionTasks").SessionTask>;
export declare const preloadComponent: (component: unknown) => Promise<typeof import("../components/SessionTasks") | typeof import("./../components/SignUp") | typeof import("./../components/SignIn") | typeof import("./../components/GoogleOneTap") | typeof import("./../components/UserVerification") | typeof import("./../components/UserButton") | typeof import("./../components/UserProfile") | typeof import("./../components/CreateOrganization") | typeof import("./../components/OrganizationProfile") | typeof import("./../components/OrganizationSwitcher") | typeof import("./../components/OrganizationList") | typeof import("./../components/Waitlist") | typeof import("./../components/BlankCaptchaModal") | typeof import("./../components/ImpersonationFab") | typeof import("../components/KeylessPrompt") | typeof import("../components/PricingTable") | typeof import("../components/ApiKeys/ApiKeys") | typeof import("../components/Checkout") | typeof import("../components/Plans") | typeof import("../components/OAuthConsent/OAuthConsent")>;
export declare const ClerkComponents: {
    SignIn: import("react").LazyExoticComponent<import("react").ComponentType<import("@clerk/types").SignInProps>>;
    SignUp: import("react").LazyExoticComponent<import("react").ComponentType<import("@clerk/types").SignUpProps>>;
    UserButton: import("react").LazyExoticComponent<import("react").ComponentType<unknown>>;
    UserProfile: import("react").LazyExoticComponent<(props: import("@clerk/types").UserProfileProps) => import("@emotion/react/jsx-runtime").JSX.Element>;
    UserVerification: import("react").LazyExoticComponent<import("react").ComponentType<import("@clerk/types").__internal_UserVerificationProps>>;
    OrganizationSwitcher: import("react").LazyExoticComponent<import("react").ComponentType<unknown>>;
    OrganizationList: import("react").LazyExoticComponent<() => import("@emotion/react/jsx-runtime").JSX.Element>;
    OrganizationProfile: import("react").LazyExoticComponent<(props: import("@clerk/types").OrganizationProfileProps) => import("@emotion/react/jsx-runtime").JSX.Element>;
    CreateOrganization: import("react").LazyExoticComponent<(props: unknown) => import("@emotion/react/jsx-runtime").JSX.Element>;
    SignInModal: import("react").LazyExoticComponent<(props: import("@clerk/types").SignInModalProps) => JSX.Element>;
    SignUpModal: import("react").LazyExoticComponent<(props: import("@clerk/types").SignUpModalProps) => JSX.Element>;
    UserProfileModal: import("react").LazyExoticComponent<(props: import("@clerk/types").UserProfileModalProps) => JSX.Element>;
    OrganizationProfileModal: import("react").LazyExoticComponent<(props: import("@clerk/types").OrganizationProfileModalProps) => JSX.Element>;
    CreateOrganizationModal: import("react").LazyExoticComponent<(props: import("@clerk/types").CreateOrganizationModalProps) => JSX.Element>;
    UserVerificationModal: import("react").LazyExoticComponent<(props: import("@clerk/types").__internal_UserVerificationModalProps) => JSX.Element>;
    GoogleOneTap: import("react").LazyExoticComponent<import("react").ComponentType<import("@clerk/types").GoogleOneTapProps>>;
    Waitlist: import("react").LazyExoticComponent<(props: unknown) => import("@emotion/react/jsx-runtime").JSX.Element>;
    WaitlistModal: import("react").LazyExoticComponent<(props: import("@clerk/types").WaitlistModalProps) => JSX.Element>;
    BlankCaptchaModal: import("react").LazyExoticComponent<typeof import("./../components/BlankCaptchaModal").BlankCaptchaModal>;
    PricingTable: import("react").LazyExoticComponent<(props: import("@clerk/types").PricingTableProps) => import("@emotion/react/jsx-runtime").JSX.Element>;
    Checkout: import("react").LazyExoticComponent<(props: import("@clerk/types").__internal_CheckoutProps) => import("@emotion/react/jsx-runtime").JSX.Element>;
    PlanDetails: import("react").LazyExoticComponent<(props: import("@clerk/types").__internal_PlanDetailsProps) => import("@emotion/react/jsx-runtime").JSX.Element>;
    APIKeys: import("react").LazyExoticComponent<import("react").ComponentType<unknown>>;
    OAuthConsent: import("react").LazyExoticComponent<(props: unknown) => import("@emotion/react/jsx-runtime").JSX.Element>;
};
export type ClerkComponentName = keyof typeof ClerkComponents;
