"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["82"],{1800:function(e,t,i){i.d(t,{b:()=>d});var a=i(9109),l=i(9655),n=i(8969),o=i(1576),s=i(9541),r=i(4174),c=i(4676);function d({title:e,arrowButtonText:t,arrowButtonEmptyText:i}){let{handleSelectPlan:d,captionForSubscription:u,canManageSubscription:h}=(0,o.usePlansContext)(),p=(0,o.useSubscriberTypeLocalizationRoot)(),g=(0,o.useSubscriberTypeContext)(),{data:b}=(0,o.useSubscriptions)(),y=(0,n.N2)(e=>e({permission:"org:sys_billing:manage"})||"user"===g),{navigate:z}=(0,c.useRouter)(),m=(e,t)=>{d({mode:"modal",plan:e.plan,planPeriod:e.planPeriod,event:t})},x=b.sort((e,t)=>"active"===e.status&&"active"!==t.status?-1:("active"===t.status&&e.status,1));return(0,a.BX)(l.zd.Root,{id:"subscriptionsList",title:e,centered:!1,sx:e=>({borderTop:"none",paddingTop:e.space.$1}),children:[b.length>0&&(0,a.BX)(s.Table,{tableHeadVisuallyHidden:!0,children:[(0,a.tZ)(s.Thead,{children:(0,a.BX)(s.Tr,{children:[(0,a.tZ)(s.Th,{localizationKey:(0,s.localizationKeys)(`${p}.billingPage.subscriptionsListSection.tableHeader__plan`)}),(0,a.tZ)(s.Th,{localizationKey:(0,s.localizationKeys)(`${p}.billingPage.subscriptionsListSection.tableHeader__startDate`)}),(0,a.tZ)(s.Th,{localizationKey:(0,s.localizationKeys)(`${p}.billingPage.subscriptionsListSection.tableHeader__edit`)})]})}),(0,a.tZ)(s.Tbody,{children:x.map(e=>(0,a.BX)(s.Tr,{children:[(0,a.tZ)(s.Td,{children:(0,a.BX)(s.Col,{gap:1,children:[(0,a.BX)(s.Flex,{align:"center",gap:1,children:[(0,a.tZ)(s.Icon,{icon:r.ou,sx:e=>({width:e.sizes.$4,height:e.sizes.$4,opacity:e.opacity.$inactive})}),(0,a.tZ)(s.Text,{variant:"subtitle",sx:e=>({marginRight:e.sizes.$1}),children:e.plan.name}),x.length>1||e.canceledAt?(0,a.tZ)(s.Badge,{colorScheme:"active"===e.status?"secondary":"primary",localizationKey:"active"===e.status?(0,s.localizationKeys)("badge__activePlan"):(0,s.localizationKeys)("badge__upcomingPlan")}):null]}),(!e.plan.isDefault||"upcoming"===e.status)&&(0,a.tZ)(s.Text,{variant:"caption",colorScheme:"secondary",localizationKey:u(e)})]})}),(0,a.tZ)(s.Td,{sx:e=>({textAlign:"right"}),children:(0,a.BX)(s.Text,{variant:"subtitle",children:[e.plan.currencySymbol,"annual"===e.planPeriod?e.plan.annualAmountFormatted:e.plan.amountFormatted,(e.plan.amount>0||e.plan.annualAmount>0)&&(0,a.tZ)(s.Span,{sx:e=>({color:e.colors.$colorTextSecondary,textTransform:"lowercase",":before":{content:'"/"',marginInline:e.space.$1}}),localizationKey:"annual"===e.planPeriod?(0,s.localizationKeys)("commerce.year"):(0,s.localizationKeys)("commerce.month")})]})}),(0,a.tZ)(s.Td,{sx:e=>({textAlign:"right"}),children:h({subscription:e})&&e.id&&!e.plan.isDefault&&(0,a.tZ)(s.Button,{"aria-label":"Manage subscription",onClick:t=>m(e,t),variant:"bordered",colorScheme:"secondary",isDisabled:!y,sx:e=>({width:e.sizes.$6,height:e.sizes.$6}),children:(0,a.tZ)(s.Icon,{icon:r.tc,sx:e=>({width:e.sizes.$4,height:e.sizes.$4,opacity:e.opacity.$inactive})})})})]},e.id))})]}),(0,a.tZ)(l.zd.ArrowButton,{id:"subscriptionsList",textLocalizationKey:b.length>0?t:i,sx:[e=>({justifyContent:"start",height:e.sizes.$8})],leftIcon:b.length>0?r.Ic:r.v3,leftIconSx:e=>({width:e.sizes.$4,height:e.sizes.$4}),onClick:()=>void z("plans")})]})}},6793:function(e,t,i){i.r(t),i.d(t,{BillingPage:()=>z});var a=i(9109),l=i(4455),n=i(2672),o=i(2654),s=i(708),r=i(1576),c=i(9541),d=i(3746),u=i(2264),h=i(6054),p=i(5515),g=i(1800);let b={0:"subscriptions",1:"statements",2:"payments"},y=(0,n.withCardStateProvider)(()=>{let e=(0,n.useCardState)(),{selectedTab:t,handleTabChange:i}=(0,d.x)(b);return(0,a.tZ)(c.Col,{elementDescriptor:c.descriptors.page,sx:e=>({gap:e.space.$8,color:e.colors.$colorText}),children:(0,a.BX)(c.Col,{elementDescriptor:c.descriptors.profilePage,elementId:c.descriptors.profilePage.setId("billing"),gap:4,children:[(0,a.tZ)(o.h.Root,{children:(0,a.tZ)(o.h.Title,{localizationKey:(0,c.localizationKeys)("userProfile.billingPage.title"),textVariant:"h2"})}),(0,a.tZ)(l.Z.Alert,{children:e.error}),(0,a.BX)(s.mQ,{value:t,onChange:i,children:[(0,a.BX)(s.dr,{sx:e=>({gap:e.space.$6}),children:[(0,a.tZ)(s.OK,{localizationKey:(0,c.localizationKeys)("userProfile.billingPage.start.headerTitle__subscriptions")}),(0,a.tZ)(s.OK,{localizationKey:(0,c.localizationKeys)("userProfile.billingPage.start.headerTitle__statements")}),(0,a.tZ)(s.OK,{localizationKey:(0,c.localizationKeys)("userProfile.billingPage.start.headerTitle__payments")})]}),(0,a.BX)(s.nP,{children:[(0,a.BX)(s.x4,{sx:e=>({width:"100%",flexDirection:"column"}),children:[(0,a.tZ)(g.b,{title:(0,c.localizationKeys)("userProfile.billingPage.subscriptionsListSection.title"),arrowButtonText:(0,c.localizationKeys)("userProfile.billingPage.subscriptionsListSection.actionLabel__switchPlan"),arrowButtonEmptyText:(0,c.localizationKeys)("userProfile.billingPage.subscriptionsListSection.actionLabel__newSubscription")}),(0,a.tZ)(h.Hw,{})]}),(0,a.tZ)(s.x4,{sx:{width:"100%"},children:(0,a.tZ)(p.f,{})}),(0,a.tZ)(s.x4,{sx:{width:"100%"},children:(0,a.tZ)(u.B,{})})]})]})]})})}),z=()=>(0,a.tZ)(r.SubscriberTypeContext.Provider,{value:"user",children:(0,a.tZ)(y,{})})}}]);