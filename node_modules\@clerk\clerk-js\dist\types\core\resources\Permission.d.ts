import type { PermissionJSON, PermissionResource } from '@clerk/types';
import { BaseResource } from './internal';
export declare class Permission extends BaseResource implements PermissionResource {
    id: string;
    key: string;
    name: string;
    description: string;
    type: 'system' | 'user';
    createdAt: Date;
    updatedAt: Date;
    constructor(data: PermissionJSON);
    protected fromJSON(data: PermissionJSON | null): this;
}
