import type { ThemableCssProp } from '@/ui/styledSystem';
type DevModeOverlayProps = {
    gradient?: number;
};
export declare const DevModeOverlay: (props: DevModeOverlayProps) => import("@emotion/react/jsx-runtime").JSX.Element | null;
type DevModeNoticeProps = {
    sx?: ThemableCssProp;
};
export declare const DevModeNotice: (props: DevModeNoticeProps) => import("@emotion/react/jsx-runtime").JSX.Element | null;
export {};
