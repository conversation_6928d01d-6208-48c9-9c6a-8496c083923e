import type { AttemptFirstFactorParams, AttemptSecondFactorParams, AuthenticateWithPasskeyParams, AuthenticateWithPopupParams, AuthenticateWithRedirectParams, AuthenticateWithWeb3Params, CreateEmailLinkFlowReturn, PrepareFirstFactorParams, PrepareSecondFactorParams, ResetPasswordParams, SignInCreateParams, SignInFirstFactor, SignInIdentifier, SignInJSON, SignInJSONSnapshot, SignInResource, SignInSecondFactor, SignInStartEmailLinkFlowParams, SignInStatus, VerificationResource } from '@clerk/types';
import { createValidatePassword } from '../../utils/passwords/password';
import { BaseResource, UserData } from './internal';
export declare class SignIn extends BaseResource implements SignInResource {
    pathRoot: string;
    id?: string;
    status: SignInStatus | null;
    supportedIdentifiers: SignInIdentifier[];
    supportedFirstFactors: SignInFirstFactor[] | null;
    supportedSecondFactors: SignInSecondFactor[] | null;
    firstFactorVerification: VerificationResource;
    secondFactorVerification: VerificationResource;
    identifier: string | null;
    createdSessionId: string | null;
    userData: UserData;
    constructor(data?: SignInJSON | SignInJSONSnapshot | null);
    create: (params: SignInCreateParams) => Promise<this>;
    resetPassword: (params: ResetPasswordParams) => Promise<SignInResource>;
    prepareFirstFactor: (factor: PrepareFirstFactorParams) => Promise<SignInResource>;
    attemptFirstFactor: (attemptFactor: AttemptFirstFactorParams) => Promise<SignInResource>;
    createEmailLinkFlow: () => CreateEmailLinkFlowReturn<SignInStartEmailLinkFlowParams, SignInResource>;
    prepareSecondFactor: (params: PrepareSecondFactorParams) => Promise<SignInResource>;
    attemptSecondFactor: (params: AttemptSecondFactorParams) => Promise<SignInResource>;
    private authenticateWithRedirectOrPopup;
    authenticateWithRedirect: (params: AuthenticateWithRedirectParams) => Promise<void>;
    authenticateWithPopup: (params: AuthenticateWithPopupParams) => Promise<void>;
    authenticateWithWeb3: (params: AuthenticateWithWeb3Params) => Promise<SignInResource>;
    authenticateWithMetamask: () => Promise<SignInResource>;
    authenticateWithCoinbaseWallet: () => Promise<SignInResource>;
    authenticateWithOKXWallet: () => Promise<SignInResource>;
    authenticateWithPasskey: (params?: AuthenticateWithPasskeyParams) => Promise<SignInResource>;
    validatePassword: ReturnType<typeof createValidatePassword>;
    protected fromJSON(data: SignInJSON | SignInJSONSnapshot | null): this;
    __internal_toSnapshot(): SignInJSONSnapshot;
}
