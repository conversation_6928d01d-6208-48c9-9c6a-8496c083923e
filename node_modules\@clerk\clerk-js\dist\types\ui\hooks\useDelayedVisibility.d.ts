/**
 * Utility hook for delaying mounting of components for enter and exit animations.
 * Delays to update the state when is switched from/to undefined.
 * Immediate change for in-between changes
 */
export declare function useDelayedVisibility<T>(valueToDelay: T, delayInMs: number): T | undefined;
export declare function useFieldMessageVisibility<T = string>(fieldMessage: T, delayInMs: number): T | undefined;
