// src/fi-FI.ts
var fiFI = {
  locale: "fi-FI",
  backButton: "<PERSON><PERSON><PERSON>",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Oletus",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Toinen j\xE4ljitelty laite",
  badge__primary: "Ensisijainen",
  badge__renewsAt: void 0,
  badge__requiresAction: "Vaaditaan toimia",
  badge__startsAt: void 0,
  badge__thisDevice: "T\xE4m\xE4 laite",
  badge__unverified: "Vahvistamaton",
  badge__upcomingPlan: void 0,
  badge__userDevice: "K\xE4ytt\xE4j\xE4n laite",
  badge__you: "Sin\xE4",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Luo organisaatio",
    invitePage: {
      formButtonReset: "Ohita"
    },
    title: "Luo organisaatio"
  },
  dates: {
    lastDay: "Eilen klo {{ date | timeString('fi-FI') }}",
    next6Days: "{{ date | weekday('fi-FI','long') }} klo {{ date | timeString('fi-FI') }}",
    nextDay: "Huomenna klo {{ date | timeString('fi-FI') }}",
    numeric: "{{ date | numeric('fi-FI') }}",
    previous6Days: "Viime {{ date | weekday('fi-FI','long') }} klo {{ date | timeString('fi-FI') }}",
    sameDay: "T\xE4n\xE4\xE4n klo {{ date | timeString('fi-FI') }}"
  },
  dividerText: "tai",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "K\xE4yt\xE4 toista tapaa",
  footerPageLink__help: "Apua",
  footerPageLink__privacy: "Tietosuoja",
  footerPageLink__terms: "K\xE4ytt\xF6ehdot",
  formButtonPrimary: "Jatka",
  formButtonPrimary__verify: "Vahvista",
  formFieldAction__forgotPassword: "Unohditko salasanasi?",
  formFieldError__matchingPasswords: "Salasanat t\xE4sm\xE4\xE4v\xE4t.",
  formFieldError__notMatchingPasswords: "Salasanat eiv\xE4t t\xE4sm\xE4\xE4.",
  formFieldError__verificationLinkExpired: "Vahvistuslinkki on vanhentunut. Pyyd\xE4 uusi linkki.",
  formFieldHintText__optional: "Valinnainen",
  formFieldHintText__slug: "Slug on luettava tunniste, joka on oltava yksil\xF6llinen. Sit\xE4 k\xE4ytet\xE4\xE4n usein URL-osoitteissa.",
  formFieldInputPlaceholder__backupCode: void 0,
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Delete account",
  formFieldInputPlaceholder__emailAddress: void 0,
  formFieldInputPlaceholder__emailAddress_username: void 0,
  formFieldInputPlaceholder__emailAddresses: "<EMAIL>, <EMAIL>",
  formFieldInputPlaceholder__firstName: void 0,
  formFieldInputPlaceholder__lastName: void 0,
  formFieldInputPlaceholder__organizationDomain: void 0,
  formFieldInputPlaceholder__organizationDomainEmailAddress: void 0,
  formFieldInputPlaceholder__organizationName: void 0,
  formFieldInputPlaceholder__organizationSlug: "minun-org",
  formFieldInputPlaceholder__password: void 0,
  formFieldInputPlaceholder__phoneNumber: void 0,
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__automaticInvitations: "Ota automaattiset kutsut k\xE4ytt\xF6\xF6n t\xE4lle verkkotunnukselle",
  formFieldLabel__backupCode: "Varakoodi",
  formFieldLabel__confirmDeletion: "Vahvistus",
  formFieldLabel__confirmPassword: "Vahvista salasana",
  formFieldLabel__currentPassword: "Nykyinen salasana",
  formFieldLabel__emailAddress: "S\xE4hk\xF6postiosoite",
  formFieldLabel__emailAddress_username: "S\xE4hk\xF6postiosoite tai k\xE4ytt\xE4j\xE4nimi",
  formFieldLabel__emailAddresses: "S\xE4hk\xF6postiosoitteet",
  formFieldLabel__firstName: "Etunimi",
  formFieldLabel__lastName: "Sukunimi",
  formFieldLabel__newPassword: "Uusi salasana",
  formFieldLabel__organizationDomain: "Verkkotunnus",
  formFieldLabel__organizationDomainDeletePending: "Poista odottavat kutsut ja ehdotukset",
  formFieldLabel__organizationDomainEmailAddress: "Vahvistuss\xE4hk\xF6postiosoite",
  formFieldLabel__organizationDomainEmailAddressDescription: "Sy\xF6t\xE4 s\xE4hk\xF6postiosoite t\xE4lle verkkotunnukselle saadaksesi koodin ja vahvistaaksesi t\xE4m\xE4n verkkotunnuksen.",
  formFieldLabel__organizationName: "Nimi",
  formFieldLabel__organizationSlug: "Slug",
  formFieldLabel__passkeyName: "P\xE4\xE4syavaimen nimi",
  formFieldLabel__password: "Salasana",
  formFieldLabel__phoneNumber: "Puhelinnumero",
  formFieldLabel__role: "Rooli",
  formFieldLabel__signOutOfOtherSessions: "Kirjaudu ulos kaikista muista laitteista",
  formFieldLabel__username: "K\xE4ytt\xE4j\xE4nimi",
  impersonationFab: {
    action__signOut: "Kirjaudu ulos",
    title: "Kirjautuneena k\xE4ytt\xE4j\xE4n\xE4 {{identifier}}"
  },
  maintenanceMode: "Olemme t\xE4ll\xE4 hetkell\xE4 huoltotilassa, mutta \xE4l\xE4 huoli, se ei kest\xE4 kauempaa kuin muutama minuutti.",
  membershipRole__admin: "Yll\xE4pit\xE4j\xE4",
  membershipRole__basicMember: "J\xE4sen",
  membershipRole__guestMember: "Vieras",
  organizationList: {
    action__createOrganization: "Luo organisaatio",
    action__invitationAccept: "Liity",
    action__suggestionsAccept: "Pyyd\xE4 liittymist\xE4",
    createOrganization: "Luo organisaatio",
    invitationAcceptedLabel: "Liittynyt",
    subtitle: "jatkaaksesi kohteeseen {{applicationName}}",
    suggestionsAcceptedLabel: "Odottaa hyv\xE4ksynt\xE4\xE4",
    title: "Valitse tili",
    titleWithoutPersonal: "Valitse organisaatio"
  },
  organizationProfile: {
    badge__automaticInvitation: "Automaattiset kutsut",
    badge__automaticSuggestion: "Automaattiset ehdotukset",
    badge__manualInvitation: "Ei automaattista liittymist\xE4",
    badge__unverified: "Vahvistamaton",
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Lis\xE4\xE4 verkkotunnus vahvistaaksesi. T\xE4m\xE4n verkkotunnuksen s\xE4hk\xF6postiosoitteilla varustetut k\xE4ytt\xE4j\xE4t voivat liitty\xE4 organisaatioon automaattisesti tai pyyt\xE4\xE4 liittymist\xE4.",
      title: "Lis\xE4\xE4 verkkotunnus"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Kutsuja ei voitu l\xE4hett\xE4\xE4. Seuraaville s\xE4hk\xF6postiosoitteille on jo odottavia kutsuja: {{email_addresses}}.",
      formButtonPrimary__continue: "L\xE4het\xE4 kutsuja",
      selectDropdown__role: "Valitse rooli",
      subtitle: "Kirjoita tai liit\xE4 yksi tai useampi s\xE4hk\xF6postiosoite, erotettuna v\xE4lily\xF6nneill\xE4 tai pilkuilla.",
      successMessage: "Kutsut l\xE4hetetty onnistuneesti",
      title: "Kutsu uusia j\xE4seni\xE4"
    },
    membersPage: {
      action__invite: "Kutsu",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "Poista j\xE4sen",
        tableHeader__actions: void 0,
        tableHeader__joined: "Liittynyt",
        tableHeader__role: "Rooli",
        tableHeader__user: "K\xE4ytt\xE4j\xE4"
      },
      detailsTitle__emptyRow: "Ei j\xE4seni\xE4 n\xE4ytett\xE4v\xE4ksi",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Kutsu k\xE4ytt\xE4ji\xE4 yhdist\xE4m\xE4ll\xE4 s\xE4hk\xF6postiverkkotunnus organisaatioosi. Kaikki, jotka rekister\xF6ityv\xE4t vastaavalla s\xE4hk\xF6postiverkkotunnuksella, voivat liitty\xE4 organisaatioon milloin tahansa.",
          headerTitle: "Automaattiset kutsut",
          primaryButton: "Hallitse vahvistettuja verkkotunnuksia"
        },
        table__emptyRow: "Ei kutsuja n\xE4ytett\xE4v\xE4ksi"
      },
      invitedMembersTab: {
        menuAction__revoke: "Peruuta kutsu",
        tableHeader__invited: "Kutsuttu"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "K\xE4ytt\xE4j\xE4t, jotka rekister\xF6ityv\xE4t vastaavalla s\xE4hk\xF6postiverkkotunnuksella, voivat n\xE4hd\xE4 ehdotuksen liittymisest\xE4 organisaatioosi.",
          headerTitle: "Automaattiset ehdotukset",
          primaryButton: "Hallitse vahvistettuja verkkotunnuksia"
        },
        menuAction__approve: "Hyv\xE4ksy",
        menuAction__reject: "Hylk\xE4\xE4",
        tableHeader__requested: "Pyydetty p\xE4\xE4sy",
        table__emptyRow: "Ei pyynt\xF6j\xE4 n\xE4ytett\xE4v\xE4ksi"
      },
      start: {
        headerTitle__invitations: "Kutsut",
        headerTitle__members: "J\xE4senet",
        headerTitle__requests: "Pyynt\xF6j\xE4"
      }
    },
    navbar: {
      billing: void 0,
      description: "Hallitse organisaatiotasi.",
      general: "Yleinen",
      members: "J\xE4senet",
      title: "Organisaatio"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: 'Kirjoita "{{organizationName}}" jatkaaksesi.',
          messageLine1: "Oletko varma, ett\xE4 haluat poistaa t\xE4m\xE4n organisaation?",
          messageLine2: "T\xE4m\xE4 toiminto on pysyv\xE4 ja peruuttamaton.",
          successMessage: "Olet poistanut organisaation.",
          title: "Poista organisaatio"
        },
        leaveOrganization: {
          actionDescription: 'Kirjoita "{{organizationName}}" jatkaaksesi.',
          messageLine1: "Oletko varma, ett\xE4 haluat poistua t\xE4st\xE4 organisaatiosta? Menet\xE4t p\xE4\xE4syn t\xE4h\xE4n organisaatioon ja sen sovelluksiin.",
          messageLine2: "T\xE4m\xE4 toiminto on pysyv\xE4 ja peruuttamaton.",
          successMessage: "Olet poistunut organisaatiosta.",
          title: "Poistu organisaatiosta"
        },
        title: "Vaara"
      },
      domainSection: {
        menuAction__manage: "Hallitse",
        menuAction__remove: "Poista",
        menuAction__verify: "Vahvista",
        primaryButton: "Lis\xE4\xE4 verkkotunnus",
        subtitle: "Salli k\xE4ytt\xE4jien liitty\xE4 organisaatioon automaattisesti tai pyyt\xE4\xE4 liittymist\xE4 vahvistetun s\xE4hk\xF6postiverkkotunnuksen perusteella.",
        title: "Vahvistetut verkkotunnukset"
      },
      successMessage: "Organisaatiota on p\xE4ivitetty.",
      title: "P\xE4ivit\xE4 profiili"
    },
    removeDomainPage: {
      messageLine1: "S\xE4hk\xF6postiverkkotunnus {{domain}} poistetaan.",
      messageLine2: "K\xE4ytt\xE4j\xE4t eiv\xE4t voi liitty\xE4 organisaatioon automaattisesti t\xE4m\xE4n j\xE4lkeen.",
      successMessage: "{{domain}} on poistettu.",
      title: "Poista verkkotunnus"
    },
    start: {
      headerTitle__general: "Yleinen",
      headerTitle__members: "J\xE4senet",
      profileSection: {
        primaryButton: "P\xE4ivit\xE4 profiili",
        title: "Organisaation profiili",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "T\xE4m\xE4n verkkotunnuksen poistaminen vaikuttaa kutsuttuihin k\xE4ytt\xE4jiin.",
        removeDomainActionLabel__remove: "Poista verkkotunnus",
        removeDomainSubtitle: "Poista t\xE4m\xE4 verkkotunnus vahvistetuista verkkotunnuksistasi",
        removeDomainTitle: "Poista verkkotunnus"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "K\xE4ytt\xE4j\xE4t kutsutaan automaattisesti liittym\xE4\xE4n organisaatioon rekister\xF6ityess\xE4\xE4n ja voivat liitty\xE4 milloin tahansa.",
        automaticInvitationOption__label: "Automaattiset kutsut",
        automaticSuggestionOption__description: "K\xE4ytt\xE4j\xE4t saavat ehdotuksen liittymisest\xE4, mutta heid\xE4n on saatava yll\xE4pit\xE4j\xE4n hyv\xE4ksynt\xE4 ennen kuin he voivat liitty\xE4 organisaatioon.",
        automaticSuggestionOption__label: "Automaattiset ehdotukset",
        calloutInfoLabel: "Enrollment-tilan muuttaminen vaikuttaa vain uusiin k\xE4ytt\xE4jiin.",
        calloutInvitationCountLabel: "K\xE4ytt\xE4jille l\xE4hetetyt odottavat kutsut: {{count}}",
        calloutSuggestionCountLabel: "K\xE4ytt\xE4jille l\xE4hetetyt odottavat ehdotukset: {{count}}",
        manualInvitationOption__description: "K\xE4ytt\xE4ji\xE4 voidaan kutsua vain manuaalisesti organisaatioon.",
        manualInvitationOption__label: "Ei automaattista liittymist\xE4",
        subtitle: "Valitse, miten t\xE4m\xE4n verkkotunnuksen k\xE4ytt\xE4j\xE4t voivat liitty\xE4 organisaatioon."
      },
      start: {
        headerTitle__danger: "Vaara",
        headerTitle__enrollment: "Liittymisvaihtoehdot"
      },
      subtitle: "Verkkotunnus {{domain}} on nyt vahvistettu. Jatka valitsemalla liittymistila.",
      title: "P\xE4ivit\xE4 {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Sy\xF6t\xE4 s\xE4hk\xF6postiosoitteeseesi l\xE4hetetty vahvistuskoodi",
      formTitle: "Vahvistuskoodi",
      resendButton: "Etk\xF6 saanut koodia? L\xE4het\xE4 uudelleen",
      subtitle: "Verkkotunnus {{domainName}} on vahvistettava s\xE4hk\xF6postitse.",
      subtitleVerificationCodeScreen: "Vahvistuskoodi l\xE4hetettiin osoitteeseen {{emailAddress}}. Sy\xF6t\xE4 koodi jatkaaksesi.",
      title: "Vahvista verkkotunnus"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Luo organisaatio",
    action__invitationAccept: "Liity",
    action__manageOrganization: "Hallitse",
    action__suggestionsAccept: "Pyyd\xE4 liittymist\xE4",
    notSelected: "Ei valittua organisaatiota",
    personalWorkspace: "Henkil\xF6kohtainen tili",
    suggestionsAcceptedLabel: "Odottaa hyv\xE4ksynt\xE4\xE4"
  },
  paginationButton__next: "Seuraava",
  paginationButton__previous: "Edellinen",
  paginationRowText__displaying: "N\xE4ytet\xE4\xE4n",
  paginationRowText__of: "yhteens\xE4",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Lis\xE4\xE4 tili",
      action__signOutAll: "Kirjaudu ulos kaikista tileist\xE4",
      subtitle: "Valitse tili, jolla haluat jatkaa.",
      title: "Valitse tili"
    },
    alternativeMethods: {
      actionLink: "Hanki apua",
      actionText: "Eik\xF6 sinulla ole n\xE4it\xE4?",
      blockButton__backupCode: "K\xE4yt\xE4 varakoodia",
      blockButton__emailCode: "L\xE4het\xE4 koodi s\xE4hk\xF6postitse {{identifier}}",
      blockButton__emailLink: "L\xE4het\xE4 linkki s\xE4hk\xF6postitse {{identifier}}",
      blockButton__passkey: "Kirjaudu sis\xE4\xE4n p\xE4\xE4syavaimellasi",
      blockButton__password: "Kirjaudu sis\xE4\xE4n salasanallasi",
      blockButton__phoneCode: "L\xE4het\xE4 SMS-koodi osoitteeseen {{identifier}}",
      blockButton__totp: "K\xE4yt\xE4 todennussovellustasi",
      getHelp: {
        blockButton__emailSupport: "S\xE4hk\xF6postituki",
        content: "Jos sinulla on vaikeuksia kirjautua tilillesi, l\xE4het\xE4 meille s\xE4hk\xF6postia, niin autamme sinua palauttamaan p\xE4\xE4syn tiliisi mahdollisimman pian.",
        title: "Hanki apua"
      },
      subtitle: "Ongelmia? Voit kirjautua sis\xE4\xE4n mill\xE4 tahansa n\xE4ist\xE4 tavoista.",
      title: "K\xE4yt\xE4 toista tapaa"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "Varakoodi on se, jonka sait asettaessasi kaksivaiheisen todennuksen.",
      title: "Sy\xF6t\xE4 varakoodi"
    },
    emailCode: {
      formTitle: "Vahvistuskoodi",
      resendButton: "Etk\xF6 saanut koodia? L\xE4het\xE4 uudelleen",
      subtitle: "jatkaaksesi kohteeseen {{applicationName}}",
      title: "Tarkista s\xE4hk\xF6postisi"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Jatkaaksesi avaa vahvistuslinkki laitteella ja selaimella, josta aloitit kirjautumisen",
        title: "Vahvistuslinkki on virheellinen t\xE4lle laitteelle"
      },
      expired: {
        subtitle: "Palaa alkuper\xE4iseen v\xE4lilehteen jatkaaksesi.",
        title: "T\xE4m\xE4 vahvistuslinkki on vanhentunut"
      },
      failed: {
        subtitle: "Palaa alkuper\xE4iseen v\xE4lilehteen jatkaaksesi.",
        title: "T\xE4m\xE4 vahvistuslinkki on virheellinen"
      },
      formSubtitle: "K\xE4yt\xE4 s\xE4hk\xF6postiisi l\xE4hetetty\xE4 vahvistuslinkki\xE4",
      formTitle: "Vahvistuslinkki",
      loading: {
        subtitle: "Sinut ohjataan pian",
        title: "Kirjaudutaan sis\xE4\xE4n..."
      },
      resendButton: "Et saanut linkki\xE4? L\xE4het\xE4 uudelleen",
      subtitle: "jatkaaksesi kohteeseen {{applicationName}}",
      title: "Tarkista s\xE4hk\xF6postisi",
      unusedTab: {
        title: "Voit sulkea t\xE4m\xE4n v\xE4lilehden"
      },
      verified: {
        subtitle: "Sinut ohjataan pian",
        title: "Kirjautuminen onnistui"
      },
      verifiedSwitchTab: {
        subtitle: "Palaa alkuper\xE4iseen v\xE4lilehteen jatkaaksesi",
        subtitleNewTab: "Palaa uuteen v\xE4lilehteen jatkaaksesi",
        titleNewTab: "Kirjautunut toiseen v\xE4lilehteen"
      }
    },
    forgotPassword: {
      formTitle: "Nollaa salasana",
      resendButton: "Etk\xF6 saanut koodia? L\xE4het\xE4 uudelleen",
      subtitle: "nollataksesi salasanasi",
      subtitle_email: "Sy\xF6t\xE4 ensin s\xE4hk\xF6postiisi l\xE4hetetty koodi",
      subtitle_phone: "Sy\xF6t\xE4 ensin puhelimeesi l\xE4hetetty koodi",
      title: "Nollaa salasana"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Nollaa salasanasi",
      label__alternativeMethods: "tai kirjaudu sis\xE4\xE4n toisella tavalla",
      title: "Unohditko salasanasi?"
    },
    noAvailableMethods: {
      message: "Kirjautuminen ei onnistu. K\xE4ytett\xE4viss\xE4 ei ole yht\xE4\xE4n todennusmenetelm\xE4\xE4.",
      subtitle: "Tapahtui virhe",
      title: "Ei voi kirjautua"
    },
    passkey: {
      subtitle: "K\xE4ytt\xE4m\xE4ll\xE4 p\xE4\xE4syavaintasi vahvistat, ett\xE4 olet se joka v\xE4it\xE4t olevasi. Laite voi pyyt\xE4\xE4 sormenj\xE4lke\xE4si, kasvojasi tai n\xE4yt\xF6n lukitusta.",
      title: "K\xE4yt\xE4 p\xE4\xE4syavaintasi"
    },
    password: {
      actionLink: "K\xE4yt\xE4 toista tapaa",
      subtitle: "Sy\xF6t\xE4 tilisi salasana",
      title: "Sy\xF6t\xE4 salasanasi"
    },
    passwordPwned: {
      title: "Salasana kompromisoitu"
    },
    phoneCode: {
      formTitle: "Vahvistuskoodi",
      resendButton: "Etk\xF6 saanut koodia? L\xE4het\xE4 uudelleen",
      subtitle: "jatkaaksesi kohteeseen {{applicationName}}",
      title: "Tarkista puhelimesi"
    },
    phoneCodeMfa: {
      formTitle: "Vahvistuskoodi",
      resendButton: "Etk\xF6 saanut koodia? L\xE4het\xE4 uudelleen",
      subtitle: "Sy\xF6t\xE4 jatkaaksesi puhelimeesi l\xE4hetetty vahvistuskoodi",
      title: "Tarkista puhelimesi"
    },
    resetPassword: {
      formButtonPrimary: "Nollaa salasana",
      requiredMessage: "Turvallisuussyist\xE4 on tarpeen nollata salasanasi.",
      successMessage: "Salasanasi on vaihdettu onnistuneesti. Kirjaudutaan sis\xE4\xE4n, odota hetki.",
      title: "Aseta uusi salasana"
    },
    resetPasswordMfa: {
      detailsLabel: "Ennen salasanan nollaamista on varmistettava henkil\xF6llisyytesi."
    },
    start: {
      actionLink: "Rekister\xF6idy",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "K\xE4yt\xE4 s\xE4hk\xF6postia",
      actionLink__use_email_username: "K\xE4yt\xE4 s\xE4hk\xF6postia tai k\xE4ytt\xE4j\xE4nime\xE4",
      actionLink__use_passkey: "K\xE4yt\xE4 p\xE4\xE4syavainta",
      actionLink__use_phone: "K\xE4yt\xE4 puhelinta",
      actionLink__use_username: "K\xE4yt\xE4 k\xE4ytt\xE4j\xE4nime\xE4",
      actionText: "Eik\xF6 sinulla ole tili\xE4?",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "jatkaaksesi kohteeseen {{applicationName}}",
      subtitleCombined: void 0,
      title: "Kirjaudu sis\xE4\xE4n",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "Todennuskoodi",
      subtitle: "Sy\xF6t\xE4 todennuskoodi autentikointisovelluksestasi",
      title: "Kaksivaiheinen todennus"
    }
  },
  signInEnterPasswordTitle: "Sy\xF6t\xE4 salasanasi",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Kirjaudu sis\xE4\xE4n",
      actionText: "Onko sinulla jo tili?",
      subtitle: "T\xE4yt\xE4 loput tiedot jatkaaksesi.",
      title: "T\xE4yt\xE4 puuttuvat kent\xE4t."
    },
    emailCode: {
      formSubtitle: "Sy\xF6t\xE4 s\xE4hk\xF6postiisi l\xE4hetetty koodi",
      formTitle: "Vahvistuskoodi",
      resendButton: "Etk\xF6 saanut koodia? L\xE4het\xE4 uudelleen",
      subtitle: "Sy\xF6t\xE4 s\xE4hk\xF6postiisi l\xE4hetetty koodi jatkaaksesi.",
      title: "Tarkista s\xE4hk\xF6postisi"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Jatkaaksesi avaa vahvistuslinkki laitteella ja selaimella, josta aloitit rekister\xF6itymisen",
        title: "Vahvistuslinkki on virheellinen t\xE4lle laitteelle"
      },
      formSubtitle: "K\xE4yt\xE4 s\xE4hk\xF6postiisi l\xE4hetetty\xE4 vahvistuslinkki\xE4",
      formTitle: "Vahvistuslinkki",
      loading: {
        title: "Rekister\xF6idyt\xE4\xE4n..."
      },
      resendButton: "Etk\xF6 saanut linkki\xE4? L\xE4het\xE4 uudelleen",
      subtitle: "jatkaaksesi kohteeseen {{applicationName}}",
      title: "Vahvista s\xE4hk\xF6postisi",
      verified: {
        title: "Rekister\xF6ityminen onnistui"
      },
      verifiedSwitchTab: {
        subtitle: "Palaa alkuper\xE4iseen v\xE4lilehteen jatkaaksesi",
        subtitleNewTab: "Palaa uuteen v\xE4lilehteen jatkaaksesi",
        title: "Rekister\xF6itynyt toiseen v\xE4lilehteen"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: 'Hyv\xE4ksyn {{ privacyPolicyLink || link("tietosuojaselosteen") }}',
        label__onlyTermsOfService: 'Hyv\xE4ksyn {{ termsOfServiceLink || link("k\xE4ytt\xF6ehdot") }}',
        label__termsOfServiceAndPrivacyPolicy: 'Hyv\xE4ksyn {{ termsOfServiceLink || link("k\xE4ytt\xF6ehdot") }} ja {{ privacyPolicyLink || link("tietosuojaselosteen") }}'
      },
      continue: {
        subtitle: "Lue ja hyv\xE4ksy ehdot jatkaaksesi",
        title: "K\xE4ytt\xF6ehdot ja tietosuojaseloste"
      }
    },
    phoneCode: {
      formSubtitle: "Sy\xF6t\xE4 puhelimeesi l\xE4hetetty koodi",
      formTitle: "Vahvistuskoodi",
      resendButton: "Etk\xF6 saanut koodia? L\xE4het\xE4 uudelleen",
      subtitle: "Sy\xF6t\xE4 puhelimeesi l\xE4hetetty koodi jatkaaksesi.",
      title: "Tarkista puhelimesi"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "Kirjaudu sis\xE4\xE4n",
      actionLink__use_email: void 0,
      actionLink__use_phone: void 0,
      actionText: "Onko sinulla jo tili?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "Tervetuloa! Luo tili jatkaaksesi.",
      subtitleCombined: "Tervetuloa! Luo tili jatkaaksesi.",
      title: "Luo tili",
      titleCombined: "Luo tili"
    }
  },
  socialButtonsBlockButton: "Jatka palvelun {{provider|titleize}} avulla",
  socialButtonsBlockButtonManyInView: "{{provider|titleize}}",
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "Rekister\xF6ityminen ep\xE4onnistui ep\xE4onnistuneiden tietoturvatarkistusten vuoksi. P\xE4ivit\xE4 sivu ja yrit\xE4 uudelleen tai ota yhteytt\xE4 tukeen.",
    captcha_unavailable: "Rekister\xF6ityminen ep\xE4onnistui, koska botin vahvistus ep\xE4onnistui. P\xE4ivit\xE4 sivu ja yrit\xE4 uudelleen tai ota yhteytt\xE4 tukeen.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: "T\xE4m\xE4 s\xE4hk\xF6postiosoite on jo k\xE4yt\xF6ss\xE4. Kokeile toista.",
    form_identifier_exists__phone_number: "T\xE4m\xE4 puhelinnumero on jo k\xE4yt\xF6ss\xE4. Kokeile toista.",
    form_identifier_exists__username: "T\xE4m\xE4 k\xE4ytt\xE4j\xE4nimi on jo k\xE4yt\xF6ss\xE4. Kokeile toista.",
    form_identifier_not_found: "Ei voi l\xF6yt\xE4\xE4 tili\xE4 n\xE4ill\xE4 tiedoilla.",
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: "S\xE4hk\xF6postiosoiteen tulee olla kelvollinen.",
    form_param_format_invalid__phone_number: "Puhelinnumeron on oltava kelvollisessa kansainv\xE4lisess\xE4 muodossa",
    form_param_max_length_exceeded__first_name: "Etunimi saa olla enint\xE4\xE4n 256 merkki\xE4 pitk\xE4.",
    form_param_max_length_exceeded__last_name: "Sukunimi saa olla enint\xE4\xE4n 256 merkki\xE4 pitk\xE4.",
    form_param_max_length_exceeded__name: "Nimi saa olla enint\xE4\xE4n 256 merkki\xE4 pitk\xE4.",
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: void 0,
    form_password_length_too_short: void 0,
    form_password_not_strong_enough: "Salasana ei ole riitt\xE4v\xE4n vahva.",
    form_password_pwned: "Salasana on ollut osallisena tietovuodossa. Valitse toinen salasana.",
    form_password_pwned__sign_in: "Salasana on ollut osallisena tietovuodossa. Vaihdathan salasanasi.",
    form_password_size_in_bytes_exceeded: "Salasanasi on ylitt\xE4nyt sallitun tavum\xE4\xE4r\xE4n, lyhenn\xE4 sit\xE4 tai poista joitain erikoismerkkej\xE4.",
    form_password_validation_failed: "V\xE4\xE4r\xE4 salasana.",
    form_username_invalid_character: void 0,
    form_username_invalid_length: void 0,
    identification_deletion_failed: "Et voi poistaa viimeist\xE4 henkil\xF6llisyytt\xE4si.",
    not_allowed_access: "S\xE4hk\xF6postiosoite tai puhelinnumero ei ole sallittu rekister\xF6ity\xE4ksesi. T\xE4m\xE4 voi johtua siit\xE4, ett\xE4 s\xE4hk\xF6postiosoite sis\xE4lt\xE4\xE4 '+', '=', '#' tai '.' merkkej\xE4, k\xE4ytt\xE4\xE4t aluetta, joka on sidottu tilap\xE4isyyden s\xE4hk\xF6postitilaukseen, tai olet eksplisiittisesti estetty. Jos uskoo, ett\xE4 t\xE4m\xE4 on virhe, ota yhteytt\xE4 tukeen.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: "P\xE4\xE4syavain on jo rekister\xF6ity t\xE4h\xE4n laitteeseen.",
    passkey_not_supported: "P\xE4\xE4syavain ei ole tuettu t\xE4ll\xE4 laitteella.",
    passkey_pa_not_supported: "Rekister\xF6inti vaatii alustan autentikaattorin, mutta laite ei tue sit\xE4.",
    passkey_registration_cancelled: "P\xE4\xE4syavaimen lis\xE4\xE4minen peruutettiin tai aikakatkaistiin.",
    passkey_retrieval_cancelled: "P\xE4\xE4syavaimella kirjautuminen peruutettiin tai aikakatkaistiin.",
    passwordComplexity: {
      maximumLength: "enint\xE4\xE4n {{length}} merkki\xE4",
      minimumLength: "v\xE4hint\xE4\xE4n {{length}} merkki\xE4",
      requireLowercase: "pieni kirjain",
      requireNumbers: "numero",
      requireSpecialCharacter: "erikoismerkki",
      requireUppercase: "iso kirjain",
      sentencePrefix: "Salasanan on sis\xE4llett\xE4v\xE4"
    },
    phone_number_exists: "T\xE4m\xE4 puhelinnumero on jo k\xE4yt\xF6ss\xE4. Kokeile toista.",
    session_exists: "Olet jo kirjautunut sis\xE4\xE4n.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "Salasanasi toimii, mutta se voisi olla vahvempi. Kokeile lis\xE4t\xE4 erikoismerkkej\xE4 tai numeroita.",
      goodPassword: "Salasanasi t\xE4ytt\xE4\xE4 kaikki tarvittavat vaatimukset.",
      notEnough: "Salasanasi ei ole riitt\xE4v\xE4n vahva.",
      suggestions: {
        allUppercase: "K\xE4yt\xE4 isoja kirjaimia, mutta ei kaikkia kirjaimia.",
        anotherWord: "\xC4l\xE4 k\xE4yt\xE4 yleisi\xE4 sanoja.",
        associatedYears: "\xC4l\xE4 k\xE4yt\xE4 vuosilukuja, jotka liittyv\xE4t sinuun.",
        capitalization: "K\xE4yt\xE4 isoja ja pieni\xE4 kirjaimia.",
        dates: "\xC4l\xE4 k\xE4yt\xE4 p\xE4iv\xE4m\xE4\xE4ri\xE4.",
        l33t: "\xC4l\xE4 k\xE4yt\xE4 l33t-kielt\xE4.",
        longerKeyboardPattern: "V\xE4lt\xE4 pitki\xE4 n\xE4pp\xE4inkuvioita.",
        noNeed: "\xC4l\xE4 k\xE4yt\xE4 t\xE4t\xE4 sanaa.",
        pwned: "\xC4l\xE4 k\xE4yt\xE4 salasanaa, joka on ollut osallisena tietovuodossa.",
        recentYears: "\xC4l\xE4 k\xE4yt\xE4 viimeaikaisia vuosilukuja.",
        repeated: "\xC4l\xE4 k\xE4yt\xE4 toistuvia sanoja.",
        reverseWords: "\xC4l\xE4 k\xE4yt\xE4 sanoja takaperin.",
        sequences: "V\xE4lt\xE4 per\xE4kk\xE4isi\xE4 numeroita tai kirjaimia.",
        useWords: "\xC4l\xE4 k\xE4yt\xE4 yleisi\xE4 sanoja."
      },
      warnings: {
        common: "T\xE4m\xE4 on yleinen salasana.",
        commonNames: "Yleiset nimet ja sukunimet ovat helppo arvata.",
        dates: "P\xE4iv\xE4m\xE4\xE4r\xE4t ovat helppo arvata.",
        extendedRepeat: "Toistuvat merkit ovat helppo arvata.",
        keyPattern: "N\xE4pp\xE4inkuvioita on helppo arvata.",
        namesByThemselves: "Nimet ovat helppo arvata.",
        pwned: "T\xE4m\xE4 salasana on ollut osallisena tietovuodossa.",
        recentYears: "Viimeaikaiset vuodet ovat helppo arvata.",
        sequences: "Per\xE4kk\xE4iset numerot ja kirjaimet ovat helppo arvata.",
        similarToCommon: "T\xE4m\xE4 on samanlainen kuin yleinen salasana.",
        simpleRepeat: "Toistuvat merkit ovat helppo arvata.",
        straightRow: "Per\xE4kk\xE4iset merkit ovat helppo arvata.",
        topHundred: "T\xE4m\xE4 on yleinen salasana.",
        topTen: "T\xE4m\xE4 on yleinen salasana.",
        userInputs: "Salasana perustuu k\xE4ytt\xE4j\xE4n sy\xF6tteisiin.",
        wordByItself: "Yksitt\xE4inen sana on helppo arvata."
      }
    }
  },
  userButton: {
    action__addAccount: "Lis\xE4\xE4 tili",
    action__manageAccount: "Hallitse tili\xE4",
    action__signOut: "Kirjaudu ulos",
    action__signOutAll: "Kirjaudu ulos kaikista tileist\xE4"
  },
  userProfile: {
    backupCodePage: {
      actionLabel__copied: "Kopioitu",
      actionLabel__copy: "Kopioi",
      actionLabel__download: "Lataa .txt",
      actionLabel__print: "Tulosta",
      infoText1: "Varakoodit otetaan k\xE4ytt\xF6\xF6n t\xE4lle tilille.",
      infoText2: "Pid\xE4 varakoodit salassa ja s\xE4ilyt\xE4 ne turvallisesti. Voit luoda uudelleen varakoodit, jos ep\xE4ilet, ett\xE4 ne ovat vaarantuneet.",
      subtitle__codelist: "S\xE4ilyt\xE4 varakoodit turvallisessa paikassa ja pid\xE4 ne salassa.",
      successMessage: "Varakoodit ovat nyt k\xE4yt\xF6ss\xE4. Voit k\xE4ytt\xE4\xE4 jotakin n\xE4ist\xE4 kirjautuaksesi tilillesi, jos menet\xE4t k\xE4ytt\xF6oikeuden todennuslaitteeseesi. Jokaista koodia voi k\xE4ytt\xE4\xE4 vain kerran.",
      successSubtitle: "Voit k\xE4ytt\xE4\xE4 jotakin n\xE4ist\xE4 kirjautuaksesi tilillesi, jos menet\xE4t k\xE4ytt\xF6oikeuden todennuslaitteeseesi.",
      title: "Lis\xE4\xE4 varakoodin vahvistus",
      title__codelist: "Varakoodit"
    },
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "Valitse palveluntarjoaja yhdist\xE4\xE4ksesi tilisi.",
      formHint__noAccounts: "Ulkoisia tilintarjoajia ei ole saatavilla.",
      removeResource: {
        messageLine1: "{{identifier}} poistetaan tililt\xE4si.",
        messageLine2: "T\xE4m\xE4 ei poista tili\xE4si palveluntarjoajalta, mutta et voi en\xE4\xE4 k\xE4ytt\xE4\xE4 sit\xE4 kirjautumiseen tai muihin toimintoihin t\xE4m\xE4n tilin kautta.",
        successMessage: "{{connectedAccount}} on poistettu tililt\xE4si.",
        title: "Poista yhdistetty tili"
      },
      socialButtonsBlockButton: "{{provider|titleize}}",
      successMessage: "Palveluntarjoaja on lis\xE4tty tilillesi",
      title: "Lis\xE4\xE4 yhdistetty tili"
    },
    deletePage: {
      actionDescription: 'Kirjoita "Delete account" poistaaksesi tilisi.',
      confirm: "Poista tili",
      messageLine1: "Oletko varma, ett\xE4 haluat poistaa tilisi?",
      messageLine2: "T\xE4m\xE4 toiminto on pysyv\xE4 ja peruuttamaton.",
      title: "Poista tili"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "Vahvistuskoodin sis\xE4lt\xE4v\xE4 s\xE4hk\xF6posti l\xE4hetet\xE4\xE4n t\xE4h\xE4n s\xE4hk\xF6postiosoitteeseen.",
        formSubtitle: "Sy\xF6t\xE4 s\xE4hk\xF6postiisi {{identifier}} l\xE4hetetty koodi",
        formTitle: "Vahvistuskoodi",
        resendButton: "Etk\xF6 saanut koodia? L\xE4het\xE4 uudelleen",
        successMessage: "S\xE4hk\xF6postiosoitteesi {{identifier}} on nyt lis\xE4tty tilillesi."
      },
      emailLink: {
        formHint: "Vahvistuslinkki l\xE4hetet\xE4\xE4n t\xE4h\xE4n s\xE4hk\xF6postiosoitteeseen.",
        formSubtitle: "K\xE4yt\xE4 s\xE4hk\xF6postiisi l\xE4hetetty\xE4 vahvistuslinkki\xE4",
        formTitle: "Vahvistuslinkki",
        resendButton: "Et saanut linkki\xE4? L\xE4het\xE4 uudelleen",
        successMessage: "S\xE4hk\xF6postiosoitteesi {{identifier}} on nyt lis\xE4tty tilillesi."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} poistetaan tililt\xE4si.",
        messageLine2: "T\xE4m\xE4 ei poista s\xE4hk\xF6postiosoitettasi, mutta et voi en\xE4\xE4 k\xE4ytt\xE4\xE4 sit\xE4 kirjautumiseen tai muihin toimintoihin t\xE4m\xE4n tilin kautta.",
        successMessage: "{{emailAddress}} on poistettu tililt\xE4si.",
        title: "Poista s\xE4hk\xF6postiosoite"
      },
      title: "Lis\xE4\xE4 s\xE4hk\xF6postiosoite",
      verifyTitle: "Vahvista s\xE4hk\xF6postiosoite"
    },
    formButtonPrimary__add: "Lis\xE4\xE4",
    formButtonPrimary__continue: "Jatka",
    formButtonPrimary__finish: "Valmis",
    formButtonPrimary__remove: "Poista",
    formButtonPrimary__save: "Tallenna",
    formButtonReset: "Peruuta",
    mfaPage: {
      formHint: "Valitse todennusmenetelm\xE4.",
      title: "Lis\xE4\xE4 kaksivaiheinen todennus"
    },
    mfaPhoneCodePage: {
      backButton: "K\xE4yt\xE4 olemassa olevaa numeroa",
      primaryButton__addPhoneNumber: "Lis\xE4\xE4 puhelinnumero",
      removeResource: {
        messageLine1: "{{identifier}} ei en\xE4\xE4 vastaanota vahvistuskoodeja kirjautuessaan sis\xE4\xE4n.",
        messageLine2: "Tilisi ei ehk\xE4 ole yht\xE4 turvallinen. Haluatko varmasti jatkaa?",
        successMessage: "SMS-koodin kaksivaiheinen todennus on poistettu {{mfaPhoneCode}}",
        title: "Poista kaksivaiheinen todennus"
      },
      subtitle__availablePhoneNumbers: "Valitse olemassa oleva puhelinnumero rekister\xF6ity\xE4ksesi SMS-koodin kaksivaiheiseen todennukseen tai lis\xE4\xE4 uusi.",
      subtitle__unavailablePhoneNumbers: "Ei ole k\xE4ytett\xE4viss\xE4 olevia puhelinnumeroita rekister\xF6ity\xE4ksesi SMS-koodin kaksivaiheiseen todennukseen, lis\xE4\xE4 uusi.",
      successMessage1: "Kirjautuessasi sinun on annettava vahvistuskoodi, joka on l\xE4hetetty t\xE4h\xE4n puhelinnumeroon lis\xE4vaiheena.",
      successMessage2: "Tallenna n\xE4m\xE4 varakoodit ja s\xE4ilyt\xE4 ne jossain turvallisessa paikassa. Jos menet\xE4t p\xE4\xE4syn todennuslaitteeseesi, voit k\xE4ytt\xE4\xE4 varakoodeja kirjautuaksesi sis\xE4\xE4n.",
      successTitle: "SMS-koodin todennus on otettu k\xE4ytt\xF6\xF6n",
      title: "Lis\xE4\xE4 SMS-koodin todennus"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Skannaa sen sijaan QR-koodi",
        buttonUnableToScan__nonPrimary: "Et voi skannata QR-koodia?",
        infoText__ableToScan: "Aseta uusi kirjautumistapa todennussovellukseesi ja skannaa seuraava QR-koodi linkitt\xE4\xE4ksesi se tilillesi.",
        infoText__unableToScan: "Aseta uusi kirjautumistapa todennussovellukseesi ja sy\xF6t\xE4 alla annettu avain.",
        inputLabel__unableToScan1: "Varmista, ett\xE4 Aikaperusteiset tai Yksitt\xE4iset salasanat on k\xE4yt\xF6ss\xE4 ja viimeistele tilin linkitys.",
        inputLabel__unableToScan2: "Vaihtoehtoisesti, jos todennussovelluksesi tukee TOTP-URI:ta, voit my\xF6s kopioida koko URI:n."
      },
      removeResource: {
        messageLine1: "T\xE4m\xE4n todennussovelluksen avulla ei en\xE4\xE4 tarvita vahvistuskoodia kirjautuessasi sis\xE4\xE4n.",
        messageLine2: "Tilisi ei ehk\xE4 ole yht\xE4 turvallinen. Haluatko varmasti jatkaa?",
        successMessage: "Kaksivaiheinen todennus todennussovelluksen avulla on poistettu.",
        title: "Poista kaksivaiheinen todennus"
      },
      successMessage: "Kaksivaiheinen todennus on nyt otettu k\xE4ytt\xF6\xF6n. Kirjautuessasi sinun on annettava vahvistuskoodi t\xE4st\xE4 todennussovelluksesta lis\xE4vaiheena.",
      title: "Lis\xE4\xE4 todennussovellus",
      verifySubtitle: "Sy\xF6t\xE4 todennuskoodi, jonka todennussovelluksesi on luonut",
      verifyTitle: "Vahvistuskoodi"
    },
    mobileButton__menu: "Valikko",
    navbar: {
      account: "Profiili",
      billing: void 0,
      description: "Hallitse tilisi tietoja",
      security: "Turvallisuus",
      title: "Tili"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "{{name}} poistetaan tililt\xE4si.",
        title: "Poista p\xE4\xE4syavain"
      },
      subtitle__rename: "Voit muuttaa p\xE4\xE4syavaimen nime\xE4 helpottaaksesi sen l\xF6yt\xE4mist\xE4.",
      title__rename: "Nime\xE4 p\xE4\xE4syavain uudelleen"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Suositellaan kirjautumista ulos kaikista muista laitteista, jotka saattavat k\xE4ytt\xE4\xE4 vanhaa salasanaasi.",
      readonly: "Salasanaa ei voi muuttaa, koska kirjautuminen on mahdollista vain yrityksen yhteyden kautta.",
      successMessage__set: "Salasanasi on asetettu.",
      successMessage__signOutOfOtherSessions: "Kaikki muut laitteet on kirjattu ulos.",
      successMessage__update: "Salasanasi on p\xE4ivitetty.",
      title__set: "Aseta salasana",
      title__update: "P\xE4ivit\xE4 salasana"
    },
    phoneNumberPage: {
      infoText: "Vahvistuskoodin sis\xE4lt\xE4v\xE4 tekstiviesti l\xE4hetet\xE4\xE4n t\xE4h\xE4n puhelinnumeroon. Viesti- ja tiedonsiirtomaksuja saatetaan peri\xE4.",
      removeResource: {
        messageLine1: "{{identifier}} poistetaan tililt\xE4si.",
        messageLine2: "T\xE4m\xE4 ei poista puhelinnumeroasi, mutta et voi en\xE4\xE4 k\xE4ytt\xE4\xE4 sit\xE4 kirjautumiseen tai muihin toimintoihin t\xE4m\xE4n tilin kautta.",
        successMessage: "{{phoneNumber}} on poistettu tililt\xE4si.",
        title: "Poista puhelinnumero"
      },
      successMessage: "Puhelinnumerosi {{identifier}} on nyt lis\xE4tty tilillesi.",
      title: "Lis\xE4\xE4 puhelinnumero",
      verifySubtitle: "Sy\xF6t\xE4 puhelimeesi l\xE4hetetty vahvistuskoodi: {{identifier}}",
      verifyTitle: "Vahvista puhelinnumero"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "Suositeltu koko 1:1, enint\xE4\xE4n 10 Mt.",
      imageFormDestructiveActionSubtitle: "Poista kuva",
      imageFormSubtitle: "Lataa kuva",
      imageFormTitle: "Profiilikuva",
      readonly: "Profiilitietosi on annettu yrityksen yhteyden kautta eik\xE4 niit\xE4 voi muuttaa.",
      successMessage: "Profiilisi on p\xE4ivitetty.",
      title: "P\xE4ivit\xE4 profiili"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Kirjaudu ulos laitteesta",
        title: "Aktiiviset laitteet"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "Yrit\xE4 uudelleen",
        actionLabel__reauthorize: "Valtuuta nyt",
        destructiveActionTitle: "Poista",
        primaryButton: "Yhdist\xE4 tili",
        subtitle__disconnected: void 0,
        subtitle__reauthorize: "Tarvittavat k\xE4ytt\xF6oikeudet on p\xE4ivitetty, ja saatat kokea rajoitettua toiminnallisuutta. Valtuuta t\xE4m\xE4 sovellus v\xE4ltt\xE4\xE4ksesi mahdolliset ongelmat.",
        title: "Yhdistetyt tilit"
      },
      dangerSection: {
        deleteAccountButton: "Poista tili",
        title: "Poista tili"
      },
      emailAddressesSection: {
        destructiveAction: "Poista s\xE4hk\xF6postiosoite",
        detailsAction__nonPrimary: "Aseta ensisijaiseksi",
        detailsAction__primary: "Viimeistele vahvistus",
        detailsAction__unverified: "Vahvista s\xE4hk\xF6postiosoite",
        primaryButton: "Lis\xE4\xE4 s\xE4hk\xF6postiosoite",
        title: "S\xE4hk\xF6postiosoitteet"
      },
      enterpriseAccountsSection: {
        title: "Yritystilit"
      },
      headerTitle__account: "Tili",
      headerTitle__security: "Turvallisuus",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Luo uudet",
          headerTitle: "Varakoodit",
          subtitle__regenerate: "Hanki uusi sarja turvallisia varakoodeja. Aiemmat varakoodit poistetaan eiv\xE4tk\xE4 ne ole en\xE4\xE4 k\xE4ytett\xE4viss\xE4.",
          title__regenerate: "Luo uudet varakoodit"
        },
        phoneCode: {
          actionLabel__setDefault: "Aseta oletukseksi",
          destructiveActionLabel: "Poista"
        },
        primaryButton: "Lis\xE4\xE4 kaksivaiheinen todennus",
        title: "Kaksivaiheinen todennus",
        totp: {
          destructiveActionTitle: "Poista",
          headerTitle: "Todennussovellus"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Poista",
        menuAction__rename: "Nime\xE4 uudelleen",
        primaryButton: void 0,
        title: "P\xE4\xE4syavaimet"
      },
      passwordSection: {
        primaryButton__setPassword: "Aseta salasana",
        primaryButton__updatePassword: "P\xE4ivit\xE4 salasana",
        title: "Salasana"
      },
      phoneNumbersSection: {
        destructiveAction: "Poista puhelinnumero",
        detailsAction__nonPrimary: "Aseta ensisijaiseksi",
        detailsAction__primary: "Viimeistele vahvistus",
        detailsAction__unverified: "Vahvista puhelinnumero",
        primaryButton: "Lis\xE4\xE4 puhelinnumero",
        title: "Puhelinnumerot"
      },
      profileSection: {
        primaryButton: "P\xE4ivit\xE4 profiili",
        title: "Profiili"
      },
      usernameSection: {
        primaryButton__setUsername: "Aseta k\xE4ytt\xE4j\xE4nimi",
        primaryButton__updateUsername: "P\xE4ivit\xE4 k\xE4ytt\xE4j\xE4nimi",
        title: "K\xE4ytt\xE4j\xE4nimi"
      },
      web3WalletsSection: {
        destructiveAction: "Poista lompakko",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Web3-lompakot",
        title: "Web3-lompakot"
      }
    },
    usernamePage: {
      successMessage: "K\xE4ytt\xE4j\xE4nimesi on p\xE4ivitetty.",
      title__set: "Aseta k\xE4ytt\xE4j\xE4nimi",
      title__update: "P\xE4ivit\xE4 k\xE4ytt\xE4j\xE4nimi"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} poistetaan tililt\xE4si.",
        messageLine2: "Et voi en\xE4\xE4 kirjautua sis\xE4\xE4n t\xE4ll\xE4 web3-lompakolla.",
        successMessage: "{{web3Wallet}} on poistettu tililt\xE4si.",
        title: "Poista web3-lompakko"
      },
      subtitle__availableWallets: "Valitse web3-lompakko yhdist\xE4\xE4ksesi tilisi.",
      subtitle__unavailableWallets: "Ei ole k\xE4ytett\xE4viss\xE4 olevia web3-lompakoita yhdist\xE4\xE4ksesi tilisi.",
      successMessage: "Web3-lompakko on lis\xE4tty tilillesi.",
      title: "Lis\xE4\xE4 web3-lompakko",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  fiFI
};
//# sourceMappingURL=fi-FI.mjs.map