import type { ExternalAccountResource, SamlAccountResource, UserPreviewId, UserResource } from '@clerk/types';
import React from 'react';
import type { LocalizationKey } from '../customizables';
import { Flex, Text } from '../customizables';
import type { PropsOfComponent, ThemableCssProp } from '../styledSystem';
export type UserPreviewProps = Omit<PropsOfComponent<typeof Flex>, 'title' | 'elementId'> & {
    size?: 'lg' | 'md' | 'sm' | 'xs';
    icon?: React.ReactNode;
    iconSx?: ThemableCssProp;
    badge?: React.ReactNode;
    imageUrl?: string | null;
    rounded?: boolean;
    elementId?: UserPreviewId;
    avatarSx?: ThemableCssProp;
    mainIdentifierSx?: ThemableCssProp;
    mainIdentifierVariant?: PropsOfComponent<typeof Text>['variant'];
    title?: LocalizationKey | string;
    subtitle?: LocalizationKey | string;
    showAvatar?: boolean;
} & ({
    user?: Partial<UserResource>;
    externalAccount?: null | undefined;
    samlAccount?: null | undefined;
} | {
    user?: null | undefined;
    externalAccount?: Partial<ExternalAccountResource>;
    samlAccount?: null | undefined;
} | {
    user?: null | undefined;
    externalAccount?: null | undefined;
    samlAccount?: Partial<SamlAccountResource>;
});
export declare const UserPreview: (props: UserPreviewProps) => import("@emotion/react/jsx-runtime").JSX.Element;
