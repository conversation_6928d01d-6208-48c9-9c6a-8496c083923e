import type { UserButtonCtx } from '../../types';
export declare const UserButtonContext: import("react").Context<UserButtonCtx | null>;
export declare const useUserButtonContext: () => {
    componentName: "UserButton";
    navigateAfterMultiSessionSingleSignOut: () => Promise<unknown>;
    navigateAfterSignOut: () => Promise<unknown>;
    signInUrl: string;
    userProfileUrl: string;
    afterMultiSessionSingleSignOutUrl: string;
    afterSignOutUrl: string;
    afterSwitchSessionUrl: string;
    userProfileMode: "modal" | "navigation";
    menutItems: import("../../utils").MenuItem[];
    showName?: boolean;
    defaultOpen?: boolean;
    __experimental_asStandalone?: boolean | ((opened: boolean) => void);
    appearance?: import("@clerk/types").UserButtonTheme;
    userProfileProps?: Pick<import("@clerk/types").UserProfileProps, "additionalOAuthScopes" | "appearance" | "customPages">;
    mode?: "modal" | "mounted";
} | {
    componentName: "UserButton";
    navigateAfterMultiSessionSingleSignOut: () => Promise<unknown>;
    navigateAfterSignOut: () => Promise<unknown>;
    signInUrl: string;
    userProfileUrl: string;
    afterMultiSessionSingleSignOutUrl: string;
    afterSignOutUrl: string;
    afterSwitchSessionUrl: string;
    userProfileMode: "modal" | "navigation";
    menutItems: import("../../utils").MenuItem[];
    showName?: boolean;
    defaultOpen?: boolean;
    __experimental_asStandalone?: boolean | ((opened: boolean) => void);
    appearance?: import("@clerk/types").UserButtonTheme;
    userProfileProps?: Pick<import("@clerk/types").UserProfileProps, "additionalOAuthScopes" | "appearance" | "customPages">;
    mode?: "modal" | "mounted";
};
