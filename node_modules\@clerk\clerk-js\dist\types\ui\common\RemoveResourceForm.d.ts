import type { FormProps } from '../elements/FormContainer';
import type { LocalizationKey } from '../localization';
type RemoveFormProps = FormProps & {
    title: LocalizationKey;
    messageLine1: LocalizationKey;
    messageLine2?: LocalizationKey;
    successMessage?: LocalizationKey;
    deleteResource: () => Promise<any>;
};
export declare const RemoveResourceForm: (props: RemoveFormProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
