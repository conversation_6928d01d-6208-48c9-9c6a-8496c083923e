"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["877"],{51618:function(e,t,n){n.r(t),n.d(t,{SessionTask:()=>_});var i=n(79109),r=n(83799),l=n(72810),o=n(69144),a=n(44455),u=n(2672),s=n(21455),c=n(76663),d=n(11576),v=n(86988),k=n(24676),h=n(30683);let g=(0,u.withCardStateProvider)(()=>{let e=(0,r.cL)(),{navigate:t}=(0,k.useRouter)(),{redirectUrlComplete:n}=(0,v.G)();return(0,o.useEffect)(()=>{let t=setTimeout(()=>{e.__experimental_navigateToTask({redirectUrlComplete:n})},500);return()=>clearTimeout(t)},[t,e,n]),(0,i.BX)(a.Z.Root,{children:[(0,i.tZ)(a.Z.Content,{children:(0,i.tZ)(s.I,{})}),(0,i.tZ)(a.Z.Footer,{})]})});function Z(){return(0,i.BX)(k.Switch,{children:[(0,i.tZ)(k.Route,{path:c.m.org,children:(0,i.tZ)(d.OrganizationListContext.Provider,{value:{componentName:"OrganizationList",skipInvitationScreen:!0},children:(0,i.tZ)(h.OrganizationList,{})})}),(0,i.tZ)(k.Route,{index:!0,children:(0,i.tZ)(g,{})})]})}function _(){var e,t;let n=(0,r.cL)(),{navigate:a}=(0,k.useRouter)(),u=(0,o.useContext)(d.SignInContext),s=(0,o.useContext)(d.SignUpContext),c=null!==(t=null!==(e=null==u?void 0:u.afterSignInUrl)&&void 0!==e?e:null==s?void 0:s.afterSignUpUrl)&&void 0!==t?t:null==n?void 0:n.buildAfterSignInUrl();(0,o.useEffect)(()=>{var e,t;let i=null===(e=n.session)||void 0===e?void 0:e.currentTask;if(!i){a(c);return}null===(t=n.telemetry)||void 0===t||t.record((0,l.Zg)("SessionTask",{task:i.key}))},[n,a,c]);let h=(0,o.useCallback)(()=>n.__experimental_navigateToTask({redirectUrlComplete:c}),[n,c]);return(0,i.tZ)(v.H.Provider,{value:{nextTask:h,redirectUrlComplete:c},children:(0,i.tZ)(Z,{})})}}}]);