// src/fr-FR.ts
var frFR = {
  locale: "fr-FR",
  backButton: "Retour",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "D\xE9faut",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Autre dispositif d'imitation",
  badge__primary: "Principal",
  badge__renewsAt: void 0,
  badge__requiresAction: "N\xE9cessite une action",
  badge__startsAt: void 0,
  badge__thisDevice: "Cet appareil",
  badge__unverified: "Non v\xE9rifi\xE9",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Appareil utilisateur",
  badge__you: "Vous",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Cr\xE9er l\u2019organisation",
    invitePage: {
      formButtonReset: "Passer"
    },
    title: "Cr\xE9er une organisation"
  },
  dates: {
    lastDay: "Hier \xE0 {{ date | timeString('fr-FR') }}",
    next6Days: "{{ date | weekday('fr-FR','long') }} \xE0 {{ date | timeString('fr-FR') }}",
    nextDay: "Demain \xE0 {{ date | timeString('fr-FR') }}",
    numeric: "{{ date | numeric('fr-FR') }}",
    previous6Days: "{{ date | weekday('fr-FR','long') }} dernier \xE0 {{ date | timeString('fr-FR') }}",
    sameDay: "Aujourd'hui \xE0 {{ date | timeString('fr-FR') }}"
  },
  dividerText: "ou",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Utiliser une autre m\xE9thode",
  footerPageLink__help: "Aide",
  footerPageLink__privacy: "Vie priv\xE9e",
  footerPageLink__terms: "Conditions",
  formButtonPrimary: "Continuer",
  formButtonPrimary__verify: "V\xE9rifier",
  formFieldAction__forgotPassword: "Mot de passe oubli\xE9 ?",
  formFieldError__matchingPasswords: "Les mots de passe correspondent.",
  formFieldError__notMatchingPasswords: "Les mots de passe ne correspondent pas.",
  formFieldError__verificationLinkExpired: "Le lien de v\xE9rification a expir\xE9. Merci de demander un nouveau lien.",
  formFieldHintText__optional: "Optionnel",
  formFieldHintText__slug: "Un slug est un identifiant lisible qui doit \xEAtre unique. Il est souvent utilis\xE9 dans les URL.",
  formFieldInputPlaceholder__backupCode: "Code de sauvegarde",
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Supprimer le compte",
  formFieldInputPlaceholder__emailAddress: "Adresse e-mail",
  formFieldInputPlaceholder__emailAddress_username: "Nom d'utilisateur ou adresse e-mail",
  formFieldInputPlaceholder__emailAddresses: "Saisissez ou collez une ou plusieurs adresses e-mail, s\xE9par\xE9es par des espaces ou des virgules",
  formFieldInputPlaceholder__firstName: "Pr\xE9nom",
  formFieldInputPlaceholder__lastName: "Nom de famille",
  formFieldInputPlaceholder__organizationDomain: "Domaine de l'organisation",
  formFieldInputPlaceholder__organizationDomainEmailAddress: "Adresse e-mail de l'organisation",
  formFieldInputPlaceholder__organizationName: "Nom de l'organisation",
  formFieldInputPlaceholder__organizationSlug: "Identifiant de l'organisation",
  formFieldInputPlaceholder__password: "Mot de passe",
  formFieldInputPlaceholder__phoneNumber: "Num\xE9ro de t\xE9l\xE9phone",
  formFieldInputPlaceholder__username: "Nom d'utilisateur",
  formFieldLabel__automaticInvitations: "Autoriser les invitations automatiques pour ce domaine",
  formFieldLabel__backupCode: "Code de r\xE9cup\xE9ration",
  formFieldLabel__confirmDeletion: "Confirmation",
  formFieldLabel__confirmPassword: "Confirmer le mot de passe",
  formFieldLabel__currentPassword: "Mot de passe actuel",
  formFieldLabel__emailAddress: "Adresse e-mail",
  formFieldLabel__emailAddress_username: "Adresse e-mail ou nom d'utilisateur",
  formFieldLabel__emailAddresses: "Adresses e-mail",
  formFieldLabel__firstName: "Pr\xE9nom",
  formFieldLabel__lastName: "Nom de famille",
  formFieldLabel__newPassword: "Nouveau mot de passe",
  formFieldLabel__organizationDomain: "Domaine",
  formFieldLabel__organizationDomainDeletePending: "Supprimer les invitations et suggestions en attente",
  formFieldLabel__organizationDomainEmailAddress: "E-mail de v\xE9rification",
  formFieldLabel__organizationDomainEmailAddressDescription: "Entrer une adresse e-mail appartenant \xE0 ce domaine pour recevoir un code et v\xE9rifier ce domaine.",
  formFieldLabel__organizationName: "Nom de l'organisation",
  formFieldLabel__organizationSlug: "Slug URL",
  formFieldLabel__passkeyName: void 0,
  formFieldLabel__password: "Mot de passe",
  formFieldLabel__phoneNumber: "Num\xE9ro de t\xE9l\xE9phone",
  formFieldLabel__role: "R\xF4le",
  formFieldLabel__signOutOfOtherSessions: "Se d\xE9connecter de tous les autres appareils",
  formFieldLabel__username: "Nom d'utilisateur",
  impersonationFab: {
    action__signOut: "D\xE9connexion",
    title: "Connect\xE9 en tant que {{identifier}}"
  },
  maintenanceMode: void 0,
  membershipRole__admin: "Administrateur",
  membershipRole__basicMember: "Membre",
  membershipRole__guestMember: "Invit\xE9",
  organizationList: {
    action__createOrganization: "Cr\xE9er une organisation",
    action__invitationAccept: "Rejoindre",
    action__suggestionsAccept: "Demande d'adh\xE9sion",
    createOrganization: "Cr\xE9er une Organisation",
    invitationAcceptedLabel: "Accept\xE9e",
    subtitle: "pour continuer vers {{applicationName}}",
    suggestionsAcceptedLabel: "En attente d\u2019approbation",
    title: "Choisissez un compte",
    titleWithoutPersonal: "Choisissez une organisation"
  },
  organizationProfile: {
    badge__automaticInvitation: "Invitations automatiques",
    badge__automaticSuggestion: "Suggestions automatiques",
    badge__manualInvitation: "Pas d'inscription automatique",
    badge__unverified: "Non v\xE9rifi\xE9",
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Ajoutez le domaine pour le v\xE9rifier. Les utilisateurs poss\xE9dant une adresses e-mail sur ce domaine peuvent rejoindre l'organisation automatiquement ou faire une demande pour y adh\xE9rer.",
      title: "Ajouter un domaine"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Les invitations suivantes n\u2019ont pas pu \xEAtre envoy\xE9es. Veuillez r\xE9gler ce probl\xE8me et r\xE9essayer:",
      formButtonPrimary__continue: "Envoyer des invitations",
      selectDropdown__role: "S\xE9lectionner un r\xF4le",
      subtitle: "Inviter des membres \xE0 rejoindre l\u2019organisation",
      successMessage: "Les invitations ont \xE9t\xE9 envoy\xE9es.",
      title: "Inviter des membres"
    },
    membersPage: {
      action__invite: "Inviter",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "Supprimer",
        tableHeader__actions: "Actions",
        tableHeader__joined: "Rejoint",
        tableHeader__role: "R\xF4le",
        tableHeader__user: "Utilisateur"
      },
      detailsTitle__emptyRow: "Aucun membre",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Invitez des utilisateurs en connectant un domaine de messagerie \xE0 votre organisation. Toute personne s'inscrivant avec une adresses e-mail sur ce domaine pourra rejoindre l'organisation.",
          headerTitle: "Invitations automatiques",
          primaryButton: "G\xE9rer les domaines valid\xE9s"
        },
        table__emptyRow: "Pas d'invitations \xE0 afficher"
      },
      invitedMembersTab: {
        menuAction__revoke: "R\xE9voquer l'invitation",
        tableHeader__invited: "Invit\xE9"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Les utilisateurs qui s'inscrivent avec un domaine de messagerie identique verront une suggestion pour demander \xE0 rejoindre votre organisation.",
          headerTitle: "Suggestions automatiques",
          primaryButton: "G\xE9rer les domaines valid\xE9s"
        },
        menuAction__approve: "Approuver",
        menuAction__reject: "Rejeter",
        tableHeader__requested: "Acc\xE8s demand\xE9",
        table__emptyRow: "Pas de demandes \xE0 afficher"
      },
      start: {
        headerTitle__invitations: "Invitations",
        headerTitle__members: "Membres",
        headerTitle__requests: "Demandes"
      }
    },
    navbar: {
      billing: void 0,
      description: "G\xE9rer votre organisation.",
      general: "G\xE9n\xE9ral",
      members: "Membres",
      title: "Organisation"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: "Saisissez {{organizationName}} ci-dessous pour continuer.",
          messageLine1: "\xCAtes-vous s\xFBr(e) de vouloir supprimer cette organisation ?",
          messageLine2: "Cette action est d\xE9finitive et irr\xE9versible.",
          successMessage: "Vous avez supprim\xE9 l'organisation.",
          title: "Supprimer l'organisation"
        },
        leaveOrganization: {
          actionDescription: "Saisissez {{organizationName}} ci-dessous pour continuer.",
          messageLine1: "\xCAtes-vous s\xFBr de vouloir quitter cette organisation ? Vous perdrez l'acc\xE8s \xE0 cette organisation et \xE0 ses applications.",
          messageLine2: "Cette action est permanente et irr\xE9versible.",
          successMessage: "Vous avez quitt\xE9 l'organisation.",
          title: "Quitter l'organisation"
        },
        title: "Danger"
      },
      domainSection: {
        menuAction__manage: "G\xE9rer",
        menuAction__remove: "Supprimer",
        menuAction__verify: "Valider",
        primaryButton: "Ajouter un domaine",
        subtitle: "Permettre aux utilisateurs de rejoindre l'organisation automatiquement ou de faire une demande d'adh\xE9sion si leur domaine de messagerie est v\xE9rifi\xE9.",
        title: "Domaines v\xE9rifi\xE9s"
      },
      successMessage: "L'organisation a \xE9t\xE9 mise \xE0 jour.",
      title: "Profil de l\u2019organisation"
    },
    removeDomainPage: {
      messageLine1: "Le domaine de messagerie {{domain}} sera supprim\xE9.",
      messageLine2: "Les utilisateurs ne pourront plus rejoindre l'organisation automatiquement apr\xE8s cela.",
      successMessage: "{{domain}} a \xE9t\xE9 supprim\xE9.",
      title: "Supprimer un domaine"
    },
    start: {
      headerTitle__general: "G\xE9n\xE9ral",
      headerTitle__members: "Membres",
      profileSection: {
        primaryButton: "Mettre \xE0 jour le profil",
        title: "Profil de l'organisation",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Supprimer ce domaine affectera les utilisateurs invit\xE9s.",
        removeDomainActionLabel__remove: "Supprimer ce domaine",
        removeDomainSubtitle: "Supprimer ce domaine de vos domaines v\xE9rifi\xE9s",
        removeDomainTitle: "Supprimer un domaine"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Les utilisateurs sont automatiquement invit\xE9s \xE0 rejoindre l'organisation lors de leur inscription et peuvent la rejoindre \xE0 tout moment.",
        automaticInvitationOption__label: "Invitations automatiques",
        automaticSuggestionOption__description: "Les utilisateurs re\xE7oivent une suggestion d'adh\xE9sion \xE0 l'organisation, mais doivent \xEAtre approuv\xE9s par un administrateur avant de pouvoir y adh\xE9rer.",
        automaticSuggestionOption__label: "Suggestions automatiques",
        calloutInfoLabel: "Changer le mode d'inscription n'affectera que les nouveaux utilisateurs.",
        calloutInvitationCountLabel: "Invitations en attente envoy\xE9es aux utilisateurs : {{count}}",
        calloutSuggestionCountLabel: "Suggestions en attente envoy\xE9es aux utilisateurs : {{count}}",
        manualInvitationOption__description: "Les utilisateurs ne peuvent \xEAtre invit\xE9s \xE0 l'organisation que manuellement.",
        manualInvitationOption__label: "Pas d'inscription automatique",
        subtitle: "Choisissez comment les utilisateurs de ce domaine peuvent rejoindre l'organisation."
      },
      start: {
        headerTitle__danger: "Danger",
        headerTitle__enrollment: "Option d'inscription"
      },
      subtitle: "Le domaine {{domain}} est maintenant v\xE9rifi\xE9. Poursuivez en s\xE9lectionnant le mode d'inscription.",
      title: "Mettre \xE0 jour {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Saisissez le code de v\xE9rification envoy\xE9 \xE0 votre adresse e-mail",
      formTitle: "Code de v\xE9rification",
      resendButton: "Vous n'avez pas re\xE7u de code ? Renvoyer",
      subtitle: "Le domaine {{domainName}} doit \xEAtre v\xE9rifi\xE9 par e-mail.",
      subtitleVerificationCodeScreen: "Un code de v\xE9rification a \xE9t\xE9 envoy\xE9 \xE0 {{emailAddress}}. Saisissez le code pour continuer.",
      title: "V\xE9rifier un domaine"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Cr\xE9er une organisation",
    action__invitationAccept: "Rejoindre",
    action__manageOrganization: "G\xE9rer l'organisation",
    action__suggestionsAccept: "Demander \xE0 rejoindre",
    notSelected: "Aucune organisation s\xE9lectionn\xE9e",
    personalWorkspace: "Espace de travail personnel",
    suggestionsAcceptedLabel: "En attente d'acceptation"
  },
  paginationButton__next: "Prochain",
  paginationButton__previous: "Pr\xE9c\xE9dent",
  paginationRowText__displaying: "Affichage",
  paginationRowText__of: "de",
  reverification: {
    alternativeMethods: {
      actionLink: "Utiliser une autre m\xE9thode",
      actionText: "Vous ne pouvez pas acc\xE9der \xE0 votre compte ?",
      blockButton__backupCode: "Utiliser un code de r\xE9cup\xE9ration",
      blockButton__emailCode: "Recevoir un code par e-mail",
      blockButton__passkey: void 0,
      blockButton__password: "Utiliser le mot de passe",
      blockButton__phoneCode: "Recevoir un code par t\xE9l\xE9phone",
      blockButton__totp: "Utiliser un code d\u2019application d\u2019authentification",
      getHelp: {
        blockButton__emailSupport: "Contacter le support par e-mail",
        content: "Si vous ne pouvez pas acc\xE9der \xE0 votre compte, contactez notre \xE9quipe de support pour obtenir de l'aide.",
        title: "Obtenir de l'aide"
      },
      subtitle: "Choisissez une m\xE9thode alternative pour v\xE9rifier votre identit\xE9.",
      title: "V\xE9rification alternative"
    },
    backupCodeMfa: {
      subtitle: "Entrez l'un de vos codes de r\xE9cup\xE9ration pour v\xE9rifier votre compte.",
      title: "V\xE9rification par code de r\xE9cup\xE9ration"
    },
    emailCode: {
      formTitle: "Entrez le code de v\xE9rification",
      resendButton: "Renvoyer le code",
      subtitle: "Un code a \xE9t\xE9 envoy\xE9 \xE0 votre adresse e-mail.",
      title: "V\xE9rification par e-mail"
    },
    noAvailableMethods: {
      message: "Aucune m\xE9thode de v\xE9rification n'est disponible.",
      subtitle: "Impossible de proc\xE9der \xE0 la v\xE9rification.",
      title: "Aucune m\xE9thode disponible"
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: "R\xE9initialiser le mot de passe",
      subtitle: "Entrez votre mot de passe pour continuer.",
      title: "V\xE9rification par mot de passe"
    },
    phoneCode: {
      formTitle: "Entrez le code de v\xE9rification",
      resendButton: "Renvoyer le code",
      subtitle: "Un code a \xE9t\xE9 envoy\xE9 \xE0 votre t\xE9l\xE9phone.",
      title: "V\xE9rification par t\xE9l\xE9phone"
    },
    phoneCodeMfa: {
      formTitle: "Entrez le code de v\xE9rification",
      resendButton: "Renvoyer le code",
      subtitle: "Un code a \xE9t\xE9 envoy\xE9 \xE0 votre t\xE9l\xE9phone pour v\xE9rification.",
      title: "V\xE9rification par t\xE9l\xE9phone"
    },
    totpMfa: {
      formTitle: "Entrez le code de v\xE9rification",
      subtitle: "Entrez le code g\xE9n\xE9r\xE9 par votre application d'authentification.",
      title: "V\xE9rification par application d\u2019authentification"
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Ajouter un compte",
      action__signOutAll: "Se d\xE9connecter de tous les comptes",
      subtitle: "S\xE9lectionnez le compte avec lequel vous souhaitez continuer.",
      title: "Choisissez un compte"
    },
    alternativeMethods: {
      actionLink: "Obtenir de l'aide",
      actionText: "Aucune de ces m\xE9thode d'authentification ?",
      blockButton__backupCode: "Utiliser un code de r\xE9cup\xE9ration",
      blockButton__emailCode: "Envoyer le code \xE0 {{identifier}}",
      blockButton__emailLink: "Envoyer le lien \xE0 {{identifier}}",
      blockButton__passkey: "Utiliser une cl\xE9 de s\xE9curit\xE9",
      blockButton__password: "Connectez-vous avec votre mot de passe",
      blockButton__phoneCode: "Envoyer le code \xE0 {{identifier}}",
      blockButton__totp: "Utilisez votre application d'authentification",
      getHelp: {
        blockButton__emailSupport: "Assistance par e-mail",
        content: "Si vous rencontrez des difficult\xE9s pour vous connecter \xE0 votre compte, envoyez-nous un e-mail et nous travaillerons avec vous pour r\xE9tablir l'acc\xE8s d\xE8s que possible.",
        title: "Obtenir de l'aide"
      },
      subtitle: "Vous rencontrez des probl\xE8mes ? Vous pouvez utiliser l'une de ces m\xE9thodes pour vous connecter.",
      title: "Utiliser une autre m\xE9thode"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "pour continuer vers {{applicationName}}",
      title: "Entrez un code de r\xE9cup\xE9ration"
    },
    emailCode: {
      formTitle: "Le code de v\xE9rification",
      resendButton: "Renvoyer le code",
      subtitle: "pour continuer vers {{applicationName}}",
      title: "V\xE9rifiez votre messagerie"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Ce lien ne correspond pas \xE0 la demande en cours.",
        title: "Erreur de correspondance du client"
      },
      expired: {
        subtitle: "Retournez \xE0 l'onglet d'origine pour continuer",
        title: "Ce lien de v\xE9rification a expir\xE9"
      },
      failed: {
        subtitle: "Retourner \xE0 l'onglet original pour continuer",
        title: "Ce lien de v\xE9rification n'est pas valide"
      },
      formSubtitle: "Utilisez le lien de v\xE9rification envoy\xE9 par e-mail",
      formTitle: "lien de v\xE9rification",
      loading: {
        subtitle: "Vous allez bient\xF4t \xEAtre redirig\xE9",
        title: "Signing in..."
      },
      resendButton: "Renvoyer le lien",
      subtitle: "pour continuer vers {{applicationName}}",
      title: "V\xE9rifiez votre messagerie",
      unusedTab: {
        title: "Vous pouvez fermer cet onglet"
      },
      verified: {
        subtitle: "Vous serez bient\xF4t redirig\xE9",
        title: "Connexion r\xE9ussie"
      },
      verifiedSwitchTab: {
        subtitle: "Revenir \xE0 l'onglet d'origine pour continuer",
        subtitleNewTab: "Revenez \xE0 l'onglet nouvellement ouvert pour continuer",
        titleNewTab: "Connect\xE9 sur un autre onglet"
      }
    },
    forgotPassword: {
      formTitle: "Code de r\xE9initialisation du mot de passe",
      resendButton: "Vous n'avez pas re\xE7u de code ? Renvoyer",
      subtitle: "pour r\xE9initialiser votre mot de passe",
      subtitle_email: "Tout d'abord, saisissez le code envoy\xE9 \xE0 votre adresse e-mail.",
      subtitle_phone: "Tout d'abord, saisissez le code envoy\xE9 \xE0 votre t\xE9l\xE9phone.",
      title: "R\xE9initialiser le mot de passe"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "R\xE9initialiser votre mot de passe",
      label__alternativeMethods: "Ou connectez-vous avec une autre m\xE9thode.",
      title: "Mot de passe oubli\xE9 ?"
    },
    noAvailableMethods: {
      message: "Impossible de poursuivre la connexion. Aucun facteur d'authentification n'est disponible.",
      subtitle: "Une erreur s'est produite",
      title: "Impossible de se connecter"
    },
    passkey: {
      subtitle: "Utilisez une cl\xE9 de s\xE9curit\xE9 pour continuer.",
      title: "Cl\xE9 de s\xE9curit\xE9"
    },
    password: {
      actionLink: "Utiliser une autre m\xE9thode",
      subtitle: "pour continuer vers {{applicationName}}",
      title: "Tapez votre mot de passe"
    },
    passwordPwned: {
      title: "Mot de passe compromis"
    },
    phoneCode: {
      formTitle: "Code de v\xE9rification",
      resendButton: "Vous n'avez pas re\xE7u de code ? Renvoyer",
      subtitle: "pour continuer vers {{applicationName}}",
      title: "V\xE9rifiez votre t\xE9l\xE9phone"
    },
    phoneCodeMfa: {
      formTitle: "Code de v\xE9rification",
      resendButton: "Vous n'avez pas re\xE7u de code ? Renvoyer",
      subtitle: "Entrez le code envoy\xE9 \xE0 votre t\xE9l\xE9phone pour continuer.",
      title: "V\xE9rifiez votre t\xE9l\xE9phone"
    },
    resetPassword: {
      formButtonPrimary: "R\xE9initialiser",
      requiredMessage: "Pour des raisons de s\xE9curit\xE9, il est n\xE9cessaire de r\xE9initialiser votre mot de passe.",
      successMessage: "Votre mot de passe a \xE9t\xE9 modifi\xE9 avec succ\xE8s. Nous vous reconnectons, veuillez patienter un instant.",
      title: "R\xE9initialiser le mot de passe"
    },
    resetPasswordMfa: {
      detailsLabel: "Nous devons v\xE9rifier votre identit\xE9 avant de r\xE9initialiser votre mot de passe."
    },
    start: {
      actionLink: "S'inscrire",
      actionLink__join_waitlist: "Rejoindre la liste d'attente",
      actionLink__use_email: "Utiliser e-mail",
      actionLink__use_email_username: "Utiliser l'e-mail ou le nom d'utilisateur",
      actionLink__use_passkey: "Utiliser une cl\xE9 de s\xE9curit\xE9",
      actionLink__use_phone: "Utiliser t\xE9l\xE9phone",
      actionLink__use_username: "Utiliser le nom d'utilisateur",
      actionText: "Vous n'avez pas encore de compte ?",
      actionText__join_waitlist: "Inscrivez-vous sur la liste d'attente",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "pour continuer vers {{applicationName}}",
      subtitleCombined: void 0,
      title: "S'identifier",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "Le code de v\xE9rification",
      subtitle: "Entrez le code de l'application d'authentification.",
      title: "V\xE9rification en deux \xE9tapes"
    }
  },
  signInEnterPasswordTitle: "Tapez votre mot de passe",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "S'identifier",
      actionText: "Vous avez d\xE9j\xE0 un compte ?",
      subtitle: "pour continuer vers {{applicationName}}",
      title: "Remplir les champs manquants"
    },
    emailCode: {
      formSubtitle: "Entrez le code de v\xE9rification envoy\xE9 \xE0 votre adresse e-mail",
      formTitle: "Le code de v\xE9rification",
      resendButton: "Renvoyer le code",
      subtitle: "pour continuer vers {{applicationName}}",
      title: "V\xE9rifiez votre e-mail"
    },
    emailLink: {
      clientMismatch: {
        subtitle: "Ce lien ne correspond pas \xE0 la demande en cours.",
        title: "Erreur de correspondance du client"
      },
      formSubtitle: "Utilisez le lien de v\xE9rification envoy\xE9 \xE0 votre adresse e-mail",
      formTitle: "lien de v\xE9rification",
      loading: {
        title: "Cr\xE9ation de votre compte..."
      },
      resendButton: "Renvoyer le lien",
      subtitle: "pour continuer vers {{applicationName}}",
      title: "V\xE9rifiez votre e-mail",
      verified: {
        title: "Compte cr\xE9\xE9"
      },
      verifiedSwitchTab: {
        subtitle: "Revenez \xE0 l'onglet nouvellement ouvert pour continuer",
        subtitleNewTab: "Revenir \xE0 l'onglet pr\xE9c\xE9dent pour continuer",
        title: "Courriel v\xE9rifi\xE9 avec succ\xE8s"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: "J'accepte la Politique de confidentialit\xE9.",
        label__onlyTermsOfService: "J'accepte les Conditions d'utilisation.",
        label__termsOfServiceAndPrivacyPolicy: `J'accepte les {{ termsOfServiceLink || link("Conditions d'utilisation") }} et la {{ privacyPolicyLink || link("Politique de confidentialit\xE9") }}.`
      },
      continue: {
        subtitle: "Lisez et acceptez les conditions pour continuer.",
        title: "Consentement l\xE9gal"
      }
    },
    phoneCode: {
      formSubtitle: "Entrez le code de v\xE9rification envoy\xE9 \xE0 votre num\xE9ro de t\xE9l\xE9phone",
      formTitle: "Le code de v\xE9rification",
      resendButton: "Renvoyer le code",
      subtitle: "pour continuer vers {{applicationName}}",
      title: "V\xE9rifiez votre t\xE9l\xE9phone"
    },
    restrictedAccess: {
      actionLink: "Contacter le support",
      actionText: "Acc\xE8s restreint.",
      blockButton__emailSupport: "Contacter le support par e-mail",
      blockButton__joinWaitlist: "Rejoindre la liste d'attente",
      subtitle: "Vous n'avez pas la permission d'acc\xE9der \xE0 cette page.",
      subtitleWaitlist: "Inscrivez-vous pour demander l'acc\xE8s.",
      title: "Acc\xE8s restreint"
    },
    start: {
      actionLink: "S'identifier",
      actionLink__use_email: "Utiliser votre adresse e-mail",
      actionLink__use_phone: "Utiliser votre t\xE9l\xE9phone",
      actionText: "Vous avez d\xE9j\xE0 un compte ?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "pour continuer vers {{applicationName}}",
      subtitleCombined: "pour continuer vers {{applicationName}}",
      title: "Cr\xE9ez votre compte",
      titleCombined: "Cr\xE9ez votre compte"
    }
  },
  socialButtonsBlockButton: "Continuer avec {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: "Vous \xEAtes d\xE9j\xE0 membre de cette organisation.",
    captcha_invalid: "Inscription \xE9chou\xE9e en raison de validations de s\xE9curit\xE9 incorrectes. Veuillez rafra\xEEchir la page pour r\xE9essayer ou contacter le support pour obtenir de l'aide.",
    captcha_unavailable: "Inscription \xE9chou\xE9e en raison d'une validation de captcha non r\xE9ussie. Veuillez actualiser la page pour r\xE9essayer ou contacter le support pour obtenir de l'aide.",
    form_code_incorrect: "Code incorrect",
    form_identifier_exists__email_address: "Cette adresse e-mail existe d\xE9j\xE0.",
    form_identifier_exists__phone_number: "Ce num\xE9ro de t\xE9l\xE9phone existe d\xE9j\xE0.",
    form_identifier_exists__username: "Ce nom d'utilisateur existe d\xE9j\xE0.",
    form_identifier_not_found: "Nous n'avons pas trouv\xE9 de compte avec ces d\xE9tails.",
    form_param_format_invalid: "Le format est invalide",
    form_param_format_invalid__email_address: "L'adresse e-mail doit \xEAtre une adresse e-mail valide.",
    form_param_format_invalid__phone_number: "Le num\xE9ro de t\xE9l\xE9phone doit \xEAtre au format international.",
    form_param_max_length_exceeded__first_name: "Le pr\xE9nom ne doit pas d\xE9passer 256 caract\xE8res.",
    form_param_max_length_exceeded__last_name: "Le nom ne doit pas d\xE9passer 256 caract\xE8res.",
    form_param_max_length_exceeded__name: "Le nom ne doit pas d\xE9passer 256 caract\xE8res.",
    form_param_nil: "Ce champ est requis.",
    form_param_value_invalid: "La valeur fournie est invalide.",
    form_password_incorrect: "Mot de passe incorrect",
    form_password_length_too_short: "Votre mot de passe est trop court.",
    form_password_not_strong_enough: "Votre mot de passe n'est pas assez fort.",
    form_password_pwned: "Ce mot de passe a \xE9t\xE9 compromis et ne peut pas \xEAtre utilis\xE9. Veuillez essayer un autre mot de passe \xE0 la place.",
    form_password_pwned__sign_in: "Mot de passe compromis. Veuillez le r\xE9initialiser.",
    form_password_size_in_bytes_exceeded: "Votre mot de passe a d\xE9pass\xE9 le nombre maximum d'octets autoris\xE9s. Veuillez le raccourcir ou supprimer certains caract\xE8res sp\xE9ciaux.",
    form_password_validation_failed: "Mot de passe incorrect",
    form_username_invalid_character: "L'identifiant contient des caract\xE8res invalides.",
    form_username_invalid_length: "Le nombre de caract\xE8res de l'identifiant est invalide.",
    identification_deletion_failed: "Vous ne pouvez pas supprimer votre derni\xE8re identification.",
    not_allowed_access: "L'adresse e-mail ou le num\xE9ro de t\xE9l\xE9phone n'est pas autoris\xE9e \xE0 s'inscrire. Cela peut \xEAtre d\xFB \xE0 l'utilisation de '+', '=', '#' ou '.' dans votre adresse e-mail, l'utilisation d'un domaine connect\xE9 \xE0 un service de messagerie temporaire ou l'exclusion explicite. Si vous pensez que c'est une erreur, veuillez contacter le support.",
    organization_domain_blocked: "Ce domaine d'organisation est bloqu\xE9.",
    organization_domain_common: "Ce domaine est trop courant pour une organisation.",
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: "Le quota de membres de l'organisation a \xE9t\xE9 d\xE9pass\xE9.",
    organization_minimum_permissions_needed: "Permissions minimales n\xE9cessaires pour acc\xE9der \xE0 cette organisation.",
    passkey_already_exists: "Cette cl\xE9 de s\xE9curit\xE9 existe d\xE9j\xE0.",
    passkey_not_supported: "Les cl\xE9s de s\xE9curit\xE9 ne sont pas prises en charge sur cet appareil.",
    passkey_pa_not_supported: "Les cl\xE9s de s\xE9curit\xE9 ne sont pas prises en charge dans cet environnement.",
    passkey_registration_cancelled: "Enregistrement de la cl\xE9 de s\xE9curit\xE9 annul\xE9.",
    passkey_retrieval_cancelled: "R\xE9cup\xE9ration de la cl\xE9 de s\xE9curit\xE9 annul\xE9e.",
    passwordComplexity: {
      maximumLength: "moins de {{length}} caract\xE8res",
      minimumLength: "{{length}} caract\xE8res ou plus",
      requireLowercase: "une lettre minuscule",
      requireNumbers: "un chiffre",
      requireSpecialCharacter: "un caract\xE8re sp\xE9cial",
      requireUppercase: "une lettre majuscule",
      sentencePrefix: "Votre mot de passe doit contenir"
    },
    phone_number_exists: "Ce num\xE9ro de t\xE9l\xE9phone est d\xE9j\xE0 utilis\xE9. Veuillez essayer un autre.",
    session_exists: "Vous \xEAtes d\xE9j\xE0 connect\xE9.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "Votre mot de passe fonctionne mais pourrait \xEAtre plus s\xFBr. Essayez d'ajouter des caract\xE8res.",
      goodPassword: "Bien jou\xE9. C'est un excellent mot de passe.",
      notEnough: "Votre mot de passe n'est pas assez fort.",
      suggestions: {
        allUppercase: "Mettez quelques lettres en majuscules, mais pas toutes.",
        anotherWord: "Ajoutez des mots moins courants.",
        associatedYears: "\xC9vitez les ann\xE9es qui vous sont associ\xE9es. (ex: date de naissance)",
        capitalization: "Capitalisez mais pas seulement la premi\xE8re lettre.",
        dates: "\xC9vitez les dates et les ann\xE9es qui vous sont associ\xE9es. (ex: date ou ann\xE9e de naissance)",
        l33t: "\xC9vitez les substitutions de lettres pr\xE9visibles comme '@' pour 'a'.",
        longerKeyboardPattern: "Utilisez des motifs de clavier plus longs et changez de sens de frappe plusieurs fois.",
        noNeed: "Vous pouvez cr\xE9er des mots de passe forts sans utiliser de symboles, de chiffres ou de lettres majuscules.",
        pwned: "Si vous utilisez ce mot de passe ailleurs, vous devriez le modifier.",
        recentYears: "\xC9vitez les derni\xE8res ann\xE9es.",
        repeated: "\xC9vitez les mots et les caract\xE8res r\xE9p\xE9t\xE9s.",
        reverseWords: "\xC9vitez les orthographes invers\xE9es des mots courants",
        sequences: "\xC9vitez les s\xE9quences de caract\xE8res courantes.",
        useWords: "Utilisez plusieurs mots, mais \xE9vitez les phrases courantes."
      },
      warnings: {
        common: "Ce mot de passe est couramment utilis\xE9.",
        commonNames: "Les noms communs et les noms de famille sont faciles \xE0 deviner.",
        dates: "Les dates sont faciles \xE0 deviner.",
        extendedRepeat: "Les caract\xE8res r\xE9p\xE9t\xE9s comme 'abcabcabc' sont faciles \xE0 deviner.",
        keyPattern: "Des motifs de clavier courts sont faciles \xE0 deviner.",
        namesByThemselves: "Des noms ou des pr\xE9noms simples sont faciles \xE0 deviner.",
        pwned: "Votre mot de passe a \xE9t\xE9 divulgu\xE9 suite \xE0 une violation de donn\xE9es sur Internet.",
        recentYears: "Les ann\xE9es r\xE9centes sont faciles \xE0 deviner.",
        sequences: "Des s\xE9quences de caract\xE8res communs comme 'abc' sont faciles \xE0 deviner.",
        similarToCommon: "Ce mot de passe est similaire \xE0 un mot de passe couramment utilis\xE9.",
        simpleRepeat: "Les caract\xE8res r\xE9p\xE9t\xE9s comme 'aaa' sont faciles \xE0 deviner.",
        straightRow: "Les lignes droites de touches de votre clavier sont faciles \xE0 deviner.",
        topHundred: "Ce mot de passe est fr\xE9quemment utilis\xE9.",
        topTen: "Ce mot de passe est tr\xE8s utilis\xE9.",
        userInputs: "Le mot de passe ne doit pas comporter de donn\xE9es personnelles ou li\xE9es au site.",
        wordByItself: "Les mots simples sont faciles \xE0 deviner."
      }
    }
  },
  userButton: {
    action__addAccount: "Ajouter un compte",
    action__manageAccount: "G\xE9rer son compte",
    action__signOut: "D\xE9connexion",
    action__signOutAll: "Se d\xE9connecter de tous les comptes"
  },
  userProfile: {
    backupCodePage: {
      actionLabel__copied: "Copi\xE9 !",
      actionLabel__copy: "Copier tous les codes",
      actionLabel__download: "T\xE9l\xE9charger en .txt",
      actionLabel__print: "Imprimer",
      infoText1: "Les codes de r\xE9cup\xE9ration seront activ\xE9s pour ce compte.",
      infoText2: "Gardez les codes de r\xE9cup\xE9ration secrets et stockez-les en toute s\xE9curit\xE9. Vous pouvez r\xE9g\xE9n\xE9rer les codes de r\xE9cup\xE9ration si vous pensez qu'ils ont \xE9t\xE9 compromis.",
      subtitle__codelist: "Conservez-les en toute s\xE9curit\xE9 et gardez-les secrets.",
      successMessage: "Les codes de r\xE9cup\xE9ration sont maintenant activ\xE9s. Vous pouvez utiliser l'un d'entre eux pour vous connecter \xE0 votre compte, si vous perdez l'acc\xE8s \xE0 votre dispositif d'authentification. Chaque code ne peut \xEAtre utilis\xE9 qu'une seule fois.",
      successSubtitle: "Vous pouvez utiliser l'un d'entre eux pour vous connecter \xE0 votre compte, si vous perdez l'acc\xE8s \xE0 votre dispositif d'authentification.",
      title: "Ajouter la v\xE9rification du code de r\xE9cup\xE9ration",
      title__codelist: "Codes de r\xE9cup\xE9ration"
    },
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "S\xE9lectionnez un fournisseur pour connecter votre compte.",
      formHint__noAccounts: "Aucun fournisseur de compte externe n'est disponible.",
      removeResource: {
        messageLine1: "{{identifier}} sera supprim\xE9 de ce compte.",
        messageLine2: "Vous ne pourrez plus utiliser ce compte connect\xE9 et toutes les fonctionnalit\xE9s d\xE9pendantes ne fonctionneront plus.",
        successMessage: "{{connectedAccount}} a \xE9t\xE9 supprim\xE9 de votre compte.",
        title: "Supprimer le compte connect\xE9"
      },
      socialButtonsBlockButton: "Connecter {{provider|titleize}} compte",
      successMessage: "Le fournisseur a \xE9t\xE9 ajout\xE9 \xE0 votre compte",
      title: "Ajouter un compte connect\xE9"
    },
    deletePage: {
      actionDescription: 'Saisissez "Supprimer le compte" ci-dessous pour continuer.',
      confirm: "Supprimer le compte",
      messageLine1: "\xCAtes-vous s\xFBr(e) de vouloir supprimer votre compte ?",
      messageLine2: "Cette action est d\xE9finitive et irr\xE9versible.",
      title: "Supprimer le compte"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "Un e-mail contenant un code de v\xE9rification sera envoy\xE9 \xE0 cette adresse e-mail.",
        formSubtitle: "Saisissez le code de v\xE9rification envoy\xE9 \xE0 {{identifier}}",
        formTitle: "Le code de v\xE9rification",
        resendButton: "Renvoyer le lien",
        successMessage: "L'e-mail {{identifier}} a \xE9t\xE9 v\xE9rifi\xE9 et ajout\xE9 \xE0 votre compte."
      },
      emailLink: {
        formHint: "Un e-mail contenant un lien de v\xE9rification sera envoy\xE9 \xE0 cette adresse e-mail.",
        formSubtitle: "Cliquez sur le lien de v\xE9rification dans l'e-mail envoy\xE9 \xE0 {{identifier}}",
        formTitle: "lien de v\xE9rification",
        resendButton: "Renvoyer le lien",
        successMessage: "L'e-mail {{identifier}} a \xE9t\xE9 v\xE9rifi\xE9 et ajout\xE9 \xE0 votre compte."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} sera supprim\xE9 de ce compte.",
        messageLine2: "Vous ne pourrez plus vous connecter avec cette adresse e-mail.",
        successMessage: "{{emailAddress}} a \xE9t\xE9 supprim\xE9 de votre compte.",
        title: "Supprimer l'adresse e-mail"
      },
      title: "Ajouter une adresse e-mail",
      verifyTitle: "Verifier un e-mail"
    },
    formButtonPrimary__add: "Ajouter",
    formButtonPrimary__continue: "Continuer",
    formButtonPrimary__finish: "Retour",
    formButtonPrimary__remove: "Supprimer",
    formButtonPrimary__save: "Sauvegarder",
    formButtonReset: "Annuler",
    mfaPage: {
      formHint: "S\xE9lectionnez une m\xE9thode \xE0 ajouter.",
      title: "Ajouter la v\xE9rification en deux \xE9tapes"
    },
    mfaPhoneCodePage: {
      backButton: "Utiliser un num\xE9ro existant",
      primaryButton__addPhoneNumber: "Ajouter un num\xE9ro de t\xE9l\xE9phone",
      removeResource: {
        messageLine1: "{{identifier}} ne recevra plus de codes de validation lors de la connexion.",
        messageLine2: "Votre compte sera moins s\xE9curis\xE9. Souhaitez-vous continuer ?",
        successMessage: "La v\xE9rification en deux \xE9tapes du code SMS a \xE9t\xE9 supprim\xE9e pour {{mfaPhoneCode}}",
        title: "Supprimer la v\xE9rification en deux \xE9tapes"
      },
      subtitle__availablePhoneNumbers: "S\xE9lectionnez un num\xE9ro de t\xE9l\xE9phone pour vous inscrire \xE0 la v\xE9rification en deux \xE9tapes du code SMS.",
      subtitle__unavailablePhoneNumbers: "Il n'y a pas de num\xE9ros de t\xE9l\xE9phone disponibles pour s'inscrire \xE0 la v\xE9rification en deux \xE9tapes du code SMS.",
      successMessage1: "Lors de la connexion, vous devrez entrer un code de v\xE9rification envoy\xE9 \xE0 ce num\xE9ro de t\xE9l\xE9phone comme \xE9tape suppl\xE9mentaire.",
      successMessage2: "Enregistrez ces codes de r\xE9cup\xE9rations et conservez-les en lieu s\xFBr. Si vous perdez l'acc\xE8s \xE0 votre appareil d'authentification, vous pourrez utiliser les codes de r\xE9cup\xE9rations pour vous connecter.",
      successTitle: "V\xE9rification par SMS activ\xE9e",
      title: "Ajouter la v\xE9rification du code SMS"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Scannez le QR code \xE0 la place",
        buttonUnableToScan__nonPrimary: "Vous ne pouvez pas scanner le QR code ?",
        infoText__ableToScan: "Configurez une nouvelle m\xE9thode de connexion dans votre application d'authentification et scannez le QR code suivant pour le lier \xE0 votre compte.",
        infoText__unableToScan: "Configurez une nouvelle m\xE9thode de connexion dans votre authentificateur et entrez la cl\xE9 fournie ci-dessous.",
        inputLabel__unableToScan1: "Assurez-vous que les mots de passe bas\xE9s sur le temps ou \xE0 usage unique sont activ\xE9s, puis terminez la liaison de votre compte.",
        inputLabel__unableToScan2: "Alternativement, si votre authentificateur prend en charge les URI TOTP, vous pouvez \xE9galement copier l'URI complet."
      },
      removeResource: {
        messageLine1: "Les codes de v\xE9rification de cet authentificateur ne seront plus requis lors de la connexion.",
        messageLine2: "Votre compte sera moins s\xE9curis\xE9. Souhaitez-vous continuer ?",
        successMessage: "La v\xE9rification en deux \xE9tapes via l'application d'authentification a \xE9t\xE9 supprim\xE9e.",
        title: "Supprimer la v\xE9rification en deux \xE9tapes"
      },
      successMessage: "La v\xE9rification en deux \xE9tapes est maintenant activ\xE9e. Lors de la connexion, vous devrez saisir un code de v\xE9rification de cet authentificateur comme \xE9tape suppl\xE9mentaire.",
      title: "Ajouter une application d'authentification",
      verifySubtitle: "Entrez le code de v\xE9rification g\xE9n\xE9r\xE9 par votre application d'authentification",
      verifyTitle: "Le code de v\xE9rification"
    },
    mobileButton__menu: "Menu",
    navbar: {
      account: "Compte",
      billing: void 0,
      description: "G\xE9rer votre compte.",
      security: "S\xE9curit\xE9",
      title: "Profil"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: "\xCAtes-vous s\xFBr de vouloir supprimer cette cl\xE9 de s\xE9curit\xE9 ?",
        title: "Supprimer la cl\xE9 de s\xE9curit\xE9"
      },
      subtitle__rename: "Renommez votre cl\xE9 de s\xE9curit\xE9 pour une identification facile.",
      title__rename: "Renommer la cl\xE9 de s\xE9curit\xE9"
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "Il est recommand\xE9 de se d\xE9connecter de tous les autres appareils qui pourraient avoir utilis\xE9 votre ancien mot de passe.",
      readonly: "Votre mot de passe ne peut pas \xEAtre modifi\xE9 pour l'instant car vous ne pouvez vous connecter qu'\xE0 l'aide de la connexion entreprise.",
      successMessage__set: "Votre mot de passe a \xE9t\xE9 mis \xE0 jour.",
      successMessage__signOutOfOtherSessions: "Tous les autres appareils ont \xE9t\xE9 d\xE9connect\xE9s.",
      successMessage__update: "Votre mot de passe a \xE9t\xE9 mis \xE0 jour.",
      title__set: "Mettre \xE0 jour le mot de passe",
      title__update: "Changer le mot de passe"
    },
    phoneNumberPage: {
      infoText: "Un SMS contenant un lien de v\xE9rification sera envoy\xE9 \xE0 ce num\xE9ro de t\xE9l\xE9phone.",
      removeResource: {
        messageLine1: "{{identifier}} sera supprim\xE9 de ce compte.",
        messageLine2: "Vous ne pourrez plus vous connecter avec ce num\xE9ro de t\xE9l\xE9phone.",
        successMessage: "{{phoneNumber}} a \xE9t\xE9 supprim\xE9 de votre compte.",
        title: "Supprimer le num\xE9ro de t\xE9l\xE9phone"
      },
      successMessage: "{{identifier}} a \xE9t\xE9 v\xE9rifi\xE9 et ajout\xE9 \xE0 votre compte.",
      title: "Ajouter un num\xE9ro de t\xE9l\xE9phone",
      verifySubtitle: "Saisissez le code de v\xE9rification envoy\xE9 \xE0 {{identifier}}",
      verifyTitle: "V\xE9rification du num\xE9ro de t\xE9l\xE9phone"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "T\xE9l\xE9chargez une image JPG, PNG, GIF ou WEBP inf\xE9rieure \xE0 10 Mo",
      imageFormDestructiveActionSubtitle: "Supprimer l'image",
      imageFormSubtitle: "T\xE9l\xE9charger une image",
      imageFormTitle: "Photo de profil",
      readonly: "Les informations de votre profil ont \xE9t\xE9 fournies par la connexion d'entreprise et ne peuvent pas \xEAtre modifi\xE9es.",
      successMessage: "Votre profil a \xE9t\xE9 mis a jour.",
      title: "Mettre \xE0 jour le profil"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Se d\xE9connecter de l'appareil",
        title: "Appareils actifs"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "R\xE9essayer",
        actionLabel__reauthorize: "Autoriser maintenant",
        destructiveActionTitle: "Retirer",
        primaryButton: "Connecter le compte",
        subtitle__disconnected: "Compte d\xE9connect\xE9. Connectez-vous \xE0 nouveau pour acc\xE9der aux fonctionnalit\xE9s.",
        subtitle__reauthorize: "Les autorisations requises ont \xE9t\xE9 mises \xE0 jour, ce qui peut entra\xEEner des fonctionnalit\xE9s limit\xE9es. Veuillez r\xE9-autoriser cette application pour \xE9viter tout probl\xE8me.",
        title: "Comptes connect\xE9s"
      },
      dangerSection: {
        deleteAccountButton: "Supprimer le compte",
        title: "Danger"
      },
      emailAddressesSection: {
        destructiveAction: "Supprimer l'adresse e-mail",
        detailsAction__nonPrimary: "D\xE9finir comme principale",
        detailsAction__primary: "Compl\xE9ter la v\xE9rification",
        detailsAction__unverified: "Compl\xE9ter la v\xE9rification",
        primaryButton: "Ajouter une adresse e-mail",
        title: "Adresses e-mail"
      },
      enterpriseAccountsSection: {
        title: "Comptes entreprises"
      },
      headerTitle__account: "Compte",
      headerTitle__security: "S\xE9curit\xE9",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "R\xE9g\xE9n\xE9rer les codes",
          headerTitle: "Codes de r\xE9cup\xE9ration",
          subtitle__regenerate: "Obtenez de nouveaux codes de r\xE9cup\xE9ration s\xE9curis\xE9s. Les codes de r\xE9cup\xE9ration ant\xE9rieurs seront supprim\xE9s et ne pourront pas \xEAtre utilis\xE9s.",
          title__regenerate: "R\xE9g\xE9n\xE9rer les codes de r\xE9cup\xE9ration"
        },
        phoneCode: {
          actionLabel__setDefault: "D\xE9finir par d\xE9faut",
          destructiveActionLabel: "Supprimer le num\xE9ro de t\xE9l\xE9phone"
        },
        primaryButton: "Ajouter la v\xE9rification en deux \xE9tapes",
        title: "V\xE9rification en deux \xE9tapes",
        totp: {
          destructiveActionTitle: "D\xE9sactiver",
          headerTitle: "Application d'authentification"
        }
      },
      passkeysSection: {
        menuAction__destructive: "Supprimer",
        menuAction__rename: "Renommer",
        primaryButton: void 0,
        title: "Cl\xE9s de s\xE9curit\xE9"
      },
      passwordSection: {
        primaryButton__setPassword: "D\xE9finir le mot de passe",
        primaryButton__updatePassword: "Changer le mot de passe",
        title: "Mot de passe"
      },
      phoneNumbersSection: {
        destructiveAction: "Supprimer le num\xE9ro de t\xE9l\xE9phone",
        detailsAction__nonPrimary: "D\xE9finir comme principale",
        detailsAction__primary: "Compl\xE9ter la v\xE9rification",
        detailsAction__unverified: "Compl\xE9ter la v\xE9rification",
        primaryButton: "Ajouter un num\xE9ro de t\xE9l\xE9phone",
        title: "Num\xE9ros de t\xE9l\xE9phone"
      },
      profileSection: {
        primaryButton: "Mettre \xE0 jour le profil",
        title: "Profil"
      },
      usernameSection: {
        primaryButton__setUsername: "D\xE9finir le nom d'utilisateur",
        primaryButton__updateUsername: "Changer le nom d'utilisateur",
        title: "Nom d'utilisateur"
      },
      web3WalletsSection: {
        destructiveAction: "Supprimer le portefeuille",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Portefeuilles Web3",
        title: "Portefeuilles Web3"
      }
    },
    usernamePage: {
      successMessage: "Votre nom d'utilisateur a \xE9t\xE9 mis \xE0 jour.",
      title__set: "Mettre \xE0 jour le nom d'utilisateur",
      title__update: "Mettre \xE0 jour le nom d'utilisateur"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} sera supprim\xE9 de ce compte.",
        messageLine2: "Vous ne pourrez plus vous connecter avec ce portefeuille web3.",
        successMessage: "{{web3Wallet}} a \xE9t\xE9 supprim\xE9 de votre compte.",
        title: "Supprimer le portefeuille Web3"
      },
      subtitle__availableWallets: "S\xE9lectionnez un portefeuille Web3 pour vous connecter \xE0 votre compte.",
      subtitle__unavailableWallets: "Il n'y a pas de portefeuilles Web3 disponibles.",
      successMessage: "Le portefeuille a \xE9t\xE9 ajout\xE9 \xE0 votre compte.",
      title: "Ajouter un portefeuille Web3",
      web3WalletButtonsBlockButton: "Utiliser un portefeuille Web3"
    }
  },
  waitlist: {
    start: {
      actionLink: "Rejoindre la liste d'attente",
      actionText: "Int\xE9ress\xE9 ? Rejoignez-nous !",
      formButton: "S'inscrire",
      subtitle: "Soyez parmi les premiers \xE0 d\xE9couvrir notre nouveau service.",
      title: "Rejoindre la liste d'attente"
    },
    success: {
      message: "Merci ! Vous avez \xE9t\xE9 ajout\xE9 \xE0 la liste d'attente.",
      subtitle: "Nous vous contacterons bient\xF4t avec plus de d\xE9tails.",
      title: "Inscription r\xE9ussie"
    }
  }
};
export {
  frFR
};
//# sourceMappingURL=fr-FR.mjs.map