// src/ro-RO.ts
var roRO = {
  locale: "ro-RO",
  backButton: "\xCEnap<PERSON>",
  badge__activePlan: void 0,
  badge__canceledEndsAt: void 0,
  badge__currentPlan: void 0,
  badge__default: "Implicit",
  badge__endsAt: void 0,
  badge__expired: void 0,
  badge__otherImpersonatorDevice: "Alt dispozitiv de imita\u021Bie",
  badge__primary: "Principal\u0103",
  badge__renewsAt: void 0,
  badge__requiresAction: "Necesit\u0103 ac\u021Biune",
  badge__startsAt: void 0,
  badge__thisDevice: "Acest dispozitiv",
  badge__unverified: "Nedeclarat",
  badge__upcomingPlan: void 0,
  badge__userDevice: "Dispozitiv de utilizator",
  badge__you: "Tu",
  commerce: {
    addPaymentMethod: void 0,
    alwaysFree: void 0,
    annually: void 0,
    availableFeatures: void 0,
    billedAnnually: void 0,
    billedMonthlyOnly: void 0,
    cancelSubscription: void 0,
    cancelSubscriptionAccessUntil: void 0,
    cancelSubscriptionNoCharge: void 0,
    cancelSubscriptionTitle: void 0,
    cannotSubscribeMonthly: void 0,
    checkout: {
      description__paymentSuccessful: void 0,
      description__subscriptionSuccessful: void 0,
      downgradeNotice: void 0,
      emailForm: {
        subtitle: void 0,
        title: void 0
      },
      lineItems: {
        title__paymentMethod: void 0,
        title__statementId: void 0,
        title__subscriptionBegins: void 0,
        title__totalPaid: void 0
      },
      perMonth: void 0,
      title: void 0,
      title__paymentSuccessful: void 0,
      title__subscriptionSuccessful: void 0
    },
    creditRemainder: void 0,
    defaultFreePlanActive: void 0,
    free: void 0,
    getStarted: void 0,
    keepSubscription: void 0,
    manage: void 0,
    manageSubscription: void 0,
    month: void 0,
    monthly: void 0,
    pastDue: void 0,
    pay: void 0,
    paymentMethods: void 0,
    paymentSource: {
      applePayDescription: {
        annual: void 0,
        monthly: void 0
      },
      dev: {
        anyNumbers: void 0,
        cardNumber: void 0,
        cvcZip: void 0,
        developmentMode: void 0,
        expirationDate: void 0,
        testCardInfo: void 0
      }
    },
    popular: void 0,
    pricingTable: {
      billingCycle: void 0,
      included: void 0
    },
    reSubscribe: void 0,
    seeAllFeatures: void 0,
    subscribe: void 0,
    subtotal: void 0,
    switchPlan: void 0,
    switchToAnnual: void 0,
    switchToMonthly: void 0,
    totalDueToday: void 0,
    viewFeatures: void 0,
    year: void 0
  },
  createOrganization: {
    formButtonSubmit: "Crea\u021Bi o organiza\u021Bie",
    invitePage: {
      formButtonReset: "Skip"
    },
    title: "Crea\u021Bi o organiza\u021Bie"
  },
  dates: {
    lastDay: "Ieri la {{ date | timeString('en-US') }}",
    next6Days: "{{ date | weekday('en-US','long') }} la {{ date | timeString('en-US') }}",
    nextDay: "M\xE2ine la {{ date | timeString('en-US') }}",
    numeric: "{{ date | numeric('en-US') }}}",
    previous6Days: "Ultimul {{ date | weekday('en-US','long') }} la {{ date | timeString('en-US') }}",
    sameDay: "Ast\u0103zi la {{ date | timeString('en-US') }}"
  },
  dividerText: "sau",
  footerActionLink__alternativePhoneCodeProvider: void 0,
  footerActionLink__useAnotherMethod: "Utiliza\u021Bi o alt\u0103 metod\u0103",
  footerPageLink__help: "Ajutor",
  footerPageLink__privacy: "Confiden\u021Bialitate",
  footerPageLink__terms: "Termeni",
  formButtonPrimary: "Continua\u021Bi",
  formButtonPrimary__verify: "Verify",
  formFieldAction__forgotPassword: "A\u021Bi uitat parola?",
  formFieldError__matchingPasswords: "Parolele se potrivesc.",
  formFieldError__notMatchingPasswords: "Parolele nu se potrivesc.",
  formFieldError__verificationLinkExpired: "Linkul de verificare a expirat. V\u0103 rug\u0103m s\u0103 solicita\u021Bi un nou link.",
  formFieldHintText__optional: "Op\u021Bional",
  formFieldHintText__slug: "Un slug este un ID lizibil de c\u0103tre om, care trebuie s\u0103 fie unic. Este adesea utilizat \xEEn URL-uri.",
  formFieldInputPlaceholder__backupCode: void 0,
  formFieldInputPlaceholder__confirmDeletionUserAccount: "Delete account",
  formFieldInputPlaceholder__emailAddress: void 0,
  formFieldInputPlaceholder__emailAddress_username: void 0,
  formFieldInputPlaceholder__emailAddresses: "Introduce\u021Bi sau lipi\u021Bi una sau mai multe adrese de e-mail, separate prin spa\u021Bii sau virgule",
  formFieldInputPlaceholder__firstName: void 0,
  formFieldInputPlaceholder__lastName: void 0,
  formFieldInputPlaceholder__organizationDomain: void 0,
  formFieldInputPlaceholder__organizationDomainEmailAddress: void 0,
  formFieldInputPlaceholder__organizationName: void 0,
  formFieldInputPlaceholder__organizationSlug: void 0,
  formFieldInputPlaceholder__password: void 0,
  formFieldInputPlaceholder__phoneNumber: void 0,
  formFieldInputPlaceholder__username: void 0,
  formFieldLabel__automaticInvitations: "Activa\u021Bi invita\u021Biile automate pentru acest domeniu",
  formFieldLabel__backupCode: "Cod de rezerv\u0103",
  formFieldLabel__confirmDeletion: "Confirmare",
  formFieldLabel__confirmPassword: "Confirma\u021Bi parola",
  formFieldLabel__currentPassword: "Parola curent\u0103",
  formFieldLabel__emailAddress: "Adresa de e-mail",
  formFieldLabel__emailAddress_username: "Adresa de e-mail sau numele de utilizator",
  formFieldLabel__emailAddresses: "Adrese de e-mail",
  formFieldLabel__firstName: "Prenume",
  formFieldLabel__lastName: "Nume",
  formFieldLabel__newPassword: "Parol\u0103 nou\u0103",
  formFieldLabel__organizationDomain: "Domeniu",
  formFieldLabel__organizationDomainDeletePending: "\u0218terge\u021Bi invita\u021Biile \u0219i sugestiile \xEEn a\u0219teptare",
  formFieldLabel__organizationDomainEmailAddress: "Adresa de e-mail de verificare",
  formFieldLabel__organizationDomainEmailAddressDescription: "Introduce\u021Bi o adres\u0103 de e-mail sub acest domeniu pentru a primi un cod \u0219i pentru a verifica acest domeniu.",
  formFieldLabel__organizationName: "Numele organiza\u021Biei",
  formFieldLabel__organizationSlug: "Slug URL",
  formFieldLabel__passkeyName: void 0,
  formFieldLabel__password: "Parola",
  formFieldLabel__phoneNumber: "Num\u0103r de telefon",
  formFieldLabel__role: "Rol",
  formFieldLabel__signOutOfOtherSessions: "Deconecta\u021Bi-v\u0103 de pe toate celelalte dispozitive",
  formFieldLabel__username: "Nume utilizator",
  impersonationFab: {
    action__signOut: "Deconecta\u021Bi-v\u0103",
    title: "Conectat ca {{identifier}}"
  },
  maintenanceMode: void 0,
  membershipRole__admin: "Admin",
  membershipRole__basicMember: "Membru",
  membershipRole__guestMember: "Invitat",
  organizationList: {
    action__createOrganization: "Crea\u021Bi o organiza\u021Bie",
    action__invitationAccept: "Al\u0103tura\u021Bi-v\u0103",
    action__suggestionsAccept: "Cerere de aderare",
    createOrganization: "Crea\u021Bi o organiza\u021Bie",
    invitationAcceptedLabel: "S-a al\u0103turat",
    subtitle: "pentru a continua la {{applicationName}}",
    suggestionsAcceptedLabel: "\xCEn curs de aprobare",
    title: "Selecta\u021Bi un cont",
    titleWithoutPersonal: "Selecta\u021Bi o organiza\u021Bie"
  },
  organizationProfile: {
    badge__automaticInvitation: "Invita\u021Bii automate",
    badge__automaticSuggestion: "Sugestii automate",
    badge__manualInvitation: "F\u0103r\u0103 \xEEnscriere automat\u0103",
    badge__unverified: "Nedeclarat",
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    createDomainPage: {
      subtitle: "Ad\u0103uga\u021Bi domeniul de verificat. Utilizatorii cu adrese de e-mail la acest domeniu se pot al\u0103tura organiza\u021Biei \xEEn mod automat sau pot solicita s\u0103 se al\u0103ture.",
      title: "Ad\u0103uga\u021Bi domeniul"
    },
    invitePage: {
      detailsTitle__inviteFailed: "Invita\u021Biile nu au putut fi trimise. Exist\u0103 deja invita\u021Bii \xEEn a\u0219teptare pentru urm\u0103toarele adrese de e-mail: {{email_addresses}}.",
      formButtonPrimary__continue: "Trimite\u021Bi invita\u021Bii",
      selectDropdown__role: "Select role",
      subtitle: "Invita\u021Bi noi membri \xEEn aceast\u0103 organiza\u021Bie",
      successMessage: "Invita\u021Bii trimise cu succes",
      title: "Invita\u021Bi membri"
    },
    membersPage: {
      action__invite: "Invita\u021Bi",
      action__search: void 0,
      activeMembersTab: {
        menuAction__remove: "\xCEndep\u0103rta\u021Bi membrul",
        tableHeader__actions: void 0,
        tableHeader__joined: "S-a al\u0103turat",
        tableHeader__role: "Rol",
        tableHeader__user: "Utilizator"
      },
      detailsTitle__emptyRow: "Nu exist\u0103 membri de afi\u0219at",
      invitationsTab: {
        autoInvitations: {
          headerSubtitle: "Invita\u021Bi utilizatorii prin conectarea unui domeniu de e-mail cu organiza\u021Bia dvs. Oricine se \xEEnscrie cu un domeniu de e-mail corespunz\u0103tor va putea s\u0103 se al\u0103ture organiza\u021Biei oric\xE2nd.",
          headerTitle: "Invita\u021Bii automate",
          primaryButton: "Gestiona\u021Bi domeniile verificate"
        },
        table__emptyRow: "Nu exist\u0103 invita\u021Bii de afi\u0219are"
      },
      invitedMembersTab: {
        menuAction__revoke: "Revocarea invita\u021Biei",
        tableHeader__invited: "Invitat"
      },
      requestsTab: {
        autoSuggestions: {
          headerSubtitle: "Utilizatorii care se \xEEnscriu cu un domeniu de e-mail corespunz\u0103tor vor putea vedea o sugestie de a solicita s\u0103 se al\u0103ture organiza\u021Biei dvs.",
          headerTitle: "Sugestii automate",
          primaryButton: "Gestiona\u021Bi domeniile verificate"
        },
        menuAction__approve: "Aprobarea",
        menuAction__reject: "Respinge\u021Bi",
        tableHeader__requested: "Accesul solicitat",
        table__emptyRow: "Nu exist\u0103 cereri de afi\u0219are"
      },
      start: {
        headerTitle__invitations: "Invita\u021Bii",
        headerTitle__members: "Membri",
        headerTitle__requests: "Cereri"
      }
    },
    navbar: {
      billing: void 0,
      description: "Manage your organization.",
      general: "General",
      members: "Members",
      title: "Organization"
    },
    plansPage: {
      alerts: {
        noPermissionsToManageBilling: void 0
      },
      title: void 0
    },
    profilePage: {
      dangerSection: {
        deleteOrganization: {
          actionDescription: "Introduce\u021Bi {{organizationName}} mai jos pentru a continua.",
          messageLine1: "Sunte\u021Bi sigur c\u0103 dori\u021Bi s\u0103 \u0219terge\u021Bi aceast\u0103 organiza\u021Bie?",
          messageLine2: "Aceast\u0103 ac\u021Biune este permanent\u0103 \u0219i ireversibil\u0103.",
          successMessage: "A\u021Bi \u0219ters organiza\u021Bia.",
          title: "\u0218terge\u021Bi organiza\u021Bia"
        },
        leaveOrganization: {
          actionDescription: "Introduce\u021Bi {{organizationName}} mai jos pentru a continua.",
          messageLine1: "E\u0219ti sigur c\u0103 vrei s\u0103 p\u0103r\u0103se\u0219ti aceast\u0103 organiza\u021Bie? Ve\u021Bi pierde accesul la aceast\u0103 organiza\u021Bie \u0219i la aplica\u021Biile sale.",
          messageLine2: "Aceast\u0103 ac\u021Biune este permanent\u0103 \u0219i ireversibil\u0103.",
          successMessage: "A\u021Bi p\u0103r\u0103sit organiza\u021Bia.",
          title: "Organizarea concediului"
        },
        title: "Pericol"
      },
      domainSection: {
        menuAction__manage: "Manage",
        menuAction__remove: "Delete",
        menuAction__verify: "Verify",
        primaryButton: "Ad\u0103uga\u021Bi domeniul",
        subtitle: "Permite\u021Bi utilizatorilor s\u0103 se al\u0103ture automat organiza\u021Biei sau s\u0103 solicite aderarea pe baza unui domeniu de e-mail verificat.",
        title: "Domenii verificate"
      },
      successMessage: "Organiza\u021Bia a fost actualizat\u0103.",
      title: "Profilul organiza\u021Biei"
    },
    removeDomainPage: {
      messageLine1: "Domeniul de e-mail {{domain}} va fi eliminat.",
      messageLine2: "Dup\u0103 aceasta, utilizatorii nu vor mai putea s\u0103 se al\u0103ture automat organiza\u021Biei.",
      successMessage: "{{domain}} a fost eliminat.",
      title: "\xCEnl\u0103tura\u021Bi domeniul"
    },
    start: {
      headerTitle__general: "General",
      headerTitle__members: "Membri",
      profileSection: {
        primaryButton: void 0,
        title: "Organization Profile",
        uploadAction__title: "Logo"
      }
    },
    verifiedDomainPage: {
      dangerTab: {
        calloutInfoLabel: "Eliminarea acestui domeniu va afecta utilizatorii invita\u021Bi.",
        removeDomainActionLabel__remove: "\xCEnl\u0103tura\u021Bi domeniul",
        removeDomainSubtitle: "Elimina\u021Bi acest domeniu din domeniile verificate",
        removeDomainTitle: "\xCEnl\u0103tura\u021Bi domeniul"
      },
      enrollmentTab: {
        automaticInvitationOption__description: "Utilizatorii sunt invita\u021Bi automat s\u0103 se al\u0103ture organiza\u021Biei atunci c\xE2nd se \xEEnscriu \u0219i se pot al\u0103tura oric\xE2nd.",
        automaticInvitationOption__label: "Invita\u021Bii automate",
        automaticSuggestionOption__description: "Utilizatorii primesc o sugestie de a solicita aderarea, dar trebuie s\u0103 fie aproba\u021Bi de un administrator \xEEnainte de a se putea al\u0103tura organiza\u021Biei.",
        automaticSuggestionOption__label: "Sugestii automate",
        calloutInfoLabel: "Schimbarea modului de \xEEnscriere va afecta doar utilizatorii noi.",
        calloutInvitationCountLabel: "Invita\u021Bii \xEEn a\u0219teptare trimise utilizatorilor: {{count}}",
        calloutSuggestionCountLabel: "Sugestii \xEEn a\u0219teptare trimise utilizatorilor: {{count}}",
        manualInvitationOption__description: "Utilizatorii pot fi invita\u021Bi doar manual \xEEn organiza\u021Bie.",
        manualInvitationOption__label: "F\u0103r\u0103 \xEEnscriere automat\u0103",
        subtitle: "Alege\u021Bi modul \xEEn care utilizatorii din acest domeniu se pot al\u0103tura organiza\u021Biei."
      },
      start: {
        headerTitle__danger: "Pericol",
        headerTitle__enrollment: "Op\u021Biuni de \xEEnscriere"
      },
      subtitle: "Domeniul {{domain}} este acum verificat. Continua\u021Bi prin selectarea modului de \xEEnscriere.",
      title: "Update {{domain}}"
    },
    verifyDomainPage: {
      formSubtitle: "Introduce\u021Bi codul de verificare trimis la adresa dvs. de e-mail",
      formTitle: "Cod de verificare",
      resendButton: "Nu a\u021Bi primit un cod? Trimite\u021Bi din nou",
      subtitle: "Domeniul {{domainName}} trebuie s\u0103 fie verificat prin e-mail.",
      subtitleVerificationCodeScreen: "Un cod de verificare a fost trimis la {{emailAddress}}. Introduce\u021Bi codul pentru a continua.",
      title: "Verific\u0103 domeniul"
    }
  },
  organizationSwitcher: {
    action__createOrganization: "Crea\u021Bi o organiza\u021Bie",
    action__invitationAccept: "Al\u0103tura\u021Bi-v\u0103",
    action__manageOrganization: "Gestiona\u021Bi organiza\u021Bia",
    action__suggestionsAccept: "Cerere de aderare",
    notSelected: "Nici o organiza\u021Bie selectat\u0103",
    personalWorkspace: "Cont personal",
    suggestionsAcceptedLabel: "\xCEn curs de aprobare"
  },
  paginationButton__next: "Urm\u0103torul",
  paginationButton__previous: "Anterior",
  paginationRowText__displaying: "Afi\u0219area",
  paginationRowText__of: "de",
  reverification: {
    alternativeMethods: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__backupCode: void 0,
      blockButton__emailCode: void 0,
      blockButton__passkey: void 0,
      blockButton__password: void 0,
      blockButton__phoneCode: void 0,
      blockButton__totp: void 0,
      getHelp: {
        blockButton__emailSupport: void 0,
        content: void 0,
        title: void 0
      },
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: void 0,
      title: void 0
    },
    emailCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    noAvailableMethods: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    },
    passkey: {
      blockButton__passkey: void 0,
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCode: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    phoneCodeMfa: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    totpMfa: {
      formTitle: void 0,
      subtitle: void 0,
      title: void 0
    }
  },
  signIn: {
    accountSwitcher: {
      action__addAccount: "Add account",
      action__signOutAll: "Sign out of all accounts",
      subtitle: "Select the account with which you wish to continue.",
      title: "Choose an account"
    },
    alternativeMethods: {
      actionLink: "Ob\u021Bine\u021Bi ajutor",
      actionText: "Don\u2019t have any of these?",
      blockButton__backupCode: "Utiliza\u021Bi un cod de rezerv\u0103",
      blockButton__emailCode: "Codul de e-mail c\u0103tre {{identifier}}",
      blockButton__emailLink: "Trimite\u021Bi un link prin e-mail c\u0103tre {{identifier}}",
      blockButton__passkey: void 0,
      blockButton__password: "Conecta\u021Bi-v\u0103 cu parola dvs",
      blockButton__phoneCode: "Trimite\u021Bi codul SMS la {{identifier}}",
      blockButton__totp: "Utiliza\u021Bi aplica\u021Bia de autentificare",
      getHelp: {
        blockButton__emailSupport: "Suport prin e-mail",
        content: "Dac\u0103 \xEEnt\xE2mpina\u021Bi dificult\u0103\u021Bi la conectarea \xEEn contul dvs., trimite\u021Bi-ne un e-mail \u0219i vom lucra cu dvs. pentru a restabili accesul c\xE2t mai cur\xE2nd posibil.",
        title: "Ob\u021Bine\u021Bi ajutor"
      },
      subtitle: "Facing issues? You can use any of these methods to sign in.",
      title: "Utiliza\u021Bi o alt\u0103 metod\u0103"
    },
    alternativePhoneCodeProvider: {
      formTitle: void 0,
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    backupCodeMfa: {
      subtitle: "pentru a continua la {{applicationName}}",
      title: "Introduce\u021Bi un cod de rezerv\u0103"
    },
    emailCode: {
      formTitle: "Cod de verificare",
      resendButton: "Nu a\u021Bi primit un cod? Trimite\u021Bi din nou",
      subtitle: "pentru a continua la {{applicationName}}",
      title: "Verific\u0103-\u021Bi e-mailul"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      expired: {
        subtitle: "Reveni\u021Bi la tab-ul ini\u021Bial pentru a continua.",
        title: "Acest link de verificare a expirat"
      },
      failed: {
        subtitle: "Reveni\u021Bi la tab-ul ini\u021Bial pentru a continua.",
        title: "Acest link de verificare nu este valid"
      },
      formSubtitle: "Folosi\u021Bi link-ul de verificare trimis pe e-mailul dvs",
      formTitle: "Link de verificare",
      loading: {
        subtitle: "Ve\u021Bi fi redirec\u021Bionat \xEEn cur\xE2nd",
        title: "Conectarea..."
      },
      resendButton: "Nu a\u021Bi primit un link? Trimite\u021Bi din nou",
      subtitle: "pentru a continua la {{applicationName}}",
      title: "Verific\u0103-\u021Bi e-mailul",
      unusedTab: {
        title: "Pute\u021Bi \xEEnchide aceast\u0103 fil\u0103"
      },
      verified: {
        subtitle: "Ve\u021Bi fi redirec\u021Bionat \xEEn cur\xE2nd",
        title: "Conectat cu succes"
      },
      verifiedSwitchTab: {
        subtitle: "Reveni\u021Bi la tab-ul ini\u021Bial pentru a continua",
        subtitleNewTab: "Reveni\u021Bi la tab-ul nou deschis pentru a continua",
        titleNewTab: "Conectat pe alt\u0103 fil\u0103"
      }
    },
    forgotPassword: {
      formTitle: "Resetarea codului de parol\u0103",
      resendButton: "Nu a\u021Bi primit un cod? Trimite\u021Bi din nou",
      subtitle: "to reset your password",
      subtitle_email: "First, enter the code sent to your email ID",
      subtitle_phone: "First, enter the code sent to your phone",
      title: "Reset password"
    },
    forgotPasswordAlternativeMethods: {
      blockButton__resetPassword: "Reseta\u021Bi-v\u0103 parola",
      label__alternativeMethods: "Sau, conecta\u021Bi-v\u0103 cu o alt\u0103 metod\u0103.",
      title: "A\u021Bi uitat parola?"
    },
    noAvailableMethods: {
      message: "Nu se poate continua autentificarea. Nu exist\u0103 niciun factor de autentificare disponibil.",
      subtitle: "S-a produs o eroare",
      title: "Nu se poate autentifica"
    },
    passkey: {
      subtitle: void 0,
      title: void 0
    },
    password: {
      actionLink: "Utiliza\u021Bi o alt\u0103 metod\u0103",
      subtitle: "pentru a continua la {{applicationName}}",
      title: "Introduce\u021Bi parola dvs"
    },
    passwordPwned: {
      title: void 0
    },
    phoneCode: {
      formTitle: "Cod de verificare",
      resendButton: "Nu a\u021Bi primit un cod? Trimite\u021Bi din nou",
      subtitle: "pentru a continua la {{applicationName}}",
      title: "Verificarea telefonului dvs"
    },
    phoneCodeMfa: {
      formTitle: "Cod de verificare",
      resendButton: "Nu a\u021Bi primit un cod? Trimite\u021Bi din nou",
      subtitle: void 0,
      title: "Verificarea telefonului dvs"
    },
    resetPassword: {
      formButtonPrimary: "Resetare parol\u0103",
      requiredMessage: "Exist\u0103 deja un cont cu o adres\u0103 de e-mail neverificat\u0103. V\u0103 rug\u0103m s\u0103 v\u0103 reseta\u021Bi parola pentru securitate.",
      successMessage: "Parola dvs. a fost schimbat\u0103 cu succes. V\u0103 rug\u0103m s\u0103 a\u0219tepta\u021Bi un moment.",
      title: "Resetare parol\u0103"
    },
    resetPasswordMfa: {
      detailsLabel: "Trebuie s\u0103 v\u0103 verific\u0103m identitatea \xEEnainte de a v\u0103 reseta parola."
    },
    start: {
      actionLink: "\xCEnscrie\u021Bi-v\u0103",
      actionLink__join_waitlist: void 0,
      actionLink__use_email: "Utiliza\u021Bi e-mailul",
      actionLink__use_email_username: "Utiliza\u021Bi e-mail sau nume de utilizator",
      actionLink__use_passkey: void 0,
      actionLink__use_phone: "Utiliza\u021Bi telefonul",
      actionLink__use_username: "Utiliza\u021Bi numele de utilizator",
      actionText: "Nu ave\u021Bi cont?",
      actionText__join_waitlist: void 0,
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "pentru a continua la {{applicationName}}",
      subtitleCombined: void 0,
      title: "Conecta\u021Bi-v\u0103",
      titleCombined: void 0
    },
    totpMfa: {
      formTitle: "Cod de verificare",
      subtitle: void 0,
      title: "Verificare \xEEn dou\u0103 etape"
    }
  },
  signInEnterPasswordTitle: "Introduce\u021Bi parola dvs",
  signUp: {
    alternativePhoneCodeProvider: {
      resendButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    continue: {
      actionLink: "Conecta\u021Bi-v\u0103",
      actionText: "Ave\u021Bi un cont?",
      subtitle: "pentru a continua la {{applicationName}}",
      title: "Completa\u021Bi c\xE2mpurile lips\u0103"
    },
    emailCode: {
      formSubtitle: "Introduce\u021Bi codul de verificare trimis la adresa dvs. de e-mail",
      formTitle: "Cod de verificare",
      resendButton: "Nu a\u021Bi primit un cod? Trimite\u021Bi din nou",
      subtitle: "pentru a continua la {{applicationName}}",
      title: "Verifica\u021Bi-v\u0103 e-mailul"
    },
    emailLink: {
      clientMismatch: {
        subtitle: void 0,
        title: void 0
      },
      formSubtitle: "Utiliza\u021Bi link-ul de verificare trimis la adresa dvs. de e-mail",
      formTitle: "Link de verificare",
      loading: {
        title: "Se creaz\u0103 contul..."
      },
      resendButton: "Nu a\u021Bi primit un link? Trimite\u021Bi din nou",
      subtitle: "pentru a continua la {{applicationName}}",
      title: "Verifica\u021Bi-v\u0103 e-mailul",
      verified: {
        title: "\xCEnregistrat cu succes"
      },
      verifiedSwitchTab: {
        subtitle: "Reveni\u021Bi la tab-ul nou deschis pentru a continua",
        subtitleNewTab: "Reveni\u021Bi la tab-ul anterior pentru a continua",
        title: "E-mail verificat cu succes"
      }
    },
    legalConsent: {
      checkbox: {
        label__onlyPrivacyPolicy: void 0,
        label__onlyTermsOfService: void 0,
        label__termsOfServiceAndPrivacyPolicy: void 0
      },
      continue: {
        subtitle: void 0,
        title: void 0
      }
    },
    phoneCode: {
      formSubtitle: "Introduce\u021Bi codul de verificare trimis la num\u0103rul dvs. de telefon",
      formTitle: "Cod de verificare",
      resendButton: "Nu a\u021Bi primit un cod? Trimite\u021Bi din nou",
      subtitle: "pentru a continua la {{applicationName}}",
      title: "Verificarea telefonului dvs"
    },
    restrictedAccess: {
      actionLink: void 0,
      actionText: void 0,
      blockButton__emailSupport: void 0,
      blockButton__joinWaitlist: void 0,
      subtitle: void 0,
      subtitleWaitlist: void 0,
      title: void 0
    },
    start: {
      actionLink: "Conecta\u021Bi-v\u0103",
      actionLink__use_email: void 0,
      actionLink__use_phone: void 0,
      actionText: "Ave\u021Bi un cont?",
      alternativePhoneCodeProvider: {
        actionLink: void 0,
        label: void 0,
        subtitle: void 0,
        title: void 0
      },
      subtitle: "pentru a continua la {{applicationName}}",
      subtitleCombined: "pentru a continua la {{applicationName}}",
      title: "Crea\u021Bi-v\u0103 un cont",
      titleCombined: "Crea\u021Bi-v\u0103 un cont"
    }
  },
  socialButtonsBlockButton: "Continua\u021Bi cu {{provider|titleize}}",
  socialButtonsBlockButtonManyInView: void 0,
  unstable__errors: {
    already_a_member_in_organization: void 0,
    captcha_invalid: "\xCEnscrierea a e\u0219uat din cauza unor valid\u0103ri de securitate nereu\u0219ite. V\u0103 rug\u0103m s\u0103 re\xEEmprosp\u0103ta\u021Bi pagina pentru a \xEEncerca din nou sau contacta\u021Bi serviciul de asisten\u021B\u0103 pentru mai mult\u0103 asisten\u021B\u0103.",
    captcha_unavailable: "\xCEnscrierea a e\u0219uat din cauza unei valid\u0103ri nereu\u0219ite a robotului. V\u0103 rug\u0103m s\u0103 re\xEEmprosp\u0103ta\u021Bi pagina pentru a \xEEncerca din nou sau contacta\u021Bi serviciul de asisten\u021B\u0103 pentru mai mult\u0103 asisten\u021B\u0103.",
    form_code_incorrect: void 0,
    form_identifier_exists__email_address: void 0,
    form_identifier_exists__phone_number: void 0,
    form_identifier_exists__username: void 0,
    form_identifier_not_found: "Nu am putut g\u0103si un cont cu aceste detalii.",
    form_param_format_invalid: void 0,
    form_param_format_invalid__email_address: "Adresa de e-mail trebuie s\u0103 fie o adres\u0103 de e-mail valid\u0103.",
    form_param_format_invalid__phone_number: "Phone number must be in a valid international format",
    form_param_max_length_exceeded__first_name: "Prenumele nu trebuie s\u0103 dep\u0103\u0219easc\u0103 256 de caractere.",
    form_param_max_length_exceeded__last_name: "Numele de familie nu trebuie s\u0103 dep\u0103\u0219easc\u0103 256 de caractere.",
    form_param_max_length_exceeded__name: "Numele nu trebuie s\u0103 dep\u0103\u0219easc\u0103 256 de caractere.",
    form_param_nil: void 0,
    form_param_value_invalid: void 0,
    form_password_incorrect: void 0,
    form_password_length_too_short: void 0,
    form_password_not_strong_enough: "Parola dvs. nu este suficient de puternic\u0103.",
    form_password_pwned: "Aceast\u0103 parol\u0103 a fost descoperit\u0103 ca parte a unei \xEEnc\u0103lc\u0103ri \u0219i nu poate fi utilizat\u0103, v\u0103 rug\u0103m s\u0103 \xEEncerca\u021Bi o alt\u0103 parol\u0103.",
    form_password_pwned__sign_in: void 0,
    form_password_size_in_bytes_exceeded: "Parola dvs. a dep\u0103\u0219it num\u0103rul maxim de octe\u021Bi permis, v\u0103 rug\u0103m s\u0103 o scurta\u021Bi sau s\u0103 elimina\u021Bi unele caractere speciale.",
    form_password_validation_failed: "Parol\u0103 incorect\u0103",
    form_username_invalid_character: void 0,
    form_username_invalid_length: void 0,
    identification_deletion_failed: "Nu v\u0103 pute\u021Bi \u0219terge ultima identificare.",
    not_allowed_access: "Adresa de e-mail sau num\u0103rul de telefon nu este permis pentru \xEEnregistrare. Acest lucru poate fi datorat utiliz\u0103rii '+', '=', '#' sau '.' \xEEn adresa dvs. de e-mail, utiliz\u0103rii unui domeniu asociat cu un serviciu de e-mail temporar sau unei excluziuni explicite.",
    organization_domain_blocked: void 0,
    organization_domain_common: void 0,
    organization_domain_exists_for_enterprise_connection: void 0,
    organization_membership_quota_exceeded: void 0,
    organization_minimum_permissions_needed: void 0,
    passkey_already_exists: void 0,
    passkey_not_supported: void 0,
    passkey_pa_not_supported: void 0,
    passkey_registration_cancelled: void 0,
    passkey_retrieval_cancelled: void 0,
    passwordComplexity: {
      maximumLength: "mai pu\u021Bin de {{length}} caractere",
      minimumLength: "{{length}} sau mai multe caractere",
      requireLowercase: "o liter\u0103 minuscul\u0103",
      requireNumbers: "un num\u0103r",
      requireSpecialCharacter: "un caracter special",
      requireUppercase: "o liter\u0103 majuscul\u0103",
      sentencePrefix: "Parola dvs. trebuie s\u0103 con\u021Bin\u0103"
    },
    phone_number_exists: "Acest num\u0103r de telefon este ocupat. V\u0103 rug\u0103m s\u0103 \xEEncerca\u021Bi altul.",
    session_exists: "Sunte\u021Bi deja conectat.",
    web3_missing_identifier: void 0,
    zxcvbn: {
      couldBeStronger: "Parola dvs. func\u021Bioneaz\u0103, dar ar putea fi mai puternic\u0103. \xCEncerca\u021Bi s\u0103 ad\u0103uga\u021Bi mai multe caractere.",
      goodPassword: "Parola dvs. \xEEndepline\u0219te toate cerin\u021Bele necesare.",
      notEnough: "Parola dvs. nu este suficient de puternic\u0103.",
      suggestions: {
        allUppercase: "Scrie\u021Bi cu majuscule unele litere, dar nu toate literele.",
        anotherWord: "Ad\u0103uga\u021Bi mai multe cuvinte care sunt mai pu\u021Bin frecvente.",
        associatedYears: "Evita\u021Bi anii care v\u0103 sunt asocia\u021Bi.",
        capitalization: "Scrie\u021Bi cu majuscul\u0103 mai mult dec\xE2t prima liter\u0103.",
        dates: "Evita\u021Bi datele \u0219i anii care v\u0103 sunt asociate.",
        l33t: 'Evita\u021Bi \xEEnlocuirile previzibile de litere, cum ar fi "@" pentru "a".',
        longerKeyboardPattern: "Utiliza\u021Bi modele de tastatur\u0103 mai lungi \u0219i schimba\u021Bi direc\u021Bia de tastare de mai multe ori.",
        noNeed: "Pute\u021Bi crea parole puternice f\u0103r\u0103 a folosi simboluri, numere sau litere majuscule.",
        pwned: "Dac\u0103 folosi\u021Bi aceast\u0103 parol\u0103 \xEEn alt\u0103 parte, ar trebui s\u0103 o schimba\u021Bi.",
        recentYears: "Evita\u021Bi ultimii ani.",
        repeated: "Evita\u021Bi cuvintele \u0219i caracterele repetate.",
        reverseWords: "Evita\u021Bi ortografia invers\u0103 a cuvintelor uzuale.",
        sequences: "Evita\u021Bi secven\u021Bele de caractere comune.",
        useWords: "Folosi\u021Bi mai multe cuvinte, dar evita\u021Bi frazele comune."
      },
      warnings: {
        common: "Aceasta este o parol\u0103 folosit\u0103 \xEEn mod obi\u0219nuit.",
        commonNames: "Numele comune \u0219i numele de familie sunt u\u0219or de ghicit.",
        dates: "Datele sunt u\u0219or de ghicit.",
        extendedRepeat: 'Modelele de caractere repetate, cum ar fi "abcabcabc", sunt u\u0219or de ghicit.',
        keyPattern: "Modelele scurte de tastatur\u0103 sunt u\u0219or de ghicit.",
        namesByThemselves: "Numele sau prenumele simple sunt u\u0219or de ghicit.",
        pwned: "Parola dvs. a fost expus\u0103 \xEEn urma unei \xEEnc\u0103lc\u0103ri a securit\u0103\u021Bii datelor pe internet.",
        recentYears: "Ultimii ani sunt u\u0219or de ghicit.",
        sequences: 'Secven\u021Bele de caractere comune, cum ar fi "abc", sunt u\u0219or de ghicit.',
        similarToCommon: "Acest lucru este similar cu o parol\u0103 folosit\u0103 \xEEn mod obi\u0219nuit.",
        simpleRepeat: 'Caracterele repetate, cum ar fi "aaa", sunt u\u0219or de ghicit.',
        straightRow: "R\xE2ndurile drepte de taste de pe tastatur\u0103 sunt u\u0219or de ghicit.",
        topHundred: "Aceasta este o parol\u0103 utilizat\u0103 frecvent.",
        topTen: "Aceasta este o parol\u0103 foarte utilizat\u0103.",
        userInputs: "Nu trebuie s\u0103 existe date personale sau legate de pagini.",
        wordByItself: "Cuvintele simple sunt u\u0219or de ghicit."
      }
    }
  },
  userButton: {
    action__addAccount: "Ad\u0103uga\u021Bi un cont",
    action__manageAccount: "Gestiona\u021Bi contul",
    action__signOut: "Deconecta\u021Bi-v\u0103",
    action__signOutAll: "Deconecta\u021Bi-v\u0103 din toate conturile"
  },
  userProfile: {
    backupCodePage: {
      actionLabel__copied: "Copiat!",
      actionLabel__copy: "Copia\u021Bi toate",
      actionLabel__download: "Descarc\u0103 .txt",
      actionLabel__print: "Imprimare",
      infoText1: "Codurile de rezerv\u0103 vor fi activate pentru acest cont.",
      infoText2: "P\u0103stra\u021Bi codurile de rezerv\u0103 \xEEn secret \u0219i p\u0103stra\u021Bi-le \xEEn siguran\u021B\u0103. Pute\u021Bi regenera codurile de rezerv\u0103 dac\u0103 b\u0103nui\u021Bi c\u0103 acestea au fost compromise.",
      subtitle__codelist: "P\u0103stra\u021Bi-le \xEEn siguran\u021B\u0103 \u0219i p\u0103stra\u021Bi-le \xEEn secret.",
      successMessage: "Codurile de rezerv\u0103 sunt acum activate. Pute\u021Bi utiliza unul dintre acestea pentru a v\u0103 conecta la contul dvs., dac\u0103 pierde\u021Bi accesul la dispozitivul de autentificare. Fiecare cod poate fi utilizat o singur\u0103 dat\u0103.",
      successSubtitle: "Pute\u021Bi utiliza unul dintre acestea pentru a v\u0103 conecta la contul dvs., dac\u0103 pierde\u021Bi accesul la dispozitivul de autentificare.",
      title: "Ad\u0103uga\u021Bi verificarea codului de rezerv\u0103",
      title__codelist: "Coduri de rezerv\u0103"
    },
    billingPage: {
      paymentSourcesSection: {
        actionLabel__default: void 0,
        actionLabel__remove: void 0,
        add: void 0,
        addSubtitle: void 0,
        cancelButton: void 0,
        formButtonPrimary__add: void 0,
        formButtonPrimary__pay: void 0,
        payWithTestCardButton: void 0,
        removeResource: {
          messageLine1: void 0,
          messageLine2: void 0,
          successMessage: void 0,
          title: void 0
        },
        title: void 0
      },
      start: {
        headerTitle__plans: void 0,
        headerTitle__statements: void 0,
        headerTitle__subscriptions: void 0
      },
      subscriptionsListSection: {
        actionLabel__newSubscription: void 0,
        actionLabel__switchPlan: void 0,
        title: void 0
      },
      subscriptionsSection: {
        actionLabel__default: void 0
      },
      switchPlansSection: {
        title: void 0
      },
      title: void 0
    },
    connectedAccountPage: {
      formHint: "Selecta\u021Bi un furnizor pentru a v\u0103 conecta contul.",
      formHint__noAccounts: "Nu exist\u0103 furnizori de conturi externe disponibili.",
      removeResource: {
        messageLine1: "{{identifier}} va fi eliminat din acest cont.",
        messageLine2: "Nu ve\u021Bi mai putea utiliza acest cont conectat, iar toate func\u021Biile dependente nu vor mai func\u021Biona.",
        successMessage: "{{connectedAccount}} a fost eliminat din contul dumneavoastr\u0103.",
        title: "\xCEnl\u0103tura\u021Bi contul conectat"
      },
      socialButtonsBlockButton: "Conecteaz\u0103 contul {{provider|titleize}}",
      successMessage: "Furnizorul a fost ad\u0103ugat \xEEn contul dvs",
      title: "Ad\u0103uga\u021Bi un cont conectat"
    },
    deletePage: {
      actionDescription: "Introduce\u021Bi Delete account (\u0218terge\u021Bi contul) mai jos pentru a continua.",
      confirm: "\u0218terge\u021Bi contul",
      messageLine1: "Sunte\u021Bi sigur c\u0103 dori\u021Bi s\u0103 v\u0103 \u0219terge\u021Bi contul?",
      messageLine2: "Aceast\u0103 ac\u021Biune este permanent\u0103 \u0219i ireversibil\u0103.",
      title: "\u0218terge\u021Bi contul"
    },
    emailAddressPage: {
      emailCode: {
        formHint: "Un e-mail con\u021Bin\xE2nd un cod de verificare va fi trimis la aceast\u0103 adres\u0103 de e-mail.",
        formSubtitle: "Introduce\u021Bi codul de verificare trimis la {{identifier}}",
        formTitle: "Cod de verificare",
        resendButton: "Nu a\u021Bi primit un cod? Trimite\u021Bi din nou",
        successMessage: "E-mailul {{identifier}} a fost ad\u0103ugat \xEEn contul dvs."
      },
      emailLink: {
        formHint: "La aceast\u0103 adres\u0103 de e-mail va fi trimis un e-mail con\u021Bin\xE2nd un link de verificare.",
        formSubtitle: "Face\u021Bi clic pe link-ul de verificare din e-mailul trimis c\u0103tre {{identifier}}",
        formTitle: "Link de verificare",
        resendButton: "Nu a\u021Bi primit un link? Trimite\u021Bi din nou",
        successMessage: "E-mailul {{identifier}} a fost ad\u0103ugat \xEEn contul dvs."
      },
      enterpriseSSOLink: {
        formButton: void 0,
        formSubtitle: void 0
      },
      formHint: void 0,
      removeResource: {
        messageLine1: "{{identifier}} va fi eliminat din acest cont.",
        messageLine2: "Nu ve\u021Bi mai putea s\u0103 v\u0103 conecta\u021Bi folosind aceast\u0103 adres\u0103 de e-mail.",
        successMessage: "{{emailAddress}} a fost eliminat din contul dvs.",
        title: "Elimina\u021Bi adresa de e-mail"
      },
      title: "Ad\u0103uga\u021Bi adresa de e-mail",
      verifyTitle: "Verify email address"
    },
    formButtonPrimary__add: "Add",
    formButtonPrimary__continue: "Continua\u021Bi",
    formButtonPrimary__finish: "Finalizare",
    formButtonPrimary__remove: "Remove",
    formButtonPrimary__save: "Save",
    formButtonReset: "Anuleaz\u0103",
    mfaPage: {
      formHint: "Selecta\u021Bi o metod\u0103 de ad\u0103ugat.",
      title: "Ad\u0103uga\u021Bi verificarea \xEEn doi pa\u0219i"
    },
    mfaPhoneCodePage: {
      backButton: "Use existing number",
      primaryButton__addPhoneNumber: "Ad\u0103uga\u021Bi un num\u0103r de telefon",
      removeResource: {
        messageLine1: "{{identifier}} nu va mai primi coduri de verificare atunci c\xE2nd se conecteaz\u0103.",
        messageLine2: "Este posibil ca contul dumneavoastr\u0103 s\u0103 nu fie la fel de sigur. Sunte\u021Bi sigur c\u0103 dori\u021Bi s\u0103 continua\u021Bi?",
        successMessage: "Verificarea \xEEn doi pa\u0219i a codului SMS a fost eliminat\u0103 pentru {{mfaPhoneCode}}",
        title: "Eliminarea verific\u0103rii \xEEn dou\u0103 etape"
      },
      subtitle__availablePhoneNumbers: "Selecta\u021Bi un num\u0103r de telefon pentru a v\u0103 \xEEnregistra pentru verificarea \xEEn doi pa\u0219i a codului SMS.",
      subtitle__unavailablePhoneNumbers: "Nu exist\u0103 numere de telefon disponibile pentru a v\u0103 \xEEnregistra pentru verificarea \xEEn doi pa\u0219i prin cod SMS.",
      successMessage1: "When signing in, you will need to enter a verification code sent to this phone number as an additional step.",
      successMessage2: "Save these backup codes and store them somewhere safe. If you lose access to your authentication device, you can use backup codes to sign in.",
      successTitle: "SMS code verification enabled",
      title: "Ad\u0103uga\u021Bi verificarea codului SMS"
    },
    mfaTOTPPage: {
      authenticatorApp: {
        buttonAbleToScan__nonPrimary: "Scana\u021Bi \xEEn schimb codul QR",
        buttonUnableToScan__nonPrimary: "Nu pute\u021Bi scana codul QR?",
        infoText__ableToScan: "Configura\u021Bi o nou\u0103 metod\u0103 de conectare \xEEn aplica\u021Bia de autentificare \u0219i scana\u021Bi urm\u0103torul cod QR pentru a o lega de contul dvs.",
        infoText__unableToScan: "Configura\u021Bi o nou\u0103 metod\u0103 de conectare \xEEn autentificatorul dvs. \u0219i introduce\u021Bi cheia furnizat\u0103 mai jos.",
        inputLabel__unableToScan1: "Asigura\u021Bi-v\u0103 c\u0103 este activat\u0103 op\u021Biunea Parole pe baz\u0103 de timp sau Parole unice, apoi finaliza\u021Bi conectarea contului dvs.",
        inputLabel__unableToScan2: "Alternativ, dac\u0103 autentificatorul dvs. accept\u0103 URI-urile TOTP, pute\u021Bi copia \u0219i URI-ul complet."
      },
      removeResource: {
        messageLine1: "Codurile de verificare de la acest autentificator nu vor mai fi necesare la autentificare.",
        messageLine2: "Este posibil ca contul dumneavoastr\u0103 s\u0103 nu fie la fel de sigur. Sunte\u021Bi sigur c\u0103 dori\u021Bi s\u0103 continua\u021Bi?",
        successMessage: "Verificarea \xEEn doi pa\u0219i prin intermediul aplica\u021Biei Authenticator a fost eliminat\u0103.",
        title: "Eliminarea verific\u0103rii \xEEn dou\u0103 etape"
      },
      successMessage: "Verificarea \xEEn doi pa\u0219i este acum activat\u0103. C\xE2nd v\u0103 conecta\u021Bi, va trebui s\u0103 introduce\u021Bi un cod de verificare de la acest autentificator ca pas suplimentar.",
      title: "Ad\u0103uga\u021Bi aplica\u021Bia de autentificare",
      verifySubtitle: "Introduce\u021Bi codul de verificare generat de autentificatorul dvs",
      verifyTitle: "Cod de verificare"
    },
    mobileButton__menu: "Meniu",
    navbar: {
      account: "Profile",
      billing: void 0,
      description: "Manage your account info.",
      security: "Security",
      title: "Account"
    },
    passkeyScreen: {
      removeResource: {
        messageLine1: void 0,
        title: void 0
      },
      subtitle__rename: void 0,
      title__rename: void 0
    },
    passwordPage: {
      checkboxInfoText__signOutOfOtherSessions: "It is recommended to sign out of all other devices which may have used your old password.",
      readonly: "\xCEn prezent, parola dvs. nu poate fi modificat\u0103, deoarece v\u0103 pute\u021Bi conecta numai prin intermediul conexiunii \xEEntreprinderii.",
      successMessage__set: "Parola dvs. a fost setat\u0103.",
      successMessage__signOutOfOtherSessions: "Toate celelalte dispozitive au fost deconectate.",
      successMessage__update: "Parola dvs. a fost actualizat\u0103.",
      title__set: "Seta\u021Bi parola",
      title__update: "Modifica\u021Bi parola"
    },
    phoneNumberPage: {
      infoText: "La acest num\u0103r de telefon va fi trimis un mesaj text con\u021Bin\xE2nd un link de verificare.",
      removeResource: {
        messageLine1: "{{identifier}} va fi eliminat din acest cont.",
        messageLine2: "Nu ve\u021Bi mai putea s\u0103 v\u0103 conecta\u021Bi folosind acest num\u0103r de telefon.",
        successMessage: "{{phoneNumber}} a fost eliminat din contul dvs.",
        title: "Elimina\u021Bi num\u0103rul de telefon"
      },
      successMessage: "{{identifier}} a fost ad\u0103ugat \xEEn contul dumneavoastr\u0103.",
      title: "Ad\u0103uga\u021Bi num\u0103rul de telefon",
      verifySubtitle: "Enter the verification code sent to {{identifier}}",
      verifyTitle: "Verify phone number"
    },
    plansPage: {
      title: void 0
    },
    profilePage: {
      fileDropAreaHint: "\xCEnc\u0103rca\u021Bi o imagine JPG, PNG, GIF sau WEBP mai mic\u0103 de 10 MB",
      imageFormDestructiveActionSubtitle: "Elimina\u021Bi imaginea",
      imageFormSubtitle: "\xCEnc\u0103rca\u021Bi imaginea",
      imageFormTitle: "Imagine de profil",
      readonly: "Informa\u021Biile din profilul dvs. au fost furnizate de c\u0103tre conexiunea cu compania \u0219i nu pot fi modificate.",
      successMessage: "Profilul dvs. a fost actualizat.",
      title: "Actualizarea profilului"
    },
    start: {
      activeDevicesSection: {
        destructiveAction: "Deconecta\u021Bi-v\u0103 de la dispozitiv",
        title: "Dispozitive active"
      },
      connectedAccountsSection: {
        actionLabel__connectionFailed: "\xCEncearc\u0103 din nou",
        actionLabel__reauthorize: "Autoriza\u021Bi acum",
        destructiveActionTitle: "Elimina\u021Bi",
        primaryButton: "Conecta\u021Bi-v\u0103 contul",
        subtitle__disconnected: void 0,
        subtitle__reauthorize: "The required scopes have been updated, and you may be experiencing limited functionality. Please re-authorize this application to avoid any issues",
        title: "Conturi conectate"
      },
      dangerSection: {
        deleteAccountButton: "\u0218terge\u021Bi contul",
        title: "Pericol"
      },
      emailAddressesSection: {
        destructiveAction: "Elimina\u021Bi adresa de e-mail",
        detailsAction__nonPrimary: "Seta\u021Bi ca principal\u0103",
        detailsAction__primary: "Verificare complet\u0103",
        detailsAction__unverified: "Verific\u0103 adresa de e-mail",
        primaryButton: "Ad\u0103uga\u021Bi o adres\u0103 de e-mail",
        title: "Adrese de e-mail"
      },
      enterpriseAccountsSection: {
        title: "Conturi de companie"
      },
      headerTitle__account: "Cont",
      headerTitle__security: "Securitate",
      mfaSection: {
        backupCodes: {
          actionLabel__regenerate: "Regenerarea codurilor",
          headerTitle: "Coduri de rezerv\u0103",
          subtitle__regenerate: "Ob\u021Bine\u021Bi un set nou de coduri de rezerv\u0103 securizate. Codurile de rezerv\u0103 anterioare vor fi \u0219terse \u0219i nu vor mai putea fi utilizate.",
          title__regenerate: "Regenerarea codurilor de rezerv\u0103"
        },
        phoneCode: {
          actionLabel__setDefault: "Setat ca implicit",
          destructiveActionLabel: "Elimina\u021Bi num\u0103rul de telefon"
        },
        primaryButton: "Ad\u0103uga\u021Bi verificarea \xEEn doi pa\u0219i",
        title: "Verificare \xEEn dou\u0103 etape",
        totp: {
          destructiveActionTitle: "Elimina\u021Bi",
          headerTitle: "Aplica\u021Bia Authenticator"
        }
      },
      passkeysSection: {
        menuAction__destructive: void 0,
        menuAction__rename: void 0,
        primaryButton: void 0,
        title: void 0
      },
      passwordSection: {
        primaryButton__setPassword: "Seta\u021Bi parola",
        primaryButton__updatePassword: "Modifica\u021Bi parola",
        title: "Parola"
      },
      phoneNumbersSection: {
        destructiveAction: "Elimina\u021Bi num\u0103rul de telefon",
        detailsAction__nonPrimary: "Seta\u021Bi ca principal",
        detailsAction__primary: "Verificare complet\u0103",
        detailsAction__unverified: "Verifica\u021Bi num\u0103rul de telefon",
        primaryButton: "Ad\u0103uga\u021Bi un num\u0103r de telefon",
        title: "Numere de telefon"
      },
      profileSection: {
        primaryButton: void 0,
        title: "Profil"
      },
      usernameSection: {
        primaryButton__setUsername: "Seta\u021Bi numele de utilizator",
        primaryButton__updateUsername: "Schimb\u0103 numele de utilizator",
        title: "Nume utilizator"
      },
      web3WalletsSection: {
        destructiveAction: "Scoate\u021Bi portofelul",
        detailsAction__nonPrimary: void 0,
        primaryButton: "Portofele Web3",
        title: "Portofele Web3"
      }
    },
    usernamePage: {
      successMessage: "Numele dvs. de utilizator a fost actualizat.",
      title__set: "Actualiza\u021Bi numele de utilizator",
      title__update: "Actualiza\u021Bi numele de utilizator"
    },
    web3WalletPage: {
      removeResource: {
        messageLine1: "{{identifier}} va fi eliminat din acest cont.",
        messageLine2: "Nu ve\u021Bi mai putea s\u0103 v\u0103 conecta\u021Bi folosind acest portofel web3.",
        successMessage: "{{web3Wallet}} a fost eliminat din contul dumneavoastr\u0103.",
        title: "\xCEndep\u0103rta\u021Bi portofelul web3"
      },
      subtitle__availableWallets: "Selecta\u021Bi un portofel web3 pentru a v\u0103 conecta la cont.",
      subtitle__unavailableWallets: "Nu exist\u0103 portofele web3 disponibile.",
      successMessage: "Portofelul a fost ad\u0103ugat \xEEn contul dumneavoastr\u0103.",
      title: "Ad\u0103uga\u021Bi portofelul web3",
      web3WalletButtonsBlockButton: void 0
    }
  },
  waitlist: {
    start: {
      actionLink: void 0,
      actionText: void 0,
      formButton: void 0,
      subtitle: void 0,
      title: void 0
    },
    success: {
      message: void 0,
      subtitle: void 0,
      title: void 0
    }
  }
};
export {
  roRO
};
//# sourceMappingURL=ro-RO.mjs.map