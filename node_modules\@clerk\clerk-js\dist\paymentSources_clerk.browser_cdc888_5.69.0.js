(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["507"],{8057:function(e,t,n){"use strict";n.d(t,{ph:()=>L,fC:()=>I,hG:()=>_,OL:()=>M});var r,o=n(9109),i=n(3799),a=n(9386),c="https://js.stripe.com/v3",u=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,l=function(){for(var e=document.querySelectorAll('script[src^="'.concat(c,'"]')),t=0;t<e.length;t++){var n=e[t];if(u.test(n.src))return n}return null},s=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",n=document.createElement("script");n.src="".concat(c).concat(t);var r=document.head||document.body;if(!r)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return r.appendChild(n),n},p=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"5.6.0",startTime:t})},d=null,f=null,m=null,h=function(e,t,n){if(null===e)return null;var r=e.apply(void 0,t);return p(r,n),r},y=!1,g=function(){return r?r:r=(null!==d?d:(d=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var n,r=l();r?r&&null!==m&&null!==f&&(r.removeEventListener("load",m),r.removeEventListener("error",f),null===(n=r.parentNode)||void 0===n||n.removeChild(r),r=s(null)):r=s(null),m=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},f=function(){t(Error("Failed to load Stripe.js"))},r.addEventListener("load",m),r.addEventListener("error",f)}catch(e){t(e);return}})).catch(function(e){return d=null,Promise.reject(e)})).catch(function(e){return r=null,Promise.reject(e)})};Promise.resolve().then(function(){return g()}).catch(function(e){y||console.warn(e)});var v=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];y=!0;var r=Date.now();return g().then(function(e){return h(e,t,r)})},S=n(9144),b=n(9626),C=n(903),E=n(4455),k=n(2672),w=n(431),P=n(9460),x=n(8487),$=n(1576),R=n(9541),j=n(7623);let T=()=>{let{organization:e}=(0,i.o8)(),{user:t}=(0,i.aF)(),n="org"===(0,$.useSubscriberTypeContext)()?e:t,{data:r,trigger:o}=(0,C.Z)({key:"commerce-payment-source-initialize",resourceId:n?.id},()=>n?.initializePaymentSource({gateway:"stripe"})),{commerceSettings:a}=(0,$.useEnvironment)(),c=r?.externalGatewayId,u=r?.externalClientSecret,l=r?.paymentMethodOrder,s=a.billing.stripePublishableKey,{data:p}=(0,b.default)(c&&s?{key:"stripe-sdk",externalGatewayId:c,stripePublishableKey:s}:null,({stripePublishableKey:e,externalGatewayId:t})=>v(e,{stripeAccount:t}),{keepPreviousData:!0,revalidateOnFocus:!1,dedupingInterval:6e4});return{stripe:p,initializePaymentSource:o,externalClientSecret:u,paymentMethodOrder:l}},[O,Z]=(0,i.uH)("AddPaymentSourceRoot"),z=({children:e,...t})=>{let{initializePaymentSource:n,externalClientSecret:r,stripe:i,paymentMethodOrder:a}=T(),[c,u]=(0,S.useState)(void 0),[l,s]=(0,S.useState)(void 0),[p,d]=(0,S.useState)(void 0);return(0,S.useEffect)(()=>{n()},[]),(0,o.tZ)(O.Provider,{value:{value:{headerTitle:c,headerSubtitle:l,submitLabel:p,setHeaderTitle:u,setHeaderSubtitle:s,setSubmitLabel:d,initializePaymentSource:n,externalClientSecret:r,stripe:i,paymentMethodOrder:a,...t}},children:e})},A=e=>{let{stripe:t,externalClientSecret:n}=Z();return t&&n?null:e.children},B=e=>{let{externalClientSecret:t,stripe:n}=Z(),{colors:r,fontWeights:i,fontSizes:c,radii:u,space:l}=(0,R.useAppearance)().parsedInternalTheme,s={variables:{colorPrimary:(0,j.YV)(r.$primary500),colorBackground:(0,j.YV)(r.$colorInputBackground),colorText:(0,j.YV)(r.$colorText),colorTextSecondary:(0,j.YV)(r.$colorTextSecondary),colorSuccess:(0,j.YV)(r.$success500),colorDanger:(0,j.YV)(r.$danger500),colorWarning:(0,j.YV)(r.$warning500),fontWeightNormal:i.$normal.toString(),fontWeightMedium:i.$medium.toString(),fontWeightBold:i.$bold.toString(),fontSizeXl:c.$xl,fontSizeLg:c.$lg,fontSizeSm:c.$md,fontSizeXs:c.$sm,borderRadius:u.$md,spacingUnit:l.$1}};return n&&t?(0,o.tZ)(a.Elements,{stripe:n,options:{clientSecret:t,appearance:s},children:e.children},t):null},I=e=>{let{children:t,...n}=e;return(0,o.BX)(z,{...n,children:[(0,o.tZ)(A,{children:(0,o.tZ)(R.Flex,{direction:"row",align:"center",justify:"center",sx:e=>({width:"100%",minHeight:e.sizes.$60}),children:(0,o.tZ)(R.Spinner,{size:"lg",colorScheme:"primary",elementDescriptor:R.descriptors.spinner})})}),(0,o.tZ)(B,{children:(0,o.tZ)(K,{children:t})})]})},D=(e,t)=>{(0,S.useRef)(()=>{t(e)}),(0,S.useEffect)(()=>{t(e)},[e,t])},L=({text:e})=>{let{setHeaderTitle:t}=Z();return D(e,t),null},_=({text:e})=>{let{setHeaderSubtitle:t}=Z();return D(e,t),null},M=({text:e})=>{let{setSubmitLabel:t}=Z();return D(e,t),null},K=({children:e})=>{let{headerTitle:t,headerSubtitle:n,submitLabel:r,checkout:i,initializePaymentSource:c,onSuccess:u,cancelAction:l,paymentMethodOrder:s}=Z(),[p,d]=(0,S.useState)(!1),f=(0,a.useStripe)(),m=(0,k.useCardState)(),h=(0,a.useElements)(),{displayConfig:y}=(0,$.useEnvironment)(),{t:g}=(0,R.useLocalizations)(),v=(0,$.useSubscriberTypeLocalizationRoot)(),b=async e=>{if(e.preventDefault(),!f||!h)return;m.setLoading(),m.setError(void 0);let{setupIntent:t,error:n}=await f.confirmSetup({elements:h,confirmParams:{return_url:""},redirect:"if_required"});if(!n)try{await u({stripeSetupIntent:t})}catch(e){(0,j.S3)(e,[],m.setError)}finally{m.setIdle(),c()}};return(0,o.tZ)(x.Y,{headerTitle:t,headerSubtitle:n,children:(0,o.BX)(w.l.Root,{onSubmit:b,sx:e=>({display:"flex",flexDirection:"column",rowGap:e.space.$3}),children:[e,(0,o.tZ)(a.PaymentElement,{onReady:()=>d(!0),options:{layout:{type:"tabs",defaultCollapsed:!1},paymentMethodOrder:s,applePay:i?{recurringPaymentRequest:{paymentDescription:`${g((0,R.localizationKeys)("month"===i.planPeriod?"commerce.paymentSource.applePayDescription.monthly":"commerce.paymentSource.applePayDescription.annual"))}`,managementURL:y.homeUrl,regularBilling:{amount:i.totals.totalDueNow?.amount||i.totals.grandTotal.amount,label:i.plan.name,recurringPaymentIntervalUnit:"annual"===i.planPeriod?"year":"month"}}}:void 0}}),(0,o.tZ)(E.Z.Alert,{children:m.error}),(0,o.tZ)(P.A,{isDisabled:!p,submitLabel:r??(0,R.localizationKeys)(`${v}.billingPage.paymentSourcesSection.formButtonPrimary__add`),onReset:l,hideReset:!l,sx:{flex:i?1:void 0}})]})})}},7106:function(e,t,n){"use strict";n.d(t,{j:()=>a});var r=n(9109),o=n(9541),i=n(4174);let a=({paymentSource:e})=>(0,r.BX)(o.Flex,{sx:{overflow:"hidden"},gap:2,align:"baseline",elementDescriptor:o.descriptors.paymentSourceRow,children:[(0,r.tZ)(o.Icon,{icon:"card"===e.paymentMethod?i.aB:i.Nn,sx:{alignSelf:"center"},elementDescriptor:o.descriptors.paymentSourceRowIcon}),(0,r.tZ)(o.Text,{sx:e=>({color:e.colors.$colorText,textTransform:"capitalize"}),truncate:!0,elementDescriptor:o.descriptors.paymentSourceRowType,children:"card"===e.paymentMethod?e.cardType:e.paymentMethod}),(0,r.tZ)(o.Text,{sx:e=>({color:e.colors.$colorTextSecondary}),variant:"caption",truncate:!0,elementDescriptor:o.descriptors.paymentSourceRowValue,children:"card"===e.paymentMethod?`⋯ ${e.last4}`:null}),e.isDefault&&(0,r.tZ)(o.Badge,{elementDescriptor:o.descriptors.paymentSourceRowBadge,elementId:o.descriptors.paymentSourceRowBadge.setId("default"),localizationKey:(0,o.localizationKeys)("badge__default")}),"expired"===e.status&&(0,r.tZ)(o.Badge,{elementDescriptor:o.descriptors.paymentSourceRowBadge,elementId:o.descriptors.paymentSourceRowBadge.setId("expired"),colorScheme:"danger",localizationKey:(0,o.localizationKeys)("badge__expired")})]})},6054:function(e,t,n){"use strict";n.d(t,{Hw:()=>k});var r=n(8057),o=n(9109),i=n(3799),a=n(9144),c=n(2672),u=n(8104),l=n(9655),s=n(3009),p=n(8969),d=n(2189),f=n(1576),m=n(9541),h=n(9805),y=n(4709),g=n(7623),v=n(7106),S=n(8e3);let b=()=>{let{t:e}=(0,m.useLocalizations)();return(0,o.BX)(m.Box,{sx:e=>({background:e.colors.$neutralAlpha50,padding:e.space.$2x5,borderRadius:e.radii.$md,borderWidth:e.borderWidths.$normal,borderStyle:e.borderStyles.$solid,borderColor:e.colors.$neutralAlpha100,display:"flex",flexDirection:"column",rowGap:e.space.$2,position:"relative"}),children:[(0,o.tZ)(m.Box,{sx:e=>({position:"absolute",inset:0,background:`repeating-linear-gradient(-45deg,${e.colors.$warningAlpha100},${e.colors.$warningAlpha100} 6px,${e.colors.$warningAlpha150} 6px,${e.colors.$warningAlpha150} 12px)`,maskImage:"linear-gradient(transparent 20%, black)",pointerEvents:"none"})}),(0,o.BX)(m.Box,{sx:{display:"flex",alignItems:"baseline",justifyContent:"space-between"},children:[(0,o.tZ)(m.Text,{variant:"caption",colorScheme:"body",localizationKey:(0,m.localizationKeys)("commerce.paymentSource.dev.testCardInfo")}),(0,o.tZ)(m.Text,{variant:"caption",sx:e=>({color:e.colors.$warning500,fontWeight:e.fontWeights.$semibold}),localizationKey:(0,m.localizationKeys)("commerce.paymentSource.dev.developmentMode")})]}),(0,o.BX)(S.a.Root,{children:[(0,o.BX)(S.a.Group,{variant:"tertiary",children:[(0,o.tZ)(S.a.Title,{title:(0,m.localizationKeys)("commerce.paymentSource.dev.cardNumber")}),(0,o.tZ)(S.a.Description,{text:"4242 4242 4242 4242"})]}),(0,o.BX)(S.a.Group,{variant:"tertiary",children:[(0,o.tZ)(S.a.Title,{title:(0,m.localizationKeys)("commerce.paymentSource.dev.expirationDate")}),(0,o.tZ)(S.a.Description,{text:"11/44"})]}),(0,o.BX)(S.a.Group,{variant:"tertiary",children:[(0,o.tZ)(S.a.Title,{title:(0,m.localizationKeys)("commerce.paymentSource.dev.cvcZip")}),(0,o.tZ)(S.a.Description,{text:e((0,m.localizationKeys)("commerce.paymentSource.dev.anyNumbers"))})]})]})]})},C=(0,c.withCardStateProvider)(({onSuccess:e})=>{let{close:t}=(0,y.XC)(),n=(0,i.cL)(),a=(0,f.useSubscriberTypeContext)(),c=(0,f.useSubscriberTypeLocalizationRoot)(),u=async r=>{let o="org"===a?n?.organization:n.user;return await o?.addPaymentSource({gateway:"stripe",paymentToken:r.stripeSetupIntent?.payment_method}),e(),t(),Promise.resolve()};return(0,o.BX)(r.fC,{onSuccess:u,cancelAction:t,children:[(0,o.tZ)(r.ph,{text:(0,m.localizationKeys)(`${c}.billingPage.paymentSourcesSection.add`)}),(0,o.tZ)(r.hG,{text:(0,m.localizationKeys)(`${c}.billingPage.paymentSourcesSection.addSubtitle`)}),(0,o.tZ)(d.P,{children:(0,o.tZ)(b,{})})]})}),E=({paymentSource:e,revalidate:t})=>{let{close:n}=(0,y.XC)(),r=(0,c.useCardState)(),u=(0,f.useSubscriberTypeContext)(),{organization:l}=(0,i.o8)(),s=(0,f.useSubscriberTypeLocalizationRoot)(),d=(0,a.useRef)(`${"card"===e.paymentMethod?e.cardType:e.paymentMethod} ${"card"===e.paymentMethod?`⋯ ${e.last4}`:"-"}`);if(!d.current)return null;let h=async()=>{await e.remove({orgId:"org"===u?l?.id:void 0}).then(t).catch(e=>{(0,g.S3)(e,[],r.setError)})};return(0,o.tZ)(p.LE,{title:(0,m.localizationKeys)(`${s}.billingPage.paymentSourcesSection.removeResource.title`),messageLine1:(0,m.localizationKeys)(`${s}.billingPage.paymentSourcesSection.removeResource.messageLine1`,{identifier:d.current}),messageLine2:(0,m.localizationKeys)(`${s}.billingPage.paymentSourcesSection.removeResource.messageLine2`),successMessage:(0,m.localizationKeys)(`${s}.billingPage.paymentSourcesSection.removeResource.successMessage`,{paymentSource:d.current}),deleteResource:h,onSuccess:n,onReset:n})},k=(0,c.withCardStateProvider)(()=>{let e=(0,i.cL)(),t=(0,f.useSubscriberTypeContext)(),n=(0,f.useSubscriberTypeLocalizationRoot)(),r="org"===t?e?.organization:e.user,{data:c,isLoading:s,mutate:p}=(0,f.usePaymentSources)(),{data:d=[]}=c||{},y=(0,a.useMemo)(()=>d.sort((e,t)=>e.isDefault&&!t.isDefault?-1:1),[d]),g=(0,a.useCallback)(()=>void p(),[p]);return r?(0,o.tZ)(l.zd.Root,{title:(0,m.localizationKeys)(`${n}.billingPage.paymentSourcesSection.title`),centered:!1,id:"paymentSources",sx:e=>({flex:1,borderTopWidth:e.borderWidths.$normal,borderTopStyle:e.borderStyles.$solid,borderTopColor:e.colors.$neutralAlpha100}),children:(0,o.tZ)(h.a.Root,{children:(0,o.tZ)(l.zd.ItemList,{id:"paymentSources",disableAnimation:!0,children:s?(0,o.tZ)(u.m,{}):(0,o.BX)(o.HY,{children:[y.map(e=>(0,o.BX)(a.Fragment,{children:[(0,o.BX)(l.zd.Item,{id:"paymentSources",children:[(0,o.tZ)(v.j,{paymentSource:e}),(0,o.tZ)(w,{paymentSource:e,revalidate:g})]}),(0,o.tZ)(h.a.Open,{value:`remove-${e.id}`,children:(0,o.tZ)(h.a.Card,{variant:"destructive",children:(0,o.tZ)(E,{paymentSource:e,revalidate:g})})})]},e.id)),(0,o.tZ)(h.a.Trigger,{value:"add",children:(0,o.tZ)(l.zd.ArrowButton,{id:"paymentSources",localizationKey:(0,m.localizationKeys)(`${n}.billingPage.paymentSourcesSection.add`)})}),(0,o.tZ)(h.a.Open,{value:"add",children:(0,o.tZ)(h.a.Card,{children:(0,o.tZ)(C,{onSuccess:g})})})]})})})}):null}),w=({paymentSource:e,revalidate:t})=>{let{open:n}=(0,y.XC)(),r=(0,c.useCardState)(),{organization:a}=(0,i.o8)(),u=(0,f.useSubscriberTypeContext)(),l=(0,f.useSubscriberTypeLocalizationRoot)(),p=[{label:(0,m.localizationKeys)(`${l}.billingPage.paymentSourcesSection.actionLabel__remove`),isDestructive:!0,onClick:()=>n(`remove-${e.id}`),isDisabled:!e.isRemovable}];return e.isDefault||p.unshift({label:(0,m.localizationKeys)(`${l}.billingPage.paymentSourcesSection.actionLabel__default`),isDestructive:!1,onClick:()=>{e.makeDefault({orgId:"org"===u?a?.id:void 0}).then(t).catch(e=>{(0,g.S3)(e,[],r.setError)})},isDisabled:!1}),(0,o.tZ)(s.a,{actions:p})}},9386:function(e,t,n){!function(e,t){"use strict";function n(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){i(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n,r,o=e&&("undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null!=o){var i=[],a=!0,c=!1;try{for(o=o.call(e);!(a=(n=o.next()).done)&&(i.push(n.value),!t||i.length!==t);a=!0);}catch(e){c=!0,r=e}finally{try{a||null==o.return||o.return()}finally{if(c)throw r}}return i}}(e,t)||function(e,t){if(e){if("string"==typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return c(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var u,l,s,p,d,f={exports:{}};f.exports=(function(){if(d)return p;d=1;var e=s?l:(s=1,l="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED");function t(){}function n(){}return n.resetWarningCache=t,p=function(){function r(t,n,r,o,i,a){if(a!==e){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function o(){return r}r.isRequired=r;var i={array:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:o,element:r,elementType:r,instanceOf:o,node:r,objectOf:o,oneOf:o,oneOfType:o,shape:o,exact:o,checkPropTypes:n,resetWarningCache:t};return i.PropTypes=i,i}})()();var m=(u=f.exports)&&u.__esModule&&Object.prototype.hasOwnProperty.call(u,"default")?u.default:u,h=function(e,n,r){var o=!!r,i=t.useRef(r);t.useEffect(function(){i.current=r},[r]),t.useEffect(function(){if(!o||!e)return function(){};var t=function(){i.current&&i.current.apply(i,arguments)};return e.on(n,t),function(){e.off(n,t)}},[o,n,e,i])},y=function(e){var n=t.useRef(e);return t.useEffect(function(){n.current=e},[e]),n.current},g=function(e){return null!==e&&"object"===o(e)},v="[object Object]",S=function e(t,n){if(!g(t)||!g(n))return t===n;var r=Array.isArray(t);if(r!==Array.isArray(n))return!1;var o=Object.prototype.toString.call(t)===v;if(o!==(Object.prototype.toString.call(n)===v))return!1;if(!o&&!r)return t===n;var i=Object.keys(t),a=Object.keys(n);if(i.length!==a.length)return!1;for(var c={},u=0;u<i.length;u+=1)c[i[u]]=!0;for(var l=0;l<a.length;l+=1)c[a[l]]=!0;var s=Object.keys(c);return s.length===i.length&&s.every(function(r){return e(t[r],n[r])})},b=function(e,t,n){return g(e)?Object.keys(e).reduce(function(o,a){var c=!g(t)||!S(e[a],t[a]);return n.includes(a)?(c&&console.warn("Unsupported prop change: options.".concat(a," is not a mutable property.")),o):c?r(r({},o||{}),{},i({},a,e[a])):o},null):null},C="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",E=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:C;if(null===e||g(e)&&"function"==typeof e.elements&&"function"==typeof e.createToken&&"function"==typeof e.createPaymentMethod&&"function"==typeof e.confirmCardPayment)return e;throw Error(t)},k=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:C;if(g(e)&&"function"==typeof e.then)return{tag:"async",stripePromise:Promise.resolve(e).then(function(e){return E(e,t)})};var n=E(e,t);return null===n?{tag:"empty"}:{tag:"sync",stripe:n}},w=function(e){e&&e._registerWrapper&&e.registerAppInfo&&(e._registerWrapper({name:"react-stripe-js",version:"3.1.0"}),e.registerAppInfo({name:"react-stripe-js",version:"3.1.0",url:"https://stripe.com/docs/stripe-js/react"}))},P=t.createContext(null);P.displayName="ElementsContext";var x=function(e,t){if(!e)throw Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},$=function(e){var n=e.stripe,r=e.options,o=e.children,i=t.useMemo(function(){return k(n)},[n]),c=a(t.useState(function(){return{stripe:"sync"===i.tag?i.stripe:null,elements:"sync"===i.tag?i.stripe.elements(r):null}}),2),u=c[0],l=c[1];t.useEffect(function(){var e=!0,t=function(e){l(function(t){return t.stripe?t:{stripe:e,elements:e.elements(r)}})};return"async"!==i.tag||u.stripe?"sync"!==i.tag||u.stripe||t(i.stripe):i.stripePromise.then(function(n){n&&e&&t(n)}),function(){e=!1}},[i,u,r]);var s=y(n);t.useEffect(function(){null!==s&&s!==n&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[s,n]);var p=y(r);return t.useEffect(function(){if(u.elements){var e=b(r,p,["clientSecret","fonts"]);e&&u.elements.update(e)}},[r,p,u.elements]),t.useEffect(function(){w(u.stripe)},[u.stripe]),t.createElement(P.Provider,{value:u},o)};$.propTypes={stripe:m.any,options:m.object};var R=function(e){return x(t.useContext(P),e)},j=function(e){return(0,e.children)(R("mounts <ElementsConsumer>"))};j.propTypes={children:m.func.isRequired};var T=["on","session"],O=t.createContext(null);O.displayName="CheckoutSdkContext";var Z=function(e,t){if(!e)throw Error("Could not find CheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CheckoutProvider> provider."));return e},z=t.createContext(null);z.displayName="CheckoutContext";var A=function(e,t){if(!e)return null;e.on,e.session;var n=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,T);return t?r(r({},n),t):r(r({},n),e.session())},B=function(e){var n=e.stripe,r=e.options,o=e.children,i=t.useMemo(function(){return k(n,"Invalid prop `stripe` supplied to `CheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")},[n]),c=a(t.useState(null),2),u=c[0],l=c[1],s=a(t.useState(function(){return{stripe:"sync"===i.tag?i.stripe:null,checkoutSdk:null}}),2),p=s[0],d=s[1],f=function(e,t){d(function(n){return n.stripe&&n.checkoutSdk?n:{stripe:e,checkoutSdk:t}})},m=t.useRef(!1);t.useEffect(function(){var e=!0;return"async"!==i.tag||p.stripe?"sync"===i.tag&&i.stripe&&!m.current&&(m.current=!0,i.stripe.initCheckout(r).then(function(e){e&&(f(i.stripe,e),e.on("change",l))})):i.stripePromise.then(function(t){t&&e&&!m.current&&(m.current=!0,t.initCheckout(r).then(function(e){e&&(f(t,e),e.on("change",l))}))}),function(){e=!1}},[i,p,r,l]);var h=y(n);t.useEffect(function(){null!==h&&h!==n&&console.warn("Unsupported prop change on CheckoutProvider: You cannot change the `stripe` prop after setting it.")},[h,n]);var v=y(r);t.useEffect(function(){if(p.checkoutSdk){!r.clientSecret||g(v)||S(r.clientSecret,v.clientSecret)||console.warn("Unsupported prop change: options.clientSecret is not a mutable property.");var e,t,n=null==v?void 0:null===(e=v.elementsOptions)||void 0===e?void 0:e.appearance,o=null==r?void 0:null===(t=r.elementsOptions)||void 0===t?void 0:t.appearance;o&&!S(o,n)&&p.checkoutSdk.changeAppearance(o)}},[r,v,p.checkoutSdk]),t.useEffect(function(){w(p.stripe)},[p.stripe]);var b=t.useMemo(function(){return A(p.checkoutSdk,u)},[p.checkoutSdk,u]);return p.checkoutSdk?t.createElement(O.Provider,{value:p},t.createElement(z.Provider,{value:b},o)):null};B.propTypes={stripe:m.any,options:m.shape({clientSecret:m.string.isRequired,elementsOptions:m.object}).isRequired};var I=function(e){var n=t.useContext(O),r=t.useContext(P);if(n&&r)throw Error("You cannot wrap the part of your app that ".concat(e," in both <CheckoutProvider> and <Elements> providers."));return n?Z(n,e):x(r,e)},D=function(e,n){var r="".concat(e.charAt(0).toUpperCase()+e.slice(1),"Element"),o=n?function(e){I("mounts <".concat(r,">"));var n=e.id,o=e.className;return t.createElement("div",{id:n,className:o})}:function(n){var o,i=n.id,c=n.className,u=n.options,l=void 0===u?{}:u,s=n.onBlur,p=n.onFocus,d=n.onReady,f=n.onChange,m=n.onEscape,g=n.onClick,v=n.onLoadError,S=n.onLoaderStart,C=n.onNetworksChange,E=n.onConfirm,k=n.onCancel,w=n.onShippingAddressChange,P=n.onShippingRateChange,x=I("mounts <".concat(r,">")),$="elements"in x?x.elements:null,R="checkoutSdk"in x?x.checkoutSdk:null,j=a(t.useState(null),2),T=j[0],O=j[1],Z=t.useRef(null),z=t.useRef(null);h(T,"blur",s),h(T,"focus",p),h(T,"escape",m),h(T,"click",g),h(T,"loaderror",v),h(T,"loaderstart",S),h(T,"networkschange",C),h(T,"confirm",E),h(T,"cancel",k),h(T,"shippingaddresschange",w),h(T,"shippingratechange",P),h(T,"change",f),d&&(o="expressCheckout"===e?d:function(){d(T)}),h(T,"ready",o),t.useLayoutEffect(function(){if(null===Z.current&&null!==z.current&&($||R)){var t=null;R?t=R.createElement(e,l):$&&(t=$.create(e,l)),Z.current=t,O(t),t&&t.mount(z.current)}},[$,R,l]);var A=y(l);return t.useEffect(function(){if(Z.current){var e=b(l,A,["paymentRequest"]);e&&"update"in Z.current&&Z.current.update(e)}},[l,A]),t.useLayoutEffect(function(){return function(){if(Z.current&&"function"==typeof Z.current.destroy)try{Z.current.destroy(),Z.current=null}catch(e){}}},[]),t.createElement("div",{id:i,className:c,ref:z})};return o.propTypes={id:m.string,className:m.string,onChange:m.func,onBlur:m.func,onFocus:m.func,onReady:m.func,onEscape:m.func,onClick:m.func,onLoadError:m.func,onLoaderStart:m.func,onNetworksChange:m.func,onConfirm:m.func,onCancel:m.func,onShippingAddressChange:m.func,onShippingRateChange:m.func,options:m.object},o.displayName=r,o.__elementType=e,o},L="undefined"==typeof window,_=t.createContext(null);_.displayName="EmbeddedCheckoutProviderContext";var M=function(){var e=t.useContext(_);if(!e)throw Error("<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>");return e},K=L?function(e){var n=e.id,r=e.className;return M(),t.createElement("div",{id:n,className:r})}:function(e){var n=e.id,r=e.className,o=M().embeddedCheckout,i=t.useRef(!1),a=t.useRef(null);return t.useLayoutEffect(function(){return!i.current&&o&&null!==a.current&&(o.mount(a.current),i.current=!0),function(){if(i.current&&o)try{o.unmount(),i.current=!1}catch(e){}}},[o]),t.createElement("div",{ref:a,id:n,className:r})},N=D("auBankAccount",L),Y=D("card",L),U=D("cardNumber",L),W=D("cardExpiry",L),X=D("cardCvc",L),F=D("fpxBank",L),q=D("iban",L),V=D("idealBank",L),G=D("p24Bank",L),H=D("epsBank",L),J=D("payment",L),Q=D("expressCheckout",L),ee=D("currencySelector",L),et=D("paymentRequestButton",L),en=D("linkAuthentication",L),er=D("address",L),eo=D("shippingAddress",L),ei=D("paymentMethodMessaging",L),ea=D("affirmMessage",L),ec=D("afterpayClearpayMessage",L);e.AddressElement=er,e.AffirmMessageElement=ea,e.AfterpayClearpayMessageElement=ec,e.AuBankAccountElement=N,e.CardCvcElement=X,e.CardElement=Y,e.CardExpiryElement=W,e.CardNumberElement=U,e.CheckoutProvider=B,e.CurrencySelectorElement=ee,e.Elements=$,e.ElementsConsumer=j,e.EmbeddedCheckout=K,e.EmbeddedCheckoutProvider=function(e){var n=e.stripe,r=e.options,o=e.children,i=t.useMemo(function(){return k(n,"Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.")},[n]),c=t.useRef(null),u=t.useRef(null),l=a(t.useState({embeddedCheckout:null}),2),s=l[0],p=l[1];t.useEffect(function(){if(!u.current&&!c.current){var e=function(e){u.current||c.current||(u.current=e,c.current=u.current.initEmbeddedCheckout(r).then(function(e){p({embeddedCheckout:e})}))};"async"===i.tag&&!u.current&&(r.clientSecret||r.fetchClientSecret)?i.stripePromise.then(function(t){t&&e(t)}):"sync"===i.tag&&!u.current&&(r.clientSecret||r.fetchClientSecret)&&e(i.stripe)}},[i,r,s,u]),t.useEffect(function(){return function(){s.embeddedCheckout?(c.current=null,s.embeddedCheckout.destroy()):c.current&&c.current.then(function(){c.current=null,s.embeddedCheckout&&s.embeddedCheckout.destroy()})}},[s.embeddedCheckout]),t.useEffect(function(){w(u)},[u]);var d=y(n);t.useEffect(function(){null!==d&&d!==n&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.")},[d,n]);var f=y(r);return t.useEffect(function(){if(null!=f){if(null==r){console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them.");return}void 0===r.clientSecret&&void 0===r.fetchClientSecret&&console.warn("Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`."),null!=f.clientSecret&&r.clientSecret!==f.clientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=f.fetchClientSecret&&r.fetchClientSecret!==f.fetchClientSecret&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead."),null!=f.onComplete&&r.onComplete!==f.onComplete&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it."),null!=f.onShippingDetailsChange&&r.onShippingDetailsChange!==f.onShippingDetailsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it."),null!=f.onLineItemsChange&&r.onLineItemsChange!==f.onLineItemsChange&&console.warn("Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.")}},[f,r]),t.createElement(_.Provider,{value:s},o)},e.EpsBankElement=H,e.ExpressCheckoutElement=Q,e.FpxBankElement=F,e.IbanElement=q,e.IdealBankElement=V,e.LinkAuthenticationElement=en,e.P24BankElement=G,e.PaymentElement=J,e.PaymentMethodMessagingElement=ei,e.PaymentRequestButtonElement=et,e.ShippingAddressElement=eo,e.useCheckout=function(){e="calls useCheckout()",Z(t.useContext(O),e);var e,n=t.useContext(z);if(!n)throw Error("Could not find Checkout Context; You need to wrap the part of your app that calls useCheckout() in an <CheckoutProvider> provider.");return n},e.useElements=function(){return R("calls useElements()").elements},e.useStripe=function(){return I("calls useStripe()").stripe}}(t,n(9144))}}]);