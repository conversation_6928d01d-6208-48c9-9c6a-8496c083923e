import type { SignInFactor } from '@clerk/types';
import React from 'react';
import type { LocalizationKey } from '../../customizables';
type AlternativeMethodsMode = 'forgot' | 'pwned' | 'default';
export type AlternativeMethodsProps = {
    onBackLinkClick: React.MouseEventHandler | undefined;
    onFactorSelected: (factor: SignInFactor) => void;
    currentFactor: SignInFactor | undefined | null;
    mode?: AlternativeMethodsMode;
};
export type AlternativeMethodListProps = AlternativeMethodsProps & {
    onHavingTroubleClick: React.MouseEventHandler;
};
export declare const AlternativeMethods: (props: AlternativeMethodsProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export declare function getButtonLabel(factor: SignInFactor): LocalizationKey;
export declare function getButtonIcon(factor: SignInFactor): React.FC<React.SVGAttributes<SVGElement>>;
export {};
