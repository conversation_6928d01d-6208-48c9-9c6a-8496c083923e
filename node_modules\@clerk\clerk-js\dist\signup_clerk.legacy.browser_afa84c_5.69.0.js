"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["710"],{95518:function(e,t,i){i.d(t,{Vh:()=>m,Vs:()=>g,bx:()=>p,mQ:()=>b,s2:()=>f,t3:()=>d,xT:()=>u}),i(28419),i(56113),i(65027);var l=i(32208),r=i(7772),n=i(77623),a=i(90577);let o=e=>t=>"safeIdentifier"in t&&t.safeIdentifier===e;function s(e){if((0,l.iW)()){let t=e.find(e=>{let{strategy:t}=e;return"passkey"===t});if(t)return t}return null}function d(e,t,i){return e&&0!==e.length?i===r.kJ.Password?function(e,t){let i=s(e);if(i)return i;let l=e.sort(a.sZ)[0];return"password"===l.strategy?l:e.find(o(t))||l||null}(e,t):function(e,t){let i=s(e);if(i)return i;let l=e.sort(a.b8),r=l.find(o(t));if(r)return r;let n=l[0];return"email_link"===n.strategy?n:e.find(o(t))||n||null}(e,t):null}let c=["passkey","email_code","password","phone_code","email_link"];function u(e){return!!e&&c.includes(e.strategy)}function p(e){if(!e||0===e.length)return null;let t=e.find(e=>"totp"===e.strategy);if(t)return t;let i=e.find(e=>"phone_code"===e.strategy);return i||e[0]}let h=["reset_password_phone_code","reset_password_email_code"],m=e=>!!e&&h.includes(e),v=e=>/^\S+@\S+\.\S+$/.test(e);function f(e){return"tel"===e.type?"phoneNumber":v(e.value)?"emailAddress":"username"}let g=(e,t,i)=>{var l,r;if(!t)return null;let a=null===(l=e.find(e=>"strategy"===e.id))||void 0===l?void 0:l.value;if(a&&"phone_code"!==a)return null;let o=null===(r=e.find(e=>e.id===i))||void 0===r?void 0:r.value;if(!o||!(null==o?void 0:o.startsWith("+")))return null;let s=(0,n.jR)(o,t);return"sms"===s?null:s},b=(e,t,i)=>{if(!e||!t||"phoneNumber"!==t||!i||!(null==i?void 0:i.startsWith("+")))return null;let l=(0,n.jR)(i,e);return"sms"===l?null:l}},85049:function(e,t,i){i.r(t),i.d(t,{SignUp:()=>et,SignUpSSOCallback:()=>k,SignUpContinue:()=>z,SignUpStart:()=>D,SignUpModal:()=>ei,SignUpVerifyEmail:()=>G,SignUpVerifyPhone:()=>J});var l=i(79109),r=i(83799),n=i(69144),a=i(7508),o=i(15112),s=i(11576),d=i(39541),c=i(4511),u=i(24676);i(65223),i(93008),i(79876),i(28419),i(38062),i(50725);var p=i(44455),h=i(2672),m=i(92654),v=i(21455),f=i(18350),g=i(77623),b=i(70431),_=i(68314),y=i(37321),A=i(81201);let U=e=>{var t,i,r,n,a,o,s,c,u,p,h,m,v,f;let{handleSubmit:g,fields:U,formState:S,canToggleEmailPhone:C,onlyLegalAcceptedMissing:Z=!1,handleEmailPhoneToggle:N}=e,{showOptionalFields:P}=(0,d.useAppearance)().parsedLayout,E=e=>{var t;return("emailAddress"===e||"phoneNumber"===e)&&C?!!U[e]:!!U[e]&&(P||(null===(t=U[e])||void 0===t?void 0:t.required))};return(0,l.BX)(b.l.Root,{onSubmit:g,gap:8,children:[!Z&&(0,l.BX)(d.Col,{gap:6,children:[(E("firstName")||E("lastName"))&&(0,l.BX)(b.l.ControlRow,{elementId:"name",sx:{[A.mqu.sm]:{flexWrap:"wrap"}},children:[E("firstName")&&(0,l.tZ)(b.l.PlainInput,{...S.firstName.props,isRequired:null===(t=U.firstName)||void 0===t?void 0:t.required,isOptional:!(null===(i=U.firstName)||void 0===i?void 0:i.required)}),E("lastName")&&(0,l.tZ)(b.l.PlainInput,{...S.lastName.props,isRequired:null===(r=U.lastName)||void 0===r?void 0:r.required,isOptional:!(null===(n=U.lastName)||void 0===n?void 0:n.required)})]}),E("username")&&(0,l.tZ)(b.l.ControlRow,{elementId:"username",children:(0,l.tZ)(b.l.PlainInput,{...S.username.props,isRequired:null===(a=U.username)||void 0===a?void 0:a.required,isOptional:!(null===(o=U.username)||void 0===o?void 0:o.required)})}),E("emailAddress")&&(0,l.tZ)(b.l.ControlRow,{elementId:"emailAddress",children:(0,l.tZ)(b.l.PlainInput,{...S.emailAddress.props,isRequired:null===(s=U.emailAddress)||void 0===s?void 0:s.required,isOptional:!(null===(c=U.emailAddress)||void 0===c?void 0:c.required),isDisabled:null===(u=U.emailAddress)||void 0===u?void 0:u.disabled,actionLabel:C?(0,d.localizationKeys)("signUp.start.actionLink__use_phone"):void 0,onActionClicked:C?()=>N("phoneNumber"):void 0})}),E("phoneNumber")&&(0,l.tZ)(b.l.ControlRow,{elementId:"phoneNumber",children:(0,l.tZ)(b.l.PhoneInput,{...S.phoneNumber.props,isRequired:null===(p=U.phoneNumber)||void 0===p?void 0:p.required,isOptional:!(null===(h=U.phoneNumber)||void 0===h?void 0:h.required),actionLabel:C?(0,d.localizationKeys)("signUp.start.actionLink__use_email"):void 0,onActionClicked:C?()=>N("emailAddress"):void 0})}),E("password")&&(0,l.tZ)(b.l.ControlRow,{elementId:"password",children:(0,l.tZ)(b.l.PasswordInput,{...S.password.props,isRequired:null===(m=U.password)||void 0===m?void 0:m.required,isOptional:!(null===(v=U.password)||void 0===v?void 0:v.required)})})]}),(0,l.BX)(d.Col,{center:!0,children:[(0,l.tZ)(y.S,{}),(0,l.BX)(d.Col,{gap:6,sx:{width:"100%"},children:[E("legalAccepted")&&(0,l.tZ)(b.l.ControlRow,{elementId:"legalAccepted",children:(0,l.tZ)(_.H,{...S.legalAccepted.props,isRequired:null===(f=U.legalAccepted)||void 0===f?void 0:f.required})}),(0,l.tZ)(b.l.SubmitButton,{hasArrow:!0,localizationKey:(0,d.localizationKeys)("formButtonPrimary")})]})]})]})};var S=i(65027);let C=["emailAddress","phoneNumber","username","firstName","lastName","password","ticket","legalAccepted"];function Z(e){return C.reduce((t,i)=>{let l=function(e,t){switch(e){case"emailAddress":return function(e){var t,i,l,r;let{attributes:n,hasTicket:a,hasEmail:o,activeCommIdentifierType:s,isProgressiveSignUp:d}=e;if(d){if(!((!a||a&&o)&&(null===(l=n.email_address)||void 0===l?void 0:l.enabled))||P(n,d)&&"emailAddress"!==s)return;return{required:!!(null===(r=n.email_address)||void 0===r?void 0:r.required),disabled:!!a&&!!o}}if((!a||a&&o)&&(null===(t=n.email_address)||void 0===t?void 0:t.enabled)&&(null===(i=n.email_address)||void 0===i?void 0:i.used_for_first_factor)&&"emailAddress"===s)return{required:!0,disabled:!!a&&!!o}}(t);case"phoneNumber":return function(e){var t,i,l;let{attributes:r,hasTicket:n,activeCommIdentifierType:a,isProgressiveSignUp:o}=e;if(o){if(!(null===(i=r.phone_number)||void 0===i?void 0:i.enabled)||P(r,o)&&"phoneNumber"!==a)return;return{required:!!(null===(l=r.phone_number)||void 0===l?void 0:l.required)}}if(!n&&(null===(t=r.phone_number)||void 0===t?void 0:t.enabled)&&r.phone_number.used_for_first_factor&&"phoneNumber"===a)return{required:!0}}(t);case"password":return function(e){var t,i;if((null===(t=e.password)||void 0===t?void 0:t.enabled)&&e.password.required)return{required:!!(null===(i=e.password)||void 0===i?void 0:i.required)}}(t.attributes);case"ticket":return function(e){if(e)return{required:!0}}(t.hasTicket);case"legalAccepted":return function(e){if(e)return{required:!0}}(t.legalConsentRequired);case"username":case"firstName":case"lastName":return function(e,t){var i,l;let r=(0,S.a1)(e);if(null===(i=t[r])||void 0===i?void 0:i.enabled)return{required:null===(l=t[r])||void 0===l?void 0:l.required}}(e,t.attributes);default:return}}(i,e);return l&&(t[i]=l),t},{})}let N=(e,t,i)=>{if(null==i?void 0:i.emailAddress)return"emailAddress";if(null==i?void 0:i.phoneNumber)return"phoneNumber";if(P(e,t))return"emailAddress";let{email_address:l,phone_number:r}=e;return((null==l?void 0:l.enabled)&&t?l.required:null==l?void 0:l.used_for_first_factor)?"emailAddress":((null==r?void 0:r.enabled)&&t?r.required:null==r?void 0:r.used_for_first_factor)?"phoneNumber":null};function P(e,t){let{email_address:i,phone_number:l}=e;return!!(t?(null==i?void 0:i.enabled)&&(null==l?void 0:l.enabled)&&!i.required&&!l.required:(null==i?void 0:i.used_for_first_factor)&&(null==l?void 0:l.used_for_first_factor))}var E=i(80138);let w=n.memo(e=>{let t=(0,r.cL)(),{navigate:i}=(0,u.useRouter)(),n=(0,h.useCardState)(),a=(0,s.useSignUpContext)(),o=(0,s.useCoreSignUp)(),d=a.ssoCallbackUrl,c=a.afterSignUpUrl||"/",p="popup"===a.oauthFlow||"auto"===a.oauthFlow&&(0,g.tc)(),{continueSignUp:m=!1,onAlternativePhoneCodeProviderClick:v,...f}=e;return(0,l.tZ)(E.L,{...f,idleAfterDelay:!p,oauthCallback:t=>{if(p){let i=window.open("about:blank","","width=600,height=800"),l=setInterval(()=>{(!i||i.closed)&&(clearInterval(l),n.setIdle())},500);return o.authenticateWithPopup({strategy:t,redirectUrl:d,redirectUrlComplete:c,popup:i,continueSignUp:m,unsafeMetadata:a.unsafeMetadata,legalAccepted:e.legalAccepted,oidcPrompt:a.oidcPrompt}).catch(e=>(0,g.S3)(e,[],n.setError))}return o.authenticateWithRedirect({continueSignUp:m,redirectUrl:d,redirectUrlComplete:c,strategy:t,unsafeMetadata:a.unsafeMetadata,legalAccepted:e.legalAccepted,oidcPrompt:a.oidcPrompt}).catch(e=>(0,g.S3)(e,[],n.setError))},web3Callback:l=>t.authenticateWithWeb3({customNavigate:i,redirectUrl:c,signUpContinueUrl:"continue",unsafeMetadata:a.unsafeMetadata,strategy:l,legalAccepted:e.legalAccepted}).catch(e=>(0,g.Ht)(e,n.setError)),alternativePhoneCodeCallback:e=>{null==v||v(e)}})});var I=i(46052);let z=(0,h.withCardStateProvider)(function(){var e,t;let i=(0,h.useCardState)(),a=(0,r.cL)(),{navigate:o}=(0,u.useRouter)(),{displayConfig:c,userSettings:b}=(0,s.useEnvironment)(),{attributes:_,usernameSettings:y}=b,{t:A,locale:S}=(0,d.useLocalizations)(),{afterSignUpUrl:C,signInUrl:E,unsafeMetadata:z,initialValues:K={},isCombinedFlow:k}=(0,s.useSignUpContext)(),R=(0,s.useCoreSignUp)(),L=!!n.useContext(s.SignInContext),F=!!(k&&L),T=b.signUp.progressive,[O,B]=n.useState(N(_,b.signUp.progressive)),q=(0,s.useSignUpContext)(),x={firstName:(0,g.Yp)("firstName",K.firstName||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__firstName"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__firstName")}),lastName:(0,g.Yp)("lastName",K.lastName||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__lastName"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__lastName")}),emailAddress:(0,g.Yp)("emailAddress",K.emailAddress||R.emailAddress||"",{type:"email",label:(0,d.localizationKeys)("formFieldLabel__emailAddress"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__emailAddress")}),username:(0,g.Yp)("username",K.username||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__username"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__username"),transformer:e=>e.trim(),buildErrorMessage:e=>(0,g.fq)(e,{t:A,locale:S,usernameSettings:y})}),phoneNumber:(0,g.Yp)("phoneNumber",K.phoneNumber||"",{type:"tel",label:(0,d.localizationKeys)("formFieldLabel__phoneNumber"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__phoneNumber")}),password:(0,g.Yp)("password","",{type:"password",label:(0,d.localizationKeys)("formFieldLabel__password"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__password"),validatePassword:!0}),legalAccepted:(0,g.Yp)("legalAccepted","",{type:"checkbox",label:"",defaultChecked:!1,isRequired:b.signUp.legal_consent_enabled||!1})},M=(0,n.useMemo)(()=>R.missingFields&&1===R.missingFields.length&&"legal_accepted"===R.missingFields[0]&&R.unverifiedFields&&0===R.unverifiedFields.length,[R.missingFields,R.unverifiedFields]);if((0,n.useEffect)(()=>{R.id||!0===a.__internal_setActiveInProgress||o(c.signUpUrl)},[]),!R.id)return(0,l.tZ)(v.W,{});let D=!!x.emailAddress.value,V=(null===(t=R.verifications)||void 0===t?void 0:null===(e=t.externalAccount)||void 0===e?void 0:e.status)=="verified",X=Z({attributes:_,hasEmail:D,activeCommIdentifierType:O,signUp:R,isProgressiveSignUp:T,legalConsentRequired:b.signUp.legal_consent_enabled});!function(e,t){if(t){var i,l,r,n,a,o,s,d;let c=!!t.emailAddress,u=(null===(l=t.verifications)||void 0===l?void 0:null===(i=l.emailAddress)||void 0===i?void 0:i.status)==="verified",p=(null===(n=t.verifications)||void 0===n?void 0:null===(r=n.phoneNumber)||void 0===r?void 0:r.status)==="verified",h=(null===(o=t.verifications)||void 0===o?void 0:null===(a=o.externalAccount)||void 0===a?void 0:a.status)==="verified",m=(null===(d=t.verifications)||void 0===d?void 0:null===(s=d.web3Wallet)||void 0===s?void 0:s.status)==="verified",v=null!==t.legalAcceptedAt;c&&u&&delete e.emailAddress,p&&delete e.phoneNumber,(h||m)&&delete e.password,t.firstName&&delete e.firstName,t.lastName&&delete e.lastName,t.username&&delete e.username,v&&delete e.legalAccepted,Object.entries(e).forEach(t=>{let[i,l]=t;l&&!l.required&&delete e[i]})}}(X,R);let W=b.authenticatableSocialStrategies,H=async e=>{var t,l;e.preventDefault();let r=Object.entries(X).reduce((e,t)=>{let[i,l]=t;return[...e,...l&&x[i]?[x[i]]:[]]},[]);z&&r.push({id:"unsafeMetadata",value:z});let n=!!(null===(t=r.find(e=>"emailAddress"===e.id))||void 0===t?void 0:t.value),s=!!(null===(l=r.find(e=>"phoneNumber"===e.id))||void 0===l?void 0:l.value);return R.missingFields.includes("email_address")&&R.missingFields.includes("phone_number")&&!n&&!s&&P(_,T)&&(r.push(x.emailAddress),r.push(x.phoneNumber)),i.setLoading(),i.setError(void 0),R.update((0,g.ni)(r)).then(e=>(0,I.completeSignUpFlow)({signUp:e,verifyEmailPath:"./verify-email-address",verifyPhonePath:"./verify-phone-number",handleComplete:()=>a.setActive({session:e.createdSessionId,redirectUrl:C}),navigate:o,oidcPrompt:q.oidcPrompt})).catch(e=>(0,g.S3)(e,r,i.setError)).finally(()=>i.setIdle())},Y=P(_,T),j=!V&&W.length>0,G=M?(0,d.localizationKeys)("signUp.legalConsent.continue.title"):(0,d.localizationKeys)("signUp.continue.title"),$=M?(0,d.localizationKeys)("signUp.legalConsent.continue.subtitle"):(0,d.localizationKeys)("signUp.continue.subtitle");return(0,l.tZ)(d.Flow.Part,{part:"complete",children:(0,l.BX)(p.Z.Root,{children:[(0,l.BX)(p.Z.Content,{children:[(0,l.BX)(m.h.Root,{showLogo:!0,children:[(0,l.tZ)(m.h.Title,{localizationKey:G}),(0,l.tZ)(m.h.Subtitle,{localizationKey:$})]}),(0,l.tZ)(p.Z.Alert,{children:i.error}),(0,l.tZ)(d.Flex,{direction:"col",elementDescriptor:d.descriptors.main,gap:8,children:(0,l.BX)(f.G,{children:[j&&!M&&(0,l.tZ)(w,{enableOAuthProviders:j,enableWeb3Providers:!1,enableAlternativePhoneCodeProviders:!1,continueSignUp:!0}),(0,l.tZ)(U,{handleSubmit:H,fields:X,formState:x,onlyLegalAcceptedMissing:M,canToggleEmailPhone:Y,handleEmailPhoneToggle:e=>{P(_,T)&&B(e)}})]})})]}),(0,l.tZ)(p.Z.Footer,{children:F?null:(0,l.BX)(p.Z.Action,{elementId:"signUp",children:[(0,l.tZ)(p.Z.ActionText,{localizationKey:(0,d.localizationKeys)("signUp.continue.actionText")}),(0,l.tZ)(p.Z.ActionLink,{localizationKey:(0,d.localizationKeys)("signUp.continue.actionLink"),to:F?"../../":a.buildUrlWithAuth(E)})]})})]})})});var K=i(8969);let k=(0,K.sN)(K.L);var R=i(36543),L=i(80753),F=i(24029),T=i(12464),O=i(95518),B=i(93234),q=i(96519);let x=()=>{let e=(0,r.cL)(),t=(0,h.useCardState)(),{navigate:i}=(0,u.useRouter)(),{signInUrl:n,waitlistUrl:a}=(0,s.useSignUpContext)(),o=(0,B.H)(),{userSettings:c}=(0,s.useEnvironment)(),{mode:v}=c.signUp,f=async()=>{await i(e.buildUrlWithAuth(a))},g=v===L.ci.RESTRICTED?(0,d.localizationKeys)("signUp.restrictedAccess.subtitle"):(0,d.localizationKeys)("signUp.restrictedAccess.subtitleWaitlist");return(0,l.BX)(p.Z.Root,{children:[(0,l.BX)(p.Z.Content,{children:[(0,l.BX)(m.h.Root,{showLogo:!0,children:[(0,l.tZ)(d.Icon,{icon:q.gO,sx:e=>({margin:"auto",width:e.sizes.$10,height:e.sizes.$10})}),(0,l.tZ)(m.h.Title,{localizationKey:(0,d.localizationKeys)("signUp.restrictedAccess.title")}),(0,l.tZ)(m.h.Subtitle,{localizationKey:g})]}),(0,l.tZ)(p.Z.Alert,{children:t.error}),v===L.ci.RESTRICTED&&o&&(0,l.tZ)(d.Flex,{direction:"col",gap:4,children:(0,l.tZ)(d.Button,{localizationKey:(0,d.localizationKeys)("signUp.restrictedAccess.blockButton__emailSupport"),onClick:()=>{window.location.href="mailto:".concat(o)}})}),v===L.ci.WAITLIST&&(0,l.tZ)(d.Flex,{direction:"col",gap:4,children:(0,l.tZ)(d.Button,{localizationKey:(0,d.localizationKeys)("signUp.restrictedAccess.blockButton__joinWaitlist"),onClick:f})})]}),(0,l.tZ)(p.Z.Footer,{children:(0,l.BX)(p.Z.Action,{elementId:"signUp",children:[(0,l.tZ)(p.Z.ActionText,{localizationKey:(0,d.localizationKeys)("signUp.restrictedAccess.actionText")}),(0,l.tZ)(p.Z.ActionLink,{localizationKey:(0,d.localizationKeys)("signUp.restrictedAccess.actionLink"),to:e.buildUrlWithAuth(n)})]})})]})},M=e=>{var t,i,r,n;let{handleSubmit:a,fields:o,formState:s,onUseAnotherMethod:c,phoneCodeProvider:u}=e,{providerToDisplayData:v,strategyToDisplayData:f}=(0,T.vO)(),g=u.name,A=u.channel,U=(0,h.useCardState)();return(0,l.tZ)(p.Z.Root,{children:(0,l.BX)(p.Z.Content,{children:[(0,l.BX)(m.h.Root,{showLogo:!0,showDivider:!0,children:[(0,l.tZ)(d.Col,{center:!0,children:(0,l.tZ)(d.Image,{src:null===(t=v[u.channel])||void 0===t?void 0:t.iconUrl,alt:"".concat(f[A].name," logo"),sx:e=>({width:e.sizes.$7,height:e.sizes.$7,maxWidth:"100%",marginBottom:e.sizes.$6})})}),(0,l.tZ)(m.h.Title,{localizationKey:(0,d.localizationKeys)("signUp.start.alternativePhoneCodeProvider.title",{provider:g})}),(0,l.tZ)(m.h.Subtitle,{localizationKey:(0,d.localizationKeys)("signUp.start.alternativePhoneCodeProvider.subtitle",{provider:g})})]}),(0,l.tZ)(p.Z.Alert,{children:U.error}),(0,l.tZ)(d.Flex,{direction:"col",elementDescriptor:d.descriptors.main,gap:6,children:(0,l.BX)(b.l.Root,{onSubmit:a,gap:8,children:[(0,l.tZ)(d.Col,{gap:6,children:(0,l.tZ)(b.l.ControlRow,{elementId:"phoneNumber",children:(0,l.tZ)(b.l.PhoneInput,{...s.phoneNumber.props,label:(0,d.localizationKeys)("signUp.start.alternativePhoneCodeProvider.label",{provider:g}),isRequired:!0,isOptional:!1,actionLabel:void 0,onActionClicked:void 0})})}),(0,l.BX)(d.Col,{center:!0,children:[(0,l.tZ)(y.S,{}),(0,l.BX)(d.Col,{gap:6,sx:{width:"100%"},children:[!!o[r="legalAccepted"]&&(null===(n=o[r])||void 0===n?void 0:n.required)&&(0,l.tZ)(b.l.ControlRow,{elementId:"legalAccepted",children:(0,l.tZ)(_.H,{...s.legalAccepted.props,isRequired:null===(i=o.legalAccepted)||void 0===i?void 0:i.required})}),(0,l.tZ)(b.l.SubmitButton,{hasArrow:!0,localizationKey:(0,d.localizationKeys)("formButtonPrimary")})]})]}),(0,l.tZ)(d.Col,{center:!0,children:(0,l.tZ)(d.Button,{variant:"link",colorScheme:"neutral",onClick:c,localizationKey:(0,d.localizationKeys)("signUp.start.alternativePhoneCodeProvider.actionLink")})})]})})]})})},D=(0,K.sN)((0,h.withCardStateProvider)(function(){let e=(0,h.useCardState)(),t=(0,r.cL)(),i=(0,T._m)(),a=(0,s.useCoreSignUp)(),{showOptionalFields:o}=(0,d.useAppearance)().parsedLayout,{userSettings:c,authConfig:b}=(0,s.useEnvironment)(),{navigate:_}=(0,u.useRouter)(),{attributes:A}=c,{setActive:S}=(0,r.cL)(),C=(0,s.useSignUpContext)(),E=!!n.useContext(s.SignInContext),{afterSignUpUrl:z,signInUrl:K,unsafeMetadata:k}=C,B=!!(C.isCombinedFlow&&E),[q,D]=n.useState(()=>{var e,t,i,l;return N(A,c.signUp.progressive,{phoneNumber:(null===(e=C.initialValues)||void 0===e?void 0:e.phoneNumber)===null?void 0:null===(t=C.initialValues)||void 0===t?void 0:t.phoneNumber,emailAddress:(null===(i=C.initialValues)||void 0===i?void 0:i.emailAddress)===null?void 0:null===(l=C.initialValues)||void 0===l?void 0:l.emailAddress,...B?{emailAddress:a.emailAddress,phoneNumber:a.phoneNumber}:{}})}),{t:V,locale:X}=(0,d.useLocalizations)(),W=C.initialValues||{},[H,Y]=n.useState(null),[j,G]=n.useState(!1),{userSettings:{passwordSettings:$,usernameSettings:J}}=(0,s.useEnvironment)(),{mode:Q}=c.signUp,ee={firstName:(0,g.Yp)("firstName",a.firstName||W.firstName||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__firstName"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__firstName")}),lastName:(0,g.Yp)("lastName",a.lastName||W.lastName||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__lastName"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__lastName")}),emailAddress:(0,g.Yp)("emailAddress",a.emailAddress||W.emailAddress||"",{type:"email",label:(0,d.localizationKeys)("formFieldLabel__emailAddress"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__emailAddress")}),username:(0,g.Yp)("username",a.username||W.username||"",{type:"text",label:(0,d.localizationKeys)("formFieldLabel__username"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__username"),transformer:e=>e.trim(),buildErrorMessage:e=>(0,g.fq)(e,{t:V,locale:X,usernameSettings:J})}),phoneNumber:(0,g.Yp)("phoneNumber",a.phoneNumber||W.phoneNumber||"",{type:"tel",label:(0,d.localizationKeys)("formFieldLabel__phoneNumber"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__phoneNumber")}),legalAccepted:(0,g.Yp)("legalAccepted","",{type:"checkbox",label:"",defaultChecked:!1,isRequired:c.signUp.legal_consent_enabled||!1}),password:(0,g.Yp)("password","",{type:"password",label:(0,d.localizationKeys)("formFieldLabel__password"),placeholder:(0,d.localizationKeys)("formFieldInputPlaceholder__password"),validatePassword:!0,buildErrorMessage:e=>(0,g.GM)(e,{t:V,locale:X,passwordSettings:$})}),ticket:(0,g.Yp)("ticket",(0,F.XV)("__clerk_ticket")||(0,F.XV)("__clerk_invitation_token")||"")},et=!!ee.ticket.value,ei=!!ee.emailAddress.value,el=c.signUp.progressive,er=c.signUp.legal_consent_enabled,en=C.oidcPrompt,ea=Z({attributes:A,hasTicket:et,hasEmail:ei,activeCommIdentifierType:q,isProgressiveSignUp:el,legalConsentRequired:er}),eo=()=>{ee.ticket.value&&(i.setLoading(),e.setLoading(),a.create({strategy:"ticket",ticket:ee.ticket.value}).then(e=>{ee.emailAddress.setValue(e.emailAddress||""),"missing_requirements"===e.status&&G(!0);let t=C.ssoCallbackUrl,i=C.afterSignUpUrl||"/";return(0,I.completeSignUpFlow)({signUp:e,redirectUrl:t,redirectUrlComplete:i,verifyEmailPath:"verify-email-address",verifyPhonePath:"verify-phone-number",handleComplete:()=>((0,F.xy)("__clerk_ticket"),(0,F.xy)("__clerk_invitation_token"),S({session:e.createdSessionId,redirectUrl:z})),navigate:_,oidcPrompt:en})}).catch(t=>{ee.ticket.setValue(""),(0,g.S3)(t,[],e.setError)}).finally(()=>{a.missingFields.some(e=>"saml"===e||"enterprise_sso"===e)||(i.setIdle(),e.setIdle())}))};n.useLayoutEffect(()=>{eo()},[]),n.useEffect(()=>{(async function(){let t=a.verifications.externalAccount.error;if(t){switch(t.code){case L.O1.NOT_ALLOWED_TO_SIGN_UP:case L.O1.OAUTH_ACCESS_DENIED:case L.O1.NOT_ALLOWED_ACCESS:case L.O1.SAML_USER_ATTRIBUTE_MISSING:case L.O1.OAUTH_EMAIL_DOMAIN_RESERVED_BY_SAML:case L.O1.USER_LOCKED:case L.O1.ENTERPRISE_SSO_USER_ATTRIBUTE_MISSING:case L.O1.ENTERPRISE_SSO_EMAIL_ADDRESS_DOMAIN_MISMATCH:case L.O1.ENTERPRISE_SSO_HOSTED_DOMAIN_MISMATCH:case L.O1.SAML_EMAIL_ADDRESS_DOMAIN_MISMATCH:case L.O1.ORGANIZATION_MEMBERSHIP_QUOTA_EXCEEDED_FOR_SSO:case L.O1.CAPTCHA_INVALID:case L.O1.FRAUD_DEVICE_BLOCKED:case L.O1.FRAUD_ACTION_BLOCKED:case L.O1.SIGNUP_RATE_LIMIT_EXCEEDED:e.setError(t);break;default:e.setError("Unable to complete action at this time. If the problem persists please contact support.")}await a.create({})}})()},[]);let es=async t=>{var i,l;let r;t.preventDefault();let n=Object.entries(ea).reduce((e,t)=>{let[i,l]=t;return e.push(...l&&ee[i]?[ee[i]]:[]),e},[]);if(k&&n.push({id:"unsafeMetadata",value:k}),ea.ticket){let e=()=>{};n.push({id:"strategy",value:"ticket",clearFeedback:e,setValue:e,onChange:e,setError:e})}let o=(null==H?void 0:H.channel)||(0,O.Vs)(n,b.preferredChannels,"phoneNumber");if(o){let e=()=>{};n.push({id:"strategy",value:"phone_code",clearFeedback:e,setValue:e,onChange:e,setError:e}),n.push({id:"channel",value:o,clearFeedback:e,setValue:e,onChange:e,setError:e})}let s=!!(null===(i=n.find(e=>"emailAddress"===e.id))||void 0===i?void 0:i.value),d=!!(null===(l=n.find(e=>"phoneNumber"===e.id))||void 0===l?void 0:l.value);!s&&!d&&P(A,el)&&(n.push(ee.emailAddress),n.push(ee.phoneNumber)),e.setLoading(),e.setError(void 0);let c=C.ssoCallbackUrl,u=C.afterSignUpUrl||"/";return(ea.ticket?a.upsert((0,g.ni)(n)):a.create((0,g.ni)(n))).then(e=>(0,I.completeSignUpFlow)({signUp:e,verifyEmailPath:"verify-email-address",verifyPhonePath:"verify-phone-number",handleComplete:()=>S({session:e.createdSessionId,redirectUrl:z}),navigate:_,redirectUrl:c,redirectUrlComplete:u,oidcPrompt:en})).catch(t=>(0,g.S3)(t,n,e.setError)).finally(()=>e.setIdle())};if(i.isLoading)return(0,l.tZ)(v.W,{});let ed=P(A,el),ec=Object.entries(ea).filter(e=>{let[t,i]=e;return o||(null==i?void 0:i.required)}),eu=function(e){let{authenticatableSocialStrategies:t,web3FirstFactors:i}=e;return e.hasValidAuthFactor||!t.length&&!i.length}(c)&&ec.length>0,ep=(!et||j)&&c.authenticatableSocialStrategies.length>0,eh=!et&&c.web3FirstFactors.length>0,em=!et&&c.alternativePhoneCodeChannels.length>0;return Q===L.ci.PUBLIC||et?(0,l.tZ)(d.Flow.Part,{part:"start",children:H?(0,l.tZ)(M,{handleSubmit:es,fields:ea,formState:ee,onUseAnotherMethod:()=>{Y(null)},phoneCodeProvider:H}):(0,l.BX)(p.Z.Root,{children:[(0,l.BX)(p.Z.Content,{children:[(0,l.BX)(m.h.Root,{showLogo:!0,children:[(0,l.tZ)(m.h.Title,{localizationKey:B?(0,d.localizationKeys)("signUp.start.titleCombined"):(0,d.localizationKeys)("signUp.start.title")}),(0,l.tZ)(m.h.Subtitle,{localizationKey:B?(0,d.localizationKeys)("signUp.start.subtitleCombined"):(0,d.localizationKeys)("signUp.start.subtitle")})]}),(0,l.tZ)(p.Z.Alert,{children:e.error}),(0,l.BX)(d.Flex,{direction:"col",elementDescriptor:d.descriptors.main,gap:6,children:[(0,l.BX)(f.G,{children:[(ep||eh||em)&&(0,l.tZ)(w,{enableOAuthProviders:ep,enableWeb3Providers:eh,enableAlternativePhoneCodeProviders:em,onAlternativePhoneCodeProviderClick:e=>{Y((0,R.H)(e)||null)},continueSignUp:j,legalAccepted:!!ee.legalAccepted.checked||void 0}),eu&&(0,l.tZ)(U,{handleSubmit:es,fields:ea,formState:ee,canToggleEmailPhone:ed,handleEmailPhoneToggle:e=>{P(A,el)&&D(e)}})]}),!eu&&(0,l.tZ)(y.S,{})]})]}),(0,l.tZ)(p.Z.Footer,{children:(0,l.BX)(p.Z.Action,{elementId:"signUp",children:[(0,l.tZ)(p.Z.ActionText,{localizationKey:(0,d.localizationKeys)("signUp.start.actionText")}),(0,l.tZ)(p.Z.ActionLink,{localizationKey:(0,d.localizationKeys)("signUp.start.actionLink"),to:B?"../":t.buildUrlWithAuth(K)})]})})]})}):(0,l.tZ)(x,{})}));var V=i(23465);let X=e=>{let{afterSignUpUrl:t}=(0,s.useSignUpContext)(),{setActive:i}=(0,r.cL)(),{navigate:n}=(0,u.useRouter)();return(0,l.tZ)(V.U,{cardTitle:e.cardTitle,cardSubtitle:e.cardSubtitle,resendButton:e.resendButton,onCodeEntryFinishedAction:(l,r,a)=>{e.attempt(l).then(async e=>(await r(),(0,I.completeSignUpFlow)({signUp:e,verifyEmailPath:"../verify-email-address",verifyPhonePath:"../verify-phone-number",continuePath:"../continue",handleComplete:()=>i({session:e.createdSessionId,redirectUrl:t}),navigate:n}))).catch(e=>a(e))},onResendCodeClicked:e.prepare,safeIdentifier:e.safeIdentifier,onIdentityPreviewEditClicked:()=>n("../",{searchParams:(0,F.z3)()}),alternativeMethodsLabel:e.alternativeMethodsLabel,onShowAlternativeMethodsClicked:e.onShowAlternativeMethodsClicked,showAlternativeMethods:e.showAlternativeMethods})},W=()=>{let e=(0,s.useCoreSignUp)(),t=(0,h.useCardState)(),i=e.verifications.emailAddress.status,r=!e.status||"verified"===i;return(0,T.ib)(r?void 0:()=>e.prepareEmailAddressVerification({strategy:"email_code"}).catch(e=>(0,g.S3)(e,[],t.setError)),{name:"prepare",strategy:"email_code",number:e.emailAddress},{staleTime:100}),(0,l.tZ)(d.Flow.Part,{part:"emailCode",children:(0,l.tZ)(X,{cardTitle:(0,d.localizationKeys)("signUp.emailCode.title"),cardSubtitle:(0,d.localizationKeys)("signUp.emailCode.subtitle"),inputLabel:(0,d.localizationKeys)("signUp.emailCode.formSubtitle"),resendButton:(0,d.localizationKeys)("signUp.emailCode.resendButton"),prepare:()=>{if(!r)return e.prepareEmailAddressVerification({strategy:"email_code"}).catch(e=>(0,g.S3)(e,[],t.setError))},attempt:t=>e.attemptEmailAddressVerification({code:t}),safeIdentifier:e.emailAddress})})};var H=i(65495),Y=i(14814);let j=()=>{let{t:e}=(0,d.useLocalizations)(),t=(0,s.useCoreSignUp)(),i=(0,s.useSignUpContext)(),{afterSignUpUrl:a}=i,o=(0,h.useCardState)(),{navigate:c}=(0,u.useRouter)(),{setActive:p}=(0,r.cL)(),[m,v]=n.useState(!1),{startEmailLinkFlow:f,cancelEmailLinkFlow:b}=(0,Y.E)(t);n.useEffect(()=>{_()},[]);let _=()=>f({redirectUrl:i.emailLinkRedirectUrl}).then(e=>y(e)).catch(e=>{(0,g.S3)(e,[],o.setError)}),y=async t=>{let i=t.verifications.emailAddress;"expired"===i.status?o.setError(e((0,d.localizationKeys)("formFieldError__verificationLinkExpired"))):i.verifiedFromTheSameClient()?v(!0):await (0,I.completeSignUpFlow)({signUp:t,continuePath:"../continue",verifyEmailPath:"../verify-email-address",verifyPhonePath:"../verify-phone-number",handleComplete:()=>p({session:t.createdSessionId,redirectUrl:a}),navigate:c})};return m?(0,l.tZ)(K.Ej,{title:(0,d.localizationKeys)("signUp.emailLink.verifiedSwitchTab.title"),subtitle:(0,d.localizationKeys)("signUp.emailLink.verifiedSwitchTab.subtitleNewTab"),status:"verified_switch_tab"}):(0,l.tZ)(d.Flow.Part,{part:"emailLink",children:(0,l.tZ)(H.J,{cardTitle:(0,d.localizationKeys)("signUp.emailLink.title"),cardSubtitle:(0,d.localizationKeys)("signUp.emailLink.subtitle"),formTitle:(0,d.localizationKeys)("signUp.emailLink.formTitle"),formSubtitle:(0,d.localizationKeys)("signUp.emailLink.formSubtitle"),resendButton:(0,d.localizationKeys)("signUp.emailLink.resendButton"),onResendCodeClicked:()=>{b(),_()},safeIdentifier:t.emailAddress})})},G=(0,h.withCardStateProvider)(()=>{var e,t;let{userSettings:i}=(0,s.useEnvironment)(),{attributes:r}=i;return(null===(t=r.email_address)||void 0===t?void 0:null===(e=t.verifications)||void 0===e?void 0:e.includes("email_link"))?(0,l.tZ)(j,{}):(0,l.tZ)(W,{})}),$=(0,h.withCardStateProvider)(()=>{let e=(0,s.useCoreSignUp)(),t=(0,h.useCardState)(),i=e.verifications.phoneNumber.channel,r=e.verifications.phoneNumber.status,n=!e.status||"verified"===r,a=!!i&&"sms"!==i,o=a?i:void 0;(0,T.ib)(n||a?void 0:()=>e.preparePhoneNumberVerification({strategy:"phone_code",channel:void 0}).catch(e=>(0,g.S3)(e,[],t.setError)),{name:"signUp.preparePhoneNumberVerification",strategy:"phone_code",number:e.phoneNumber},{staleTime:100});let c=(0,d.localizationKeys)("signUp.phoneCode.title"),u=(0,d.localizationKeys)("signUp.phoneCode.subtitle"),p=(0,d.localizationKeys)("signUp.phoneCode.resendButton");if(a){var m;let e=null===(m=(0,R.H)(i))||void 0===m?void 0:m.name;c=(0,d.localizationKeys)("signUp.alternativePhoneCodeProvider.title",{provider:e}),u=(0,d.localizationKeys)("signUp.alternativePhoneCodeProvider.subtitle",{provider:e}),p=(0,d.localizationKeys)("signUp.alternativePhoneCodeProvider.resendButton")}return t.isLoading?(0,l.tZ)(v.W,{}):(0,l.tZ)(d.Flow.Part,{part:"phoneCode",children:(0,l.tZ)(X,{cardTitle:c,cardSubtitle:u,resendButton:p,prepare:()=>{if(!n)return e.preparePhoneNumberVerification({strategy:"phone_code",channel:o}).catch(e=>(0,g.S3)(e,[],t.setError))},attempt:t=>e.attemptPhoneNumberVerification({code:t}),safeIdentifier:e.phoneNumber,alternativeMethodsLabel:a?(0,d.localizationKeys)("footerActionLink__alternativePhoneCodeProvider"):void 0,onShowAlternativeMethodsClicked:a?()=>{t.setLoading(),t.setError(void 0),e.preparePhoneNumberVerification({strategy:"phone_code",channel:void 0}).catch(e=>(0,g.S3)(e,[],t.setError)).finally(()=>t.setIdle())}:void 0,showAlternativeMethods:!!a||void 0})})}),J=()=>(0,l.tZ)($,{});function Q(){let e=(0,r.cL)();return n.useEffect(()=>{e.redirectToSignUp()},[]),null}function ee(){(0,c.z)();let{__internal_setComponentNavigationContext:e}=(0,r.cL)(),{navigate:t,indexPath:i}=(0,u.useRouter)(),p=(0,s.useSignUpContext)();return n.useEffect(()=>null==e?void 0:e({indexPath:i,navigate:t}),[i,t]),(0,l.tZ)(d.Flow.Root,{flow:"signUp",children:(0,l.BX)(u.Switch,{children:[(0,l.tZ)(u.Route,{path:"verify-email-address",canActivate:e=>!!e.client.signUp.emailAddress,children:(0,l.tZ)(G,{})}),(0,l.tZ)(u.Route,{path:"verify-phone-number",canActivate:e=>!!e.client.signUp.phoneNumber,children:(0,l.tZ)(J,{})}),(0,l.tZ)(u.Route,{path:"sso-callback",children:(0,l.tZ)(k,{signUpUrl:p.signUpUrl,signInUrl:p.signInUrl,signUpForceRedirectUrl:p.afterSignUpUrl,signInForceRedirectUrl:p.afterSignInUrl,secondFactorUrl:p.secondFactorUrl,continueSignUpUrl:"../continue",verifyEmailAddressUrl:"../verify-email-address",verifyPhoneNumberUrl:"../verify-phone-number"})}),(0,l.tZ)(u.Route,{path:"verify",children:(0,l.tZ)(o.$,{redirectUrlComplete:p.afterSignUpUrl,verifyEmailPath:"../verify-email-address",verifyPhonePath:"../verify-phone-number"})}),(0,l.BX)(u.Route,{path:"continue",children:[(0,l.tZ)(u.Route,{path:"verify-email-address",canActivate:e=>!!e.client.signUp.emailAddress,children:(0,l.tZ)(G,{})}),(0,l.tZ)(u.Route,{path:"verify-phone-number",canActivate:e=>!!e.client.signUp.phoneNumber,children:(0,l.tZ)(J,{})}),(0,l.tZ)(u.Route,{index:!0,children:(0,l.tZ)(z,{})})]}),(0,l.tZ)(u.Route,{path:"tasks",children:(0,l.tZ)(a.x7,{})}),(0,l.tZ)(u.Route,{index:!0,children:(0,l.tZ)(D,{})}),(0,l.tZ)(u.Route,{children:(0,l.tZ)(Q,{})})]})})}ee.displayName="SignUp";let et=(0,s.withCoreSessionSwitchGuard)(ee),ei=e=>{let t={signInUrl:"/".concat(u.VIRTUAL_ROUTER_BASE_PATH,"/sign-in"),waitlistUrl:"/".concat(u.VIRTUAL_ROUTER_BASE_PATH,"/waitlist"),...e};return(0,l.tZ)(u.Route,{path:"sign-up",children:(0,l.tZ)(s.SignUpContext.Provider,{value:{componentName:"SignUp",...t,routing:"virtual",mode:"modal"},children:(0,l.tZ)("div",{children:(0,l.tZ)(et,{...t,routing:"virtual"})})})})}},46052:function(e,t,i){i.r(t),i.d(t,{completeSignUpFlow:()=>l.v});var l=i(65756)}}]);