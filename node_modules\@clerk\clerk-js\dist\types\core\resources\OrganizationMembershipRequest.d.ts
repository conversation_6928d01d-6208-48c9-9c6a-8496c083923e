import type { OrganizationInvitationStatus, OrganizationMembershipRequestJSON, OrganizationMembershipRequestResource } from '@clerk/types';
import { BaseResource, PublicUserData } from './internal';
export declare class OrganizationMembershipRequest extends BaseResource implements OrganizationMembershipRequestResource {
    id: string;
    organizationId: string;
    status: OrganizationInvitationStatus;
    publicUserData: PublicUserData;
    createdAt: Date;
    updatedAt: Date;
    constructor(data: OrganizationMembershipRequestJSON);
    accept: () => Promise<OrganizationMembershipRequestResource>;
    reject: () => Promise<OrganizationMembershipRequestResource>;
    protected fromJSON(data: OrganizationMembershipRequestJSON | null): this;
}
