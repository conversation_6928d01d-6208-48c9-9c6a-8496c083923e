import type { NavbarRoute } from '@/ui/elements/Navbar';
import type { OrganizationProfileCtx } from '../../types';
import type { CustomPageContent } from '../../utils';
type PagesType = {
    routes: NavbarRoute[];
    contents: CustomPageContent[];
    pageToRootNavbarRouteMap: Record<string, NavbarRoute>;
};
export type OrganizationProfileContextType = OrganizationProfileCtx & {
    pages: PagesType;
    navigateAfterLeaveOrganization: () => Promise<unknown>;
    navigateToGeneralPageRoot: () => Promise<unknown>;
    isMembersPageRoot: boolean;
    isGeneralPageRoot: boolean;
    isBillingPageRoot: boolean;
    isApiKeysPageRoot: boolean;
};
export declare const OrganizationProfileContext: import("react").Context<OrganizationProfileCtx | null>;
export declare const useOrganizationProfileContext: () => OrganizationProfileContextType;
export {};
