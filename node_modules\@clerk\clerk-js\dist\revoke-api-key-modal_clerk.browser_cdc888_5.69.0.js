"use strict";(globalThis.webpackChunk_clerk_clerk_js=globalThis.webpackChunk_clerk_clerk_js||[]).push([["642"],{3250:function(e,t,i){i.r(t),i.d(t,{RevokeAPIKeyConfirmationModal:()=>k});var o=i(9109),l=i(3799),n=i(4126),r=i(4455),a=i(431),s=i(9460),d=i(8487),c=i(6459),p=i(1085),h=i(7623);let k=({subject:e,isOpen:t,onOpen:i,onClose:k,apiKeyId:u,apiKeyName:m,modalRoot:b})=>{let y=(0,l.cL)(),{mutate:f}=(0,n.kY)(),v=async t=>{t.preventDefault(),u&&(await y.apiKeys.revoke({apiKeyID:u}),f({key:"api-keys",subject:e}),k())},C=(0,h.Yp)("apiKeyRevokeConfirmation","",{type:"text",label:'Type "Revoke" to confirm',placeholder:"Revoke",isRequired:!0}),g="Revoke"===C.value;return t?(0,o.tZ)(c.Modal,{handleOpen:i,handleClose:k,canCloseModal:!1,portalRoot:b,containerSx:[{alignItems:"center"},b?e=>({position:"absolute",right:0,bottom:0,backgroundColor:"inherit",backdropFilter:`blur(${e.sizes.$2})`,display:"flex",justifyContent:"center",minHeight:"100%",height:"100%",width:"100%"}):{}],children:(0,o.tZ)(r.Z.Root,{role:"alertdialog",children:(0,o.tZ)(r.Z.Content,{sx:e=>({textAlign:"left",padding:`${e.sizes.$4} ${e.sizes.$5} ${e.sizes.$4} ${e.sizes.$6}`}),children:(0,o.tZ)(d.Y,{headerTitle:(0,p.u1)("apiKeys.revokeConfirmation.formTitle",{apiKeyName:m}),headerSubtitle:(0,p.u1)("apiKeys.revokeConfirmation.formHint"),children:(0,o.BX)(a.l.Root,{onSubmit:v,children:[(0,o.tZ)(a.l.ControlRow,{elementId:C.id,children:(0,o.tZ)(a.l.PlainInput,{...C.props})}),(0,o.tZ)(s.A,{submitLabel:(0,p.u1)("apiKeys.revokeConfirmation.formButtonPrimary__revoke"),colorScheme:"danger",isDisabled:!g,onReset:k})]})})})})}):null}}}]);