import type { UsePasswordCbs, UsePasswordConfig } from '../../utils/passwords/password';
import type { FormControlState } from '../utils';
export declare const usePassword: (config: UsePasswordConfig, callbacks?: UsePasswordCbs) => {
    validatePassword: (password: string, internalCallbacks?: import("@clerk/types").ValidatePasswordCallbacks) => void;
};
export declare const useConfirmPassword: ({ passwordField, confirmPasswordField, }: {
    passwordField: FormControlState;
    confirmPasswordField: FormControlState;
}) => {
    setConfirmPasswordFeedback: (password: string) => void;
    isPasswordMatch: boolean;
};
