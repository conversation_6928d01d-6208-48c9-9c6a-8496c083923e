import type { JW<PERSON>, TokenJSON, TokenJSONSnapshot, TokenResource } from '@clerk/types';
import { BaseResource } from './internal';
export declare class Token extends BaseResource implements TokenResource {
    pathRoot: string;
    jwt?: JWT;
    static create(path: string, body?: any): Promise<TokenResource>;
    constructor(data: TokenJSON | TokenJSONSnapshot | null, pathRoot?: string);
    getRawString: () => string;
    protected fromJSON(data: TokenJSON | TokenJSONSnapshot | null): this;
    __internal_toSnapshot(): TokenJSONSnapshot;
}
