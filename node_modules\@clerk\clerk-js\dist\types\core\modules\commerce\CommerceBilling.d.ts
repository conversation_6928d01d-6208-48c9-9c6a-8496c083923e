import type { ClerkPaginatedResponse, CommerceBillingNamespace, CommercePaymentResource, CommercePlanResource, CommerceStatementResource, CommerceSubscriptionResource, CreateCheckoutParams, GetPaymentAttemptsParams, GetPlansParams, GetStatementsParams, GetSubscriptionsParams } from '@clerk/types';
import { CommerceCheckout } from '../../resources/internal';
export declare class CommerceBilling implements CommerceBillingNamespace {
    getPlans: (params?: GetPlansParams) => Promise<CommercePlanResource[]>;
    getSubscriptions: (params: GetSubscriptionsParams) => Promise<ClerkPaginatedResponse<CommerceSubscriptionResource>>;
    getStatements: (params: GetStatementsParams) => Promise<ClerkPaginatedResponse<CommerceStatementResource>>;
    getPaymentAttempts: (params: GetPaymentAttemptsParams) => Promise<ClerkPaginatedResponse<CommercePaymentResource>>;
    startCheckout: (params: CreateCheckoutParams) => Promise<CommerceCheckout>;
}
