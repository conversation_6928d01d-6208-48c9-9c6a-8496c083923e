import type { MenuId } from '@clerk/types';
import type { Placement } from '@floating-ui/react';
import type { PropsWithChildren } from 'react';
import React from 'react';
import type { Button } from '../customizables';
import { Col } from '../customizables';
import type { UsePopoverReturn } from '../hooks';
import type { PropsOfComponent } from '../styledSystem';
type MenuState = {
    popoverCtx: UsePopoverReturn;
    elementId?: MenuId;
};
export declare const MenuStateCtx: React.Context<{
    value: MenuState;
} | undefined>, useMenuState: () => MenuState;
type MenuProps = PropsWithChildren<Record<never, never>> & {
    elementId?: MenuId;
    popoverPlacement?: Placement;
};
export declare const Menu: React.ComponentType<MenuProps>;
type MenuTriggerProps = React.PropsWithChildren<{
    arialLabel?: string | ((open: boolean) => string);
}>;
export declare const MenuTrigger: (props: MenuTriggerProps) => React.DetailedReactHTMLElement<React.HTMLAttributes<HTMLElement>, HTMLElement> | null;
type MenuListProps = PropsOfComponent<typeof Col> & {
    asPortal?: boolean;
};
export declare const MenuList: (props: MenuListProps) => import("@emotion/react/jsx-runtime").JSX.Element;
type MenuItemProps = PropsOfComponent<typeof Button> & {
    destructive?: boolean;
    closeAfterClick?: boolean;
};
export declare const MenuItem: (props: MenuItemProps) => import("@emotion/react/jsx-runtime").JSX.Element;
export {};
