import type { Theme } from '@clerk/types';
import type { fontSizes, fontWeights } from '../foundations/typography';
export declare const createColorScales: (theme: Theme) => Record<string, unknown>;
export declare const toHSLA: (str: string | undefined) => import("@clerk/types").HslaColorString | undefined;
export declare const createRadiiUnits: (theme: Theme) => {
    sm: string;
    md: string;
    lg: string;
    xl: string;
} | undefined;
export declare const createSpaceScale: (theme: Theme) => any;
export declare const createFontSizeScale: (theme: Theme) => Record<keyof typeof fontSizes, string> | undefined;
export declare const createFontWeightScale: (theme: Theme) => Record<keyof typeof fontWeights, any>;
export declare const createFonts: (theme: Theme) => Record<string, unknown>;
