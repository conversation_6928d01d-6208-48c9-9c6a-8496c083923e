import type { RoleJSON, RoleResource } from '@clerk/types';
import { BaseResource } from './internal';
import { Permission } from './Permission';
export declare class Role extends BaseResource implements RoleResource {
    id: string;
    key: string;
    name: string;
    description: string;
    permissions: Permission[];
    createdAt: Date;
    updatedAt: Date;
    constructor(data: RoleJSON);
    protected fromJSON(data: RoleJSON | null): this;
}
